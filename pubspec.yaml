name: niimbot_flutter_canvas
description: Flutter <PERSON> By Niimbot.
version: 0.8.6+3

homepage: https://git.jc-ai.cn/print/foundation/niimbot_flutter_canvas

environment:
  sdk: ">=3.5.0 <4.0.0"
  flutter: ">=3.24.0"

dependencies:
  flutter:
    sdk: flutter
  provider: ^6.0.3
  dio: ^5.0.0
  image: ^4.2.0
  uuid: ^4.0.0
  dotted_border: ^2.0.0+2
  cached_network_image: ^3.2.0
  custom_pop_up_menu: ^1.2.4
  path_provider: ^2.0.6
  isolate: ^2.1.1
  netal_plugin: ^1.4.5+2
  flutter_canvas_plugins_interface: ^1.0.14
  niimbot_excel: ^1.1.9
  niimbot_analytics: ^0.2.0
  collection: ^1.17.0
  url_launcher: ^6.1.11
  decimal: ^3.0.0
  lottie: ^3.0.0
  niimbot_ui: ^1.7.2
  niimbot_intl: ^1.2.3
  file_picker: ^8.0.0
  super_clipboard: ^0.9.0-dev.6
  image_compression: ^1.0.4
  should_rebuild: ^1.0.1
  niimbot_template: ^0.4.8+2
  niimbot_qbarcode: ^1.0.3
  file: ^7.0.0
  vector_math: ^2.1.4
  excel: ^4.0.6
  shared_preferences: ^2.3.2
  equatable: ^2.0.5
  crypto: ^3.0.5
  path: ^1.9.0
  intl: any #跟随Flutter版本
  flutter_hooks: ^0.20.0
  async: ^2.11.0
  niimbot_http: ^0.4.2-bate1
  two_dimensional_scrollables: ^0.3.3
  freezed_annotation: ^2.4.4
  pdfx: ^2.8.3

#dependency_overrides:
#  niimbot_template:
#    path: ../niimbot_template
#  netal_plugin:
#    path: ../netal_plugin
#  niimbot_http:
#    path: ../niimbot_http/niimbot_http

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: any
  cider: ^0.2.8
  build_runner: ^2.4.13
#  freezed: ^3.0.3
#  build_runner: ^2.1.11
#  json_serializable: ^6.2.0
#  json_annotation: ^4.5.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:
  #  uses-material-design: true
  # To add assets to your package, add an assets section, like this:
  assets:
    - assets/
    #    - assets/app_bar/
    #    - assets/bottom_bar/
    #    - assets/element/
    #    - assets/element/attribute/
    #    - assets/element/icon/
    #    - assets/floating_shortcut/
    #    - assets/input_bar/
    - assets/common/
    - assets/common/text_direction/
    #    - assets/translations/
    #    - assets/scan/
    #    - assets/ocr/
    #    - assets/asr/
    #    - assets/excel/
    - assets/holder/
    - assets/lottie/

  #
  # For details regarding assets in packages, see
  # https://flutter.dev/assets-and-images/#from-packages
  #
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # To add custom fonts to your package, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts in packages, see
  # https://flutter.dev/custom-fonts/#from-packages
