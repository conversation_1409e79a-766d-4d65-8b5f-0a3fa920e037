## 2025/7/28 [0.8.5+17,0.8.5+18]

- [update] 保存按钮刷新策略

## 2025/7/28 [0.8.5+16]

- [update] 多图裁剪等性能问题

## 2025/7/24 [0.8.5+14&0.8.5+15]

- [update] 上传多图处理

## 2025/7/24 [0.8.5+13]

- [update] 修复部分已知问题

## 2025/7/23 [0.8.5+12]

- [update] 修复部分已知问题

## 2025/7/22 [0.8.5+11]

- [update] 修复断开设备未恢复默认色

## 2025/7/21 [0.8.5+8 & 0.8.5+9& 0.8.5+10]

- [update] 修复部分多图bug

## 2025/7/17 [0.8.5+5 & 0.8.5+6 & 0.8.5+7]

- [update] 完成保存开发

## 2025/7/15 [0.8.5+3 & 0.8.5+4]

- [update] 裁剪优化

## 2025/7/11 [0.8.5+2]

- [update] 放开pdf对比度相关代码

## 2025/7/11 [0.8.5+1]

- [update] 合并多图相关代码

## 2025/7/8 [0.8.4 & 0.8.5]

- [update] 兼容红黑碳带画板样式渲染
- [fix] 日语默认字体相关问题修复

## 2025/7/7 [0.8.3]

- [fix] 日语默认字体

## 2025/7/1 [0.7.10-beta1]

- [fix] 修复出血线缩放

## 2025/7/1 [0.7.9 & 0.8.0 & 0.8.1 & 0.8.2]

- [fix] pdf性能优化已知问题修复
- [fix] 修复出血线缩放

## 2025/6/30 [0.7.7 & 0.7.8]

- [fix] pdf性能优化,裁剪loading优化

## 2025/6/27 [0.7.6]

- [fix] pdf性能优化,添加裁剪loading

## 2025/6/24 [0.7.3 & 0.7.4 & 0.7.5]

- [fix] pdf性能优化,添加裁剪loading

## 2025/6/24 [0.7.3-beta3 & 0.7.3-beta4& 0.7.3-beta5& 0.7.3-beta6]

- [fix] pdf预览性能优化

## 2025/6/24 [0.7.3-bate2]

- [fix] pdf裁剪bug修复

## 2025/6/23 [0.7.2-bate9]&[0.7.3-bate1]

- [fix] pdf裁剪bug修复

## 2025/6/20 [0.7.2-bate7&0.7.2-bate8]

- [fix] pdf裁剪bug修复

## 2025/6/19 [0.7.2-bate6]

- [fix] pdf相关已知bug修复

## 2025/6/18 [0.7.2-bate4&0.7.2-bate5]

- [update] pdf导入图像倍率计算

## 2025/6/12 [0.7.2-bate3]

- [update] 剪裁和遮挡、旋转相关问题修复

## 2025/6/12 [0.7.1-beta9]&[0.7.2-bate1]&[0.7.2-bate2]

- [update] 剪裁和遮挡相关问题

## 2025/6/9 [0.7.1-beta8]

- [update] pdf解析相关

## 2025/5/27 [0.7.1-beta2] && [0.7.1-beta3]&& [0.7.1-beta4]&& [0.7.1-beta5]&& [0.7.1-beta6]&& [0.7.1-beta7]

- [fix] 修复已知问题

## 2025/5/16 [0.7.1-beta1]

- [fix] 重新绑定数据源,一维码、二维码去掉列表勾选

## 2025/5/16 [0.7.0-bate9]

- [fix] 画板文本元素，文本输入框相关逻辑调整

## 2025/5/14 [0.7.0-bate8]

- [fix] 数据源切换一维码二维码是位置不对问题修复

## 2025/4/18 [0.7.0-bate6 && 0.7.0-bate7]

- [fix] 更新:updateCanvasTemplate相关方法调整

## 2025/4/7 [0.7.0-bate4 && 0.7.0-beta5]

- [fix] 更新:数据源解析调整

## 2025/4/7 [0.7.0-bate2]&[0.7.0-bate3]

- [fix] 更新:合并初始化代码

## 2025/3/27 [0.7.0-bate1]

- [fix] 更新:依赖C1升级TemplateData等

## 2025/5/19 [0.6.9]

- [fix] 修复:文本元素聚焦失焦问题文件替换

## 2025/3/27 [0.6.8]

- [fix] 修复:标尺像素毫米互转异常

## 2025/3/27 [0.6.6]&[0.6.7]

- [fix] 修复:部分像素毫米转换异常

## 2025/3/20 [0.6.5]

- [fix] 修复:tab样式异常

## 2025/3/20 [0.6.4]

- [chore] 更新:依赖

## 2025/3/19 [0.6.2]&[0.6.3]

- [feature] 调整:支持`lineMode`字段
- [feature] 调整:使用`RatioUtils`处理倍率转换

## 2025/3/18 [0.6.1]

- [chore] 更新:依赖

## 2025/3/14 [0.6.0]

- [chore] 升级:Flutter依赖更新

## 2025/3/5 [0.5.34]&[0.5.35]&& [0.5.36]&& [0.5.37]

- [fix] 修复:鼠标滚动逻辑
- [fix] 优化:画板底色调整
- [fix] 修复:控制判断

## 2025/2/28 [0.5.33]

- [feature] 优化:行业模板逻辑优化

## 2025/2/27 [0.5.31]&[0.5.32]

- [feature] 优化:画板聚焦逻辑
- [feature] 优化:获取实时时间默认值时矫正错误情况
- [feature] 优化:获取实时时间星期不匹配互转

## 2025/2/24 [0.5.30]

- [feature] 优化:获取背景图兼容性

## 2025/2/24 [0.5.29]

- [feature] 优化:添加数据源快速定位输入框、新增日期格式

## 2025/2/21 [0.5.22]&[0.5.23]&[0.5.24]&[0.5.25]&[0.5.26]&[0.5.27]&[0.5.28]

- [fix] 修复:旋转视图 相关问题

## 2025/2/20 [0.5.18]&[0.5.19]&[0.5.20]&[0.5.21]

- [fix] 修复:旋转视图 相关问题
- [feature] 适配:移动端兼容`rotate`字段为`int`类型

## 2025/2/19 [0.5.16]&[0.5.17]

- [feature] 优化:单元格交互
- [fix] 修复:旋转视图 表格在缩放时单元格选中异常

## 2025/2/18 [0.5.15]

- [fix] 修复:旋转视图 相关问题

## 2025/2/17 [0.5.14]

- [fix] 修复:旋转视图 相关问题

## 2025/2/14 [0.5.13]

- [fix] 修复:旋转视图 相关问题

## 2025/2/12 [0.5.12]

- [fix] 修复:旋转视图 相关问题

## 2025/2/7 [0.5.11]

- [fix] 修复:旋转视图 关联元素旋转未同步

## 2025/2/6 [0.5.10]

- [fix] 修复:旋转视图 背景图旋转问题

## 2025/2/5 [0.5.9]

- [feature] 新增:旋转视图

## 2025/1/10 [0.5.8]

- [feature] 升级图像库、excel解析库，合入崩溃问题

## 2025/1/9 [0.5.7]

- [feature] 新增:数据源导入情况下拖拽元素大小、位置结束后更新预览图
- [fix] 修复:表格拖拽相关问题

## 2025/1/6 [0.5.6]

- [feature] 新增:文本元素画板输入框自适应
- [feature] 新增:表格元素拖拽位置区域优化

## 2025/1/2 [0.5.5]

- [fix] 修复:元素变更后不刷新问题

## 2024/12/20 [0.5.4]

- [fix] 修复:单通道图片问题处理

## 2024/12/19 [0.5.3]

- [fix] 修复:画板外部图片复制粘贴问题

## 2024/12/18 [0.5.2]

- [fix] 修复:画板埋点相关bug修复
- [fix] 修复:excel展示表格勾选条数不对修复

## 2024/12/17 [0.5.1]

- [fix] 修复:画板埋点相关bug修复
- [fix] 修复:拖拽添加元素相关问题修复

## 2024/12/16 [0.5.0]

- [fix] 修复:出血线相关问题
- [feature] 新增: 画板埋点

## 2024/12/12 [0.4.16]

- [fix] 修复:excel展示表格渲染条数不对
- [fix] 修复:图标边框拖拽添加问题

## 2024/12/12 [0.4.14]&[0.4.15]

- [chore] 更新:更新`niimbot_excel`,`netal_plugin`依赖库
- [fix] 修复: `niimbot_excel`,`netal_plugin`库存在内存泄露
- [feature] 优化: Excel导入表格展示Excel数据
- [fix] 修复:单通道图片导入异常
- [feature] 优化: 元素重绘
- [feature] 新增: 关联时间前缀
- [fix] 修复: 出血线范围错误
- [feature] 优化: 标尺性能

## 2024/12/9 [0.4.10]&[0.4.11]&[0.4.12]&[0.4.13]

- [fix] fix:修复画板焦点问题
- [fix] fix:优化图片上传压缩体验问题

## 2024/12/5 [0.4.9]

- [fix] fix:更新niimbot_template依赖库
- [fix] fix:优化图片上传压缩问题

## 2024/12/5 [0.4.8]

- [fix] fix:更换双击编辑多语言
- [fix] fix:更新niimbot_template依赖库

## 2024/12/3 [0.4.7]

- [fix] fix:新增查看教程和下载模板多语言

## 2024/12/2 [0.4.5]&[0.4.6]

- [fix] fix:画板右键粘贴问题修复

## 2024/11/28 [0.4.4]

- [fix] fix:画板复制粘贴及埋点问题修复

## 2024/11/27 [0.4.3]

- [fix] fix:画板焦点问题修复

## 2024/11/25 [0.4.2]

- [fix] fix:复制粘贴问题修复

## 2024/11/21 [0.4.0]&[0.4.1]

- [feature] 适配:标签纸替换
- [feature] 适配:多页签

## 2024/11/18 [0.3.27]

- [fix] fix:数据源选择问题修复

## 2024/11/13 [0.3.26]

- [fix] fix:数据源预览切换页面优化

## 2024/11/13 [0.3.23]&[0.3.24]&[0.3.25]

- [fix] fix:数据源崩溃问题修复
- [fix] fix:屏幕缩放数据源滚动条问题修复
- [fix] fix:数据源替换问题修复

## 2024/11/12 [0.3.20]&[0.3.21]&[0.3.22]

- [fix] fix:数据源预览滚动条处理
- [chore] 适配:UI组件库

## 2024/11/11 [0.3.19]

- [fix] fix:数据源优化及修复

## 2024/11/8 [0.3.17]&[0.3.18]

- [fix] fix:多语言时间显示问题
- [fix] fix:修复替换数据源问题

## 2024/11/7 [0.3.16]

- [fix] fix:多语言时间显示问题、数据源预览优化

## 2024/11/5 [0.3.14]

- [fix] fix:升级图像库,合并内存暴涨问题

## 2024/11/5 [0.3.14]

- [fix] fix:升级flutter等问题修复

## 2024/11/4 [0.3.12]&[0.3.13]

- [fix] fix:优化画板渲染

## 2024/10/31 [0.3.10]&[0.3.11]

- [fix] fix:合并数据源交互优化
- [fix] 修复:优化画板刷新频率

## 2024/10/28 [0.3.9]

- [chore] fix:rtl适配数据源动画

## 2024/10/28 [0.3.8]

- [chore] fix:rtl适配

## 2024/10/15 [0.3.5]&[0.3.7]

- [chore] fix:单通道图片问题处理

## 2024/10/15 [0.3.5]&[0.3.6]

- [chore] fix:ltf样式适配

## 2024/10/15 [0.3.3]&[0.3.4]

- [chore] fix:ltf表格适配
- [chore] fix:数据源修复

## 2024/10/14 [0.3.2]

- [chore] fix:修复 左侧弹出异常

## 2024/10/11 [0.3.1]

- [chore] 更新:rtl适配

## 2024/10/11 [0.3.0]

- [chore] 更新:调整依赖

## 2024/9/26 [0.2.62]

- [fix] 修复:根据耗材颜色更新元素颜色导致堆栈溢出

## 2024/9/24 [0.2.61]

- [chore] 适配:`niimbot_ui`更新

## 2024/9/23 [0.2.60]

- [fix] 修复:单个元素对齐无效
- [fix] 修复:线条旋转后改变长度导致位置偏移

## 2024/9/13 [0.2.58]&[0.2.59]

- [feature] 修复sp存储问题
- [feature] 修复时间组件默认值问题

## 2024/9/13 [0.2.57]

- [feature] 修复实时时间默认问题
- [feature] 修复保存按钮置灰问题

## 2024/9/9 [0.2.56]

- [feature] 打印列名优化

## 2024/9/9 [0.2.55]

- [feature] 表格自适应标签纸、数据源预览优化

## 2024/9/6 [0.2.54]

- [fix] 修复:时间元素相关bug

## 2024/9/5 [0.2.53]

- [feature] 完善:保存按钮状态表现

## 2024/9/4 [0.2.52]

- [fix] 修复:时间元素相关问题

## 2024/9/3 [0.2.51]

- [feature] 完善:数据绑定一维码
- [feature] 完善:数据源多语言翻译
- [fix] 修复:边框缩放显示

## 2024/9/2 [0.2.49]&[0.2.50]

- [fix] 修复:时间元素bug修复
- [feature] 调整:添加元素默认位置为`Offset(1,1)`

## 2024/8/28 [0.2.48]

- [fix] 修复:时间组件相关问题

## 2024/8/27 [0.2.46]&[0.2.47]

- [fix] 修复:时间组件回填问题
- [fix] 修复:部分已知bug

## 2024/8/26 [0.2.44]&[0.2.45]

- [feature] 完善:时间组件

## 2024/8/20 [0.2.43]

- [feature] 优化:升级组件库

## 2024/8/20 [0.2.42]

- [feature] 优化:部分性能

## 2024/8/19 [0.2.40]&[0.2.41]

- [feature] 优化:部分性能

## 2024/8/16 [0.2.38]&[0.2.39]

- [fix] 修复:文本模式相关bug

## 2024/8/15 [0.2.36]&[0.2.37]

- [fix] 修复:卡顿问题导致部分元素不可拖
- [fix] 修复:部分已知问题

## 2024/8/14 [0.2.35]

- [fix] 修复:文本模式相关bug

## 2024/8/12 [0.2.34]

- [fix] 修复:画板相关bug

## 2024/8/12 [0.2.33]

- [fix] 修复:文本模式相关bug

## 2024/8/7 [0.2.32]

- [fix] 修复:动画资源文件引入

## 2024/8/6 [0.2.31]

- [fix] 修复:文本模式异常表现

## 2024/8/5 [0.2.27]&[0.2.28]&[0.2.29]&[0.2.30]

- [feature] 合并删除数据源
- [feature] 数据源动画
- [fix] 修复:文本模式异常表现

## 2024/8/2 [0.2.26]

- [feature] 适配:合并环境区分

## 2024/8/1 [0.2.25]

- [feature] 适配:文本模式

## 2024/7/30 [0.2.24]

- [fix] 一维码识别校验规则修改

## 2024/7/30 [0.2.23]

- [fix] 多选元素颜色选中bug修复

## 2024/7/25 [0.2.22]

- [fix] 数据源表头作为数据

## 2024/7/25 [0.2.21]

- [fix] 边框及背景图bug

## 2024/7/22 [0.2.20]

- [fix] 标签拖拽bug修复

## 2024/7/22 [0.2.19]

- [fix] 数据源无背景图时生成bug修复

## 2024/7/22 [0.2.18]

- [fix] 删除PC未使用的三方库
- [fix] 升级解析库

## 2024/7/18 [0.2.16]&[0.2.17]

- [fix] 合并线宽代码
- [fix] 升级解析库

## 2024/7/18 [0.2.13]&[0.2.14]&[0.2.15]

- [fix] 修复弧形文本x/y坐标问题
- [fix] 升级解析库，修复移动端保存模板vip标识处理问题

## 2024/7/15 [0.2.12]

- [fix] 调整MediaQuery.of(context)，修复替换标签纸撤销恢复问题

## 2024/7/15 [0.2.10]&[0.2.11]

- [fix] 升级ui库。修复pagination的问题
- [fix] 升级解析库。修复toJson时间更新的问题

## 2024/7/12 [0.2.9]

- [fix] 升级解析库（日期校准）。修复保存按钮UI问题

## 2024/7/11 [0.2.7]&[0.2.8]

- [fix] 升级解析库（字距、日期校准）。修复保存按钮UI刷新不及时问题
- [fix] 日期相关多语言样式问题

## 2024/7/10 [0.2.6]

- [fix] 表格合并后样式设置兼容移动端

## 2024/7/9 [0.2.5]

- [fix] 修复升级flutter样式问题

## 2024/7/5 [0.2.4]

- [fix] 升级解析库，修复VIP权益相关bug

## 2024/7/5 [0.2.2]&[0.2.3]

- [upgrade] 升级解析库，修复VIP权益相关bug
- [fix] 修复:修复图片压缩问题

## 2024/7/4 [0.2.1]

- [upgrade] 升级UI库，完成VIP权益相关开发

## 2024/7/1 [0.2.0]

- [upgrade] 升级:Flutter 3.22

## 2024/6/27 [0.1.54]

- [fix] 修复:保存按钮相关修复

## 2024/6/26 [0.1.53]

- [fix] 修复:图片压缩报错问题

## 2024/6/25 [0.1.49]&[0.1.50]&[0.1.51]&[0.1.52]

- [fix] 修复:修复走纸方向左右反向问题
- [fix] 修复:升级图像库 修复打印数据生成图片、图标问题
- [fix] 修复:升级图像库 修复数据源撤销

## 2024/6/24 [0.1.47]&[0.1.48]

- [fix] 修复:旋转等部分异常、升级UI库解析库

## 2024/6/21 [0.1.45]&[0.1.46]

- [fix] 修复:部分异常、升级图像库
- [fix] 修复:图片压缩处理、尾巴长度

## 2024/6/20 [0.1.43]&[0.1.44]

- [fix] 修复:部分异常

## 2024/6/19 [0.1.42]

- [fix] 修复:画板图片支持本地路径;

## 2024/6/18 [0.1.39]&[0.1.40]&[0.1.41]

- [fix] 修复:画板使用相关bug;

## 2024/6/17 [0.1.35]&[0.1.36]&[0.1.37]&[0.1.38]

- [fix] 修复:画板使用相关bug;

## 2024/6/15 [0.1.34]

### [fix] 修复:画板使用相关bug

## 2024/6/14 [0.1.31]&[0.1.32]&[0.1.33]

### [fix] 修复:画板使用相关bug

## 2024/6/12 [0.1.29]&[0.1.30]

### [fix] 修复:画板使用相关bug

## 2024/6/12 [0.1.26]&[0.1.27]&[0.1.28]

### [fix] 修复:画板使用相关bug

## 2024/6/11 [0.1.24]&[0.1.25]

### [fix] 修复:画板使用相关bug

## 2024/6/7 [0.1.22]&[0.1.23]

### [fix] 修复:解析等相关bug修复

## 2024/6/5 [0.1.20]&[0.1.21]

### [fix] 修复:解析等相关bug修复

## 2024/6/3 [0.1.17]&[0.1.18]&[0.1.19]

### [fix] 修复:数据源相关bug

## 2024/6/3 [0.1.15]&[0.1.16]

### [fix] 修复:数据源相关bug

## 2024/5/31 [0.1.14]

### [fix] 修复:标签纸识别后支持颜色、部分已知bug修复

## 2024/5/30 [0.1.13]

### [fix] 修复:部分bug

## 2024/5/23 [0.1.12]

### [fix] 修复:部分bug

## 2024/5/28 [0.1.10]& [0.1.11]

### [fix] 修复:部分bug

## 2024/5/27 [0.1.7] && [0.1.8] && [0.1.9]

### [fix] 修复:多端兼容问题部分bug修复

## 2024/5/23 [0.1.6]

### [fix] 修复:部分bug

## 2024/5/21 [0.1.5]

### [fix] 修复:部分bug

## 2024/5/21 [0.1.4]

### [fix] 修复:部分Model变更导致bug

## 2024/5/21 [0.1.3]

### [fix] 修复:部分bug

## 2024/5/16 [0.1.1]&[0.1.2]

### [fix] 修复:部分bug

## 2024/5/8 [0.1.0]

### [init] 初始化:首次发布
