import 'dart:async';

import 'package:collection/collection.dart';
import 'package:excel/excel.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:netal_plugin/models/copy_wrapper.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:netal_plugin/netal_image_result.dart';
import 'package:netal_plugin/utils/ratio.dart';
import 'package:niimbot_flutter_canvas/business/desktop/utils/canvas_widget_manager_utils.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/excel/excel_utils.dart';
import 'package:niimbot_flutter_canvas/extensions/base_element.dart';
import 'package:niimbot_flutter_canvas/extensions/image_element.dart';
import 'package:niimbot_flutter_canvas/extensions/list.dart';
import 'package:niimbot_flutter_canvas/extensions/map.dart';
import 'package:niimbot_flutter_canvas/extensions/offset.dart';
import 'package:niimbot_flutter_canvas/extensions/rect.dart';
import 'package:niimbot_flutter_canvas/extensions/template_data.dart';
import 'package:niimbot_flutter_canvas/model/element/element_utils.dart';
import 'package:niimbot_flutter_canvas/model/element/style_attr/color_style_attr.dart';
import 'package:niimbot_flutter_canvas/model/template/constants.dart';
import 'package:niimbot_flutter_canvas/model/template/template_utils.dart';
import 'package:niimbot_flutter_canvas/provider/controllers/barcode_controller.dart';
import 'package:niimbot_flutter_canvas/provider/controllers/base_controller.dart';
import 'package:niimbot_flutter_canvas/provider/controllers/data_source_controller.dart';
import 'package:niimbot_flutter_canvas/provider/controllers/image_controller.dart';
import 'package:niimbot_flutter_canvas/provider/controllers/line_controller.dart';
import 'package:niimbot_flutter_canvas/provider/controllers/qrcode_controller.dart';
import 'package:niimbot_flutter_canvas/provider/controllers/table_controller.dart';
import 'package:niimbot_flutter_canvas/provider/controllers/text_controller.dart';
import 'package:niimbot_flutter_canvas/provider/history/history_store.dart';
import 'package:niimbot_flutter_canvas/provider/models/bar_code_element_updater.dart';
import 'package:niimbot_flutter_canvas/provider/models/base_element_updater.dart';
import 'package:niimbot_flutter_canvas/provider/models/canvas_template_data_updater.dart';
import 'package:niimbot_flutter_canvas/provider/models/index.dart';
import 'package:niimbot_flutter_canvas/provider/models/qr_code_element_updater.dart';
import 'package:niimbot_flutter_canvas/provider/models/serial_element_updater.dart';
import 'package:niimbot_flutter_canvas/provider/models/table_element_updater.dart';
import 'package:niimbot_flutter_canvas/provider/models/text_element_updater.dart';
import 'package:niimbot_flutter_canvas/provider/models/value_element_updater.dart';
import 'package:niimbot_flutter_canvas/provider/utils/canvas_store_utils.dart';
import 'package:niimbot_flutter_canvas/provider/utils/store_status_utils.dart';
import 'package:niimbot_flutter_canvas/provider/utils/style_attr_utils.dart';
import 'package:niimbot_flutter_canvas/utils/buried_utils.dart';
import 'package:niimbot_flutter_canvas/utils/template_data_source_utils_extension.dart';
import 'package:niimbot_flutter_canvas/widgets/canvas/canvas_utils.dart';
import 'package:niimbot_http/business/apis/rest/models/material_item.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_template/extensions/template_data.dart';
import 'package:niimbot_template/models/elements/bar_code_element.dart';
import 'package:niimbot_template/models/elements/base_element.dart';
import 'package:niimbot_template/models/elements/bind_element.dart';
import 'package:niimbot_template/models/elements/color_element.dart';
import 'package:niimbot_template/models/elements/date_element.dart';
import 'package:niimbot_template/models/elements/graph_element.dart';
import 'package:niimbot_template/models/elements/image_element.dart';
import 'package:niimbot_template/models/elements/line_element.dart';
import 'package:niimbot_template/models/elements/material_element.dart';
import 'package:niimbot_template/models/elements/qr_code_element.dart';
import 'package:niimbot_template/models/elements/serial_element.dart';
import 'package:niimbot_template/models/elements/table_element.dart';
import 'package:niimbot_template/models/elements/text_element.dart';
import 'package:niimbot_template/models/template/file_data_source.dart';
import 'package:niimbot_template/models/template/file_data_source_rule.dart';
import 'package:niimbot_template/models/template/template_data_source.dart';
import 'package:niimbot_template/models/template/template_data_source_info.dart';
import 'package:niimbot_template/models/template/template_data_source_modify.dart';
import 'package:niimbot_template/models/template_data.dart';
import 'package:niimbot_template/models/values/element_value.dart';
import 'package:niimbot_template/utils/template_data_source_utils.dart';
import 'package:niimbot_ui/model/niimbot_dropdown_model.dart';
import 'package:niimbot_ui/widgets/niimbot_toast/niimbot_loading_controller.dart';
import 'package:uuid/uuid.dart';

final Logger _logger = Logger("CanvasStore", on: kDebugMode);

/// 共享画板数据
class CanvasStore
    extends HistoryManager<(TemplateData, Map<String, NetalImageResult>)> {
  // 裁剪当前选中页
  Future<(List<BaseElement>, List<FileDataSource>, List<String>)>
      clipCurrentPage(final List<BaseElement> list,
          final List<FileDataSource> fileDataSources) async {
    List<FileDataSource> newFileDataSources = fileDataSources;
    List<BaseElement> newElements = [];
    List<String> updateIds = [];
    for (var element in list) {
      if (element is ImageElement) {
        final fileDataSource = fileDataSources.firstWhereOrNull((final val) =>
            val.rule.any((final v) => v.elementId == element.id));
        if (fileDataSource != null) {
          element = element.generateOriginElement(fileDataSources).copyWith(
              localImageUrl:
                  fileDataSource.pageImages[fileDataSource.pageCount - 1]);
        }
        final cropElement = await element.imageElementClipped(fileDataSource);
        updateIds.add(cropElement.id);
        newFileDataSources = newFileDataSources.map((final e) {
          if (element is ImageElement &&
              e.rule.any((final v) => v.elementId == cropElement.id)) {
            return e.copyWith(
                originImage: element.localImageUrl,
                croppedImage: cropElement.localImageUrl);
          }
          return e;
        }).toList();
        newElements.add(cropElement);
      } else {
        if (element.isBindingElement || element is SerialElement) {
          updateIds.add(element.id);
        }
        newElements.add(element);
      }
    }
    return (newElements, newFileDataSources, updateIds);
  }

  ///PDF相关
  ///更新pdf信息
  Future<void> updatePdfInfo(final int page) async {
    TemplateDataSourceInfo? dataSourceBindInfo = canvasData.dataSourceBindInfo;
    if (canvasData.isHaveDataSources && dataSourceBindInfo != null) {
      dataSourceBindInfo = TemplateDataSourceInfo(
          total: dataSourceBindInfo.total,
          page: page > dataSourceBindInfo.total
              ? dataSourceBindInfo.total
              : page);
    }
    final fileDataSources = (canvasData.fileDataSources ?? []).map((final e) {
      return e.copyWith(
          pageCount: page > e.pageImages.length ? e.pageImages.length : page);
    }).toList();
    final (newElements, newFileDataSources, clipIds) =
        await clipCurrentPage(canvasData.elements, fileDataSources);
    super.snapshot(
      (
        canvasData.copyWith(
          dataSourceBindInfo: dataSourceBindInfo,
          fileDataSources: newFileDataSources,
          elements: newElements,
        ),
        currentState.$2.where((final k, final v) {
          return !clipIds.contains(k);
        })
      ),
    );
  }

  /// 选中元素id
  final Map<String, Set<String>> _selectedElementIds = {};

  /// 正在移动的元素id
  final Set<String> _movingElementIds = {};

  /// 正在调整大小的元素id
  String? _resizingElementId;

  bool? _isImageSliderIng;

  bool get isImageSliderIng => _isImageSliderIng ?? false;

  void setImageSliderIng(final bool value) {
    _isImageSliderIng = value;
    if (!value) {
      notifyListeners();
    }
  }

  TemplateData get canvasData => super.currentState.$1;

  TemplateData get originCanvasData =>
      canvasData.originCanvasData(clippingElementId: _clippingElementId);

  /// 视图旋转角度
  double _rotate = 0;

  double get rotate => _rotate;

  /// 是否显示另存为
  bool get showCanvasDataSaveAs => canvasData.id?.isNotEmpty == true;

  /// 是否包含标签纸
  bool get includedLabel =>
      canvasData.profile.extra.labelId?.isEmpty == false &&
      canvasData.labelId?.isEmpty == false;

  /// 是否显示线缆标签
  bool get showCable => includedLabel ? canvasData.isCable : true;

  String? _consumablesColor;

  // bool _isRedAndBlack = false;
  //
  bool get isRedAndBlack {
    if (_consumablesColor == null || _consumablesColor!.isEmpty) {
      return false;
    }
    final colorList = _consumablesColor!.split(',');
    if (colorList.isNotEmpty) {
      return (colorList.length == 2 &&
          colorList[0] == TemplateConstants.blackColorString &&
          colorList[1] == TemplateConstants.redColorString);
    }
    return false;
  }

  /// 获取打印颜色
  Color? get printColor {
    if (isRedAndBlack) {
      // 红黑模式，元素色为黑色
      return TemplateConstants.DEFAULT_PAPER_COLOR.first;
    }
    if (consumablesColor.length == 1) {
      /* 单色耗材 使用耗材颜色 */
      return consumablesColor.first;
    }

    /* 其它情况 使用元素颜色 */
    return null;
  }

  /// 获取耗材颜色
  List<Color> get consumablesColor {
    if (_consumablesColor != null) {
      /* 有色耗材 */
      final colorList = _consumablesColor!.split(',');
      if (colorList.isNotEmpty) {
        bool isRedAndBlack = (colorList.length == 2 &&
            colorList[0] == TemplateConstants.blackColorString &&
            colorList[1] == TemplateConstants.redColorString);

        if (isRedAndBlack) {
          return [TemplateConstants.DEFAULT_PAPER_COLOR.first];
        }
        return colorList
            .map((final e) => ElementUtils.parseColorFromStr(e))
            .toList();
      }
    }

    return TemplateConstants.DEFAULT_PAPER_COLOR;
  }

  /// 耗材材质
  List<NiimbotDropDownModel<int?>>? _consumableList;

  List<NiimbotDropDownModel<int?>> get consumableList => _consumableList ?? [];

  /// 获取标签纸名称
  String get simpleInfo {
    String info =
        "${NiimbotIntl.getIntlMessage('app100000966', '自定义')}-${canvasData.width.toDouble()}X${canvasData.height.toDouble()}";
    if (canvasData.cableLength > 0) {
      info += "+${canvasData.cableLength}";
    }
    String paperTypeName = paperTypeList
            .firstWhereOrNull(
                (final element) => element.value == canvasData.paperType)
            ?.label ??
        NiimbotIntl.getIntlMessage('app01213', '走纸类型');
    info += "-$paperTypeName";
    if (canvasData.profile.extra.labelId?.isNotEmpty == true) {
      info = canvasData.labelNameIntl;
    }
    return info;
  }

  /// 出纸方向
  List<NiimbotDropDownModel<int>>? _paperTypeList;

  List<NiimbotDropDownModel<int>> get paperTypeList => _paperTypeList ?? [];

  List<BaseElement> get _elements => canvasData.elements;

  /// 画板上显示的列表
  List<BaseElement> get canvasElements {
    Map<String, FileDataSource> fileDataSourceMap =
        (canvasData.fileDataSources ?? []).fold({}, (final map, final v) {
      for (var k in v.rule) {
        map[k.elementId] = v;
      }
      return map;
    });
    return _elements.fold<List<BaseElement>>([], (final res, final e) {
      if (e is ImageElement) {
        final fileDataSource = fileDataSourceMap[e.id];
        if (fileDataSource != null &&
            (fileDataSource.type == 'pdf' ||
                (fileDataSource.type == 'image' &&
                    fileDataSource.pageImages.length > 1)) &&
            fileDataSource.pageImages.length < canvasData.currentPageValue) {
          return res;
        }
        return [...res, e];
      }
      final newElement = e.generateElement(canvasData);
      return [
        ...res,
        ...(newElement != null ? [newElement] : [])
      ];
    });
  }

  TemplateDataSourceModifies? get _dataSourceModifies =>
      canvasData.dataSourceModifies;

  /// 本地文件存储相关方法
  final Future<String?> Function(Uint8List data, {String? fileName})?
      _localFileSave;

  Future<String?> Function(Uint8List data, {String? fileName})?
      get localFileSave => _localFileSave;

  ///是否多选
  bool get multiSelect {
    return selectedElement.length > 1;
  }

  /// 选择框
  /// [includeMirror] 是否包含镜像元素
  Rect? getSelectRect({final bool includeMirror = true}) {
    if (focusedElement != null) {
      return focusedElement!.getViewRect(rotate, canvasData.center);
    }
    if (multiSelect) {
      Rect? rect;
      for (var element in selectedElement) {
        if (rect == null) {
          rect = element.getViewRect(rotate, canvasData.center);
        } else {
          rect = rect
              .expandToInclude(element.getViewRect(rotate, canvasData.center));
        }
        if (includeMirror && element.isOpenMirror) {
          /* 如果是镜像元素 */
          final mirrored = ElementUtils.buildMirrorElement(
            element: element,
            templateSize: canvasData.size,
          );
          rect = rect
              .expandToInclude(mirrored.getViewRect(rotate, canvasData.center));
        }
      }
      return rect;
    }
    return null;
  }

  /// 聚焦元素
  BaseElement? get focusedElement {
    final focusedElementId = _selectedElementIds.keys.length == 1
        ? _selectedElementIds.keys.first
        : null;
    return _elements.firstWhereOrNull((final e) => e.id == focusedElementId);
  }

  /// 选中元素
  List<BaseElement> get selectedElement {
    return _elements
        .where((final e) => _selectedElementIds.keys.contains(e.id))
        .toList();
  }

  /// 选中复制元素
  List<BaseElement> get copySelectedElement {
    final Map<String, BaseElement> map = {};
    final List<BaseElement> elements = (selectedElement.length > 1
        ? selectedElement
        : (focusedElement != null ? [focusedElement!] : []));
    for (final e in elements) {
      map[e.id] = e.copyWith();
      if (e is DateElement) {
        if (e.associateId.isNotEmpty) {
          final other = _elements.whereType<DateElement>().firstWhereOrNull(
              (final de) => de.associateId == e.associateId && e.id != de.id);
          if (other != null) {
            map[other.id] = other.copyWith();
          }
        }
      } else if (e is ImageElement) {
        final fileDataSource = canvasData.fileDataSources?.firstWhereOrNull(
            (final item) => item.rule.any((final v) => v.elementId == e.id));
        if (fileDataSource != null) {
          map[e.id] = e.copyWith(
              hasVipRes: false,
              imageProcessingType:
                  e.imageProcessingType == NetalImageRenderType.pdfMode
                      ? NetalImageRenderType.threshold
                      : e.imageProcessingType);
        }
      }
    }
    return map.values.toList();
  }

  /// 选中元素Id
  List<String> get selectedElementIds {
    return selectedElement.map((final e) => e.id).toList();
  }

  /// 是否显示元素属性面板
  bool get showElementAttributePanel {
    if (multiSelect) {
      /* 多选 */
      return selectedElementStyleAttribute != null;
    }
    return focusedElement != null;
  }

  /// 多选元素属性面板类型
  ColorStyleAttribute? get selectedElementStyleAttribute {
    return StyleAttributeUtils.buildSelectedStyleAttribute(selectedElement);
  }

  /// 是否有元素在移动
  bool get isMoving {
    return _elements
        .where((final e) => _movingElementIds.contains(e.id))
        .isNotEmpty;
  }

  /// 是否有元素在移动
  bool get isResizing {
    return _resizingElementId != null;
  }

  List<num> _printArea = [0.0, 0.0, 0.0, 0.0];

  List<num> get printArea => _printArea;

  Rect? get printAreaRect {
    double left = printArea[2].toDouble();
    double top = printArea[0].toDouble();
    double width = (canvasData.size.width - printArea[3] - printArea[2]);
    double height = (canvasData.size.height - printArea[1] - printArea[0]);
    // 健壮性保护，防止 width/height 为0
    if (width <= 0) width = canvasData.size.width;
    if (height <= 0) height = canvasData.size.height;
    return Rect.fromLTWH(left, top, width, height)
        .rotate(-rotate, canvasData.center);
  }

  bool _showPrintArea = false;

  /// 是否显示打印区域
  bool get showPrintArea => _showPrintArea;

  /// 是否正在编辑内容
  String? _editing;

  bool get editing => _editing != null;

  /// 画板视图矩形
  Rect get canvasViewRect {
    return canvasData.getView(rotate);
  }

  /// 用于画板渲染元素
  List<BaseElement> get elements => canvasData.elements
      .map((final e) => ElementUtils.rebuildElementValue(e,
          dataSources: canvasData.dataSources,
          modify: canvasData.dataSourceModifies,
          bindInfo: canvasData.dataSourceBindInfo,
          page: canvasData.dataSourceBindInfo?.page ?? 1))
      .toList();

  late BaseController _baseController;

  BaseController get baseController => _baseController;

  late TableController _tableController;

  TableController get tableController => _tableController;

  late TextController _textController;

  TextController get textController => _textController;

  late BarCodeController _barcodeController;

  BarCodeController get barcodeController => _barcodeController;

  late LineController _lineController;

  LineController get lineController => _lineController;

  late ImageController _imageController;

  ImageController get imageController => _imageController;

  late QRCodeController _qrcodeController;

  QRCodeController get qrcodeController => _qrcodeController;

  late DataSourceController _dataSourceController;

  DataSourceController get dataSourceController => _dataSourceController;
  late List<String> _initElementIds;

  /// 所有元素被生成完成
  bool get initElementsGenerated => _initElementIds.isEmpty;

  /// 拖拽图标后添加到本地使用
  final Function(MaterialItem item, bool isBorder)? _addLocalRecent;

  Function(MaterialItem item, bool isBorder)? get addLocalRecent =>
      _addLocalRecent;

  /// 判断本地图片是否存在并获取图片
  Future<String?> getLoadBackgroundImage({final TemplateData? template}) async {
    if (localFileSave != null) {
      final data = template ?? canvasData;
      return TemplateUtils.getTemplateLoadBackgroundImage(data, localFileSave!);
    }
    return null;
  }

  final String _canvasKey;

  String get canvasKey => _canvasKey;

  CanvasStore({
    required final TemplateData data,
    final List<NiimbotDropDownModel<int?>>? consumableList,
    final List<NiimbotDropDownModel<int>>? paperTypeList,
    final List<num> printArea = const [0.0, 0.0, 0.0, 0.0],
    final String? localFilePath,
    final Future<String?> Function(Uint8List data, {String? fileName})?
        localFileSave,
    final Function(MaterialItem item, bool isBorder)? addLocalRecent,
    final String? consumablesColor,
  })  : _printArea = printArea,
        _localFileSave = localFileSave,
        _addLocalRecent = addLocalRecent,
        _consumablesColor = consumablesColor,
        _canvasKey = const Uuid().v4(),
        super(init: (data, {})) {
    _consumableList = consumableList;
    _paperTypeList = paperTypeList;
    _initElementIds = data.elements.map((final e) => e.id).toList();
    _baseController = BaseController(
      onUpdateElements: _updateElements,
      elements: () => _elements,
      selectedElement: () => selectedElement,
      printArea: () => printAreaRect,
      templateInfo: () => (
        rotate,
        Rect.fromLTWH(0, 0, canvasData.size.width, canvasData.size.height)
      ),
      selectRect: getSelectRect,
      cancelEdit: cancelEdit,
      resizingElement: resizingElement,
    );
    _dataSourceController = DataSourceController(
      dataSourceModifies: _dataSourceModifies,
      updateTemplateDataSourceModifies: _updateTemplateDataSourceModifies,
      updateTemplateDataSource: _updateTemplateDataSource,
      upDateBreakBind: _upDateBreakBind,
    );
    _tableController = TableController(
      onUpdateElements: _updateElements,
      elements: () => _elements,
      dataSourceController: _dataSourceController,
      getSelected: getSelected,
      selectedElementChildren: selectedElementChildren,
    );
    _textController = TextController(
      onUpdateElements: _updateElements,
      onUsedFontsRemoveFont: _usedFontsRemoveFont,
      elements: () => _elements,
      templateSize: () => canvasData.size,
      selectedElement: () => selectedElement,
      selectedElementIds: () => selectedElementIds,
      cancelEdit: cancelEdit,
      addElements: addElements,
      deleteElements: deleteElement,
      runInAction: runInAction,
      templateInfo: () => (
        rotate,
        Rect.fromLTWH(0, 0, canvasData.size.width, canvasData.size.height)
      ),
    );
    _barcodeController = BarCodeController(
        onUpdateElements: _updateElements, elements: _elements);
    _lineController = LineController(
      onUpdateElements: _updateElements,
      elements: () => _elements,
      selectedElement: () => selectedElement,
      selectedElementIds: () => selectedElementIds,
    );
    _imageController =
        ImageController(onUpdateElements: _updateElements, elements: _elements);
    _qrcodeController = QRCodeController(onUpdateElements: _updateElements);
  }

  /// 当前绑定元素显示的值
  String? bindElementValue(final String id) {
    final element = canvasData.elements
        .whereType<BindElement>()
        .firstWhereOrNull((final e) => e.id == id);
    if (element == null) {
      return null;
    }
    if (element.value != null) {
      final value = TemplateDataSourceUtils.getElementBindValue(
        element.id,
        element.value!,
        element.dataBind,
        canvasData.dataSources,
        canvasData.dataSourceModifies,
        canvasData.dataSourceBindInfo?.page ?? 1,
      );
      return value;
    }

    final bindValue = canvasData.values
        ?.whereType<DataSourceTextValue>()
        .firstWhereOrNull((final e) => e.elementId == id);
    if (bindValue != null) {
      final value = TemplateDataSourceUtils.getElementBindValue(
        element.id,
        bindValue.value,
        bindValue.dataBind,
        canvasData.dataSources,
        canvasData.dataSourceModifies,
        canvasData.dataSourceBindInfo?.page ?? 1,
      );
      return value;
    }
    return null;
  }

  bool bindElementUseTitle(final String id) {
    return TemplateDataSourceUtilsExtension.getUseTitle(
        eId: id, modify: canvasData.dataSourceModifies);
  }

  final Map<String, KeepPosition?> _cacheKeepPosition = {};

  /// 更新元素
  /// [history] 是否在撤销恢复时记录 默认记录
  /// [type] 仅创建镜像
  void _updateElements(final Map<String, BaseElementUpdater> update,
      {final SnapshotType type = SnapshotType.create}) {
    if (update.isNotEmpty || type == SnapshotType.ignoreEnd) {
      /* 忽略结束时 无论是否有更新 必须生成一次快照 */
      final Map<String, NetalImageResult> imageCache =
          Map.from(currentState.$2);
      final elements = canvasData.elements.map((final e) {
        final up = update[e.id];
        if (up?.isNotEmpty ?? false) {
          if (up?.imageCache != null) {
            if (up!.imageCache!.value != null) {
              imageCache[e.id] = up.imageCache!.value!;
            } else {
              imageCache.remove(e.id);
            }
          }
          _cacheKeepPosition[e.id] = up?.keepPosition?.value;
          return CanvasStoreUtils.copyByUpdater(
              e, up!, rotate, canvasData.center);
        }
        return e;
      }).toList();
      // if (_clippingElementId == null && _clippedElementIds.isNotEmpty) {
      //   runInActionEnd(() {
      //     _clippedElementIds = [];
      //     super.snapshot((canvasData.copyWith(elements: elements), imageCache),
      //         type: type);
      //   });
      // } else {
      final newCanvasData = canvasData.copyWith(elements: elements);
      super.snapshot((newCanvasData, imageCache), type: type);
      // }
      if (initElementsGenerated && super.history.first.$1 != canvasData) {
        setSaveEnable(true);
      }
    }
  }

  void _usedFontsRemoveFont(final List<String> list) {
    canvasData.usedFonts
        .removeWhere((final key, final value) => list.contains(value));
    notifyListeners();
  }

  ///删除单个元素绑定关系
  void _upDateBreakBind(final String? eId) {
    if (eId == null) {
      return;
    }
    List<BaseElement> afterElements = [];
    List<ElementValue>? afterValues;
    for (var element in canvasData.elements) {
      if (element.id == eId) {
        if (element is BindElement) {
          /// 可以绑定数据源的元素
          if (element.value != null) {
            /// 元素value存在
            final value = TemplateDataSourceUtils.getElementBindValue(
              element.id,
              element.value!,
              element.dataBind,
              canvasData.dataSources,
              canvasData.dataSourceModifies,
              canvasData.dataSourceBindInfo?.page ?? 1,
            );
            afterElements.add((element).copyWith(
                dataBind: const CopyWrapper.value(null), value: value));
            continue;
          } else {
            final bindValue = canvasData.values
                ?.whereType<DataSourceTextValue>()
                .firstWhereOrNull((final e) => e.elementId == eId);
            if (bindValue != null) {
              final value = TemplateDataSourceUtils.getElementBindValue(
                element.id,
                bindValue.value,
                bindValue.dataBind,
                canvasData.dataSources,
                canvasData.dataSourceModifies,
                canvasData.dataSourceBindInfo?.page ?? 1,
              );
              afterElements.add((element).copyWith(
                  dataBind: const CopyWrapper.value(null), value: value));
              afterValues = canvasData.values!
                  .whereNot((final e) => e.elementId == eId)
                  .toList();
              continue;
            }
          }
        }
      }
      afterElements.add(element);
    }
    super.snapshot((
      canvasData.copyWith(elements: afterElements, values: afterValues),
      currentState.$2
    ));
  }

  /// 更新元素数据源修改标记
  void _updateTemplateDataSourceModifies(
      final TemplateDataSourceModifies? dataSourceModifies,
      {final SnapshotType type = SnapshotType.create}) {
    if (dataSourceModifies?.isNotEmpty ?? false) {
      /* 修改不为空是 重绘所有修改元素 */
      final Map<String, BaseElementUpdater> update = {};
      for (var eId in dataSourceModifies!.keys) {
        update[eId] =
            const BaseElementUpdater(imageCache: CopyWrapper.value(null));
      }
      _updateElements(update, type: SnapshotType.ignore);
    }
    super.snapshot((
      canvasData.copyWith(dataSourceModifies: dataSourceModifies),
      currentState.$2
    ), type: SnapshotType.ignore);
  }

  /// 更新数据源
  void _updateTemplateDataSource<T extends BaseElement>(
      final List<TemplateDataSource>? templateDataSource,
      {final int? page,
      final bool? needRefresh,
      final SnapshotType type = SnapshotType.ignore}) {
    ///为null代表置空断开关联操作
    if (templateDataSource == null) {
      _removeTemplateDataSource(type: type);
      return;
    }
    num total = 0;

    ///此处range为空时代表 兼容的数据 range为空 为选择全部
    if ((templateDataSource.first.range ?? []).isEmpty) {
      total = templateDataSource.first.rowData.length - 1;
    } else {
      for (var element in templateDataSource.first.range ?? []) {
        total += element.e - element.s + 1;
      }
    }
    int dataSourcePage = page != null && total >= page ? page : 1;

    TemplateDataSourceInfo dataSourceInfo = TemplateDataSourceInfo(
      total: total.toInt(),
      page: dataSourcePage,
    );

    ///此时代表更换了数据源需要清空掉选择的 需要把上次绑定的元素重新绑定
    if (canvasData.dataSources?.isNotEmpty == true &&
        canvasData.dataSources?.first.hash != null &&
        templateDataSource.isNotEmpty &&
        templateDataSource.first.hash != canvasData.dataSources?.first.hash) {
      _replaceTemplateDataSource(templateDataSource, dataSourceInfo,
          type: type);
      return;
    }
    _createTemplateDataSource(
      templateDataSource,
      dataSourceInfo,
      needRefresh: needRefresh,
      type: type,
    );
  }

  /// 导入数据源
  void _createTemplateDataSource(
    final List<TemplateDataSource> templateDataSource,
    final TemplateDataSourceInfo dataSourceInfo, {
    final bool? needRefresh,
    final SnapshotType type = SnapshotType.ignore,
  }) {
    if (templateDataSource.isNotEmpty &&
        (dataSourceInfo.page != canvasData.dataSourceBindInfo?.page ||
            dataSourceInfo.total != canvasData.dataSourceBindInfo?.total ||
            templateDataSource.first.range !=
                canvasData.dataSources?.first.range ||
            templateDataSource.first.hash !=
                canvasData.dataSources?.first.hash)) {
      /* 总条数发生变化 */
      /* 当前页发生变化 */
      /* 数据范围发生变化 */
      /* 刷新数据 */
      final Map<String, BaseElementUpdater> update = {};
      for (final e in canvasData.elements) {
        if (e is BindElement) {
          if (e.dataBind != null && e.dataBind!.isNotEmpty) {
            if (e.dataBind?[0] == templateDataSource.first.hash) {
              update[e.id] =
                  const BaseElementUpdater(imageCache: CopyWrapper.value(null));
            } else if (needRefresh == true) {
              String sheetName =
                  templateDataSource.first.headers?.keys.first ?? 'Sheet1';

              if (e is TextElement) {
                update[e.id] = TextElementUpdater(
                    dataBind: CopyWrapper.value(
                        [templateDataSource.first.hash, sheetName]));
              } else if (e is QRCodeElement) {
                update[e.id] = QRCodeElementUpdater(
                    dataBind: CopyWrapper.value(
                        [templateDataSource.first.hash, sheetName]));
              } else if (e is BarCodeElement) {
                update[e.id] = BarCodeElementUpdater(
                    dataBind: CopyWrapper.value(
                        [templateDataSource.first.hash, sheetName]));
              }
            }
          } else if (e is SerialElement) {
            update[e.id] =
                const SerialElementUpdater(imageCache: CopyWrapper.value(null));
          }
        }
      }
      _updateElements(update, type: SnapshotType.ignore);
    }
    super.snapshot(
      (
        canvasData.copyWith(
          dataSourceBindInfo: dataSourceInfo,
          dataSources: templateDataSource,
          fileDataSources: canvasData.fileDataSources
              ?.map((final e) => e.copyWith(pageCount: 1))
              .toList(),
        ),
        currentState.$2
      ),
      type: type,
    );
  }

  /// 取消数据源关联
  void _removeTemplateDataSource(
      {final SnapshotType type = SnapshotType.ignore}) {
    List<BaseElement> afterElements = [];
    for (var element in elements) {
      if ((element is TextElement) && (element).dataBind != null) {
        afterElements
            .add((element).copyWith(dataBind: const CopyWrapper.value(null)));
      } else if ((element is QRCodeElement) && (element).dataBind != null) {
        afterElements
            .add((element).copyWith(dataBind: const CopyWrapper.value(null)));
      } else if ((element is BarCodeElement) && (element).dataBind != null) {
        afterElements
            .add(element.copyWith(dataBind: const CopyWrapper.value(null)));
      } else {
        afterElements.add(element);
      }
    }
    super.snapshot(
      (
        canvasData.copyWith(
            dataSources: null,
            dataSourceBindInfo: null,
            dataSourceModifies: null,
            elements: afterElements),
        currentState.$2
      ),
      type: type,
    );
  }

  /// 替换数据源关联
  void _replaceTemplateDataSource(
      final List<TemplateDataSource> templateDataSource,
      final TemplateDataSourceInfo dataSourceInfo,
      {final SnapshotType type = SnapshotType.ignore}) {
    final dataSource = templateDataSource.first;
    String sheetName = dataSource.headers?.keys.first ?? 'Sheet1';
    Map<String, Map<String, TemplateDataSourceModify>>
        templateDataSourceModifyUpdate = {};

    /// 列名勾选上
    final isSelect = CanvasWidgetManager().getExcelSelectColumn();
    final templateDataSourceModify =
        TemplateDataSourceModify(useTitle: isSelect);
    final Set<String> removeImageCache = {};
    final List<BaseElement> baseElementData = [];
    List<ElementValue>? afterValues;
    for (final e in canvasData.elements) {
      if (((e is TextElement) ||
              (e is BarCodeElement) ||
              (e is QRCodeElement)) &&
          e.isBindingElement &&
          (e is BindElement)) {
        if (e.dataBind?.isNotEmpty == true && e.value != null) {
          int columnIndex = CellIndex.indexByString(e.value!).columnIndex;
          if (!ExcelUtils.isEmptyColumn(columnIndex, dataSource)) {
            baseElementData.add(e.copyWith(
              dataBind: CopyWrapper.value([dataSource.hash, sheetName]),
            ));
            removeImageCache.add(e.id);
            final useTitle = e is TextElement;
            templateDataSourceModifyUpdate[e.id] = {
              "0": useTitle
                  ? templateDataSourceModify
                  : TemplateDataSourceModify(useTitle: false)
            };
            continue;
          }
        }
        final bindValue = canvasData.values
            ?.whereType<DataSourceTextValue>()
            .firstWhereOrNull((final e) => e.elementId == e.id);
        if (bindValue != null) {
          int columnIndex =
              CellIndex.indexByString(bindValue.value).columnIndex;
          if (!ExcelUtils.isEmptyColumn(columnIndex, dataSource)) {
            baseElementData.add(e);
            removeImageCache.add(e.id);
            templateDataSourceModifyUpdate[e.id] = {
              "0": templateDataSourceModify
            };
            afterValues = canvasData.values?.replaceBy((final e) {
              if (e is DataSourceTextValue && e.elementId == e.id) {
                return bindValue
                    .copyWith(dataBind: [dataSource.hash, sheetName]);
              }
              return null;
            });
            continue;
          }
        }
      } else {
        baseElementData.add(e);
      }
    }
    BuriedUtils().track('click', '007_015_065');
    super.snapshot((
      canvasData.copyWith(
        elements: baseElementData,
        dataSourceModifies: templateDataSourceModifyUpdate,
        dataSourceBindInfo: dataSourceInfo,
        dataSources: templateDataSource,
        values: afterValues,
        fileDataSources: canvasData.fileDataSources
            ?.map((final e) => e.copyWith(pageCount: 1))
            .toList(),
      ),
      currentState.$2.where((final k, final v) {
        return !removeImageCache.contains(k);
      })
    ), type: type);
  }

  /// 更新打印区域
  void updatePrintArea(final List<num> data) {
    final same = const DeepCollectionEquality().equals(data, printArea);
    if (!same) {
      _printArea = data;
      notifyListeners();
    }
  }

  /// 更新耗材类型
  void updateConsumablesType(final int type) {
    super.snapshot(
      (canvasData.copyWith(consumableType: type), currentState.$2),
      type: SnapshotType.replace,
    );
  }

  /// 更新模板打印颜色
  void updateConsumablesPrintColor(final String? colorList,
      {final SnapshotType type = SnapshotType.replace}) {
    final List<String> newColors = (colorList != null
        ? colorList.split(',')
        : [
            TemplateConstants.blackColorString,
            TemplateConstants.redColorString
          ]);
    final List<String> oldColors = _consumablesColor?.split(',') ??
        [TemplateConstants.blackColorString, TemplateConstants.redColorString];
    final sameContains =
        const DeepCollectionEquality().equals(newColors, oldColors)
            ? true
            : oldColors.any((final e) => newColors.contains(e));
    _consumablesColor = colorList;
    // super.snapshot((canvasData, {}), type: type);
    super.snapshot((canvasData, sameContains ? currentState.$2 : {}),
        type: type);
  }

  /// 更新元素值
  void updateElementsValues(final Map<String, ValueElementUpdater> update,
      {final SnapshotType type = SnapshotType.create}) {
    _updateElements(update, type: type);
  }

  /// 更新元素颜色
  void updateElementsColor(final Color color) {
    final elements = canvasData.elements;
    if (elements.isEmpty) {
      _logger.log('updateElementsColor: 没有元素需要更新颜色');
      return;
    }

    final Map<String, ValueElementUpdater> updateMap = {};
    for (final element in elements) {
      // 只更新支持颜色的元素类型
      if (element is ColorElement) {
        final updater = ValueElementUpdater(
          elementColor: color,
          imageCache: CopyWrapper.value(null),
        );

        updateMap[element.id] = updater;
        _logger.log(
            'updateElementsColor:updater=${updater.toString()} updateMap=$updateMap');
      }
    }

    if (updateMap.isEmpty) {
      _logger.log('updateElementsColor: 没有支持颜色的元素');
      return;
    }

    _logger.log('updateElementsColor: 更新${updateMap.length}个元素颜色为$color');

    // 使用 create 类型确保UI更新，并通知监听器
    _updateElements(updateMap, type: SnapshotType.ignore);
  }

  /// 更新元素信息通过图像库返回信息
  void updateElementNetalInfo<T extends BaseElementUpdater>({
    final NetalImageResult? image,
    required final String id,
    final T? updater,
  }) {
    final keepPosition = _cacheKeepPosition[id];
    if (updater != null) {
      _updateElements({
        id: updater.copyWith(
            imageCache: CopyWrapper.value(image),
            keepPosition: CopyWrapper.value(keepPosition)),
      }, type: SnapshotType.replace);
    } else {
      _updateElements({
        id: BaseElementUpdater(
            imageCache: CopyWrapper.value(image),
            keepPosition: CopyWrapper.value(keepPosition)),
      }, type: SnapshotType.replace);
    }
    _cacheKeepPosition.remove(id);
    if (_initElementIds.isNotEmpty) {
      _initElementIds = _initElementIds.whereNot((final e) => e == id).toList();
    }
  }

  /// 旋转标签纸(模板)/视图 清掉之前的历史记录
  void rotateTemplate({final bool isView = false}) async {
    ///此处需求 不清掉选中元素
    if (isView) {
      _rotate = (_rotate + 270) % 360;
      notifyListeners();
    } else {
      NetalCableDirection? cableDirection;
      if (canvasData.cableDirection != null) {
        cableDirection = NetalCableDirection.byValue(
                (canvasData.cableDirection!.value + 1) % 4) ??
            NetalCableDirection.top;
      }
      final newCanvasData = canvasData.copyWith(
        width: canvasData.size.height,
        height: canvasData.size.width,
        canvasRotate: (canvasData.canvasRotate + 90) % 360,
        rotate: (canvasData.rotate + 270) % 360,
        cableDirection: cableDirection,
      );
      await getLoadBackgroundImage(template: newCanvasData);
      super
          .snapshot((newCanvasData, currentState.$2), type: SnapshotType.reset);
      setSaveEnable(true);
    }
  }

  /// 构建元素附带颜色
  List<BaseElement> _buildElementsWithColor<T extends BaseElement>(
      final List<T> list) {
    /// 2025.7.15 解决反白异常

    return list;
    // return list.map((final e) {
    //   if (e is ColorElement &&
    //       !consumablesColor.any((final v) => v == e.elementColor)) {
    //     return e.copyWith(elementColor: consumablesColor.first);
    //   } else if (e is TableElement &&
    //       (!consumablesColor.any((final v) => v == e.lineColor) ||
    //           !consumablesColor.any((final v) => v == e.contentColor))) {
    //     return e.copyWith(
    //         lineColor: consumablesColor.first,
    //         contentColor: consumablesColor.first);
    //   } else {
    //     return e;
    //   }
    // }).toList();
  }

  _addBuried(final BaseElement element, final bool isClick, {final int? type}) {
    final Map<String, dynamic> ext = type == null ? {} : {'type': type};
    if (element is MaterialElement) {
      bool hasVipRes = element.hasVipRes;
      ext.addAll(
          {'is_vip': hasVipRes ? 1 : 0, 'material_id': element.materialId});
      if (element.materialType.value == 2) {
        BuriedUtils()
            .track(isClick ? 'click' : 'drag', '007_014_022', ext: ext);
      }
      if (element.materialType.value == 1) {
        BuriedUtils()
            .track(isClick ? 'click' : 'drag', '007_014_024', ext: ext);
      }
    } else if (element is ImageElement) {
      BuriedUtils().track(isClick ? 'click' : 'drag', '007_014_023', ext: ext);
    } else if (element is BarCodeElement) {
      BuriedUtils().track(isClick ? 'click' : 'drag', '007_014_025', ext: ext);
    } else if (element is QRCodeElement) {
      BuriedUtils().track(isClick ? 'click' : 'drag', '007_014_027', ext: ext);
    } else if (element is DateElement) {
      bool hasVipRes = element.hasVipRes;
      ext.addAll({'is_vip': hasVipRes ? 1 : 0});
      BuriedUtils().track(isClick ? 'click' : 'drag', '007_014_029', ext: ext);
    } else if (element is TableElement) {
      BuriedUtils().track(isClick ? 'click' : 'drag', '007_014_031', ext: ext);
    } else if (element is GraphElement) {
      BuriedUtils().track(isClick ? 'click' : 'drag', '007_014_035', ext: ext);
    } else if (element is LineElement) {
      BuriedUtils().track(isClick ? 'click' : 'drag', '007_014_033', ext: ext);
    } else if (element is SerialElement) {
      BuriedUtils().track(isClick ? 'click' : 'drag', '007_014_037', ext: ext);
    } else if (element is TextElement) {
      BuriedUtils().track(isClick ? 'click' : 'drag', '007_014_021', ext: ext);
    }
  }

  /// 构建添加元素默认坐标
  Offset _buildAddDefaultOffset(
      final Offset? offset, final bool isViewOffset, final bool isAddImage) {
    if (offset == null) {
      if (elements.isNotEmpty) {
        var last = Offset(0, 0);
        for (final element in elements) {
          final rect = element.getViewRect(rotate, canvasData.center);
          if (canvasViewRect.overlaps(rect)) {
            /* 在画布之内 */
            if (rect.bottom >= last.dy || rect.left >= last.dx) {
              last = rect.bottomLeft;
            }
          }
        }
        return last;
      }
      return isViewOffset
          ? Offset(canvasViewRect.left + (isAddImage ? 0 : 1),
              canvasViewRect.top + (isAddImage ? 0 : 1))
          : Offset(isAddImage ? 0 : 1, isAddImage ? 0 : 1);
    }
    return offset;
  }

  /// 添加元素 到指定坐标
  /// [offset] 坐标 默认为视图中心点
  /// [isViewOffset] 是否视图坐标 默认为模板坐标
  void addElements<T extends BaseElement>(
    final List<T> list, {
    final Offset? offset,
    final SnapshotType type = SnapshotType.create,
    final bool ignore = true,
    final bool isClick = true,
    final bool isViewOffset = true,
    final bool isAddBorder = false,
    final bool isAddImage = false,
    final FileDataSource? fileDataSource,
  }) {
    clearSelected();
    cancelEdit();
    var pos = _buildAddDefaultOffset(offset, isViewOffset, isAddImage);
    if (!isViewOffset) {
      /// 元素排序复制粘贴时相对原位置偏移
      list.sort((final a, final b) =>
          a.x.toInt() * a.y.toInt() - b.x.toInt() * b.y.toInt());
      if (offset != Offset.zero) {
        pos = pos - Offset(list.first.x.toDouble(), list.first.y.toDouble());
      }
    }
    final colorElements = _buildElementsWithColor(list);
    for (var e in colorElements) {
      _selectedElementIds[e.id] = {};
    }
    List<BaseElement> elements;
    if (isAddImage || isAddBorder) {
      elements = [
        ...generateTempList(colorElements, isClick, isViewOffset, pos,
            isAddImage || isAddBorder),
        ...canvasData.elements,
      ];
    } else {
      elements = [
        ...canvasData.elements,
        ...generateTempList(colorElements, isClick, isViewOffset, pos,
            isAddImage || isAddBorder)
      ];
    }
    if (fileDataSource == null) {
      super.snapshot((canvasData.copyWith(elements: elements), currentState.$2),
          type: type, ignore: ignore);
      return;
    }
    List<FileDataSource>? tempFileDataSources = canvasData.fileDataSources;
    tempFileDataSources ??= [];
    tempFileDataSources.add(fileDataSource);
    super.snapshot((
      canvasData.copyWith(
        elements: elements,
        fileDataSources: tempFileDataSources.map((final e) {
          return e.copyWith(pageCount: 1);
        }).toList(),
        dataSourceBindInfo: canvasData.dataSourceBindInfo != null
            ? TemplateDataSourceInfo(
                total: canvasData.dataSourceBindInfo!.total,
                page: 1,
              )
            : null,
      ),
      currentState.$2,
    ), type: type, ignore: ignore);
  }

  ///生成list
  List<BaseElement> generateTempList(
      final List<BaseElement> colorElements,
      final bool isClick,
      final bool isViewOffset,
      final Offset position,
      final bool isAddImage) {
    return [
      ...colorElements.map((final e) {
        _addBuried(e, isClick, type: e is MaterialElement ? 1 : null);
        final pos = !isAddImage
            ? position
            : Offset(((canvasData.width - e.width) / 2).digits(6).toDouble(),
                ((canvasData.height - e.height) / 2).digits(6).toDouble());
        if (isViewOffset) {
          final afterRotate = (rotate.toDouble() + e.rotate) % 360;
          return CanvasStoreUtils.copyByUpdater(
              e.copyWith(rotate: afterRotate.toInt()),
              BaseElementUpdater(dx: pos.dx, dy: pos.dy),
              rotate,
              canvasData.center);
        }
        return e.copyWith(x: e.x + pos.dx, y: e.y + pos.dy);
      })
    ];
  }

  /// 复制元素
  void copyElements() {
    if (copySelectedElement.isNotEmpty) {
      ///复制元素时 添加本身的数据源值
      Map<String, dynamic> valueMap = {};
      for (final e in copySelectedElement) {
        if (((e is TextElement) ||
                (e is BarCodeElement) ||
                (e is QRCodeElement)) &&
            e.isBindingElement) {
          valueMap.addAll({e.id: bindElementValue(e.id)});
        }
      }
      CanvasUtils.copyElementToClipboard(copySelectedElement,
          valueMap: valueMap, canvasKey: _canvasKey);
    }
  }

  /// 粘贴元素
  /// [offset] 视图坐标
  void pasteElements({final Offset? offset}) async {
    final (elements, isContent) = await CanvasUtils.getElementFromClipboard(
      canvasSize: canvasData.size,
      canvasKey: _canvasKey,
      localFileSave: localFileSave,
    );
    if (elements.isNotEmpty) {
      var offsetMm = offset;
      if (offsetMm != null && !isContent) {
        offsetMm = offsetMm.rotate(canvasData.center, rotate);
        final rect = elements.getRect(
            rotate: rotate,
            center: canvasData.center,
            templateSize: canvasData.size);
        if (rotate == 90) {
          offsetMm = offsetMm + Offset(-rect.height, 0);
        } else if (rotate == 180) {
          offsetMm = offsetMm + Offset(-rect.width, -rect.height);
        } else if (rotate == 270) {
          offsetMm = offsetMm + Offset(0, -rect.width);
        }
      }
      addElements(elements, offset: offsetMm, isViewOffset: isContent);
    }
  }

  ///删除元素
  void deleteElement(
      {final List<String>? ids,
      final SnapshotType type = SnapshotType.create}) {
    List<String> delIds = ids ??
        (selectedElementIds.length > 1
            ? selectedElementIds
            : (focusedElement != null ? [focusedElement!.id] : []));
    if (delIds.contains(_clippingElementId)) {
      _clippingElementId = null;
    }

    if (delIds.isNotEmpty) {
      setSaveEnable(true, notify: false);
      final deleteList = canvasData.elements
          .where((final e) => delIds.contains(e.id))
          .toList();
      var list = canvasData.elements
          .whereNot((final e) => delIds.contains(e.id))
          .toList();
      for (var element in deleteList) {
        if (element is DateElement) {
          if ((element).isAssociateOrigin) {
            /* 关联元素的源元素 同时删除关联元素 */
            list.removeWhere((final e) =>
                (e is DateElement) && (e).associateId == (element).associateId);
          }
          if ((element).isAssociate) {
            /* 关联元素 更新源元素数据 */
            list = list.map((final e) {
              if (e is DateElement &&
                  (e).associateId == (element).associateId) {
                return e.copyWith(
                  associateId: const CopyWrapper.value(null),
                  associated: false,
                  validityPeriod: const CopyWrapper.value(null),
                  validityPeriodUnit: const CopyWrapper.value(null),
                );
              } else {
                return e;
              }
            }).toList();
          }
        }
      }

      ///此处代表删除了pdf的那张图
      final fileDataSources = canvasData.fileDataSources
          ?.fold<List<FileDataSource>>([], (final res, final element) {
        if (!element.rule.any((final v) => delIds.contains(v.elementId))) {
          res.add(element);
        }
        return res;
      });
      super.snapshot((
        canvasData.copyWith(
          elements: list,
          fileDataSources:
              fileDataSources?.isNotEmpty ?? false ? fileDataSources : null,
        ),
        currentState.$2.where((final k, final v) {
          return !delIds.contains(k);
        })
      ), type: type);
    }
  }

  /// 替换元素
  void replaceElement<T extends BaseElement>(
      final T oldElement, final T newElement,
      {final bool ignore = true, final bool isClick = true}) {
    clearSelected();
    List<BaseElement> elements = [..._elements];
    int index = elements.map((final e) => e.id).toList().indexOf(oldElement.id);
    if (index != -1) {
      elements.removeAt(index);
      final newElementList = _buildElementsWithColor([newElement]);
      for (var e in newElementList) {
        _selectedElementIds[e.id] = {};
      }
      elements.insert(index, newElementList.first);
      if (newElementList.first is MaterialElement) {
        _addBuried(newElementList.first, isClick,
            type: newElementList.first is MaterialElement ? 2 : null);
      }
      super.snapshot((
        canvasData.copyWith(elements: elements),
        currentState.$2.where((final k, final v) {
          return k != oldElement.id;
        })
      ), ignore: false);
    }
    if ((newElement is BindElement) &&
        (newElement as BindElement).dataBind != null) {
      final Map<String, BaseElementUpdater> update = {};
      update[newElement.id] =
          const BaseElementUpdater(imageCache: CopyWrapper.value(null));
      _updateElements(update, type: SnapshotType.replace);
    }
  }

  /// 批量替换
  void batchReplaceDataSource<T extends List<BaseElement>>(final T elements) {
    super.snapshot((canvasData.copyWith(elements: elements), currentState.$2),
        type: SnapshotType.ignoreEnd);
  }

  /// 更新模板背景图
  void updateTemplateBackgroundImage(final int index) {
    super.snapshot(
        (canvasData.copyWith(multipleBackIndex: index), currentState.$2),
        type: SnapshotType.replace);
  }

  /// 更新模板大小 不计入撤销恢复
  void updateTemplateSize({final num? width, final num? height}) {
    if (width != canvasData.width) {
      /* 宽度发生变化 刷新所有文本元素 */
      final Set<String> refreshIds = {};
      final elements = canvasData.elements.map((final e) {
        if (e is TextElement) {
          refreshIds.add(e.id);
          return CanvasStoreUtils.copyByUpdater(
              e, const BaseElementUpdater(), rotate, canvasData.center);
        }
        return e;
      }).toList();

      super.snapshot((
        canvasData.copyWith(
            width: width ?? canvasData.width,
            height: height ?? canvasData.height,
            elements: elements),
        currentState.$2.where((final k, final v) {
          return !refreshIds.contains(k);
        })
      ), type: SnapshotType.reset);
    } else {
      super.snapshot((
        canvasData.copyWith(
            width: width ?? canvasData.width,
            height: height ?? canvasData.height),
        currentState.$2
      ), type: SnapshotType.reset);
    }
  }

  /// 更新纸张类型
  void updateTemplatePaperType(final int type) {
    super.snapshot((canvasData.copyWith(paperType: type), currentState.$2),
        type: SnapshotType.replace);
  }

  /// 更新模板是否有尾巴
  void updateTemplateIsCable(final bool isCable) {
    super.snapshot((
      canvasData.copyWith(
        isCable: isCable,
        cableDirection: isCable ? NetalCableDirection.top : null,
      ),
      currentState.$2
    ), type: SnapshotType.replace);
  }

  /// 更新模板数据
  void updateCanvasTemplate(
    final CanvasTemplateDataUpdater updater, {
    final bool saveEnable = true,
    final List<NiimbotDropDownModel<int?>>? consumableList,
    final List<NiimbotDropDownModel<int>>? paperTypeList,
    final List<String> needChangeNullList = const [],
  }) {
    setSaveEnable(saveEnable, notify: false);
    if (consumableList != null) _consumableList = consumableList;
    if (paperTypeList != null) _paperTypeList = paperTypeList;
    final tempData = canvasData.copyWith();
    var template = canvasData.copyWith(
      id: updater.id ?? tempData.id,
      name: updater.name ?? tempData.name,
      cloudTemplateId: updater.cloudTemplateId ?? tempData.cloudTemplateId,
      names: updater.names ?? tempData.names,
      description: updater.description ?? tempData.description,
      thumbnail: updater.thumbnail ?? tempData.thumbnail,
      backgroundImage: updater.backgroundImage ?? tempData.backgroundImage,
      multipleBackIndex:
          updater.multipleBackIndex ?? tempData.multipleBackIndex,
      width: updater.width ?? tempData.width,
      height: updater.height ?? tempData.height,
      rotate: updater.rotate ?? tempData.rotate,
      canvasRotate: updater.canvasRotate ?? tempData.canvasRotate,
      consumableType: updater.consumableType ?? tempData.consumableType,
      paperType: updater.paperType ?? tempData.paperType,
      isCable: updater.isCable ?? tempData.isCable,
      cableLength: updater.cableLength ?? tempData.cableLength,
      cableDirection: updater.cableDirection ?? tempData.cableDirection,
      margin: updater.margin ?? tempData.margin,
      elements: updater.elements ?? tempData.elements,
      labelId: updater.labelId ?? tempData.labelId,
      accuracyName: updater.accuracyName ?? tempData.accuracyName,
      hasVipRes: updater.hasVipRes ?? tempData.hasVipRes,
      vip: updater.vip ?? tempData.vip,
      commodityTemplate:
          updater.commodityTemplate ?? tempData.commodityTemplate,
      dataSourceModifies: needChangeNullList.contains('dataSourceModifies')
          ? null
          : updater.dataSourceModifies ?? tempData.dataSourceModifies,
      dataSources: needChangeNullList.contains('dataSources')
          ? null
          : updater.dataSources ?? tempData.dataSources,
      dataSourceBindInfo: needChangeNullList.contains('dataSourceBindInfo')
          ? null
          : updater.dataSourceBindInfo ?? tempData.dataSourceBindInfo,
      profile: updater.profile ?? tempData.profile,
      fileDataSources: updater.fileDataSources ?? tempData.fileDataSources,
    );
    if (updater.labelNames != null) {
      template = template.copyWith(labelNames: updater.labelNames!);
    }
    if (updater.localBackground != null) {
      template = template.copyWith(localBackground: updater.localBackground!);
    }
    super.snapshot((template, currentState.$2), type: SnapshotType.replace);
  }

  /// 更新模板尾巴方向
  void updateTemplateCableDirection(final NetalCableDirection direction) {
    super.snapshot(
        (canvasData.copyWith(cableDirection: direction), currentState.$2),
        type: SnapshotType.replace);
  }

  /// 更新模板尾巴长度
  void updateTemplateCableLength(final num length) {
    super.snapshot((canvasData.copyWith(cableLength: length), currentState.$2),
        type: SnapshotType.replace);
  }

  /// 更新模板名称
  void updateTemplateName(final String name) {
    super.snapshot((canvasData.copyWith(name: name), currentState.$2),
        type: SnapshotType.replace);
  }

  /// 更新是否显示模板打印区域
  void updateShowTemplatePrintArea(final bool show) {
    _showPrintArea = show;
    notifyListeners();
  }

  /// 置顶/置底 选中元素
  void updateSelectedElementsZIndex(final ZIndexType type) {
    final List<String> selected = selectedElementIds.length > 1
        ? selectedElementIds
        : (focusedElement != null ? [focusedElement!.id] : []);
    if (selected.isNotEmpty) {
      /// 所有非选中的元素
      final List<BaseElement> unSelected = List.from(
          canvasData.elements.whereNot((final e) => selected.contains(e.id)));
      final selectedElements =
          canvasData.elements.where((final e) => selected.contains(e.id));
      final List<BaseElement> list = [];
      if (type == ZIndexType.Top) {
        /* 置顶 */
        list.addAll(unSelected);
        list.addAll(List.from(selectedElements));
      } else {
        /* 置底 */
        list.addAll(List.from(selectedElements));
        list.addAll(unSelected);
      }
      super.snapshot((canvasData.copyWith(elements: list), currentState.$2));
    }
  }

  /// 更新标签纸
  void updateTemplateLabel(
      final TemplateData data,
      final List<NiimbotDropDownModel<int?>>? consumable,
      final List<NiimbotDropDownModel<int>>? paperType) {
    _consumableList = consumable;
    _paperTypeList = paperType;
    final profile = data.profile.copyWith(
        extra: data.profile.extra
            .copyWith(folderId: canvasData.profile.extra.folderId));
    super.snapshot((
      data.copyWith(
        id: canvasData.id,
        name: canvasData.name,
        elements: canvasData.elements,
        dataSourceModifies: canvasData.dataSourceModifies,
        dataSourceBindInfo: canvasData.dataSourceBindInfo,
        dataSources: canvasData.dataSources,
        profile: profile,
      ),
      currentState.$2
    ), type: SnapshotType.create);
  }

  /// 开始编辑
  void startEdit(final String id) {
    if (id == _editing) return;
    cancelEdit();
    Future.delayed(const Duration(milliseconds: 100), () {
      _editing = id;
      notifyListeners();
    });
  }

  /// 取消编辑
  void cancelEdit() {
    _editing = null;
    notifyListeners();
  }

  /// 自适应标签纸大小
  void tableAutoLabelSize(final String id) {
    final table = _elements
        .whereType<TableElement>()
        .firstWhereOrNull((final e) => e.id == id);
    if (table == null) return;
    final afterRotate = ((360 - rotate.toDouble()) + table.rotate) % 360;
    var width = (printAreaRect?.width ?? canvasViewRect.width) - 0.5;
    var height = (printAreaRect?.height ?? canvasViewRect.height) - 0.5;
    if (afterRotate == 90 || afterRotate == 270) {
      final temp = width;
      width = height;
      height = temp;
    }
    runInAction(() {
      _updateElements({
        table.id: TableElementUpdater(
          width: width,
          height: height,
        )
      });
      _updateElements({
        table.id: TableElementUpdater(
          dx: (printAreaRect?.left ?? 0.5) + 0.5,
          dy: (printAreaRect?.top ?? 0.5) + 0.5,
        )
      });
    });
  }

  /// 清空选中元素
  void clearSelected() {
    _selectedElementIds.clear();
    notifyListeners();
  }

  /// 聚焦元素
  void focusElement(final String id, {final String? child}) {
    _selectedElementIds.clear();
    _selectedElementIds[id] = child != null ? {child} : {};
    notifyListeners();
  }

  /// 批量选中元素
  void selectedElements(final List<String> ids) {
    _selectedElementIds.clear();
    for (var id in ids) {
      final element = _elements.firstWhereOrNull((final e) => e.id == id);
      if (!(element?.isLock ?? false)) {
        /* 锁定元素不能选中 */
        _selectedElementIds[id] = {};
      }
    }
    notifyListeners();
  }

  /// 批量选中元素子元素
  void selectedElementChildren(final String id, final Set<String> ids) {
    _selectedElementIds[id] = ids;
    notifyListeners();
  }

  /// 清空选中元素的子元素
  void clearElementChildrenSelected(final String id) {
    _selectedElementIds[id] = {};
    notifyListeners();
  }

  ///移动选中元素
  void moveSelectedElements(final Offset offset) {
    final selected =
        selectedElement.where((final element) => !element.isLock).toList();
    final update = selected.asMap().map((final k, final v) {
      final rect = v.getViewRect(rotate, canvasData.center);
      return MapEntry(v.id, rect.translate(offset.dx, offset.dy).topLeft);
    });
    movingElements(update);
    Future.delayed(Duration(seconds: 1), () {
      movedElements();
    });
  }

  /// 移动元素(基于视图)
  void movingElements(final Map<String, Offset> update) {
    _movingElementIds.clear();
    cancelEdit();
    if (update.values.length == 1) {
      /* 拖动一个元素时 清除选中 */
      clearSelected();
    }
    final Map<String, BaseElementUpdater> updated = {};
    for (var id in update.keys) {
      final element = _elements.firstWhereOrNull((final e) => e.id == id);
      if (!(element?.isLock ?? false)) {
        /* 锁定元素不能拖动 */
        final du = update[id];
        if (du != null) {
          _selectedElementIds[id] = {};
          _movingElementIds.add(id);
          updated[id] = BaseElementUpdater(
            dx: du.dx,
            dy: du.dy,
          );
        }
      }
    }
    _updateElements(updated, type: SnapshotType.ignore);
  }

  /// 移动元素完成
  void movedElements() {
    _movingElementIds.clear();
    _resizingElementId = null;
    notifyListeners();
  }

  /// 开始调整元素大小
  void resizingElement(final String id) {
    _resizingElementId = id;
  }

  /// 全选
  void selectAll() {
    selectedElements(_elements.map((final e) => e.id).toList());
  }

  /// 获取选中信息
  Set<String> getSelected(final String id) {
    return _selectedElementIds[id] ?? {};
  }

  /// 判断元素是否选中
  bool getIsSelected(final String id) {
    return _selectedElementIds.containsKey(id);
  }

  /// 判断元素是否选中
  bool getAssociateIsSelected(final String id) {
    final element = _selectedElementIds.values.firstWhereOrNull((final d) {
      return d.contains(id);
    });
    return element != null;
  }

  /// 判断元素是否选中
  bool getIsFocus(final String id) {
    return focusedElement?.id == (id);
  }

  /// 获取图片结果
  NetalImageResult? getElementImage(final String id) {
    return currentState.$2[id];
  }

  /// 获取元素值
  ElementValue? getElementValue(final String id) {
    return canvasData.values?.firstWhereOrNull((final e) => e.elementId == id);
  }

  // 正在剪裁的元素id
  String? _clippingElementId;

  String? get clippingElementId => _clippingElementId;

  List<String> _clippedElementIds = [];

  /// 获取剪裁区域根据元素id
  Rect? _getClippedRect(final String? id) {
    return canvasData.fileDataSources
        ?.firstWhereOrNull(
            (final element) => element.rule.any((final e) => e.elementId == id))
        ?.crop;
  }

  // 获取当前正在剪裁区域
  Rect? get clippingRect {
    return _getClippedRect(_clippingElementId);
  }

  /// 判断元素是否正在剪裁
  bool getIsClipping(final String id) {
    return _clippingElementId == id;
  }

  /// 进入剪裁图片元素模式
  void updateElementsCrop() {
    if (!StoreStatusUtils.canCrop(selectedElement)) {
      return;
    }
    bool isPdf = (canvasData.pdfInfoModel != null &&
        canvasData.pdfInfoModel!.rule
            .any((final e) => e.elementId == selectedElement.first.id));
    BuriedUtils().track('click', '007_051_067', ext: {'source': isPdf ? 2 : 1});

    /// 剪裁元素id
    final clipElementId = _selectedElementIds.keys.firstOrNull;
    if (_clippingElementId == clipElementId) {
      clippedElement();
      return;
    }

    final List<BaseElement> newElements = [];
    BaseElement? clippingElement;
    _clippedElementIds = [];
    for (var e in _elements) {
      _clippedElementIds.add(e.id);
      if (e is ImageElement && e.id == clipElementId) {
        clippingElement = e.generateOriginElement(canvasData.fileDataSources);
      } else {
        newElements.add(e);
      }
    }
    if (clippingElement != null) newElements.add(clippingElement);

    /// 剪裁后元素
    runInActionStart(() {
      _clippingElementId = clipElementId;
      super.snapshot((
        canvasData.copyWith(
          elements: newElements,
        ),
        currentState.$2.where((final k, final v) {
          return k != _clippingElementId;
        })
      ), type: SnapshotType.create);
    });
  }

  /// 更新剪裁区域 rect剪裁比例
  void updateClippingRect(final Rect rect) {
    final element =
        _elements.firstWhereOrNull((final e) => e.id == _clippingElementId);
    if (element == null) return;
    final fileDataClip = canvasData.fileDataSources?.any((final e) =>
            e.rule.any((final v) => v.elementId == _clippingElementId)) ??
        false;
    final fileDataSources = fileDataClip
        ? canvasData.fileDataSources?.map((final e) {
            if (e.rule.any((final v) => v.elementId == _clippingElementId)) {
              return e.copyWith(crop: rect);
            }
            return e;
          }).toList()
        : [
            ...?canvasData.fileDataSources,
            FileDataSource(
              type: 'image',
              crop: rect,
              pageCount: 1,
              pageImages: [(element as ImageElement).localImageUrl],
              rule: [
                FileDataSourceRule(
                  elementId: _clippingElementId!,
                  pages: [],
                )
              ],
            )
          ];
    super.snapshot((
      canvasData.copyWith(
        fileDataSources: fileDataSources,
      ),
      currentState.$2
    ), type: SnapshotType.create);
  }

  /// 剪裁完成
  void clippedElement() async {
    final clipElement =
        _elements.firstWhereOrNull((final e) => e.id == _clippingElementId);
    if (clipElement == null) {
      _clippingElementId = null;
      _selectedElementIds.clear();
      notifyListeners();
      return;
    }
    final startTime = DateTime.now().millisecondsSinceEpoch;
    NiimbotLoadingController().show(
      NiimbotIntl.getIntlMessage('翻译缺失', '裁剪中...'),
      duration: Duration.zero,
      showMask: true,
    );
    final List<BaseElement> newElements = [];
    List<FileDataSource> newFileDataSources = canvasData.fileDataSources ?? [];
    for (var id in _clippedElementIds) {
      final element = _elements.firstWhereOrNull((final e) => e.id == id);
      if (element != null) {
        if (element is ImageElement && element.id == _clippingElementId) {
          final fileDataSource = canvasData.fileDataSources?.firstWhereOrNull(
              (final val) =>
                  val.rule.any((final v) => v.elementId == element.id));
          final cropElement = await element.imageElementClipped(fileDataSource);
          newFileDataSources = newFileDataSources.map((final e) {
            if (e.rule.any((final v) => v.elementId == cropElement.id)) {
              return e.copyWith(
                  originImage: element.localImageUrl,
                  croppedImage: cropElement.localImageUrl);
            }
            return e;
          }).toList();
          newElements.add(cropElement);
        } else {
          newElements.add(element);
        }
      }
    }
    final endTime = DateTime.now().millisecondsSinceEpoch;
    if (endTime - startTime < 1000) {
      await Future.delayed(
          Duration(milliseconds: 1000 - (endTime - startTime)));
    }
    NiimbotLoadingController().close();
    runInActionEnd(() {
      _clippingElementId = null;
      _selectedElementIds.clear();
      super.snapshot((
        canvasData.copyWith(
          elements: newElements,
          fileDataSources: newFileDataSources,
        ),
        currentState.$2.where((final k, final v) {
          return k != clipElement.id;
        })
      ), type: SnapshotType.create);
    });
  }
}
