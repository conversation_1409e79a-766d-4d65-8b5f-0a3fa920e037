import 'dart:async';

import 'package:flutter/material.dart';

enum SnapshotType {
  /// 替换当前
  replace,

  /// 忽略此次快照
  ignore,

  /// 忽略结束
  ignoreEnd,

  /// 创建快照
  create,

  /// 重置
  reset,
}

enum RunInActionType { init, created }

class HistoryManager<T> extends ChangeNotifier {
  /// 最大记录次数
  final int max;

  List<T> _history = [];
  int _currentIndex = 0;

  List<T> get history => _history;

  T get currentState => _history[_currentIndex];

  bool get canUndo => _currentIndex > 0;

  bool get canRedo => _currentIndex < _history.length - 1;

  bool _firstIgnore = true;

  RunInActionType? _runInActionType;

  HistoryManager({
    final int? max,
    required final T init,
  })  : max = max ?? 100,
        _history = [init];

  /// 监听画板是否可以保存
  bool _saveEnable = false;

  setSaveEnable(final bool value,
      {final bool notify = true, final bool isIndustryTemplate = false}) {
    _saveEnable = value;
    _isIndustryTemplate = isIndustryTemplate;
    if (notify) notifyListeners();
  }

  bool get saveEnable => _saveEnable;

  ///是否是行业模板
  bool _isIndustryTemplate = false;

  bool get isIndustryTemplate => _isIndustryTemplate;

  /// 生成一次快照
  /// [type] 快照类型
  void snapshot(final T newState,
      {final SnapshotType type = SnapshotType.create,
      final bool ignore = true}) {
    if (_runInActionType != null) {
      if (_runInActionType == RunInActionType.init) {
        _create(newState);
        _runInActionType = RunInActionType.created;
      } else {
        _replace(newState);
      }
    } else {
      switch (type) {
        case SnapshotType.create:
          _firstIgnore = ignore;
          _create(newState);
          break;
        case SnapshotType.replace:
          _replace(newState);
          break;
        case SnapshotType.ignore:
          _ignore(newState);
          break;
        case SnapshotType.ignoreEnd:
          _firstIgnore = true;
          _replace(newState);
          break;
        case SnapshotType.reset:
          _firstIgnore = true;
          _reset(newState);
          break;
      }
    }
  }

  /// 生成一次快照
  void _create(final T newState) {
    if (canRedo) {
      /* 可以恢复的时候 再创建镜像 应当重置  */
      /*可以恢复的时候 要去掉恢复的步骤 留下撤销的步骤*/
      _history = _history.sublist(0, _currentIndex + 1);
    }
    if (_history.length >= max) {
      /* 移除第一个 */
      _history.removeRange(0, 1);
      _currentIndex--;
    }
    _history.insert(_currentIndex + 1, newState);
    _currentIndex++;
    notifyListeners();
  }

  /// 替换当前快照
  void _replace(final T newState) {
    _history[_currentIndex] = newState;
    notifyListeners();
  }

  /// 忽略此次快照
  void _ignore(final T newState) {
    if (_firstIgnore) {
      /* 第一个忽略 应当创建一共快照 */
      _create(newState);
    } else {
      _replace(newState);
    }
    _firstIgnore = false;
  }

  /// 重置快照堆栈
  void _reset(final T newState) {
    _history = [newState];
    _currentIndex = 0;
    notifyListeners();
  }

  /// 撤销
  void undo() {
    if (canUndo) {
      _currentIndex--;
      _saveEnable = _history.first != currentState;
      notifyListeners();
    }
  }

  /// 恢复
  void redo() {
    if (canRedo) {
      _currentIndex++;
      notifyListeners();
    }
  }

  /// 所有的操作合并成一个快照
  void runInAction(final FutureOr<void> Function() action) async {
    _runInActionType = RunInActionType.init;
    await action();
    _runInActionType = null;
  }

  /// 开始合并快照
  void runInActionStart(final FutureOr<void> Function() action) async {
    _runInActionType = RunInActionType.init;
    await action();
  }

  /// 结束合并快照
  void runInActionEnd(final FutureOr<void> Function() action) async {
    await action();
    _runInActionType = null;
  }
}
