import 'package:niimbot_flutter_canvas/model/interface.dart';
import 'package:niimbot_flutter_canvas/provider/history/history_store.dart';
import 'package:niimbot_flutter_canvas/provider/models/image_element_updater.dart';

import 'package:niimbot_template/models/elements/base_element.dart';

class ImageController {
  final List<BaseElement> _elements;

  final UpdateElementsCallback _onUpdateElements;

  const ImageController({
    required final UpdateElementsCallback onUpdateElements,
    required final List<BaseElement> elements,
  })  : _onUpdateElements = onUpdateElements,
        _elements = elements;

  /// 更新图片元素
  void updateImageElements(final Map<String, ImageElementUpdater> update) {
    _onUpdateElements(update);
    // _onUpdateElements(update.map((key, value) {
    //   return MapEntry(
    //       key,
    //       ImageElementUpdater(
    //         imageProcessingValue: value.imageProcessingValue,
    //         allowFreeZoom: value.allowFreeZoom,
    //         imageData: value.imageData,
    //         localUrl: value.localUrl,
    //         imageUrl: value.imageUrl,
    //         materialId: value.materialId,
    //         height: value.height,
    //       ));
    // }));
  }

  ///浓度深浅 不计历史操作
  void updateImageElementImageProcessingValue(
      final Map<String, ImageElementUpdater> update) {
    _onUpdateElements(update, type: SnapshotType.ignore);
  }
}
