import 'package:niimbot_template/models/elements/base_element.dart';
import 'package:niimbot_template/models/elements/graph_element.dart';

import 'package:niimbot_flutter_canvas/model/interface.dart';
import 'package:niimbot_flutter_canvas/provider/models/graph_element_updater.dart';
import 'package:niimbot_flutter_canvas/provider/models/line_element_updater.dart';

class LineController {
  final List<BaseElement> Function() _elements;

  final UpdateElementsCallback _onUpdateElements;
  final List<BaseElement> Function() _selectedElement;
  final List<String> Function() _selectedElementIds;

  const LineController({
    required final UpdateElementsCallback onUpdateElements,
    required final List<BaseElement> Function() elements,
    required final List<BaseElement> Function() selectedElement,
    required final List<String> Function() selectedElementIds,
  })  : _onUpdateElements = onUpdateElements,
        _elements = elements,
        _selectedElement = selectedElement,
        _selectedElementIds = selectedElementIds;

  /// 更新线条元素
  void updateLineElements(final Map<String, LineElementUpdater> update) {
    _onUpdateElements(update);
  }

  /// 更新图形元素
  void updateGraphElements(final Map<String, GraphElementUpdater> update) {
    _onUpdateElements(update);
    /*_onUpdateElements(update.map((key, value) {
      final element = _elements.firstWhere((e) => e.id == key);
      if (element is GraphElement) {
        */ /* 图形元素 */ /*
        if ((value.fromGraph ?? false)) {
          // return MapEntry(key, element.fromGraph());
        }
        if (value.graphType == NetalGraphType.round) {
          */ /* 切换成圆形时 宽高相等 */ /*
          return MapEntry(
              key,
              GraphElementUpdater(
                graphType: value.graphType,
                width: element.height,
                lineWidth: value.lineWidth,
              ));
        }
        return MapEntry(
            key,
            GraphElementUpdater(
              graphType: value.graphType,
              lineWidth: value.lineWidth,
            ));
      } else {
        */ /* 线条元素 */ /*
        if (value.fromLine ?? false) {
          */ /* 将线条元素转换成图形元素 */ /*
          // return MapEntry(
          //     key,
          //     GraphElement.fromLineElement(element as LineElement).copyWith(
          //       graphType: value.graphType,
          //       lineWidth: value.lineWidth,
          //     ));
        } else {
          */ /* 无需转换 */ /*
          return MapEntry(
              key,
              GraphElementUpdater(
                height: value.lineWidth,
              ));
        }
      }
    }));*/
  }

  /// 更新线宽
  void updateLineWidth(final num lineWidth, {final List<String>? ids}) {
    var elements = _selectedElement();
    if (ids != null) {
      elements = _elements().where((final e) => ids.contains(e.id)).toList();
    }
    final update = {
      for (final e in elements)
        e.id: e is GraphElement
            ? GraphElementUpdater(lineWidth: lineWidth)
            : LineElementUpdater(height: lineWidth)
    };
    _onUpdateElements(update);
  }
}
