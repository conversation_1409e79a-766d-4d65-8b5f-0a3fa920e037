import 'package:niimbot_template/models/elements/base_element.dart';

import 'package:niimbot_flutter_canvas/provider/models/bar_code_element_updater.dart';
import 'package:niimbot_flutter_canvas/model/interface.dart';

class BarCodeController {
  final UpdateElementsCallback _onUpdateElements;

  const BarCodeController({
    required final UpdateElementsCallback onUpdateElements,
    required final List<BaseElement> elements,
  }) : _onUpdateElements = onUpdateElements;

  void updateBarCodes(final Map<String, BarCodeElementUpdater> update) {
    _onUpdateElements(update);
  }
}
