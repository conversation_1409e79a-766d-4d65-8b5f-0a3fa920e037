import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:netal_plugin/models/copy_wrapper.dart';
import 'package:niimbot_flutter_canvas/extensions/table_element.dart';
import 'package:niimbot_template/models/elements/base_element.dart';
import 'package:niimbot_template/models/elements/table_cell_element.dart';
import 'package:niimbot_template/models/elements/table_combine_cell_element.dart';
import 'package:niimbot_template/models/elements/table_element.dart';

import 'package:niimbot_flutter_canvas/provider/controllers/data_source_controller.dart';
import 'package:niimbot_flutter_canvas/provider/history/history_store.dart';
import 'package:niimbot_flutter_canvas/provider/models/table_cell_element_updater.dart';
import 'package:niimbot_flutter_canvas/provider/models/table_element_updater.dart';
import 'package:niimbot_flutter_canvas/model/element/element_utils.dart';
import 'package:niimbot_flutter_canvas/model/interface.dart';

class TableController {
  final List<BaseElement> Function() _getElements;
  final UpdateElementsCallback _onUpdateElements;
  final DataSourceController _dataSourceController;
  final Set<String> Function(String id) _getSelected;
  final void Function(String id, Set<String> ids) _selectedElementChildren;

  const TableController({
    required final List<BaseElement> Function() elements,
    required final UpdateElementsCallback onUpdateElements,
    required final DataSourceController dataSourceController,
    required final Set<String> Function(String id) getSelected,
    required final void Function(String id, Set<String> ids)
        selectedElementChildren,
  })  : _getElements = elements,
        _onUpdateElements = onUpdateElements,
        _dataSourceController = dataSourceController,
        _getSelected = getSelected,
        _selectedElementChildren = selectedElementChildren;

  /// 获取单元格的默认值

  /// 更新表格属性
  void updateTableElements(final Map<String, TableElementUpdater> update) {
    _onUpdateElements(update);
  }

  /// 更新单元格的内容（文本、字体、字体大小等）
  void updateTableCellElements(
      final String id, final Map<String, TableCellElementUpdater> update) {
    if (update.isNotEmpty) {
      final table = _getElements()
          .whereType<TableElement>()
          .firstWhereOrNull((final e) => e.id == id);
      if (table == null) return;
      final List<TableCellElement> cells = [];
      final List<TableCombineCellElement> combineCells = [];
      bool? hasVipRes;
      for (final cell in [...table.combineCells, ...table.cells]) {
        TableCellElementUpdater? up = update[cell.id];
        if (up?.hasVipRes == null || hasVipRes == true) {
          hasVipRes = up?.hasVipRes;
        }
        if (cell is TableCellElement) {
          if (up != null) {
            cells.add(cell.copyWith(
              value: up.value,
              textAlignHorizontal: up.textAlignHorizontal,
              textAlignVertical: up.textAlignVertical,
              wordSpacing: up.wordSpacing,
              letterSpacing: up.letterSpacing,
              lineSpacing: up.lineSpacing,
              fontCode: up.fontCode,
              fontFamily: up.fontFamily,
              hasVipRes: up.hasVipRes,
              fontSize: up.fontSize,
              lineBreakMode: up.lineBreakMode,
              fontStyle: up.fontStyle,
            ));
          } else {
            cells.add(cell.copyWith());
          }
        } else {
          if (up != null) {
            combineCells.add(cell.copyWith(
              value: up.value,
              textAlignHorizontal: up.textAlignHorizontal,
              textAlignVertical: up.textAlignVertical,
              wordSpacing: up.wordSpacing,
              letterSpacing: up.letterSpacing,
              lineSpacing: up.lineSpacing,
              fontCode: up.fontCode,
              fontFamily: up.fontFamily,
              hasVipRes: up.hasVipRes,
              fontSize: up.fontSize,
              lineBreakMode: up.lineBreakMode,
              fontStyle: up.fontStyle,
            ));
          } else {
            combineCells.add(cell.copyWith());
          }
        }
      }
      _onUpdateElements({
        table.id: TableElementUpdater(
            cells: cells, combineCells: combineCells, hasVipRes: hasVipRes)
      });
    }
  }

  /// 批量更新单元格字体、样式等
  void updateTableCellElementsStyle(
      final String id, final TableCellElementUpdater update) {
    final table = _getElements()
        .whereType<TableElement>()
        .firstWhereOrNull((final e) => e.id == id);
    if (table == null) return;
    final selected = _getSelected(table.id);
    if (selected.isEmpty) {
      selected.length;
      debugPrint(
          'updateTableCellElementsStyle =>${update.letterSpacing},selected:${selected.length}');
    }

    final cells = table.getSelectedCells(selected);
    final selectedCells = cells.isNotEmpty ? cells : table.allCells;
    Map<String, TableCellElementUpdater> updateMap = {};
    for (final e in selectedCells) {
      updateMap[e.id] = update;
      final cellFirst =
          table.cells.firstWhereOrNull((final v) => v.combineId == e.id);
      if (cellFirst != null) {
        updateMap[cellFirst.id] = update;
      }
    }
    updateTableCellElements(table.id, updateMap);
  }

  /// 更新表格行
  void updateTableElementsRow(final String id, final int num) {
    final table = _getElements()
        .whereType<TableElement>()
        .firstWhereOrNull((final e) => e.id == id);
    if (table == null) return;
    int changeRow = num - table.row;
    if (changeRow == 0) {
      return;
    } else if (changeRow > 0) {
      addRowAfterRowIndex(id, table.row - 1, insertNum: changeRow);
    } else {
      removeRows(id, -changeRow);
    }
  }

  /// 更新表格列
  void updateTableElementsColumn(final String id, final int num) {
    final table = _getElements()
        .whereType<TableElement>()
        .firstWhereOrNull((final e) => e.id == id);
    if (table == null) return;
    int changeColumn = num - table.column;
    if (changeColumn == 0) {
      return;
    } else if (changeColumn > 0) {
      addColumnAfterColumnIndex(id, table.column - 1, insertNum: changeColumn);
    } else {
      removeColumns(id, -changeColumn);
    }
  }

  /// 删除合并单元格、重置被合并单元格的 combineId
  void unMergeSelectedCell(final String id) {
    final table = _getElements()
        .whereType<TableElement>()
        .firstWhereOrNull((final e) => e.id == id);
    if (table == null) return;
    final List<TableCellElement> cells = [];
    final List<TableCombineCellElement> combineCells = [];
    final selected = _getSelected(table.id);
    final combineCellId = selected.first;
    final combineCell = table.getSelectedCells(selected).first;
    final combineCellValue = combineCell.value;
    final cellId = table.getFirstCellOfCombine(combineCellId)?.id;
    for (final cell in [...table.combineCells, ...table.cells]) {
      if (cell is TableCellElement) {
        if (cell.combineId == combineCellId) {
          if (cellId == cell.id) {
            cells.add(cell.copyWith(
              combineId: const CopyWrapper.value(null),
              value: combineCellValue,
              fontSize: combineCell.fontSize.toDouble(),
              fontStyle: [...combineCell.fontStyle],
              textAlignHorizontal: combineCell.textAlignHorizontal,
              letterSpacing: combineCell.letterSpacing.toDouble(),
              lineSpacing: combineCell.lineSpacing.toDouble(),
              wordSpacing: combineCell.wordSpacing.toDouble(),
              textAlignVertical: combineCell.textAlignVertical,
              fontFamily: combineCell.fontFamily,
              fontCode: combineCell.fontCode,
            ));
          } else {
            cells.add(cell.copyWith(combineId: const CopyWrapper.value(null)));
          }
        } else {
          cells.add(cell.copyWith());
        }
      } else {
        /* 合并单元格 */
        if (cell.id != combineCellId) {
          combineCells.add(cell.copyWith());
        }
      }
    }
    _onUpdateElements({
      table.id: TableElementUpdater(cells: cells, combineCells: combineCells)
    });
    _selectedElementChildren(table.id, {});
  }

  ///在指定column后增加(n)列，默认1列
  void addColumnAfterColumnIndex(final String id, final int columnIndex,
      {final int insertNum = 1}) {
    final table = _getElements()
        .whereType<TableElement>()
        .firstWhereOrNull((final e) => e.id == id);
    if (table == null) return;
    if (table.columnWidth.length >= 20) {
      /* 最大20列 */
      return;
    }
    TableCellElement baseCell = table.getBottomRightCell();

    /// 新增一列
    /// columnIndex后的列元素的index对应+1，相当于后移一列
    final cells = table.cells.map((final cell) {
      if (cell.columnIndex > columnIndex) {
        return cell.copyWith(columnIndex: cell.columnIndex + insertNum);
      }
      return cell.copyWith();
    }).toList();
    final columnWidth = table.columnWidth.toList();
    for (var i = 1; i <= insertNum; i++) {
      columnWidth.insert(columnIndex + i,
          table.columnWidth[columnIndex < 0 ? 0 : columnIndex]);
      cells.addAll(List.generate(
          table.row,
          (final index) => TableCellElement(
                id: ElementUtils.generateId(),
                rowIndex: index,
                columnIndex: columnIndex + i,
                combineId: '',
                fontSize: baseCell.fontSize.toDouble(),
                fontStyle: [...baseCell.fontStyle],
                textAlignHorizontal: baseCell.textAlignHorizontal,
                letterSpacing: baseCell.letterSpacing.toDouble(),
                lineSpacing: baseCell.lineSpacing.toDouble(),
                value: '',
                wordSpacing: baseCell.wordSpacing.toDouble(),
                textAlignVertical: baseCell.textAlignVertical,
                fontFamily: baseCell.fontFamily,
                fontCode: baseCell.fontCode,
                // tableElement: table,
              )));
    }

    /// 表格元素正向排序
    cells.sort((final TableCellElement a, final TableCellElement b) {
      if (a.rowIndex == b.rowIndex) {
        return a.columnIndex.compareTo(b.columnIndex);
      } else {
        return a.rowIndex.compareTo(b.rowIndex);
      }
    });
    _onUpdateElements({
      table.id: TableElementUpdater(
        cells: cells,
        columnWidth: columnWidth,
      )
    });
  }

  ///在指定row下面增加(n)行，默认1行
  void addRowAfterRowIndex(final String id, final int rowIndex,
      {final int insertNum = 1}) {
    final table = _getElements()
        .whereType<TableElement>()
        .firstWhereOrNull((final e) => e.id == id);
    if (table == null) return;
    if (table.rowHeight.length >= 20) {
      /* 最大20行 */
      return;
    }
    TableCellElement baseCell = table.getBottomRightCell();

    /// 新增一列
    /// columnIndex后的列元素的index对应+1，相当于后移一列
    final cells = table.cells.map((final cell) {
      if (cell.rowIndex > rowIndex) {
        return cell.copyWith(rowIndex: cell.rowIndex + insertNum);
      }
      return cell;
    }).toList();

    /// 新增一行
    final rowHeight = table.rowHeight.toList();

    for (var i = 1; i <= insertNum; i++) {
      rowHeight.insert(
          rowIndex + i, table.rowHeight[rowIndex < 0 ? 0 : rowIndex]);

      cells.addAll(List.generate(
          table.column,
          (final index) => TableCellElement(
                rowIndex: rowIndex + i,
                columnIndex: index,
                combineId: '',
                fontSize: baseCell.fontSize.toDouble(),
                fontStyle: [...baseCell.fontStyle],
                textAlignHorizontal: baseCell.textAlignHorizontal,
                letterSpacing: baseCell.letterSpacing.toDouble(),
                lineSpacing: baseCell.lineSpacing.toDouble(),
                value: '',
                wordSpacing: baseCell.wordSpacing.toDouble(),
                textAlignVertical: baseCell.textAlignVertical,
                fontFamily: baseCell.fontFamily,
                fontCode: baseCell.fontCode,
                // tableElement: table,
              )));
    }

    /// 表格元素正向排序
    cells.sort((final TableCellElement a, final TableCellElement b) {
      if (a.rowIndex == b.rowIndex) {
        return a.columnIndex.compareTo(b.columnIndex);
      } else {
        return a.rowIndex.compareTo(b.rowIndex);
      }
    });

    _onUpdateElements({
      table.id: TableElementUpdater(
        cells: cells,
        rowHeight: rowHeight,
      )
    });
  }

  ///删除区间列数
  void removeColumnInRange(final String id, final int start, final int end) {
    final table = _getElements()
        .whereType<TableElement>()
        .firstWhereOrNull((final e) => e.id == id);
    if (table == null) return;

    /// 删除的列数
    int total = end - start + 1;
    if (total >= table.column) {
      /* 不能删除所有 */
      return;
    }
    final columnWidth = table.columnWidth.toList();
    columnWidth.removeRange(start, end + 1);

    List<TableCombineCellElement> combineCells =
        table.combineCells.where((final cell) {
      final index = table.getCellIndex(cell);
      if (index.last.last > end || index.last.first < start) {
        List<TableCellElement> cellData = table.cells
            .where((final element) => element.combineId == cell.id)
            .toList();
        return cellData
            .whereNot((final element) =>
                element.columnIndex > end || element.columnIndex < start)
            .isEmpty;
      } else {
        return false;
      }
    }).toList();

    final List<TableCellElement> cells = [];
    for (final cell in [...table.cells]) {
      if (cell.columnIndex > end || cell.columnIndex < start) {
        String? combineId =
            combineCells.any((final element) => element.id == cell.combineId)
                ? cell.combineId
                : '';
        if (cell.columnIndex > end) {
          cells.add(cell.copyWith(
              columnIndex: cell.columnIndex - total,
              combineId: CopyWrapper.value(combineId)));
        }
        if (cell.columnIndex < start) {
          cells.add(cell.copyWith(combineId: CopyWrapper.value(combineId)));
        }
      }
    }

    _onUpdateElements({
      table.id: TableElementUpdater(
        cells: cells,
        combineCells: combineCells,
        columnWidth: columnWidth,
      )
    });
  }

  ///删除列数
  void removeColumns(final String id, final int count) {
    final table = _getElements()
        .whereType<TableElement>()
        .firstWhereOrNull((final e) => e.id == id);
    if (table == null) return;
    final columnWidth = table.columnWidth.toList();
    columnWidth.removeRange(columnWidth.length - count, columnWidth.length);
    final cells = table.cells.map((final e) => e.copyWith()).toList();
    final combineCells =
        table.combineCells.map((final e) => e.copyWith()).toList();
    cells.removeWhere((final cell) {
      if (cell.columnIndex >= columnWidth.length) {
        combineCells
            .removeWhere((final element) => element.id == cell.combineId);
        return true;
      }
      return false;
    });

    /// 表格元素正向排序
    cells.sort((final TableCellElement a, final TableCellElement b) {
      if (a.rowIndex == b.rowIndex) {
        return a.columnIndex.compareTo(b.columnIndex);
      } else {
        return a.rowIndex.compareTo(b.rowIndex);
      }
    });
    _onUpdateElements({
      table.id: TableElementUpdater(
        cells: cells,
        combineCells: combineCells,
        columnWidth: columnWidth,
      )
    });
  }

  ///删除区间行数
  void removeRowInRange(final String id, final int start, final int end) {
    final table = _getElements()
        .whereType<TableElement>()
        .firstWhereOrNull((final e) => e.id == id);
    if (table == null) return;

    /// 删除的行数
    int total = end - start + 1;
    if (total >= table.row) {
      /* 不能删除所有 */
      return;
    }
    final rowHeight = table.rowHeight.toList();
    rowHeight.removeRange(start, end + 1);

    List<TableCombineCellElement> combineCells =
        table.combineCells.where((final cell) {
      final index = table.getCellIndex(cell);
      if (index.first.last > end || index.first.first < start) {
        List<TableCellElement> cellData = table.cells
            .where((final element) => element.combineId == cell.id)
            .toList();
        return cellData
            .whereNot((final element) =>
                element.rowIndex > end || element.rowIndex < start)
            .isEmpty;
      } else {
        return false;
      }
    }).toList();

    final List<TableCellElement> cells = [];
    for (final cell in [...table.cells]) {
      if (cell.rowIndex > end || cell.rowIndex < start) {
        String? combineId =
            combineCells.any((final element) => element.id == cell.combineId)
                ? cell.combineId
                : '';
        if (cell.rowIndex > end) {
          cells.add(cell.copyWith(
              rowIndex: cell.rowIndex - total,
              combineId: CopyWrapper.value(combineId)));
        }
        if (cell.rowIndex < start) {
          cells.add(cell.copyWith(combineId: CopyWrapper.value(combineId)));
        }
      }
    }

    _onUpdateElements({
      table.id: TableElementUpdater(
        cells: cells,
        combineCells: combineCells,
        rowHeight: rowHeight,
      )
    });
  }

  void removeRows(final String id, final int count) {
    final table = _getElements()
        .whereType<TableElement>()
        .firstWhereOrNull((final e) => e.id == id);
    if (table == null) return;
    final rowHeight = table.rowHeight.toList();
    rowHeight.removeRange(rowHeight.length - count, rowHeight.length);
    final cells = table.cells.map((final e) => e.copyWith()).toList();
    final combineCells =
        table.combineCells.map((final e) => e.copyWith()).toList();
    cells.removeWhere((final cell) {
      if (cell.rowIndex >= rowHeight.length) {
        combineCells
            .removeWhere((final element) => element.id == cell.combineId);
        return true;
      }
      return false;
    });

    /// 表格元素正向排序
    cells.sort((final TableCellElement a, final TableCellElement b) {
      if (a.rowIndex == b.rowIndex) {
        return a.columnIndex.compareTo(b.columnIndex);
      } else {
        return a.rowIndex.compareTo(b.rowIndex);
      }
    });

    _onUpdateElements({
      table.id: TableElementUpdater(
        cells: cells,
        combineCells: combineCells,
        rowHeight: rowHeight,
      )
    });
  }

  ///清空区间单元格数据
  // void clearCellInRange(
  //     TableElement table, CellPosition start, CellPosition end) {
  //   final cells = table.cells.map((cell) {
  //     if (cell.rowIndex >= start.rowIndex &&
  //         cell.rowIndex <= end.rowIndex &&
  //         cell.columnIndex >= start.columnIndex &&
  //         cell.columnIndex <= end.columnIndex) {
  //       _dataSourceController.delElementModify(cell.id);
  //       return cell.copyWith(
  //           value: '', dataBind: const CopyWrapper.value(null));
  //     }
  //     return cell;
  //   }).toList();
  //   final combineCells = table.combineCells.map((cell) {
  //     final cellRowIndex = table.getFirstCellOfCombine(cell.id)!.rowIndex;
  //     final cellColumnIndex = table.getFirstCellOfCombine(cell.id)!.columnIndex;
  //     if (cellRowIndex >= start.rowIndex &&
  //         cellRowIndex <= end.rowIndex &&
  //         cellColumnIndex >= start.columnIndex &&
  //         cellColumnIndex <= end.columnIndex) {
  //       _dataSourceController.delElementModify(cell.id);
  //       return cell.copyWith(
  //           value: '', dataBind: const CopyWrapper.value(null));
  //     }
  //     return cell;
  //   }).toList();
  //   _onUpdateElements({
  //     table.id: TableElementUpdater(
  //       cells: cells,
  //       combineCells: combineCells,
  //     )
  //   });
  // }

  /// 更新表格行高
  void updateRowHeight(final String id, final double value) {
    final table = _getElements()
        .whereType<TableElement>()
        .firstWhereOrNull((final e) => e.id == id);
    if (table == null) return;
    final selected = _getSelected(table.id);
    final focusCells = table.getSelectedCells(selected);
    final rowHeight = table.rowHeight.toList();
    if (focusCells.isEmpty) {
      for (int index = 0; index < table.row; index++) {
        rowHeight[index] = value;
      }
    } else {
      for (var element in focusCells) {
        if (element is TableCellElement) {
          rowHeight[element.rowIndex] = value;
        }
      }
    }
    _onUpdateElements({
      table.id: TableElementUpdater(
        rowHeight: rowHeight,
      )
    });
  }

  /// 更新表格列宽
  void updateColumnWidth(final String id, final double value) {
    final table = _getElements()
        .whereType<TableElement>()
        .firstWhereOrNull((final e) => e.id == id);
    if (table == null) return;
    final selected = _getSelected(id);
    final focusCells = table.getSelectedCells(selected);
    final columnWidth = table.columnWidth.toList();
    if (focusCells.isEmpty) {
      for (int index = 0; index < table.column; index++) {
        columnWidth[index] = value;
      }
    } else {
      for (var element in focusCells) {
        if (element is TableCellElement) {
          columnWidth[element.columnIndex] = value;
        }
      }
    }
    _onUpdateElements({
      id: TableElementUpdater(
        columnWidth: columnWidth,
      )
    });
  }

  /// 更新表格线宽
  void updateLineWidth(final String id, final double value) {
    _onUpdateElements({
      id: TableElementUpdater(
        lineWidth: value,
      )
    });
  }

  /// 选中行
  void selectedRow(final String id, final int row) {
    final table = _getElements()
        .whereType<TableElement>()
        .firstWhereOrNull((final e) => e.id == id);
    if (table == null) return;
    final cells = table.cells
        .where((final e) =>
            e.combineId?.isNotEmpty == true ? false : e.rowIndex == row)
        .map((final e) => e.id)
        .toSet();
    final combineCells = table.combineCells
        .where((final e) {
          final rowIndex = table.getCellRowIndex(e);
          return row >= rowIndex.first && row <= rowIndex.last;
        })
        .map((final e) => e.id)
        .toSet();
    _selectedElementChildren(table.id, {...cells, ...combineCells});
  }

  /// 选中单元格
  void selectedColumn(final String id, final int column) {
    final table = _getElements()
        .whereType<TableElement>()
        .firstWhereOrNull((final e) => e.id == id);
    if (table == null) return;
    final cells = table.cells
        .where((final e) =>
            e.combineId?.isNotEmpty == true ? false : e.columnIndex == column)
        .map((final e) => e.id)
        .toSet();
    final combineCells = table.combineCells
        .where((final e) {
          final columnIndex = table.getCellColumnIndex(e);
          return column >= columnIndex.first && column <= columnIndex.last;
        })
        .map((final e) => e.id)
        .toSet();

    _selectedElementChildren(table.id, {...cells, ...combineCells});
  }

  /// 清空内容
  void clearContent(final String id) {
    final table = _getElements()
        .whereType<TableElement>()
        .firstWhereOrNull((final e) => e.id == id);
    if (table == null) return;
    final cells = table.cells.map((final e) => e.copyWith(value: '')).toList();
    final combineCells =
        table.combineCells.map((final e) => e.copyWith(value: '')).toList();
    _onUpdateElements({
      table.id: TableElementUpdater(
        cells: cells,
        combineCells: combineCells,
      )
    });
  }

  /// 判断选中内容是否为空
  bool isSelectedContentEmpty(final String id) {
    final table = _getElements()
        .whereType<TableElement>()
        .firstWhereOrNull((final e) => e.id == id);
    if (table == null) return true;
    final selected = _getSelected(id);
    return table.cells
            .where((e) => selected.contains(e.id))
            .every((cell) => cell.value.isEmpty) &&
        table.combineCells
            .where((e) => selected.contains(e.id))
            .every((cell) => cell.value.isEmpty);
  }

  /// 清空选中单元格内容
  void clearSelectedContent(final String id) {
    final table = _getElements()
        .whereType<TableElement>()
        .firstWhereOrNull((final e) => e.id == id);
    if (table == null) return;
    final selected = _getSelected(id);
    final cells = table.cells
        .map((final e) =>
            selected.contains(e.id) ? e.copyWith(value: '') : e.copyWith())
        .toList();
    final combineCells = table.combineCells
        .map((final e) =>
            selected.contains(e.id) ? e.copyWith(value: '') : e.copyWith())
        .toList();
    _onUpdateElements({
      id: TableElementUpdater(
        cells: cells,
        combineCells: combineCells,
      )
    });
  }

  TableCombineCellElement _buildCombineCell(
      final TableCombineCellElement cell) {
    return TableCombineCellElement(
      textAlignHorizontal: cell.textAlignHorizontal,
      textAlignVertical: cell.textAlignVertical,
      wordSpacing: cell.wordSpacing,
      letterSpacing: cell.letterSpacing,
      lineSpacing: cell.lineSpacing,
      fontCode: cell.fontCode,
      fontFamily: cell.fontFamily,
      fontStyle: cell.fontStyle,
      fontSize: cell.fontSize,
      lineBreakMode: cell.lineBreakMode,
      value: cell.value,
      dataBind: cell.dataBind,
      isBinding: cell.isBinding,
      fieldName: cell.fieldName,
      contentTitle: cell.contentTitle,
      isTitle: cell.isTitle,
    );
  }

  /// 合并选中单元格
  void mergeSelectedCells(final String id) {
    final table = _getElements()
        .whereType<TableElement>()
        .firstWhereOrNull((final e) => e.id == id);
    if (table == null) return;
    final selected = _getSelected(id);
    final selectedFirstCell = table.getSelectedFirstCell(selected);
    TableCombineCellElement? combineCell =
        selectedFirstCell != null ? _buildCombineCell(selectedFirstCell) : null;
    final List<TableCellElement> cells = [];
    final List<TableCombineCellElement> combineCells = [];

    final List<String> reMergeCellIds = [];

    // 获取当前合并区域内的第一个有值的单元格
    TableCellElement? selectedCell = table.cells
        .where((final element) => selected.contains(element.id))
        .sorted((final TableCellElement a, final TableCellElement b) {
      if (a.rowIndex == b.rowIndex) {
        return a.columnIndex.compareTo(b.columnIndex);
      } else {
        return a.rowIndex.compareTo(b.rowIndex);
      }
    }).firstWhereOrNull((final element) => element.value.isNotEmpty);

    for (final cell in [...table.combineCells, ...table.cells]) {
      if (selected.contains(cell.id)) {
        if (combineCell != null) {
          /* 如果已经生成合并单元格 */
          if (combineCell.value.isEmpty && cell.value.isNotEmpty) {
            /* 合并的单元格是没有值 并且当前单元格是有值 */
            final curCell = selectedCell ?? cell;
            combineCell = combineCell.copyWith(
              value: curCell.value,
              textAlignHorizontal: curCell.textAlignHorizontal,
              textAlignVertical: curCell.textAlignVertical,
              wordSpacing: curCell.wordSpacing,
              letterSpacing: curCell.letterSpacing,
              lineSpacing: curCell.lineSpacing,
              fontCode: curCell.fontCode,
              fontFamily: curCell.fontFamily,
              fontSize: curCell.fontSize,
              lineBreakMode: curCell.lineBreakMode,
              fontStyle: curCell.fontStyle,
            );
          }
        } else {
          /* 没有生成合并单元格 */
          combineCell = _buildCombineCell(cell);
        }
        if (cell is TableCellElement) {
          cells.add(cell.copyWith(
            combineId: CopyWrapper.value(combineCell.id),
          ));
        } else {
          reMergeCellIds.add(cell.id);
        }
      } else {
        /* 其它单元格 */
        if (cell is TableCellElement) {
          if (reMergeCellIds.contains(cell.combineId)) {
            /* 重新指定合并单元格 */
            cells.add(cell.copyWith(
              combineId: CopyWrapper.value(combineCell!.id),
            ));
          } else {
            cells.add(cell.copyWith());
          }
        } else {
          combineCells.add(cell.copyWith());
        }
      }
    }
    if (combineCell != null) {
      combineCells.add(combineCell);
      _selectedElementChildren(table.id, {combineCell.id});
    }
    _onUpdateElements({
      table.id: TableElementUpdater(cells: cells, combineCells: combineCells)
    });
  }

  /// 更新行大小
  /// [index] 指定行
  /// [size] 更新后的大小
  void updateRowSize(final String id, final int index, final num size) {
    if (size < 1 || size > 350) {
      return;
    }
    final table = _getElements()
        .whereType<TableElement>()
        .firstWhereOrNull((final e) => e.id == id);
    if (table == null) return;
    final rowHeight = table.rowHeight
        .mapIndexed((final i, final e) => i == index ? size : e)
        .toList();
    _onUpdateElements({table.id: TableElementUpdater(rowHeight: rowHeight)},
        type: SnapshotType.ignore);
  }

  /// 更新行大小
  /// [index] 指定行
  /// [size] 更新后的大小
  void updateColumnSize(final String id, final int index, final num size) {
    if (size < 1 || size > 350) {
      return;
    }
    final table = _getElements()
        .whereType<TableElement>()
        .firstWhereOrNull((final e) => e.id == id);
    if (table == null) return;
    final columnWidth = table.columnWidth
        .mapIndexed((final i, final e) => i == index ? size : e)
        .toList();
    _onUpdateElements({table.id: TableElementUpdater(columnWidth: columnWidth)},
        type: SnapshotType.ignore);
  }

  void updateSizeEnd(final String id) {
    _onUpdateElements({id: const TableElementUpdater()},
        type: SnapshotType.ignoreEnd);
  }
}
