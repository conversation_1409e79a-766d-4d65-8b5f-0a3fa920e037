import 'dart:math';

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:netal_plugin/models/copy_wrapper.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:niimbot_flutter_canvas/provider/models/base_element_updater.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_template/models/elements/base_element.dart';
import 'package:niimbot_template/models/elements/date_element.dart';
import 'package:niimbot_template/models/elements/element_enum.dart';
import 'package:niimbot_template/models/elements/text_element.dart';
import 'package:niimbot_template/utils/element_utils.dart';

import 'package:niimbot_flutter_canvas/provider/history/history_store.dart';
import 'package:niimbot_flutter_canvas/provider/models/date_element_updater.dart';
import 'package:niimbot_flutter_canvas/provider/models/text_element_updater.dart';
import 'package:niimbot_flutter_canvas/model/interface.dart';

class TextController {
  final UpdateElementsCallback _onUpdateElements;
  final UsedFontsRemoveFontCallback _onUsedFontsRemoveFont;
  final Size Function() _templateSize;
  final List<BaseElement> Function() _getElements;
  final List<String> Function() _selectedElementIds;
  final List<BaseElement> Function() _selectedElement;
  final VoidCallback _cancelEdit;
  final AddElementsFunction _addElements;
  final DeleteElementsFunction _deleteElements;
  final HistoryStoreRunInAction _runInAction;
  final (double, Rect) Function() _templateInfo;

  const TextController({
    required final UpdateElementsCallback onUpdateElements,
    required final UsedFontsRemoveFontCallback onUsedFontsRemoveFont,
    required final List<BaseElement> Function() elements,
    required final Size Function() templateSize,
    required final List<String> Function() selectedElementIds,
    required final List<BaseElement> Function() selectedElement,
    required final VoidCallback cancelEdit,
    required final AddElementsFunction addElements,
    required final DeleteElementsFunction deleteElements,
    required final HistoryStoreRunInAction runInAction,
    required final (double, Rect) Function() templateInfo,
  })  : _onUpdateElements = onUpdateElements,
        _onUsedFontsRemoveFont = onUsedFontsRemoveFont,
        _getElements = elements,
        _templateSize = templateSize,
        _selectedElement = selectedElement,
        _selectedElementIds = selectedElementIds,
        _cancelEdit = cancelEdit,
        _addElements = addElements,
        _deleteElements = deleteElements,
        _runInAction = runInAction,
        _templateInfo = templateInfo;

  void updateTextElements(final Map<String, TextElementUpdater> update,
      {final SnapshotType type = SnapshotType.create}) {
    _onUpdateElements(update, type: type);
  }

  void _updateTextElementsAssociateSync(
      final Map<String, TextElementUpdater> update,
      {final SnapshotType type = SnapshotType.ignore}) {
    final Map<String, TextElementUpdater> map = {};
    final dateElements = _getElements().whereType<DateElement>();
    for (final e in update.keys) {
      final updater = update[e]!;
      map[e] = updater;
      final element =
          dateElements.firstWhereOrNull((final element) => element.id == e);
      if (element?.associateId.isNotEmpty ?? false) {
        final associateId = element!.associateId;
        final other = dateElements.firstWhereOrNull(
            (final de) => de.associateId == associateId && de.id != e);
        if (other != null) {
          map[other.id] = updater;
        }
      }
    }
    updateTextElements(map, type: type);
  }

  /// 更新文本排版模式
  void updateTextTypesettingMode(
      final List<String> ids, final TextTypesettingMode mode) {
    final Map<String, TextElementUpdater> update = {};
    final templateSize = _templateSize();
    final elements = _getElements()
        .where((final e) => ids.contains(e.id))
        .whereType<TextElement>()
        .toList();
    for (final e in elements) {
      if (mode == TextTypesettingMode.Horizontal) {
        /* 更新为横排 */
        if (e.textTypesettingMode == TextTypesettingMode.Arc) {
          /* 从弧形切换到横排 */
          update[e.id] = TextElementUpdater(
              typesettingMode: NetalTypesettingMode.horizontal,
              width: min(e.width.toDouble(), templateSize.width),
              rotate: 0,
              horizontalAlign: e.textAlignVertical,
              verticalAlign: e.textAlignHorizontal,
              keepPosition: const CopyWrapper.value(KeepPosition.center));
        }
        if (e.textTypesettingMode == TextTypesettingMode.Vertical) {
          /* 从竖排切换到横排 */
          update[e.id] = TextElementUpdater(
              typesettingMode: NetalTypesettingMode.horizontal,
              width: e.height,
              height: e.width,
              rotate: 0,
              horizontalAlign: e.textAlignVertical,
              verticalAlign: e.textAlignHorizontal,
              keepPosition: const CopyWrapper.value(KeepPosition.center));
        }
        if (e.textTypesettingMode == TextTypesettingMode.Horizontal_90) {
          /* 从横排90°切换到横排 */
          update[e.id] = TextElementUpdater(
              typesettingMode: NetalTypesettingMode.horizontal,
              width: e.height,
              height: e.width,
              rotate: 0,
              keepPosition: const CopyWrapper.value(KeepPosition.center));
        }
      }
      if (mode == TextTypesettingMode.Vertical) {
        /* 更新为竖排 */
        if (e.textTypesettingMode == TextTypesettingMode.Horizontal) {
          /* 从横排切换到竖排 */
          update[e.id] = TextElementUpdater(
              typesettingMode: NetalTypesettingMode.vertical,
              width: e.height,
              height: e.width,
              rotate: 0,
              horizontalAlign: e.textAlignVertical,
              verticalAlign: e.textAlignHorizontal,
              keepPosition: const CopyWrapper.value(KeepPosition.center));
        }
        if (e.textTypesettingMode == TextTypesettingMode.Arc) {
          /* 从弧形切换到竖排 */
          update[e.id] = TextElementUpdater(
              typesettingMode: NetalTypesettingMode.vertical,
              horizontalAlign: e.textAlignVertical,
              verticalAlign: e.textAlignHorizontal,
              rotate: 0,
              keepPosition: const CopyWrapper.value(KeepPosition.center));
        }
        if (e.textTypesettingMode == TextTypesettingMode.Horizontal_90) {
          /* 从横排90°切换到竖排 */
          update[e.id] = const TextElementUpdater(
            typesettingMode: NetalTypesettingMode.vertical,
            rotate: 0,
          );
        }
      }
      if (mode == TextTypesettingMode.Arc) {
        /* 更新为弧形 */
        update[e.id] = TextElementUpdater(
            typesettingMode: NetalTypesettingMode.arc,
            horizontalAlign: e.textAlignVertical,
            verticalAlign: e.textAlignHorizontal,
            rotate: 0,
            keepPosition: const CopyWrapper.value(KeepPosition.center));
      }
      if (mode == TextTypesettingMode.Horizontal_90) {
        /* 更新为横排90° */
        if (e.textTypesettingMode == TextTypesettingMode.Horizontal) {
          /* 从横排切换到横排90° */
          update[e.id] = TextElementUpdater(
              typesettingMode: NetalTypesettingMode.vertical,
              width: e.height,
              height: e.width,
              rotate: 270,
              horizontalAlign: e.textAlignVertical,
              verticalAlign: e.textAlignHorizontal,
              keepPosition: const CopyWrapper.value(KeepPosition.center));
        }
        if (e.textTypesettingMode == TextTypesettingMode.Vertical) {
          /* 从竖排切换到横排90° */
          update[e.id] = TextElementUpdater(
            typesettingMode: NetalTypesettingMode.vertical,
            rotate: 270,
            horizontalAlign: e.textAlignVertical,
          );
        }
        if (e.textTypesettingMode == TextTypesettingMode.Arc) {
          /* 从弧形切换到横排90° */
          update[e.id] = TextElementUpdater(
              typesettingMode: NetalTypesettingMode.vertical,
              rotate: 270,
              horizontalAlign: e.textAlignVertical,
              verticalAlign: e.textAlignHorizontal,
              keepPosition: const CopyWrapper.value(KeepPosition.center));
        }
      }
    }
    _updateTextElementsAssociateSync(update);
  }

  /// 更新文本对齐方式
  void updateTextAlign(final NetalTextAlign align, {final List<String>? ids}) {
    var elements = _selectedElement().whereType<TextElement>();
    if (ids != null) {
      elements = _getElements()
          .where((final e) => ids.contains(e.id))
          .whereType<TextElement>()
          .toList();
    }
    final update = {
      for (final e in elements)
        e.id: e.typesettingMode == NetalTypesettingMode.vertical
            ? TextElementUpdater(verticalAlign: align)
            : TextElementUpdater(horizontalAlign: align)
    };
    _updateTextElementsAssociateSync(update);
  }

  ///更新文本字距
  void updateTextLetterSpacing(final num spacing, {final List<String>? ids}) {
    var elements = _selectedElement().whereType<TextElement>();
    if (ids != null) {
      elements = _getElements()
          .where((final e) => ids.contains(e.id))
          .whereType<TextElement>()
          .toList();
    }
    final update = {
      for (final e in elements)
        e.id: TextElementUpdater(letterSpacing: spacing, wordSpacing: spacing)
    };
    _updateTextElementsAssociateSync(update);
  }

  /// 是否开启关联元素
  void openDateAssociate(final String id, final bool associated) {
    final element = _getElements().whereType<DateElement>().firstWhere((final e) => e.id == id);
    if (associated) {
      final eId = ElementUtils.generateId();
      Offset associateOffset =
          Offset(element.x.toDouble(), element.y.toDouble());
      if (element.typesettingMode == NetalTypesettingMode.vertical) {
        switch (element.rotate % 360) {
          case 0:
            associateOffset = associateOffset.translate(-element.width - 1, 0);
            break;
          case 90:
            associateOffset = associateOffset.translate(0, element.width + 1);
            break;
          case 180:
            associateOffset = associateOffset.translate(element.width + 1, 0);
            break;
          case 270:
            associateOffset = associateOffset.translate(0, element.width + 1);
            break;
          default:
        }
      } else {
        switch (element.rotate % 360) {
          case 0:
            associateOffset = associateOffset.translate(0, element.height + 1);
            break;
          case 90:
            associateOffset = associateOffset.translate(-element.height - 1, 0);
            break;
          case 180:
            associateOffset = associateOffset.translate(0, -element.height - 1);
            break;
          case 270:
            associateOffset = associateOffset.translate(element.height + 1, 0);
            break;
          default:
        }
      }
      _runInAction(() {
        _addElements([
          element.copyWith(
            id: ElementUtils.generateId(),
            associateId: CopyWrapper.value(eId),
            contentTitle: (element.contentTitle?.isNotEmpty ?? false)
                ? CopyWrapper.value(
                    NiimbotIntl.getIntlMessage('app100001408', '保质期至'))
                : null,
          )
        ], offset: associateOffset, isViewOffset: false);
        _onUpdateElements({
          element.id: DateElementUpdater(
            associated: true,
            associateId: CopyWrapper.value(eId),
          )
        }, type: SnapshotType.ignore);
      });
    } else {
      if (element.isAssociate) {
        deleteAssociateElement(element);
      } else {
        final associate = _getElements()
            .whereType<DateElement>()
            .firstWhereOrNull((final e) =>
                e.associateId == element.associateId && e.isAssociate);
        if (associate != null) {
          deleteAssociateElement(associate);
        }
      }
    }
  }

  /// 删除关联元素
  deleteAssociateElement(final DateElement associateElement) {
    _runInAction(() {
      /// 获取原始元素id
      final originId = getAssociateOriginId(associateElement.associateId);
      if (originId != null) {
        _onUpdateElements({
          originId: const DateElementUpdater(
            associated: false,
            validityPeriod: CopyWrapper.value(null),
            validityPeriodUnit: CopyWrapper.value(null),
          )
        });
      }
      _deleteElements(ids: [associateElement.id]);
    });
  }

  /// 通过关联元素ID获取原生元素
  DateElement? getAssociateOriginElement(final String associateId) {
    if (associateId.isNotEmpty) {
      final origin = _getElements().whereType<DateElement>().firstWhereOrNull(
          (final e) => e.associateId == associateId && e.isAssociateOrigin);
      return origin;
    }
    return null;
  }

  /// 通过关联元素ID获取关联元素
  DateElement? getAssociateElement(final String associateId) {
    if (associateId.isNotEmpty) {
      final origin = _getElements().whereType<DateElement>().firstWhereOrNull(
          (final e) => e.associateId == associateId && e.isAssociate);
      return origin;
    }
    return null;
  }

  /// 获取关联元素的原始id
  String? getAssociateOriginId(final String associateId) {
    if (associateId.isNotEmpty) {
      final origin = _getElements().whereType<DateElement>().firstWhereOrNull(
          (final e) => e.associateId == associateId && e.isAssociateOrigin);
      return origin?.id;
    }
    return null;
  }

  /// 获取关联元素的原始标题
  String? getAssociateOriginContentTitle(final String associateId) {
    if (associateId.isNotEmpty) {
      final origin = _getElements().whereType<DateElement>().firstWhereOrNull(
          (final e) => e.associateId == associateId && e.isAssociateOrigin);
      return origin?.contentTitle;
    }
    return null;
  }

  /// 获取关联元素的标题
  String? getAssociateContentTitle(final String associateId) {
    if (associateId.isNotEmpty) {
      final associate = _getElements()
          .whereType<DateElement>()
          .firstWhereOrNull(
              (final e) => e.associateId == associateId && e.isAssociate);
      return associate?.contentTitle;
    }
    return null;
  }

  /// 更新关联元素标题
  void updateDateAssociateContentTitle(
      final String id, final String? contentTitle) {
    final element = _getElements().whereType<DateElement>().firstWhere((final e) => e.id == id);
    final updater = DateElementUpdater(
      contentTitle: CopyWrapper.value(contentTitle),
    );
    if (element.isAssociate) {
      updateTextElements({element.id: updater});
    } else {
      final associate = _getElements()
          .whereType<DateElement>()
          .firstWhereOrNull((final e) =>
              e.associateId == element.associateId && e.isAssociate);
      if (associate != null) {
        updateTextElements({associate.id: updater});
      }
    }
  }

  /// 更新关联元素的有效期
  void updateDateAssociateValidityPeriod(
      final String associateId, final int validityPeriod) {
    final elements = _getElements()
        .whereType<DateElement>()
        .where((final e) => e.associateId == associateId);
    final updater = DateElementUpdater(
      validityPeriod: CopyWrapper.value(validityPeriod),
    );
    updateTextElements({for (final e in elements) e.id: updater});
  }

  /// 更新元素通过关联id
  void updateDateByAssociateId(final String id, final String associateId,
      final DateElementUpdater updater) {
    if (associateId.isNotEmpty) {
      final elements = _getElements()
          .whereType<DateElement>()
          .where((final e) => e.associateId == associateId);
      updateTextElements({for (final e in elements) e.id: updater},
          type: SnapshotType.create);
    } else {
      updateTextElements({id: updater}, type: SnapshotType.create);
    }
  }

  /// 更新选中元素
  void updateSelectedTextElement(final TextElementUpdater updater) {
    final selected = _selectedElement();
    final Map<String, TextElementUpdater> update = {};
    for (var e in selected) {
      final id = e.id;
      update[id] = updater;
    }
    _updateTextElementsAssociateSync(update);
  }
}
