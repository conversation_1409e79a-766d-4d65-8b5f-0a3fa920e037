import 'package:niimbot_template/models/template/template_data_source_modify.dart';
import 'package:niimbot_template/models/template/template_enum.dart';
import 'package:niimbot_template/models/template_data.dart';

import 'package:niimbot_flutter_canvas/model/interface.dart';
import 'package:niimbot_flutter_canvas/model/template/template_utils.dart';
import 'package:niimbot_flutter_canvas/provider/history/history_store.dart';

class DataSourceController {
  final TemplateDataSourceModifies? _dataSourceModifies;
  final UpdateTemplateDataSourceModifiesCallBack
      _updateTemplateDataSourceModifies;
  final UpdateTemplateDataSourceCallBack _updateTemplateDataSource;
  final UpdateTemplateDataSingleBindBreakCallBack? _upDateBreakBind;

  DataSourceController(
      {final TemplateDataSourceModifies? dataSourceModifies,
      required final UpdateTemplateDataSourceModifiesCallBack
          updateTemplateDataSourceModifies,
      required final UpdateTemplateDataSourceCallBack updateTemplateDataSource,
      final UpdateTemplateDataSingleBindBreakCallBack? upDateBreakBind})
      : _dataSourceModifies = dataSourceModifies,
        _updateTemplateDataSourceModifies = updateTemplateDataSourceModifies,
        _updateTemplateDataSource = updateTemplateDataSource,
        _upDateBreakBind = upDateBreakBind;

  ///删除元素绑定关系
  void delElementModify(final String? elementId) {
    if (elementId == null) {
      return;
    }
    final originModify = TemplateUtils.cloneModify(_dataSourceModifies);
    final update = TemplateUtils.cloneModify(_dataSourceModifies);
    update?.remove(elementId);
    _updateTemplateDataSourceModifies(update);
    final modifyAfter = TemplateUtils.cloneModify(_dataSourceModifies);
  }

  ///变更元素修改（包含列名是否显示，列名修改，列值修改）
  void changeElementModify(
      final String? elementId, final ElementModifyType modifyType,
      {final bool displayHeader = false,
      final String? afterTitle,
      final String? afterValue,
      final bool isGlobalEffective = false}) {
    if (elementId == null) {
      return;
    }
    final originModify = TemplateUtils.cloneModify(_dataSourceModifies);
    final update = TemplateUtils.cloneModify(_dataSourceModifies);
    final globalModify = update?[elementId]
        ?.putIfAbsent("0", () => TemplateDataSourceModify(delimiter: "："));
    // int excelRow = ExcelTransformManager.sharedInstance().getCurrentExcelRow();
    int excelRow = 0;

    ///列名的显示与修改，全局生效
    if (modifyType == ElementModifyType.modifyUseTitle) {
      globalModify?.useTitle = displayHeader;
    } else if (modifyType == ElementModifyType.modifyTitle) {
      globalModify?.title = afterTitle;
    } else if (modifyType == ElementModifyType.modifyValue) {
      final rowModify = update?[elementId]
          ?.putIfAbsent(excelRow.toString(), () => TemplateDataSourceModify());
      rowModify?.value = afterValue;
    }
    _updateTemplateDataSourceModifies(update);
    final modifyAfter = TemplateUtils.cloneModify(_dataSourceModifies);
  }

  /// view层调用的方法
  void updateTemplateDataSource(final templateDataSource,
      {final int page = 1,
      final bool refresh = false,
      final SnapshotType type = SnapshotType.create}) {
    if (templateDataSource != null) {
      _updateTemplateDataSource([templateDataSource],
          page: page, needRefresh: refresh, type: type);
    }
  }

  ///更新元素的modify
  void updateTemplateModify(final TemplateData canvasData, final String eId,
      final String value, final TemplateDataSourceModify modify) {
    Map<String, Map<String, TemplateDataSourceModify>> update = {};
    if (canvasData.dataSourceModifies == null) {
      update = {
        eId: {"0": modify}
      };
    } else {
      Map<String, Map<String, TemplateDataSourceModify>> originModify =
          TemplateUtils.cloneModify(canvasData.dataSourceModifies)!;
      if (originModify[eId] == null) {
        originModify[eId] = {"0": modify};
      } else {
        originModify[eId]!["0"] = modify;
      }
      update = originModify;
    }
    _updateTemplateDataSourceModifies(update);
  }

  ///批量更新是否显示列名
  void updateTemplateColumnName(
      final Map<String, Map<String, TemplateDataSourceModify>> update,
      {final SnapshotType type = SnapshotType.create}) {
    _updateTemplateDataSourceModifies(update, type: type);
  }

  void upDateBreakBind(final String eId) {
    if (_upDateBreakBind != null) {
      _upDateBreakBind!(eId);
    }
  }

  ///断开关联
  void breakBindData() {
    _updateTemplateDataSource(null);
  }
}
