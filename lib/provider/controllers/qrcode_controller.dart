import 'package:niimbot_flutter_canvas/model/interface.dart';
import 'package:niimbot_flutter_canvas/provider/models/qr_code_element_updater.dart';

class QRCodeController {
  final UpdateElementsCallback _onUpdateElements;

  const QRCodeController({
    required final UpdateElementsCallback onUpdateElements,
  }) : _onUpdateElements = onUpdateElements;

  updateQRCodeElements(final Map<String, QRCodeElementUpdater> update) {
    _onUpdateElements(update);
  }
}
