import 'package:collection/collection.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:netal_plugin/utils/ratio.dart';
import 'package:niimbot_flutter_canvas/extensions/base_element.dart';
import 'package:niimbot_flutter_canvas/extensions/rect.dart';
import 'package:niimbot_flutter_canvas/model/interface.dart';
import 'package:niimbot_flutter_canvas/provider/history/history_store.dart';
import 'package:niimbot_flutter_canvas/provider/models/base_element_updater.dart';
import 'package:niimbot_flutter_canvas/provider/models/color_element_updater.dart';
import 'package:niimbot_flutter_canvas/provider/models/color_reverse_updater.dart';
import 'package:niimbot_flutter_canvas/provider/models/index.dart';
import 'package:niimbot_flutter_canvas/provider/models/text_element_updater.dart';
import 'package:niimbot_flutter_canvas/provider/utils/canvas_store_utils.dart';
import 'package:niimbot_flutter_canvas/provider/utils/store_status_utils.dart';
import 'package:niimbot_template/models/elements/base_element.dart';
import 'package:niimbot_template/models/elements/date_element.dart';
import 'package:niimbot_template/models/elements/element_enum.dart';
import 'package:niimbot_template/models/elements/text_element.dart';

final Logger _logger = Logger("BaseController", on: kDebugMode);

class BaseController {
  final UpdateElementsCallback _onUpdateElements;
  final List<BaseElement> Function() _getElements;
  final List<BaseElement> Function() _selectedElement;
  final Rect? Function() _printArea;
  final (double, Rect) Function() _templateInfo;
  final GetSelectRect _selectRect;
  final VoidCallback _cancelEdit;
  final ValueChanged<String> _resizingElement;

  BaseController({
    required final UpdateElementsCallback onUpdateElements,
    required final List<BaseElement> Function() elements,
    required final List<BaseElement> Function() selectedElement,
    required final Rect? Function() printArea,
    required final (double, Rect) Function() templateInfo,
    required final GetSelectRect selectRect,
    required final VoidCallback cancelEdit,
    required final ValueChanged<String> resizingElement,
  })  : _onUpdateElements = onUpdateElements,
        _getElements = elements,
        _selectedElement = selectedElement,
        _printArea = printArea,
        _templateInfo = templateInfo,
        _selectRect = selectRect,
        _cancelEdit = cancelEdit,
        _resizingElement = resizingElement;

  /// 更新元素锁定状态
  void updateElementsLock({final List<String>? ids}) {
    List<String> lockedIds =
        ids ?? _selectedElement().map((final e) => e.id).toList();
    if (lockedIds.isNotEmpty) {
      Map<String, BaseElementUpdater> update;
      if (lockedIds.length > 1) {
        /* 多选 */
        /* 锁定元素不参与多选 多选只有锁定 */
        bool isLock =
            !_selectedElement().every((final element) => element.isLock);
        update = {
          for (var e in lockedIds) (e): BaseElementUpdater(isLock: isLock)
        };
      } else {
        /* 单选 */
        final locked = _selectedElement()
                .firstWhereOrNull((final e) => e.id == lockedIds.first)
                ?.isLock ??
            false;
        update = {lockedIds.first: BaseElementUpdater(isLock: !locked)};
      }
      _onUpdateElements(update);
    }
  }

  /// 更新元素大小
  /// [size] 元素原始大小
  void updateElementsSize(final String id, final Size size) {
    final element = _getElements().firstWhereOrNull((final e) => e.id == id);
    if (element != null) {
      var updater = BaseElementUpdater(
        width: size.width,
        height: size.height,
      );
      if (element is TextElement) {
        if (element.boxStyle == NetalTextBoxStyle.autoWidth) {
          updater = TextElementUpdater(
            width: size.width,
            height: size.height,
            boxStyle: NetalTextBoxStyle.autoHeight,
          );
        }
      }
      _resizingElement(id);
      _updateElementsAssociateSync({id: updater}, type: SnapshotType.ignore);
    }
  }

  /// 更新元素旋转角度
  void updateElementsRotate(final Map<String, num> update) {
    _updateElementsAssociateSync(update.map((final key, final value) {
      return MapEntry(key, BaseElementUpdater(rotate: (value % 360).toInt()));
    }));
  }

  /// 旋转选中元素
  void rotateSelectedElements() {
    final selected = _selectedElement();
    if (!StoreStatusUtils.canRotate(selected)) {
      return;
    }
    updateElementsRotate({for (var e in selected) e.id: e.rotate + 90});
  }

  /// 镜像选中元素
  void mirrorSelectedElements() {
    final selected = _selectedElement();
    if (!StoreStatusUtils.canMirror(selected)) {
      return;
    }
    _updateElementsAssociateSync({
      for (final e in selected)
        e.id: BaseElementUpdater(
          isOpenMirror: !selected.first.isOpenMirror,
          mirrorType: ElementMirrorType.canvasCenter,
        ),
    });
  }

  ///对齐选中元素
  void alignSelectedElements(final AlignType type) {
    final selected =
        _selectedElement().where((final e) => !e.isLock); // 锁定元素不参与对齐
    final Map<String, BaseElementUpdater> update = {};

    final (r, templateRect) = _templateInfo();
    if (selected.length == 1) {
      /* 单选 */
      var rect =
          Rect.fromLTWH(0, 0, templateRect.size.width, templateRect.size.height)
              .rotate(r);
      final area = _printArea();
      if (area != null) {
        rect = area;
      }

      update[selected.first.id] = CanvasStoreUtils.buildAlignUpdater(
          type, rect, selected.first, r, templateRect.center);
    } else if (selected.length > 1) {
      /* 多选 */
      final rect = _selectRect(includeMirror: false);
      if (rect != null) {
        for (final e in selected) {
          update[e.id] = CanvasStoreUtils.buildAlignUpdater(
              type, rect, e, r, templateRect.center);
        }
      }
    }
    _onUpdateElements(update);
  }

  /// 等距选中元素
  void isometricSelectedElements(final IsometricType type) {
    final selected =
        _selectedElement().where((final e) => !e.isLock); // 锁定元素不参与分布
    if (selected.length >= 3) {
      /* 选中三个及以上才可进行等距操作 */
      final rect = _selectRect(includeMirror: false);
      final (rotate, templateRect) = _templateInfo();
      if (rect != null) {
        final Map<String, BaseElementUpdater> update = {};
        if (type == IsometricType.Horizontal) {
          final list = selected.sorted((final a, final b) => a
              .getViewRect(rotate, templateRect.center)
              .center
              .dx
              .compareTo(b.getViewRect(rotate, templateRect.center).center.dx));

          /// 平均间距 (总宽度-元素总宽度)/(元素个数-1)
          final spacing = (rect.width -
                  list.fold(
                      0,
                      (final v, final e) =>
                          v +
                          e.getViewRect(rotate, templateRect.center).width)) /
              (list.length - 1);
          var preRect = list.first.getViewRect(rotate, templateRect.center);
          for (int i = 1; i < list.length - 1; i++) {
            final curElement = list[i];
            update[curElement.id] =
                BaseElementUpdater(dx: spacing + preRect.right);
            final curRect = curElement.getViewRect(rotate, templateRect.center);
            preRect =
                curRect.translate(spacing + preRect.right - curRect.left, 0);
          }
        }
        if (type == IsometricType.Vertical) {
          final list = selected.sorted((final a, final b) => a
              .getViewRect(rotate, templateRect.center)
              .center
              .dy
              .compareTo(b.getViewRect(rotate, templateRect.center).center.dy));

          /// 平均间距 (总高度-元素总高度)/(元素个数-1)
          final spacing = (rect.height -
                  list.fold(
                      0,
                      (final v, final e) =>
                          v +
                          e.getViewRect(rotate, templateRect.center).height)) /
              (list.length - 1);
          var preRect = list.first.getViewRect(rotate, templateRect.center);
          for (int i = 1; i < list.length - 1; i++) {
            final curElement = list[i];
            update[curElement.id] =
                BaseElementUpdater(dy: spacing + preRect.bottom);
            final curRect = curElement.getViewRect(rotate, templateRect.center);
            preRect =
                curRect.translate(0, spacing + preRect.bottom - curRect.top);
          }
        }
        _onUpdateElements(update);
      }
    }
  }

  /// 更新元素颜色
  void updateElementsColors(final ColorElementUpdater update) {
    final selected = _selectedElement();
    final Map<String, BaseElementUpdater> map = {};
    for (var e in selected) {
      final id = e.id;
      map[id] = update;
    }
    _updateElementsAssociateSync(map);
  }

  /// 批量更新元素颜色
  void batchUpdateElementsColors(
      final Map<String, ColorElementUpdater> update) {
    _updateElementsAssociateSync(update);
  }

  /// 更新元素反色
  void updateElementsColorReverse(final bool colorReverse) {
    final selected = _selectedElement();
    final Map<String, BaseElementUpdater> map = {};
    final updater = ColorReverseUpdater(colorReverse: colorReverse);
    for (var e in selected) {
      final id = e.id;
      map[id] = updater;
    }
    _updateElementsAssociateSync(map);
  }

  /// 更新元素 并同步关联元素
  void _updateElementsAssociateSync(
      final Map<String, BaseElementUpdater> update,
      {final SnapshotType type = SnapshotType.ignore}) {
    final Map<String, BaseElementUpdater> map = {};
    final dateElements = _getElements().whereType<DateElement>();
    for (final e in update.keys) {
      final updater = update[e]!;
      map[e] = updater;
      final element =
          dateElements.firstWhereOrNull((final element) => element.id == e);
      if (element?.associateId.isNotEmpty ?? false) {
        final associateId = element!.associateId;
        final other = dateElements.firstWhereOrNull(
            (final de) => de.associateId == associateId && de.id != e);
        if (other != null) {
          map[other.id] = updater;
        }
      }
    }
    _onUpdateElements(map, type: type);
  }
}
