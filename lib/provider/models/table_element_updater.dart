import 'package:flutter/material.dart';
import 'package:netal_plugin/models/copy_wrapper.dart';
import 'package:netal_plugin/netal_image_result.dart';
import 'package:niimbot_template/models/elements/element_enum.dart';
import 'package:niimbot_template/models/elements/table_cell_element.dart';
import 'package:niimbot_template/models/elements/table_combine_cell_element.dart';

import 'package:niimbot_flutter_canvas/provider/models/base_element_updater.dart';

@immutable
class TableElementUpdater extends BaseElementUpdater {
  final List<num>? rowHeight;
  final List<TableCellElement>? cells;
  final List<num>? columnWidth;
  final List<TableCombineCellElement>? combineCells;
  final num? lineWidth;
  final Color? contentColor;
  final int? contentColorChannel;
  final Color? lineColor;
  final int? lineColorChannel;

  /// 是否清理缓存
  final bool _clearImageCache;

  const TableElementUpdater({
    super.x,
    super.y,
    super.dx,
    super.dy,
    super.width,
    super.height,
    super.zIndex,
    super.rotate,
    super.isLock,
    super.isOpenMirror,
    super.mirrorType,
    super.hasVipRes,
    super.imageCache,
    this.rowHeight,
    this.columnWidth,
    this.cells,
    this.combineCells,
    this.lineWidth,
    this.contentColor,
    this.contentColorChannel,
    this.lineColor,
    this.lineColorChannel,
    final bool clearImageCache = true,
  }) : _clearImageCache = clearImageCache;

  @override
  bool get isNotEmpty {
    return super.isNotEmpty ||
        rowHeight != null ||
        columnWidth != null ||
        cells != null ||
        combineCells != null ||
        lineWidth != null ||
        contentColor != null ||
        contentColorChannel != null ||
        lineColor != null ||
        lineColorChannel != null;
  }

  @override
  bool get clearImageCache {
    if (_clearImageCache) {
      return super.clearImageCache ||
          rowHeight != null ||
          columnWidth != null ||
          cells != null ||
          combineCells != null ||
          lineWidth != null ||
          contentColor != null ||
          contentColorChannel != null ||
          lineColor != null ||
          lineColorChannel != null;
    }
    return false;
  }

  @override
  TableElementUpdater copyWith({
    final num? x,
    final num? y,
    final num? dx,
    final num? dy,
    final num? width,
    final num? height,
    final int? zIndex,
    final int? rotate,
    final bool? isLock,
    final bool? isOpenMirror,
    final ElementMirrorType? mirrorType,
    final bool? hasVipRes,
    final CopyWrapper<NetalImageResult?>? imageCache,
    final double? lastWidth,
    final double? lastHeight,
    final CopyWrapper<KeepPosition?>? keepPosition,
  }) {
    return TableElementUpdater(
      x: x ?? this.x,
      y: y ?? this.y,
      dx: dx ?? this.dx,
      dy: dy ?? this.dy,
      // width: width ?? this.width,
      // height: height ?? this.height,
      zIndex: zIndex ?? this.zIndex,
      rotate: rotate ?? this.rotate,
      isLock: isLock ?? this.isLock,
      isOpenMirror: isOpenMirror ?? this.isOpenMirror,
      mirrorType: mirrorType ?? this.mirrorType,
      hasVipRes: hasVipRes ?? this.hasVipRes,
      imageCache: imageCache ?? this.imageCache,
    );
  }
}
