import 'package:flutter/material.dart';
import 'package:netal_plugin/models/copy_wrapper.dart';
import 'package:netal_plugin/netal_image_result.dart';
import 'package:niimbot_template/models/elements/element_enum.dart';

enum KeepPosition { center, topLeft }

@immutable
class BaseElementUpdater {
  /// 显示x坐标
  final num? dx;

  /// 显示y坐标
  final num? dy;
  final num? x;
  final num? y;
  final num? width;
  final num? height;
  final int? zIndex;
  final int? rotate;
  final bool? isLock;
  final bool? isOpenMirror;
  final ElementMirrorType? mirrorType;
  final bool? hasVipRes;
  final CopyWrapper<NetalImageResult?>? _imageCache;
  final CopyWrapper<KeepPosition?>? keepPosition;

  CopyWrapper<NetalImageResult?>? get imageCache {
    if (_imageCache != null) {
      return _imageCache;
    }
    if (clearImageCache) {
      return const CopyWrapper.value(null);
    }
    return _imageCache;
  }

  const BaseElementUpdater({
    this.x,
    this.y,
    this.dx,
    this.dy,
    this.width,
    this.height,
    this.zIndex,
    this.rotate,
    this.isLock,
    this.isOpenMirror,
    this.mirrorType,
    this.hasVipRes,
    final CopyWrapper<NetalImageResult?>? imageCache,
    this.keepPosition,
  }) : _imageCache = imageCache;

  /// 是否为空
  bool get isNotEmpty {
    return x != null ||
        y != null ||
        dx != null ||
        dy != null ||
        rotate != null ||
        zIndex != null ||
        width != null ||
        height != null ||
        isLock != null ||
        isOpenMirror != null ||
        mirrorType != null ||
        hasVipRes != null ||
        imageCache != null;
  }

  /// 是否清空图片缓存
  bool get clearImageCache => (width != null || height != null);

  BaseElementUpdater copyWith({
    final num? x,
    final num? y,
    final num? dx,
    final num? dy,
    final num? width,
    final num? height,
    final int? zIndex,
    final int? rotate,
    final bool? isLock,
    final bool? isOpenMirror,
    final ElementMirrorType? mirrorType,
    final bool? hasVipRes,
    final CopyWrapper<NetalImageResult?>? imageCache,
    final CopyWrapper<KeepPosition?>? keepPosition,
  }) {
    return BaseElementUpdater(
      x: x ?? this.x,
      y: y ?? this.y,
      dx: dx ?? this.dx,
      dy: dy ?? this.dy,
      width: width ?? this.width,
      height: height ?? this.height,
      zIndex: zIndex ?? this.zIndex,
      rotate: rotate ?? this.rotate,
      isLock: isLock ?? this.isLock,
      isOpenMirror: isOpenMirror ?? this.isOpenMirror,
      mirrorType: mirrorType ?? this.mirrorType,
      hasVipRes: hasVipRes ?? this.hasVipRes,
      imageCache: imageCache ?? this.imageCache,
      keepPosition: keepPosition ?? this.keepPosition,
    );
  }
}
