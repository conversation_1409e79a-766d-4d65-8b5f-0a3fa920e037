import 'package:netal_plugin/models/netal_enum.dart';
import 'package:niimbot_flutter_canvas/provider/models/line_element_updater.dart';

class GraphElementUpdater extends LineElementUpdater {
  final num? cornerRadius;

  final NetalGraphType? graphType;

  final num? lineWidth;

  final bool? fromGraph;

  final bool? fromLine;

  @override
  bool get isNotEmpty {
    return super.isNotEmpty ||
        graphType != null ||
        lineType != null ||
        lineWidth != null;
  }

  @override
  bool get clearImageCache {
    return super.clearImageCache ||
        graphType != null ||
        lineType != null ||
        lineWidth != null;
  }

  const GraphElementUpdater({
    super.x,
    super.y,
    super.width,
    super.height,
    super.zIndex,
    super.rotate,
    super.isLock,
    super.isOpenMirror,
    super.mirrorType,
    super.hasVipRes,
    super.colorChannel,
    super.elementColor,
    super.paperColorIndex,
    super.lineType,
    super.imageCache,
    this.cornerRadius,
    this.graphType,
    this.lineWidth,
    this.fromGraph,
    this.fromLine,
  });
}
