import 'dart:typed_data';

import 'package:netal_plugin/models/copy_wrapper.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:netal_plugin/netal_image_result.dart';
import 'package:niimbot_flutter_canvas/provider/models/base_element_updater.dart';
import 'package:niimbot_template/models/elements/element_enum.dart';

import 'package:niimbot_flutter_canvas/provider/models/color_element_updater.dart';

class QRCodeElementUpdater extends ColorElementUpdater {
  final double? lastWidth;
  final double? lastHeight;
  final String? value;
  final NetalQRCodeType? codeType;
  final CopyWrapper<List<String>?>? dataBind;
  final NetalQRCodeCorrectLevel? correctLevel;
  final Uint8List? qrCodeImageData;

  const QRCodeElementUpdater({
    super.x,
    super.y,
    super.dx,
    super.dy,
    super.width,
    super.height,
    super.zIndex,
    super.rotate,
    super.isLock,
    super.isOpenMirror,
    super.mirrorType,
    super.hasVipRes,
    super.colorChannel,
    super.elementColor,
    super.paperColorIndex,
    super.imageCache,
    this.lastWidth,
    this.lastHeight,
    this.value,
    this.codeType,
    this.dataBind,
    this.correctLevel,
    this.qrCodeImageData,
  });

  @override
  bool get isNotEmpty {
    return super.isNotEmpty ||
        lastHeight != null ||
        lastWidth != null ||
        dataBind != null ||
        value != null ||
        codeType != null ||
        correctLevel != null ||
        colorChannel != null;
  }

  @override
  bool get clearImageCache {
    return super.clearImageCache ||
        dataBind != null ||
        value != null ||
        codeType != null ||
        correctLevel != null ||
        colorChannel != null;
  }

  @override
  QRCodeElementUpdater copyWith({
    final num? x,
    final num? y,
    final num? dx,
    final num? dy,
    final num? width,
    final num? height,
    final int? zIndex,
    final int? rotate,
    final bool? isLock,
    final bool? isOpenMirror,
    final ElementMirrorType? mirrorType,
    final bool? hasVipRes,
    final CopyWrapper<NetalImageResult?>? imageCache,
    final double? lastWidth,
    final double? lastHeight,
    final Uint8List? qrCodeImageData,
    final CopyWrapper<KeepPosition?>? keepPosition,
  }) {
    return QRCodeElementUpdater(
      x: x ?? this.x,
      y: y ?? this.y,
      dx: dx ?? this.dx,
      dy: dy ?? this.dy,
      width: width ?? this.width,
      height: height ?? this.height,
      zIndex: zIndex ?? this.zIndex,
      rotate: rotate ?? this.rotate,
      isLock: isLock ?? this.isLock,
      isOpenMirror: isOpenMirror ?? this.isOpenMirror,
      mirrorType: mirrorType ?? this.mirrorType,
      hasVipRes: hasVipRes ?? this.hasVipRes,
      lastWidth: lastWidth ?? this.lastWidth,
      lastHeight: lastHeight ?? this.lastHeight,
      qrCodeImageData: qrCodeImageData ?? this.qrCodeImageData,
      imageCache: imageCache ?? this.imageCache,
    );
  }
}
