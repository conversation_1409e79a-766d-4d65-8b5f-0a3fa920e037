import 'package:flutter/material.dart';
import 'package:netal_plugin/models/copy_wrapper.dart';
import 'package:netal_plugin/models/netal_enum.dart';

@immutable
class TableCellElementUpdater {
  final String? value;
  final CopyWrapper<List<String>?>? dataBind;
  final String? fontCode;
  final String? fontFamily;
  final num? fontSize;
  final List<NetalTextFontStyle>? fontStyle;
  final NetalTextAlign? textAlignHorizontal;
  final NetalTextAlign? textAlignVertical;

  // final NetalTypesettingMode? typesettingMode;
  // final List<int>? typesettingParam;
  final num? wordSpacing;
  final num? lineSpacing;
  final num? letterSpacing;
  final NetalTextLineBreakMode? lineBreakMode;
  final bool? hasVipRes;

  // final bool? colorReverse;

  const TableCellElementUpdater({
    this.value,
    this.dataBind,
    this.fontCode,
    this.fontFamily,
    this.fontSize,
    this.fontStyle,
    this.textAlignHorizontal,
    this.textAlignVertical,
    // this.typesettingParam,
    // this.typesettingMode,
    this.wordSpacing,
    this.lineSpacing,
    this.letterSpacing,
    this.lineBreakMode,
    this.hasVipRes,
    // this.colorReverse
  });

  TableCellElementUpdater copyWith(
      {final String? value,
      final CopyWrapper<List<String>?>? dataBind,
      final String? fontCode,
      final String? fontFamily,
      final num? fontSize,
      final List<NetalTextFontStyle>? fontStyle,
      final NetalTextAlign? textAlignHorizontal,
      final NetalTextAlign? textAlignVertical,
      final NetalTypesettingMode? typesettingMode,
      final List<int>? typesettingParam,
      final num? wordSpacing,
      final num? lineSpacing,
      final num? letterSpacing,
      final NetalTextLineBreakMode? lineBreakMode,
      final bool? hasVipRes,
      final bool? colorReverse}) {
    return TableCellElementUpdater(
      textAlignHorizontal: textAlignHorizontal ?? this.textAlignHorizontal,
      textAlignVertical: textAlignVertical ?? this.textAlignVertical,
      wordSpacing: wordSpacing ?? this.wordSpacing,
      letterSpacing: letterSpacing ?? this.letterSpacing,
      lineSpacing: lineSpacing ?? this.lineSpacing,
      fontCode: fontCode ?? this.fontCode,
      fontFamily: fontFamily ?? this.fontFamily,
      fontSize: fontSize ?? this.fontSize,
      lineBreakMode: lineBreakMode ?? this.lineBreakMode,
      hasVipRes: hasVipRes ?? this.hasVipRes,
      fontStyle: fontStyle ?? this.fontStyle,
      // typesettingParam: typesettingParam ?? this.typesettingParam,
      value: value ?? this.value,
      dataBind: dataBind ?? this.dataBind,
    );
  }
}
