
import 'package:netal_plugin/models/copy_wrapper.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:netal_plugin/netal_image_result.dart';
import 'package:niimbot_flutter_canvas/provider/models/base_element_updater.dart';
import 'package:niimbot_template/models/elements/element_enum.dart';

import 'package:niimbot_flutter_canvas/provider/models/color_element_updater.dart';

class LineElementUpdater extends ColorElementUpdater {
  final List<num>? dashWidth;
  final NetalLineType? lineType;

  const LineElementUpdater({
    super.x,
    super.y,
    super.dx,
    super.dy,
    super.width,
    super.height,
    super.zIndex,
    super.rotate,
    super.isLock,
    super.isOpenMirror,
    super.mirrorType,
    super.hasVipRes,
    super.colorChannel,
    super.elementColor,
    super.paperColorIndex,
    super.imageCache,
    this.dashWidth,
    this.lineType,
  });

  @override
  bool get isNotEmpty {
    return super.isNotEmpty ||
        colorChannel != null ||
        height != null ||
        lineType != null;
  }

  @override
  bool get clearImageCache {
    return super.clearImageCache ||
        colorChannel != null ||
        height != null ||
        lineType != null;
  }

  @override
  LineElementUpdater copyWith({
    final num? x,
    final num? y,
    final num? dx,
    final num? dy,
    final num? width,
    final num? height,
    final int? zIndex,
    final int? rotate,
    final bool? isLock,
    final bool? isOpenMirror,
    final ElementMirrorType? mirrorType,
    final bool? hasVipRes,
    final bool? dragging,
    final bool? focused,
    final bool? selected,
    final CopyWrapper<NetalImageResult?>? imageCache,
    final CopyWrapper<KeepPosition?>? keepPosition,
  }) {
    return LineElementUpdater(
      x: x ?? this.x,
      y: y ?? this.y,
      dx: dx ?? this.dx,
      dy: dy ?? this.dy,
      width: width ?? this.width,
      height: height ?? this.height,
      zIndex: zIndex ?? this.zIndex,
      rotate: rotate ?? this.rotate,
      isLock: isLock ?? this.isLock,
      isOpenMirror: isOpenMirror ?? this.isOpenMirror,
      mirrorType: mirrorType ?? this.mirrorType,
      hasVipRes: hasVipRes ?? this.hasVipRes,
      imageCache: imageCache ?? this.imageCache,
    );
  }
}
