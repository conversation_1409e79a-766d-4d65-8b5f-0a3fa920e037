import 'dart:ui';

import 'package:netal_plugin/models/copy_wrapper.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:netal_plugin/netal_image_result.dart';
import 'package:niimbot_flutter_canvas/provider/models/base_element_updater.dart';
import 'package:niimbot_template/models/elements/element_enum.dart';

import 'package:niimbot_flutter_canvas/provider/models/text_element_updater.dart';

class SerialElementUpdater extends TextElementUpdater {
  final CopyWrapper<String?>? prefix;
  final String? startNumber;
  final CopyWrapper<String?>? suffix;
  final int? incrementValue;
  final int? fixLength;
  final String? fixValue;

  const SerialElementUpdater({
    super.x,
    super.y,
    super.dx,
    super.dy,
    super.width,
    super.height,
    super.zIndex,
    super.rotate,
    super.isLock,
    super.isOpenMirror,
    super.mirrorType,
    super.hasVipRes,
    super.elementColor,
    super.paperColorIndex,
    super.fontFamily,
    super.fontCode,
    super.fontSize,
    super.horizontalAlign,
    super.verticalAlign,
    super.fontStyle,
    super.letterSpacing,
    super.lineSpacing,
    super.typesettingMode,
    super.lineBreakMode,
    super.colorReverse,
    super.colorChannel,
    super.lastWidth,
    super.lastHeight,
    super.dataBind,
    super.typesettingParam,
    super.boxStyle,
    super.keepPosition,
    super.imageCache,
    this.prefix,
    this.startNumber,
    this.suffix,
    this.incrementValue,
    this.fixLength,
    this.fixValue,
  });

  @override
  bool get isNotEmpty {
    return super.isNotEmpty ||
        prefix != null ||
        startNumber != null ||
        suffix != null ||
        incrementValue != null ||
        fixLength != null ||
        fixValue != null;
  }

  @override
  bool get clearImageCache {
    return super.clearImageCache ||
        prefix != null ||
        startNumber != null ||
        suffix != null ||
        incrementValue != null ||
        fixLength != null ||
        fixValue != null;
  }

  @override
  SerialElementUpdater copyWith({
    final num? x,
    final num? y,
    final num? dx,
    final num? dy,
    final num? width,
    final num? height,
    final int? zIndex,
    final int? rotate,
    final bool? isLock,
    final bool? isOpenMirror,
    final ElementMirrorType? mirrorType,
    final bool? hasVipRes,
    final bool? dragging,
    final bool? focused,
    final bool? selected,
    final CopyWrapper<NetalImageResult?>? imageCache,
    final String? value,
    final int? colorChannel,
    final Color? elementColor,
    final int? paperColorIndex,
    final double? lastWidth,
    final double? lastHeight,
    final CopyWrapper<String?>? prefix,
    final String? startNumber,
    final CopyWrapper<String?>? suffix,
    final int? incrementValue,
    final int? fixLength,
    final String? fixValue,
    final NetalTextBoxStyle? boxStyle,
    final CopyWrapper<KeepPosition?>? keepPosition,
  }) {
    return SerialElementUpdater(
      x: x ?? this.x,
      y: y ?? this.y,
      dx: dx ?? this.dx,
      dy: dy ?? this.dy,
      width: width ?? this.width,
      height: height ?? this.height,
      zIndex: zIndex ?? this.zIndex,
      rotate: rotate ?? this.rotate,
      isLock: isLock ?? this.isLock,
      isOpenMirror: isOpenMirror ?? this.isOpenMirror,
      mirrorType: mirrorType ?? this.mirrorType,
      hasVipRes: hasVipRes ?? this.hasVipRes,
      colorChannel: colorChannel ?? this.colorChannel,
      elementColor: elementColor ?? this.elementColor,
      paperColorIndex: paperColorIndex ?? this.paperColorIndex,
      lastWidth: lastWidth ?? this.lastWidth,
      lastHeight: lastHeight ?? this.lastHeight,
      prefix: prefix ?? this.prefix,
      startNumber: startNumber ?? this.startNumber,
      suffix: suffix ?? this.suffix,
      incrementValue: incrementValue ?? this.incrementValue,
      fixLength: fixLength ?? this.fixLength,
      fixValue: fixValue ?? this.fixValue,
      boxStyle: boxStyle ?? this.boxStyle,
      keepPosition: keepPosition ?? this.keepPosition,
      imageCache: imageCache ?? this.imageCache,
    );
  }
}
