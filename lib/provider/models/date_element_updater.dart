import 'dart:ui';

import 'package:netal_plugin/models/copy_wrapper.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:netal_plugin/netal_image_result.dart';
import 'package:niimbot_flutter_canvas/model/date_element_helper.dart';
import 'package:niimbot_flutter_canvas/provider/models/base_element_updater.dart';
import 'package:niimbot_template/models/elements/element_enum.dart';

import 'package:niimbot_flutter_canvas/provider/models/text_element_updater.dart';

class DateElementUpdater extends TextElementUpdater {
  final CopyWrapper<ElementDateFormat?>? dateFormat;

  final CopyWrapper<ElementTimeFormat?>? timeFormat;

  final int? time;

  final bool? dateIsRefresh;

  /// 前缀
  final CopyWrapper<String?>? contentTitle;

  /// 12小时制
  final CopyWrapper<ElementTimeUnit?>? timeUnit;

  /// 时间偏移
  final int? timeOffset;

  /// 是否被关联（true：第一时间, false：第二时间）
  final bool? associated;

  /// 关联Id
  final CopyWrapper<String?>? associateId;

  /// 关联时间值
  final CopyWrapper<int?>? validityPeriod;



  /// 关联时间单位
  final CopyWrapper<ElementDateAssociatedUnit?>? validityPeriodUnit;

  int get validityPeriodNew {
    int validityPeriodNewValue = validityPeriod?.value ?? 0;
    if (validityPeriodUnit == DateElementHelper.Associated_Day) {
      validityPeriodNewValue = validityPeriod - 1;
    }
    return validityPeriodNewValue;
  }


  set validityPeriodNew(int value) {
    int num = value;
    if (validityPeriodUnit == DateElementHelper.Associated_Day) {
      validityPeriod = value + 1;
    }
  }

  const DateElementUpdater({
    super.x,
    super.y,
    super.dx,
    super.dy,
    super.width,
    super.height,
    super.zIndex,
    super.rotate,
    super.isLock,
    super.isOpenMirror,
    super.mirrorType,
    super.hasVipRes,
    super.elementColor,
    super.paperColorIndex,
    super.fontFamily,
    super.fontCode,
    super.fontSize,
    super.horizontalAlign,
    super.verticalAlign,
    super.fontStyle,
    super.letterSpacing,
    super.lineSpacing,
    super.typesettingMode,
    super.lineBreakMode,
    super.colorReverse,
    super.colorChannel,
    super.lastWidth,
    super.lastHeight,
    super.dataBind,
    super.typesettingParam,
    super.value,
    super.boxStyle,
    super.keepPosition,
    super.imageCache,
    this.time,
    this.dateFormat,
    this.timeFormat,
    this.dateIsRefresh,
    this.contentTitle,
    this.timeUnit,
    this.timeOffset,
    this.associated,
    this.associateId,
    this.validityPeriod,
    this.validityPeriodUnit,
  });

  @override
  bool get isNotEmpty {
    return super.isNotEmpty ||
        time != null ||
        dateFormat != null ||
        timeFormat != null ||
        dateIsRefresh != null ||
        contentTitle != null ||
        timeUnit != null ||
        timeOffset != null ||
        associated != null ||
        associateId != null ||
        validityPeriod != null ||
        validityPeriodUnit != null;
  }

  @override
  bool get clearImageCache {
    return super.clearImageCache ||
        time != null ||
        dateFormat != null ||
        timeFormat != null ||
        dateIsRefresh != null ||
        contentTitle != null ||
        timeUnit != null ||
        timeOffset != null ||
        associated != null ||
        associateId != null ||
        validityPeriod != null ||
        validityPeriodUnit != null;
  }

  @override
  DateElementUpdater copyWith({
    final num? x,
    final num? y,
    final num? dx,
    final num? dy,
    final num? width,
    final num? height,
    final int? zIndex,
    final int? rotate,
    final bool? isLock,
    final bool? isOpenMirror,
    final ElementMirrorType? mirrorType,
    final bool? hasVipRes,
    final CopyWrapper<NetalImageResult?>? imageCache,
    final String? value,
    final int? colorChannel,
    final Color? elementColor,
    final int? paperColorIndex,
    final double? lastWidth,
    final double? lastHeight,
    final NetalTextBoxStyle? boxStyle,
    final CopyWrapper<KeepPosition?>? keepPosition,
    final CopyWrapper<String?>? contentTitle,
    final CopyWrapper<ElementTimeUnit?>? timeUnit,
    final int? timeOffset,
    final bool? associated,
    final CopyWrapper<String?>? associateId,
    final CopyWrapper<String?>? associateContentTitle,
    final CopyWrapper<Size?>? associateSize,
    final CopyWrapper<Offset?>? associateOffset,
    final CopyWrapper<int?>? validityPeriod,
    final CopyWrapper<ElementDateAssociatedUnit?>? validityPeriodUnit,
    final bool? dateIsRefresh,
    final CopyWrapper<ElementDateFormat?>? dateFormat,
    final CopyWrapper<ElementTimeFormat?>? timeFormat,
  }) {
    return DateElementUpdater(
      x: x ?? this.x,
      y: y ?? this.y,
      dx: dx ?? this.dx,
      dy: dy ?? this.dy,
      width: width ?? this.width,
      height: height ?? this.height,
      zIndex: zIndex ?? this.zIndex,
      rotate: rotate ?? this.rotate,
      isLock: isLock ?? this.isLock,
      isOpenMirror: isOpenMirror ?? this.isOpenMirror,
      mirrorType: mirrorType ?? this.mirrorType,
      hasVipRes: hasVipRes ?? this.hasVipRes,
      colorChannel: colorChannel ?? this.colorChannel,
      elementColor: elementColor ?? this.elementColor,
      paperColorIndex: paperColorIndex ?? this.paperColorIndex,
      lastWidth: lastWidth ?? this.lastWidth,
      lastHeight: lastHeight ?? this.lastHeight,
      boxStyle: boxStyle ?? this.boxStyle,
      keepPosition: keepPosition ?? this.keepPosition,
      contentTitle: contentTitle ?? this.contentTitle,
      timeUnit: timeUnit ?? this.timeUnit,
      timeOffset: timeOffset ?? this.timeOffset,
      associated: associated ?? this.associated,
      associateId: associateId ?? this.associateId,
      validityPeriod: validityPeriod ?? this.validityPeriod,
      validityPeriodNew:validityPeriodNew?? this.validityPeriodNew
      validityPeriodUnit: validityPeriodUnit ?? this.validityPeriodUnit,
      dateIsRefresh: dateIsRefresh ?? this.dateIsRefresh,
      dateFormat: dateFormat ?? this.dateFormat,
      timeFormat: timeFormat ?? this.timeFormat,
      imageCache: imageCache ?? this.imageCache,
    );
  }
}
