import 'package:flutter/cupertino.dart';
import 'package:netal_plugin/models/copy_wrapper.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:netal_plugin/netal_image_result.dart';
import 'package:niimbot_flutter_canvas/provider/models/base_element_updater.dart';
import 'package:niimbot_template/models/elements/element_enum.dart';

import 'package:niimbot_flutter_canvas/provider/models/value_element_updater.dart';


@immutable
class TextElementUpdater extends ValueElementUpdater {
  final double? lastWidth;
  final double? lastHeight;
  final CopyWrapper<List<String>?>? dataBind;
  final NetalTextAlign? horizontalAlign;
  final NetalTextAlign? verticalAlign;
  final String? fontCode;
  final String? fontFamily;
  final List<NetalTextFontStyle>? fontStyle;
  final num? letterSpacing;
  final num? wordSpacing;
  final num? lineSpacing;
  final NetalTypesettingMode? typesettingMode;
  final NetalTextLineBreakMode? lineBreakMode;
  final num? fontSize;
  final bool? colorReverse;
  final NetalTextAlign? textAlignVertical;
  final List<int>? typesettingParam;
  final NetalTextBoxStyle? boxStyle;

  const TextElementUpdater({
    super.x,
    super.y,
    super.dx,
    super.dy,
    super.width,
    super.height,
    super.zIndex,
    super.rotate,
    super.isLock,
    super.isOpenMirror,
    super.mirrorType,
    super.hasVipRes,
    super.colorChannel,
    super.elementColor,
    super.paperColorIndex,
    super.value,
    super.imageCache,
    super.keepPosition,
    this.lastWidth,
    this.lastHeight,
    this.dataBind,
    this.horizontalAlign,
    this.verticalAlign,
    this.fontFamily,
    this.fontCode,
    this.fontStyle,
    this.letterSpacing,
    this.wordSpacing,
    this.lineSpacing,
    this.typesettingMode,
    this.lineBreakMode,
    this.fontSize,
    this.colorReverse,
    this.textAlignVertical,
    this.typesettingParam,
    this.boxStyle,
  });

  @override
  bool get isNotEmpty {
    return super.isNotEmpty ||
        lastHeight != null ||
        lastWidth != null ||
        dataBind != null ||
        value != null ||
        horizontalAlign != null ||
        verticalAlign != null ||
        fontFamily != null ||
        fontCode != null ||
        fontStyle != null ||
        letterSpacing != null ||
        lineSpacing != null ||
        typesettingMode != null ||
        lineBreakMode != null ||
        fontSize != null ||
        colorReverse != null ||
        colorChannel != null ||
        textAlignVertical != null ||
        typesettingParam != null ||
        boxStyle != null;
  }

  @override
  bool get clearImageCache {
    return super.clearImageCache ||
        dataBind != null ||
        value != null ||
        horizontalAlign != null ||
        verticalAlign != null ||
        fontFamily != null ||
        fontCode != null ||
        fontStyle != null ||
        letterSpacing != null ||
        lineSpacing != null ||
        typesettingMode != null ||
        lineBreakMode != null ||
        fontSize != null ||
        colorReverse != null ||
        colorChannel != null ||
        textAlignVertical != null ||
        typesettingParam != null ||
        boxStyle != null;
  }

  @override
  TextElementUpdater copyWith({
    final num? x,
    final num? y,
    final num? dx,
    final num? dy,
    final num? width,
    final num? height,
    final int? zIndex,
    final int? rotate,
    final bool? isLock,
    final bool? isOpenMirror,
    final ElementMirrorType? mirrorType,
    final bool? hasVipRes,
    final CopyWrapper<NetalImageResult?>? imageCache,
    final String? value,
    final int? colorChannel,
    final Color? elementColor,
    final int? paperColorIndex,
    final double? lastWidth,
    final double? lastHeight,
    final NetalTextBoxStyle? boxStyle,
    final CopyWrapper<KeepPosition?>? keepPosition,
  }) {
    return TextElementUpdater(
      x: x ?? this.x,
      y: y ?? this.y,
      dx: dx ?? this.dx,
      dy: dy ?? this.dy,
      width: width ?? this.width,
      height: height ?? this.height,
      zIndex: zIndex ?? this.zIndex,
      rotate: rotate ?? this.rotate,
      isLock: isLock ?? this.isLock,
      isOpenMirror: isOpenMirror ?? this.isOpenMirror,
      mirrorType: mirrorType ?? this.mirrorType,
      hasVipRes: hasVipRes ?? this.hasVipRes,
      colorChannel: colorChannel ?? this.colorChannel,
      elementColor: elementColor ?? this.elementColor,
      paperColorIndex: paperColorIndex ?? this.paperColorIndex,
      value: value ?? this.value,
      lastWidth: lastWidth ?? this.lastWidth,
      lastHeight: lastHeight ?? this.lastHeight,
      boxStyle: boxStyle ?? this.boxStyle,
      keepPosition: keepPosition ?? this.keepPosition,
      imageCache: imageCache ?? this.imageCache,
    );
  }
}
