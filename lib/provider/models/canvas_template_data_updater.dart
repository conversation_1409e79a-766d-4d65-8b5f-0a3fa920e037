import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:niimbot_template/models/elements/base_element.dart';
import 'package:niimbot_template/models/template/file_data_source.dart';
import 'package:niimbot_template/models/template/label_name_info.dart';
import 'package:niimbot_template/models/template/template_data_source.dart';
import 'package:niimbot_template/models/template/template_data_source_info.dart';
import 'package:niimbot_template/models/template/template_data_source_modify.dart';
import 'package:niimbot_template/models/template/template_profile.dart';

part 'canvas_template_data_updater.freezed.dart';

@freezed
class CanvasTemplateDataUpdater with _$CanvasTemplateDataUpdater {
  final String? id;
  final String? name;
  final String? cloudTemplateId;
  final List<LabelNameInfo>? names;
  final List<LabelNameInfo>? labelNames;
  final String? description;
  final String? thumbnail;
  final String? backgroundImage;
  final int? multipleBackIndex;
  final num? width;
  final num? height;
  final num? rotate;
  final num? canvasRotate;
  final int? consumableType;
  final int? paperType;
  final bool? isCable;
  final num? cableLength;
  final NetalCableDirection? cableDirection;
  final List<num>? margin;
  final List<BaseElement>? elements;
  final String? labelId;
  final num? accuracyName;
  final bool? hasVipRes;
  final bool? vip;
  final bool? commodityTemplate;
  final List<String>? paperColor;
  final TemplateDataSourceModifies? dataSourceModifies;
  final List<TemplateDataSource>? dataSources;
  final TemplateDataSourceInfo? dataSourceBindInfo;
  final TemplateProfile? profile;

  // final num? templatePrintMode;
  final List<String>? localBackground;
  final List<FileDataSource>? fileDataSources;

  // final String? localContentThumb;

  const CanvasTemplateDataUpdater({
    this.id,
    this.name,
    this.cloudTemplateId,
    this.names,
    this.labelNames,
    this.description,
    this.thumbnail,
    this.backgroundImage,
    this.multipleBackIndex,
    this.width,
    this.height,
    this.rotate,
    this.canvasRotate,
    this.consumableType,
    this.paperType,
    this.isCable,
    this.cableLength,
    this.cableDirection,
    this.margin,
    this.elements,
    this.labelId,
    this.accuracyName,
    this.hasVipRes,
    this.vip,
    this.commodityTemplate,
    this.paperColor,
    this.dataSourceModifies,
    this.dataSources,
    this.dataSourceBindInfo,
    this.profile,
    // this.templatePrintMode,
    this.localBackground,
    this.fileDataSources,
    // this.localContentThumb,
  });
}
