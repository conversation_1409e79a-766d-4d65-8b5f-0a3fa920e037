import 'dart:ui';

import 'package:niimbot_flutter_canvas/provider/models/base_element_updater.dart';

class ColorElementUpdater extends BaseElementUpdater {
  final int? colorChannel;
  final Color? elementColor;
  final int? paperColorIndex;

  const ColorElementUpdater({
    super.x,
    super.y,
    super.dx,
    super.dy,
    super.width,
    super.height,
    super.zIndex,
    super.rotate,
    super.isLock,
    super.isOpenMirror,
    super.mirrorType,
    super.hasVipRes,
    super.imageCache,
    super.keepPosition,
    this.colorChannel,
    this.elementColor,
    this.paperColorIndex,
  });

  /// 是否为空
  @override
  bool get isNotEmpty {
    return super.isNotEmpty ||
        colorChannel != null ||
        elementColor != null ||
        paperColorIndex != null;
  }

  @override
  bool get clearImageCache {
    return super.clearImageCache ||
        colorChannel != null ||
        elementColor != null ||
        paperColorIndex != null;
  }
}
