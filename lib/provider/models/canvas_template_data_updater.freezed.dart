// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'canvas_template_data_updater.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$CanvasTemplateDataUpdater {
  String? get id;
  String? get name;
  String? get cloudTemplateId;
  List<LabelNameInfo>? get names; // final List<LabelNameInfo>? labelNames;
  String? get description;
  String? get thumbnail;
  String? get backgroundImage;
  int? get multipleBackIndex;
  num? get width;
  num? get height;
  num? get rotate;
  num? get canvasRotate;
  int? get consumableType;
  int? get paperType;
  bool? get isCable;
  num? get cableLength;
  NetalCableDirection? get cableDirection;
  List<num>? get margin;
  List<BaseElement>? get elements;
  String? get labelId;
  num? get accuracyName;
  bool? get hasVipRes;
  bool? get vip;
  bool? get commodityTemplate;
  List<String>? get paperColor;
  TemplateDataSourceModifies? get dataSourceModifies;
  List<TemplateDataSource>? get dataSources;
  TemplateDataSourceInfo? get dataSourceBindInfo;
  TemplateProfile? get profile;

  /// Create a copy of CanvasTemplateDataUpdater
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CanvasTemplateDataUpdaterCopyWith<CanvasTemplateDataUpdater> get copyWith =>
      _$CanvasTemplateDataUpdaterCopyWithImpl<CanvasTemplateDataUpdater>(
          this as CanvasTemplateDataUpdater, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CanvasTemplateDataUpdater &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.cloudTemplateId, cloudTemplateId) ||
                other.cloudTemplateId == cloudTemplateId) &&
            const DeepCollectionEquality().equals(other.names, names) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.thumbnail, thumbnail) ||
                other.thumbnail == thumbnail) &&
            (identical(other.backgroundImage, backgroundImage) ||
                other.backgroundImage == backgroundImage) &&
            (identical(other.multipleBackIndex, multipleBackIndex) ||
                other.multipleBackIndex == multipleBackIndex) &&
            (identical(other.width, width) || other.width == width) &&
            (identical(other.height, height) || other.height == height) &&
            (identical(other.rotate, rotate) || other.rotate == rotate) &&
            (identical(other.canvasRotate, canvasRotate) ||
                other.canvasRotate == canvasRotate) &&
            (identical(other.consumableType, consumableType) ||
                other.consumableType == consumableType) &&
            (identical(other.paperType, paperType) ||
                other.paperType == paperType) &&
            (identical(other.isCable, isCable) || other.isCable == isCable) &&
            (identical(other.cableLength, cableLength) ||
                other.cableLength == cableLength) &&
            (identical(other.cableDirection, cableDirection) ||
                other.cableDirection == cableDirection) &&
            const DeepCollectionEquality().equals(other.margin, margin) &&
            const DeepCollectionEquality().equals(other.elements, elements) &&
            (identical(other.labelId, labelId) || other.labelId == labelId) &&
            (identical(other.accuracyName, accuracyName) ||
                other.accuracyName == accuracyName) &&
            (identical(other.hasVipRes, hasVipRes) ||
                other.hasVipRes == hasVipRes) &&
            (identical(other.vip, vip) || other.vip == vip) &&
            (identical(other.commodityTemplate, commodityTemplate) ||
                other.commodityTemplate == commodityTemplate) &&
            const DeepCollectionEquality()
                .equals(other.paperColor, paperColor) &&
            const DeepCollectionEquality()
                .equals(other.dataSourceModifies, dataSourceModifies) &&
            const DeepCollectionEquality()
                .equals(other.dataSources, dataSources) &&
            (identical(other.dataSourceBindInfo, dataSourceBindInfo) ||
                other.dataSourceBindInfo == dataSourceBindInfo) &&
            (identical(other.profile, profile) || other.profile == profile));
  }

  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        name,
        cloudTemplateId,
        const DeepCollectionEquality().hash(names),
        description,
        thumbnail,
        backgroundImage,
        multipleBackIndex,
        width,
        height,
        rotate,
        canvasRotate,
        consumableType,
        paperType,
        isCable,
        cableLength,
        cableDirection,
        const DeepCollectionEquality().hash(margin),
        const DeepCollectionEquality().hash(elements),
        labelId,
        accuracyName,
        hasVipRes,
        vip,
        commodityTemplate,
        const DeepCollectionEquality().hash(paperColor),
        const DeepCollectionEquality().hash(dataSourceModifies),
        const DeepCollectionEquality().hash(dataSources),
        dataSourceBindInfo,
        profile
      ]);

  @override
  String toString() {
    return 'CanvasTemplateDataUpdater(id: $id, name: $name, cloudTemplateId: $cloudTemplateId, names: $names, description: $description, thumbnail: $thumbnail, backgroundImage: $backgroundImage, multipleBackIndex: $multipleBackIndex, width: $width, height: $height, rotate: $rotate, canvasRotate: $canvasRotate, consumableType: $consumableType, paperType: $paperType, isCable: $isCable, cableLength: $cableLength, cableDirection: $cableDirection, margin: $margin, elements: $elements, labelId: $labelId, accuracyName: $accuracyName, hasVipRes: $hasVipRes, vip: $vip, commodityTemplate: $commodityTemplate, paperColor: $paperColor, dataSourceModifies: $dataSourceModifies, dataSources: $dataSources, dataSourceBindInfo: $dataSourceBindInfo, profile: $profile)';
  }
}

/// @nodoc
abstract mixin class $CanvasTemplateDataUpdaterCopyWith<$Res> {
  factory $CanvasTemplateDataUpdaterCopyWith(CanvasTemplateDataUpdater value,
          $Res Function(CanvasTemplateDataUpdater) _then) =
      _$CanvasTemplateDataUpdaterCopyWithImpl;
  @useResult
  $Res call(
      {String? id,
      String? name,
      String? cloudTemplateId,
      List<LabelNameInfo>? names,
      String? description,
      String? thumbnail,
      String? backgroundImage,
      int? multipleBackIndex,
      num? width,
      num? height,
      num? rotate,
      num? canvasRotate,
      int? consumableType,
      int? paperType,
      bool? isCable,
      num? cableLength,
      NetalCableDirection? cableDirection,
      List<num>? margin,
      List<BaseElement>? elements,
      String? labelId,
      num? accuracyName,
      bool? hasVipRes,
      bool? vip,
      bool? commodityTemplate,
      List<String>? paperColor,
      Map<String, Map<String, TemplateDataSourceModify>>? dataSourceModifies,
      List<TemplateDataSource>? dataSources,
      TemplateDataSourceInfo? dataSourceBindInfo,
      TemplateProfile? profile});
}

/// @nodoc
class _$CanvasTemplateDataUpdaterCopyWithImpl<$Res>
    implements $CanvasTemplateDataUpdaterCopyWith<$Res> {
  _$CanvasTemplateDataUpdaterCopyWithImpl(this._self, this._then);

  final CanvasTemplateDataUpdater _self;
  final $Res Function(CanvasTemplateDataUpdater) _then;

  /// Create a copy of CanvasTemplateDataUpdater
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? cloudTemplateId = freezed,
    Object? names = freezed,
    Object? description = freezed,
    Object? thumbnail = freezed,
    Object? backgroundImage = freezed,
    Object? multipleBackIndex = freezed,
    Object? width = freezed,
    Object? height = freezed,
    Object? rotate = freezed,
    Object? canvasRotate = freezed,
    Object? consumableType = freezed,
    Object? paperType = freezed,
    Object? isCable = freezed,
    Object? cableLength = freezed,
    Object? cableDirection = freezed,
    Object? margin = freezed,
    Object? elements = freezed,
    Object? labelId = freezed,
    Object? accuracyName = freezed,
    Object? hasVipRes = freezed,
    Object? vip = freezed,
    Object? commodityTemplate = freezed,
    Object? paperColor = freezed,
    Object? dataSourceModifies = freezed,
    Object? dataSources = freezed,
    Object? dataSourceBindInfo = freezed,
    Object? profile = freezed,
  }) {
    return _then(CanvasTemplateDataUpdater(
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      cloudTemplateId: freezed == cloudTemplateId
          ? _self.cloudTemplateId
          : cloudTemplateId // ignore: cast_nullable_to_non_nullable
              as String?,
      names: freezed == names
          ? _self.names
          : names // ignore: cast_nullable_to_non_nullable
              as List<LabelNameInfo>?,
      description: freezed == description
          ? _self.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      thumbnail: freezed == thumbnail
          ? _self.thumbnail
          : thumbnail // ignore: cast_nullable_to_non_nullable
              as String?,
      backgroundImage: freezed == backgroundImage
          ? _self.backgroundImage
          : backgroundImage // ignore: cast_nullable_to_non_nullable
              as String?,
      multipleBackIndex: freezed == multipleBackIndex
          ? _self.multipleBackIndex
          : multipleBackIndex // ignore: cast_nullable_to_non_nullable
              as int?,
      width: freezed == width
          ? _self.width
          : width // ignore: cast_nullable_to_non_nullable
              as num?,
      height: freezed == height
          ? _self.height
          : height // ignore: cast_nullable_to_non_nullable
              as num?,
      rotate: freezed == rotate
          ? _self.rotate
          : rotate // ignore: cast_nullable_to_non_nullable
              as num?,
      canvasRotate: freezed == canvasRotate
          ? _self.canvasRotate
          : canvasRotate // ignore: cast_nullable_to_non_nullable
              as num?,
      consumableType: freezed == consumableType
          ? _self.consumableType
          : consumableType // ignore: cast_nullable_to_non_nullable
              as int?,
      paperType: freezed == paperType
          ? _self.paperType
          : paperType // ignore: cast_nullable_to_non_nullable
              as int?,
      isCable: freezed == isCable
          ? _self.isCable
          : isCable // ignore: cast_nullable_to_non_nullable
              as bool?,
      cableLength: freezed == cableLength
          ? _self.cableLength
          : cableLength // ignore: cast_nullable_to_non_nullable
              as num?,
      cableDirection: freezed == cableDirection
          ? _self.cableDirection
          : cableDirection // ignore: cast_nullable_to_non_nullable
              as NetalCableDirection?,
      margin: freezed == margin
          ? _self.margin
          : margin // ignore: cast_nullable_to_non_nullable
              as List<num>?,
      elements: freezed == elements
          ? _self.elements
          : elements // ignore: cast_nullable_to_non_nullable
              as List<BaseElement>?,
      labelId: freezed == labelId
          ? _self.labelId
          : labelId // ignore: cast_nullable_to_non_nullable
              as String?,
      accuracyName: freezed == accuracyName
          ? _self.accuracyName
          : accuracyName // ignore: cast_nullable_to_non_nullable
              as num?,
      hasVipRes: freezed == hasVipRes
          ? _self.hasVipRes
          : hasVipRes // ignore: cast_nullable_to_non_nullable
              as bool?,
      vip: freezed == vip
          ? _self.vip
          : vip // ignore: cast_nullable_to_non_nullable
              as bool?,
      commodityTemplate: freezed == commodityTemplate
          ? _self.commodityTemplate
          : commodityTemplate // ignore: cast_nullable_to_non_nullable
              as bool?,
      paperColor: freezed == paperColor
          ? _self.paperColor
          : paperColor // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      dataSourceModifies: freezed == dataSourceModifies
          ? _self.dataSourceModifies
          : dataSourceModifies // ignore: cast_nullable_to_non_nullable
              as Map<String, Map<String, TemplateDataSourceModify>>?,
      dataSources: freezed == dataSources
          ? _self.dataSources
          : dataSources // ignore: cast_nullable_to_non_nullable
              as List<TemplateDataSource>?,
      dataSourceBindInfo: freezed == dataSourceBindInfo
          ? _self.dataSourceBindInfo
          : dataSourceBindInfo // ignore: cast_nullable_to_non_nullable
              as TemplateDataSourceInfo?,
      profile: freezed == profile
          ? _self.profile
          : profile // ignore: cast_nullable_to_non_nullable
              as TemplateProfile?,
    ));
  }
}

// dart format on
