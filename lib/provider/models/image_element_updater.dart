import 'package:netal_plugin/models/copy_wrapper.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:netal_plugin/netal_image_result.dart';
import 'package:niimbot_flutter_canvas/provider/models/base_element_updater.dart';
import 'package:niimbot_template/models/elements/element_enum.dart';

import 'package:niimbot_flutter_canvas/provider/models/color_element_updater.dart';

class ImageElementUpdater extends ColorElementUpdater {
  final NetalImageRenderType? imageProcessingType;

  final List<num>? imageProcessingValue;

  final bool? allowFreeZoom;

  final String? imageData;

  final String? localUrl;

  final CopyWrapper<String?>? imageUrl;

  final CopyWrapper<String?>? materialId;
  final double? lastWidth;
  final double? lastHeight;
  final bool? colorReverse;

  const ImageElementUpdater({
    super.x,
    super.y,
    super.dx,
    super.dy,
    super.width,
    super.height,
    super.zIndex,
    super.rotate,
    super.isLock,
    super.isOpenMirror,
    super.mirrorType,
    super.hasVipRes,
    super.colorChannel,
    super.elementColor,
    super.paperColorIndex,
    super.imageCache,
    this.imageProcessingType,
    this.imageProcessingValue,
    this.allowFreeZoom,
    this.imageData,
    this.localUrl,
    this.imageUrl,
    this.materialId,
    this.lastWidth,
    this.lastHeight,
    this.colorReverse,
  });

  @override
  bool get isNotEmpty {
    return super.isNotEmpty ||
        lastHeight != null ||
        lastWidth != null ||
        colorChannel != null ||
        height != null ||
        allowFreeZoom != null ||
        imageProcessingType != null ||
        colorReverse != null ||
        imageProcessingValue != null;
  }

  @override
  bool get clearImageCache {
    return super.clearImageCache ||
        colorChannel != null ||
        height != null ||
        allowFreeZoom != null ||
        imageProcessingType != null ||
        colorReverse != null ||
        imageProcessingValue != null;
  }

  @override
  ImageElementUpdater copyWith({
    final num? x,
    final num? y,
    final num? dx,
    final num? dy,
    final num? width,
    final num? height,
    final int? zIndex,
    final int? rotate,
    final bool? isLock,
    final bool? isOpenMirror,
    final ElementMirrorType? mirrorType,
    final bool? hasVipRes,
    final bool? dragging,
    final bool? focused,
    final bool? selected,
    final CopyWrapper<NetalImageResult?>? imageCache,
    final double? lastWidth,
    final double? lastHeight,
    final CopyWrapper<KeepPosition?>? keepPosition,
  }) {
    return ImageElementUpdater(
      x: x ?? this.x,
      y: y ?? this.y,
      dx: dx ?? this.dx,
      dy: dy ?? this.dy,
      width: width ?? this.width,
      height: height ?? this.height,
      zIndex: zIndex ?? this.zIndex,
      rotate: rotate ?? this.rotate,
      isLock: isLock ?? this.isLock,
      isOpenMirror: isOpenMirror ?? this.isOpenMirror,
      mirrorType: mirrorType ?? this.mirrorType,
      hasVipRes: hasVipRes ?? this.hasVipRes,
      lastWidth: lastWidth ?? this.lastWidth,
      lastHeight: lastHeight ?? this.lastHeight,
      imageCache: imageCache ?? this.imageCache,
    );
  }
}
