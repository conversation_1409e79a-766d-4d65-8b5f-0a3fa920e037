import 'package:niimbot_template/models/elements/base_element.dart';
import 'package:niimbot_template/models/elements/image_element.dart';
import 'package:niimbot_template/models/elements/material_element.dart';
import 'package:niimbot_template/models/elements/table_element.dart';

class StoreStatusUtils {
  /// 是否可以镜像元素
  static bool canMirror(final List<BaseElement> list) {
    bool disable = list.isEmpty;
    for (final e in list) {
      if (e.isLock ||
          e is TableElement ||
          e.isOpenMirror != list.first.isOpenMirror) {
        /// 锁定元素
        /// 表格元素
        /// 不是全部镜像/非镜像
        disable = true;
        break;
      }
    }
    return !disable;
  }

  /// 选中或聚焦元素是否开启镜像
  static bool isMirrorOpen(
      final List<BaseElement> list, final BaseElement? focused) {
    if (list.length > 1) {
      return list.every((final e) => e.isOpenMirror);
    }
    if (focused != null) {
      return focused.isOpenMirror;
    }
    return false;
  }

  /// 是否可以旋转元素
  static bool canRotate(final List<BaseElement> list) {
    bool disable = list.isEmpty;
    for (final e in list) {
      if (e.isLock) {
        /// 锁定元素
        disable = true;
        break;
      }
    }
    return !disable;
  }

  static bool canLock(
      final List<BaseElement> list, final BaseElement? focused) {
    if (list.length > 1) {
      return list.any((final e) => e.isLock);
    }
    if (focused != null) {
      return focused.isLock;
    }
    return false;
  }

  static bool canCrop(final List<BaseElement> list) {
    if (list.length > 1) {
      return false;
    }
    final selected = list.firstOrNull;
    return selected is ImageElement && selected is! MaterialElement;
  }
}
