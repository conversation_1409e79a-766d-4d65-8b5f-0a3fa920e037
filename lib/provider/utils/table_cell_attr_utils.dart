import 'package:collection/collection.dart';
import 'package:netal_plugin/models/copy_wrapper.dart';
import 'package:niimbot_template/models/elements/table_combine_cell_element.dart';

import 'package:niimbot_flutter_canvas/model/element/style_attr/table_cell_style_attr.dart';

class TableCellAttributeUtils {
  static TableCellStyleAttribute _buildTextStyleAttr(
      final TableCellStyleAttribute attr, final TableCombineCellElement element) {
    return attr.copyWith(
      fontFamily: attr.fontFamily == element.fontFamily
          ? null
          : const CopyWrapper.value(null),
      fontCode: attr.fontCode == element.fontCode
          ? null
          : const CopyWrapper.value(null),
      fontSize: attr.fontSize > element.fontSize ? element.fontSize : null,
      textAlignHorizontal:
          attr.textAlignHorizontal == element.textAlignHorizontal
              ? null
              : const CopyWrapper.value(null),
      textAlignVertical: attr.textAlignVertical == element.textAlignVertical
          ? null
          : const CopyWrapper.value(null),
      fontStyle:
          const DeepCollectionEquality().equals(attr.fontStyle, element.fontStyle)
              ? null
              : const CopyWrapper.value(null),
      letterSpacing: attr.letterSpacing > element.letterSpacing
          ? element.letterSpacing
          : null,
      lineSpacing:
          attr.lineSpacing > element.lineSpacing ? element.lineSpacing : null,
      lineBreakMode: attr.lineBreakMode == element.lineBreakMode
          ? null
          : const CopyWrapper.value(null),
    );
  }

  static TableCellStyleAttribute? buildSelectedStyleAttribute(
      final List<TableCombineCellElement> list) {
    var element = list.firstOrNull;
    TableCellStyleAttribute? attr;
    if (element is TableCombineCellElement) {
      attr = TableCellStyleAttribute(
        fontFamily: element.fontFamily,
        fontCode: element.fontCode,
        fontSize: element.fontSize,
        textAlignHorizontal: element.textAlignHorizontal,
        textAlignVertical: element.textAlignVertical,
        fontStyle: element.fontStyle,
        letterSpacing: element.letterSpacing,
        lineSpacing: element.lineSpacing,
        lineBreakMode: element.lineBreakMode,
      );
      for (var i = 1; i < list.length; i++) {
        final element = list[i];
        /* 单元格元素 */
        /* 且该当前元素位 是 文本/时间/序列号 */
        attr = _buildTextStyleAttr(attr!, element);
                    }
    }
    return attr;
  }
}
