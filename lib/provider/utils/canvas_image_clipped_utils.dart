import 'dart:async';
import 'dart:collection';
import 'dart:io';
import 'dart:isolate';
import 'package:image/image.dart' as img;
import 'dart:ui';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pointycastle/digests/sha1.dart';

///处理图片裁剪

class ImageClipper {
  static IsolatePool _pool = IsolatePool(Platform.numberOfProcessors);

  /// 初始化线程池
  static Future<void> init(final int size) async {
    _pool = IsolatePool(size);
    _pool.initialize();
  }

  /// 释放线程池资源
  static void dispose() => _pool.dispose();

  /// 批量裁剪图片
  static Future<void> cropImages(
    final List<String> paths,
    final Rect clippingRect, {
    final Function(double)? onProgress,
    final Function(ImageResult)? onResult,
    final Function(String)? onError,
  }) async {
    final tasks = paths.asMap().entries.map((final entry) {
      return _createTask(
        entry.value,
        clippingRect,
        entry.key,
      );
    }).toList();
    // final tasks = paths.map((final filePath) {
    //   return _createTask(
    //     filePath,
    //     clippingRect,
    //   );
    // }).toList();
    final resultSub = _pool.resultStream.listen((final result) {
      onResult?.call(result);
      // _pool._markTaskCompleted(); // 标记任务完成
    });
    final progressSub = _pool.progressStream.listen(onProgress);
    final errorSub = _pool.errorStream.listen((final error) {
      onError?.call(error);
      // _pool._markTaskCompleted(); // 错误也标记为任务完成
    });
    await _pool.runTasks(tasks);
    await _pool.awaitAllTasks();
    resultSub.cancel();
    progressSub.cancel();
    errorSub.cancel();
  }

  /// 创建裁剪任务
  static Map<String, dynamic> _createTask(
      final String path, final Rect rect, final int index) {
    return {
      'type': 'crop',
      'inputPath': path,
      'rect': [rect.left, rect.top, rect.right, rect.bottom],
      'index': index,
    };
  }
}

/// 裁剪结果数据模型
class ImageResult {
  final Uint8List bytes;
  final Rect normalizedRect;
  final String outPutPath;
  final int index;

  ImageResult(this.bytes, this.normalizedRect, this.outPutPath, this.index);
}

class IsolatePool {
  final List<Isolate> _isolates = [];
  final List<SendPort> _workerPorts = [];
  final List<ReceivePort> _receivePorts = [];
  final int _size;
  final Queue<Map<String, dynamic>> _taskQueue = Queue();
  late Completer<void> _initCompleter;

  late StreamController<ImageResult> _resultController;
  late StreamController<double> _progressController;
  late StreamController<String> _errorController;

  int _completedTasks = 0;
  int _totalTasks = 0;
  Completer<void> _allTasksCompleter = Completer();

  IsolatePool(this._size) {
    _initControllers();
  }

  void _initControllers() {
    _resultController = StreamController<ImageResult>.broadcast();
    _progressController = StreamController<double>.broadcast();
    _errorController = StreamController<String>.broadcast();
    _initCompleter = Completer();
  }

  /// 初始化线程池
  Future<void> initialize() async {
    if (_isolates.isNotEmpty) {
      dispose();
    }
    if (_resultController.isClosed) {
      _resultController = StreamController<ImageResult>.broadcast();
      _progressController = StreamController<double>.broadcast();
      _errorController = StreamController<String>.broadcast();
    }
    for (int i = 0; i < _size; i++) {
      final receivePort = ReceivePort();
      _receivePorts.add(receivePort);
      final isolate = await Isolate.spawn(
        _workerMain,
        receivePort.sendPort,
        debugName: 'IsolateWorker${i + 1}',
        errorsAreFatal: false,
      );

      ///receivePort.first  也会产生监听 第一次 listen 也会监听第二次
      // final workerPort = await receivePort.first as SendPort;
      final workerPortCompleter = Completer<SendPort>();
      receivePort.listen((final message) {
        if (message is SendPort) {
          workerPortCompleter.complete(message); // 初始SendPort
        } else if (message is Map && message['type'] == 'error') {
          _errorController.add('${message['message']}\n${message['stack']}');
        }
      });
      final workerPort = await workerPortCompleter.future;
      _workerPorts.add(workerPort);
      _isolates.add(isolate);
    }
    _initCompleter.complete();
  }

  /// 结果流
  Stream<ImageResult> get resultStream => _resultController.stream;

  /// 进度流（0.0 - 1.0）
  Stream<double> get progressStream => _progressController.stream;

  /// 错误流
  Stream<String> get errorStream => _errorController.stream;

  /// 执行批量任务
  Future<void> runTasks(final List<Map<String, dynamic>> tasks) async {
    await _initCompleter.future;
    _totalTasks = tasks.length;
    _completedTasks = 0;
    _allTasksCompleter = Completer();
// 添加任务到队列
    _taskQueue.addAll(tasks);
// 启动任务处理
    for (var port in _workerPorts) {
      if (_taskQueue.isNotEmpty) {
        _sendNextTask(port);
      }
    }
    await _allTasksCompleter.future;
  }

  void _sendNextTask(final SendPort workerPort) {
    if (_taskQueue.isEmpty) return;
    final task = _taskQueue.removeFirst();
    // 创建专用通信端口
    final taskPort = ReceivePort();
    taskPort.listen((final result) {
      if (result is ImageResult) {
        _resultController.add(result); // 转发结果
        _markTaskCompleted();
        // _handleOrderedResult(result);
      } else if (result is Map && result['type'] == 'error') {
        _errorController.add(result['message']);
        _markTaskCompleted();
      }
      taskPort.close(); // 关闭临时端口
    });
    // 仅传递可序列化数据 传递 SendPort 而非 StreamSink
    workerPort.send({...task, 'replyPort': taskPort.sendPort});
    // workerPort.send({
    //   ...task,
    //   'replyPort': _resultController.sink // 直接使用结果流发送端口
    // });
  }

  final Map<int, ImageResult> _resultBuffer = {};
  int _nextExpectedIndex = 0;

  /// 顺序交付结果
  void _handleOrderedResult(final ImageResult result) {
    // 缓存结果
    _resultBuffer[result.index] = result;
    // 顺序交付机制
    while (_resultBuffer.containsKey(_nextExpectedIndex)) {
      _resultController.add(_resultBuffer[_nextExpectedIndex]!);
      _resultBuffer.remove(_nextExpectedIndex);
      _nextExpectedIndex++;
      _markTaskCompleted();
    }
  }

  /// 标记任务完成（成功/失败都调用）
  void _markTaskCompleted() {
    _completedTasks++;
    final progress = _completedTasks / _totalTasks;
    _progressController.add(progress);
    if (_completedTasks == _totalTasks) {
      _allTasksCompleter.complete();
    }
// 继续分配新任务
    for (var port in _workerPorts) {
      if (_taskQueue.isNotEmpty) {
        _sendNextTask(port);
      }
    }
  }

  Future<void> awaitAllTasks() => _allTasksCompleter.future;

  /// 释放资源
  void dispose() {
    for (var isolate in _isolates) {
      isolate.kill(priority: Isolate.immediate);
    }
// 关闭接收端口
    for (var port in _receivePorts) {
      port.close();
    }
// 关闭流控制器
    if (!_resultController.isClosed) {
      _resultController.close();
    }
    if (!_progressController.isClosed) {
      _progressController.close();
    }
    if (!_errorController.isClosed) {
      _errorController.close();
    }
// 清理资源
    _isolates.clear();
    _workerPorts.clear();
    _receivePorts.clear();
    _taskQueue.clear();
    _initControllers();
  }

  /// Isolate工作线程主函数
  static void _workerMain(final SendPort mainPort) {
    final receivePort = ReceivePort();
    mainPort.send(receivePort.sendPort); // 向主线程发送通信端口
    receivePort.listen((final dynamic message) async {
      try {
        if (message is Map<String, dynamic>) {
          final replyPort = message['replyPort'] as SendPort;
          if (message['type'] == 'crop') {
            final result = await _processCropTask(message);
            replyPort.send(result);
          }
        }
      } catch (e, stack) {
        mainPort.send({
          'type': 'error',
          'index': message['index'],
          'message': 'Isolate error: ${e.toString()}',
          'stack': stack.toString()
        });
      }
    });
  }

  /// 处理裁剪任务
  static Future<ImageResult> _processCropTask(
      final Map<String, dynamic> task) async {
    final path = task['inputPath'] as String;
    final rectValues = task['rect'] as List<double>;
    final rect = Rect.fromLTRB(
      rectValues[0],
      rectValues[1],
      rectValues[2],
      rectValues[3],
    );
    final img.Image? image = await img.decodeImageFile(path);
    if (image == null) {
      throw Exception('Failed to load image');
    }
    final pixelRect = Rect.fromLTRB(
      rect.left * image.width,
      rect.top * image.height,
      rect.right * image.width,
      rect.bottom * image.height,
    );
    final cropped = img.copyCrop(
      image,
      x: pixelRect.left.toInt(),
      y: pixelRect.top.toInt(),
      width: pixelRect.width.toInt(),
      height: pixelRect.height.toInt(),
    );
    final Uint8List croppedBytes = img.encodePng(cropped);
    final normalizedRect = Rect.fromLTWH(
      pixelRect.left / image.width,
      pixelRect.top / image.height,
      pixelRect.width / image.width,
      pixelRect.height / image.height,
    );
    return ImageResult(croppedBytes, normalizedRect, '', task['index']);
  }
}
