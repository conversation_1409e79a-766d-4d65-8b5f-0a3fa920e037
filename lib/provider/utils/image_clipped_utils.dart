import 'dart:async';
import 'dart:collection';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:image/image.dart' as img;
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_ui/widgets/niimbot_toast/niimbot_loading_controller.dart';

import 'canvas_image_clipped_utils.dart';

final Logger _logger = Logger("ImageClippedUtils", on: kDebugMode);

class Semaphore {
  int _tokens;
  final _queue = Queue<Completer<void>>();

  Semaphore(this._tokens);

  Future<void> acquire() {
    if (_tokens > 0) {
      _tokens--;
      return Future.value();
    }
    final completer = Completer<void>();
    _queue.add(completer);
    return completer.future;
  }

  void release() {
    if (_queue.isNotEmpty) {
      final completer = _queue.removeFirst();
      completer.complete();
    } else {
      _tokens++;
    }
  }
}

class ImageClippedUtils {
  /// 返回图片和实际剪裁区域
  static Future<(Uint8List, Rect)> cropImage(
      final (String, List<double>) data) async {
    try {
      // 使用compute在后台线程中处理图像数据，避免阻塞UI
      final result = await _cropImageInBackground(data);
      return result;
    } catch (e) {
      _logger.log('Error cropping image: $e');
      rethrow;
    }
  }

  // 真正的裁剪逻辑在这里实现
  static Future<(Uint8List, Rect)> _cropImageInBackground(
      final (String, List<double>) data) async {
    final (imagePath, rectValues) = data;
    final Rect rect = Rect.fromLTRB(
      rectValues[0],
      rectValues[1],
      rectValues[2],
      rectValues[3],
    );

    final img.Image? originalImage = await img.decodeImageFile(imagePath);
    if (originalImage == null) {
      throw Exception('Failed to load image');
    }
    final Rect cropRect = Rect.fromLTRB(
      rect.left * originalImage.width,
      rect.top * originalImage.height,
      rect.right * originalImage.width,
      rect.bottom * originalImage.height,
    );
    final x = cropRect.left.toInt(),
        y = cropRect.top.toInt(),
        width = cropRect.width.toInt(),
        height = cropRect.height.toInt();

    final img.Image croppedImage = img.copyCrop(
      originalImage,
      x: x,
      y: y,
      width: width,
      height: height,
    );

    final Uint8List croppedBytes = img.encodePng(croppedImage);

    return (
      croppedBytes,
      Rect.fromLTWH(
        x / originalImage.width,
        y / originalImage.height,
        width / originalImage.width,
        height / originalImage.height,
      )
    );
  }

  static Future<List<(Uint8List, Rect)?>> batchCropImage(
      final (List<String>, Rect) data) async {
    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      final (paths, rect) = data;
      final int length = paths.length;

      const int maxConcurrent = 8; // 减少并发数量以避免资源竞争
      final Semaphore semaphore = Semaphore(maxConcurrent);

      final List<Future<(Uint8List, Rect)?>> futures = [];

      for (int i = 0; i < length; i++) {
        final String path = paths[i];
        final List<double> rectValues = [
          rect.left,
          rect.top,
          rect.right,
          rect.bottom,
        ];

        Future<(Uint8List, Rect)?> future() async {
          try {
            await semaphore.acquire();
            final result = await cropImage((path, rectValues));
            return result;
          } catch (e, stackTrace) {
            _logger.log('Error processing image at index $i: $e\n$stackTrace');
            return null;
          } finally {
            semaphore.release();
          }
        }

        futures.add(future());
      }

      // 直接使用 Future.wait 而不是中间封装
      final results = await Future.wait(futures);
      final endTime = DateTime.now().millisecondsSinceEpoch;
      _logger.log('cropImage ==> ${endTime - startTime}');
      return results;
    } catch (e, stackTrace) {
      _logger.log('Error in batch cropping image: $e\n$stackTrace');
      return [];
    }
  }

  static _updateLoading(final double progress) {
    NiimbotLoadingController().update(
      value: progress,
      text: NiimbotIntl.getIntlMessage('pc0397', '裁剪中：\$',
          param: ['${(progress * 100).ceil()}%']),
    );
  }

  static Future<(List<String>, Rect)> batchCropLoading(
      final List<String> paths,
      final Rect clippingRect,
      final Future<String?> Function(Uint8List data, {String? fileName})?
          saveImage) async {
    final Completer<(List<String>, Rect)> completer =
        Completer<(List<String>, Rect)>();
    final List<String> localPath =
        List.filled(paths.length, '', growable: false);
    Rect imgClippingRect = clippingRect;
    int progress = 0;
    bool completed = false;
    print('==========================${DateTime.now()}');
    NiimbotLoadingController().show(
      NiimbotIntl.getIntlMessage('pc0397', '裁剪中：\$', param: ['$progress%']),
      value: progress / 100,
      duration: Duration.zero,
      showMask: true,
    );
    List<ImageResult?> results =
        List.filled(paths.length, null, growable: false);
    int taskLength = (Platform.numberOfProcessors / 4).ceil();
    if (paths.length < taskLength) {
      taskLength = (paths.length / 2).ceil() - 1;
    }
    if (taskLength < 1) {
      taskLength = 1;
    }
    List<String> tempPathList = [];
    bool isError = false;
    await ImageClipper.init(taskLength);
    ImageClipper.cropImages(
      paths,
      clippingRect,
      onProgress: (final progress) {
        _updateLoading(progress);
      },
      onResult: (final ImageResult result) async {
        if (isError) return;
        results[result.index] = result;
        final local = results[result.index]!.bytes;
        final rect = results[result.index]!.normalizedRect;
        final path = await saveImage?.call(local);
        tempPathList.add(path ?? "");
        if (path != null) {
          localPath[result.index] = path;
          imgClippingRect = rect;
        }
        if (!completed && tempPathList.length == paths.length) {
          completed = true;
          _updateLoading(1);
          await Future.delayed(Duration(milliseconds: 300));
          completer.complete((localPath, imgClippingRect));
          print('==========================${DateTime.now()}');
        }
      },
      onError: (final error) {
        if (isError) return;
        _logger.log('Error in batch cropping image: $error');
        isError = true;
        ImageClipper.dispose();
        completer.completeError(error);
      },
    );
    return completer.future;
  }
}
