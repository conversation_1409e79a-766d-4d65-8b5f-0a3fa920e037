import 'dart:io';
import 'dart:ui' as ui;

import 'package:excel/excel.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:intl/intl.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:netal_plugin/utils/ratio.dart';
import 'package:niimbot_flutter_canvas/business/desktop/models/canvas_drag_model.dart';
import 'package:niimbot_flutter_canvas/business/desktop/utils/canvas_widget_manager_utils.dart';
import 'package:niimbot_flutter_canvas/extensions/base_element.dart';
import 'package:niimbot_flutter_canvas/extensions/material_item.dart';
import 'package:niimbot_flutter_canvas/extensions/rect.dart';
import 'package:niimbot_flutter_canvas/model/date_element_helper.dart';
import 'package:niimbot_flutter_canvas/model/element/qr_code_element.dart';
import 'package:niimbot_flutter_canvas/provider/models/bar_code_element_updater.dart';
import 'package:niimbot_flutter_canvas/provider/models/base_element_updater.dart';
import 'package:niimbot_flutter_canvas/provider/models/color_element_updater.dart';
import 'package:niimbot_flutter_canvas/provider/models/color_reverse_updater.dart';
import 'package:niimbot_flutter_canvas/provider/models/date_element_updater.dart';
import 'package:niimbot_flutter_canvas/provider/models/graph_element_updater.dart';
import 'package:niimbot_flutter_canvas/provider/models/image_element_updater.dart';
import 'package:niimbot_flutter_canvas/provider/models/index.dart';
import 'package:niimbot_flutter_canvas/provider/models/line_element_updater.dart';
import 'package:niimbot_flutter_canvas/provider/models/qr_code_element_updater.dart';
import 'package:niimbot_flutter_canvas/provider/models/serial_element_updater.dart';
import 'package:niimbot_flutter_canvas/provider/models/table_element_updater.dart';
import 'package:niimbot_flutter_canvas/provider/models/text_element_updater.dart';
import 'package:niimbot_flutter_canvas/provider/models/value_element_updater.dart';
import 'package:niimbot_http/business/apis/rest/models/material_item.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_template/models/elements/bar_code_element.dart';
import 'package:niimbot_template/models/elements/base_element.dart';
import 'package:niimbot_template/models/elements/color_element.dart';
import 'package:niimbot_template/models/elements/date_element.dart';
import 'package:niimbot_template/models/elements/element_enum.dart';
import 'package:niimbot_template/models/elements/graph_element.dart';
import 'package:niimbot_template/models/elements/image_element.dart';
import 'package:niimbot_template/models/elements/line_element.dart';
import 'package:niimbot_template/models/elements/material_element.dart';
import 'package:niimbot_template/models/elements/qr_code_element.dart';
import 'package:niimbot_template/models/elements/serial_element.dart';
import 'package:niimbot_template/models/elements/table_cell_element.dart';
import 'package:niimbot_template/models/elements/table_element.dart';
import 'package:niimbot_template/models/elements/text_element.dart';
import 'package:niimbot_template/models/elements/value_element.dart';
import 'package:niimbot_ui/widgets/niimbot_toast/niimbot_toast_controller.dart';

final Logger _logger = Logger("CanvasStoreUtils", on: kDebugMode);

class CanvasStoreUtils {
  /// 复制元素通过更新器
  /// [element] 元素
  /// [updater] 更新器
  static S copyByUpdater<T extends BaseElementUpdater, S extends BaseElement>(
    final S element,
    final T updater,
    final double rotate,
    final Offset center,
  ) {
    final offset = _buildOffset(element, updater, rotate, center);
    _logger.log('onOffsetChange => offset:$offset');
    if (element is BarCodeElement && updater is BarCodeElementUpdater) {
      return element.copyWith(
        x: offset?.dx,
        y: offset?.dy,
        width: updater.width,
        height: updater.height,
        rotate: updater.rotate,
        isLock: updater.isLock,
        isOpenMirror: updater.isOpenMirror,
        mirrorType: updater.mirrorType,
        hasVipRes: updater.hasVipRes,
        value: updater.value,
        codeType: updater.codeType,
        dataBind: updater.dataBind,
        textPosition: updater.textPosition,
        fontSize: updater.fontSize,
        textHeight: updater.textHeight,
        colorChannel: updater.colorChannel,
        elementColor: updater.elementColor,
      ) as S;
    }
    if (element is MaterialElement) {
      final update = _materialElementUpdate(offset, element, updater);
      if (update != null) {
        return update as S;
      }
    }
    if (element is ImageElement && updater is ImageElementUpdater) {
      return element.copyWith(
        x: offset?.dx,
        y: offset?.dy,
        width: updater.width,
        height: updater.height,
        rotate: updater.rotate,
        isLock: updater.isLock,
        isOpenMirror: updater.isOpenMirror,
        mirrorType: updater.mirrorType,
        hasVipRes: updater.hasVipRes,
        colorChannel: updater.colorChannel,
        elementColor: updater.elementColor,
        imageData: updater.imageData,
        localImageUrl: updater.localUrl,
        allowFreeZoom: updater.allowFreeZoom,
        imageProcessingValue: updater.imageProcessingValue,
        imageProcessingType: updater.imageProcessingType,
      ) as S;
    }
    if (element is GraphElement && updater is GraphElementUpdater) {
      return element.copyWith(
        x: offset?.dx,
        y: offset?.dy,
        width: updater.width,
        height: updater.height,
        rotate: updater.rotate,
        isLock: updater.isLock,
        isOpenMirror: updater.isOpenMirror,
        mirrorType: updater.mirrorType,
        hasVipRes: updater.hasVipRes,
        colorChannel: updater.colorChannel,
        elementColor: updater.elementColor,
        lineType: updater.lineType,
        cornerRadius: updater.cornerRadius,
        graphType: updater.graphType,
        lineWidth: updater.lineWidth,
      ) as S;
    }
    if (element is LineElement && updater is LineElementUpdater) {
      return element.copyWith(
        x: offset?.dx,
        y: offset?.dy,
        width: updater.width,
        height: updater.height,
        rotate: updater.rotate,
        isLock: updater.isLock,
        isOpenMirror: updater.isOpenMirror,
        mirrorType: updater.mirrorType,
        hasVipRes: updater.hasVipRes,
        colorChannel: updater.colorChannel,
        elementColor: updater.elementColor,
        lineType: updater.lineType,
      ) as S;
    }
    if (element is QRCodeElement) {
      final update = _qrCodeElementUpdate(offset, element, updater);
      if (update != null) {
        return update as S;
      }
    }
    if (element is DateElement && updater is DateElementUpdater) {
      return element.copyWith(
        x: offset?.dx,
        y: offset?.dy,
        width: updater.width,
        height: updater.height,
        rotate: updater.rotate,
        isLock: updater.isLock,
        isOpenMirror: updater.isOpenMirror,
        mirrorType: updater.mirrorType,
        hasVipRes: updater.hasVipRes,
        dateFormat: updater.dateFormat,
        timeFormat: updater.timeFormat,
        time: updater.time,
        dateIsRefresh: updater.dateIsRefresh,
        textAlignHorizontal: updater.horizontalAlign,
        textAlignVertical: updater.verticalAlign,
        fontFamily: updater.fontFamily,
        fontCode: updater.fontCode,
        fontSize: updater.fontSize,
        fontStyle: updater.fontStyle,
        letterSpacing: updater.letterSpacing,
        wordSpacing: updater.wordSpacing,
        lineSpacing: updater.lineSpacing,
        typesettingMode: updater.typesettingMode,
        lineBreakMode: updater.lineBreakMode,
        colorReverse: updater.colorReverse,
        colorChannel: updater.colorChannel,
        elementColor: updater.elementColor,
        typesettingParam: updater.typesettingParam,
        contentTitle: updater.contentTitle,
        timeUnit: updater.timeUnit,
        timeOffset: updater.timeOffset,
        associated: updater.associated,
        associateId: updater.associateId,
        validityPeriod: updater.validityPeriod,
        validityPeriodUnit: updater.validityPeriodUnit,
      ) as S;
    }
    if (element is SerialElement && updater is SerialElementUpdater) {
      return element.copyWith(
        x: offset?.dx,
        y: offset?.dy,
        width: updater.width,
        height: updater.height,
        rotate: updater.rotate,
        isLock: updater.isLock,
        isOpenMirror: updater.isOpenMirror,
        mirrorType: updater.mirrorType,
        hasVipRes: updater.hasVipRes,
        prefix: updater.prefix,
        suffix: updater.suffix,
        startNumber: updater.startNumber,
        fixLength: updater.fixLength,
        incrementValue: updater.incrementValue,
        textAlignHorizontal: updater.horizontalAlign,
        textAlignVertical: updater.verticalAlign,
        fontFamily: updater.fontFamily,
        fontCode: updater.fontCode,
        fontSize: updater.fontSize,
        fontStyle: updater.fontStyle,
        letterSpacing: updater.letterSpacing,
        wordSpacing: updater.wordSpacing,
        lineSpacing: updater.lineSpacing,
        typesettingMode: updater.typesettingMode,
        lineBreakMode: updater.lineBreakMode,
        colorReverse: updater.colorReverse,
        colorChannel: updater.colorChannel,
        elementColor: updater.elementColor,
        typesettingParam: updater.typesettingParam,
      ) as S;
    }
    if (element is TextElement) {
      final update = _textElementUpdate(offset, element, updater);
      if (update != null) {
        return update as S;
      }
    }
    if (element is TableElement) {
      final update = _tableElementUpdate(offset, element, updater);
      if (update != null) {
        return update as S;
      }
    }
    if (element is ValueElement) {
      final update = _valueElementUpdate(offset, element, updater);
      if (update != null) {
        return update as S;
      }
    }
    if (element is ColorElement) {
      final update = _colorElementUpdate(offset, element, updater);
      if (update != null) {
        return update as S;
      }
    }
    return element.copyWith(
      x: offset?.dx,
      y: offset?.dy,
      width: updater.width,
      height: updater.height,
      rotate: updater.rotate,
      isLock: updater.isLock,
      isOpenMirror: updater.isOpenMirror,
      mirrorType: updater.mirrorType,
      hasVipRes: updater.hasVipRes,
    ) as S;
  }

  static Future<BaseElement?> generateElementByExcel<T extends BaseElement>(
      final CanvasExcelDragSendDataModel val,
      {final Offset? offset}) async {
    String languageCode = NiimbotIntl.getCurrentLocale().languageCode;
    final isLangJa = languageCode == 'ja';
    return TextElement(
        fontFamily: !isLangJa ?  'Harmony' : 'ZT825',
        fontCode: !isLangJa ? 'ZT001' : 'ZT825',
        value: CellIndex.indexByColumnRow(columnIndex: val.column, rowIndex: 0)
            .cellId,
        dataBind: [val.hash, val.sheetName],
        x: offset?.dx ?? 0,
        y: offset?.dy ?? 0,
        boxStyle: NetalTextBoxStyle.autoWidth,
        isBinding: true) as T;
  }

  /// 生成元素通过标识 item图标用  imageList图片
  static Future<BaseElement?>
      generateElementByIdentifier<T extends BaseElement>(
    final BuildContext context,
    final String identifier, {
    final MaterialItem? item,
    final Uint8List? imageList,
    final bool isBorder = false,
    final Future<String?> Function(Uint8List data)? localFileSave,
    final Size? labelSize,
    final Size? canvasSize,
    final bool isPdf = false,
  }) async {
    String languageCode = NiimbotIntl.getCurrentLocale().languageCode;
    final isLangJa = languageCode == 'ja';
    if (identifier == 'text') {
      return TextElement(
          value: '',
          fontFamily: !isLangJa ?  'Harmony' : 'ZT825',
          fontCode: !isLangJa ? 'ZT001' : 'ZT825',
          boxStyle: NetalTextBoxStyle.autoWidth,
          textAlignHorizontal:
              Directionality.of(context) == ui.TextDirection.rtl
                  ? NetalTextAlign.end
                  : NetalTextAlign.start) as T;
    }
    if (identifier == 'date') {
      final defaultModel = DateElementHelper.getDateElementDefault();
      bool isAmTime =
          DateFormat('hh:MM:ss a').format(DateTime.now()).contains('AM');
      return DateElement(
          textAlignHorizontal:
              Directionality.of(context) == ui.TextDirection.rtl
                  ? NetalTextAlign.end
                  : NetalTextAlign.start,
          boxStyle: NetalTextBoxStyle.autoWidth,
          fontFamily: !isLangJa ?  'Harmony' : 'ZT825',
          fontCode: !isLangJa ? 'ZT001' : 'ZT825',
          time: DateTime.now().millisecondsSinceEpoch,
          hasVipRes: defaultModel.dateIsRefresh,
          dateIsRefresh: defaultModel.dateIsRefresh,
          contentTitle: defaultModel.contentTitle,
          dateFormat: defaultModel.dateFormatOpen == true
              ? DateElementHelper.getDateFormat()
              : null,
          timeFormat: defaultModel.timeFormatOpen == true
              ? DateElementHelper.getTimeFormat()
              : null,
          timeUnit: defaultModel.use12Hours == true
              ? (isAmTime ? ElementTimeUnit.morning : ElementTimeUnit.afternoon)
              : null) as T;
    }
    if (identifier == 'image') {
      final localUrl = await localFileSave?.call(imageList!);
      double screenScale = View.of(context).devicePixelRatio;
      return ImageElement(
          width: labelSize!.width.px2mm() / screenScale,
          height: labelSize.height.px2mm() / screenScale,
          imageData: '',
          allowFreeZoom: false,
          localImageUrl: localUrl ?? '',
          imageProcessingValue: isPdf ? [127] : [5],
          imageProcessingType: isPdf
              ? NetalImageRenderType.pdfMode
              : NetalImageRenderType.grayscale);
    }
    if (identifier == 'serial') {
      return SerialElement(
          fontFamily: !isLangJa ?  'Harmony' : 'ZT825',
          fontCode: !isLangJa ? 'ZT001' : 'ZT825',
          startNumber: '01',
          incrementValue: 1,
          boxStyle: NetalTextBoxStyle.autoWidth,
          textAlignHorizontal:
              Directionality.of(context) == ui.TextDirection.rtl
                  ? NetalTextAlign.end
                  : NetalTextAlign.start) as T;
    }
    if (identifier == 'qrcode') {
      return QRCodeElement() as T;
    }
    if (identifier == 'barcode') {
      return BarCodeElement() as T;
    }
    if (identifier == 'graph') {
      return GraphElement() as T;
    }
    if (identifier == 'line') {
      return LineElement() as T;
    }
    if (identifier == 'table') {
      return TableElement(
          cells: List.generate(
        6,
        (final i) => TableCellElement(
          fontFamily: !isLangJa ?  'Harmony' : 'ZT825',
          fontCode: !isLangJa ? 'ZT001' : 'ZT825',
          rowIndex: i ~/ 2,
          columnIndex: i % 2,
          textAlignHorizontal:
              Directionality.of(context) == ui.TextDirection.rtl
                  ? NetalTextAlign.end
                  : NetalTextAlign.start,
        ),
      )) as T;
    }
    if (identifier == 'material' || identifier == 'border') {
      if (item == null) return null;
      final mapJson = CanvasWidgetManager.sharedInstance()
          .getCanvasMaterialOssLocalMapUse();
      String? localUrl = mapJson[item.image];
      // 本地文件存在
      final file = localUrl != null ? File(localUrl) : null;
      bool fileExistsSync = file?.existsSync() ?? false;
      int height = 100;
      late int width;
      Uint8List bytes;
      try {
        bytes = fileExistsSync
            ? file!.readAsBytesSync()
            : (await NetworkAssetBundle(Uri.parse(item.image)).load(item.image))
                .buffer
                .asUint8List();
        if (!fileExistsSync) {
          Uint8List thumbBytes =
              (await NetworkAssetBundle(Uri.parse(item.thumbnail))
                      .load(item.thumbnail))
                  .buffer
                  .asUint8List();
          ui.Image image = await decodeImageFromList(thumbBytes);
          width = image.width;
          height = image.height;
        } else {
          ui.Image image = await decodeImageFromList(bytes);
          width = (height * (image.width / image.height)).toInt();
        }
      } catch (e) {
        NiimbotToastController()
            .showWarning(NiimbotIntl.getIntlMessage('login0131', '网络异常'));
        return null;
      }
      double screenScale = View.of(context).devicePixelRatio;
      if (canvasSize != null && isBorder) {
        if (width != canvasSize.width.mm2px()) {
          double scale = (canvasSize.width.mm2px()) / width;
          width = (width * scale * screenScale).toInt();
          height = (height * scale * screenScale).toInt();
        }
        if (height > canvasSize.height.mm2px()) {
          double scale = (canvasSize.height.mm2px()) / height;
          width = (width * scale * screenScale).toInt();
          height = (height * scale * screenScale).toInt();
        }
      }

      if (!fileExistsSync) localUrl = await localFileSave?.call(bytes);
      return MaterialElement(
        materialType:
            isBorder ? MaterialElementType.border : MaterialElementType.icon,
        materialId: item.id,
        width: width.px2mm() / screenScale,
        height: height.px2mm() / screenScale,
        imageData: '',
        imageUrl: item.image,
        localImageUrl: localUrl ?? '',
      ) as T;
    }
    return null;
  }

  /// 构建对齐更新器
  static BaseElementUpdater buildAlignUpdater(
      final AlignType type,
      final Rect rect,
      final BaseElement element,
      final double rotate,
      final Offset center) {
    var updater = const BaseElementUpdater();
    final eRect = element.getViewRect(rotate, center);
    switch (type) {
      case AlignType.VerticalTop:
        updater = updater.copyWith(dy: rect.top);
        break;
      case AlignType.VerticalBottom:
        updater = updater.copyWith(dy: rect.bottom - eRect.height);
        break;
      case AlignType.VerticalCenter:
        updater =
            updater.copyWith(dy: rect.top + (rect.height - eRect.height) / 2);
        break;
      case AlignType.HorizontalLeft:
        updater = updater.copyWith(dx: rect.left);
        break;
      case AlignType.HorizontalRight:
        updater = updater.copyWith(dx: rect.right - eRect.width);
        break;
      case AlignType.HorizontalCenter:
        updater =
            updater.copyWith(dx: rect.left + (rect.width - eRect.width) / 2);
        break;
    }
    return updater;
  }

  /// 计算表格大小
  static List<num> _buildTableSize(
      final num size, final List<num> origin, final num lineWidth) {
    /// 可以分配的大小
    final allocAbleSize = size - lineWidth * (origin.length + 1);

    /// 原始总大小
    final totalOrigin = origin.reduce((final a, final c) => a + c);

    /// 借用量
    num cwOffset = 0;
    return origin.map((final e) {
      final s = allocAbleSize * (e / totalOrigin) + cwOffset;
      if (s > 1) {
        return s;
      }
      cwOffset = s - 1;
      return 1;
    }).toList();
  }

  /// 更新文本元素
  static TextElement? _textElementUpdate(final Offset? offset,
      final TextElement element, final BaseElementUpdater updater) {
    if (updater is TextElementUpdater) {
      return element.copyWith(
        x: offset?.dx,
        y: offset?.dy,
        width: updater.width,
        height: updater.height,
        rotate: updater.rotate,
        isLock: updater.isLock,
        isOpenMirror: updater.isOpenMirror,
        mirrorType: updater.mirrorType,
        hasVipRes: updater.hasVipRes,
        dataBind: updater.dataBind,
        value: updater.value,
        textAlignHorizontal: updater.horizontalAlign,
        textAlignVertical: updater.verticalAlign,
        fontFamily: updater.fontFamily,
        fontCode: updater.fontCode,
        fontSize: updater.fontSize,
        fontStyle: updater.fontStyle,
        letterSpacing: updater.letterSpacing,
        wordSpacing: updater.wordSpacing,
        lineSpacing: updater.lineSpacing,
        typesettingMode: updater.typesettingMode,
        lineBreakMode: updater.lineBreakMode,
        colorReverse: updater.colorReverse,
        colorChannel: updater.colorChannel,
        elementColor: updater.elementColor,
        typesettingParam: updater.typesettingParam,
        boxStyle: updater.boxStyle,
      );
    }
    if (updater is ColorReverseUpdater) {
      return element.copyWith(
        x: offset?.dx,
        y: offset?.dy,
        width: updater.width,
        height: updater.height,
        rotate: updater.rotate,
        isLock: updater.isLock,
        isOpenMirror: updater.isOpenMirror,
        mirrorType: updater.mirrorType,
        hasVipRes: updater.hasVipRes,
        colorChannel: updater.colorChannel,
        elementColor: updater.elementColor,
        colorReverse: updater.colorReverse,
      );
    }
    return null;
  }

  /// 更新素材元素
  static MaterialElement? _materialElementUpdate(final Offset? offset,
      final MaterialElement element, final BaseElementUpdater updater) {
    if (updater is ImageElementUpdater) {
      return element.copyWith(
        x: offset?.dx,
        y: offset?.dy,
        width: updater.width,
        height: updater.height,
        rotate: updater.rotate,
        isLock: updater.isLock,
        isOpenMirror: updater.isOpenMirror,
        mirrorType: updater.mirrorType,
        hasVipRes: updater.hasVipRes,
        colorChannel: updater.colorChannel,
        elementColor: updater.elementColor,
        localImageUrl: updater.localUrl,
        imageData: updater.imageData,
        allowFreeZoom: updater.allowFreeZoom,
        imageProcessingValue: updater.imageProcessingValue,
        colorReverse: updater.colorReverse,
      );
    }
    if (updater is ColorReverseUpdater) {
      return element.copyWith(
        x: offset?.dx,
        y: offset?.dy,
        width: updater.width,
        height: updater.height,
        rotate: updater.rotate,
        isLock: updater.isLock,
        isOpenMirror: updater.isOpenMirror,
        mirrorType: updater.mirrorType,
        hasVipRes: updater.hasVipRes,
        colorChannel: updater.colorChannel,
        elementColor: updater.elementColor,
        colorReverse: updater.colorReverse,
      );
    }
    return null;
  }

  /// 更新二维码
  static CanvasQRCodeElement? _qrCodeElementUpdate(final Offset? offset,
      final QRCodeElement element, final BaseElementUpdater updater) {
    if (updater is QRCodeElementUpdater) {
      final qrcode = element is CanvasQRCodeElement
          ? element
          : CanvasQRCodeElement.fromQRCodeElement(element);
      return qrcode.copyWith(
        x: offset?.dx,
        y: offset?.dy,
        width: updater.width,
        height: updater.height,
        rotate: updater.rotate,
        isLock: updater.isLock,
        isOpenMirror: updater.isOpenMirror,
        mirrorType: updater.mirrorType,
        hasVipRes: updater.hasVipRes,
        value: updater.value,
        codeType: updater.codeType,
        dataBind: updater.dataBind,
        correctLevel: updater.correctLevel,
        colorChannel: updater.colorChannel,
        elementColor: updater.elementColor,
        qrCodeImageData: updater.qrCodeImageData,
      );
    }
    return null;
  }

  /// 更新值元素
  static ValueElement? _valueElementUpdate(final Offset? offset,
      final ValueElement element, final BaseElementUpdater updater) {
    if (updater is ValueElementUpdater) {
      return element.copyWith(
        x: offset?.dx,
        y: offset?.dy,
        width: updater.width,
        height: updater.height,
        rotate: updater.rotate,
        isLock: updater.isLock,
        isOpenMirror: updater.isOpenMirror,
        mirrorType: updater.mirrorType,
        hasVipRes: updater.hasVipRes,
        value: updater.value,
        colorChannel: updater.colorChannel,
        elementColor: updater.elementColor,
      );
    }
    return null;
  }

  /// 更新颜色元素
  static ColorElement? _colorElementUpdate(final Offset? offset,
      final ColorElement element, final BaseElementUpdater updater) {
    if (updater is ColorElementUpdater) {
      return element.copyWith(
        x: offset?.dx,
        y: offset?.dy,
        width: updater.width,
        height: updater.height,
        rotate: updater.rotate,
        isLock: updater.isLock,
        isOpenMirror: updater.isOpenMirror,
        mirrorType: updater.mirrorType,
        hasVipRes: updater.hasVipRes,
        colorChannel: updater.colorChannel,
        elementColor: updater.elementColor,
      );
    }
    return null;
  }

  /// 更新颜色元素
  static TableElement? _tableElementUpdate(final Offset? offset,
      final TableElement element, final BaseElementUpdater updater) {
    List<num>? columnWidth;
    List<num>? rowHeight;
    if (updater.width != null) {
      columnWidth = _buildTableSize(
          (updater.width)!, element.columnWidth, element.lineWidth);
    }
    if (updater.height != null) {
      rowHeight = _buildTableSize(
          (updater.height)!, element.rowHeight, element.lineWidth);
    }
    if (updater is TableElementUpdater) {
      return element.copyWith(
        x: offset?.dx,
        y: offset?.dy,
        columnWidth: columnWidth ?? updater.columnWidth,
        rowHeight: rowHeight ?? updater.rowHeight,
        rotate: updater.rotate,
        isLock: updater.isLock,
        isOpenMirror: updater.isOpenMirror,
        mirrorType: updater.mirrorType,
        hasVipRes: updater.hasVipRes,
        cells: updater.cells,
        combineCells: updater.combineCells,
        lineWidth: updater.lineWidth,
        contentColor: updater.contentColor,
        contentColorChannel: updater.contentColorChannel,
        lineColor: updater.lineColor,
        lineColorChannel: updater.lineColorChannel,
      );
    }
    return element.copyWith(
      x: offset?.dx,
      y: offset?.dy,
      columnWidth: columnWidth,
      rowHeight: rowHeight,
      rotate: updater.rotate,
      isLock: updater.isLock,
      isOpenMirror: updater.isOpenMirror,
      mirrorType: updater.mirrorType,
      hasVipRes: updater.hasVipRes,
    );
  }

  static double _calcTableSize(final List<num> val, final num lineWidth) {
    return val.reduce((final a, final c) => a + c) +
        lineWidth * (val.length + 1);
  }

  static Offset? _buildOffset(
    final BaseElement element,
    final BaseElementUpdater updater,
    final double rotate,
    final Offset center,
  ) {
    final keepCenter =
        updater.keepPosition?.value == KeepPosition.center; // 是否保持中心点

    final elementMove = updater.dx != null || updater.dy != null; // 元素移动
    final elementResize =
        (updater.width != null || updater.height != null); // 元素大小改变
    final tableResize = (updater is TableElementUpdater &&
        element is TableElement &&
        (updater.rowHeight != null || updater.columnWidth != null)); // 表格元素大小改变
    if (keepCenter && elementResize) {
      return Offset(
          (element.x - ((updater.width ?? element.width) - element.width) / 2)
              .digits(6)
              .toDouble(),
          (element.y -
                  ((updater.height ?? element.height) - element.height) / 2)
              .digits(6)
              .toDouble());
    }
    if (elementMove || elementResize || tableResize) {
      var rect = element.getViewRect(rotate, center);
      if (updater.dx != null) {
        rect = rect.translateTo(left: updater.dx!.toDouble());
      }
      if (updater.dy != null) {
        rect = rect.translateTo(top: updater.dy!.toDouble());
      }

      if (elementResize || tableResize) {
        /* 改变大小 或者 改变表格元素大小 */
        var size = Size((updater.width ?? element.width).toDouble(),
            (updater.height ?? element.height).toDouble());
        if (tableResize) {
          size = Size(
              _calcTableSize(updater.columnWidth ?? element.columnWidth,
                  element.lineWidth),
              _calcTableSize(
                  updater.rowHeight ?? element.rowHeight, element.lineWidth));
        }

        final afterRotate = (360 - rotate + element.rotate) % 360;

        if (afterRotate == 90) {
          rect = Rect.fromLTRB(rect.right - size.height, rect.top, rect.right,
              rect.top + size.width);
        } else if (afterRotate == 180) {
          final wDiff = (size.width - rect.width);
          final hDiff = (size.height - rect.height);
          rect = Rect.fromLTRB(
              rect.left - wDiff, rect.top - hDiff, rect.right, rect.bottom);
        } else if (afterRotate == 270) {
          rect = Rect.fromLTRB(rect.right - size.height,
              rect.bottom - size.width, rect.right, rect.bottom);
        } else {
          rect = rect.resize(size: size);
        }
      }
      var beforeRotateRect = rect;
      if (rotate != 0) {
        /// 相对画布旋转之前
        beforeRotateRect = rect.rotate(rotate, center);
      }
      var origin = beforeRotateRect;

      if (updater.rotate != null) {
        /// 更新元素旋转角度
        origin = beforeRotateRect.rotate(-updater.rotate!.toDouble());
      } else {
        if (element.rotate != 0) {
          /// 元素自身具有角度
          origin = beforeRotateRect.rotate(-element.rotate.toDouble());
        }
      }
      return Offset((updater.x ?? origin.left).toDouble(),
          (updater.y ?? origin.top).toDouble());
    }
    return null;
  }
}
