import 'package:collection/collection.dart';
import 'package:netal_plugin/models/copy_wrapper.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:niimbot_template/models/elements/bar_code_element.dart';
import 'package:niimbot_template/models/elements/base_element.dart';
import 'package:niimbot_template/models/elements/color_element.dart';
import 'package:niimbot_template/models/elements/graph_element.dart';
import 'package:niimbot_template/models/elements/image_element.dart';
import 'package:niimbot_template/models/elements/line_element.dart';
import 'package:niimbot_template/models/elements/material_element.dart';
import 'package:niimbot_template/models/elements/text_element.dart';

import 'package:niimbot_flutter_canvas/model/element/element_utils.dart';
import 'package:niimbot_flutter_canvas/model/element/style_attr/barcode_style_attr.dart';
import 'package:niimbot_flutter_canvas/model/element/style_attr/color_reverse_style_attr.dart';
import 'package:niimbot_flutter_canvas/model/element/style_attr/color_style_attr.dart';
import 'package:niimbot_flutter_canvas/model/element/style_attr/graph_style_attr.dart';
import 'package:niimbot_flutter_canvas/model/element/style_attr/image_style_attr.dart';
import 'package:niimbot_flutter_canvas/model/element/style_attr/line_style_attr.dart';
import 'package:niimbot_flutter_canvas/model/element/style_attr/material_style_attr.dart';
import 'package:niimbot_flutter_canvas/model/element/style_attr/text_style_attr.dart';
class StyleAttributeUtils {
  static TextStyleAttribute _buildTextStyleAttr(
      final TextStyleAttribute attr, final TextElement element) {
    CopyWrapper<List<NetalTextFontStyle>?>? fontStyle;
    if (attr.fontStyle == element.fontStyle) {
      fontStyle = null;
    } else {
      final attrFSSet = attr.fontStyle?.toSet() ?? {};
      final elemFSSet = element.fontStyle.toSet();
      final fontStyleIntersection = attrFSSet.intersection(elemFSSet).toList();
      if (fontStyleIntersection.isEmpty) {
        fontStyle = const CopyWrapper.value(null);
      } else {
        fontStyle = CopyWrapper.value(fontStyleIntersection);
      }
    }
    return attr.copyWith(
      colorChannel: attr.colorChannel == element.colorChannel
          ? null
          : const CopyWrapper.value(null),
      elementColor: attr.elementColor == element.elementColor
          ? null
          : const CopyWrapper.value(null),
      colorReverse: attr.colorReverse == element.colorReverse ? null : false,
      fontFamily: attr.fontFamily == element.fontFamily
          ? null
          : const CopyWrapper.value(null),
      fontSize: attr.fontSize > element.fontSize ? element.fontSize : null,
      textAlign: attr.textAlign ==
              (element.typesettingMode == NetalTypesettingMode.vertical
                  ? element.textAlignVertical
                  : element.textAlignHorizontal)
          ? null
          : const CopyWrapper.value(null),
      fontStyle: fontStyle,
      letterSpacing: attr.letterSpacing > element.letterSpacing
          ? element.letterSpacing
          : null,
      lineSpacing:
          attr.lineSpacing > element.lineSpacing ? element.lineSpacing : null,
      typesettingMode: attr.typesettingMode == element.typesettingMode
          ? null
          : const CopyWrapper.value(null),
      typesettingParam: (attr.typesettingParam.isNotEmpty) && element.typesettingParam.isNotEmpty &&
              attr.typesettingParam[1] > element.typesettingParam[1]
          ? element.typesettingParam
          : null,
      lineBreakMode: attr.lineBreakMode == element.lineBreakMode
          ? null
          : const CopyWrapper.value(null),
    );
  }

  static BarCodeStyleAttribute _buildBarCodeStyleAttr(
      final BarCodeStyleAttribute attr, final BarCodeElement element) {
    ///此处判断一维码里面是否有不可选 textPosition
    NetalBarcodeType? type = attr.type;
    if ([
      NetalBarcodeType.UPC_A,
      NetalBarcodeType.UPC_E,
      NetalBarcodeType.EAN8,
      NetalBarcodeType.EAN13
    ].contains(type)) {
      type = NetalBarcodeType.UPC_A;
    }
    return attr.copyWith(
      colorChannel: attr.colorChannel == element.colorChannel
          ? null
          : const CopyWrapper.value(null),
      elementColor: attr.elementColor == element.elementColor
          ? null
          : const CopyWrapper.value(null),
      fontSize: attr.fontSize > element.fontSize ? element.fontSize : null,
      textPosition: attr.textPosition == element.textPosition
          ? null
          : const CopyWrapper.value(null),
      type: CopyWrapper.value(type),
    );
  }

  static GraphStyleAttribute _buildGraphStyleAttr(
      final GraphStyleAttribute attr, final GraphElement element) {
    return attr.copyWith(
      colorChannel: attr.colorChannel == element.colorChannel
          ? null
          : const CopyWrapper.value(null),
      elementColor: attr.elementColor == element.elementColor
          ? null
          : const CopyWrapper.value(null),
      lineType: attr.lineType == element.lineType
          ? null
          : const CopyWrapper.value(null),
      lineWidth: attr.lineWidth > element.height ? element.height : null,
      graphType: attr.graphType == element.graphType
          ? null
          : const CopyWrapper.value(null),
    );
  }

  static LineStyleAttribute _buildLineStyleAttr(
      final LineStyleAttribute attr, final LineElement element) {
    return LineStyleAttribute(
            colorChannel: attr.colorChannel,
            lineWidth: attr.lineWidth,
            lineType: attr.lineType,
            elementColor: attr.elementColor)
        .copyWith(
      colorChannel: attr.colorChannel == element.colorChannel
          ? null
          : const CopyWrapper.value(null),
      elementColor: attr.elementColor == element.elementColor
          ? null
          : const CopyWrapper.value(null),
      lineType: attr.lineType == element.lineType
          ? null
          : const CopyWrapper.value(null),
      lineWidth: attr.lineWidth > element.height ? element.height : null,
    );
  }

  static MaterialStyleAttribute _buildMaterialStyleAttr(
      final MaterialStyleAttribute attr, final MaterialElement element) {
    return attr.copyWith(
      colorChannel: attr.colorChannel == element.colorChannel
          ? null
          : const CopyWrapper.value(null),
      elementColor: attr.elementColor == element.elementColor
          ? null
          : const CopyWrapper.value(null),
      colorReverse: attr.colorReverse == element.colorReverse ? null : false,
      processingValue: attr.processingValue[0] > element.imageProcessingValue[0]
          ? element.imageProcessingValue
          : null,
      allowFreeZoom: attr.allowFreeZoom == element.allowFreeZoom ? null : true,
    );
  }

  static ImageStyleAttribute _buildImageStyleAttr(
      final ImageStyleAttribute attr, final ImageElement element) {
    return attr.copyWith(
      colorChannel: attr.colorChannel == element.colorChannel
          ? null
          : const CopyWrapper.value(null),
      elementColor: attr.elementColor == element.elementColor
          ? null
          : const CopyWrapper.value(null),
      processingValue: attr.processingValue[0] > element.imageProcessingValue[0]
          ? element.imageProcessingValue
          : null,
      allowFreeZoom: attr.allowFreeZoom == element.allowFreeZoom ? null : false,
      renderType: attr.renderType == element.imageProcessingType
          ? null
          : const CopyWrapper.value(null),
    );
  }

  static ColorReverseStyleAttribute _buildColorReverseStyleAttr(
      final ColorReverseStyleAttribute attr, final dynamic element) {
    return ColorReverseStyleAttribute(
      colorChannel: attr.colorChannel,
      colorReverse: attr.colorReverse,
      elementColor: attr.elementColor,
    ).copyWith(
      colorChannel: attr.colorChannel == element.colorChannel
          ? null
          : const CopyWrapper.value(null),
      colorReverse: attr.colorReverse == element.colorReverse ? null : false,
      elementColor: attr.elementColor == element.elementColor
          ? null
          : const CopyWrapper.value(null),
    );
  }

  static _buildColorAttrChange(final ColorStyleAttribute attr, final ColorElement element) {
    return ColorStyleAttribute(
      colorChannel: attr.colorChannel,
      elementColor: attr.elementColor,
    ).copyWith(
      colorChannel: attr.colorChannel == element.colorChannel
          ? null
          : const CopyWrapper.value(null),
      elementColor: attr.elementColor == element.elementColor
          ? null
          : const CopyWrapper.value(null),
    );
  }

  static ColorStyleAttribute? buildSelectedStyleAttribute(
      final List<BaseElement> list) {
    var attr = ElementUtils.elementToStyleAttribute(list.firstOrNull);

    if (attr != null) {
      for (var i = 1; i < list.length; i++) {
        final element = list[i];
        if (attr is TextStyleAttribute) {
          /* 文本元素 */
          if (element is TextElement) {
            /* 且该当前元素位 是 文本/时间/序列号 */
            attr = _buildTextStyleAttr(attr, element);
          } else if (element is MaterialElement) {
            /* 素材元素 */
            attr = _buildColorReverseStyleAttr(attr, element);
          } else if (element is ColorElement) {
            /* 颜色元素 */
            attr = _buildColorAttrChange(attr, element);
          } else {
            attr = null;
          }
        } else if (attr is BarCodeStyleAttribute) {
          /* 一维码元素 */
          if (element is BarCodeElement) {
            attr = _buildBarCodeStyleAttr(attr, element);
          } else if (element is ColorElement) {
            /* 颜色元素 */
            attr = _buildColorAttrChange(attr, element);
          } else {
            attr = null;
          }
        } else if (attr is GraphStyleAttribute) {
          /* 图形元素 */
          if (element is GraphElement) {
            attr = _buildGraphStyleAttr(attr, element);
          } else if (element is LineElement) {
            attr = _buildLineStyleAttr(attr, element);
          } else if (element is ColorElement) {
            /* 颜色元素 */
            attr = _buildColorAttrChange(attr, element);
          } else {
            attr = null;
          }
        } else if (attr is LineStyleAttribute) {
          /* 线条元素 */
          if (element is LineElement) {
            attr = _buildLineStyleAttr(attr, element);
          } else if (element is ColorElement) {
            /* 颜色元素 */
            attr = _buildColorAttrChange(attr, element);
          } else {
            attr = null;
          }
        } else if (attr is MaterialStyleAttribute) {
          /* 图片元素 */
          if (element is MaterialElement) {
            attr = _buildMaterialStyleAttr(attr, element);
          } else if (element is ImageElement) {
            attr = _buildImageStyleAttr(attr, element);
          } else if (element is ColorElement) {
            /* 颜色元素 */
            // attr = attr.copyWith(
            //   colorChannel: attr.colorChannel == element.colorChannel
            //       ? null
            //       : const CopyWrapper.value(null),
            // );
            attr = _buildColorAttrChange(attr, element);
          } else {
            attr = null;
          }
        } else if (attr is ImageStyleAttribute) {
          /* 图片元素 但不是素材 */
          if (element is MaterialElement) {
            attr = _buildMaterialStyleAttr(
                MaterialStyleAttribute(
                  colorChannel: attr.colorChannel,
                  processingValue: attr.processingValue,
                  allowFreeZoom: attr.allowFreeZoom,
                  colorReverse: attr.colorReverse,
                  elementColor: attr.elementColor,
                ),
                element);
          } else if (element is ImageElement) {
            attr = _buildImageStyleAttr(attr, element);
          } else if (element is ColorElement) {
            /* 颜色元素 */
            attr = _buildColorAttrChange(attr, element);
          } else {
            attr = null;
          }
        }
        // if (type == ElementSelectType.table) {
        //   /* 表格元素 */
        //   if (selectedElement[i].type == NetalElementType.table) {
        //   } else {
        //     type = null;
        //   }
        // }
        else if (attr is ColorReverseStyleAttribute) {
          /* 反白元素 */
          if (element is TextElement || element is MaterialElement) {
            attr = _buildColorReverseStyleAttr(attr, element);
          } else if (element is ColorElement) {
            attr = _buildColorAttrChange(attr, element);
          } else {
            attr = null;
          }
        } else if (attr is ColorStyleAttribute) {
          /* 文本元素 */
          if (element is ColorElement) {
            attr = attr.copyWith(
              colorChannel: attr.colorChannel == element.colorChannel
                  ? null
                  : const CopyWrapper.value(null),
              elementColor: attr.elementColor == element.elementColor
                  ? null
                  : const CopyWrapper.value(null),
            );
          } else {
            attr = null;
          }
        }
      }
    }
    return attr;
  }
}
