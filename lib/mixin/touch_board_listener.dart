import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

///基本的事件处理
mixin TouchBoardListenerMixin<T extends StatefulWidget> on State<T> {
  bool get enableKeyListener;

  @override
  void initState() {
    super.initState();
    HardwareKeyboard.instance.addHandler(_handleKey);
  }

  @override
  void dispose() {
    HardwareKeyboard.instance.removeHandler(_handleKey);
    super.dispose();
  }

  void handleKeyUp(final KeyUpEvent event) {}

  void handleKeyDown(final KeyDownEvent event) {}

  bool _handleKey(final KeyEvent event) {
    if (!enableKeyListener) return false;
    if (event is KeyDownEvent) handleKeyDown(event);
    if (event is KeyUpEvent) handleKeyUp(event);
    return false;
  }
}
