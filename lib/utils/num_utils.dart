import 'package:decimal/decimal.dart';

class NumberUtils {
  ///获取数组[list]中的最接近[val]的值
  static getCloseValue(final List<num> list, final num val) {
    var index = 0; //保存最接近数值在数组中的索引
    num minDiff = double.maxFinite; //最小差值
    for (var i = 0; i < list.length; i++) {
      if (list[i] == val) {
        /* 一样大则返回该值 */
        return val;
      }
      final diffVal =
          (Decimal.parse(list[i].toString()) - Decimal.parse(val.toString()))
              .abs()
              .toDouble();
      if (diffVal < minDiff) {
        minDiff = diffVal;
        index = i;
      }
    }
    return list[index];
  }
  /// 保留一位小数
  static double roundDownToOneDecimal(final double value) {
    return (value * 10).floor() / 10;
  }
}
