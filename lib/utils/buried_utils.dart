import 'package:flutter/foundation.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:niimbot_analytics/niimbot_analytics.dart';

/// 埋点工具类
Logger _logger = Logger("BuriedUtils", on: kDebugMode);

class BuriedUtils {
  BuriedUtils._();

  static BuriedUtils? _instance;

  factory BuriedUtils() {
    _instance ??= BuriedUtils._();
    return _instance!;
  }

  /// 埋点
  /// [eventName] 事件编码
  /// [posCode] 点位编码
  Future<void> track(final String eventName, final String posCode,
      {final Map<String, dynamic>? ext}) async {
    try {
      await NiimbotAnalytics().track(eventName, posCode, ext: ext);
    } catch (err) {
      _logger.log('埋点失败：$err');
    }
  }
}
