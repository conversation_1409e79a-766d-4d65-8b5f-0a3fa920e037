import 'package:flutter/material.dart';

class StringUtils {
  static bool isEmpty(final String s) => s.trim().isEmpty;

  static bool isNotEmpty(final String s) => !isEmpty(s);

  static bool isEmail(final String value) {
    if (isEmpty(value)) return false;
    return RegExp(
            r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+")
        .hasMatch(value);
  }

  // Measures text using an off-screen canvas. It's not fast, but not overly slow either. Use with (mild) caution :)
  static Size measure(final String text, final TextStyle style,
      {final int maxLines = 1,
      final TextDirection direction = TextDirection.ltr,
      final double? maxWidth}) {
    final TextPainter textPainter = TextPainter(
        text: TextSpan(text: text, style: style),
        maxLines: maxLines,
        textDirection: direction)
      ..layout(minWidth: 0, maxWidth: maxWidth ?? double.infinity);
    return textPainter.size;
  }

  // Measures longest text item in a list of Strings. Useful for things like Dropdown Menu, where you just want to take up as much space as the content requires.
  static double measureLongest(final List<String> items, final TextStyle style,
      [final int? maxItems]) {
    double l = 0;
    if (maxItems != null && maxItems < items.length) {
      items.length = maxItems;
    }
    for (var item in items) {
      double m = measure(item, style).width;
      if (m > l) l = m;
    }
    return l;
  }

  /// Gracefully handles null values, and skips the suffix when null
  static String safeGet(final String value, [final String? suffix]) {
    return (value ?? "") + (!isEmpty(value) ? suffix ?? "" : "");
  }

  static String pluralize(final String s, final int length) {
    if (length == 1) return s;
    return "${s}s";
  }

  static String titleCaseSingle(final String s) =>
      '${s[0].toUpperCase()}${s.substring(1)}';

  static String titleCase(final String s) =>
      s.split(" ").map(titleCaseSingle).join(" ");

  static String defaultOnEmpty(final String value, final String fallback) =>
      isEmpty(value) ? fallback : value;

  static int perLineWords(final String text, final double width, final double fontSize) {
    String cpText = text.substring(0, 1);
    final TextPainter textPainter = TextPainter(
        text: TextSpan(text: cpText, style: TextStyle(fontSize: fontSize)),
        textDirection: TextDirection.ltr)
      ..layout();
    Size textPSize = textPainter.size;
    int wordSizePerLine = (width / (textPSize.width)).truncate();
    return wordSizePerLine;
  }

  static double wordsHeight(final String text, final double fontSize) {
    String cpText = text.substring(0, 1);
    final TextPainter textPainter = TextPainter(
        text: TextSpan(text: cpText, style: TextStyle(fontSize: fontSize)),
        textDirection: TextDirection.ltr)
      ..layout();
    Size textPSize = textPainter.size;
    return textPSize.height;
  }

  static String addCharAtPosition(final String s, final String char, final int position,
      {final bool repeat = false}) {
    if (!repeat) {
      if (s.length < position) {
        return s;
      }
      var before = s.substring(0, position);
      var after = s.substring(position, s.length);
      return before + char + after;
    } else {
      if (position == 0) {
        return s;
      }
      var buffer = StringBuffer();
      for (var i = 0; i < s.length; i++) {
        if (i != 0 && i % position == 0) {
          buffer.write(char);
        }
        buffer.write(String.fromCharCode(s.runes.elementAt(i)));
      }
      return buffer.toString();
    }
  }

  /// 处理换行符
  static String? covertNewLineChar(final String? str) {
    return str?.replaceAll('\r\n', '\n').replaceAll('\r', '\n');
  }

  /// 处理是否按单词换行
  static String? toCharacterBreak(final String? str, { final bool wordChar = true }) {
    if (str == null || str.isEmpty) {
      return str;
    }
    String breakWord = str;
    if (wordChar) {
      breakWord = '';
      for (final element in str.runes) {
        breakWord += String.fromCharCode(element);
        breakWord += '\u200B';
      }
    } else {
      breakWord = breakWord.replaceAll('\u200B', '');
    }
    return breakWord;
  }
}
