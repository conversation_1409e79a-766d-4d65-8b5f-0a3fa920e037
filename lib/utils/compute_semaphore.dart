import 'dart:async';
import 'dart:collection';
import 'dart:io';
import 'dart:math';

import 'package:async/src/restartable_timer.dart';
import 'package:flutter/foundation.dart';

/// 并发计算执行器（限制最大同时执行数）
class ConcurrentCompute<M, R> {
  static final int _maxConcurrent =
  max(1, (Platform.numberOfProcessors / 4).ceil()); // 最大并发数

  final Queue<_ComputeTask<M, R>> _queue = Queue(); // 任务队列
  int _runningCount = 0; // 当前运行中的任务数

  /// 提交计算任务并返回单个任务结果
  Future<R> run(final ComputeCallback<M, R> callback, final M message) {
    final completer = Completer<R>();

    _queue.add(_ComputeTask<M, R>(
      completer: completer,
      function: callback,
      argument: message,
    ));

    // 确保任务加入队列后立即触发执行
    Future.microtask(_unPark);

    return completer.future;
  }

  /// 触发任务执行
  void _unPark() {
    if (_runningCount >= _maxConcurrent || _queue.isEmpty) {
      return;
    }
    final task = _queue.removeFirst();
    _runningCount++;
    try {
      // 使用compute执行隔离任务
      compute(task.function, task.argument).then((final result) {
        task.completer.complete(result);
      }).catchError((final e, final stackTrace) {
        task.completer.completeError(e, stackTrace);
      }).whenComplete(() {
        if (_runningCount > 0) _runningCount--;
        // 当前任务完成后立即尝试启动下一个
        _unPark();
      });
    } catch (e, stackTrace) {
      // 捕获可能的同步错误（如队列操作异常）
      debugPrint('ConcurrentCompute exception: $e\n$stackTrace');
      task.completer.completeError(e, stackTrace);
      if (_runningCount > 0) _runningCount--;
      // 递归继续处理
      _unPark();
    }
  }

  RestartableTimer? restartableTimer;

  /// 等待正在执行的任务完成
  void queueClear() {
    _queue.clear();
  }
}

/// 内部任务结构
class _ComputeTask<M, R> {
  final Completer<R> completer;
  final ComputeCallback<M, R> function;
  final M argument;

  _ComputeTask({
    required this.completer,
    required this.function,
    required this.argument,
  });
}
