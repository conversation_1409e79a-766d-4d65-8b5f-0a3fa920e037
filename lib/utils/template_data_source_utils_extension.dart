import 'dart:io';

import 'package:dio/dio.dart';
import 'package:niimbot_excel/niimbot_excel_utils.dart';
import 'package:niimbot_template/models/template/template_data_source.dart';
import 'package:niimbot_template/models/template/template_data_source_modify.dart';
import 'package:niimbot_template/models/template/template_enum.dart';
import 'package:niimbot_template/utils/template_data_source_utils.dart';

class TemplateDataSourceUtilsExtension {
  /// 获取元素是否使用标题
  static bool getUseTitle(
      {required final String eId, final TemplateDataSourceModifies? modify}) {
    if (modify != null) {
      TemplateDataSourceModify? globalModify = modify[eId]?['0'];
      TemplateDataSourceModify? rowModify = modify[eId]?['0'];
      bool useTitle = rowModify?.useTitle ?? globalModify?.useTitle ?? false;
      return useTitle;
    }
    return false;
  }

  /// 构建本地文件数据源路径
  /// [dataSource] 数据源
  static Future<String> _buildFileLocalDataSourcePath(
    final TemplateDataSource dataSource,
  ) async {
    final path =
        await TemplateDataSourceUtils.buildLocalDataSourcePath(dataSource.hash);
    final localFileExists = await File(path).exists();
    if (!localFileExists) {
      /* 如果本地文件不存在 */
      await Dio().download(dataSource.uri, path);
    }
    return path;
  }

  /// 获取数据源中的指定区域数据
  /// [dataSource] 数据源
  /// [tableName] 表名
  /// [range] 数据范围 例如：A4:F8 表示读取从第 1 列到 第 6列数据，从第 4 行到第 8 行
  static Future<List<List<String>>> getDataSourceRange(
    final TemplateDataSource dataSource,
    final String tableName,
    final String range,
  ) async {
    if (dataSource.type == TemplateDataSourceType.excel) {
      /* 解析Excel类型数据 */
      var path = await _buildFileLocalDataSourcePath(dataSource);
      return NiimbotExcelUtils.getSheetRange(path, tableName, range);
    }
    return [];
  }


  /// 预处理数据源，没有的话下载
  /// [dataSource] 数据源
  static Future<String> processDataSource(final TemplateDataSource dataSource) async {
    var path = await _buildFileLocalDataSourcePath(dataSource);
    return path;
  }
}
