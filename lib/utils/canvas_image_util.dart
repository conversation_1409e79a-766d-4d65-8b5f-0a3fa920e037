import 'dart:async';
import 'dart:io';
import 'dart:ui' as ui;

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image/image.dart' as imgPlus;
import 'package:niimbot_flutter_canvas/utils/theme_color.dart';
import 'package:niimbot_flutter_canvas/widgets/components/svg_icon.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_ui/widgets/niimbot_toast/niimbot_toast_controller.dart';

class CanvasImageUtils {
  static Future<Uint8List?> rotateImageFile(
      final String filePath, final num angle,
      {final bool writeFile = false}) async {
    final imgPlus.Command cmd = imgPlus.Command()
      ..decodeImageFile(filePath)
      ..copyRotate(angle: angle);
    if (writeFile) {
      cmd.writeToFile(filePath);
    }
    imgPlus.Image? png = await cmd.getImage();
    return png?.toUint8List();
  }

  static Container errorHolder({
    final double width = 50.0,
    final double height = 50.0,
    final ui.Color color = ThemeColor.COLOR_F2F2F2,
  }) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
          color: color,
          borderRadius: const BorderRadius.all(Radius.circular(10))),
      child: const SvgIcon(
        'assets/holder/niimbot_placeholder.svg',
        useDefaultColor: false,
        fit: BoxFit.fill,
      ),
    );
  }

  static Widget imageWidget(
    final double width,
    final double height, {
    final String? url,
    final String? localPath,
    final BoxFit fit = BoxFit.contain,
  }) {
    if (localPath != null && localPath.isNotEmpty) {
      final localImage = File(localPath);
      if (localImage.existsSync()) {
        return Image.file(
          localImage,
          width: width,
          height: height,
          fit: fit,
          errorBuilder: (final _, final __, final ___) =>
              imageWidget(width, height, url: url, fit: fit),
        );
      }
    }
    if (url?.isNotEmpty ?? false) {
      return CachedNetworkImage(
        imageUrl: url!,
        imageBuilder:
            (final BuildContext context, final ImageProvider imageProvider) {
          return Image(
            fit: fit,
            image: imageProvider,
            width: width,
            height: height,
          );
        },
        errorWidget: (final _, final __, final ___) =>
            errorHolder(width: width, height: height),
      );
    }
    return errorHolder(width: width, height: height);
  }

  static Future<ui.Image> decodeImage(final Uint8List data) {
    Completer<ui.Image> completer = Completer<ui.Image>();
    callback(final ui.Image result) {
      completer.complete(result);
    }

    ui.decodeImageFromList(data, callback);
    return completer.future;
  }

  // 上传图片前置处理及校验
  static Future<Uint8List?> preProcessImage(final Uint8List data,
      {final bool showToast = true, final double? targetRatio}) async {
    imgPlus.Image? thumbnail;
    try {
      if ((data.lengthInBytes / (1024 * 1024)) > 5) {
        if (showToast) {
          NiimbotToastController().showWarning(
              NiimbotIntl.getIntlMessage('pc0076', '请上传5M以内的图片进行识别'));
        }
        throw NiimbotIntl.getIntlMessage('pc0076', '请上传5M以内的图片进行识别');
        // return null;
      }
      thumbnail = imgPlus.decodeImage(data);
      // 将图片编码为 JPG 格式
      if (thumbnail == null) return null;
      if (thumbnail.width >= 5000 || thumbnail.height >= 5000) {
        if (showToast) {
          NiimbotToastController().showWarning(
              NiimbotIntl.getIntlMessage('pc0075', '上传失败，建议上传宽高5000像素以内的图片'));
        }
        throw NiimbotIntl.getIntlMessage('pc0075', '上传失败，建议上传宽高5000像素以内的图片');
        // return null;
      }
    } catch (err) {
      rethrow;
      // return null;
    }
    ui.Image image;
    if (targetRatio != null) {
      final resizeImg = resizeWithContain(
          original: thumbnail, targetRatio: targetRatio);
      final resizeBytes = imgPlus.encodePng(resizeImg);
      image = await decodeImageFromList(resizeBytes);
    } else {
      image = await decodeImageFromList(data);
    }
    Uint8List pngBytes = await image
        .toByteData(format: ui.ImageByteFormat.png)
        .then((final byteData) => byteData!.buffer.asUint8List());

    return await _compressImage(pngBytes);
  }

  /// 压缩图片
  static Future<Uint8List?> _compressImage(final Uint8List data) async {
    try {
      imgPlus.Image? thumbnail = imgPlus.decodeImage(data);
      // 将图片编码为 JPG 格式
      if (thumbnail == null) return null;
      final dataBytes = data.lengthInBytes;
      if (1 * 1024 * 1024 < dataBytes && thumbnail.getPixel(0, 0).a != 0) {
        double quality = (1 * 1024 * 1024) / data.lengthInBytes;
        if (quality > 0 && quality < 100) {
          final jpgThumbnail = imgPlus.decodeImage(data);
          if (jpgThumbnail == null) return null;
          return imgPlus.encodeJpg(
            jpgThumbnail,
            quality: quality.toInt(),
          );
        }
      } else {
        return data;
      }
    } catch (e) {
      return null;
    }
    return data;
  }

  /// 非等比放大但保持内容完整（类似BoxFit.contain效果）
  static imgPlus.Image resizeWithContain({
    required final imgPlus.Image original,
    required final double targetRatio,
    // required final int targetWidth,
    // required final int targetHeight,
    final Color? backgroundColor, // 白色背景
    final imgPlus.Interpolation interpolation = imgPlus.Interpolation.cubic,
  }) {
    // 计算原始和目标宽高比
    final originalRatio = original.width / original.height;
    // final targetRatio = targetWidth / targetHeight;

    // 计算等比放大后的实际尺寸
    int actualWidth, actualHeight;
    // 目标宽高
    int targetWidth, targetHeight;
    if (targetRatio > 1) {
      targetHeight = original.height;
      targetWidth = (targetHeight * targetRatio).toInt();
    } else {
      targetWidth = original.width;
      targetHeight = (targetWidth / targetRatio).toInt();
    }

    if (originalRatio > targetRatio) {
      // 以宽度为基准
      actualWidth = targetWidth;
      actualHeight = (targetWidth / originalRatio).round();
    } else {
      // 以高度为基准
      actualHeight = targetHeight;
      actualWidth = (targetHeight * originalRatio).round();
    }

    // 创建目标画布
    final canvas =
        imgPlus.Image(width: targetWidth, height: targetHeight, numChannels: 4);

    // 填充背景色
    imgPlus.fill(canvas, color: imgPlus.ColorFloat32.rgba(255, 255, 255, 0));

    // 等比放大图片
    final resized = imgPlus.copyResize(
      original,
      width: actualWidth,
      height: actualHeight,
      interpolation: interpolation,
    );

    // 将放大后的图片绘制到画布中央
    final offsetX = (targetWidth - actualWidth) ~/ 2;
    final offsetY = (targetHeight - actualHeight) ~/ 2;

    imgPlus.compositeImage(
      canvas,
      resized,
      dstX: offsetX,
      dstY: offsetY,
    );

    return canvas;
  }
}
