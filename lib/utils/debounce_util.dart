import 'package:flutter/foundation.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';

Logger _logger = Logger("DebounceUtil", on: kDebugMode);

class DebounceUtil {
  static DateTime? lastClickTime;

  static bool checkClick({final int needTime = 1000}) {
    if (null == lastClickTime ||
        DateTime.now().difference(lastClickTime!) >
            Duration(milliseconds: needTime)) {
      lastClickTime = DateTime.now();
      return true;
    } else {
      return false;
    }
  }

  static void debounce(final Function() action, {final int needTime = 1}) {
    if (null == lastClickTime ||
        DateTime.now().difference(lastClickTime!) >
            Duration(seconds: needTime)) {
      lastClickTime = DateTime.now();
      try {
        action.call();
      } catch (e) {
        _logger.log("debounce Exception: $e");
      }
    }
  }
}
