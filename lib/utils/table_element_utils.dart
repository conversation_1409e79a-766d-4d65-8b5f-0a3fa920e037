import 'dart:ui';

import 'package:niimbot_flutter_canvas/extensions/base_element.dart';
import 'package:niimbot_template/models/elements/table_element.dart';

class TableElementUtils {
  static Rect buildCellViewRect(final TableElement table, final num rotate,
      final Offset center, final Rect cellRect) {
    final afterRotate = ((360 - rotate.toDouble()) + table.rotate) % 360;
    final rect = table.getViewRect(rotate.toDouble(), center);
    var viewRect = Rect.fromLTWH(rect.left + cellRect.left,
        rect.top + cellRect.top, cellRect.width, cellRect.height);
    if (afterRotate == 90) {
      viewRect =
          Rect.fromLTWH(rect.right - cellRect.top - cellRect.height, rect.top + cellRect.left, cellRect.height, cellRect.width);
    } else if (afterRotate == 180) {
      viewRect = Rect.fromLTWH(
          rect.right - cellRect.left - cellRect.width, rect.bottom - cellRect.top - cellRect.height, cellRect.width, cellRect.height);
    } else if (afterRotate == 270) {
      viewRect =
          Rect.fromLTWH(rect.left + cellRect.top, rect.bottom - cellRect.left - cellRect.width, cellRect.height, cellRect.width);
    }
    return viewRect;
  }
}
