class FileUtils{
  /// 获取不带后缀名的文件名称
  static String getFileNameWithoutExtension(final path) {
    Uri fileUri = Uri.parse(path);
    String fileNameWithExtension = fileUri.pathSegments.last;
    int dotIndex = fileNameWithExtension.lastIndexOf('.');
    String fileNameWithoutExtension = dotIndex == -1
        ? fileNameWithExtension // 如果没有找到'.'，则文件名就是完整的文件名
        : fileNameWithExtension.substring(0, dotIndex); // 否则去掉扩展名
    return fileNameWithoutExtension;
  }
}