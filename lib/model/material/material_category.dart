/// 素材分类
class MaterialCategory {
  /// 二级分类id（最近分类和vip分类本地构造，id分别为-2和-1)
  int id = 0;

  /// 二级分类名称
  String name = "";

  /// 一级分类id（最近分类和vip分类本地构造，id分别为-2和-1)
  int parentId = 0;

  //是否显示拉新角标
  int? showNewestVersion;

  MaterialCategory({
    this.id = 0,
    this.name = '',
    this.parentId = 0,
    this.showNewestVersion,
  });

  MaterialCategory.fromJson(final Map<String, dynamic> json)
      : id = json["id"],
        name = json["name"],
        parentId = json["parentId"],
        showNewestVersion = json["showNewestVersion"];

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data["id"] = id;
    data["name"] = name;
    data["parentId"] = parentId;
    data["showNewestVersion"] = showNewestVersion;
    return data;
  }

  ///是否为最近使用分类
  bool isRecentUse() {
    return id == -1 && parentId == -1;
  }

  ///是否为vip分类
  bool isVip() {
    return id == -2 && parentId == -2;
  }
}
