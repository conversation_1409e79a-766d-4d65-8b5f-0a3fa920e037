import 'package:niimbot_http/business/apis/rest/models/material_item.dart';

/// 素材分类
class MaterialListGroup {
  /// 一级分类id（最近分类和vip分类本地构造，id分别为-2和-1)
  int industryId = 0;

  /// 二级分类id（最近分类和vip分类本地构造，id分别为-2和-1)
  int catId = 0;

  ///当前请求页码
  int page = 1;

  ///每页请求个数
  int limit = 30;

  ///是否有更多
  bool hasMore = false;

  bool requestFromNet = false;

  List<MaterialItem> itemList = [];

  MaterialListGroup({
    this.industryId = 0,
    this.catId = 0,
    this.page = 1,
    this.limit = 30,
    this.hasMore = false,
    this.itemList = const [],
  });

  MaterialListGroup.withDefaultValue();

  MaterialListGroup.fromJson(final Map<String, dynamic> json) {
    industryId = json["industryId"];
    catId = json["catId"];
    page = json["page"];
    limit = json["limit"];
    hasMore = json["hasMore"];
    requestFromNet = json["requestFromNet"];
    itemList = [];
    if (json["itemList"] != null) {
      json["itemList"].forEach((final v) {
        itemList.add(MaterialItem.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data["industryId"] = industryId;
    data["catId"] = catId;
    data["page"] = page;
    data["limit"] = limit;
    data["hasMore"] = hasMore;
    data["requestFromNet"] = requestFromNet;
    if (itemList.isNotEmpty) {
      data["itemList"] = itemList.map((final e) => e.toJson()).toList();
    }
    return data;
  }
}
