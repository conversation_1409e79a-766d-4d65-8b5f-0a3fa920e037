import 'dart:ui';

import 'package:niimbot_intl/niimbot_intl.dart';

import 'package:niimbot_flutter_canvas/model/font_size_config.dart';

class TemplateConstants {
  ///当前客户端能够支持的JSON协议版本号
  static const SUPPORT_TEMPLATE_VERSION = '1.7.0.1';

  /// 当前客户端保存模板时的JSON协议版本号
  static const SAVE_TEMPLATE_VERSION = '1.7.0.1';

  /// 文本行距 -5到5
  static final FONT_LINE_SPACING = List.generate(100, (final i) => i * 0.1 - 5);

  /// 文本字间距 0-5
  static final FONT_WORD_SPACING = List.generate(50, (final i) => i * 0.1);

  /// 文本字体大小
  static const FONT_SIZE_LIST = [
    FontSizeConfig("4", 4, 1.5),
    FontSizeConfig("5", 5, 1.8),
    FontSizeConfig("5.5", 5.5, 1.9),
    FontSizeConfig("6.5", 6.5, 2.3),
    FontSizeConfig("7.5", 7.5, 2.6),
    FontSizeConfig("8", 8, 2.9),
    FontSizeConfig("9", 9, 3.2),
    FontSizeConfig("9.8", 9.8, 3.4),
    FontSizeConfig("10.5", 10.5, 3.7),
    FontSizeConfig("11.2", 11.2, 3.9),
    FontSizeConfig("12", 12, 4.2),
    FontSizeConfig("13", 13, 4.5),
    FontSizeConfig("14", 14, 4.9),
    FontSizeConfig("15", 15, 5.3),
    FontSizeConfig("16", 16, 5.6),
    FontSizeConfig("17", 17, 5.9),
    FontSizeConfig("18", 18, 6.3),
    FontSizeConfig("20", 20, 7.0),
    FontSizeConfig("22", 22, 7.8),
    FontSizeConfig("24", 24, 8.5),
    FontSizeConfig("26", 26, 9.2),
    FontSizeConfig("28", 28, 10.1),
    FontSizeConfig("30", 30, 11),
    FontSizeConfig("33", 33, 11.9),
    FontSizeConfig("36", 36, 12.7),
    FontSizeConfig("39", 39, 13.8),
    FontSizeConfig("42", 42, 14.8),
    FontSizeConfig("45", 45, 16.0),
    FontSizeConfig("48", 48, 17.0),
    FontSizeConfig("51", 51, 18.0),
    FontSizeConfig("54", 54, 19.0),
    FontSizeConfig("57", 57, 20.0),
    FontSizeConfig("60", 60, 21.0),
    FontSizeConfig("63", 63, 22.2),
    FontSizeConfig("66", 66, 23.0),
    FontSizeConfig("69", 69, 24.0),
    FontSizeConfig("72", 72, 25.4),
    FontSizeConfig("76", 76, 27.0),
    FontSizeConfig("80", 80, 28.2),
    FontSizeConfig("90", 90, 31.7),
    FontSizeConfig("100", 100, 35.3),
    FontSizeConfig("110", 110, 38.8),
    FontSizeConfig("120", 120, 42.3),
    FontSizeConfig("130", 130, 45.8),
    FontSizeConfig("140", 140, 49.3),
    FontSizeConfig("150", 150, 52.8),
    FontSizeConfig("160", 160, 56.5),
    FontSizeConfig("170", 170, 60.5),
    FontSizeConfig("180", 180, 64.6),
    FontSizeConfig("190", 190, 68.0),
    FontSizeConfig("200", 200, 71.8),
  ];

  /// 商品新字段描述名称
  static final GOODS_INFO_FIELD_DESC_NAMES = [
    NiimbotIntl.getIntlMessage("app100000927", "商品条码"),
    NiimbotIntl.getIntlMessage("app01052", "商品名称"),
    NiimbotIntl.getIntlMessage("app01053", "产地"),
    NiimbotIntl.getIntlMessage("app01055", "单位"),
    NiimbotIntl.getIntlMessage("app01054", "规格"),
    NiimbotIntl.getIntlMessage("app01058", "等级"),
    NiimbotIntl.getIntlMessage("app01056", "零售价"),
    NiimbotIntl.getIntlMessage("app01057", "促销价"),
    NiimbotIntl.getIntlMessage("app01116", "物价员"),
  ];

  /// 商品库字段
  static const GOODS_INFO_FIELD_NAMES = [
    "barcode",
    "name",
    "originPlace",
    "unit",
    "norm",
    "level",
    "retailPrice",
    "rushPrice",
    "priceOfficer",
    "id",
    "userId",
    "userCategoryId"
  ];

  ///默认纸张颜色
  static const DEFAULT_PAPER_COLOR = [
    Color.fromARGB(255, 0, 0, 0),
    Color.fromARGB(255, 230, 0, 18)
  ];

  static const blackColorString = '0.0.0';

  static const redColorString = '230.0.18';
  
}

///模板版本不支持
class TemplateVersionNotSupportException implements Exception {
  TemplateVersionNotSupportException();

  @override
  String toString() {
    return 'Template Version Not Support !';
  }
}
