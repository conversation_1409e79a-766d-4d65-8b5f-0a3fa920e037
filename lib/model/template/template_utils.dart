import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:netal_plugin/models/copy_wrapper.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_template/extensions/template_data.dart';
import 'package:niimbot_template/models/elements/bar_code_element.dart';
import 'package:niimbot_template/models/elements/base_element.dart';
import 'package:niimbot_template/models/elements/bind_element.dart';
import 'package:niimbot_template/models/elements/date_element.dart';
import 'package:niimbot_template/models/elements/qr_code_element.dart';
import 'package:niimbot_template/models/elements/table_element.dart';
import 'package:niimbot_template/models/elements/text_element.dart';
import 'package:niimbot_template/models/template/template_data_source_modify.dart';
import 'package:niimbot_template/models/template_data.dart';

import 'package:niimbot_template/template_generate.dart';
import 'package:niimbot_template/utils/element_utils.dart';
import 'package:path/path.dart';

class TemplateUtils {
  /// 复制一个元素 重新生成元素ID
  static T cloneElement<T extends BaseElement>(final T element,
      {final bool isCurrent = true,
      required final Map<String, dynamic> valuesMap}) {
    if (element is TableElement) {
      Map<String, String> combineIdMap = {};
      final combineCells = element.combineCells.map((final e) {
        final newId = ElementUtils.generateId();
        combineIdMap[e.id] = newId;
        return e.copyWith(id: newId);
      }).toList();
      final cells = element.cells.map((final e) {
        final newId = ElementUtils.generateId();
        if (e.combineId?.isNotEmpty ?? false) {
          final combineId = combineIdMap[e.combineId];
          return e.copyWith(id: newId, combineId: CopyWrapper.value(combineId));
        }
        return e.copyWith(id: newId);
      }).toList();
      return element.copyWith(
          id: ElementUtils.generateId(),
          combineCells: combineCells,
          cells: cells) as T;
    } else {
      final newId = ElementUtils.generateId();
      if (!isCurrent &&
          ((((element is TextElement) ||
                  (element is BarCodeElement) ||
                  (element is QRCodeElement))) &&
              valuesMap.isNotEmpty &&
              valuesMap.containsKey(element.id))) {
        ///代表此时复制的有值
        return (element as BindElement).copyWith(
          id: newId,
          value: valuesMap[element.id],
          dataBind: const CopyWrapper.value(null),
        ) as T;
      } else {
        return element.copyWith(
          id: newId,
        ) as T;
      }
    }
  }

  /// 复制元素列表 重新生成Id 并复制关联Id
  static List<BaseElement> cloneElements(final List<BaseElement> elements,
      {final bool isCurrent = true,
      required final Map<String, dynamic> valuesMap}) {
    final Map<String, String> newAssociateId = {};
    return elements.map((final e) {
      final newElement =
          cloneElement(e, isCurrent: isCurrent, valuesMap: valuesMap);
      if (newElement is DateElement) {
        if (newElement.associateId.isNotEmpty) {
          final associateId = newAssociateId[newElement.associateId];
          if (associateId != null) {
            return newElement.copyWith(
                associateId: CopyWrapper.value(associateId));
          } else {
            final id = ElementUtils.generateId();
            newAssociateId[newElement.associateId] = id;
            return newElement.copyWith(associateId: CopyWrapper.value(id));
          }
        }
      }
      return newElement;
    }).toList();
  }

  ///克隆excel导入修改
  static TemplateDataSourceModifies? cloneModify(
      final TemplateDataSourceModifies? modify) {
    if (modify == null) {
      return null;
    }
    return modify.map((final key, final value) => MapEntry(
        key,
        value
            .map((final key, final value) => MapEntry(key, value.copyWith()))));
  }

  /// 获取纸张类型名称
  static String getCableDirectionName(final NetalCableDirection type) {
    switch (type) {
      case NetalCableDirection.top:
        return NiimbotIntl.getIntlMessage('app100000597', '向上');
      case NetalCableDirection.left:
        return NiimbotIntl.getIntlMessage('app100000600', '向左');
      case NetalCableDirection.bottom:
        return NiimbotIntl.getIntlMessage('app100000599', '向下');
      case NetalCableDirection.right:
        return NiimbotIntl.getIntlMessage('app100000598', '向右');
    }
  }

  /// 判断本地图片是否存在并获取图片
  static Future<String?> getLoadBackgroundImage(
    final String localBackImageUrl,
    final num canvasRotate,
    final Uint8List? localBackImageFile,
    final Future<String?> Function(Uint8List data, {String? fileName})
        localFileSave,
  ) async {
    if (localBackImageUrl.isEmpty) {
      return null;
    }
    if (canvasRotate == 0) {
      return localBackImageUrl;
    } else {
      final ext = extension(localBackImageUrl);
      final suffix = ext.isEmpty ? '' : '.$ext';
      final localBackImageUrlRotate =
          '${localBackImageUrl.replaceAll(suffix, '')}-$canvasRotate';
      if (!(File('$localBackImageUrlRotate$suffix').existsSync())) {
        Uint8List? list = localBackImageFile;
        String? backgroundImage = list == null ? null : base64Encode(list);
        if (backgroundImage != null) {
          final base64Image = await TemplateGenerate.rotateBase64Image(
              backgroundImage, canvasRotate);
          Uint8List imageBytes = base64Decode(base64Image);
          final fileName = localBackImageUrlRotate.split('\\').last;
          await localFileSave.call(imageBytes, fileName: fileName);
        }
      }
      return '$localBackImageUrlRotate$suffix';
    }
  }

  static Map<String, Uint8List> _backgroundDataMap = {};

  /// 获取本地图片文件
  static Uint8List? getLocalBackImageFile(final String selectedLocalPath) {
    final localImage = File(selectedLocalPath);
    if (_backgroundDataMap[selectedLocalPath] == null &&
        localImage.existsSync()) {
      _backgroundDataMap.clear();
      _backgroundDataMap[selectedLocalPath] = localImage.readAsBytesSync();
    }
    return _backgroundDataMap[selectedLocalPath];
  }

  /// 获取模板背景图片
  static Future<String?> getTemplateLoadBackgroundImage(
    final TemplateData template,
    final Future<String?> Function(Uint8List data, {String? fileName})
        localFileSave,
  ) async {
    if (template.localBackImageUrl.isEmpty) return null;
    return TemplateUtils.getLoadBackgroundImage(
        template.localBackImageUrl,
        template.canvasRotate,
        getLocalBackImageFile(template.localBackImageUrl),
        localFileSave);
  }
}
