import 'dart:math';

import 'package:niimbot_flutter_canvas/model/template/constants.dart';

class FontSizeConfig {
  /// 小一
  final String title;

  /// 字号
  final double fontSize;

  /// mm
  final double mm;

  const FontSizeConfig(this.title, this.fontSize, this.mm);
}


/// templateWidth, templateHeight 为毫米
/// 根据模版宽高，获取最大字体
List<FontSizeConfig> calcMaxFontSize(final double templateWidth, final double templateHeight) {
  double maxMM = 0;
  double maxEdge = max(templateWidth, templateHeight);
  double minEdge = min(templateWidth, templateHeight);
  if (maxEdge < 2 * minEdge) {
    maxMM = maxEdge / 2;
  }
  if (maxEdge >= 2 * minEdge) {
    maxMM = minEdge;
  }
  int left = 0;
  int right = TemplateConstants.FONT_SIZE_LIST.length - 1;

  int mid = left;
  while (left <= right) {
    mid = (left + right) ~/ 2;
    if (TemplateConstants.FONT_SIZE_LIST[mid].mm == maxMM) {
      left = mid;
      break;
    } else if (TemplateConstants.FONT_SIZE_LIST[mid].mm > maxMM) {
      right = mid - 1;
    } else {
      left = mid + 1;
    }
  }

  if (left >= TemplateConstants.FONT_SIZE_LIST.length) {
    left = TemplateConstants.FONT_SIZE_LIST.length - 1;
  } else if (left < 8) {
    left = 8;
  } else if ((left > 0 &&
      (maxMM - TemplateConstants.FONT_SIZE_LIST[left - 1].mm).abs() < (maxMM - TemplateConstants.FONT_SIZE_LIST[left].mm).abs())) {
    left--;
  }

  return TemplateConstants.FONT_SIZE_LIST.sublist(0, left + 1);
  // return allFontSizeConfigList;
}

extension ClosestValueExtension on List<FontSizeConfig> {
  int getClosestValueIndex(final double target) {
    int low = 0;
    int high = length - 1;
    int closestIndex = -1;

    while (low <= high) {
      int mid = (low + high) ~/ 2;
      double midValue = this[mid].mm;

      if (midValue == target) {
        closestIndex = mid;
        break;
      }

      if (midValue < target) {
        closestIndex = mid;
        low = mid + 1;
      } else {
        high = mid - 1;
      }
    }
    // 检查前一个 double 是否更接近
    if (closestIndex > 0 && target - this[closestIndex - 1].mm < this[closestIndex].mm - target) {
      closestIndex -= 1;
    }

    return closestIndex;

  }

}