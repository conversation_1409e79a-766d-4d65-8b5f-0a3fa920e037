import 'dart:async';
import 'dart:ui';

import 'package:netal_plugin/models/copy_wrapper.dart';
import 'package:netal_plugin/netal_image_result.dart';
import 'package:niimbot_template/models/elements/base_element.dart';
import 'package:niimbot_template/models/elements/element_enum.dart';
import 'package:niimbot_template/models/template/template_data_source.dart';
import 'package:niimbot_template/models/template/template_data_source_modify.dart';

import 'package:niimbot_flutter_canvas/provider/history/history_store.dart';
import 'package:niimbot_flutter_canvas/provider/models/base_element_updater.dart';

/// 埋点事件
/// [data] 埋点数据
typedef TracerCallback = void Function(Map<String, dynamic> data);

/// 更新元素位置事件
/// [elementId] 元素Id
/// [offset] 元素位置
typedef UpdateElementOffsetCallback = void Function(
  String elementId,
  Offset offset,
);

/// 批量更新元素位置事件
/// [elementsId] 元素Id集合
/// [offset] 元素位置
typedef UpdateElementsOffsetCallback = void Function(
  List<String> elementsId,
  Offset offset,
);

/// 批量更新元素位置事件
/// [update.key] 元素id
/// [update.value] 元素位置
typedef UpdateElementsOffsetsCallback = void Function(
    Map<String, Offset> update);

/// 镜像更新
class MirrorUpdate {
  bool isOpenMirror;
  CopyWrapper<String?>? mirrorId;
  ElementMirrorType mirrorType;

  MirrorUpdate({
    required this.isOpenMirror,
    this.mirrorId,
    required this.mirrorType,
  });
}

/// 批量更新元素镜像信息事件
/// [update.key] 元素id
/// [update.value] 元素镜像更新
typedef UpdateElementsMirrorCallback = void Function(
    Map<String, MirrorUpdate> update);

/// 批量更新元素事件
/// [update.key] 元素id
/// [update.value] 元素
typedef UpdateElementsCallback = void
    Function(Map<String, BaseElementUpdater> update, {SnapshotType type});

/// 移除已使用字体
typedef UsedFontsRemoveFontCallback = void Function(List<String> fontCode);

/// 更新表格单元格聚焦事件
/// [tableId] 表格id
/// [update.key] 元素id
/// [update.value] 元素是否聚焦
typedef UpdateTableCellFocusCallBack = void Function(
    String tableId, Map<String, bool> update);

/// 更新表格单元格图片事件
/// [tableId] 表格id
/// [update.key] 元素id
/// [update.value] 单元格图片
typedef UpdateTableCellImageCallBack = void Function(
    String tableId, Map<String, NetalImageResult> update);

class ColorUpdate {
  final int? colorChannel;

  final Color? elementColor;

  final Color? contentColor;

  final Color? lineColor;

  const ColorUpdate({
    this.colorChannel,
    this.elementColor,
    this.contentColor,
    this.lineColor,
  });

  ColorUpdate copyWith({
    final int? colorChannel,
    final Color? elementColor,
    final Color? contentColor,
    final Color? lineColor,
  }) {
    return ColorUpdate(
      colorChannel: colorChannel ?? this.colorChannel,
      elementColor: elementColor ?? this.elementColor,
      contentColor: contentColor ?? this.contentColor,
      lineColor: lineColor ?? this.lineColor,
    );
  }
}

/// 更新元素颜色事件
/// [update.key] 元素id
/// [update.value] 颜色
typedef UpdateElementColorCallBack = void Function(
    Map<String, ColorUpdate> update);

/// 更新元素值
typedef UpdateElementsValuesCallBack = void Function(
    Map<String, String> update);

class SizeUpdate {
  final num? width;
  final num? height;

  const SizeUpdate({this.width, this.height});

  SizeUpdate copyWith({
    final num? width,
    final num? height,
  }) {
    return SizeUpdate(
      width: width ?? this.width,
      height: height ?? this.height,
    );
  }
}

class BindUpdate {
  final String? value;
  final CopyWrapper<List<String>?>? dataBind;
  final String? parentId;

  BindUpdate({this.value, this.dataBind, this.parentId});
}

/// 更新元素数据源修改标记
typedef UpdateTemplateDataSourceModifiesCallBack = void Function(
    TemplateDataSourceModifies? dataSourceModifies,
    {SnapshotType type});
typedef GetSelectRect = Rect? Function({bool includeMirror});

/// 更新数据源标记
typedef UpdateTemplateDataSourceCallBack = void Function(
    List<TemplateDataSource>? templateDataSource,
    {int? page,
    bool? needRefresh,
    SnapshotType type});

///断开单个数据源绑定
typedef UpdateTemplateDataSingleBindBreakCallBack = void Function(String? eId);

///添加元素
typedef AddElementsFunction<T extends BaseElement> = void Function(List<T> list,
    {Offset? offset, SnapshotType type, bool ignore, bool isViewOffset});

///删除元素
typedef DeleteElementsFunction = void Function(
    {List<String>? ids, SnapshotType type});

/// 多步历史记录合并记录
typedef HistoryStoreRunInAction = void Function(
    FutureOr<void> Function() action);
