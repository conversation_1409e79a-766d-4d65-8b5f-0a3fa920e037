import 'dart:typed_data';

import 'package:flutter/cupertino.dart';
import 'package:netal_plugin/models/copy_wrapper.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:netal_plugin/netal_image_result.dart';
import 'package:niimbot_template/models/elements/element_enum.dart';
import 'package:niimbot_template/models/elements/qr_code_element.dart';

/// 二维码元素
@immutable
class CanvasQRCodeElement extends QRCodeElement {
  ///二维码图片
  final Uint8List? qrCodeImageData;

  CanvasQRCodeElement({
    super.id,
    super.x,
    super.y,
    super.width = 10,
    super.height = 10,
    super.rotate,
    super.isLock,
    super.isOpenMirror,
    super.mirrorType,
    // super.paperColorIndex,
    super.hasVipRes,
    super.colorChannel,
    super.elementColor,
    super.isBinding,
    super.dataBind,
    super.fieldName,
    super.codeType,
    super.correctLevel,
    super.value,
    super.isForm = false,
    super.isLive = false,
    super.liveCodeId,
    super.formId,
    this.qrCodeImageData,
  });

  @override
  CanvasQRCodeElement copyWith({
    final num? x,
    final num? y,
    final int? rotate,
    final num? width,
    final num? height,
    final String? id,
    final bool? isLock,
    final bool? isOpenMirror,
    final Color? elementColor,
    final String? value,
    final ElementMirrorType? mirrorType,
    final bool? hasVipRes,
    final int? colorChannel,
    final NetalQRCodeType? codeType,
    final NetalQRCodeCorrectLevel? correctLevel,
    final CopyWrapper<List<String>?>? dataBind,
    final CopyWrapper<String?>? fieldName,
    final bool? isBinding,
    // int? paperColorIndex,
    final bool? dragging,
    final bool? focused,
    final bool? selected,
    final CopyWrapper<NetalImageResult?>? imageCache,
    final Uint8List? qrCodeImageData,
  }) {
    return CanvasQRCodeElement(
      x: x ?? this.x,
      y: y ?? this.y,
      rotate: rotate ?? this.rotate,
      width: width ?? this.width,
      height: height ?? this.height,
      id: id ?? this.id,
      isLock: isLock ?? this.isLock,
      isOpenMirror: isOpenMirror ?? this.isOpenMirror,
      mirrorType: mirrorType ?? this.mirrorType,
      hasVipRes: hasVipRes ?? this.hasVipRes,
      colorChannel: colorChannel ?? this.colorChannel,
      elementColor: elementColor ?? this.elementColor,
      value: value ?? this.value,
      codeType: codeType ?? this.codeType,
      correctLevel: correctLevel ?? this.correctLevel,
      dataBind: dataBind != null ? dataBind.value : this.dataBind,
      fieldName: fieldName != null ? fieldName.value : this.fieldName,
      qrCodeImageData: qrCodeImageData ?? this.qrCodeImageData,
    );
  }

  factory CanvasQRCodeElement.fromQRCodeElement(final QRCodeElement element) {
    return CanvasQRCodeElement(
      x: element.x,
      y: element.y,
      rotate: element.rotate,
      width: element.width,
      height: element.height,
      id: element.id,
      isLock: element.isLock,
      isOpenMirror: element.isOpenMirror,
      mirrorType: element.mirrorType,
      hasVipRes: element.hasVipRes,
      colorChannel: element.colorChannel,
      elementColor: element.elementColor,
      value: element.value,
      codeType: element.codeType,
      correctLevel: element.correctLevel,
      dataBind: element.dataBind,
      fieldName: element.fieldName,
    );
  }

  static String get defaultValue => '123456789';

  @override
  List<Object?> get props => [...super.props, qrCodeImageData];
}
