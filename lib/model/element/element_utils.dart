import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_template/models/elements/bar_code_element.dart';
import 'package:niimbot_template/models/elements/base_element.dart';
import 'package:niimbot_template/models/elements/bind_element.dart';
import 'package:niimbot_template/models/elements/element_enum.dart';
import 'package:niimbot_template/models/elements/graph_element.dart';
import 'package:niimbot_template/models/elements/image_element.dart';
import 'package:niimbot_template/models/elements/line_element.dart';
import 'package:niimbot_template/models/elements/material_element.dart';
import 'package:niimbot_template/models/elements/qr_code_element.dart';
import 'package:niimbot_template/models/elements/serial_element.dart';
import 'package:niimbot_template/models/elements/table_element.dart';
import 'package:niimbot_template/models/elements/text_element.dart';
import 'package:niimbot_template/models/template/template_data_source.dart';
import 'package:niimbot_template/models/template/template_data_source_info.dart';
import 'package:niimbot_template/models/template/template_data_source_modify.dart';
import 'package:niimbot_template/models/values/element_value.dart';
import 'package:niimbot_template/utils/template_data_source_utils.dart';
import 'package:uuid/uuid.dart';

import 'package:niimbot_flutter_canvas/model/element/style_attr/barcode_style_attr.dart';
import 'package:niimbot_flutter_canvas/model/element/style_attr/color_style_attr.dart';
import 'package:niimbot_flutter_canvas/model/element/style_attr/graph_style_attr.dart';
import 'package:niimbot_flutter_canvas/model/element/style_attr/image_style_attr.dart';
import 'package:niimbot_flutter_canvas/model/element/style_attr/line_style_attr.dart';
import 'package:niimbot_flutter_canvas/model/element/style_attr/material_style_attr.dart';
import 'package:niimbot_flutter_canvas/model/element/style_attr/text_style_attr.dart';

final Logger _logger = Logger("ElementUtils", on: kDebugMode);

class ElementUtils {
  /// 生成 id
  static String generateId() {
    return const Uuid().v4().replaceAll("-", "");
  }

  /// 解析颜色
  static Color parseColorFromStr(final String color) {
    List<int> elementColor = color
        .split(".")
        .map((final e) => int.tryParse(e))
        .whereType<int>()
        .toList();
    if (elementColor.length == 4) {
      return Color.fromARGB(
          elementColor[0], elementColor[1], elementColor[2], elementColor[3]);
    } else if (elementColor.length == 3) {
      return Color.fromARGB(
          255, elementColor[0], elementColor[1], elementColor[2]);
    }
    return Colors.black;
  }

  /// 元素类型转换成选择类型
  static ColorStyleAttribute? elementToStyleAttribute(
      final BaseElement? element) {
    if (element is TextElement) {
      return TextStyleAttribute(
        colorChannel: element.colorChannel,
        colorReverse: element.colorReverse,
        fontFamily: element.fontFamily,
        fontSize: element.fontSize,
        textAlign: element.typesettingMode == NetalTypesettingMode.vertical
            ? element.textAlignVertical
            : element.textAlignHorizontal,
        fontStyle: element.fontStyle,
        letterSpacing: element.letterSpacing,
        lineSpacing: element.lineSpacing,
        typesettingMode: element.typesettingMode,
        typesettingParam: element.typesettingParam,
        lineBreakMode: element.lineBreakMode,
        rotate: element.rotate,
        elementColor: element.elementColor,
      );
    }
    if (element is BarCodeElement) {
      return BarCodeStyleAttribute(
        colorChannel: element.colorChannel,
        fontSize: element.fontSize,
        textPosition: element.textPosition,
        width: element.width,
        height: element.height,
        value: element.value,
        elementColor: element.elementColor,
      );
    }
    if (element is QRCodeElement) {
      return ColorStyleAttribute(
        colorChannel: element.colorChannel,
        elementColor: element.elementColor,
      );
    }
    if (element is GraphElement) {
      return GraphStyleAttribute(
        colorChannel: element.colorChannel,
        lineWidth: element.lineWidth,
        lineType: element.lineType,
        graphType: element.graphType,
        elementColor: element.elementColor,
      );
    }
    if (element is LineElement) {
      return LineStyleAttribute(
        colorChannel: element.colorChannel,
        lineWidth: element.height,
        lineType: element.lineType,
        elementColor: element.elementColor,
      );
    }
    if (element is MaterialElement) {
      return MaterialStyleAttribute(
        colorChannel: element.colorChannel,
        processingValue: element.imageProcessingValue,
        allowFreeZoom: element.allowFreeZoom,
        colorReverse: element.colorReverse,
        elementColor: element.elementColor,
      );
    }
    if (element is ImageElement) {
      return ImageStyleAttribute(
        colorChannel: element.colorChannel,
        processingValue: element.imageProcessingValue,
        allowFreeZoom: element.allowFreeZoom,
        renderType: element.imageProcessingType,
        elementColor: element.elementColor,
      );
    }

    // if (element is TableElement) {
    //   return ElementSelectType.table;
    // }TODO 表格未实现
    return null;
  }

  /// 生成镜像元素
  ///
  static T buildMirrorElement<T extends BaseElement>({
    required final T element,
    required final Size templateSize,
  }) {
    /// 模板中心点
    Offset templateCenter = templateSize.center(Offset.zero);
    Offset mirrorPosition = templateCenter * 2 -
        Offset(element.x.toDouble() + element.width,
            element.y.toDouble() + element.height);

    final rotate = (element.rotate + 180) % 360;
    if (element.mirrorType == ElementMirrorType.canvasCenter) {
      /// 画板中心点镜像
      return element.copyWith(
          x: mirrorPosition.dx, y: mirrorPosition.dy, rotate: rotate) as T;
    } else {
      if (element.mirrorType == ElementMirrorType.canvasCenterX) {
        /// 画板中心 x 轴镜像
        return element.copyWith(x: mirrorPosition.dx, rotate: rotate) as T;
      } else {
        /// 画板中心 y 轴镜像
        return element.copyWith(y: mirrorPosition.dy, rotate: rotate) as T;
      }
    }
  }

  ///是否支持竖排文字
  static bool checkSupportVerticalText() {
    // 竖排文字仅支持应用内 简中/繁中/韩文/日文/英文
    String currentLanguage = NiimbotIntl.getCurrentLocale().languageCode;
    return currentLanguage == LanguageName.LANGUAGE_ZH_CN ||
        currentLanguage == LanguageName.LANGUAGE_ZH_TW ||
        currentLanguage == "zh" ||
        currentLanguage == LanguageName.LANGUAGE_KO ||
        currentLanguage == LanguageName.LANGUAGE_JA ||
        currentLanguage == LanguageName.LANGUAGE_EN;
  }

  /// 重新生成元素数据
  static T rebuildElementValue<T extends BaseElement>(
    final T element, {
    final int? page,
    final List<TemplateDataSource>? dataSources,
    final TemplateDataSourceModifies? modify,
    final TemplateDataSourceInfo? bindInfo,
    final DataSourceTextValue? dValue,
  }) {
    if (element is SerialElement) {
      return element.copyWith(index: page ?? bindInfo?.page ?? 1) as T;
    } else if (element is TableElement) {
      final cells = element.cells.map((final e) {
        final value = TemplateDataSourceUtils.getElementBindValue(
          e.id,
          e.value,
          e.dataBind,
          dataSources,
          modify,
          page ?? bindInfo?.page ?? 1,
        );
        return e.copyWith(value: value);
      }).toList();
      final combineCells = element.combineCells.map((final e) {
        final value = TemplateDataSourceUtils.getElementBindValue(
          e.id,
          e.value,
          e.dataBind,
          dataSources,
          modify,
          page ?? bindInfo?.page ?? 1,
        );
        return e.copyWith(value: value);
      }).toList();
      return element.copyWith(cells: cells, combineCells: combineCells) as T;
    } else if (element is BindElement) {
      if (element is BarCodeElement) {
        /// 一维码始终 不显示标题
        final elementModify = modify?[element.id];
        if (elementModify != null) {
          modify?[element.id] = elementModify.map((final k, final v) {
            return MapEntry(k, v.copyWith(useTitle: false));
          });
        }
      }
      if (element.value != null) {
        final value = TemplateDataSourceUtils.getElementBindValue(
          element.id,
          element.value!,
          (element.dataBind?.isNotEmpty ?? false) ? element.dataBind : null,
          dataSources,
          modify,
          page ?? bindInfo?.page ?? 1,
        );
        return element.copyWith(value: value) as T;
      }
      if (dValue != null) {
        final value = TemplateDataSourceUtils.getElementBindValue(
          element.id,
          dValue.value,
          (dValue.dataBind.isNotEmpty) ? dValue.dataBind : null,
          dataSources,
          modify,
          page ?? bindInfo?.page ?? 1,
        );
        return element.copyWith(value: value) as T;
      }
    }
    return element;
  }
}
