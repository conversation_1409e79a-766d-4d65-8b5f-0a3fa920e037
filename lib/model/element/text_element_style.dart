
import 'package:netal_plugin/models/netal_enum.dart';

/// 文本框样式
enum TextElementBoxStyle {
  /// 高度固定 ，宽度自适应
  autoWidth,

  /// 宽度固定， 高度自适应
  autoHeight,

  /// 宽度固定，高度固定
  fixedWidthHeight,

  /// 空模式，兼容老版本
  empty,
}

/// 文本框样式拓展方法
extension TextElementBoxStyleString on TextElementBoxStyle {
  /// 获取字符串
  String get value {
    switch (this) {
      case TextElementBoxStyle.autoWidth:
        return "auto-width";
      case TextElementBoxStyle.autoHeight:
        return "auto-height";
      case TextElementBoxStyle.fixedWidthHeight:
        return "fixed-width-height";
      default:
        return "";
    }
  }

  /// 字符串转文本框样式
  static TextElementBoxStyle create(final String value) {
    return TextElementBoxStyle.values
        .firstWhere((final element) => element.value == value, orElse: () => TextElementBoxStyle.autoHeight);
  }
}

/// 内容样式
enum TextElementTextStyle {
  // 正常模式（默认）。文本内容按指定字号填充到文本框中，不做任何处理
  norm,
  // 缩小模式 - 如果文本超出文本框后，文本缩小适后填充到文本框中（字号有临界值，小于临界值后不在缩放）
  minimized,
  // 截断模式 - 如果文本超出文本框后，文本截断后填充到文本框中
  truncate,
  // 省略号模式 - 如果文本超出文本框，超出部分用 "..." 表示后填充到文本框中（暂不支持）
  ellipsis,
  // 自适应模式 - 文本填满文本框（放大或者缩小）
  adaptive,
  // 拉伸模式 - 如果文本内容超出文本框，文字竖向拉伸文本后填充到文本框中（单行模式）
  stretching
}

extension TextElementTextStyleString on NetalTextStyle {
  String get trackName {
    switch (this) {
      case NetalTextStyle.norm:
        return "norm";
        break;
      case NetalTextStyle.minimized:
        return "填满后缩小";
        break;
      case NetalTextStyle.truncate:
        return "截断模式";
        break;
      case NetalTextStyle.ellipsis:
        return "省略号模式";
        break;
      case NetalTextStyle.adaptive:
        return "自适应";
        break;
      case NetalTextStyle.stretching:
        return "拉伸模式";
        break;
    }
    return "";
  }
}

/*extension TextElementTextStyleBIO on TextElement {
  /// Json To Model
  void textStyleFromJson(Map<String, dynamic> json) {
    /// 老版本默认使用autoHeight
    var newTextStyle = <TextElementTextStyle>[];
    if (json["textStyle"] != null && json["textStyle"] is List) {
      final originTextStyle = List.from(json["textStyle"]);
      newTextStyle = originTextStyle.map((e) {
        return TextElementTextStyleString.create(e);
      }).toList();
    }
    if (newTextStyle == null || newTextStyle.length == 0) {
      newTextStyle = TextElementBO.defaultTextStyles();
    }
    this.textStyle = newTextStyle;
    final boxStyle = TextElementBoxStyleString.create(json["boxStyle"]) ?? TextElementBoxStyle.empty;
    this.updateBoxStyle(boxStyle);
    if (this.lastUnlessFixWidthHeightBoxStyle == null && json["lastUnlessFixWidthHeightBoxStyle"] is String) {
      this.lastUnlessFixWidthHeightBoxStyle =
          TextElementBoxStyleString.create(json["lastUnlessFixWidthHeightBoxStyle"]);
    }
    this.isEditing = json["isEditing"] ?? false;
    this.fontSizeChanged = json["fontSizeChanged"] ?? false;
  }

  /// Model To Json
  void textStyleToJson(Map<String, dynamic> json) {
    if (this.isSupportBoxStyle()) {
      /// 文字存在换行，需要切换模式
      this.handleBoxStyleWhenValueChange(json["value"]);
      json["boxStyle"] = this.boxStyle.value;
      json["textStyle"] = this.textStyle != null ? this.textStyle.map((e) => e.value).toList() : [];
      json["lastUnlessFixWidthHeightBoxStyle"] = this.lastUnlessFixWidthHeightBoxStyle.value;
      json["isEditing"] = this.isEditing ?? false;
      json["fontSizeChanged"] = this.fontSizeChanged ?? false;
    }
  }
}*/
