@startuml netal
class NetalElementBase {
  +num x
  +num y
  +num width
  +num height
  +NetalElementType type
  +int zIndex
  +num rotate
  +Map<String, dynamic> toJson()
}

class NetalColorElement {
  +Color elementColor
  +int colorChannel
  +Map<String, dynamic> toJson()
}

NetalElementBase <|-- NetalColorElement

class NetalBarcodeElement {
  +num fontSize
  +num textHeight
  +NetalBarcodeTextPosition textPosition
  +NetalBarcodeType codeType
  +Map<String, dynamic> toJson()
}

NetalValueElement <|-- NetalBarcodeElement

class NetalGraphElement {
  +num cornerRadius
  +num lineWidth
  +NetalGraphType graphType
  +Map<String, dynamic> toJson()
}

NetalLineElement <|-- NetalGraphElement

class NetalImageElement {
  +String imageData
  +NetalImageRenderType imageProcessingType
  +List<num> imageProcessingValue
  +Map<String, dynamic> toJson()
}

NetalColorElement <|-- NetalImageElement

class NetalLineElement {
  +NetalLineType lineType
  +List<num> dashWidth
  +Map<String, dynamic> toJson()
}

NetalColorElement <|-- NetalLineElement

class NetalQRCodeElement {
  +NetalQRCodeCorrectLevel correctLevel
  +NetalQRCodeType codeType
  +Map<String, dynamic> toJson()
}

NetalValueElement <|-- NetalQRCodeElement

class NetalTableCellElement {
  +String? combineId
  +int rowIndex
  +int columnIndex
  +Map<String, dynamic> toJson()
}

NetalTableCombineCellElement <|-- NetalTableCellElement

class NetalTableCombineCellElement {
  +String id
  +String value
  +Map<String, dynamic> toJson()
}

NetalTextProps <|-- NetalTableCombineCellElement

class NetalTableElement {
  +List<num> rowHeight
  +List<num> columnWidth
  +num lineWidth
  +NetalLineType lineType
  +Color lineColor
  +Color contentColor
  +int lineColorChannel
  +int contentColorChannel
  +List<NetalTableCellElement> cells
  +List<NetalTableCombineCellElement> combineCells
  +int row
  +int column
  +num width
  +num height
  +Map<String, dynamic> toJson()
}

NetalElementBase <|-- NetalTableElement
NetalTableCellElement o-- NetalTableElement
NetalTableCombineCellElement o-- NetalTableElement

class NetalTextElement {
  +bool colorReverse
  +String fontCode
  +String fontFamily
  +num fontSize
  +List<NetalTextFontStyle> fontStyle
  +num letterSpacing
  +NetalTextLineBreakMode lineBreakMode
  +NetalTextLineMode lineMode
  +num lineSpacing
  +NetalTextAlign textAlignHorizontal
  +NetalTextAlign textAlignVertical
  +NetalTypesettingMode typesettingMode
  +List<num> typesettingParam
  +num wordSpacing
  +NetalTextWriteMode write_mode
  +Map<String, dynamic> toJson()
}

NetalValueElement <|-- NetalTextElement
NetalTextProps <|-- NetalTextElement

class NetalTextProps {
  +NetalTextAlign textAlignHorizontal
  +NetalTextAlign textAlignVertical
  +NetalTextLineMode lineMode
  +num wordSpacing
  +num letterSpacing
  +num lineSpacing
  +String fontCode
  +String fontFamily
  +List<NetalTextFontStyle> fontStyle
  +num fontSize
  +NetalTextLineBreakMode lineBreakMode
  +NetalTypesettingMode typesettingMode
  +List<num> typesettingParam
  +bool colorReverse
  +NetalTextWriteMode write_mode
  +Map<String, dynamic> toJson()
}

class NetalValueElement {
  +String value
  +Map<String, dynamic> toJson()
}

NetalColorElement <|-- NetalValueElement
@enduml
