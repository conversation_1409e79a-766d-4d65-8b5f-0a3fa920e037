import 'package:flutter/material.dart';
import 'package:netal_plugin/models/copy_wrapper.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:niimbot_template/models/elements/element_enum.dart';

import 'package:niimbot_flutter_canvas/model/element/style_attr/color_reverse_style_attr.dart';

class TextStyleAttribute extends ColorReverseStyleAttribute {
  final String? fontFamily;
  final num fontSize;
  final NetalTextAlign? textAlign;
  final List<NetalTextFontStyle>? fontStyle;
  final num letterSpacing;
  final num lineSpacing;
  final NetalTypesettingMode? typesettingMode;
  final List<num> typesettingParam;
  final NetalTextLineBreakMode? lineBreakMode;
  final num? rotate;

  const TextStyleAttribute({
    this.fontFamily,
    required this.fontSize,
    this.textAlign,
    this.fontStyle,
    required this.letterSpacing,
    required this.lineSpacing,
    this.typesettingMode,
    required this.typesettingParam,
    this.lineBreakMode,
    super.colorChannel,
    super.colorReverse,
    super.elementColor,
    this.rotate,
  });

  @override
  TextStyleAttribute copyWith({
    final CopyWrapper<int?>? colorChannel,
    final bool? colorReverse,
    final CopyWrapper<String?>? fontFamily,
    final num? fontSize,
    final CopyWrapper<NetalTextAlign?>? textAlign,
    final CopyWrapper<List<NetalTextFontStyle>?>? fontStyle,
    final num? letterSpacing,
    final num? lineSpacing,
    final CopyWrapper<NetalTypesettingMode?>? typesettingMode,
    final List<num>? typesettingParam,
    final CopyWrapper<NetalTextLineBreakMode?>? lineBreakMode,
    final num? rotate,
    final CopyWrapper<Color?>? elementColor,
  }) {
    return TextStyleAttribute(
      colorChannel:
          colorChannel != null ? colorChannel.value : this.colorChannel,
      colorReverse: colorReverse ?? this.colorReverse,
      fontFamily: fontFamily != null ? fontFamily.value : this.fontFamily,
      fontSize: fontSize ?? this.fontSize,
      textAlign: textAlign != null ? textAlign.value : this.textAlign,
      fontStyle: fontStyle != null ? fontStyle.value : this.fontStyle,
      letterSpacing: letterSpacing ?? this.letterSpacing,
      lineSpacing: lineSpacing ?? this.lineSpacing,
      typesettingMode: typesettingMode != null
          ? typesettingMode.value
          : this.typesettingMode,
      typesettingParam: typesettingParam ?? this.typesettingParam,
      lineBreakMode:
          lineBreakMode != null ? lineBreakMode.value : this.lineBreakMode,
      rotate: rotate ?? this.rotate,
      elementColor:
          elementColor != null ? elementColor.value : this.elementColor,
    );
  }

  TextTypesettingMode get textTypesettingMode {
    if (typesettingMode == NetalTypesettingMode.vertical) {
      if (rotate == 270) {
        return TextTypesettingMode.Horizontal_90;
      } else {
        return TextTypesettingMode.Vertical;
      }
    }
    if (typesettingMode == NetalTypesettingMode.arc) {
      return TextTypesettingMode.Arc;
    }
    return TextTypesettingMode.Horizontal;
  }
}
