import 'package:flutter/material.dart';
import 'package:netal_plugin/models/copy_wrapper.dart';
import 'package:netal_plugin/models/netal_enum.dart';

import 'package:niimbot_flutter_canvas/model/element/style_attr/color_reverse_style_attr.dart';

class ImageStyleAttribute extends ColorReverseStyleAttribute {
  final NetalImageRenderType? renderType;
  final List<num> processingValue;
  final bool allowFreeZoom;

  const ImageStyleAttribute({
    super.colorChannel,
    super.colorReverse,
    super.elementColor,
    required this.processingValue,
    final bool? allowFreeZoom,
    this.renderType,
  }) : allowFreeZoom = allowFreeZoom ?? false;

  @override
  ImageStyleAttribute copyWith({
    final CopyWrapper<int?>? colorChannel,
    final bool? colorReverse,
    final List<num>? processingValue,
    final bool? allowFreeZoom,
    final CopyWrapper<NetalImageRenderType?>? renderType,
    final CopyWrapper<Color?>? elementColor,
  }) {
    return ImageStyleAttribute(
      colorChannel:
          colorChannel != null ? colorChannel.value : this.colorChannel,
      colorReverse: colorReverse ?? this.colorReverse,
      processingValue: processingValue ?? this.processingValue,
      allowFreeZoom: allowFreeZoom ?? this.allowFreeZoom,
      renderType: renderType != null ? renderType.value : this.renderType,
      elementColor:
          elementColor != null ? elementColor.value : this.elementColor,
    );
  }
}
