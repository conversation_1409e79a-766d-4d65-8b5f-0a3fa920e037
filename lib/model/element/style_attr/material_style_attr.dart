import 'package:flutter/material.dart';
import 'package:netal_plugin/models/copy_wrapper.dart';
import 'package:netal_plugin/models/netal_enum.dart';

import 'package:niimbot_flutter_canvas/model/element/style_attr/image_style_attr.dart';

class MaterialStyleAttribute extends ImageStyleAttribute {
  const MaterialStyleAttribute({
    super.colorChannel,
    super.colorReverse,
    required super.processingValue,
    super.elementColor,
    super.allowFreeZoom,
  });

  @override
  MaterialStyleAttribute copyWith({
    final CopyWrapper<int?>? colorChannel,
    final bool? colorReverse,
    final List<num>? processingValue,
    final bool? allowFreeZoom,
    final CopyWrapper<Color?>? elementColor,
    final CopyWrapper<NetalImageRenderType?>? renderType,
  }) {
    return MaterialStyleAttribute(
      colorChannel:
          colorChannel != null ? colorChannel.value : this.colorChannel,
      colorReverse: colorReverse ?? this.colorReverse,
      processingValue: processingValue ?? this.processingValue,
      allowFreeZoom: allowFreeZoom ?? this.allowFreeZoom,
      elementColor:
          elementColor != null ? elementColor.value : this.elementColor,
    );
  }
}
