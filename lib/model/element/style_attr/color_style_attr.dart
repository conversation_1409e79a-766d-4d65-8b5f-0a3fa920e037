import 'package:flutter/material.dart';
import 'package:netal_plugin/models/copy_wrapper.dart';

/// 颜色样式属性
@immutable
class ColorStyleAttribute {
  final int? colorChannel;
  final Color? elementColor;

  const ColorStyleAttribute({this.colorChannel, this.elementColor});

  ColorStyleAttribute copyWith({
    final CopyWrapper<int?>? colorChannel,
    final CopyWrapper<Color?>? elementColor,
  }) {
    return ColorStyleAttribute(
      colorChannel:
          colorChannel != null ? colorChannel.value : this.colorChannel,
      elementColor:
          elementColor != null ? elementColor.value : this.elementColor,
    );
  }
}
