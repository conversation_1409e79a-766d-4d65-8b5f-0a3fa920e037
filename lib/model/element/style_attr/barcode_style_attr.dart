import 'package:netal_plugin/models/copy_wrapper.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:flutter/material.dart';

import 'package:niimbot_flutter_canvas/model/element/style_attr/color_style_attr.dart';

class BarCodeStyleAttribute extends ColorStyleAttribute {
  final num fontSize;
  final NetalBarcodeTextPosition? textPosition;
  final num? width;
  final num? height;
  final String? value;
  final NetalBarcodeType? type;

  const BarCodeStyleAttribute({
    super.colorChannel,
    super.elementColor,
    required this.fontSize,
    this.textPosition,
    this.width,
    this.height,
    this.value,
    this.type,
  });

  @override
  BarCodeStyleAttribute copyWith({
    final CopyWrapper<int?>? colorChannel,
    final num? fontSize,
    final CopyWrapper<NetalBarcodeTextPosition?>? textPosition,
    final num? width,
    final num? height,
    final String? value,
    final CopyWrapper<Color?>? elementColor,
    final CopyWrapper<NetalBarcodeType?>? type,
  }) {
    return BarCodeStyleAttribute(
      colorChannel:
          colorChannel != null ? colorChannel.value : this.colorChannel,
      fontSize: fontSize ?? this.fontSize,
      textPosition:
          textPosition != null ? textPosition.value : this.textPosition,
      width: width ?? this.width,
      height: height ?? this.height,
      value: value ?? this.value,
      type: type != null ? type.value : this.type,
      elementColor:
          elementColor != null ? elementColor.value : this.elementColor,
    );
  }
}
