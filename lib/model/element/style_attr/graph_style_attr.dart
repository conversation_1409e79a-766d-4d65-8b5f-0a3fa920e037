import 'package:flutter/material.dart';
import 'package:netal_plugin/models/copy_wrapper.dart';
import 'package:netal_plugin/models/netal_enum.dart';

import 'package:niimbot_flutter_canvas/model/element/style_attr/line_style_attr.dart';

class GraphStyleAttribute extends LineStyleAttribute {
  final NetalGraphType? graphType;

  const GraphStyleAttribute({
    super.colorChannel,
    super.elementColor,
    super.lineType,
    required super.lineWidth,
    this.graphType,
  });

  @override
  GraphStyleAttribute copyWith({
    final CopyWrapper<int?>? colorChannel,
    final CopyWrapper<NetalLineType?>? lineType,
    final num? lineWidth,
    final CopyWrapper<NetalGraphType?>? graphType,
    final CopyWrapper<Color?>? elementColor,
  }) {
    return GraphStyleAttribute(
      colorChannel:
          colorChannel != null ? colorChannel.value : this.colorChannel,
      lineType: lineType != null ? lineType.value : this.lineType,
      lineWidth: lineWidth ?? this.lineWidth,
      graphType: graphType != null ? graphType.value : this.graphType,
      elementColor:
          elementColor != null ? elementColor.value : this.elementColor,
    );
  }
}
