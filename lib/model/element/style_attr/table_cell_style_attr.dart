import 'package:netal_plugin/models/copy_wrapper.dart';
import 'package:netal_plugin/models/netal_enum.dart';

class TableCellStyleAttribute {
  final String? fontFamily;
  final String? fontCode;
  final num fontSize;
  final NetalTextAlign? textAlignHorizontal;
  final NetalTextAlign? textAlignVertical;
  final List<NetalTextFontStyle>? fontStyle;
  final num letterSpacing;
  final num lineSpacing;
  final NetalTextLineBreakMode? lineBreakMode;

  TableCellStyleAttribute({
    this.fontFamily,
    this.fontCode,
    required this.fontSize,
    this.textAlignHorizontal,
    this.textAlignVertical,
    this.fontStyle,
    required this.letterSpacing,
    required this.lineSpacing,
    this.lineBreakMode,
  });

  TableCellStyleAttribute copyWith({
    final CopyWrapper<String?>? fontFamily,
    final CopyWrapper<String?>? fontCode,
    final num? fontSize,
    final CopyWrapper<NetalTextAlign?>? textAlignHorizontal,
    final CopyWrapper<NetalTextAlign?>? textAlignVertical,
    final CopyWrapper<List<NetalTextFontStyle>?>? fontStyle,
    final num? letterSpacing,
    final num? lineSpacing,
    final List<num>? typesettingParam,
    final CopyWrapper<NetalTextLineBreakMode?>? lineBreakMode,
  }) {
    return TableCellStyleAttribute(
      fontFamily: fontFamily != null ? fontFamily.value : this.fontFamily,
      fontCode: fontCode != null ? fontCode.value : this.fontCode,
      fontSize: fontSize ?? this.fontSize,
      textAlignHorizontal: textAlignHorizontal != null ? textAlignHorizontal.value : this.textAlignHorizontal,
      textAlignVertical: textAlignVertical != null ? textAlignVertical.value : this.textAlignVertical,
      fontStyle: fontStyle != null ? fontStyle.value : this.fontStyle,
      letterSpacing: letterSpacing ?? this.letterSpacing,
      lineSpacing: lineSpacing ?? this.lineSpacing,
      lineBreakMode:
          lineBreakMode != null ? lineBreakMode.value : this.lineBreakMode,
    );
  }

}
