import 'package:flutter/material.dart';
import 'package:netal_plugin/models/copy_wrapper.dart';
import 'package:netal_plugin/models/netal_enum.dart';

import 'package:niimbot_flutter_canvas/model/element/style_attr/color_style_attr.dart';
class LineStyleAttribute extends ColorStyleAttribute {
  final NetalLineType? lineType;
  final num lineWidth;

  const LineStyleAttribute({
    super.colorChannel,
    super.elementColor,
    this.lineType,
    required this.lineWidth,
  });

  @override
  LineStyleAttribute copyWith({
    final CopyWrapper<int?>? colorChannel,
    final CopyWrapper<NetalLineType?>? lineType,
    final num? lineWidth,
    final CopyWrapper<Color?>? elementColor,
  }) {
    return LineStyleAttribute(
      colorChannel:
          colorChannel != null ? colorChannel.value : this.colorChannel,
      lineType: lineType != null ? lineType.value : this.lineType,
      lineWidth: lineWidth ?? this.lineWidth,
      elementColor:
          elementColor != null ? elementColor.value : this.elementColor,
    );
  }
}
