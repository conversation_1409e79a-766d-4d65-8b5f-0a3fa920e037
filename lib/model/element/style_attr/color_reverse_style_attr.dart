import 'package:flutter/material.dart';
import 'package:netal_plugin/models/copy_wrapper.dart';

import 'package:niimbot_flutter_canvas/model/element/style_attr/color_style_attr.dart';

/// 颜色跟反白样式属性
@immutable
class ColorReverseStyleAttribute extends ColorStyleAttribute {
  final bool colorReverse;

  const ColorReverseStyleAttribute({
    super.colorChannel,
    super.elementColor,
    final bool? colorReverse,
  }) : colorReverse = colorReverse ?? false;

  @override
  ColorReverseStyleAttribute copyWith({
    final CopyWrapper<int?>? colorChannel,
    final bool? colorReverse,
    final CopyWrapper<Color?>? elementColor,
  }) {
    return ColorReverseStyleAttribute(
      colorChannel:
          colorChannel != null ? colorChannel.value : this.colorChannel,
      colorReverse: colorReverse ?? this.colorReverse,
      elementColor:
          elementColor != null ? elementColor.value : this.elementColor,
    );
  }
}
