import 'package:niimbot_flutter_canvas/business/desktop/models/canvas_element_text_content_choose_model.dart';
import 'package:niimbot_flutter_canvas/business/desktop/utils/canvas_widget_manager_utils.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_template/models/elements/element_enum.dart';

class DateElementHelperModel {
  final bool? dateIsRefresh;
  final String? contentTitle;
  final bool dateFormatOpen;
  final bool? timeFormatOpen;
  final String? dateFormat;
  final String? timeFormat;
  final bool use12Hours;
  final String? associateContentTitle;
  final String? dateElementPrefix;
  final String? dateAssociatePrefix;

  DateElementHelperModel({
    this.dateIsRefresh,
    this.contentTitle,
    required this.dateFormatOpen,
    this.timeFormatOpen,
    this.dateFormat,
    this.timeFormat,
    required this.use12Hours,
    this.associateContentTitle,
    this.dateElementPrefix,
    this.dateAssociatePrefix,
  });

  DateElementHelperModel.fromJson(final Map<String, dynamic> json)
      : dateIsRefresh = json["dateIsRefresh"],
        contentTitle = json["contentTitle"],
        dateFormatOpen = json["dateFormatOpen"] ?? true,
        timeFormatOpen = json["timeFormatOpen"],
        dateFormat = json["dateFormat"],
        timeFormat = json["timeFormat"],
        use12Hours = json["use12Hours"] ?? false,
        associateContentTitle = json['associateContentTitle'],
        dateElementPrefix = json['dateElementPrefix'],
        dateAssociatePrefix = json['dateAssociatePrefix'];

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data["dateIsRefresh"] = dateIsRefresh;
    data["contentTitle"] = contentTitle;
    data["dateFormatOpen"] = dateFormatOpen;
    data["timeFormatOpen"] = timeFormatOpen;
    data["dateFormat"] = dateFormat;
    data["timeFormat"] = timeFormat;
    data["use12Hours"] = use12Hours;
    data["associateContentTitle"] = associateContentTitle;
    data["dateElementPrefix"] = dateElementPrefix;
    data["dateAssociatePrefix"] = dateAssociatePrefix;
    return data;
  }

  DateElementHelperModel copyWith({
    final bool? dateIsRefresh,
    final String? contentTitle,
    final bool? dateFormatOpen,
    final bool? timeFormatOpen,
    final String? dateFormat,
    final String? timeFormat,
    final bool? use12Hours,
    final String? associateContentTitle,
    final String? dateElementPrefix,
    final String? dateAssociatePrefix,
  }) =>
      DateElementHelperModel(
        dateIsRefresh: dateIsRefresh ?? this.dateIsRefresh,
        contentTitle: contentTitle ?? this.contentTitle,
        dateFormatOpen: dateFormatOpen ?? this.dateFormatOpen,
        timeFormatOpen: timeFormatOpen ?? this.timeFormatOpen,
        dateFormat: dateFormat ?? this.dateFormat,
        timeFormat: timeFormat ?? this.timeFormat,
        use12Hours: use12Hours ?? this.use12Hours,
        associateContentTitle:
            associateContentTitle ?? this.associateContentTitle,
        dateElementPrefix: dateElementPrefix ?? this.dateElementPrefix,
        dateAssociatePrefix: dateAssociatePrefix ?? this.dateAssociatePrefix,
      );
}

class DateElementHelper {
  static DateElementHelperModel getDateElementDefault() {
    final dateElementDefault = CanvasWidgetManager().getDateElementDefault();
    DateElementHelperModel model =
        DateElementHelperModel.fromJson(dateElementDefault);
    if (model.dateFormatOpen == false && model.timeFormatOpen != true) {
      if (model.dateFormat != null) {
        model = model.copyWith(dateFormatOpen: true);
      }
      if (model.timeFormat != null) {
        model = model.copyWith(timeFormatOpen: true);
      }
    }
    return model;
  }

  ///添加时间元素获取日期格式
  ///没有手动设置过，按照app语言返回（简中繁中返回yyyy年MM月dd日，日韩返回yyyyMMdd，其他语言返回MM-dd-yyyy）
  ///手动设置过，返回最近一次手动设置的日期格式
  static ElementDateFormat getDateFormat() {
    // return ElementDateFormat.YMD_C;
    DateElementHelperModel dateElementDefault = getDateElementDefault();
    final recentDateFormat = dateElementDefault.dateFormat;
    if (recentDateFormat != null && recentDateFormat.isNotEmpty) {
      final val = ElementDateFormat.byValue(recentDateFormat);
      if (val != null) {
        if ([ElementDateFormat.YMDE_C, ElementDateFormat.MDYE_S]
            .contains(val)) {
          final formatList = CanvasElementTextContentChooseModel.dateFormatList(
              NiimbotIntl.getCurrentLocale().languageCode);
          if (!formatList.any((final e) => e.value == val)) {
            return val == ElementDateFormat.YMDE_C
                ? ElementDateFormat.MDYE_S
                : ElementDateFormat.YMDE_C;
          }
        }
        return val;
      }
    }

    // String languageCode = CanvasUserCenter().languageCode;
    String languageCode = NiimbotIntl.getCurrentLocale().languageCode;
    if (languageCode == "zh" ||
        languageCode == "zh-cn" ||
        languageCode == "zh-cn-t") {
      return ElementDateFormat.YMD_C;
    } else if (languageCode == 'en') {
      return ElementDateFormat.MDY_S;
    } else if (languageCode == "ja") {
      return ElementDateFormat.YMD_S;
    } else if (languageCode == "ko") {
      return ElementDateFormat.YMD_DOT;
    } else if (languageCode == "ru" ||
        languageCode == "de" ||
        languageCode == "th") {
      return ElementDateFormat.DMY_DOT;
    } else {
      return ElementDateFormat.DMY_LC;
    }
  }

  ///添加时间元素获取时间格式
  ///没有手动设置过，返回空
  ///手动设置过，返回最近一次手动设置的时间格式
  static ElementTimeFormat getTimeFormat() {
    DateElementHelperModel dateElementDefault = getDateElementDefault();
    final recentTimeFormat = dateElementDefault.timeFormat;
    if (recentTimeFormat != null && recentTimeFormat.isNotEmpty) {
      final val = ElementTimeFormat.byValue(recentTimeFormat);
      if (val != null) {
        return val;
      }
    }
    return ElementTimeFormat.HMS;
  }
}
