import 'package:flutter/cupertino.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:netal_plugin/utils/ratio.dart';
import 'package:niimbot_flutter_canvas/extensions/base_element.dart';
import 'package:niimbot_flutter_canvas/extensions/rect.dart';
import 'package:niimbot_flutter_canvas/extensions/table_element.dart';
import 'package:niimbot_flutter_canvas/hooks/use_canvas_state.dart';
import 'package:niimbot_flutter_canvas/model/element/style_attr/table_cell_style_attr.dart';
import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_flutter_canvas/provider/utils/table_cell_attr_utils.dart';
import 'package:niimbot_flutter_canvas/utils/table_element_utils.dart';
import 'package:niimbot_template/extensions/template_data.dart';
import 'package:niimbot_template/models/elements/table_cell_element.dart';
import 'package:niimbot_template/models/elements/table_combine_cell_element.dart';
import 'package:niimbot_template/models/elements/table_element.dart';
import 'package:provider/provider.dart';

/// 当前表格选中单元格
List<TableCombineCellElement> useSelectedCells(final TableElement table) {
  final ids = useSelectedIds(table.id);
  return table.getSelectedCells(ids);
}

///当前表格选中单元格样式默认值
TableCellStyleAttribute? useSelectedCellStyleAttr(final TableElement table) {
  final selectedCells = useSelectedCells(table);
  return TableCellAttributeUtils.buildSelectedStyleAttribute(
      (selectedCells.isNotEmpty ? selectedCells : table.allCells));
}

/// 当前表格选中单元格范围
Rect? useFocusedCellsRect(final TableElement table) {
  final ids = useSelectedIds(table.id);
  return table.getFocusedCellsRect(ids);
}

/// 当前表格是否可以合并单元格
bool useCanMergeCell(final TableElement? table) {
  if (table == null) return false;
  final ids = useSelectedIds(table.id);
  return ids.length > 1;
}

/// 当前表格是否可以拆分单元格
bool useCanUnMergeCell(final TableElement? table) {
  if (table == null) return false;
  final cells = useSelectedCells(table);
  return cells.length == 1 && cells.first is! TableCellElement;
}

/// 当前表格是否为一整行
bool useIsFullRow(final TableElement table, [final int? index]) {
  final ids = useSelectedIds(table.id);
  final fullRow = table.getSelectedFullRow(ids);
  if (fullRow == null) return false;
  if (index == null) return (fullRow.last - fullRow.first != table.row - 1);
  return fullRow.first <= index && fullRow.last >= index;
}

/// 当前表格是否为一整列
bool useIsFullColumn(final TableElement table, [final int? index]) {
  final ids = useSelectedIds(table.id);
  final fullColumn = table.getSelectedFullColumn(ids);
  if (fullColumn == null) return false;
  if (index == null)
    return (fullColumn.last - fullColumn.first != table.column - 1);
  return fullColumn.first <= index && fullColumn.last >= index;
}

/// 当前表格视图矩形和旋转角度
(Rect, double) useTableEditBoxRect(final TableElement table) {
  final context = useContext();
  final (rotate, center) = context.select<CanvasStore, (double, Offset)>(
      (final v) => (v.rotate, v.canvasData.center));
  final afterRotate = ((360 - rotate) + table.rotate) % 360;
  final focusedCellsRect = useFocusedCellsRect(table);
  final rect = table.getViewRect(rotate, center);
  final cellRect = Rect.fromLTWH(
      focusedCellsRect?.left ?? 0,
      focusedCellsRect?.top ?? 0,
      (focusedCellsRect?.width ?? rect.width),
      (focusedCellsRect?.height ?? rect.height));
  return (
    TableElementUtils.buildCellViewRect(table, rotate, center, cellRect)
        .rotate(afterRotate)
        .mm2px(),
    afterRotate
  );
}
