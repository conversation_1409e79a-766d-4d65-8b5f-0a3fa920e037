import 'dart:ui';

import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:netal_plugin/netal_image_result.dart';
import 'package:niimbot_flutter_canvas/extensions/base_element.dart';
import 'package:niimbot_flutter_canvas/extensions/rect.dart';
import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_template/extensions/template_data.dart';
import 'package:niimbot_template/models/elements/base_element.dart';
import 'package:niimbot_template/models/elements/date_element.dart';
import 'package:niimbot_template/models/values/element_value.dart';
import 'package:provider/provider.dart';

/// 响应选中信息
Set<String> useSelectedIds(final String id) {
  final context = useContext();
  return context
      .select<CanvasStore, Set<String>>((final v) => v.getSelected(id));
}

/// 响应是否选中
bool useIsSelected(final String id, {final String? associateId}) {
  final context = useContext();
  return context.select<CanvasStore, bool>((final v) =>
      v.getIsSelected(id) ||
      (associateId != null ? v.getAssociateIsSelected(associateId) : false));
}

/// 响应元素图片
NetalImageResult? useElementImage(final String id) {
  final context = useContext();
  return context.select<CanvasStore, NetalImageResult?>(
      (final v) => v.getElementImage(id));
}

/// 响应元素矩形
(Rect, double) useElementRect(final BaseElement element) {
  final context = useContext();
  final (rotate, center) = context.select<CanvasStore, (double, Offset)>(
      (final v) => (v.rotate, v.canvasData.center));
  final rect = element.getViewRect(rotate, center);
  final afterRotate = ((360 - rotate) + element.rotate) % 360;
  return (rect.rotate(afterRotate), afterRotate);
}

/// 响应元素信息
(Rect, double, NetalImageResult?, bool, Color?, ElementValue?, bool)
    useElementInfo(final BaseElement element) {
  final context = useContext();
  final associateId = element is DateElement ? element.associateId : null;
  final (
    rotate,
    center,
    imageCache,
    isSelected,
    printColor,
    value,
    isClipping,
    // imageRect
  ) = context.select<
      CanvasStore,
      (
        double,
        Offset,
        NetalImageResult?,
        bool,
        Color?,
        ElementValue?,
        bool,
        // Rect?
      )>((final v) {
    final rotate = v.rotate, center = v.canvasData.center;
    return (
      rotate,
      center,
      v.getElementImage(element.id),
      v.getIsSelected(element.id) ||
          (associateId != null ? v.getAssociateIsSelected(associateId) : false),
      v.printColor,
      v.getElementValue(element.id),
      v.getIsClipping(element.id),
      // element is ImageElement ? element.getViewRect(rotate, center) : null
    );
  });
  // final rect = imageRect ?? element.getViewRect(rotate, center);
  final rect = element.getViewRect(rotate, center);
  final afterRotate = ((360 - rotate) + element.rotate) % 360;
  return (
    rect.rotate(afterRotate),
    afterRotate,
    imageCache,
    isSelected,
    printColor,
    value,
    isClipping,
  );
}
