import 'package:netal_plugin/models/netal_enum.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_template/models/elements/element_enum.dart';
import 'package:niimbot_template/models/elements/table_cell_element.dart';
import 'package:niimbot_template/models/elements/text_element.dart';

import 'package:niimbot_flutter_canvas/extensions/text_element_text_style_tip.dart';
import 'package:niimbot_flutter_canvas/model/element/text_element_style.dart';

extension TextElementExtension on TextElement {
  /// 是否支持文本框样式
  bool isSupportBoxStyle() {
    return (type == NetalElementType.date ||
            type == NetalElementType.text ||
            type == NetalElementType.serial) &&
        this is! TableCellElement;
  }

  /// 默认文本框样式
  static TextElementBoxStyle defaultBoxStyle() {
    return TextElementBoxStyle.autoWidth;
  }

  /// 默认文本样式
  static List<NetalTextStyle> defaultTextStyles() {
    return [NetalTextStyle.norm];
  }

  /// 可用文本样式
  static Map<NetalTextStyle, String> availableTextStyles() {
    return {
      NetalTextStyle.adaptive:
          NiimbotIntl.getIntlMessage("app100001223", "自适应"),
      NetalTextStyle.minimized:
          NiimbotIntl.getIntlMessage("app100001222", "填满后缩小"),
    };
  }

  /// 可用文本样式说明
  static List<TextElementTextStyleTip> availableTextStyleTips() {
    return [
      TextElementTextStyleTip(
          title: NiimbotIntl.getIntlMessage("app100001224", "缩小模式"),
          desc: NiimbotIntl.getIntlMessage(
              "app100001225", "开启文本适应文本框，以防止文字超出而影响排版样式"),
          image: "assets/lottie/text_style_minimized.json"),
      TextElementTextStyleTip(
          title: NiimbotIntl.getIntlMessage("app100001226", "自适应模式"),
          desc: NiimbotIntl.getIntlMessage("app100001227", "文字大小随文本框缩放"),
          image: "assets/lottie/text_style_adaptive.json"),
    ];
  }

  /// 处理TextElement横向拖动
  /// 垂直方向为竖向拖动
  /// 文本方向
  /// - 水平方向：模式3不变
  ///  - boxStyle=autoHeight
  ///  - textStyle=norm
  /// - 垂直方向
  ///  - boxStyle=autoHeight
  ///  - textStyle=norm
  /// - 弧形方向：拖动禁用
  ///
  void handleTextElementHorizontal() {
    var boxStyle = NetalTextBoxStyle.autoWidth;
    if (getTextMode() == TextTypesettingMode.Horizontal) {
      /// 横向, 模式3不变
      if (this.boxStyle != NetalTextBoxStyle.fixedWidthHeight) {
        boxStyle = NetalTextBoxStyle.autoHeight;
      } else {
        boxStyle = NetalTextBoxStyle.fixedWidthHeight;
      }
    } else if (getTextMode() == TextTypesettingMode.Vertical ||
        getTextMode() == TextTypesettingMode.Horizontal_90) {
      /// 垂直
      boxStyle = NetalTextBoxStyle.autoHeight;
    } else if (getTextMode() == TextTypesettingMode.Arc) {
      /// 弧形
      boxStyle = NetalTextBoxStyle.autoWidth;
    }
    updateBoxStyle(boxStyle);
  }

  /// 处理对角线拖动，只有水平方向支持对角线拖动
  /// 文本方向
  /// - 水平方向
  ///  - boxStyle=fixedWidthHeight
  ///  - textStyle=adaptive
  /// - 垂直方向
  ///  - boxStyle=auto-height
  ///  - textStyle=norm
  void handleTextElementDiagonal() {
    var boxStyle = NetalTextBoxStyle.autoWidth;
    if (getTextMode() == TextTypesettingMode.Horizontal) {
      /// 水平方向
      boxStyle = NetalTextBoxStyle.fixedWidthHeight;
      // if (this.textStyle.contains(TextElementTextStyle.minimized)) {
      //   /// 对角线拖动，缩小模式变更为自适应模式
      //   this.textStyle = [TextElementTextStyle.adaptive];
      // }
    } else if (getTextMode() == TextTypesettingMode.Vertical ||
        getTextMode() == TextTypesettingMode.Horizontal_90) {
      /// 垂直方向
      boxStyle = NetalTextBoxStyle.autoHeight;
    } else if (getTextMode() == TextTypesettingMode.Arc) {
      boxStyle = NetalTextBoxStyle.autoWidth;
    }
    updateBoxStyle(boxStyle);
  }

  /// 处理字号变动逻辑
  // void handleFontSizeChange() {
  //   if (boxStyle == NetalTextBoxStyle.fixedWidthHeight) {
  //     /// 修改字号，模式3切换为模式2
  //     /// 字号变更时，文本框宽度限制，高度向下延展，框的宽高是贴着文字的。拖动结束后，框得到新的宽高，仍然是模式3
  //     // this.boxStyle = NetalTextBoxStyle.autoHeight;
  //
  //     /// 标记字号变更
  //     /// 后续图像库返回updateBoxStyleWithInt时，在模式2下变更模式为模式3，且文本适应模式变为缩小
  //     fontSizeChanged = true;
  //   }
  //   // if (this.boxStyle == TextElementBoxStyle.fixedWidthHeight
  //   //     && this.textStyle != null
  //   //     && this.textStyle.length > 0
  //   //     && this.textStyle.first == TextElementTextStyle.adaptive) {
  //   //       /// 1. 文本模式在3时，文本适应模式为自适应时，点击字号，文本适应模式变为缩小。
  //   //       this.textStyle = [TextElementTextStyle.minimized];
  //   // }
  // }

  /// 文本方向切换，回退到上次的文本样式
  void handleTextElementTypeSettingMode(final int mode) {
    var lastBoxStyle = lastBoxStyles[mode] ?? NetalTextBoxStyle.autoWidth;

    /// 判断文本方向
    if (mode == 1) {
      /// 水平方向
      lastBoxStyle ??= NetalTextBoxStyle.autoWidth;
    } else if (mode == 2) {
      /// 垂直方向
      lastBoxStyle = NetalTextBoxStyle.autoHeight;
    } else if (mode == 3) {
      lastBoxStyle = NetalTextBoxStyle.autoWidth;
    }
    updateBoxStyle(lastBoxStyle);
  }

  /// 处理字符变动触发文本样式变动
  /// 横向模式一回车变成模式二  模式二回车还是模式二  模式三回车还是模式三
  void handleBoxStyleWhenValueChange(final String value) {
    if (value.isEmpty) return;
    if (!value.contains('\n')) return;
    var boxStyle = this.boxStyle;
    if (boxStyle == NetalTextBoxStyle.autoWidth) {
      boxStyle = NetalTextBoxStyle.autoHeight;
    }
    updateBoxStyle(boxStyle);
  }

  /// 更新文本框样式
  void updateBoxStyle(final NetalTextBoxStyle boxStyle) {
    var lastTextStyle = <NetalTextStyle>[];
    if (boxStyle == NetalTextBoxStyle.autoHeight) {
      lastTextStyle = [NetalTextStyle.norm];
    } else if (boxStyle == NetalTextBoxStyle.autoWidth) {
      lastTextStyle = [NetalTextStyle.norm];
    } else if (boxStyle == NetalTextBoxStyle.fixedWidthHeight) {
      if (textStyle.isEmpty ||
          textStyle.first == TextElementExtension.defaultTextStyles().first) {
        lastTextStyle = [TextElementExtension.availableTextStyles().keys.first];
      } else {
        lastTextStyle = textStyle;
      }
    }
    // this.lastBoxStyles ??= Map<int, NetalTextBoxStyle>();
    if (typesettingMode.value <= 1) {
      lastBoxStyles[0] = boxStyle;
      lastBoxStyles[1] = boxStyle;
    } else {
      lastBoxStyles[typesettingMode.value] = boxStyle;
    }
    // this.boxStyle = boxStyle;
    // this.textStyle = lastTextStyle;
    if (this.boxStyle != NetalTextBoxStyle.fixedWidthHeight) {
      // this.lastUnlessFixWidthHeightBoxStyle = this.boxStyle;
    }
  }

  /// 更新文本框样式
  void updateTextStyle(final NetalTextStyle textStyle) {
    // this.textStyle = [textStyle];
  }

  /// 根据int类型调整文本框样式
  // void updateBoxStyleWithInt(final int boxStyle) {
  //   NetalTextBoxStyle style = NetalTextBoxStyle.empty;
  //   if (boxStyle == 0) {
  //     style = NetalTextBoxStyle.autoWidth;
  //   } else if (boxStyle == 1) {
  //     if (fontSizeChanged) {
  //       style = NetalTextBoxStyle.fixedWidthHeight;
  //       // this.textStyle = [NetalTextStyle.minimized];
  //       fontSizeChanged = false;
  //       // if (canvasElement != null && canvasElement.imageCache != null) {
  //       // canvasElement.imageCache!.boxStyle = 2; TODO
  //       // }
  //     } else {
  //       style = NetalTextBoxStyle.autoHeight;
  //     }
  //   } else if (boxStyle == 2) {
  //     style = NetalTextBoxStyle.fixedWidthHeight;
  //   }
  //   updateBoxStyle(style);
  // }

  /// 更新文本字号，最大字号
  // updateFontSize(final double fontSize, final double fontSizeThreshold) {
  //   // this.fontSize = max(fontSize.digits(2), allFontSizeConfigList.first.mm);
  //   fixedWidthHeightMaxFontSize = max(fontSizeThreshold.digits(2).toDouble(),
  //       TemplateConstants.FONT_SIZE_LIST.first.mm);
  // }

  /// 文字延展
  /// 根据文本对齐方式确定文字延展方向
  /// - 水平方向
  ///  - 左对齐：向右延展
  ///  - 居中对齐：向两边延展
  ///  - 右对齐：向左延展
  /// - 垂直方向
  ///   - 上对齐：向下延展
  ///   - 居中对齐：向上下延展
  ///   - 下对齐：向上延展
  /// - 弧形方向：无
  void handleTextElementPostion(final double newWidth, final double newHeight) {
    /*if (this.boxStyle == TextElementBoxStyle.autoHeight) {
      if (this.getTextMode() == TextMode.Horizontal) {
        /// 固定宽度，只修改高度
        newHeight = newHeight;
        newWidth = this.width.toDouble();
      } else if (this.getTextMode() == TextMode.Vertical ||
          this.getTextMode() == TextMode.Horizontal_90) {
        /// 固定高度，只修改宽度
        newWidth = newWidth;
        newHeight = newHeight;
      } else {
        newWidth = newWidth;
        newHeight = newHeight;
      }
    } else if (this.boxStyle == TextElementBoxStyle.autoWidth) {
      /// 固定高度，只修改宽度
      newWidth = newWidth;
      newHeight = newHeight;
    } else if (this.boxStyle == TextElementBoxStyle.fixedWidthHeight) {
      /// 宽高固定，不修改
      newWidth = this.width.toDouble();
      newHeight = this.height.toDouble();
    } else {
      newWidth = newWidth;
      newHeight = newHeight;
    }

    final offset = Offset(newWidth.toDouble(), newHeight.toDouble()) -
        Offset(this.width.toDouble(), this.height.toDouble());
    // 文本旋转状态下宽高变化后校正 x,y
    double differenceHeight = newHeight - this.height;
    double differenceWidth = newWidth - this.width;

    Offset center = Offset(this.x.toDouble(), this.y.toDouble()) +
        Offset(this.width.toDouble() / 2, this.height.toDouble() / 2);
    if (this.getTextMode() == TextMode.Horizontal) {
      /// 水平方向
      if (this.textAlignHorizontal.value == 0) {
        /// 左对齐, minX不变
        if (this.rotate == 0) {
          this.x = this.x;
        } else if (this.rotate == 90) {
          this.x = this.x - offset.dx / 2.0 - offset.dy / 2.0;
          this.y = this.y + offset.dx / 2.0 - offset.dy / 2.0;
        } else if (this.rotate == 180) {
          final maxX = this.x + this.width;
          final newX = maxX - newWidth;
          this.x = newX;
          this.y = this.y - offset.dy;
        } else if (this.rotate == 270) {
          this.x = this.x - offset.dx / 2.0 + offset.dy / 2.0;
          this.y = this.y - offset.dx / 2.0 - offset.dy / 2.0;
        }
      } else if (this.textAlignHorizontal.value == 1) {
        /// 居中对齐, midX不变
        if (this.rotate == 0) {
          if (this.boxStyle == TextElementBoxStyle.autoWidth) {
            final midX = this.x + this.width / 2.0;
            final newX = midX - newWidth / 2.0;
            this.x = newX;
          } else {
            this.x = x;
            this.y = y;
          }
        } else if (this.rotate == 90) {
          if (this.boxStyle == TextElementBoxStyle.autoWidth) {
            final midX = this.x + this.width / 2.0;
            final newX = midX - newWidth / 2.0;
            this.x = newX - offset.dy / 2.0;
            this.y = this.y - offset.dy / 2.0;
          } else {
            Offset offsetCenter =
                center + Offset(-differenceHeight, differenceWidth) / 2;
            this.y = (offsetCenter - Offset(newWidth, newHeight) / 2).dy;
            this.x = (offsetCenter - Offset(newWidth, newHeight) / 2).dx;
          }
        } else if (this.rotate == 180) {
          if (this.boxStyle == TextElementBoxStyle.autoWidth) {
            final midX = this.x + this.width / 2.0;
            final newX = midX - newWidth / 2.0;
            this.x = newX;
            this.y = this.y - offset.dy;
          } else {
            this.y -= (newHeight - this.height);
            this.x -= (newWidth - this.width);
          }
        } else if (this.rotate == 270) {
          if (this.boxStyle == TextElementBoxStyle.autoWidth) {
            final midX = this.x + this.width / 2.0;
            final newX = midX - newWidth / 2.0;
            this.x = newX + offset.dy / 2.0;
            this.y = this.y - offset.dy / 2.0;
          } else {
            Offset offsetCenter =
                center + Offset(differenceHeight, -differenceWidth) / 2;
            this.y = (offsetCenter - Offset(newWidth, newHeight) / 2).dy;
            this.x = (offsetCenter - Offset(newWidth, newHeight) / 2).dx;
          }
        }
      } else if (this.textAlignHorizontal.value == 2) {
        /// 右对齐. maxX不变
        if (this.rotate == 0) {
          if (this.boxStyle == TextElementBoxStyle.autoWidth) {
            final maxX = this.x + this.width;
            final newX = maxX - newWidth;
            this.x = newX;
          } else {
            this.x = x;
            this.y = y;
          }
        } else if (this.rotate == 90) {
          if (this.boxStyle == TextElementBoxStyle.autoWidth) {
            this.x = this.x - offset.dx / 2.0 - offset.dy / 2.0;
            this.y = this.y - offset.dx / 2.0 - offset.dy / 2.0;
          } else {
            Offset offsetCenter =
                center + Offset(-differenceHeight, differenceWidth) / 2;
            this.y = (offsetCenter - Offset(newWidth, newHeight) / 2).dy;
            this.x = (offsetCenter - Offset(newWidth, newHeight) / 2).dx;
          }
        } else if (this.rotate == 180) {
          if (this.boxStyle == TextElementBoxStyle.autoWidth) {
            this.x = this.x;
            this.y = this.y - offset.dy;
          } else {
            this.y -= (newHeight - this.height);
            this.x -= (newWidth - this.width);
          }
        } else if (this.rotate == 270) {
          if (this.boxStyle == TextElementBoxStyle.autoWidth) {
            this.x = this.x - offset.dx / 2.0 + offset.dy / 2.0;
            this.y = this.y + offset.dx / 2.0 - offset.dy / 2.0;
          } else {
            Offset offsetCenter =
                center + Offset(differenceHeight, -differenceWidth) / 2;
            this.y = (offsetCenter - Offset(newWidth, newHeight) / 2).dy;
            this.x = (offsetCenter - Offset(newWidth, newHeight) / 2).dx;
          }
        }
      }
    } else if (this.getTextMode() == TextMode.Vertical ||
        this.getTextMode() == TextMode.Horizontal_90) {
      /// 垂直方向
      if (this.textAlignVertical.value == 0) {
        /// 上对齐,minY不变
        if (this.rotate == 0) {
          if (this.boxStyle == TextElementBoxStyle.autoWidth) {
            this.y = this.y;
            this.x = this.x - offset.dx;
          } else {
            /// maxX不变
            final maxX = this.x + width;
            this.x = maxX - newWidth;
            this.y = y;
          }
        } else if (this.rotate == 90) {
          if (this.boxStyle == TextElementBoxStyle.autoWidth) {
            this.y = this.y - offset.dy / 2.0 - offset.dx / 2.0;
            this.x = this.x - offset.dy / 2.0 - offset.dx / 2.0;
          } else {
            Offset offsetCenter =
                center + Offset(-differenceHeight, -differenceWidth) / 2;
            this.y = (offsetCenter - Offset(newWidth, newHeight) / 2).dy;
            this.x = (offsetCenter - Offset(newWidth, newHeight) / 2).dx;
          }
        } else if (this.rotate == 180) {
          if (this.boxStyle == TextElementBoxStyle.autoWidth) {
            this.y = this.y - offset.dy / 2.0;
            this.x = this.x - offset.dy / 2.0;
          } else {
            this.y -= (newHeight - this.height);
            // final maxX = this.x + this.width;
            this.x = this.x;
          }
        } else if (this.rotate == 270) {
          if (this.boxStyle == TextElementBoxStyle.autoWidth) {
            this.y = this.y + offset.dx / 2.0 - offset.dy / 2.0;
            this.x = this.x - offset.dx / 2.0 + offset.dy / 2.0;
          } else {
            Offset offsetCenter =
                center + Offset(differenceHeight, differenceWidth) / 2;
            this.y = (offsetCenter - Offset(newWidth, newHeight) / 2).dy;
            this.x = (offsetCenter - Offset(newWidth, newHeight) / 2).dx;
          }
        }
      } else if (this.textAlignVertical.value == 1) {
        /// 居中对齐, midY不变
        if (this.rotate == 0) {
          if (this.boxStyle == TextElementBoxStyle.autoWidth) {
            final midY = this.y + this.height / 2.0;
            final newY = midY - newHeight / 2.0;
            this.y = newY;
            this.x = this.x - offset.dx;
          } else {
            /// maxX不变
            final maxX = this.x + width;
            this.x = maxX - newWidth;
            this.y = y;
          }
        } else if (this.rotate == 90) {
          if (this.boxStyle == TextElementBoxStyle.autoWidth) {
            final midY = this.y + this.height / 2.0;
            final newY = midY - newHeight / 2.0;
            this.y = newY - offset.dy / 2.0 - offset.dx / 2.0;
            this.x = this.x - offset.dy / 2.0 - offset.dx / 2.0;
          } else {
            Offset offsetCenter =
                center + Offset(-differenceHeight, -differenceWidth) / 2;
            this.y = (offsetCenter - Offset(newWidth, newHeight) / 2).dy;
            this.x = (offsetCenter - Offset(newWidth, newHeight) / 2).dx;
          }
        } else if (this.rotate == 180) {
          if (this.boxStyle == TextElementBoxStyle.autoWidth) {
            final midY = this.y + this.height / 2.0;
            final newY = midY - newHeight / 2.0;
            this.y = newY - offset.dy / 2.0;
            this.x = this.x - offset.dy / 2.0;
          } else {
            this.y -= (newHeight - this.height);
            // this.x = this.x - offset.dy/2.0;
            this.x = this.x;
          }
        } else if (this.rotate == 270) {
          if (this.boxStyle == TextElementBoxStyle.autoWidth) {
            final midY = this.y + this.height / 2.0;
            final newY = midY - newHeight / 2.0;
            this.y = newY - offset.dy / 2.0 + offset.dx / 2.0;
            this.x = this.x - offset.dy / 2.0 - offset.dx / 2.0;
          } else {
            Offset offsetCenter =
                center + Offset(differenceHeight, differenceWidth) / 2;
            this.y = (offsetCenter - Offset(newWidth, newHeight) / 2).dy;
            this.x = (offsetCenter - Offset(newWidth, newHeight) / 2).dx;
          }
        }
      } else if (this.textAlignVertical.value == 2) {
        /// 下对齐
        if (this.rotate == 0) {
          if (this.boxStyle == TextElementBoxStyle.autoWidth) {
            final maxY = this.y + this.height;
            final newY = maxY - newHeight;
            this.y = newY;
            this.x = this.x - offset.dx;
          } else {
            /// maxX不变
            final maxX = this.x + width;
            this.x = maxX - newWidth;
            this.y = y;
          }
        } else if (this.rotate == 90) {
          if (this.boxStyle == TextElementBoxStyle.autoWidth) {
            final midY = this.y + this.height / 2.0;
            final newY = midY - newHeight / 2.0;
            this.y = newY - offset.dy / 2.0 - offset.dx / 2.0;
            this.x = this.x - offset.dy / 2.0 - offset.dx / 2.0;
          } else {
            Offset offsetCenter =
                center + Offset(-differenceHeight, -differenceWidth) / 2;
            this.y = (offsetCenter - Offset(newWidth, newHeight) / 2).dy;
            this.x = (offsetCenter - Offset(newWidth, newHeight) / 2).dx;
          }
        } else if (this.rotate == 180) {
          if (this.boxStyle == TextElementBoxStyle.autoWidth) {
            final midY = this.y + this.height / 2.0;
            final newY = midY - newHeight / 2.0;
            this.y = newY - offset.dy / 2.0;
            this.x = this.x - offset.dy / 2.0;
          } else {
            this.y -= (newHeight - this.height);
            // this.x = this.x - offset.dy/2.0;
            this.x = this.x;
          }
        } else if (this.rotate == 270) {
          if (this.boxStyle == TextElementBoxStyle.autoWidth) {
            final midY = this.y + this.height / 2.0;
            final newY = midY - newHeight / 2.0;
            this.y = newY - offset.dy / 2.0 + offset.dx / 2.0;
            this.x = this.x - offset.dy / 2.0 - offset.dx / 2.0;
          } else {
            Offset offsetCenter =
                center + Offset(differenceHeight, differenceWidth) / 2;
            this.y = (offsetCenter - Offset(newWidth, newHeight) / 2).dy;
            this.x = (offsetCenter - Offset(newWidth, newHeight) / 2).dx;
          }
        }
      }
    } else if (this.getTextMode() == TextMode.Arc) {
      /// 弧形
      if (this.rotate == 0) {
        this.x = this.x;
        this.y = this.y;
      } else if (this.rotate == 90) {
        this.x = this.x - offset.dx / 2.0 - offset.dy / 2.0;
        this.y = this.y + offset.dx / 2.0 - offset.dy / 2.0;
      } else if (this.rotate == 180) {
        final maxX = this.x + this.width;
        final newX = maxX - newWidth;
        this.x = newX;
        this.y = this.y - offset.dy;
      } else if (this.rotate == 270) {
        this.x = this.x - offset.dx / 2.0 + offset.dy / 2.0;
        this.y = this.y - offset.dx / 2.0 - offset.dy / 2.0;
      }
    }
    this.width = newWidth;
    this.height = newHeight;*/
  }

  TextTypesettingMode getTextMode() {
    if (typesettingMode.value == 2) {
      if (rotate == 270) {
        return TextTypesettingMode.Horizontal_90;
      } else {
        return TextTypesettingMode.Vertical;
      }
    }
    if (typesettingMode.value == 3) {
      return TextTypesettingMode.Arc;
    }
    return TextTypesettingMode.Horizontal;
  }
}
