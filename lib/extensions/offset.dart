import 'dart:math';
import 'dart:ui';

extension OffsetExtions on Offset {
  /// 以指定点为中心旋转指定角度
  /// [angle] 旋转角度 [0-360]
  /// [point] 指定点坐标
  Offset rotate(final Offset point, final double angle) {
    if (angle == 0) {
      return this;
    }
    final a = angle * pi / 180;
    final double x =
        (dx - point.dx) * cos(a) - (dy - point.dy) * sin(a) + point.dx;
    final double y =
        (dx - point.dx) * sin(a) + (dy - point.dy) * cos(a) + point.dy;
    return Offset(x, y);
  }
}
