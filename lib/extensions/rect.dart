import 'dart:ui';

import 'package:niimbot_flutter_canvas/extensions/offset.dart';

enum FixedPointType {
  topLeft,
  topRight,
  bottomLeft,
  bottomRight,
  center,
}

extension RectExtensions on Rect {
  /// 以指定点为中心旋转指定角度
  /// [angle] 旋转角度 [0-360]
  /// [point] 指定点坐标 默认自身中心点
  Rect rotate(final double angle, [final Offset? point]) {
    if (angle == 0) {
      return this;
    }
    final pos = point ?? center;
    return Rect.fromPoints(
        topLeft.rotate(pos, angle), bottomRight.rotate(pos, angle));
  }

  /// 改变矩形的大小
  Rect resize({
    final double? width,
    final double? height,
    final Size? size,
    final FixedPointType fixedPointType = FixedPointType.topLeft,
  }) {
    final _width = width ?? size?.width ?? this.width;
    final _height = width ?? size?.height ?? this.height;
    switch (fixedPointType) {
      case FixedPointType.topLeft:
        return Rect.fromLTWH(left, top, _width, _height);
      case FixedPointType.topRight:
        return Rect.fromLTRB(right - _width, top, right, top + _height);
      case FixedPointType.bottomLeft:
        return Rect.fromLTRB(left, bottom - _height, left + _width, bottom);
      case FixedPointType.bottomRight:
        return Rect.fromLTRB(right - _width, bottom - _height, right, bottom);
      case FixedPointType.center:
        return Rect.fromLTWH(
            left - _width / 2, top - _height / 2, _width, _height);
    }
  }

  /// 移动矩形到指定位置
  Rect translateTo(
      {final double? left, final double? top, final Offset? offset}) {
    return Rect.fromLTWH(left ?? offset?.dx ?? this.left,
        top ?? offset?.dy ?? this.top, width, height);
  }

  Rect operator *(final double value) {
    return Rect.fromLTWH(
        left * value, top * value, width * value, height * value);
  }
}
