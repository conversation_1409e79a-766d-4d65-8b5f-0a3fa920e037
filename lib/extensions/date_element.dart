import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart';
import 'package:niimbot_template/models/elements/date_element.dart';
import 'package:niimbot_template/models/elements/element_enum.dart';

extension DateElementExtension on DateElement {
  ElementDateFormat getSelectorDateFormat() {
    if (dateFormat?.value.isEmpty ?? true) {
      return DateElementHelper.getDateFormat();
    }
    return dateFormat!;
  }

  ElementTimeFormat getSelectorTimeFormat() {
    if (timeFormat?.value.isEmpty ?? true) {
      return DateElementHelper.getTimeFormat();
    }
    return timeFormat!;
  }

  int _buildValue() {
    final datetime = DateTime.fromMillisecondsSinceEpoch(time);
    switch (validityPeriodUnit) {
      case ElementDateAssociatedUnit.hour:
        return DateTime(
                datetime.year,
                datetime.month,
                datetime.day,
                datetime.hour + validityPeriod,
                datetime.minute,
                datetime.second,
                datetime.millisecond,
                datetime.microsecond)
            .millisecondsSinceEpoch;
      case ElementDateAssociatedUnit.day:
        return DateTime(
                datetime.year,
                datetime.month,
                datetime.day + validityPeriod,
                datetime.hour,
                datetime.minute,
                datetime.second,
                datetime.millisecond,
                datetime.microsecond)
            .millisecondsSinceEpoch;
      case ElementDateAssociatedUnit.month:
        return DateTime(
                datetime.year,
                datetime.month + validityPeriod,
                datetime.day,
                datetime.hour,
                datetime.minute,
                datetime.second,
                datetime.millisecond,
                datetime.microsecond)
            .millisecondsSinceEpoch;
      case ElementDateAssociatedUnit.year:
        return DateTime(
                datetime.year + validityPeriod,
                datetime.month,
                datetime.day,
                datetime.hour,
                datetime.minute,
                datetime.second,
                datetime.millisecond,
                datetime.microsecond)
            .millisecondsSinceEpoch;
      default:
        return time;
    }
  }

  DateElement get associate {
    return DateElement(
      id: associateId,
      // x: associateOffset?.dx ?? x,
      // y: associateOffset?.dy ?? y + 5,
      // width: associateSize?.width ?? width,
      // height: associateSize?.height ?? height,
      rotate: rotate,
      isLock: isLock,
      isOpenMirror: isOpenMirror,
      mirrorType: mirrorType,
      typesettingMode: typesettingMode,
      typesettingParam: typesettingParam,
      textAlignHorizontal: textAlignHorizontal,
      textAlignVertical: textAlignVertical,
      lineBreakMode: lineBreakMode,
      wordSpacing: wordSpacing,
      letterSpacing: letterSpacing,
      lineSpacing: lineSpacing,
      fontFamily: fontFamily,
      fontCode: fontCode,
      fontStyle: fontStyle,
      fontSize: fontSize,
      fontColor: fontColor,
      isTitle: isTitle,
      colorReverse: colorReverse,
      colorChannel: colorChannel,
      elementColor: elementColor,
      hasVipRes: hasVipRes,
      isEditing: isEditing,
      boxStyle: boxStyle,
      textStyle: textStyle,
      contentTitle: contentTitle,
      dateFormat: dateFormat,
      timeFormat: timeFormat,
      time: _buildValue(),
      dateIsRefresh: dateIsRefresh,
      timeUnit: timeUnit,
      // selected: selected,
      // imageCache: associateImageCache,
    );
  }
}
