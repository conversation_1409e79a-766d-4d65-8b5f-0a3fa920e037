import 'dart:ui';

import 'package:collection/collection.dart';
import 'package:niimbot_flutter_canvas/extensions/base_element.dart';
import 'package:niimbot_flutter_canvas/extensions/image_element.dart';
import 'package:niimbot_flutter_canvas/extensions/rect.dart';
import 'package:niimbot_template/models/elements/bar_code_element.dart';
import 'package:niimbot_template/models/elements/base_element.dart';
import 'package:niimbot_template/models/elements/image_element.dart';
import 'package:niimbot_template/models/elements/table_element.dart';
import 'package:niimbot_template/models/elements/text_element.dart';
import 'package:niimbot_template/models/template/file_data_source.dart';
import 'package:niimbot_template/models/template_data.dart';

extension TemplateDataExtension on TemplateData {
  Rect getView(final double rotate) {
    return Rect.fromLTWH(0, 0, width.toDouble(), height.toDouble())
        .rotate(rotate);
  }

  ///pdfMOdel
  FileDataSource? get pdfInfoModel => fileDataSources
      ?.firstWhereOrNull((final element) => element.type == 'pdf');

  int get currentPageValue {
    return (fileDataSources ?? []).fold(dataSourceBindInfo?.page ?? 1,
        (final int res, final element) {
      if (element.pageCount > res) {
        return element.pageCount;
      }
      return res;
    });
  }

  int get totalPageValue {
    return (fileDataSources ?? []).fold(dataSourceBindInfo?.total ?? 1,
        (final int res, final element) {
      if (element.pageImages.length > res) {
        return element.pageImages.length;
      }
      return res;
    });
  }

  (int?, int?) get backPageAndTotal {
    final dataSources = this.dataSources;

    if (dataSources?.isNotEmpty != true &&
        fileDataSources?.isNotEmpty != true) {
      return (null, null);
    }

    return (currentPageValue, totalPageValue);
  }

  bool get isHavePdf => pdfInfoModel != null;

  bool get isHaveDataSources {
    return dataSources?.isNotEmpty == true;
  }

  num calculatePreviewScale() {
    num templatePreScale = 0.5;
    if (width * height <= 10000) {
      return 1;
    }
    return templatePreScale;
  }

  /// 生成打印和预览的canvasData  isPreviewScale是否预览缩小尺寸
  // Future<TemplateData> printPreviewTemplate(final int? page,
  //     {final num templatePreScale = 1, final String? clippingElementId}) async {
  Future<TemplateData> printPreviewTemplate(final (int?, num?, String?) params) async {
    final (page, preScale, clippingElementId) = params;
    final templatePreScale = preScale ?? 1;
    if (pdfInfoModel == null &&
        !isHaveDataSources &&
        fileDataSources?.isEmpty == true) {
      return this;
    }
    bool isScale = templatePreScale != 1;
    List<BaseElement> afterList = [];
    for (var element in elements) {
      BaseElement? newElement = element.generateElement(
        this,
        page: page,
      );
      if (newElement is ImageElement) {
        newElement = newElement.id == clippingElementId
            ? newElement
            : await newElement.imageElementClipped(
                fileDataSources?.firstWhereOrNull((final val) =>
                    val.rule.any((final v) => v.elementId == element.id)));
      }
      if (newElement != null) {
        if (isScale) {
          newElement = newElement.copyWith(
            x: newElement.x * templatePreScale,
            y: newElement.y * templatePreScale,
            width: newElement.width * templatePreScale,
            height: newElement.height * templatePreScale,
          );
          if ((newElement is TextElement)) {
            newElement = newElement.copyWith(
              fontSize: newElement.fontSize * templatePreScale,
            );
          } else if (newElement is BarCodeElement) {
            newElement = newElement.copyWith(
              fontSize: newElement.fontSize * templatePreScale,
              // > 1.5
              //   ? newElement.fontSize * templatePreScale
              //   : 1.5,
            );
          } else if (newElement is TableElement) {
            final tempColumnWidth = newElement.columnWidth
                .map(
                    (final e) => (num.parse((e * templatePreScale).toString())))
                .toList();
            final tempRowHeight = newElement.rowHeight
                .map(
                    (final e) => (num.parse((e * templatePreScale).toString())))
                .toList();
            newElement = (newElement).copyWith(
              columnWidth: tempColumnWidth,
              rowHeight: tempRowHeight,
              cells: newElement.cells.map((final cell) {
                return cell.copyWith(
                  fontSize: cell.fontSize * templatePreScale,
                );
              }).toList(),
              combineCells: newElement.combineCells.map((final combineCell) {
                return combineCell.copyWith(
                  fontSize: combineCell.fontSize * templatePreScale,
                );
              }).toList(),
              lineWidth: newElement.lineWidth * templatePreScale,
            );
          }
        }
        afterList.add(newElement);
      }
    }
    return copyWith(
      elements: afterList,
      width: isScale ? width * templatePreScale : width,
      height: isScale ? height * templatePreScale : height,
    );
  }

  /// 获取裁剪前原生的canvasData
  TemplateData originCanvasData({final String? clippingElementId}) => copyWith(
        elements: elements.map((final e) {
          if (e is ImageElement && e.id != clippingElementId) {
            return e.generateOriginElement(fileDataSources);
          }
          return e;
        }).toList(),
      );
}
