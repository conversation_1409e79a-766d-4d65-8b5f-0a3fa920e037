import 'dart:math';

import 'package:collection/collection.dart';
import 'package:flutter/widgets.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:netal_plugin/utils/ratio.dart';
import 'package:niimbot_flutter_canvas/extensions/rect.dart';
import 'package:niimbot_flutter_canvas/extensions/template_data.dart';
import 'package:niimbot_template/models/elements/bar_code_element.dart';
import 'package:niimbot_template/models/elements/base_element.dart';
import 'package:niimbot_template/models/elements/bind_element.dart';
import 'package:niimbot_template/models/elements/graph_element.dart';
import 'package:niimbot_template/models/elements/image_element.dart';
import 'package:niimbot_template/models/elements/qr_code_element.dart';
import 'package:niimbot_template/models/elements/text_element.dart';
import 'package:niimbot_template/models/template/file_data_source.dart';
import 'package:niimbot_template/models/template_data.dart';
import 'package:niimbot_template/utils/element_utils.dart';
import 'package:niimbot_template/utils/template_data_source_utils.dart';

extension BaseElementExtension on BaseElement {
  double getItemDisplayMinWidth(final BuildContext context) {
    if (this is BarCodeElement) {
      switch ((this as BarCodeElement).codeType) {
        case NetalBarcodeType.EAN13:
          return 26.mm2px().toDouble();
        case NetalBarcodeType.EAN8:
          return 14.mm2px().toDouble();
        case NetalBarcodeType.UPC_A:
          return 22.mm2px().toDouble();
        case NetalBarcodeType.UPC_E:
          return 14.mm2px().toDouble();
        case NetalBarcodeType.ITF25:
          return 4.mm2px().toDouble();
        default:
          String contentValue = "";
          return contentValue.isEmpty
              ? 2.mm2px().toDouble()
              : min((contentValue.length * 2).mm2px(), 200.mm2px()).toDouble();
      }
    }
    if (this is QRCodeElement) {
      if ((this as QRCodeElement).codeType == NetalQRCodeType.PDF417) {
        ///pdf417码最小宽度12
        return 12.mm2px().toDouble();
      }
      return 6.mm2px().toDouble();
    }
    if (this is TextElement) {
      return max((this as TextElement).getMinTextWidth(), 1.mm2px().toDouble());
    }
    return 1.mm2px().toDouble();
  }

  double getItemDisplayMaxWidth() {
    if (this is BarCodeElement) {
      return 200.mm2px().toDouble();
    }
    if (this is QRCodeElement) {
      return 100.mm2px().toDouble();
    }
    return double.infinity.mm2px().toDouble();
  }

  double getItemDisplayMinHeight() {
    if (this is BarCodeElement) {
      double codePhysicalHeight = 3.0;
      double actualTextHeight = 0.0;
      if ((this as BarCodeElement).textPosition ==
          NetalBarcodeTextPosition.none) {
        actualTextHeight = 0.0;
      } else {
        actualTextHeight = (this as BarCodeElement).textHeight + 0.2;
      }
      double minHeight = codePhysicalHeight + actualTextHeight;
      return minHeight.mm2px().toDouble();
    }
    if (this is GraphElement || this is ImageElement) {
      return 1.mm2px().toDouble();
    }
    if (this is QRCodeElement) {
      if ((this as QRCodeElement).codeType == NetalQRCodeType.PDF417) {
        ///pdf417码生成的图像高度比较小，做下特殊处理
        return 0.mm2px().toDouble();
      }
      return 6.mm2px().toDouble();
    }
    if (this is TextElement) {
      return max(
          (this as TextElement).getMinTextHeight(), 1.mm2px().toDouble());
    }
    return 0.2.mm2px().toDouble();
  }

  double getItemDisplayMaxHeight() {
    if (this is BarCodeElement || this is QRCodeElement) {
      return 100.mm2px().toDouble();
    }

    return double.infinity.mm2px().toDouble();
  }

  BoxConstraints getBoxConstrains(final BuildContext context) {
    return BoxConstraints(
        minWidth: getItemDisplayMinWidth(context),
        maxWidth: getItemDisplayMaxWidth(),
        minHeight: getItemDisplayMinHeight(),
        maxHeight: getItemDisplayMaxHeight());
  }

  double get rotateRadian => rotate * pi / 180;

  /// 原始范围
  Rect get rect {
    return Rect.fromLTWH(offset.dx, offset.dy, size.width, size.height);
  }

  /// 视图范围
  /// [rotate] 画布旋转角度
  /// [center] 画布中心点
  Rect getViewRect(final double rotate, final Offset center) {
    /// 相对自身旋转之后的视图
    final afterRotateBySelfRect =
        rect.rotate(this.rotate.toDouble(), rect.center);

    /// 相对画布旋转之后的视图
    return afterRotateBySelfRect.rotate(-rotate, center);
  }

  Size get size => Size(width.toDouble(), height.toDouble());

  Offset get offset => Offset(x.toDouble(), y.toDouble());

  /// 是否需要切换宽高
  bool get needSwapSize => rotate % 180 != 0;

  /// 是否可以双击编辑文本
  bool get canDoubleClickToEditValue {
    return (type == NetalElementType.text ||
        type == NetalElementType.barcode ||
        type == NetalElementType.qrcode ||
        type == NetalElementType.table);
  }

  String get mirrorId {
    return id.split('').reversed.join();
  }

  bool isExcelModifyIdMatch(final String modifyId) {
    return id == modifyId;
  }

  /// 是否可调整
  bool get resizeEnable {
    if (this is TextElement) {
      return (this as TextElement).typesettingMode != NetalTypesettingMode.arc;
    }
    return true;
  }

  /// 是否等比
  bool get isEqualRatio {
    if (this is GraphElement) {
      /** 圆 */
      return (this as GraphElement).graphType == NetalGraphType.round;
    }
    if (this is ImageElement) {
      return !(this as ImageElement).allowFreeZoom;
    }
    if (this is QRCodeElement) {
      return true;
    }

    return false;
  }

  /// 获取两个元素不同的key
  List<String> diffKeys(final BaseElement element) {
    final List<String> keys = [];
    final json = toJson();
    final eJson = element.toJson();
    const de = DeepCollectionEquality();
    final jKeys = json.keys.length > eJson.length ? json.keys : eJson.keys;
    for (var key in jKeys) {
      if (!de.equals(json[key], eJson[key])) {
        keys.add(key);
      }
    }
    return keys;
  }

  bool get isDataBindElement {
    if ((this is TextElement) &&
        (this as TextElement).dataBind != null &&
        (this as TextElement).dataBind!.isNotEmpty) {
      return true;
    }
    if ((this is QRCodeElement) &&
        (this as QRCodeElement).dataBind != null &&
        (this as QRCodeElement).dataBind!.isNotEmpty) {
      return true;
    }
    if ((this is BarCodeElement) &&
        (this as BarCodeElement).dataBind != null &&
        (this as BarCodeElement).dataBind!.isNotEmpty) {
      return true;
    }
    return false;
  }

  /// 当有导入pdf或剪裁图片时 需要重新计算element，当返回为空时元素不渲染
  /// 该方法必须剪裁结束后调用
  /// 剪裁相关逻辑需要调整
  BaseElement? generateElement(
    final TemplateData canvasData, {
    final int? page,
  }) {
    final dataSourceBindInfo = canvasData.dataSourceBindInfo;

    final muImage = canvasData.fileDataSources;

    ///  当前页码 pdf和数据源都存在 时取最大值
    final pageValue = page ?? canvasData.currentPageValue;
    final dataSourceTotal = dataSourceBindInfo?.total ?? 1;

    ///取出所有的多图ids
    final imageElementIds = ((muImage ?? [])
            .map((final e) => e.rule.firstOrNull?.elementId)
            .toList())
        .whereType<String>()
        .toList();

    ///  当前是否图片元素
    if (this is ImageElement) {
      if (imageElementIds.contains(id)) {
        final FileDataSource? fileDataSource =
            (muImage ?? []).firstWhereOrNull((final e) {
          return e.rule.any((final element) => element.elementId == id);
        });
        if (fileDataSource != null &&
            (fileDataSource.type == 'image'
                ? fileDataSource.pageImages.length > 1
                : true)) {
          if (fileDataSource.pageImages.length >= pageValue) {
            final pmuImageIndex = pageValue - 1;
            String? localImageUrl = fileDataSource.pageImages[pmuImageIndex];
            return (this as ImageElement)
                .copyWith(localImageUrl: localImageUrl);
          } else {
            return null;
          }
        }
      }
      return this;
    } else {
      /// 是否元素绑定
      if (isDataBindElement) {
        if (pageValue <= dataSourceTotal) {
          if ((this is QRCodeElement) || (this is BarCodeElement)) {
            final elementValue = (this as BindElement).value;
            if (elementValue != null) {
              final value = TemplateDataSourceUtils.getElementBindValue(
                id,
                elementValue,
                (this as BindElement).dataBind,
                canvasData.dataSources,
                canvasData.dataSourceModifies,
                pageValue,
              );
              if (value.isEmpty) {
                return null;
              }
            }
          }
          return this;
        } else {
          return null;
        }
      } else {
        return this;
      }
    }
  }
}

extension BaseElementListExtension on List<BaseElement> {
  /// [includeMirror] 是否包含镜像元素
  Rect getRect({
    final bool includeMirror = true,
    required final double rotate,
    required final Offset center,
    required final Size templateSize,
  }) {
    Rect? rect;
    for (var element in this) {
      if (rect == null) {
        rect = element.getViewRect(rotate, center);
      } else {
        rect = rect.expandToInclude(element.getViewRect(rotate, center));
      }
      if (includeMirror && element.isOpenMirror) {
        /* 如果是镜像元素 */
        final mirrored = ElementUtils.buildMirrorElement(
          element: element,
          templateSize: templateSize,
        );
        rect = rect.expandToInclude(mirrored.getViewRect(rotate, center));
      }
    }
    return rect!;
  }

}
