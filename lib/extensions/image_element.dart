import 'dart:io';
import 'dart:math';

import 'package:collection/collection.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:image/image.dart' as img;
import 'package:netal_plugin/models/copy_wrapper.dart';
import 'package:pointycastle/digests/sha1.dart';
import 'package:netal_plugin/utils/ratio.dart';
import 'package:niimbot_flutter_canvas/extensions/offset.dart';
import 'package:niimbot_flutter_canvas/extensions/rect.dart';
import 'package:niimbot_template/models/elements/image_element.dart';
import 'package:niimbot_template/models/template/file_data_source.dart';
import 'package:path/path.dart';

extension ImageElementExtension on ImageElement {
  /// 构建文件Key
  String _buildFileKey(final Uint8List bytes) {
    final digest = SHA1Digest();
    digest.update(bytes, 0, bytes.length);
    final hash = Uint8List(digest.digestSize);
    digest.doFinal(hash, 0);
    return hash
        .map((final byte) => byte.toRadixString(16).padLeft(2, '0'))
        .join();
  }

  Future<String> saveCroppedImage(final Uint8List croppedBytes) async {
    final runPath = dirname(Platform.resolvedExecutable);
    final directory = Directory(join(runPath, 'temp', 'cropImage'));
    final fileName = '${_buildFileKey(croppedBytes)}.png';
    final outputFile = File(join(directory.path, fileName));
    // 确保目录存在
    if (!await directory.exists()) {
      await directory.create(recursive: true);
    }

    // 原子性写入：先写入临时文件，再重命名为目标文件
    if (await outputFile.exists()) {
      return outputFile.path;
    }

    // 使用临时文件写入
    // final tempFile = File('${outputFile.path}.tmp');
    // await tempFile.writeAsBytes(croppedBytes, flush: true);
    await outputFile.writeAsBytes(croppedBytes, flush: true);

    // 重命名为最终文件名
    // await tempFile.rename(outputFile.path);

    return outputFile.path;
  }

  Future<String?> cropImage(
      final String path, final Rect rect, final Size size) async {
    final img.Image? originalImage = await img.decodeImageFile(path);
    if (originalImage == null) {
      throw Exception('Failed to load image');
    }
    final widthMax = size.width > size.height;

    /// 剪裁后渲染宽高
    final renderWidth = widthMax
        ? originalImage.height * size.width / size.height
        : originalImage.width;
    final renderHeight = widthMax
        ? originalImage.height
        : originalImage.width * size.height / size.width;
    final offsetTop = (renderHeight - originalImage.height) / 2;
    final offsetLeft = (renderWidth - originalImage.width) / 2;

    final rectLeft = rect.left * renderWidth - offsetLeft;
    final rectTop = rect.top * renderHeight - offsetTop;
    final rectRight = rect.right * renderWidth - offsetLeft;
    final rectBottom = rect.bottom * renderHeight - offsetTop;
    final Rect cropRect = Rect.fromLTRB(
        rectLeft < 0 ? 0 : rectLeft,
        rectTop < 0 ? 0 : rectTop,
        rectRight < 0 ? 0 : rectRight,
        rectBottom < 0 ? 0 : rectBottom
    );
    final x = cropRect.left.toInt(),
        y = cropRect.top.toInt(),
        width = cropRect.width.toInt(),
        height = cropRect.height.toInt();

    final img.Image croppedImage = img.copyCrop(
      originalImage,
      x: x,
      y: y,
      width: width,
      height: height,
    );

    final Uint8List croppedBytes = img.encodePng(croppedImage);
    return await saveCroppedImage(croppedBytes);
  }

  // 根据当前坐标和旋转获取旋转后偏移位置
  Offset _getPositionRotateOffset(
      final double angle, final Offset position, final Offset offset) {
    Offset rotateOffset = offset;
    if (angle == 90) {
      rotateOffset = Offset(-offset.dy, offset.dx);
    } else if (angle == 180) {
      rotateOffset = Offset(-offset.dx, -offset.dy);
    } else if (angle == 270) {
      rotateOffset = Offset(offset.dy, -offset.dx);
    }
    return position + rotateOffset;
  }

  // 根据两个对角坐标获取元素实际大小和位置
  Rect _getToOffsetRect(final Offset topLeft, final Offset bottomRight) {
    final left = min(topLeft.dx, bottomRight.dx);
    final top = min(topLeft.dy, bottomRight.dy);
    final right = max(topLeft.dx, bottomRight.dx);
    final bottom = max(topLeft.dy, bottomRight.dy);
    return Rect.fromLTRB(
      left,
      top,
      right,
      bottom,
    );
  }

  /// 将生成剪裁图片赋值给元素用于渲染
  /// 有剪裁和旋转元素坐标需要重新计算
  Future<ImageElement> imageElementClipped(
      final FileDataSource? fileDataSource) async {
    final chippingRect = fileDataSource?.crop;
    final isClipped = chippingRect != null;
    if (isClipped) {
      final angle = (rotate % 360).toDouble();
      final Rect originRect = rect;
      final Offset originCenter = originRect.center;
      final double offsetLeft =
          (originRect.width * chippingRect.left).digits(6).toDouble();
      final double offsetTop =
          (originRect.height * chippingRect.top).digits(6).toDouble();
      final double clippedWidth =
          (originRect.width * chippingRect.width).digits(6).toDouble();
      final double clippedHeight =
          (originRect.height * chippingRect.height).digits(6).toDouble();
      final Offset originRotateTopLeft = _getPositionRotateOffset(
          angle,
          originRect.topLeft.rotate(originCenter, angle),
          Offset(offsetLeft, offsetTop));
      final Offset originRotateBottomRight = _getPositionRotateOffset(
          angle, originRotateTopLeft, Offset(clippedWidth, clippedHeight));
      final clippedRect =
          _getToOffsetRect(originRotateTopLeft, originRotateBottomRight)
              .rotate(360 - angle);

      return copyWith(
        x: clippedRect.topLeft.dx.digits(6),
        y: clippedRect.topLeft.dy.digits(6),
        width: clippedRect.width.digits(6),
        height: clippedRect.height.digits(6),
        imageUrl: CopyWrapper.value(null),
        localImageUrl:
            await cropImage(localImageUrl, chippingRect, originRect.size),
      );
    }
    return this;
  }

  // 剪裁后元素转化为剪裁前元素
  ImageElement generateOriginElement(
      final List<FileDataSource>? fileDataSources) {
    final fileDataSource = fileDataSources?.firstWhereOrNull(
        (final element) => element.rule.any((final v) => v.elementId == id));
    final chippingRect = fileDataSource?.crop;
    if (chippingRect == null) return this;
    final angle = (rotate % 360).toDouble();
    final Rect clippedRect = rect;
    final Offset clippedCenter = clippedRect.center;
    final double originWidth =
        (width / chippingRect.width).digits(6).toDouble();
    final double originHeight =
        (height / chippingRect.height).digits(6).toDouble();
    final double offsetLeft =
        (originWidth * chippingRect.left).digits(6).toDouble();
    final double offsetTop =
        (originHeight * chippingRect.top).digits(6).toDouble();
    final Offset originRotateTopLeft = _getPositionRotateOffset(
        angle,
        clippedRect.topLeft.rotate(clippedCenter, angle),
        -Offset(offsetLeft, offsetTop));
    final Offset originRotateBottomRight = _getPositionRotateOffset(
        angle, originRotateTopLeft, Offset(originWidth, originHeight));
    final originRect =
        _getToOffsetRect(originRotateTopLeft, originRotateBottomRight)
            .rotate(360 - angle);
    return copyWith(
      x: originRect.topLeft.dx.digits(6),
      y: originRect.topLeft.dy.digits(6),
      width: originRect.width.digits(6),
      height: originRect.height.digits(6),
      imageUrl: CopyWrapper.value(null),
      localImageUrl: fileDataSource?.originImage,
    );
  }
}
