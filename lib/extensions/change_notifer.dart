import 'package:collection/collection.dart';
import 'package:flutter/material.dart';

extension ChangeNotifierExtension on ChangeNotifier {
  /// 监听数据的一次变化
  /// [selector] 数据筛选器
  /// [callback] 数据发送变化时回调
  /// [first] 是否回调首次数据 默认true
  once<T extends ChangeNotifier, R>(
      final R Function(T value) selector, final Function(R oldVal, R newVal) callback,
      {final first = true}) {
    final selected = selector(this as T);
    listener() {
      final newVal = selector(this as T);
      final same = const DeepCollectionEquality().equals(newVal, selected);
      if (!same) {
        callback(selected, newVal);
        removeListener(listener);
      }
    }

    if (first) callback(selected, selected);
    addListener(listener);
  }

  /// 监听数据的变化
  /// [selector] 数据筛选器
  /// [callback] 数据发送变化时回调
  /// [first] 是否回调首次数据 默认true
  /// 返回取消监听函数
  void Function() watch<T extends ChangeNotifier, R>(
      final R Function(T value) selector, final Function(R oldVal, R newVal) callback,
      {final first = true}) {
    var selected = selector(this as T);
    listener() {
      final newVal = selector(this as T);
      final same = const DeepCollectionEquality().equals(newVal, selected);
      if (!same) {
        callback(selected, newVal);
        selected = newVal;
      }
    }

    if (first) callback(selected, selected);
    addListener(listener);
    return () => removeListener(listener);
  }
}
