import 'dart:ui';

import 'package:niimbot_template/models/elements/table_combine_cell_element.dart';
import 'package:niimbot_template/models/elements/table_element.dart';

import 'package:niimbot_flutter_canvas/model/element/style_attr/table_cell_style_attr.dart';
import 'package:niimbot_flutter_canvas/provider/utils/table_cell_attr_utils.dart';

extension TableElementExtension on TableElement {
  /// 返回当前选中的单元格和合并的单元格
  List<TableCombineCellElement> getSelectedCells(final Set<String> ids) {
    List<TableCombineCellElement> cells =
        combineCells.where((final e) => ids.contains(e.id)).toList();
    cells.addAll(this.cells.where((final e) => ids.contains(e.id)).toList());
    return cells;
  }

  /// 获取选中单元格样式默认值
  TableCellStyleAttribute? getCellStyleAttr(final Set<String> ids) =>
      TableCellAttributeUtils.buildSelectedStyleAttribute(
          (getSelectedCells(ids).isNotEmpty
              ? getSelectedCells(ids)
              : allCells));

  /// 获取单元格范围
  Rect getCellRect(final TableCombineCellElement cell) {
    final index = getCellIndex(cell);
    final width = getCellWidth(index.last);
    final height = getCellHeight(index.first);
    final x = getCellX(index.last);
    final y = getCellY(index.first);
    return Rect.fromLTWH(
        x.toDouble(), y.toDouble(), width.toDouble(), height.toDouble());
  }

  /// 获取选中单元格范围
  Rect? getFocusedCellsRect(final Set<String> ids) {
    final selected = getSelectedCells(ids);
    Rect? rect;
    for (var cell in selected) {
      final elementRect = getCellRect(cell);
      if (rect == null) {
        rect = elementRect;
      } else {
        rect = rect.expandToInclude(elementRect);
      }
    }
    return rect;
  }

  /// 获取选中单元格的索引
  List<List<int>>? getSelectedIndex(final Set<String> ids) {
    final selected = getSelectedCells(ids);
    final firstCell = selected.firstOrNull;
    if (firstCell != null) {
      /* 存在选中元素 */
      final index = getCellIndex(firstCell);
      for (int i = 1; i < selected.length; i++) {
        final cellIndex = getCellIndex(selected[i]);
        if (cellIndex.first.first < index.first.first) {
          index[0][0] = cellIndex.first.first;
        }
        if (cellIndex.first.last > index.first.last) {
          index[0][1] = cellIndex.first.last;
        }
        if (cellIndex.last.first < index.last.first) {
          index[1][0] = cellIndex.last.first;
        }
        if (cellIndex.last.last > index.last.last) {
          index[1][1] = cellIndex.last.last;
        }
      }
      return index;
    }
    return null;
  }

  ///选中行 顶部行 <0则表示未选中
  int getSelectedTopRow(final Set<String> ids) {
    return getSelectedIndex(ids)?.first.first ?? -1;
  }

  ///选中行 底部行 <0则表示未选中
  int getSelectedBottomRow(final Set<String> ids) {
    return getSelectedIndex(ids)?.first.last ?? -1;
  }

  /// 选中列 左边列 <0则表示未选中
  int getSelectedLeftColumn(final Set<String> ids) {
    return getSelectedIndex(ids)?.last.first ?? -1;
  }

  /// 选中列 右边 <0则表示未选中
  int getSelectedRightColumn(final Set<String> ids) {
    return getSelectedIndex(ids)?.last.last ?? -1;
  }

  /// 选中单元格是否包含内容
  bool getSelectedCellsHasContent(final Set<String> ids) {
    bool has = false;
    for (final cell in getSelectedCells(ids)) {
      if (cell.value.isNotEmpty) {
        has = true;
        break;
      }
    }
    return has;
  }

  /// 选中元素的首个元素 左上角元素
  TableCombineCellElement? getSelectedFirstCell(final Set<String> ids) {
    final selected = getSelectedCells(ids);
    TableCombineCellElement? first = selected.firstOrNull;
    if (first != null) {
      for (int i = 1; i < selected.length; i++) {
        final firstIndex = getCellIndex(first!);
        final cell = selected[i];
        final cellIndex = getCellIndex(cell);
        if (cellIndex.first.first < firstIndex.first.first ||
            cellIndex.last.first < firstIndex.last.first) {
          first = cell;
        }
      }
    }
    return first;
  }

  /// 选中整列  null则表示未选中
  List<int>? getSelectedFullColumn(final Set<String> ids) {
    final index = getSelectedIndex(ids);
    if (index != null) {
      if (index.first.first == 0 && index.first.last + 1 == row) {
        return index.last;
      }
    }
    return null;
  }

  /// 选中整行 <0则表示未选中
  List<int>? getSelectedFullRow(final Set<String> ids) {
    final index = getSelectedIndex(ids);
    if (index != null) {
      if (index.last.first == 0 && index.last.last + 1 == column) {
        return index.first;
      }
    }
    return null;
  }
}
