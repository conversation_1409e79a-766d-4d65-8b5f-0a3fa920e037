import 'package:flutter/cupertino.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:niimbot_flutter_canvas/extensions/base_element.dart';
import 'package:niimbot_flutter_canvas/extensions/image_element.dart';
import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_flutter_canvas/widgets/canvas/canvas_interface.dart';
import 'package:niimbot_flutter_canvas/widgets/canvas/widgets/element_item.dart';
import 'package:niimbot_flutter_canvas/widgets/components/stack2.dart';
import 'package:niimbot_template/models/elements/bar_code_element.dart';
import 'package:niimbot_template/models/elements/base_element.dart';
import 'package:niimbot_template/models/elements/bind_element.dart';
import 'package:niimbot_template/models/elements/image_element.dart';
import 'package:niimbot_template/models/elements/qr_code_element.dart';
import 'package:niimbot_template/models/elements/table_element.dart';
import 'package:niimbot_template/models/elements/text_element.dart';
import 'package:provider/provider.dart';

class ElementList extends HookWidget {
  final CanvasItemBuilder itemBuilder;
  final double canvasScale;
  final Size templateSize;
  final bool isSelection;

  const ElementList({
    super.key,
    required this.itemBuilder,
    required this.canvasScale,
    required this.templateSize,
    this.isSelection = false,
  });

  @override
  Widget build(final BuildContext context) {
    final elements = context
        .select<CanvasStore, List<BaseElement>>((final v) => v.canvasElements);

    void onElementDoubleTap() {
      final element = context.read<CanvasStore>().focusedElement;
      if (((element is TextElement) ||
              (element is QRCodeElement) ||
              (element is BarCodeElement)) &&
          ((element as BindElement).dataBind != null) &&
          (element).dataBind!.isNotEmpty) {
        return;
      }
      if (element != null && element.canDoubleClickToEditValue) {
        if (element is TableElement) {
          // if (element.focusedCell != null) {
          //   context.read<CanvasStore>().startEdit(element.focusedCell!.id);
          // }
        } else {
          context.read<CanvasStore>().startEdit(element.id);
        }
      }
    }

    List<Widget> generateCanvasElement<T extends BaseElement>(
        final List<T> list) {
      final List<Widget> items = [];
      for (var element in list) {
        items.add(CanvasElementItem(
          element: element,
          itemBuilder: itemBuilder,
          canvasScale: canvasScale,
          templateSize: templateSize,
          isSelection: isSelection,
          onDoubleTap: onElementDoubleTap,
        ));
        if (element.isOpenMirror) {
          items.add(CanvasElementItem(
            element: element,
            itemBuilder: itemBuilder,
            canvasScale: canvasScale,
            templateSize: templateSize,
            isMirror: true,
          ));
        }
      }
      return items.toList();
    }

    return Stack2(
      clipBehavior: Clip.none,
      children: generateCanvasElement(elements),
    );
  }
}
