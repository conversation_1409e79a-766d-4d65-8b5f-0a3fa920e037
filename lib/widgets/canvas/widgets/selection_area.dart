
import 'package:flutter/material.dart';

class CanvasSelectionArea extends StatelessWidget {
  final Rect rect;

  const CanvasSelectionArea({super.key, required this.rect});

  @override
  Widget build(final BuildContext context) {
    return Positioned(
      left: rect.left - 2,
      top: rect.top - 2,
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(color: Colors.blue, width: 2),
          color: Colors.blue.withAlpha(20),
        ),
        width: rect.width,
        height: rect.height,
      ),
    );
  }
}
