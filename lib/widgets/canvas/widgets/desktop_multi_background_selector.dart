import 'dart:math';

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:niimbot_ui/icons/icons.dart';
import 'package:niimbot_ui/styles/border_radius.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:provider/provider.dart';

import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_flutter_canvas/utils/canvas_image_util.dart';

/// 多背景选择
class DesktopCanvasMultiBackgroundSelector extends StatefulWidget {
  final String selected;
  final String localSelected;
  final Size canvasSize;
  final List<String> backgroundList;
  final List<String> localBackgroundList;

  const DesktopCanvasMultiBackgroundSelector({
    super.key,
    required this.selected,
    required this.localSelected,
    required this.canvasSize,
    required this.backgroundList,
    required this.localBackgroundList,
  });

  @override
  State<DesktopCanvasMultiBackgroundSelector> createState() =>
      _DesktopCanvasMultiBackgroundSelectorState();
}

class _DesktopCanvasMultiBackgroundSelectorState
    extends State<DesktopCanvasMultiBackgroundSelector> {
  bool _expandMore = false;

  @override
  Widget build(final BuildContext context) {
    final canvasRotate = context
        .select<CanvasStore, num>((final v) => v.canvasData.canvasRotate);

    Widget buildBackgroundImageMenu() {
      double ratio = canvasRotate == 90 || canvasRotate == 270
          ? widget.canvasSize.height / widget.canvasSize.width
          : widget.canvasSize.width / widget.canvasSize.height;
      double itemWidth = ratio > 1 ? 80 : 80 * ratio;
      return ClipRRect(
        borderRadius: BorderRadius.circular(8.0),
        child: Container(
            width: itemWidth,
            color: NiimbotTheme.of(context).colors.systemFillColorWhite,
            padding: const EdgeInsets.only(bottom: 5.0),
            child: Column(
              children: [
                GridView.count(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 5, vertical: 5),
                  crossAxisCount: 1,
                  crossAxisSpacing: 10,
                  mainAxisSpacing: 10,
                  childAspectRatio: ratio,
                  shrinkWrap: true,
                  children: _generateBackgroundItems(context, _expandMore),
                ),
                InkWell(
                  onTap: () {
                    setState(() {
                      _expandMore = !_expandMore;
                    });
                  },
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Transform.rotate(
                        angle: _expandMore ? pi : 0,
                        child: RotatedBox(
                            quarterTurns: 3,
                            child: NiimbotIcons.chevron(
                              size: 16,
                              color: NiimbotTheme.of(context)
                                  .colors
                                  .textFillColorTertiary,
                            )),
                      ),
                    ],
                  ),
                ),
              ],
            )),
      );
    }

    return Positioned.directional(
      end: 20,
      top: 44,
      textDirection: Directionality.of(context),
      child: buildBackgroundImageMenu(),
    );
  }

  List<Widget> _generateBackgroundItems(
      final BuildContext context, final bool expandMore) {
    List<Widget> result = [];

    if (expandMore) {
      widget.backgroundList.forEachIndexed((final index, final element) {
        result.add(
          InkWell(
            onTap: () {
              context.read<CanvasStore>().updateTemplateBackgroundImage(index);
            },
            child: Container(
              padding: const EdgeInsets.all(2.0),
              constraints: const BoxConstraints(maxWidth: 80, maxHeight: 80),
              decoration: BoxDecoration(
                  border: element == widget.selected
                      ? Border.all(
                          width: 1.0,
                          color: NiimbotTheme.of(context).colors.brandColor,
                        )
                      : null,
                  borderRadius: NiimbotRadius.middle),
              child: CanvasImageUtils.imageWidget(
                80,
                80,
                url: element,
                localPath: widget.localBackgroundList.length > index
                    ? widget.localBackgroundList[index]
                    : '',
              ),
            ),
          ),
        );
      });
    } else {
      result.add(
        Container(
          constraints: const BoxConstraints(maxWidth: 80, maxHeight: 80),
          child: CanvasImageUtils.imageWidget(
            80,
            80,
            url: widget.selected,
            localPath: widget.localSelected,
          ),
        ),
      );
    }
    return result;
  }
}
