import 'package:collection/collection.dart';
import 'package:custom_pop_up_menu/custom_pop_up_menu.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_flutter_canvas/utils/canvas_image_util.dart';
import 'package:niimbot_flutter_canvas/widgets/components/svg_icon.dart';

/// 多背景选择
class CanvasMultiBackgroundSelector extends StatelessWidget {
  final String selected;
  final String localSelected;
  final Size canvasSize;
  final List<String> backgroundList;
  final List<String> localBackgroundList;

  CanvasMultiBackgroundSelector({
    super.key,
    required this.selected,
    required this.localSelected,
    required this.canvasSize,
    required this.backgroundList,
    required this.localBackgroundList,
  });

  final CustomPopupMenuController _popController = CustomPopupMenuController();

  @override
  Widget build(final BuildContext context) {
    final canvasRotate = context
        .select<CanvasStore, num>((final v) => v.canvasData.canvasRotate);

    Widget buildBackgroundImageMenu() {
      double ratio = canvasRotate == 90 || canvasRotate == 270
          ? canvasSize.height / canvasSize.width
          : canvasSize.width / canvasSize.height;
      double itemWidth = ratio > 1 ? 80 : 80 * ratio;
      return ClipRRect(
        borderRadius: BorderRadius.circular(24),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
          constraints: const BoxConstraints(maxWidth: 270, maxHeight: 270),
          width: itemWidth * 2 + 50,
          color: Colors.black54,
          child: GridView.count(
            padding: const EdgeInsets.symmetric(horizontal: 2, vertical: 2),
            crossAxisCount: 2,
            crossAxisSpacing: ratio > 1 ? 10 : 20,
            mainAxisSpacing: ratio > 1 ? 10 : 20,
            childAspectRatio: ratio,
            shrinkWrap: true,
            children: _generateBackgroundItems(context),
          ),
        ),
      );
    }

    return Positioned.directional(
        end: 20,
        top: 44,
        textDirection: Directionality.of(context),
        child: CustomPopupMenu(
          menuBuilder: buildBackgroundImageMenu,
          barrierColor: Colors.transparent,
          pressType: PressType.singleClick,
          showArrow: false,
          enablePassEvent: false,
          controller: _popController,
          verticalMargin: -34,
          child: Container(
            padding: const EdgeInsets.fromLTRB(5, 6, 5, 6),
            decoration: BoxDecoration(
                color: Colors.black12, borderRadius: BorderRadius.circular(6)),
            child: Center(
              child: Row(
                children: [
                  CanvasImageUtils.imageWidget(20, 20,
                      url: selected, localPath: localSelected),
                  const SizedBox(
                    width: 3,
                  ),
                  const SvgIcon(
                    'assets/common/template_bg_change.svg',
                    color: Colors.white,
                  ),
                ],
              ),
            ),
          ),
        ));
  }

  List<Widget> _generateBackgroundItems(final BuildContext context) {
    List<Widget> result = [];

    backgroundList.forEachIndexed((final index, final element) {
      result.add(GestureDetector(
          onTap: () {
            context.read<CanvasStore>().updateTemplateBackgroundImage(index);
            _popController.hideMenu();
          },
          child: Container(
            constraints: const BoxConstraints(maxWidth: 80, maxHeight: 80),
            child: CanvasImageUtils.imageWidget(
              80,
              80,
              url: element,
              localPath: localBackgroundList.length > index
                  ? localBackgroundList[index]
                  : '',
            ),
          )));
    });
    // }
    return result;
  }
}
