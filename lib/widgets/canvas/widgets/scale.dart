import 'dart:math';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/canvas_clipping_marker.dart';
import 'package:niimbot_flutter_canvas/utils/platform_utils.dart';
import 'package:niimbot_flutter_canvas/widgets/components/svg_icon.dart';
import 'package:niimbot_ui/icons/icons.dart';
import 'package:niimbot_ui/styles/theme.dart';

Logger _logger = Logger("CanvasScale", on: kDebugMode);

class CanvasScaleConstants {
  static const double MIN = 0.2;
  static const double MAX = 3;
}

/// 画布复位开关
class CanvasScale extends StatelessWidget {
  final num scale;

  final ValueChanged<num>? onChange;
  final VoidCallback? onReset;

  const CanvasScale(
      {super.key, required this.scale, this.onChange, this.onReset});

  @override
  Widget build(final BuildContext context) {
    if (PlatformUtils.isDesktop) {
      return _CanvasScaleDesktop(
        scale: scale,
        onChange: onChange,
        onReset: onReset,
      );
    }
    return _CanvasScaleMobile(scale: scale, onChange: onChange);
  }
}

class _CanvasScaleDesktop extends StatelessWidget {
  final num scale;

  final ValueChanged<num>? onChange;
  final VoidCallback? onReset;

  const _CanvasScaleDesktop({required this.scale, this.onChange, this.onReset});

  _onResetTap() {
    onReset?.call();
  }

  _onIncreaseTap() {
    num tempScale =
        (double.parse(((scale * 100 + 0.1 * 100).toStringAsFixed(1)))
                .roundToDouble() /
            100);
    onChange?.call(tempScale);
  }

  _onReduceTap() {
    num tempScale =
        (double.parse(((scale * 100 - 0.1 * 100).toStringAsFixed(1)))
                .roundToDouble() /
            100);
    onChange?.call(tempScale);
  }

  num addFixedDecimalNumbers(
      final int num1, final int num2, final int decimalPlaces) {
    num factor = pow(10, decimalPlaces);
    num result = ((num1 * factor) + (num2 * factor)) / factor;
    return result;
  }

  @override
  Widget build(final BuildContext context) {
    final themeColors = NiimbotTheme.of(context).colors;
    bool zoomOutEnabled =
        (scale * 100).toInt() > (CanvasScaleConstants.MIN * 100).toInt();
    bool zoomInEnabled =
        (scale * 100).toInt() < (CanvasScaleConstants.MAX * 100).toInt();

    return Positioned.directional(
      start: 40,
      bottom: 24,
      textDirection: Directionality.of(context),
      child: CanvasClippingMarker(
        child: Container(
          // width: 200,
          height: 32,
          padding: const EdgeInsets.symmetric(
            vertical: 4,
            horizontal: 12,
          ),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(64),
            color: themeColors.systemFillColorWhite,
          ),
          child: Row(
            children: [
              InkWell(
                mouseCursor: zoomOutEnabled
                    ? SystemMouseCursors.click
                    : SystemMouseCursors.noDrop,
                onTap: () {
                  if (zoomOutEnabled) _onReduceTap();
                },
                child: NiimbotIcons.zoomOut(
                  color: zoomOutEnabled
                      ? themeColors.textFillColorPrimary
                      : themeColors.textFillColorTertiary,
                  size: 24,
                ),
              ),
              const SizedBox(
                width: 5,
              ),
              InkWell(
                onTap: _onResetTap,
                child: Text('${(scale * 100).round()}%'),
              ),
              const SizedBox(
                width: 5,
              ),
              InkWell(
                mouseCursor: zoomInEnabled
                    ? SystemMouseCursors.click
                    : SystemMouseCursors.noDrop,
                onTap: () {
                  if (zoomInEnabled) _onIncreaseTap();
                },
                child: NiimbotIcons.zoomIn(
                  color: zoomInEnabled
                      ? themeColors.textFillColorPrimary
                      : themeColors.textFillColorTertiary,
                  size: 24,
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}

class _CanvasScaleMobile extends StatelessWidget {
  final num scale;

  final ValueChanged<num>? onChange;

  const _CanvasScaleMobile({required this.scale, this.onChange});

  _onResetTap() {
    onChange?.call(1);
  }

  @override
  Widget build(final BuildContext context) {
    return Positioned.directional(
        end: 16,
        bottom: 320.0 + 56,
        textDirection: Directionality.of(context),
        child: GestureDetector(
          onTap: _onResetTap,
          child: SvgIcon(
            scale > 1 ? 'assets/canvas_shrink.svg' : 'assets/canvas_expand.svg',
            width: 36,
            height: 36,
            useDefaultColor: false,
          ),
        ));
  }
}
