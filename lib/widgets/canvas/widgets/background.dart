import 'dart:math';

import 'package:flutter/material.dart';
import 'package:netal_plugin/utils/ratio.dart';
import 'package:niimbot_template/extensions/template_data.dart';

import 'package:provider/provider.dart';

import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_flutter_canvas/utils/canvas_image_util.dart';

/// 画板背景
class CanvasBackground extends StatelessWidget {
  final String selected;

  const CanvasBackground({
    super.key,
    required this.selected,
  });

  bool get isNotEmpty {
    return selected.isNotEmpty;
  }

  @override
  Widget build(final BuildContext context) {
    final size = context
        .select<CanvasStore, Size>((final v) => v.canvasData.size.mm2px());
    final rotate = context.select<CanvasStore, num>((final v) => v.rotate);
    return Positioned(
        top: 0,
        left: 0,
        child: Transform.rotate(
          angle: (360 - rotate) * pi / 180,
          child: Container(
            constraints: BoxConstraints(
              minWidth: size.width,
              minHeight: size.height,
            ),
            decoration: BoxDecoration(
                color: isNotEmpty ? null : Colors.white,
                borderRadius: BorderRadius.all(Radius.circular(10))),
            child: isNotEmpty
                ? FutureBuilder<String?>(
                    future: context.select<CanvasStore, Future<String?>>(
                        (v) => v.getLoadBackgroundImage()),
                    builder: (final _, final snapshot) {
                      return CanvasImageUtils.imageWidget(
                        size.width,
                        size.height,
                        // url: selected,
                        localPath: snapshot.data,
                        fit: BoxFit.fill,
                      );
                    })
                : null,
          ),
        ));
  }
}
