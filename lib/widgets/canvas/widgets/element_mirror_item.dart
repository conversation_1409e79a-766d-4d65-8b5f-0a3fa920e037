import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';

import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:netal_plugin/netal_image_result.dart';
import 'package:netal_plugin/utils/ratio.dart';
import 'package:niimbot_template/models/elements/base_element.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:niimbot_ui/widgets/context_menu/niimbot_context_menu.dart';
import 'package:provider/provider.dart';

import 'package:niimbot_flutter_canvas/extensions/base_element.dart';
import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_flutter_canvas/utils/theme_color.dart';
import 'package:niimbot_flutter_canvas/widgets/canvas/canvas_interface.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/context_menu/context_menu_utils.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/interactive/drag_update.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/interactive/interactive_element.dart';

final Logger _logger = Logger("CanvasElementItemMirror", on: kDebugMode);

class CanvasElementItemMirror extends HookWidget {
  final BaseElement element;
  final CanvasItemBuilder itemBuilder;
  final double canvasScale;
  final GestureTapDownCallback? onElementTap;
  final GestureTapCallback? onSecondaryTap;
  final void Function(Offset offset)? onDragging;
  final Offset offset;
  final NetalImageResult? imageCache;
  final bool isSelected;
  final double rotate;
  final Color? printColor;

  const CanvasElementItemMirror({
    super.key,
    required this.element,
    required this.itemBuilder,
    required this.canvasScale,
    this.onElementTap,
    this.onSecondaryTap,
    this.onDragging,
    required this.offset,
    this.imageCache,
    required this.isSelected,
    required this.rotate,
    this.printColor,
  });

  Rect get rect => Rect.fromLTWH(offset.dx, offset.dy,
      element.size.mm2px().width, element.size.mm2px().height);

  @override
  Widget build(final BuildContext context) {
    void onElementDragging(final DragUpdate update) {
      if (update.dragType == DragType.position) {
        onDragging?.call(update.position);
      }
    }

    return InteractiveElement(
      isSelected: isSelected,
      rotate: (rotate + 180) % 360,
      isLock: element.isLock,
      isDashedBorder: true,
      scale: canvasScale,
      onUpdate: (final update) => onElementDragging(update),
      rect: rect,
      onDragEnd: context.read<CanvasStore>().movedElements,
      equalRatio: element.isEqualRatio,
      constraints: element.getBoxConstrains(context),
      color: element.hasVipSource
          ? ThemeColor.COLOR_FEC96E
          : element.isLock
              ? NiimbotTheme.of(context).colors.borderColorEnter
              : ThemeColor.COLOR_00A7FF,
      isBindingElement: element.isBindingElement &&
          context.read<CanvasStore>().getIsFocus(element.id),
      onTapDown: onElementTap,
      child: NiimbotContextMenu(
        builder: (final TapUpDetails details) {
          return ContextMenuUtils.buildContextMenu(
              context,
              details.localPosition / canvasScale,
              element.type == NetalElementType.table
                  ? ContextMenuType.common
                  : ContextMenuType.element,
              element: element);
        },
        child: GestureDetector(
          onSecondaryTap: onSecondaryTap,
          child: itemBuilder(
            element: element,
            color: printColor,
            imageCache: imageCache,
          ),
        ),
      ),
    );
  }
}
