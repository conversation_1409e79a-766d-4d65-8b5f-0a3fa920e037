import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';

import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:netal_plugin/utils/ratio.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_template/models/elements/base_element.dart';
import 'package:niimbot_template/models/elements/date_element.dart';
import 'package:niimbot_template/models/elements/element_enum.dart';
import 'package:niimbot_template/models/elements/table_element.dart';
import 'package:niimbot_template/utils/element_utils.dart';
import 'package:niimbot_ui/hooks/use_mounted.dart';
import 'package:niimbot_ui/icons/icons.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:niimbot_ui/widgets/context_menu/niimbot_context_menu.dart';
import 'package:niimbot_ui/widgets/niimbot_toast/niimbot_toast.dart';
import 'package:niimbot_ui/widgets/niimbot_toast/niimbot_toast_controller.dart';
import 'package:provider/provider.dart';

import 'package:niimbot_flutter_canvas/extensions/base_element.dart';
import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_flutter_canvas/utils/buried_utils.dart';
import 'package:niimbot_flutter_canvas/utils/keyboard_utils.dart';
import 'package:niimbot_flutter_canvas/utils/theme_color.dart';
import 'package:niimbot_flutter_canvas/widgets/canvas/canvas_interface.dart';
import 'package:niimbot_flutter_canvas/widgets/canvas/canvas_utils.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/context_menu/context_menu_utils.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/interactive/drag_update.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/table/interactive_table_element.dart';
import 'package:niimbot_flutter_canvas/extensions/table_element.dart';
import 'package:niimbot_flutter_canvas/hooks/use_canvas_state.dart';
import 'package:niimbot_flutter_canvas/widgets/canvas/widgets/element_mirror_item.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/interactive/interactive_element.dart';

final Logger _logger = Logger("CanvasElementItem", on: kDebugMode);

class CanvasElementItem extends HookWidget {
  final BaseElement element;
  final CanvasItemBuilder itemBuilder;
  final double canvasScale;
  final GestureTapCallback? onDoubleTap;
  final GestureTapCallback? onFocus;
  final Size templateSize;

  /// 是否是镜像元素
  final bool isMirror;

  /// 是否拖拽选区
  final bool isSelection;

  const CanvasElementItem({
    super.key,
    required this.element,
    required this.itemBuilder,
    required this.canvasScale,
    this.isSelection = false,
    this.isMirror = false,
    this.onDoubleTap,
    this.onFocus,
    required this.templateSize,
  });

  /// 画板中心点
  Offset get templateCenter => templateSize.center(Offset.zero);

  @override
  Widget build(final BuildContext context) {
    final (rect, rotate, imageCache, isSelected, printColor, value, isClipping) =
        useElementInfo(element);
    _logger.log('build => ${rect.topLeft.mm2px()}');
    void onElementFocus() {
      if (context.read<CanvasStore>().clippingElementId != null) {
        context.read<CanvasStore>().clippedElement();
        return;
      }
      onFocus?.call();
      if (element is DateElement &&
          (element as DateElement).associateId.isNotEmpty) {
        context.read<CanvasStore>().focusElement(element.id,
            child: (element as DateElement).associateId);
      } else {
        context.read<CanvasStore>().focusElement(element.id);
      }

      context.read<CanvasStore>().cancelEdit();

      FocusScope.of(context).requestFocus();
    }

    void onElementDoubleTap() {
      onElementFocus();
      onDoubleTap?.call();
    }

    void onElementTapDown(final TapDownDetails details) {
      /* 其它元素处理方式 */
      if (KeyboardUtils.isControlPressed) {
        /* 多选 */
        if (element.isLock) {
          NiimbotToastController().show(
              type: ToastType.warning,
              NiimbotIntl.getIntlMessage('app100000868', '锁定元素不支持多选'),
              icon: NiimbotIcons.warning(
                color: NiimbotTheme.of(context).colors.warningColorNormal,
                size: 20.00,
              ));
        } else {
          final selectIds = context.read<CanvasStore>().selectedElementIds;
          final focused = context.read<CanvasStore>().focusedElement?.id;
          context.read<CanvasStore>().selectedElements([
            if (selectIds.length > 1) ...selectIds,
            if (focused != null) focused,
            element.id
          ]);
        }
      } else {
        if (element is TableElement) {
          /* 表格元素单独处理 */
          context.read<CanvasStore>().clearSelected();
          for (final e in [
            ...(element as TableElement).combineCells,
            ...(element as TableElement).cells
          ]) {
            final rect = (element as TableElement).getCellRect(e).mm2px();
            if (rect.contains(details.localPosition)) {
              // inCell = true;
              context.read<CanvasStore>().focusElement(element.id, child: e.id);
              // context.read<CanvasStore>().startEdit(e.id);

              FocusScope.of(context).requestFocus();
              break;
            }
          }
        } else {
          onElementFocus();
        }
      }
    }

    ///取消元素关联
    void onBreakBind() {
      context
          .read<CanvasStore>()
          .dataSourceController
          .upDateBreakBind(element.id);
    }

    void onElementUpdate(final DragUpdate update) {
      if (element.isLock) {
        //当锁定的元素 触发拖拽事件时 聚焦 显示小锁
        context.read<CanvasStore>().focusElement(element.id);
      } else {
        if (update.dragType == DragType.position) {
          context
              .read<CanvasStore>()
              .movingElements({element.id: update.position.px2mm()});
        }
        if (update.dragType == DragType.size) {
          if (element is TableElement) {
            context
                .read<CanvasStore>()
                .clearElementChildrenSelected(element.id);
          }
          context
              .read<CanvasStore>()
              .baseController
              .updateElementsSize(element.id, update.size.px2mm());
        }
      }
    }

    void onDragEnd() {
      context.read<CanvasStore>().movedElements();
    }

    void onUnlock() {
      BuriedUtils().track('click', '007_013_016',
          ext: {'source': !element.isLock ? 1 : 0, 'type': 2});
      context
          .read<CanvasStore>()
          .baseController
          .updateElementsLock(ids: [element.id]);
    }

    void onMirrorDragging(final Offset position) {
      var offset = Offset(
        -position.dx.px2mm() +
            templateCenter.dx * 2 -
            ((rotate == 90 || rotate == 270) ? rect.height : rect.width),
        -position.dy.px2mm() +
            templateCenter.dy * 2 -
            ((rotate == 90 || rotate == 270) ? rect.width : rect.height),
      );
      if (element.mirrorType == ElementMirrorType.canvasCenterY) {
        offset = Offset(
          position.dx.px2mm().toDouble(),
          -position.dy.px2mm() + templateCenter.dy * 2 - rect.height,
        );
      } else if (element.mirrorType == ElementMirrorType.canvasCenterX) {
        offset = Offset(
          -position.dx.px2mm() + templateCenter.dx * 2 - rect.width,
          position.dy.px2mm().toDouble(),
        );
      }
      context.read<CanvasStore>().movingElements({element.id: offset});
    }

    if (isMirror) {
      return CanvasElementItemMirror(
        key: ValueKey('${element.id}-mirror'),
        element: element,
        itemBuilder: itemBuilder,
        canvasScale: canvasScale,
        onElementTap: onElementTapDown,
        onSecondaryTap: onElementFocus,
        onDragging: onMirrorDragging,
        imageCache: imageCache,
        offset: ElementUtils.buildMirrorRect(
                rect: rect,
                templateSize: templateSize,
                mirrorType: element.mirrorType)
            .topLeft
            .mm2px(),
        isSelected: isSelected,
        rotate: rotate,
        printColor: printColor,
      );
    }
    useMounted((_) {
      if (isSelected) {
        FocusScope.of(context).requestFocus();
      }
    }, [isSelected]);
    if (element is TableElement) {
      return InteractiveTableElement(
        key: ValueKey(element.id),
        isSelection: isSelection,
        isSelected: isSelected,
        rotate: rotate,
        isLock: element.isLock,
        resizeEnable: context.read<CanvasStore>().getIsFocus(element.id) &&
            element.resizeEnable,
        scale: canvasScale,
        onUpdate: (final update) => onElementUpdate(update),
        rect: rect.mm2px(),
        onDragEnd: onDragEnd,
        equalRatio: element.isEqualRatio,
        constraints: element.getBoxConstrains(context),
        color: element.hasVipSource
            ? ThemeColor.COLOR_FEC96E
            : element.isLock
                ? NiimbotTheme.of(context).colors.borderColorEnter
                : ThemeColor.COLOR_00A7FF,
        isBindingElement: element.isBindingElement,
        onUnlock: onUnlock,
        resizeTypes: CanvasUtils.buildElementResizeType(element),
        table: element as TableElement,
        child: NiimbotContextMenu(
          builder: (final TapUpDetails details) {
            return ContextMenuUtils.buildContextMenu(context,
                details.localPosition / canvasScale, ContextMenuType.common,
                element: element);
          },
          child: GestureDetector(
            onTapDown: onElementTapDown,
            onSecondaryTap: onElementFocus,
            child: itemBuilder(
              element: element,
              color: printColor,
              imageCache: imageCache,
              updateElementNetalInfo:
                  context.read<CanvasStore>().updateElementNetalInfo,
            ),
          ),
        ),
      );
    }
    final isDateAssociate =
        element is DateElement && (element as DateElement).isAssociate;

    return InteractiveElement(
      key: ValueKey(element.id),
      isSelected: isSelected && !isClipping,
      isClipping: isClipping,
      isDashedBorder: isDateAssociate,
      rotate: rotate,
      isLock: element.isLock,
      resizeEnable: context.read<CanvasStore>().getIsFocus(element.id) &&
          (element.resizeEnable || element.isLock) && !isClipping,
      scale: canvasScale,
      onUpdate: (final update) => onElementUpdate(update),
      rect: rect.mm2px(),
      onDragEnd: onDragEnd,
      onBreakBind: onBreakBind,
      equalRatio: element.isEqualRatio,
      constraints: element.getBoxConstrains(context),
      color: element.hasVipSource
          ? ThemeColor.COLOR_FEC96E
          : element.isLock
              ? NiimbotTheme.of(context).colors.borderColorEnter
              : ThemeColor.COLOR_00A7FF,
      isBindingElement: element.isBindingElement &&
          context.read<CanvasStore>().getIsFocus(element.id),
      onUnlock: onUnlock,
      resizeTypes:
          isDateAssociate ? [] : CanvasUtils.buildElementResizeType(element),
      onTapDown: onElementTapDown,
      child: NiimbotContextMenu(
        builder: (final TapUpDetails details) {
          return ContextMenuUtils.buildContextMenu(context,
              details.localPosition / canvasScale, ContextMenuType.element,
              element: element);
        },
        child: GestureDetector(
          onTapDown: onElementTapDown,
          onSecondaryTap: onElementFocus,
          onDoubleTap: onElementDoubleTap,
          child: itemBuilder(
            element: element,
            color: printColor,
            imageCache: imageCache,
            updateElementNetalInfo:
                context.read<CanvasStore>().updateElementNetalInfo,
          ),
        ),
      ),
    );
  }
}
