import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:flutter_canvas_plugins_interface/utils/display_util.dart';

import 'package:netal_plugin/utils/ratio.dart';

import 'package:provider/provider.dart';

import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_flutter_canvas/utils/num_utils.dart';
import 'package:niimbot_flutter_canvas/utils/platform_utils.dart';
import 'package:niimbot_flutter_canvas/widgets/box_frame/desktop_canvas_ruler.dart';
import 'package:niimbot_flutter_canvas/widgets/box_frame/canvas_ruler.dart';
import 'package:niimbot_flutter_canvas/widgets/canvas/canvas_interface.dart';
import 'package:niimbot_flutter_canvas/widgets/canvas/canvas_utils.dart';
import 'package:niimbot_flutter_canvas/widgets/canvas/widgets/batch_widget.dart';
import 'package:niimbot_flutter_canvas/widgets/canvas/widgets/canvas_scrollview_desktop.dart';
import 'package:niimbot_flutter_canvas/widgets/canvas/widgets/core_view_box.dart';
import 'package:niimbot_flutter_canvas/widgets/canvas/widgets/desktop_multi_background_selector.dart';
import 'package:niimbot_flutter_canvas/widgets/canvas/widgets/edit_box.dart';
import 'package:niimbot_flutter_canvas/widgets/canvas/widgets/multi_backgroun_selector.dart';
import 'package:niimbot_flutter_canvas/widgets/canvas/widgets/scale.dart';
import 'package:niimbot_flutter_canvas/widgets/canvas/widgets/selection_area.dart';

final Logger _logger = Logger("CanvasCoreViewContainer", on: kDebugMode);

class CanvasCoreViewContainer extends StatefulWidget {
  final List<int>? batchInfo;
  final void Function() rfidBind;
  final List<Widget> children;
  final VoidCallback? onBatchPreviewTap;
  final CanvasItemBuilder itemBuilder;
  final List<String> backgroundList;
  final List<String> localBackgrounds;
  final int multipleBackIndex;
  final Size templateSize;
  final BoxConstraints constraints;
  final void Function(num val)? scaleOnChange;
  final bool showBatchPreview;
  final GlobalKey? canvasBgKey;

  const CanvasCoreViewContainer({
    super.key,
    this.batchInfo,
    this.onBatchPreviewTap,
    required this.rfidBind,
    this.children = const [],
    required this.itemBuilder,
    required this.backgroundList,
    required this.localBackgrounds,
    required this.multipleBackIndex,
    required this.templateSize,
    required this.constraints,
    this.scaleOnChange,
    required this.showBatchPreview,
    this.canvasBgKey,
  });

  @override
  createState() => _CanvasCoreViewContainerState();
}

class _CanvasCoreViewContainerState extends State<CanvasCoreViewContainer> {
  final ValueNotifier<Rect?> _selection = ValueNotifier(null);
  late TransformationController _transformationController;

  /// 选中背景图片
  String get backgroundSelected {
    if (widget.backgroundList.isEmpty) {
      return '';
    }
    return widget.backgroundList[widget.multipleBackIndex];
  }

  /// 选中本地背景图片
  String get localBackgroundSelected {
    return widget.localBackgrounds.length > widget.multipleBackIndex &&
            widget.multipleBackIndex >= 0
        ? widget.localBackgrounds[widget.multipleBackIndex]
        : "";
  }

  /// 画布缩放比例
  double get _canvasScale =>
      _transformationController.value.getMaxScaleOnAxis();

  /// 画布偏移量
  Offset get _canvasOffset => Offset(
      _transformationController.value.getTranslation().x,
      _transformationController.value.getTranslation().y);

  bool get showScale {
    if (PlatformUtils.isDesktop) {
      return true;
    }
    return _canvasScale != 1;
  }

  Size get _canvasSize => widget.templateSize.mm2px();

  double get _canvasScrollSize {
    return 500.mm2px() * _canvasScale;
  }

  double get _canvasScrollDyMultiple {
    if (_canvasOffset.dy.toInt() ==
        (widget.constraints.maxHeight - _canvasSize.height * _canvasScale) ~/
            2) {
      return 0.5;
    }
    return (1 - _canvasOffset.dy / _canvasScrollSize) / 2;
  }

  double get _canvasScrollDxMultiple {
    if (_canvasOffset.dx.toInt() ==
        (widget.constraints.maxWidth - _canvasSize.width * _canvasScale) ~/ 2) {
      return 0.5;
    }
    return (1 - _canvasOffset.dx / _canvasScrollSize) / 2;
  }

  @override
  void initState() {
    super.initState();
    if (PlatformUtils.isDesktop) {
      _transformationController = TransformationController(
          Matrix4.translationValues(
              (widget.constraints.maxWidth - _canvasSize.width) / 2,
              (widget.constraints.maxHeight - _canvasSize.height) / 2,
              0));
    } else {
      _transformationController = TransformationController(
          Matrix4.translationValues(
              DisplayUtil.marginBounds, DisplayUtil.designMarginBounds * 2, 0));
    }
    _transformationController.addListener(_transformationControllerListener);
    initScale();
  }

  @override
  void didUpdateWidget(final CanvasCoreViewContainer oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.templateSize != widget.templateSize ||
        oldWidget.constraints != widget.constraints) {
      _canvasToCenter();
    }
  }

  @override
  void dispose() {
    super.dispose();
    _transformationController.dispose();
    _transformationController.removeListener(_transformationControllerListener);
  }

  _transformationControllerListener() {
    setState(() {});
  }

  _onSelection(final Rect? rect) {
    _selection.value = rect;
  }

  void _onScaleChangeHandle(final num val) {
    if (widget.scaleOnChange != null) {
      widget.scaleOnChange!(val);
    }
  }

  initScale() {
    var viewCanvasWidth = widget.constraints.maxWidth;
    var viewCanvasHeight = widget.constraints.maxHeight;
    final canvasData = context.read<CanvasStore>().canvasData;
    var labelWidth = canvasData.width.mm2px();
    var labelHeight = canvasData.height.mm2px();
    var wRatio = viewCanvasWidth / labelWidth;
    var hRatio = viewCanvasHeight / labelHeight;
    var fitRatio = hRatio < wRatio ? hRatio : wRatio; // 得出容器的大小与图片大小的比例
    var setFitRatio = fitRatio - 0.1;
    if (fitRatio > 1) {
      setFitRatio = 1;
    } else if (setFitRatio < 0.2) {
      setFitRatio = 0.2;
    }
    _onScaleChange(NumberUtils.roundDownToOneDecimal(setFitRatio));
  }

  void _onScaleChange(final num val) {
    _onScaleChangeHandle(val);
    final pos = Offset(_canvasOffset.dx + _canvasSize.width / 2 * _canvasScale,
        _canvasOffset.dy + _canvasSize.height / 2 * _canvasScale);
    final matrix4 = Matrix4.identity();
    final focalPointScene = CanvasUtils.matrix4ToScene(matrix4, pos);
    matrix4.scale(val.toDouble());
    final focalPointSceneScaled = CanvasUtils.matrix4ToScene(matrix4, pos);
    final dx = focalPointSceneScaled.dx - focalPointScene.dx;
    final dy = focalPointSceneScaled.dy - focalPointScene.dy;
    matrix4.translate(pos.dx - _canvasSize.width / 2 + dx,
        pos.dy - _canvasSize.height / 2 + dy);
    _transformationController.value = matrix4;
    setState(() {});
  }

  void _onScaleReset() {
    _onScaleChangeHandle(1.0);
    _transformationController.value = (Matrix4.identity()
      ..translate((widget.constraints.maxWidth - _canvasSize.width) / 2,
          (widget.constraints.maxHeight - _canvasSize.height) / 2)
      ..scale(1.0));
    setState(() {});
  }

  void _canvasToCenter() {
    _transformationController.value = (Matrix4.identity()
      ..translate(
          (widget.constraints.maxWidth - _canvasSize.width * _canvasScale) / 2,
          (widget.constraints.maxHeight - _canvasSize.height * _canvasScale) /
              2)
      ..scale(_canvasScale));
    initScale();
    setState(() {});
  }

  void _onScrollViewDeskTop(final double multiple,
      {final bool modeVertical = true}) {
    double dx = _canvasOffset.dx, dy = _canvasOffset.dy;
    if (modeVertical) {
      dy = _canvasScrollSize * (1 - 2 * multiple);
    } else {
      dx = _canvasScrollSize * (1 - 2 * multiple);
    }
    _transformationController.value = (Matrix4.identity()
      ..translate(dx, dy)
      ..scale(_canvasScale));
    setState(() {});
  }

  void _onPointerSignal(final PointerSignalEvent event) {
    if (event is PointerScrollEvent) {
      if (event.scrollDelta.dy == 0.0) {
        return;
      }
      final scrollDy = -event.scrollDelta.dy;
      double dx = _canvasOffset.dx, dy = _canvasOffset.dy;
      dy += scrollDy;
      if (dy <= _canvasScrollSize && dy >= -_canvasScrollSize) {
        _transformationController.value = (Matrix4.identity()
          ..translate(dx, dy)
          ..scale(_canvasScale));
        setState(() {});
      }
    }
  }

  @override
  Widget build(final BuildContext context) {
    final isMoving = context.select<CanvasStore, bool>((final v) => v.isMoving);
    final editing = context.select<CanvasStore, bool>((final v) => v.editing);

    _logger.log("build, isMoving: $isMoving, editing: $editing");

    /// 避免拖拽画板跑出滚动范围
    if (_canvasScrollDyMultiple > 1) _onScrollViewDeskTop(1);
    if (_canvasScrollDyMultiple < 0) _onScrollViewDeskTop(0);
    if (_canvasScrollDxMultiple > 1) {
      _onScrollViewDeskTop(1, modeVertical: false);
    }
    if (_canvasScrollDxMultiple < 0) {
      _onScrollViewDeskTop(0, modeVertical: false);
    }

    return Stack(
      children: [
        Positioned.fill(
          child: CanvasCoreViewBox(
            itemBuilder: widget.itemBuilder,
            backgroundSelected: backgroundSelected,
            localBackgroundSelected: localBackgroundSelected,
            templateSize: widget.templateSize,
            canvasBgKey: widget.canvasBgKey,
            onSelection: _onSelection,
            transformationController: _transformationController,
            isMoving: isMoving,
            onPointerSignal: _onPointerSignal,
          ),
        ),
        if (PlatformUtils.isDesktop || isMoving) ...[
          /// 纵向标尺
          _VerticalRuler(
            canvasScale: _canvasScale,
            constraints: widget.constraints,
            canvasOffset: _canvasOffset,
          ),

          /// 横向标尺
          _HorizontalRuler(
            canvasScale: _canvasScale,
            constraints: widget.constraints,
            canvasOffset: _canvasOffset,
          )
        ],
        if (PlatformUtils.isDesktop) ...[
          CanvasScrollViewDeskTop(
            widgetLong: widget.constraints.maxHeight,
            scrollLong: _canvasScrollDyMultiple,
            onScroll: _onScrollViewDeskTop,
          ),
          CanvasScrollViewDeskTop(
            mode: CanvasScrollModelType.horizontal,
            widgetLong: widget.constraints.maxWidth,
            scrollLong: _canvasScrollDxMultiple,
            onScroll: (final e) => _onScrollViewDeskTop(e, modeVertical: false),
          ),
          ValueListenableBuilder(
              valueListenable: _selection,
              builder: (final BuildContext context, final Rect? rect,
                  final Widget? child) {
                if (rect != null) return CanvasSelectionArea(rect: rect);
                return const Positioned(child: SizedBox.shrink());
              }),
        ],

        if (PlatformUtils.isDesktop && editing)
          CanvasEditBox(
            start: _canvasOffset,
            canvasScale: _canvasScale,
          ),

        /// 批量数据分页
        if (widget.batchInfo != null)
          CanvasBatchWidget(
            info: widget.batchInfo!,
            showBatchPreview: widget.showBatchPreview,
            onBatchPreviewTap: widget.onBatchPreviewTap,
          ),

        /// 画板缩放操作
        if (showScale)
          CanvasScale(
            scale: _canvasScale,
            onChange: _onScaleChange,
            onReset: _onScaleReset,
          ),

        ///多背景切换入口
        if (widget.backgroundList.length > 1)
          PlatformUtils.isDesktop
              ? DesktopCanvasMultiBackgroundSelector(
                  canvasSize: _canvasSize,
                  selected: backgroundSelected,
                  localSelected: localBackgroundSelected,
                  backgroundList: widget.backgroundList,
                  localBackgroundList: widget.localBackgrounds,
                )
              : CanvasMultiBackgroundSelector(
                  canvasSize: _canvasSize,
                  selected: backgroundSelected,
                  localSelected: localBackgroundSelected,
                  backgroundList: widget.backgroundList,
                  localBackgroundList: widget.localBackgrounds,
                ),

        ...(widget.children)
      ],
    );
  }
}

/// 构建横向标尺
class _HorizontalRuler extends StatelessWidget {
  final Offset canvasOffset;
  final BoxConstraints constraints;
  final double canvasScale;

  const _HorizontalRuler({
    super.key,
    required this.canvasOffset,
    required this.constraints,
    required this.canvasScale,
  });

  @override
  Widget build(final BuildContext context) {
    Offset hRulerOffset = Offset(DisplayUtil.marginBounds, 0);
    return Positioned(
      left: 0,
      top: (!PlatformUtils.isDesktop ? 34 : 0),
      child: !PlatformUtils.isDesktop
          ? CanvasRuler(
              startOffset: hRulerOffset,
              canvasOffset: Offset(canvasOffset.dx, 0),
              scale: canvasScale,
              size: constraints.maxWidth,
            )
          : DesktopCanvasRuler(
              size: constraints.maxWidth,
              scale: canvasScale,
              offset: Offset(canvasOffset.dx, 0),
            ),
    );
  }
}

/// 构建纵向标尺
class _VerticalRuler extends StatelessWidget {
  final Offset canvasOffset;
  final BoxConstraints constraints;
  final double canvasScale;

  const _VerticalRuler({
    super.key,
    required this.canvasOffset,
    required this.constraints,
    required this.canvasScale,
  });

  @override
  Widget build(final BuildContext context) {
    Offset vRulerOffset = Offset(0, DisplayUtil.designMarginBounds * 2 - 34);
    return Positioned(
      left: 0,
      top: (!PlatformUtils.isDesktop ? 34 : 0),
      child: !PlatformUtils.isDesktop
          ? CanvasRuler(
              startOffset: vRulerOffset,
              canvasOffset: Offset(0, canvasOffset.dy),
              scale: canvasScale,
              axis: Axis.vertical,
              size: constraints.maxHeight,
            )
          : DesktopCanvasRuler(
              axis: Axis.vertical,
              size: constraints.maxHeight,
              scale: canvasScale,
              offset: Offset(0, canvasOffset.dy),
            ),
    );
  }
}
