import 'package:flutter/material.dart';

import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_ui/icons/icons.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:niimbot_ui/widgets/niimbot_direction.dart';

/// 批量预览
class CanvasBatchWidget extends StatelessWidget {
  final List<int> info;

  final void Function()? onBatchPreviewTap;
  final bool showBatchPreview;

  const CanvasBatchWidget({
    super.key,
    required this.info,
    required this.showBatchPreview,
    this.onBatchPreviewTap,
  });

  @override
  Widget build(final BuildContext context) {
    final niimbotTheme = NiimbotTheme.of(context);
    if (info.length != 2) {
      return Container();
    }
    String excelPageInfoFirst;
    String excelPageInfoLast;
    if (Directionality.of(context) == TextDirection.rtl) {
      excelPageInfoFirst = '${info.last}';
      excelPageInfoLast = '${info.first}';
    } else {
      excelPageInfoFirst = '${info.first}';
      excelPageInfoLast = '${info.last}';
    }
    if (!showBatchPreview) {
      return Container();
    }
    return Positioned.directional(
      end: 20,
      bottom: 24,
      textDirection: Directionality.of(context),
      child: Container(
        height: 32,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
        decoration: BoxDecoration(
          color: niimbotTheme.colors.systemFillColorWhite,
          borderRadius: BorderRadius.circular(28),
        ),
        child: InkWell(
          onTap: onBatchPreviewTap,
          child: Center(
            child: Row(
              children: [
                Text(
                  '${NiimbotIntl.getIntlMessage("app01102", "预览")}: ',
                  style: niimbotTheme.typography.body,
                ),
                Text(
                  excelPageInfoFirst,
                  style: niimbotTheme.typography.body
                      ?.copyWith(color: niimbotTheme.colors.brandColor),
                ),
                const SizedBox(width: 2),
                Text(
                  '/$excelPageInfoLast',
                  style: niimbotTheme.typography.body,
                ),
                const SizedBox(
                  width: 10,
                ),
                NiimbotDirection(
                  child: RotatedBox(
                      quarterTurns: 1, child: NiimbotIcons.chevron(size: 16)),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
