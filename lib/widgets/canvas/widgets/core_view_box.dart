import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:netal_plugin/utils/ratio.dart';
import 'package:niimbot_flutter_canvas/extensions/rect.dart';
import 'package:niimbot_flutter_canvas/extensions/table_element.dart';
import 'package:niimbot_flutter_canvas/utils/table_element_utils.dart';
import 'package:niimbot_flutter_canvas/widgets/canvas/auxiliary/auxiliary_lines_widget.dart';
import 'package:niimbot_flutter_canvas/widgets/canvas/printarea/print_area_widget.dart';
import 'package:niimbot_flutter_canvas/widgets/canvas/widgets/element_list.dart';
import 'package:niimbot_flutter_canvas/widgets/canvas/widgets/multi_select_rect.dart';
import 'package:niimbot_flutter_canvas/widgets/components/niimbot_interactive_viewer.dart';
import 'package:niimbot_flutter_canvas/widgets/components/stack2.dart';
import 'package:niimbot_template/extensions/template_data.dart';
import 'package:niimbot_template/models/elements/table_cell_element.dart';
import 'package:niimbot_template/models/elements/table_element.dart';
import 'package:niimbot_ui/widgets/context_menu/niimbot_context_menu.dart';
import 'package:provider/provider.dart';

import 'package:niimbot_flutter_canvas/extensions/base_element.dart';
import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_flutter_canvas/widgets/canvas/canvas_interface.dart';
import 'package:niimbot_flutter_canvas/widgets/canvas/widgets/background.dart';
import 'package:niimbot_flutter_canvas/widgets/canvas/widgets/scale.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/context_menu/context_menu_utils.dart';

final Logger _logger = Logger("CanvasCoreViewBox", on: kDebugMode);

/// 画布视图
class CanvasCoreViewBox extends HookWidget {
  final CanvasItemBuilder itemBuilder;
  final ValueChanged<Rect?>? onSelection;
  final String backgroundSelected;
  final String localBackgroundSelected;
  final Size templateSize;
  final TransformationController transformationController;
  final bool isMoving;
  final PointerSignalEventListener? onPointerSignal;
  final GlobalKey? canvasBgKey;

  const CanvasCoreViewBox({
    super.key,
    required this.itemBuilder,
    this.onSelection,
    required this.backgroundSelected,
    required this.localBackgroundSelected,
    required this.templateSize,
    required this.transformationController,
    required this.isMoving,
    this.onPointerSignal,
    this.canvasBgKey,
  });

  /// 画布缩放比例
  double get _canvasScale => transformationController.value.getMaxScaleOnAxis();

  /// 画布偏移量
  Offset get _canvasOffset => Offset(
      transformationController.value.getTranslation().x,
      transformationController.value.getTranslation().y);

  @override
  Widget build(final BuildContext context) {
    _logger.log("build");

    final showPrintArea =
        context.select<CanvasStore, bool>((final v) => v.showPrintArea);
    final multiSelect =
        context.select<CanvasStore, bool>((final v) => v.multiSelect);

    final isSelection = useState(false);

    void onSelection(final Rect? rect) {
      this.onSelection?.call(rect);
      isSelection.value = rect != null && !rect.size.isEmpty;
      if (rect != null) {
        /* 框选区域 */
        if (!rect.size.isEmpty) {
          context.read<CanvasStore>().clearSelected();
          final elements = context.read<CanvasStore>().canvasData.elements;
          final rotate = context.read<CanvasStore>().rotate;
          final canvasCenter = context.read<CanvasStore>().canvasData.center;
          List<String> ids = [];
          for (final e in elements) {
            final $rect = e.getViewRect(rotate, canvasCenter).mm2px();
            final eRect = Rect.fromLTWH(
                ($rect.left * _canvasScale + _canvasOffset.dx),
                ($rect.top * _canvasScale + _canvasOffset.dy),
                $rect.width * _canvasScale,
                $rect.height * _canvasScale);

            if (e is TableElement &&
                eRect.contains(rect.topLeft) &&
                eRect.contains(rect.bottomRight)) {
              /* 表格元素 */
              /* 选择框在元素内部 */

              ids.clear();
              Set<String> cells = {};

              for (final cell in [...e.combineCells, ...e.cells]) {
                if (cell is TableCellElement) {
                  if (cell.combineId?.isNotEmpty ?? false) {
                    continue;
                  }
                }
                final cellRect = (TableElementUtils.buildCellViewRect(
                                e, rotate, canvasCenter, e.getCellRect(cell))
                            .mm2px() *
                        _canvasScale)
                    .translate(_canvasOffset.dx, _canvasOffset.dy);
                if (rect.overlaps(cellRect)) {
                  cells.add(cell.id);
                }
              }
              context.read<CanvasStore>().selectedElementChildren(e.id, cells);
              break;
            } else {
              if (rect.overlaps(eRect)) {
                ids.add(e.id);
              }
            }
          }
          if (ids.isNotEmpty) {
            context.read<CanvasStore>().selectedElements(ids);
            return;
          }
        }
      }
    }

    return NiimbotContextMenu(
      builder: (final TapUpDetails details) {
        return ContextMenuUtils.buildContextMenu(
          context,
          Offset((details.localPosition.dx - _canvasOffset.dx),
                  (details.localPosition.dy - _canvasOffset.dy)) /
              _canvasScale,
          ContextMenuType.common,
        );
      },
      child: NiimbotInteractiveViewer(
        transformationController: transformationController,
        boundaryMargin: const EdgeInsets.all(double.infinity),
        minScale: CanvasScaleConstants.MIN,
        maxScale: CanvasScaleConstants.MAX,
        constrained: false,
        onSelection: onSelection,
        onPointerSignal: onPointerSignal,
        child: Stack2(
          fit: StackFit.loose,
          clipBehavior: Clip.none,
          children: [
            /// 画板背景
            CanvasBackground(
              key: canvasBgKey,
              selected: localBackgroundSelected,
            ),

            /// 构建可打印区域
            if (showPrintArea) PrintAreaWidget(templateSize: templateSize),
            ElementList(
              itemBuilder: itemBuilder,
              canvasScale: _canvasScale,
              templateSize: templateSize,
              isSelection: isSelection.value,
            ),

            /// 多选元素外框
            if (multiSelect) MultiSelectRect(scale: _canvasScale),

            /// 计算并构建辅助线
            if (isMoving) AuxiliaryLinesWidget(scale: _canvasScale),
          ],
        ),
      ),
    );
  }
}
