import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:netal_plugin/utils/ratio.dart';

import 'package:niimbot_flutter_canvas/extensions/base_element.dart';
import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_template/extensions/template_data.dart';
import 'package:niimbot_ui/widgets/context_menu/niimbot_context_menu.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/context_menu/context_menu_utils.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/interactive/draggable_point.dart';
import 'package:provider/provider.dart';

final Logger _logger = Logger("MultiSelectRect", on: kDebugMode);

class MultiSelectRect extends StatelessWidget {
  final num scale;

  const MultiSelectRect({
    super.key,
    required this.scale,
  });

  @override
  Widget build(final BuildContext context) {
    final rect = context
        .select<CanvasStore, Rect?>((final v) => v.getSelectRect()?.mm2px());
    onDrag(final Offset d) {
      final elements = context.read<CanvasStore>().selectedElement;
      final rotate = context.read<CanvasStore>().rotate;
      final canvasCenter = context.read<CanvasStore>().canvasData.center;
      final position = d / scale.toDouble();
      final update = {
        for (var e in elements)
          (e.id): e.getViewRect(rotate, canvasCenter).topLeft + position.px2mm()
      };
      context.read<CanvasStore>().movingElements(update);
    }

    if (rect == null) return const SizedBox.shrink();
    return Positioned(
      left: (rect.left) - 2,
      top: (rect.top) - 2,
      child: DraggablePoint(
        onDrag: onDrag,
        onDragEnd: context.read<CanvasStore>().movedElements,
        child: NiimbotContextMenu(
          builder: (final TapUpDetails details) {
            return ContextMenuUtils.buildContextMenu(
                context,
                details.localPosition / scale.toDouble(),
                ContextMenuType.element);
          },
          child: DottedBorder(
              dashPattern: const [4, 4],
              color: Colors.blue,
              strokeWidth: 2,
              child: SizedBox(
                width: rect.width,
                height: rect.height,
              )),
        ),
      ),
    );
  }
}
