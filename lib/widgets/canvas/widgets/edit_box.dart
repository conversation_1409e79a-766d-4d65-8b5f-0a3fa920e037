import 'dart:math';

import 'package:flutter/material.dart';

import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:netal_plugin/utils/ratio.dart';
import 'package:niimbot_flutter_canvas/extensions/table_element.dart';
import 'package:niimbot_flutter_canvas/hooks/use_table_element_state.dart';
import 'package:niimbot_flutter_canvas/hooks/use_widget.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_template/extensions/template_data.dart';
import 'package:niimbot_template/models/elements/base_element.dart';
import 'package:niimbot_template/models/elements/table_combine_cell_element.dart';
import 'package:niimbot_template/models/elements/table_element.dart';
import 'package:niimbot_template/models/elements/value_element.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:provider/provider.dart';

import 'package:niimbot_flutter_canvas/extensions/base_element.dart';
import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_flutter_canvas/provider/history/history_store.dart';
import 'package:niimbot_flutter_canvas/provider/models/table_cell_element_updater.dart';
import 'package:niimbot_flutter_canvas/provider/models/value_element_updater.dart';
import 'package:niimbot_flutter_canvas/utils/string_utils.dart';

class CanvasEditBox extends HookWidget {
  final Offset start;
  final num canvasScale;

  const CanvasEditBox({
    super.key,
    required this.start,
    required this.canvasScale,
  });

  @override
  Widget build(final BuildContext context) {
    final element = context
        .select<CanvasStore, BaseElement?>((final v) => v.focusedElement);
    final selectedCells =
        element is TableElement ? useSelectedCells(element) : [];

    if (element != null &&
        ((element.canDoubleClickToEditValue &&
                element.type != NetalElementType.table) ||
            (element.type == NetalElementType.table &&
                selectedCells.length == 1))) {
      String? value;
      final rotate = context.read<CanvasStore>().rotate;
      final canvasCenter = context.read<CanvasStore>().canvasData.center;
      final viewRect = element.getViewRect(rotate, canvasCenter).mm2px();
      if ((element is ValueElement)) {
        value = element.value;
      }

      onValueChange(final String val) {
        context.read<CanvasStore>().updateElementsValues(
            {element.id: ValueElementUpdater(value: val)},
            type: SnapshotType.ignore);
      }

      onBlurChange(final String val) {
        // context.read<CanvasStore>().updateElementsValues(
        //     {element.id: ValueElementUpdater(value: val)},
        //     type: SnapshotType.ignoreEnd);
        context.read<CanvasStore>().cancelEdit();
      }

      if (element.type != NetalElementType.table) {
        return Positioned(
          left: viewRect.left * canvasScale + start.dx,
          top: viewRect.bottom * canvasScale + start.dy + 10,
          child: CustomTextField(
            width: 270,
            minHeight: 72,
            autofocus: true,
            value: StringUtils.covertNewLineChar(value),
            onChanged: onValueChange,
            onBlur: () {
              onBlurChange(value ?? '');
            },
          ),
        );
      } else if (element.type == NetalElementType.table &&
          selectedCells.length == 1 &&
          element is TableElement) {
        return TableCellEditBox(
          canvasScale: canvasScale,
          start: start,
        );
      }
    }
    return const SizedBox.shrink();
  }
}

class TableCellEditBox extends HookWidget {
  final num canvasScale;
  final Offset start;
  final VoidCallback? onBlur;

  const TableCellEditBox({
    super.key,
    required this.canvasScale,
    required this.start,
    this.onBlur,
  });

  @override
  Widget build(final BuildContext context) {
    final focusNode = useFocusNode();
    final globalKey = useGlobalKey();
    final initWidget = useState(true);
    final minHeight = useState(0.0);
    final textController = useTextEditingController();
    final isInputFocused = useState(false); // 新增：跟踪输入框焦点状态
    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((final _) {
        focusNode.requestFocus();
      });
      return null;
    }, []);
    var element = context.read<CanvasStore>().focusedElement as TableElement;

    List<TableCombineCellElement> selectedCells = useSelectedCells(element);
    TableCombineCellElement? selectedCell =
        selectedCells.isNotEmpty ? selectedCells.first : null;
    final (rect, rotate) = useTableEditBoxRect(element);

    double width = rect.width * canvasScale + 2;

    initElement({final VoidCallback? success}) {
      if (context.read<CanvasStore>().focusedElement is TableElement) {
        element = context.read<CanvasStore>().focusedElement as TableElement;
        final ids = context.read<CanvasStore>().getSelected(element.id);
        selectedCells = element.getSelectedCells(ids);
        selectedCell = selectedCells.isNotEmpty ? selectedCells.first : null;
        success?.call();
      } else {
        context.read<CanvasStore>().cancelEdit();
        onBlur?.call();
      }
    }

    getTextFieldHeight() {
      final renderBox =
          globalKey.currentContext?.findRenderObject() as RenderBox?;
      minHeight.value = rect.height * canvasScale;
      if (renderBox != null) {
        double sizeHeight = renderBox.size.height;
        final ids = context.read<CanvasStore>().getSelected(element.id);
        final rowIndex = element.getSelectedTopRow(ids);
        if (sizeHeight > minHeight.value) {
          for (var i = rowIndex; i < element.row; i++) {
            double rowHeight =
                element.getCellHeight([rowIndex, i]).mm2px().toDouble() *
                    canvasScale;
            if (rowHeight >= sizeHeight) {
              minHeight.value = rowHeight;
              // setState(() {});
              return;
            }
          }
        }
      }
    }

    onValueChange(final String val, {final bool? focused}) {
      final focusedElement = context.read<CanvasStore>().focusedElement;
      if (focusedElement is TableElement && selectedCell != null) {
        context.read<CanvasStore>().tableController.updateTableCellElements(
            focusedElement.id, {
          selectedCell!.id:
              TableCellElementUpdater(value: val.replaceAll('\u200B', ''))
        });
      }
    }

    /// 初始化输入框内容
    setTextFieldInit(final String text, {final bool wordChar = false}) {
      String newText =
          StringUtils.toCharacterBreak(text, wordChar: wordChar) ?? '';
      textController.value = TextEditingValue(
        text: newText,
        selection: TextSelection.fromPosition(
          TextPosition(
            affinity: TextAffinity.downstream,
            offset: newText.length,
          ),
        ),
      );
      getTextFieldHeight();
    }

    textFieldOnChanged(final String val) {
      getTextFieldHeight();
      onValueChange(val);
    }

    if (initWidget.value) {
      initWidget.value = false;
      minHeight.value = rect.height * canvasScale;
      focusNode.addListener(() {
        if (focusNode.hasFocus) {
          isInputFocused.value = true; // 获取焦点时设置为true
          initElement(success: () {
            setTextFieldInit(
              StringUtils.covertNewLineChar(selectedCell?.value) ?? '',
              wordChar:
                  selectedCell?.lineBreakMode != NetalTextLineBreakMode.char,
            );
          });
        } else {
          isInputFocused.value = false; // 失去焦点时设置为false
          context.read<CanvasStore>().cancelEdit();
          onValueChange(textController.text, focused: false);
          onBlur?.call();
          FocusScope.of(context).requestFocus(focusNode.parent);
        }
      });
    }

    bool fontNotHarmony = selectedCell?.fontFamily != 'Harmony';

    return Positioned(
      left: rect.left * canvasScale + start.dx,
      top: rect.top * canvasScale + start.dy,
      child: Transform.rotate(
        angle: rotate * pi / 180,
        child: Container(
          width: width - 7,
          color: Colors.white,
          // color: const Color.fromRGBO(211, 234, 253, 1),
          constraints: BoxConstraints(
            minHeight: minHeight.value - 4,
          ),
          alignment: Alignment.centerLeft,
          child: TextField(
              key: globalKey,
              focusNode: focusNode,
              controller: textController,
              minLines: 1,
              maxLines: 10,
              keyboardType: TextInputType.multiline,
              textAlign: selectedCell?.textAlignHorizontal ==
                      NetalTextAlign.start
                  ? Directionality.of(context) == TextDirection.rtl
                      ? TextAlign.end
                      : TextAlign.start
                  : selectedCell?.textAlignHorizontal == NetalTextAlign.center
                      ? TextAlign.center
                      : Directionality.of(context) == TextDirection.rtl
                          ? TextAlign.start
                          : TextAlign.end,
              textAlignVertical: TextAlignVertical.center,
              onChanged: textFieldOnChanged,
              cursorWidth: 4.0,
              cursorColor:
                  NiimbotTheme.of(context).colors.systemFillColorSelected,
              // cursorHeight: selectedCell!.fontSize.mm2px().toDouble(),
              style: (fontNotHarmony
                      ? const TextStyle()
                      : NiimbotTheme.of(context).typography.body)
                  ?.copyWith(
                color: isInputFocused.value ? Colors.black : element.contentColor, // 输入时为黑色，失去焦点时为element.contentColor
                height: selectedCell != null
                    ? selectedCell!.lineSpacing / selectedCell!.fontSize + 1
                    : null,
                fontSize: selectedCell != null
                    ? selectedCell!.fontSize.mm2px().toDouble() * canvasScale
                    : null,
                fontFamily: fontNotHarmony ? selectedCell?.fontFamily : null,
                letterSpacing: selectedCell != null
                    ? selectedCell!.letterSpacing.mm2px().toDouble() *
                        canvasScale
                    : null,
                wordSpacing: selectedCell != null
                    ? selectedCell!.lineSpacing.mm2px().toDouble() * canvasScale
                    : null,
                fontWeight: selectedCell != null &&
                        selectedCell!.fontStyle
                            .contains(NetalTextFontStyle.bold)
                    ? FontWeight.bold
                    : FontWeight.normal,
                fontStyle: selectedCell != null &&
                        selectedCell!.fontStyle
                            .contains(NetalTextFontStyle.italic)
                    ? FontStyle.italic
                    : FontStyle.normal,
                decoration: selectedCell != null &&
                        selectedCell!.fontStyle
                            .contains(NetalTextFontStyle.underline)
                    ? TextDecoration.underline
                    : TextDecoration.none,
                decorationColor: isInputFocused.value ? Colors.black : element.contentColor, // 下划线颜色也保持一致
                overflow: TextOverflow.clip,
              ),
              decoration: const InputDecoration(
                filled: false,
                isCollapsed: true,
                contentPadding: EdgeInsets.zero,
                border: InputBorder.none,
              ),
              contextMenuBuilder: (final BuildContext ctx,
                  final EditableTextState editableTextState) {
                focusNode.unfocus();
                return SizedBox.shrink();
              }
              /*contextMenuBuilder: (final BuildContext ctx,
                final EditableTextState editableTextState) {
              Offset localPosition =
                  editableTextState.contextMenuAnchors.primaryAnchor;
              final menuList = ContextMenuUtils.buildContextMenu(
                  context, localPosition, ContextMenuType.element,
                  element: element, callback: () {
                initElement(success: () {
                  setTextFieldInit(
                    selectedCell?.value ?? '',
                    wordChar: selectedCell?.lineBreakMode !=
                        NetalTextLineBreakMode.char,
                  );
                  editableTextState.hideToolbar();
                });
              });
              if (menuList == null) return Container();
              return Stack(
                children: [
                  NiimbotContextMenuContainer(
                    list: menuList,
                    offset: () {
                      return Offset(localPosition.dx, localPosition.dy);
                    },
                  )
                ],
              );
            },*/
              ),
        ),
      ),
    );
  }
}

// 自定义输入框
class CustomTextField extends HookWidget {
  final double width;
  final double minHeight;
  final bool autofocus;
  final String? value;
  final ValueChanged<String>? onChanged;
  final VoidCallback? onBlur;

  const CustomTextField({
    super.key,
    required this.width,
    required this.minHeight,
    this.autofocus = false,
    this.value,
    this.onChanged,
    this.onBlur,
  });

  @override
  Widget build(final BuildContext context) {
    final niimbotTheme = NiimbotTheme.of(context);

    final textController = useTextEditingController();
    final focusNode = useFocusNode();
    final globalKey = useGlobalKey();
    initTextController() {
      String? showValue = value;
      if (showValue != null && showValue != textController.text) {
        textController.value = TextEditingValue(
          text: showValue,
          selection: TextSelection.fromPosition(
            TextPosition(
              /// 保持光标在最后
              affinity: TextAffinity.downstream,
              offset: showValue.length,
            ),
          ),
        );
      }
    }

    useEffect(() {
      initTextController();
      return null;
    }, [value]);
    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((final _) {
        focusNode.requestFocus();
      });
      focusNode.addListener(() {
        if (!focusNode.hasFocus) {
          onBlur?.call();
          FocusScope.of(context).requestFocus(focusNode.parent);
        }
      });
      return null;
    }, []);
    return Container(
      width: width,
      constraints: BoxConstraints(
        minHeight: minHeight,
      ),
      alignment: Alignment.centerLeft,
      child: TextField(
        key: globalKey,
        autofocus: true,
        focusNode: focusNode,
        controller: textController,
        minLines: 3,
        maxLines: 10,
        keyboardType: TextInputType.multiline,
        textAlignVertical: TextAlignVertical.center,
        onChanged: onChanged,
        cursorWidth: 1,
        cursorColor: niimbotTheme.colors.textFillColorPrimary,
        style: TextStyle(
            height: 1.25,
            fontSize: niimbotTheme.typography.body?.fontSize,
            color: niimbotTheme.colors.textFillColorPrimary,
            fontFamily: niimbotTheme.typography.body?.fontFamily),
        decoration: InputDecoration(
          filled: true,
          isCollapsed: true,
          contentPadding:
              const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
          hoverColor: Colors.transparent,
          fillColor: niimbotTheme.colors.solidBackgroundFillColorBase,
          border: OutlineInputBorder(
              borderSide:
                  BorderSide(color: niimbotTheme.colors.borderColorNormal),
              borderRadius: const BorderRadius.all(Radius.circular(6.0))),
          enabledBorder: OutlineInputBorder(
              borderSide:
                  BorderSide(color: niimbotTheme.colors.borderColorNormal),
              borderRadius: const BorderRadius.all(Radius.circular(6.0))),
          focusedBorder: OutlineInputBorder(
              borderSide:
                  BorderSide(color: niimbotTheme.colors.borderColorEnter),
              borderRadius: const BorderRadius.all(Radius.circular(6.0))),
          counterText: "",
          hintText: NiimbotIntl.getIntlMessage("pc0020", "输入内容"),
          hintStyle: TextStyle(
              height: 1.25,
              fontSize: niimbotTheme.typography.body?.fontSize,
              color: niimbotTheme.colors.textFillColorSecondary,
              fontFamily: niimbotTheme.typography.body?.fontFamily),
        ),
      ),
    );
  }
}
