import 'dart:math' as math;

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:netal_plugin/netal_image_result.dart';
import 'package:netal_plugin/utils/ratio.dart';
import 'package:niimbot_flutter_canvas/extensions/template_data.dart';
import 'package:niimbot_flutter_canvas/utils/compute_semaphore.dart';
import 'package:niimbot_flutter_canvas/widgets/canvas/printarea/print_area_builder.dart';
import 'package:niimbot_template/extensions/template_data.dart';
import 'package:niimbot_template/models/template_data.dart';
import 'package:niimbot_template/template_generate.dart';

Logger _logger = Logger("PreviewTemplateWidget", on: kDebugMode);

class PreviewTemplateWidget extends StatelessWidget {
  final ConcurrentCompute<(int?, num?, String?), TemplateData>?
      concurrentCompute;
  final TemplateData template;
  final int? page;
  final Color? color;
  final List<Color?>? previewColor;

  /// 出血线显示，传入就显示
  final List<num>? printArea;
  final String? localBackgroundImageUrl;

  const PreviewTemplateWidget(
      {super.key,
      this.concurrentCompute,
      required this.template,
      this.page,
      this.color,
      this.printArea,
      this.localBackgroundImageUrl,
      this.previewColor});

  @override
  Widget build(final BuildContext context) {
    _logger.log('build');

    /// 构建可打印区域
    List<Positioned> buildPrintArea(final Size size, final List<num>? area) {
      return PrintAreaBuilder.build(
        printArea: area,
        canvasSize: size,
      );
    }

    final templatePreScale = template.calculatePreviewScale();

    Future<NetalImageResult?> generatePreviewImageAsync() async {
      final templateData = concurrentCompute != null
          ? await concurrentCompute!.run(
              template.printPreviewTemplate, (page, templatePreScale, null))
          : await template.printPreviewTemplate((page, templatePreScale, null));
      return TemplateGenerate.generatePreviewImageAsync(
        templateData,
        page: page ?? 1,
        colorList: previewColor ?? [color],
        localBackgroundImageUrl: localBackgroundImageUrl,
      );
    }

    return FutureBuilder(
      future: generatePreviewImageAsync(),
      builder: (final _, final snapshot) {
        final data = snapshot.data;
        if (data?.pixels.isNotEmpty ?? false) {
          return LayoutBuilder(
            builder:
                (final BuildContext ctx, final BoxConstraints constraints) {
              final canvasSize = template.size.mm2px();

              double scale = math.min(
                  (constraints.maxWidth * templatePreScale) / canvasSize.width,
                  (constraints.maxHeight * templatePreScale) /
                      canvasSize.height);
              // if (scale > 1) scale = 1.0;

              return Stack(
                fit: StackFit.loose,
                clipBehavior: Clip.none,
                children: [
                  Image.memory(
                    data!.pixels,
                    /** 避免闪动 */
                    gaplessPlayback: true,
                    scale: RatioUtils().ratioScale,
                    fit: BoxFit.cover,
                    // filterQuality: FilterQuality.low,
                    isAntiAlias: true,
                  ),
                  ...buildPrintArea(
                      canvasSize * scale / (templatePreScale.toDouble()),
                      printArea
                          ?.map((final e) => e * scale / templatePreScale)
                          .toList()),
                ],
              );
            },
          );
        }
        _logger.log(template.toJson(), 'imageData is Empty');
        return SizedBox.shrink();
      },
    );
  }
}
