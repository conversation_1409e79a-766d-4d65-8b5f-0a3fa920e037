import 'package:flutter/material.dart';
import 'package:niimbot_ui/styles/border_radius.dart';
import 'package:niimbot_ui/styles/theme.dart';

import 'package:niimbot_flutter_canvas/business/desktop/models/canvas_drag_model.dart';

enum CanvasScrollModelType { vertical, horizontal }

class CanvasScrollViewDeskTop extends StatefulWidget {
  final CanvasScrollModelType mode;
  final double widgetLong;
  final ValueChanged<double> onScroll;
  final double? scrollLong;

  const CanvasScrollViewDeskTop({
    super.key,
    this.mode = CanvasScrollModelType.vertical,
    required this.widgetLong,
    required this.onScroll,
    this.scrollLong,
  });

  @override
  State<CanvasScrollViewDeskTop> createState() =>
      _CanvasScrollViewDeskTopState();
}

class _CanvasScrollViewDeskTopState extends State<CanvasScrollViewDeskTop> {
  double _scroll = 0;
  double _scrollMax = 0;

  @override
  void didUpdateWidget(final CanvasScrollViewDeskTop oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.scrollLong != null &&
        oldWidget.scrollLong != widget.scrollLong) {
      double scrollLong = _scrollMax * widget.scrollLong!;
      if (scrollLong != _scroll) {
        setState(() {
          _scroll = scrollLong;
          if (scrollLong < 0) {
            _scroll = 0;
          } else if (scrollLong > _scrollMax) {
            _scroll = _scrollMax;
          }
        });
      }
    }
  }

  _onDragUpdate(final Offset offset) {
    bool modeVertical = widget.mode == CanvasScrollModelType.vertical;
    double num = modeVertical ? offset.dy : offset.dx;
    // setState(() {
    _scroll += num;
    if (_scroll < 0) {
      _scroll = 0;
    } else if (_scroll > _scrollMax) {
      _scroll = _scrollMax;
    }
    widget.onScroll.call(_scroll / _scrollMax);
    // });
  }

  _setScroll(
    double scroll,
  ) {
    if (scroll >= 0) {
      _scroll = scroll;
    } else {
      _scroll = 0;
    }
  }

  @override
  Widget build(final BuildContext context) {
    bool modeVertical = widget.mode == CanvasScrollModelType.vertical;

    double newScrollMax = widget.widgetLong - 100;
    if (newScrollMax != _scrollMax) {
      _scrollMax = newScrollMax;
      _setScroll(newScrollMax / 2);
    }

    return Positioned(
      left: modeVertical ? null : 0,
      top: modeVertical ? 0 : null,
      right: 0,
      bottom: 0,
      child: Container(
        width: modeVertical ? 12 : null,
        height: modeVertical ? null : 12,
        alignment: modeVertical ? Alignment.topCenter : Alignment.centerLeft,
        child: Draggable(
          rootOverlay: true,
          feedback: Container(),
          ignoringFeedbackSemantics: false,
          data: CanvasDragModel(
              canvasDragModelType: CanvasDragModelType.canvasBaseScrollType,
              data: {}),
          onDragStarted: () {},
          onDragUpdate: (final e) {
            _onDragUpdate(e.delta);
          },
          onDragEnd: (final DraggableDetails details) {},
          onDragCompleted: () {},
          onDraggableCanceled:
              (final Velocity velocity, final Offset offset) {},
          child: Container(
            width: modeVertical ? 8 : 100,
            height: modeVertical ? 100 : 8,
            margin: modeVertical
                ? EdgeInsets.only(top: _scroll)
                : EdgeInsets.only(left: _scroll),
            decoration: BoxDecoration(
              color: NiimbotTheme.of(context).colors.borderColorNormal,
              borderRadius: NiimbotRadius.small,
            ),
          ),
        ),
      ),
    );
  }
}
