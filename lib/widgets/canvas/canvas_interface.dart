import 'package:flutter/material.dart';
import 'package:netal_plugin/netal_image_result.dart';
import 'package:niimbot_template/models/elements/base_element.dart';

import 'package:niimbot_flutter_canvas/provider/models/base_element_updater.dart';

typedef CanvasItemBuilder = Widget Function<T extends BaseElement>({
  required T element,
  Color? color,
  NetalImageResult? imageCache,
  UpdateElementNetalInfo? updateElementNetalInfo,
});

/// 通过图像库更新元素信息
/// [image] 图像库生成的图像 用于缓存
/// [element] 根据图像库信息 重构元素数据
typedef UpdateElementNetalInfo = void Function<T extends BaseElementUpdater>({
  NetalImageResult? image,
  required String id,
  T? updater,
});
