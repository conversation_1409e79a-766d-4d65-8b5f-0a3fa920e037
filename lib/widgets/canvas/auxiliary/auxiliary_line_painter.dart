import 'package:flutter/material.dart';

import 'package:niimbot_flutter_canvas/utils/canvas_ext.dart';

enum AuxiliaryLineDirection { Horizontal, Vertical }

class AuxiliaryLinePainter extends CustomPainter {
  final AuxiliaryLineDirection direction;
  final double lineWidth;

  Color color;
  final Paint _paint;

  AuxiliaryLinePainter(this.direction, this.lineWidth, this.color)
      : _paint = Paint()
          ..style = PaintingStyle.stroke
          ..strokeWidth = lineWidth
          ..color = color;

  @override
  void paint(final Canvas canvas, final Size size) {
    // print("辅助线的size： ${size.toString()}");
    canvas.drawColor(Colors.transparent, BlendMode.color);

    Rect rect = Rect.fromLTWH(
        size.width / 4, size.height / 4, size.width / 2, size.height / 2);

    // print("辅助线的rect： ${_rect.toString()}");

    if (direction == AuxiliaryLineDirection.Horizontal) {
      canvas.drawDashLine(rect.topLeft, rect.topRight, 3, 3, _paint);
    } else {
      canvas.drawDashLine(rect.topLeft, rect.bottomLeft, 3, 3, _paint);
    }
  }

  @override
  bool shouldRepaint(covariant final CustomPainter oldDelegate) {
    return false;
  }
}
