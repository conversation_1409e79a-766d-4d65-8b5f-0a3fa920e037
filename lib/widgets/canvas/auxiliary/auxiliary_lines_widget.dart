import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:netal_plugin/utils/ratio.dart';
import 'package:niimbot_flutter_canvas/extensions/base_element.dart';
import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_flutter_canvas/widgets/components/dotted_line.dart';
import 'package:niimbot_flutter_canvas/widgets/components/stack2.dart';
import 'package:niimbot_template/extensions/template_data.dart';
import 'package:niimbot_template/models/elements/base_element.dart';
import 'package:provider/provider.dart';

class AuxiliaryLinesWidget extends StatelessWidget {
  final num scale;

  const AuxiliaryLinesWidget({super.key, required this.scale});

  /// 辅助线长度
  static const int _lineLength = 20000;

  /// 辅助线精度 单位像素
  static const int _accuracy = 1;

  @override
  Widget build(final BuildContext context) {
    final (elements, selectedElements, selectRect, rotate, canvasCenter) =
        context.select<CanvasStore,
                (List<BaseElement>, List<BaseElement>, Rect?, double, Offset)>(
            (final v) => (
                  v.canvasData.elements,
                  v.selectedElement,
                  v.getSelectRect(),
                  v.rotate,
                  v.canvasData.center.mm2px()
                ));

    final others = elements
        .where((final e) =>
            selectedElements.firstWhereOrNull((final se) => se.id == e.id) ==
            null)
        .toList();
    double? top;
    double? left;
    double? bottom;
    double? right;
    double? vCenter;
    double? hCenter;
    double rectWidth = selectedElements.length > 1 ? 2 : 1;

    /// 当前选中Rect
    /// 避免未选中元素报错
    if (selectedElements.isNotEmpty) {
      final cRect = selectRect?.mm2px() ??
          selectedElements.first.getViewRect(rotate, canvasCenter).mm2px();
      if (others.isNotEmpty) {
        /* 有参考元素 */
        for (var e in others) {
          final eRect = e.getViewRect(rotate, canvasCenter).mm2px();
          if ((cRect.left - eRect.left).abs() < _accuracy) {
            /* 左对齐 */
            left = cRect.left - rectWidth;
          }
          // if ((cRect.left - eRect.center.dx).abs() < _accuracy) {
          //   /* 左边与元素中心对齐 */
          //   left = cRect.left - rectWidth;
          // }
          if ((cRect.left - eRect.right).abs() < _accuracy) {
            /* 左边与元素右边对齐 */
            left = cRect.left - rectWidth;
          }
          if ((cRect.top - eRect.top).abs() < _accuracy) {
            /* 顶对齐 */
            top = cRect.top - rectWidth;
          }
          // if ((cRect.top - eRect.center.dy).abs() < _accuracy) {
          //   /* 顶与元素中心对齐 */
          //   top = cRect.top - rectWidth;
          // }
          if ((cRect.top - eRect.bottom).abs() < _accuracy) {
            /* 顶与元素底对齐 */
            top = cRect.top - rectWidth;
          }
          if ((cRect.right - eRect.right).abs() < _accuracy) {
            /* 右对齐 */
            right = cRect.right + rectWidth;
          }
          // if ((cRect.right - eRect.center.dx).abs() < _accuracy) {
          //   /* 右与元素中心对齐 */
          //   right = cRect.right + rectWidth;
          // }
          if ((cRect.right - eRect.left).abs() < _accuracy) {
            /* 右与元素左对齐 */
            right = cRect.right + rectWidth;
          }
          if ((cRect.bottom - eRect.bottom).abs() < _accuracy) {
            /* 底对齐 */
            bottom = cRect.bottom + rectWidth;
          }
          // if ((cRect.bottom - eRect.center.dx).abs() < _accuracy) {
          //   /* 底与元素中心对齐 */
          //   bottom = cRect.bottom + rectWidth;
          // }
          if ((cRect.bottom - eRect.top).abs() < _accuracy) {
            /* 底与元素顶对齐 */
            bottom = cRect.bottom + rectWidth;
          }
          if ((cRect.center.dx - eRect.center.dx).abs() < _accuracy) {
            /* 水平对齐 */
            vCenter = cRect.center.dx;
          }
          // if ((cRect.center.dx - eRect.left).abs() < _accuracy) {
          //   /* 中心与元素左对齐 */
          //   vCenter = cRect.center.dx;
          // }
          // if ((cRect.center.dx - eRect.right).abs() < _accuracy) {
          //   /* 中心与元素左对齐 */
          //   vCenter = cRect.center.dx;
          // }
          if ((cRect.center.dy - eRect.center.dy).abs() < _accuracy) {
            /* 垂直对齐 */
            hCenter = cRect.center.dy;
          }
          // if ((cRect.center.dy - eRect.top).abs() < _accuracy) {
          //   /* 中心与元素顶对齐 */
          //   hCenter = cRect.center.dy;
          // }
          // if ((cRect.center.dy - eRect.bottom).abs() < _accuracy) {
          //   /* 中心与元素底对齐 */
          //   hCenter = cRect.center.dy;
          // }
        }
      }
      if ((cRect.center.dx - canvasCenter.dx).abs() < _accuracy) {
        /* 中心与画布水平对齐 */
        vCenter = canvasCenter.dx;
      }
      if ((cRect.center.dy - canvasCenter.dy).abs() < _accuracy) {
        /* 中心与画布垂直对齐 */
        hCenter = canvasCenter.dy;
      }
    }
    return Stack2(fit: StackFit.loose, clipBehavior: Clip.none, children: [
      if (left != null) _buildVerticalLine(left, scale),
      if (top != null) _buildHorizontalLine(top, scale),
      if (right != null) _buildVerticalLine(right, scale),
      if (bottom != null) _buildHorizontalLine(bottom, scale),
      if (vCenter != null) _buildVerticalLine(vCenter, scale),
      if (hCenter != null) _buildHorizontalLine(hCenter, scale),
    ]);
  }

  /// 生成垂直辅助线
  Positioned _buildVerticalLine(final double left, final num scale) {
    return Positioned(
      left: left,
      top: -_lineLength / 2 / scale,
      child: DottedLine(
        width: 1,
        color: Colors.blue,
        axis: Axis.vertical,
        length: _lineLength ~/ scale,
      ),
    );
  }

  /// 生成水平辅助线
  Positioned _buildHorizontalLine(final double top, final num scale) {
    return Positioned(
      left: -_lineLength / 2 / scale,
      top: top,
      child: DottedLine(
        width: 1,
        color: Colors.blue,
        axis: Axis.horizontal,
        length: _lineLength ~/ scale,
      ),
    );
  }
}
