import 'package:flutter/material.dart';

import 'package:niimbot_flutter_canvas/widgets/canvas/auxiliary/auxiliary_line_painter.dart';

/// 辅助线位置
class AuxiliaryAlignment {
  static const int Top = 1;
  static const int VerticalCenter = (1 << 1);
  static const int Bottom = (1 << 2);
  static const int Left = (1 << 3);
  static const int HorizontalCenter = (1 << 4);
  static const int Right = (1 << 5);
}

class AuxiliaryLine {
  /// 辅助线长度
  static const double sideLength = 2000;

  /// 辅助线中心点
  final Offset center;

  /// 辅助线方向
  final AuxiliaryLineDirection direction;

  final Color color;

  AuxiliaryLine(this.center, this.direction, this.color);
}

class AuxiliaryLineWidget extends StatelessWidget {
  final AuxiliaryLineDirection direction;
  final double sideLength;
  final Color color;
  final double lineWidth;

  const AuxiliaryLineWidget({
    super.key,
    required this.direction,
    required this.sideLength,
    this.color = Colors.red,
    this.lineWidth = 1.0,
  });

  @override
  Widget build(final BuildContext context) {
    return SizedBox(
      width: direction == AuxiliaryLineDirection.Horizontal
          ? sideLength
          : lineWidth,
      height: direction == AuxiliaryLineDirection.Vertical
          ? sideLength
          : lineWidth,
      child: CustomPaint(
        painter: AuxiliaryLinePainter(direction, lineWidth, color),
      ),
    );
  }
}
