import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';

import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:netal_plugin/utils/ratio.dart';
import 'package:niimbot_flutter_canvas/widgets/canvas/canvas_core_shortcuts.dart';
import 'package:niimbot_template/extensions/template_data.dart';
import 'package:provider/provider.dart';

import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_flutter_canvas/widgets/canvas/canvas_interface.dart';
import 'package:niimbot_flutter_canvas/widgets/canvas/widgets/core_view_container.dart';

final Logger _logger = Logger("CanvasCore", on: kDebugMode);

/// 画板绘制层
class CanvasCore extends HookWidget {
  const CanvasCore({
    super.key,
    required this.itemBuilder,
    this.batchInfo,
    this.onBatchPreviewTap,
    required this.backgroundImage,
    required this.multipleBackIndex,
    required this.localBackgrounds,
    required this.rfidBind,
    this.children = const [],
    required this.showBatchPreview,
    this.activated = true,
    this.canvasBgKey,
  });

  final bool activated;
  final bool showBatchPreview;

  final CanvasItemBuilder itemBuilder;
  final List<int>? batchInfo;

  /// 批量预览
  final VoidCallback? onBatchPreviewTap;

  /// rfid绑定
  final void Function() rfidBind;

  final String backgroundImage;
  final int multipleBackIndex;
  final List<String> localBackgrounds;
  final List<Widget> children;
  final GlobalKey? canvasBgKey;

  /// 背景图片列表
  List<String> get backgroundList {
    return backgroundImage.split(',');
  }

  @override
  Widget build(final BuildContext context) {
    _logger.log("=============canvas_core组件的build");

    /// 初始化计算倍率
    RatioUtils().init(context);

    final templateSize =
        context.select<CanvasStore, Size>((final v) => v.canvasData.size);

    return CanvasCoreShortcuts(
      autofocus: activated,
      child: LayoutBuilder(
        builder:
            (final BuildContext context, final BoxConstraints constraints) {
          return Container(
            color: const Color(0xFFEBEBF0),
            constraints: constraints,
            width: constraints.maxWidth,
            height: constraints.maxHeight,
            child: CanvasCoreViewContainer(
              batchInfo: batchInfo,
              showBatchPreview: showBatchPreview,
              onBatchPreviewTap: onBatchPreviewTap,
              rfidBind: rfidBind,
              itemBuilder: itemBuilder,
              backgroundList: backgroundList,
              localBackgrounds: localBackgrounds,
              multipleBackIndex: multipleBackIndex,
              templateSize: templateSize,
              constraints: constraints,
              canvasBgKey: canvasBgKey,
              children: children,
            ),
          );
        },
      ),
    );
  }
}
