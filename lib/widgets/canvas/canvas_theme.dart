import 'package:flutter/widgets.dart';

import 'package:flutter_canvas_plugins_interface/config/canvas_theme_data.dart';

/// 共享主题
class CanvasTheme extends InheritedWidget {
  final CanvasThemeData themeData;
  final String language;

  const CanvasTheme({
    super.key,
    required this.themeData,
    required this.language,
    required super.child,
  });

  static CanvasThemeData of(final BuildContext context) {
    return context.dependOnInheritedWidgetOfExactType<CanvasTheme>()!.themeData;
  }

  static String langOf(final BuildContext context) {
    return context.dependOnInheritedWidgetOfExactType<CanvasTheme>()!.language;
  }

  @override
  bool updateShouldNotify(final CanvasTheme oldWidget) {
    return oldWidget.themeData != themeData;
  }
}
