import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:netal_plugin/utils/ratio.dart';
import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_flutter_canvas/widgets/canvas/canvas_utils.dart';
import 'package:niimbot_template/models/elements/bind_element.dart';
import 'package:niimbot_template/models/elements/table_element.dart';
import 'package:provider/provider.dart';

class CanvasCoreShortcuts extends HookWidget {
  final Widget child;
  final bool autofocus;

  const CanvasCoreShortcuts({
    super.key,
    required this.child,
    this.autofocus = true,
  });

  @override
  Widget build(final BuildContext context) {
    final focusNode = useFocusNode(debugLabel: 'CanvasCoreShortcuts');
    onDelete() {
      /* 删除元素 */
      final elements = context.read<CanvasStore>().selectedElement;
      final first = elements.isNotEmpty ? elements.first : null;
      if (elements.length == 1 && first is TableElement) {
        /* 表格元素选中单元格 清除单元格内容 */
        final ids = context.read<CanvasStore>().getSelected(first.id);
        if (ids.isNotEmpty) {
          final isSelectedContentEmpty = context
              .read<CanvasStore>()
              .tableController
              .isSelectedContentEmpty(first.id);
          if (isSelectedContentEmpty) {
            context.read<CanvasStore>().focusElement(first.id);
          } else {
            context
                .read<CanvasStore>()
                .tableController
                .clearSelectedContent(first.id);
          }
        } else {
          context.read<CanvasStore>().deleteElement();
        }
      } else {
        context.read<CanvasStore>().deleteElement();
      }
    }

    onLock() {
      /* 锁定/解锁元素 */
      context.read<CanvasStore>().baseController.updateElementsLock();
    }

    onSelectAll() {
      /* 全选 */
      context.read<CanvasStore>().selectAll();
    }

    onPaste() {
      /* 粘贴 */
      if (!context.mounted) return;
      context.read<CanvasStore>().pasteElements();
    }

    onCopy() {
      /* 复制 */
      if (!context.mounted) return;
      context.read<CanvasStore>().copyElements();
    }

    onCut() async {
      /* 剪切 */
      if (!context.mounted) return;
      final elements = context.read<CanvasStore>().copySelectedElement;
      if (elements.isNotEmpty) {
        ///复制元素时 添加本身的数据源值
        Map<String, String?> valueMap = {};
        for (final e in elements) {
          if ((e is BindElement) && e.isBindingElement) {
            valueMap.addAll({
              e.id: context.select<CanvasStore, String?>(
                  (final v) => v.bindElementValue(e.id))
            });
          }
        }
        await CanvasUtils.copyElementToClipboard(elements,
            valueMap: valueMap,
            canvasKey: context.read<CanvasStore>().canvasKey);
        if (!context.mounted) return;
        context.read<CanvasStore>().deleteElement();
      }
    }

    onUndo() {
      /* 撤销 */
      if (!context.mounted) return;
      context.read<CanvasStore>().undo();
    }

    onRedo() {
      /* 恢复 */
      if (!context.mounted) return;
      context.read<CanvasStore>().redo();
    }

    onMoveUp() {
      /* 向上移动 */
      final offset = HardwareKeyboard.instance.isShiftPressed
          ? const Offset(0, -1)
          : const Offset(0, -1).px2mm();
      if (!context.mounted) return;
      context.read<CanvasStore>().moveSelectedElements(offset);
    }

    onMoveDown() {
      /* 向下移动 */
      final offset = HardwareKeyboard.instance.isShiftPressed
          ? const Offset(0, 1)
          : const Offset(0, 1).px2mm();
      if (!context.mounted) return;
      context.read<CanvasStore>().moveSelectedElements(offset);
    }

    onMoveLeft() {
      /* 向左移动 */
      final offset = HardwareKeyboard.instance.isShiftPressed
          ? const Offset(-1, 0)
          : const Offset(-1, 0).px2mm();
      if (!context.mounted) return;
      context.read<CanvasStore>().moveSelectedElements(offset);
    }

    onMoveRight() {
      /* 向右移动 */
      final offset = HardwareKeyboard.instance.isShiftPressed
          ? const Offset(1, 0)
          : const Offset(1, 0).px2mm();
      if (!context.mounted) return;
      context.read<CanvasStore>().moveSelectedElements(offset);
    }

    onEnter() {
      if (!context.mounted) return;
      context.read<CanvasStore>().clippedElement();
    }

    onEscape() {
      if (!context.mounted) return;
      context.read<CanvasStore>().clippedElement();
    }

    final bindings = useMemoized(() {
      return {
        const SingleActivator(LogicalKeyboardKey.delete): onDelete,
        const SingleActivator(LogicalKeyboardKey.backspace): onDelete,
        const SingleActivator(LogicalKeyboardKey.keyL, control: true): onLock,
        const SingleActivator(LogicalKeyboardKey.keyA, control: true):
            onSelectAll,
        const SingleActivator(LogicalKeyboardKey.keyV, control: true): onPaste,
        const SingleActivator(LogicalKeyboardKey.keyC, control: true): onCopy,
        const SingleActivator(LogicalKeyboardKey.keyX, control: true): onCut,
        const SingleActivator(LogicalKeyboardKey.keyZ, control: true): onUndo,
        const SingleActivator(LogicalKeyboardKey.keyY, control: true): onRedo,
        const SingleActivator(LogicalKeyboardKey.arrowUp): onMoveUp,
        const SingleActivator(LogicalKeyboardKey.arrowDown): onMoveDown,
        const SingleActivator(LogicalKeyboardKey.arrowLeft): onMoveLeft,
        const SingleActivator(LogicalKeyboardKey.arrowRight): onMoveRight,
        const SingleActivator(LogicalKeyboardKey.enter): onEnter,
        const SingleActivator(LogicalKeyboardKey.escape): onEscape,
      };
    });

    onFocusCanvas() {
      focusNode.requestFocus();
      if (context.read<CanvasStore>().clippingElementId != null) {
        context.read<CanvasStore>().clippedElement();
      } else {
        context.read<CanvasStore>().clearSelected();
        context.read<CanvasStore>().cancelEdit();
      }
    }

    // A helper function to make the stack trace more useful if the callback
    // throws, by providing the activator and event as arguments that will appear
    // in the stack trace.
    bool applyKeyEventBinding(
        final ShortcutActivator activator, final KeyEvent event) {
      if (activator.accepts(event, HardwareKeyboard.instance)) {
        bindings[activator]!.call();
        return true;
      }
      return false;
    }

    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((final _) {
        if (autofocus) {
          focusNode.requestFocus();
        } else {
          context.read<CanvasStore>().clearSelected();
          context.read<CanvasStore>().cancelEdit();
        }
      });

      return null;
    }, [autofocus]);

    return FocusScope(
      child: Focus(
        debugLabel: 'CanvasCoreShortcuts',
        focusNode: focusNode,
        autofocus: autofocus,
        onKeyEvent: (final FocusNode node, final KeyEvent event) {
          KeyEventResult result = KeyEventResult.ignored;
          if (!focusNode.hasPrimaryFocus) return result;
          // Activates all key bindings that match, returns "handled" if any handle it.
          for (final ShortcutActivator activator in bindings.keys) {
            result = applyKeyEventBinding(activator, event)
                ? KeyEventResult.handled
                : result;
          }
          return result;
        },
        child: GestureDetector(
            onTap: onFocusCanvas,
            // onTapDown: onTapDownCanvas,
            behavior: HitTestBehavior.opaque,
            child: child),
      ),
    );
  }
}
