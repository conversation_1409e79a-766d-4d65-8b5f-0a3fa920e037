import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:netal_plugin/netal_image_result.dart';
import 'package:niimbot_template/models/elements/bar_code_element.dart';
import 'package:niimbot_template/models/elements/base_element.dart';
import 'package:niimbot_template/models/elements/graph_element.dart';
import 'package:niimbot_template/models/elements/image_element.dart';
import 'package:niimbot_template/models/elements/line_element.dart';
import 'package:niimbot_template/models/elements/qr_code_element.dart';
import 'package:niimbot_template/models/elements/table_element.dart';

import 'package:niimbot_flutter_canvas/widgets/canvas/canvas_interface.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/bar_code_widget.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/graph_widget.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/image_widget.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/line_widget.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/qr_code_widget.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/table/table_widget.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/text_widget.dart';
import 'package:niimbot_template/models/elements/text_element.dart';

Logger _logger = Logger("DefaultCanvasItemBuilder", on: kDebugMode);

/// 新增isCleanImageWidget参数，代表是否只返回干净的Image组件，防止wrap-Stack导致的包裹尺寸问题
CanvasItemBuilder DefaultCanvasItemBuilder = <T extends BaseElement>({
  required final T element,
  final Color? color,
  final NetalImageResult? imageCache,
  final UpdateElementNetalInfo? updateElementNetalInfo,
}) {
  if (element is BarCodeElement) {
    return BarCodeWidget(
      element: element,
      color: color,
      imageCache: imageCache,
      updateElementNetalInfo: updateElementNetalInfo,
    );
  }
  if (element is GraphElement) {
    return GraphWidget(
      element: element,
      color: color,
      imageCache: imageCache,
      updateElementNetalInfo: updateElementNetalInfo,
    );
  }
  if (element is ImageElement) {
    return ImageWidget(
      element: element,
      color: color,
      imageCache: imageCache,
      updateElementNetalInfo: updateElementNetalInfo,
    );
  }
  if (element is LineElement) {
    return LineWidget(
      element: element,
      color: color,
      imageCache: imageCache,
      updateElementNetalInfo: updateElementNetalInfo,
    );
  }
  if (element is QRCodeElement) {
    return QRCodeWidget(
      element: element,
      color: color,
      imageCache: imageCache,
      updateElementNetalInfo: updateElementNetalInfo,
    );
  }
  if (element is TableElement) {
    return TableWidget(
      element: element,
      color: color,
      imageCache: imageCache,
      updateElementNetalInfo: updateElementNetalInfo,
    );
  }
  if (element is TextElement) {
    return TextWidget(
      element: element,
      color: color,
      imageCache: imageCache,
      updateElementNetalInfo: updateElementNetalInfo,
    );
  }
  return SizedBox.shrink();
};
