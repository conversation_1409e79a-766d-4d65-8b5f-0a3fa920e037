import 'dart:async';
import 'dart:convert' show base64, json<PERSON><PERSON><PERSON>, json<PERSON><PERSON>de, utf8;

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';

import 'package:netal_plugin/models/netal_enum.dart';
import 'package:netal_plugin/utils/ratio.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_template/models/elements/bar_code_element.dart';
import 'package:niimbot_template/models/elements/base_element.dart';
import 'package:niimbot_template/models/elements/graph_element.dart';
import 'package:niimbot_template/models/elements/image_element.dart';
import 'package:niimbot_template/models/elements/line_element.dart';
import 'package:niimbot_template/models/elements/material_element.dart';
import 'package:niimbot_template/models/elements/qr_code_element.dart';
import 'package:niimbot_template/models/elements/table_element.dart';
import 'package:niimbot_template/models/elements/text_element.dart';
import 'package:niimbot_template/template_parse.dart';
import 'package:niimbot_ui/widgets/niimbot_toast/niimbot_loading_controller.dart';
import 'package:super_clipboard/super_clipboard.dart';
import 'package:vector_math/vector_math_64.dart' show Vector3, Matrix4;

import 'package:niimbot_flutter_canvas/business/desktop/utils/canvas_images_handle_utils.dart';
import 'package:niimbot_flutter_canvas/model/template/template_utils.dart';
import 'package:niimbot_flutter_canvas/utils/buried_utils.dart';
import 'package:niimbot_flutter_canvas/utils/canvas_image_util.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/interactive/resize_point.dart';

Logger _logger = Logger("CanvasUtils", on: kDebugMode);

/// 精臣粘贴板格式
const NIIMBOT_CLIPBOARD_FORMAT = CustomValueFormat<Uint8List>(
  applicationId: "com.niimbot.canvas",
);

class CanvasUtils {
  static Offset matrix4ToScene(
      final Matrix4 matrix4, final Offset viewportPoint) {
    final Matrix4 inverseMatrix = Matrix4.inverted(matrix4);
    final Vector3 untransformed = inverseMatrix.transform3(Vector3(
      viewportPoint.dx,
      viewportPoint.dy,
      0,
    ));
    return Offset(untransformed.x, untransformed.y);
  }

  static Offset getPosition(
      final GlobalKey parentKey, final GlobalKey childKey) {
    final parentBox =
        parentKey.currentContext?.findRenderObject() as RenderBox?;
    if (parentBox == null) {
      throw Exception();
    }
    final childBox = childKey.currentContext?.findRenderObject() as RenderBox?;
    if (childBox == null) {
      throw Exception();
    }

    final parentPosition = parentBox.localToGlobal(Offset.zero);

    final childPosition = childBox.localToGlobal(Offset.zero);

    final x = childPosition.dx - parentPosition.dx;
    final y = childPosition.dy - parentPosition.dy;

    return Offset(x, y);
  }

  static Future<ImageElement?> _getClipboardImageByFormat(
    final ClipboardReader reader,
    final Size canvasSize,
    final FileFormat format, {
    final Future<String?> Function(Uint8List data)? localFileSave,
  }) {
    Completer<ImageElement?> c = Completer();
    final progress = reader.getFile(format, (final file) async {
      final fileBytes = await file.readAll();
      try {
        if (fileBytes.isNotEmpty) {
          NiimbotLoadingController().show(
            NiimbotIntl.getIntlMessage('pc0372', '上传中...'),
            showMask: true,
            duration: Duration.zero,
          );
          final Uint8List? bytes =
              await CanvasImageUtils.preProcessImage(fileBytes);
          if (bytes == null) {
            NiimbotLoadingController().close();
            c.complete(null);
          } else {
            Size size = await CanvasImagesHandleUtils()
                .canvasImageElementGenerate(bytes, canvasSize);
            final localPath = await localFileSave?.call(bytes);
            NiimbotLoadingController().close();
            if (localPath == null) {
              c.complete(null);
            } else {
              c.complete(ImageElement(
                width: size.width.px2mm(),
                height: size.height.px2mm(),
                localImageUrl: localPath,
                imageData: '',
              ));
            }
          }
        }
      } catch (e) {
        c.complete(null);
      }
    }, onError: (final e) {
      c.completeError(e);
    });
    if (progress == null) {
      c.complete(null);
    }
    return c.future;
  }

  /// 获取粘贴板中的图片
  static Future<List<BaseElement>?> _getClipboardImage(
    final ClipboardReader reader,
    final Size canvasSize, {
    final Future<String?> Function(Uint8List data)? localFileSave,
  }) async {
    if (reader.canProvide(Formats.png)) {
      final element = await _getClipboardImageByFormat(
        reader,
        canvasSize,
        Formats.png,
        localFileSave: localFileSave,
      );
      if (element != null) {
        return [element];
      }
    }
    if (reader.canProvide(Formats.jpeg)) {
      final element = await _getClipboardImageByFormat(
        reader,
        canvasSize,
        Formats.jpeg,
        localFileSave: localFileSave,
      );
      if (element != null) {
        return [element];
      }
    }
    if (reader.canProvide(Formats.heif)) {
      final element = await _getClipboardImageByFormat(
        reader,
        canvasSize,
        Formats.heif,
        localFileSave: localFileSave,
      );
      if (element != null) {
        return [element];
      }
    }
    if (reader.canProvide(Formats.heic)) {
      final element = await _getClipboardImageByFormat(
        reader,
        canvasSize,
        Formats.heic,
        localFileSave: localFileSave,
      );
      if (element != null) {
        return [element];
      }
    }
    return null;
  }

  /// 粘贴埋点
  static buriedStack(final bool isCurrent, final List<BaseElement> elements) {
    BuriedUtils().track('click', '007_038_064', ext: {
      'content': elements.map((final e) => e.type).join(','),
      'source': isCurrent ? 1 : 2
    });
  }

  /// 复制元素到粘贴板
  static copyElementToClipboard(final List<BaseElement> elements,
      {required final Map<String, dynamic> valueMap,
      required final String canvasKey}) async {
    final clipboard = SystemClipboard.instance;
    final item = DataWriterItem();
    final bytes = Uint8List.fromList(utf8.encode(jsonEncode({
      'elements': elements,
      'valuesMap': valueMap,
      'canvasKey': canvasKey
    })));
    item.add(NIIMBOT_CLIPBOARD_FORMAT(bytes));
    await clipboard?.write([item]);
  }

  /// 从粘贴板中获取元素 把当前画板的元素传递过来
  /// 返回元素列表及是否为内容元素
  static Future<(List<BaseElement>, bool)> getElementFromClipboard({
    required final Size canvasSize,
    required final String canvasKey,
    final Future<String?> Function(Uint8List data)? localFileSave,
  }) async {
    final reader = await SystemClipboard.instance?.read();
    if (reader == null) return ([] as List<BaseElement>, false);
    if (reader.canProvide(NIIMBOT_CLIPBOARD_FORMAT)) {
      /* 存在精臣粘贴板格式 */
      final bytes = await reader.readValue(NIIMBOT_CLIPBOARD_FORMAT);
      if (bytes?.isNotEmpty ?? false) {
        final value = utf8.decode(bytes!.toList());
        final data = jsonDecode(value);
        final elements =
            await TemplateParse.parseTemplateElements(data['elements']);
        final Map<String, dynamic> valuesMap = data['valuesMap'];
        final String copyCanvasKey = data['canvasKey'] ?? '';

        ///粘贴时需要判断当前是否是有数据源的粘贴以及当前画板粘贴还是不同画板粘贴
        final bool isCurrent = copyCanvasKey == canvasKey;
        final tempElements = TemplateUtils.cloneElements(elements,
            isCurrent: isCurrent, valuesMap: valuesMap ?? {});
        buriedStack(isCurrent, tempElements);

        return (tempElements, false);
      }
    }
    final elements = await _getClipboardImage(
      reader,
      canvasSize,
      localFileSave: localFileSave,
    );
    if (elements != null) {
      buriedStack(false, elements);
      return (elements, true);
    }
    if (reader.canProvide(Formats.plainText)) {
      /* 纯文本 */
      final value = await reader.readValue(Formats.plainText);
      if (value != null) {
        String languageCode = NiimbotIntl.getCurrentLocale().languageCode;
        final isLangJa = languageCode == 'ja';
        final elements = [
          TextElement(
            fontFamily: !isLangJa ?  'Harmony' : 'ZT825',
            fontCode: !isLangJa ? 'ZT001' : 'ZT825',
            value: value,
            boxStyle: NetalTextBoxStyle.autoWidth,
          )
        ];
        buriedStack(false, elements);

        return (elements, true);
      }
    }
    return (List<BaseElement>.from([]), false);
  }

  static List<ResizePointType> buildElementResizeType(
      final BaseElement element) {
    if (element is MaterialElement) {
      /* 素材只支持等比缩放 */
      return [ResizePointType.bottomRight];
    }
    if (element is ImageElement) {
      return [ResizePointType.bottomRight];
    }
    if (element is BarCodeElement) {
      return [ResizePointType.bottomRight];
    }
    if (element is QRCodeElement) {
      if (element.codeType == NetalQRCodeType.PDF417) {
        return [ResizePointType.right];
      }
      return [ResizePointType.bottomRight];
    }
    if (element is GraphElement) {
      return [ResizePointType.bottomRight];
    }
    if (element is LineElement) {
      return [ResizePointType.right];
    }
    if (element is TextElement) {
      if (element.typesettingMode == NetalTypesettingMode.arc) {
        return [];
      }
      if (element.typesettingMode == NetalTypesettingMode.vertical) {
        return [ResizePointType.down];
      }
      return [ResizePointType.right];
    }
    if (element is TableElement) {
      return [ResizePointType.bottomRight];
    }
    return [];
  }
}
