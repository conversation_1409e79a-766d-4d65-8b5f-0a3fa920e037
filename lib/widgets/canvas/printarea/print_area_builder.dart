import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:netal_plugin/utils/ratio.dart';

import 'package:niimbot_template/utils/platform_utils.dart';

import 'package:niimbot_flutter_canvas/widgets/canvas/printarea/area_line_widget.dart';

final Logger _logger = Logger("PrintAreaBuilder", on: kDebugMode);

/// 辅助线构建器
class PrintAreaBuilder {
  /// 计算并构建打印区域
  static List<Positioned> build({
    final List<num>? printArea,
    final bool isDragging = false,
    required final Size canvasSize,
  }) {
    double left = 0.0;
    double top = 0.0;
    double right = 0.0;
    double bottom = 0.0;
    List<AreaLine> areaLines = [];

    if (PlatformUtils.isDesktop || isDragging) {
      _logger.log("获取的打印盲区：${printArea.toString()}");
      if (printArea != null && printArea.length == 4) {
        top = printArea[0].mm2px().toDouble();
        bottom = printArea[1].mm2px().toDouble();
        left = printArea[2].mm2px().toDouble();
        right = printArea[3].mm2px().toDouble();

        areaLines.add(AreaLine(
            Offset(left, top), Offset(left, canvasSize.height - bottom)));
        areaLines.add(
            AreaLine(Offset(left, top), Offset(canvasSize.width - right, top)));
        areaLines.add(AreaLine(Offset(canvasSize.width - right, top),
            Offset(canvasSize.width - right, canvasSize.height - bottom)));
        areaLines.add(AreaLine(Offset(left, canvasSize.height - bottom),
            Offset(canvasSize.width - right, canvasSize.height - bottom)));
      }
    }

    return areaLines
        .map((final e) => Positioned(
              left: e.start.dx,
              top: e.start.dy,
              child: AreaLineWidget(areaLine: e),
            ))
        .toList();
  }
}
