import 'package:flutter/material.dart';

import 'package:niimbot_flutter_canvas/widgets/canvas/printarea/area_line_painter.dart';

class AreaLine {
  /// 辅助线起点
  final Offset start;

  /// 辅助线终点
  final Offset end;

  /// 线宽
  final double lineWidth;

  AreaLine(this.start, this.end, {this.lineWidth = 1.0});

  double get width {
    if (start.dx == end.dx) {
      return lineWidth;
    } else {
      return end.dx - start.dx;
    }
  }

  double get height {
    if (start.dy == end.dy) {
      return lineWidth;
    } else {
      return end.dy - start.dy;
    }
  }

  AreaLineDirection get direction {
    if (start.dx == end.dx) {
      return AreaLineDirection.Vertical;
    } else {
      return AreaLineDirection.Horizontal;
    }
  }
}

class AreaLineWidget extends StatelessWidget {
  final Color color;
  final AreaLine areaLine;

  const AreaLineWidget({
    super.key,
    required this.areaLine,
    this.color = Colors.red,
  }) ;

  @override
  Widget build(final BuildContext context) {
    return SizedBox(
      width: areaLine.width,
      height: areaLine.height,
      child: CustomPaint(
        painter: AreaLinePainter(areaLine.direction, areaLine.lineWidth, color),
      ),
    );
  }
}
