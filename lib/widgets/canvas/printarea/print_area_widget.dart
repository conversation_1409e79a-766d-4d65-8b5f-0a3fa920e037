import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';

import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:netal_plugin/utils/ratio.dart';
import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:provider/provider.dart';

class PrintAreaWidget extends HookWidget {
  final Size templateSize;

  const PrintAreaWidget({super.key, required this.templateSize});

  @override
  Widget build(final BuildContext context) {
    final printArea =
        context.select<CanvasStore, Rect?>((final v) => v.printAreaRect);
    if (printArea != null) {
      return Positioned(
          left: printArea.left.mm2px().toDouble(),
          top: printArea.top.mm2px().toDouble(),
          child: DottedBorder(
            borderType: BorderType.Rect,
            dashPattern: const [4, 4],
            padding: EdgeInsets.zero,
            color: Colors.red,
            child: SizedBox.fromSize(size: printArea.size.mm2px()),
          ));
    }
    return SizedBox.shrink();
  }
}
