import 'package:flutter/material.dart';

import 'package:niimbot_flutter_canvas/utils/canvas_ext.dart';

enum AreaLineDirection { Horizontal, Vertical }

class AreaLinePainter extends CustomPainter {
  final AreaLineDirection direction;
  final double lineWidth;

  Color color;
  final Paint _paint;

  AreaLinePainter(this.direction, this.lineWidth, this.color)
      : _paint = Paint()
          ..style = PaintingStyle.stroke
          ..strokeWidth = lineWidth
          ..color = color;

  @override
  void paint(final Canvas canvas, final Size size) {
    // print("辅助线的size： ${size.toString()}");
    canvas.drawColor(Colors.transparent, BlendMode.color);

    Rect rect = Rect.fromLTWH(0, 0, size.width, size.height);

    // print("辅助线的rect： ${_rect.toString()}");

    if (direction == AreaLineDirection.Horizontal) {
      canvas.drawDashLine(rect.topLeft, rect.topRight, 4, 3, _paint);
    } else {
      canvas.drawDashLine(rect.topLeft, rect.bottomLeft, 4, 3, _paint);
    }
  }

  @override
  bool shouldRepaint(covariant final CustomPainter oldDelegate) {
    return false;
  }
}
