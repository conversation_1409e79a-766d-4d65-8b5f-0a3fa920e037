import 'package:flutter/material.dart';

class ChevronIcon extends StatelessWidget {
  final Widget icon;

  const ChevronIcon({super.key, required this.icon});

  @override
  Widget build(final BuildContext context) {
    final bool isRtl = Directionality.of(context) == TextDirection.rtl;
    return isRtl
        ? RotatedBox(
            quarterTurns: 2,
            child: icon,
          )
        : icon;
  }
}
