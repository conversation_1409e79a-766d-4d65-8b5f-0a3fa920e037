import 'dart:math';

import 'package:flutter/cupertino.dart';

import 'package:netal_plugin/utils/ratio.dart';

import 'package:niimbot_flutter_canvas/utils/theme_color.dart';

class RulerPainterDesktop extends CustomPainter {
  /// 标尺方向
  final Axis axis;

  /// 标尺宽度
  final double width;

  /// 标尺高度
  final double height;

  /// 标尺缩放比例
  final double scale;

  /// 标尺偏移
  final Offset offset;

  /// 模板宽度
  final double templateWidth;

  /// 模板高度
  final double templateHeight;

  /// 刻度颜色
  final Color lineColor;

  /// 文字颜色
  final Color textColor;

  /// 背景颜色
  final Color backgroundColor;

  /// 标记颜色
  final Color markingColor;

  /// 标记的偏移
  final Offset markingOffset;

  final TextDirection direction;

  const RulerPainterDesktop(
    this.axis,
    this.scale,
    this.offset,
    this.templateWidth,
    this.templateHeight, {
    this.width = 2000,
    this.height = 12,
    this.lineColor = ThemeColor.COLOR_999999,
    this.textColor = ThemeColor.COLOR_999999,
    this.backgroundColor = ThemeColor.background,
    this.markingColor = ThemeColor.opacityBackground,
    this.markingOffset = Offset.zero,
    this.direction = TextDirection.ltr,
  });

  @override
  void paint(final Canvas canvas, final Size size) {
    if (direction == TextDirection.ltr) {
      // _drawLine(canvas);
      _drawBackgroundColor(canvas);
      _drawMarkingBackgroundColor(canvas);
      _drawScale(canvas);
    } else {
      // _drawLineRTL(canvas);
      _drawBackgroundColor(canvas);
      _drawMarkingBackgroundColor(canvas);
      _drawScaleRTL(canvas);
    }
  }

  _drawBackgroundColor(final Canvas canvas) {
    Paint linePaint = Paint();
    linePaint.color = backgroundColor;
    linePaint.style = PaintingStyle.fill;
    linePaint.strokeWidth = 1;
    if (axis == Axis.horizontal) {
      Rect rect = Rect.fromLTWH(0, 0, width, 20);
      canvas.drawRect(rect, linePaint);
    } else {
      Rect rect = Rect.fromLTWH(0, 20, 20, width);
      canvas.drawRect(rect, linePaint);
    }
  }

  _drawMarkingBackgroundColor(final Canvas canvas) {
    Paint linePaint = Paint();
    linePaint.color = markingColor;
    linePaint.style = PaintingStyle.fill;
    linePaint.strokeWidth = 1;
    Offset zero2markingOffset = offset + markingOffset;
    if (axis == Axis.horizontal) {
      Rect rect = Rect.fromLTWH(
          (zero2markingOffset.dx) * scale, 0, templateWidth * scale, 20);
      canvas.drawRect(rect, linePaint);
    } else {
      Rect rect = Rect.fromLTWH(
          0, (zero2markingOffset.dy) * scale, 20, templateHeight * scale);
      canvas.drawRect(rect, linePaint);
    }
  }

  _drawScale(final Canvas canvas) {
    Paint linePaint = Paint();
    linePaint.color = lineColor;
    linePaint.style = PaintingStyle.stroke;
    linePaint.strokeWidth = 1;
    TextPainter textPainter = TextPainter();
    textPainter.textDirection = direction;
    if (axis == Axis.horizontal) {
      for (int i = 0; i < width; i++) {
        final lineInt = i - offset.dx.px2mm().toInt();
        var lineHeight = lineInt % 10 == 0
            ? height
            : lineInt % 5 == 0
                ? height / 2
                : height / 3;

        /// （画板偏移+刻度间隔）*画板缩放倍率
        canvas.drawLine(Offset(i.mm2px().toDouble(), 0) * scale,
            Offset((i.mm2px().toDouble()) * scale, lineHeight), linePaint);

        /// 整十显示刻度
        // if (RatioUtils().ratio < 2.5 ? i % 20 == 0 : i % 10 == 0) {
        if (scale < 0.5 ? lineInt % 20 == 0 : lineInt % 10 == 0) {
          textPainter.text = TextSpan(
              text: "$lineInt",
              style: TextStyle(color: textColor, fontSize: 10));
          textPainter.layout();
          textPainter.paint(
              canvas, Offset((i.mm2px() - 5) * scale, lineHeight));
        }
      }
    } else {
      for (int i = 0; i < width; i++) {
        final lineInt = i - offset.dy.px2mm().toInt();
        var lineHeight = lineInt % 10 == 0
            ? height
            : lineInt % 5 == 0
                ? height / 2
                : height / 3;
        canvas.drawLine(Offset(0, i.mm2px().toDouble()) * scale,
            Offset(lineHeight, (i.mm2px()) * scale), linePaint);
        // if (RatioUtils().ratio < 2.5 ? i % 20 == 0 : i % 10 == 0) {
        if (scale < 0.5 ? lineInt % 20 == 0 : lineInt % 10 == 0) {
          textPainter.text = TextSpan(children: [
            if (lineInt < 0)
              TextSpan(
                  text: '-', style: TextStyle(color: textColor, fontSize: 14)),
            TextSpan(
                text: '$lineInt'.replaceAll('-', ''),
                style: TextStyle(color: textColor, fontSize: 10))
          ]);
          textPainter.layout();
          canvas.save();
          double textWidth = textPainter.width, textHeight = textPainter.height;
          canvas.translate(lineHeight, (i.mm2px()) * scale);
          canvas.rotate(-pi / 2);
          textPainter.paint(canvas, Offset(-textWidth / 4, -textHeight / 4));
          canvas.restore();
        }
      }
    }
  }

  _drawScaleRTL(final Canvas canvas) {
    Paint linePaint = Paint();
    linePaint.color = lineColor;
    linePaint.style = PaintingStyle.stroke;
    linePaint.strokeWidth = 1;
    TextPainter textPainter = TextPainter();
    textPainter.textDirection = direction;
    if (axis == Axis.horizontal) {
      for (int i = 0; i < width; i++) {
        int lineInt = i - offset.dx.px2mm().toInt();
        var lineHeight = lineInt % 10 == 0
            ? height
            : lineInt % 5 == 0
                ? height / 2
                : height / 3;

        /// （画板偏移+刻度间隔）*画板缩放倍率
        canvas.drawLine(Offset(i.mm2px().toDouble(), 0) * scale,
            Offset((i.mm2px()) * scale, lineHeight), linePaint);

        /// 整十显示刻度
        // if (RatioUtils().ratio < 2.5 ? i % 20 == 0 : i % 10 == 0) {
        if (scale < 0.5 ? lineInt % 20 == 0 : lineInt % 10 == 0) {
          /// 刻度计算反向
          int tmpWidth = templateWidth.px2mm().toInt();
          textPainter.text = TextSpan(
              text: "${tmpWidth - lineInt}",
              style: TextStyle(color: textColor, fontSize: 10));
          textPainter.layout();
          textPainter.paint(
              canvas, Offset((i.mm2px() - 5) * scale, lineHeight));
        }
      }
    } else {
      for (int i = 0; i < width; i++) {
        int lineInt = i - offset.dy.px2mm().toInt();
        var lineHeight = lineInt % 10 == 0
            ? height
            : lineInt % 5 == 0
                ? height / 2
                : height / 3;
        canvas.drawLine(Offset(0, i.mm2px().toDouble()) * scale,
            Offset(lineHeight, (i.mm2px()) * scale), linePaint);
        // if (RatioUtils().ratio < 2.5 ? i % 20 == 0 : i % 10 == 0) {
        if (scale < 0.5 ? lineInt % 20 == 0 : lineInt % 10 == 0) {
          // textPainter.text = TextSpan(
          //     text: "$i", style: TextStyle(color: textColor, fontSize: 10));
          textPainter.text = TextSpan(children: [
            if (lineInt < 0)
              TextSpan(
                  text: '-', style: TextStyle(color: textColor, fontSize: 14)),
            TextSpan(
                text: '$lineInt'.replaceAll('-', ''),
                style: TextStyle(color: textColor, fontSize: 10))
          ]);
          textPainter.layout();
          canvas.save();
          double textWidth = textPainter.width, textHeight = textPainter.height;
          canvas.translate(lineHeight, (i.mm2px()) * scale);
          canvas.rotate(-pi / 2);
          textPainter.paint(canvas, Offset(-textWidth / 4, -textHeight / 4));
          canvas.restore();
        }
      }
    }
  }

  @override
  bool shouldRepaint(covariant final CustomPainter oldDelegate) {
    var old = oldDelegate as RulerPainterDesktop;
    return old.width != width ||
        old.height != height ||
        old.scale != scale ||
        old.offset.dx != offset.dx ||
        old.offset.dy != offset.dy;
  }
}
