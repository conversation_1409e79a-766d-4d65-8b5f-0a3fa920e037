import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:niimbot_flutter_canvas/extensions/template_data.dart';
import 'package:provider/provider.dart';

import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_flutter_canvas/widgets/box_frame/ruler_painter.dart';

final Logger _logger = Logger("CanvasRuler", on: kDebugMode);

class CanvasRuler extends StatefulWidget {
  final Axis axis;

  final Offset startOffset;

  final Offset canvasOffset;

  final double scale;

  /// 标尺尺寸
  final num size;

  const CanvasRuler({
    required this.startOffset,
    required this.canvasOffset,
    required this.scale,
    required this.size,
    super.key,
    this.axis = Axis.horizontal,
  });

  @override
  CanvasRulerState createState() => CanvasRulerState();
}

class CanvasRulerState extends State<CanvasRuler> {
  double scale = 1;

  Offset offset = Offset.zero;

  void jump(final Offset offset, final double scale) {
    this.offset = offset;
    this.scale = scale;
    setState(() {});
  }

  @override
  void initState() {
    super.initState();
    scale = widget.scale;
    offset = widget.startOffset + widget.canvasOffset;
  }

  @override
  Widget build(final BuildContext context) {
    final size = context.select<CanvasStore, Size>(
        (final v) => v.canvasData.getView(v.rotate).size);
    _logger.log(
        "scale: $scale, startOffset: ${widget.startOffset}, canvasOffset: ${widget.canvasOffset},size : $size");
    return SizedBox(
      width: widget.axis == Axis.horizontal ? 2000 / scale : 12,
      height: widget.axis == Axis.horizontal ? 12 : 2000 / scale,
      child: CustomPaint(
        painter: RulerPainter(widget.axis, scale, offset / scale,
            size.width.toDouble(), size.height.toDouble()),
      ),
    );
  }
}
