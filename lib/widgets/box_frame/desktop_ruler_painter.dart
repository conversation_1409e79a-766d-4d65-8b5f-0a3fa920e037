import 'dart:math';

import 'package:flutter/cupertino.dart';
import 'package:netal_plugin/utils/ratio.dart';

import 'package:niimbot_flutter_canvas/utils/theme_color.dart';

class DesktopRulerPainter extends CustomPainter {
  /// 标尺方向
  final Axis axis;

  // 标尺刻度尺个数
  final int tickCount;

  /// 标尺缩放比例
  final double scale;

  /// 标尺偏移
  final Offset offset;

  /// 刻度颜色
  final Color lineColor;

  /// 文字颜色
  final Color textColor;

  /// 文本方向
  final TextDirection direction;

  const DesktopRulerPainter({
    required this.axis,
    required this.scale,
    required this.offset,
    required this.tickCount,
    required this.lineColor,
    required this.textColor,
    this.direction = TextDirection.ltr,
  });

  @override
  void paint(final Canvas canvas, final Size size) {
    _drawBackground(canvas, size);
    _drawTickLine(canvas);
  }

  // 绘制背景区块
  _drawBackground(final Canvas canvas, final Size size) {
    Paint backgroundPaint = Paint();
    backgroundPaint.color = ThemeColor.background;
    backgroundPaint.style = PaintingStyle.fill;
    Rect rect = Rect.fromLTWH(0, 0, size.width, size.height);
    canvas.drawRect(rect, backgroundPaint);
    Paint linePaint = Paint();
    linePaint.color = lineColor;
    linePaint.style = PaintingStyle.stroke;
    linePaint.strokeWidth = 1;
    if (axis == Axis.horizontal) {
      canvas.drawLine(
          Offset(0, size.height), Offset(size.width, size.height), linePaint);
    } else {
      canvas.drawLine(
          Offset(size.width, 0), Offset(size.width, size.height), linePaint);
    }
  }

  /// 绘制刻度线和刻度值
  _drawTickLine(final Canvas canvas) {
    /// 刻度线偏移
    final offsetTickDouble =
        (axis == Axis.horizontal ? offset.dx - 20 : offset.dy - 20).px2mm() / scale;
    final int offsetTick = offsetTickDouble.ceil();
    final paddingValue = 20 - (offsetTick - offsetTickDouble).mm2px() * scale;
    Paint linePaint = Paint();
    linePaint.color = lineColor;
    linePaint.style = PaintingStyle.stroke;
    linePaint.strokeWidth = 1;
    TextPainter textPainter = TextPainter(textAlign: TextAlign.center);
    textPainter.textDirection = direction;

    for (int tickValue = 0; tickValue <= tickCount; tickValue++) {
      /// 实际刻度值
      final int currentTickValue = tickValue - offsetTick;

      /// 刻度线长
      final double tickLength = currentTickValue % 5 == 0 ? 9 : 6;

      /// （画板偏移+刻度间隔）*画板缩放倍率
      final position = (tickValue.mm2px() * scale + paddingValue).toDouble();
      if (axis == Axis.horizontal) {
        canvas.drawLine(
            Offset(position, 0), Offset(position, tickLength), linePaint);
      } else {
        canvas.drawLine(
            Offset(0, position), Offset(tickLength, position), linePaint);
      }
      _drawTickText(canvas, textPainter, currentTickValue, position);
    }
  }

  // 绘制刻度值
  _drawTickText(final Canvas canvas, final TextPainter textPainter,
      final int currentTickValue, final double position) {
    if (currentTickValue % (scale < 0.5 ? 20 : 10) == 0) {
      textPainter.text = TextSpan(children: [
        if (currentTickValue < 0)
          TextSpan(text: '-', style: TextStyle(color: textColor, fontSize: 14)),
        TextSpan(
            text: '$currentTickValue'.replaceAll('-', ''),
            style: TextStyle(color: textColor, fontSize: 10))
      ]);
      textPainter.layout();
      double textWidth = textPainter.width, textHeight = textPainter.height;
      if (axis == Axis.horizontal) {
        textPainter.paint(
            canvas, Offset(position - textWidth / 2, 8 - textHeight / 4));
      } else {
        canvas.save();
        canvas.translate(8, position + textHeight / 2);
        canvas.rotate(-pi / 2);
        textPainter.paint(canvas, Offset(0, -textHeight / 4));
        canvas.restore();
      }
    }
  }

  @override
  bool shouldRepaint(covariant final CustomPainter oldDelegate) {
    var old = oldDelegate as DesktopRulerPainter;
    return old.tickCount != tickCount ||
        old.scale != scale ||
        old.offset != offset ||
        old.direction != direction;
  }
}
