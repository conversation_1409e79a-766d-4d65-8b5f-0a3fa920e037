import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';

import 'package:netal_plugin/utils/ratio.dart';
import 'package:niimbot_flutter_canvas/extensions/template_data.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:provider/provider.dart';

import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_flutter_canvas/widgets/box_frame/desktop_ruler_painter.dart';

final Logger _logger = Logger("DesktopCanvasRuler", on: kDebugMode);

class DesktopCanvasRuler extends StatelessWidget {
  /// 标尺方向
  final Axis axis;

  /// 标尺大小
  final num size;

  /// 标尺缩放比例
  final double scale;

  /// 标尺偏移
  final Offset offset;

  const DesktopCanvasRuler({
    super.key,
    this.axis = Axis.horizontal,
    required this.size,
    required this.scale,
    required this.offset,
  });

  @override
  Widget build(final BuildContext context) {
    final niimbotColors = NiimbotTheme.of(context).colors;
    final TextDirection direction = Directionality.of(context);
    final double width = axis == Axis.horizontal ? size.toDouble() : 20;
    final double height = axis == Axis.horizontal ? 20 : size.toDouble();
    final tickCount = ((size - 20).px2mm() / scale).ceil();

    return CustomPaint(
      size: Size(width, height),
      painter: DesktopRulerPainter(
        axis: axis,
        tickCount: tickCount,
        scale: scale,
        offset: offset,
        direction: direction,
        lineColor: niimbotColors.textFillColorQuarternary,
        textColor: niimbotColors.textFillColorTertiary,
      ),
      child: RulerDesktopMark(
        width: width,
        height: height,
        axis: axis,
        scale: scale,
        offset: offset,
        direction: direction,
      ),
    );
  }
}

/// 标记蓝色区块
class RulerDesktopMark extends StatelessWidget {
  final double width;
  final double height;

  /// 标尺方向
  final Axis axis;

  /// 标尺缩放比例
  final double scale;

  /// 标尺偏移
  final Offset offset;

  /// 文本方向
  final TextDirection direction;

  const RulerDesktopMark({
    super.key,
    required this.width,
    required this.height,
    required this.axis,
    required this.scale,
    required this.offset,
    required this.direction,
  });

  @override
  Widget build(final BuildContext context) {
    final niimbotColors = NiimbotTheme.of(context).colors;

    /// 用于渲染标尺元素的选中
    final markRect = context.select<CanvasStore, Rect>((final v) =>
        v.getSelectRect()?.mm2px() ?? v.canvasData.getView(v.rotate).mm2px());
    return Stack(
      children: [
        SizedBox(width: width, height: height),
        Positioned.directional(
          textDirection: direction,
          start:
              axis == Axis.horizontal ? offset.dx + markRect.left * scale : 0,
          top: axis == Axis.horizontal ? 0 : offset.dy + markRect.top * scale,
          child: Container(
            width: axis == Axis.horizontal ? markRect.width * scale : 20,
            height: axis == Axis.horizontal ? 20 : markRect.height * scale,
            color: niimbotColors.systemFillColorSelected.withOpacity(0.1),
          ),
        )
      ],
    );
  }
}
