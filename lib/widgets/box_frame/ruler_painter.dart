import 'package:flutter/cupertino.dart';
import 'package:flutter_canvas_plugins_interface/utils/display_util.dart';
import 'package:netal_plugin/utils/ratio.dart';

import 'package:niimbot_flutter_canvas/utils/theme_color.dart';

class RulerPainter extends CustomPainter {
  /// 标尺方向
  final Axis axis;

  /// 标尺宽度
  final double width;

  /// 标尺高度
  final double height;

  /// 标尺缩放比例
  final double scale;

  /// 标尺偏移
  final Offset offset;

  /// 画板宽度
  final double templateWidth;

  /// 画板高度
  final double templateHeight;

  /// 刻度颜色
  final Color lineColor;

  /// 文字颜色
  final Color textColor;

  final TextDirection direction;

  const RulerPainter(
    this.axis,
    this.scale,
    this.offset,
    this.templateWidth,
    this.templateHeight, {
    this.width = 2000,
    this.height = 12,
    this.lineColor = ThemeColor.COLOR_999999,
    this.textColor = ThemeColor.COLOR_999999,
    this.direction = TextDirection.ltr,
  });

  @override
  void paint(final Canvas canvas, final Size size) {
    if (direction == TextDirection.ltr) {
      _drawLine(canvas);
      _drawScale(canvas);
    } else {
      _drawLineRTL(canvas);
      _drawScaleRTL(canvas);
    }
  }

  _drawLine(final Canvas canvas) {
    Paint linePaint = Paint();
    linePaint.color = lineColor;
    linePaint.style = PaintingStyle.stroke;
    linePaint.strokeWidth = 1;
    if (axis == Axis.horizontal) {
      canvas.drawLine(Offset(offset.dx, 0) * scale,
          Offset(offset.dx + width, 0) * scale, linePaint);
    } else {
      canvas.drawLine(Offset(0, offset.dy) * scale,
          Offset(0, offset.dy + width) * scale, linePaint);
    }
  }

  _drawScale(final Canvas canvas) {
    Paint linePaint = Paint();
    linePaint.color = lineColor;
    linePaint.style = PaintingStyle.stroke;
    linePaint.strokeWidth = 1;
    TextPainter textPainter = TextPainter();
    textPainter.textDirection = direction;
    if (axis == Axis.horizontal) {
      for (int i = 0; i < templateWidth + 6; i++) {
        var lineHeight = i % 10 == 0
            ? height
            : i % 5 == 0
                ? height / 2
                : height / 3;

        /// （画板偏移+刻度间隔）*画板缩放倍率
        canvas.drawLine(Offset(offset.dx + i.mm2px(), 0) * scale,
            Offset((offset.dx + i.mm2px()) * scale, lineHeight), linePaint);

        /// 整十显示刻度
        if (RatioUtils().ratio < 2.5 ? i % 20 == 0 : i % 10 == 0) {
          textPainter.text = TextSpan(
              text: "$i", style: TextStyle(color: textColor, fontSize: 12));
          textPainter.layout();
          textPainter.paint(
              canvas, Offset((offset.dx + i.mm2px() - 6) * scale, lineHeight));
        }
      }
    } else {
      for (int i = 0; i < templateHeight + 6; i++) {
        var lineHeight = i % 10 == 0
            ? height
            : i % 5 == 0
                ? height / 2
                : height / 3;
        canvas.drawLine(Offset(0, offset.dy + i.mm2px()) * scale,
            Offset(lineHeight, (offset.dy + i.mm2px()) * scale), linePaint);
        if (RatioUtils().ratio < 2.5 ? i % 20 == 0 : i % 10 == 0) {
          textPainter.text = TextSpan(
              text: "$i", style: TextStyle(color: textColor, fontSize: 12));
          textPainter.layout();
          textPainter.paint(
              canvas, Offset(lineHeight, (offset.dy + i.mm2px() - 6) * scale));
        }
      }
    }
  }

  _drawLineRTL(final Canvas canvas) {
    Paint linePaint = Paint();
    linePaint.color = lineColor;
    linePaint.style = PaintingStyle.stroke;
    linePaint.strokeWidth = 1;
    if (axis == Axis.horizontal) {
      canvas.drawLine(
          Offset(templateWidth.mm2px() + offset.dx, 0) * scale,
          Offset(templateWidth.mm2px() + offset.dx - width, 0) * scale,
          linePaint);
    } else {
      canvas.drawLine(Offset(0, offset.dy) * scale,
          Offset(0, offset.dy + width) * scale, linePaint);
    }
  }

  _drawScaleRTL(final Canvas canvas) {
    Paint linePaint = Paint();
    linePaint.color = lineColor;
    linePaint.style = PaintingStyle.stroke;
    linePaint.strokeWidth = 1;
    TextPainter textPainter = TextPainter();
    textPainter.textDirection = direction;
    if (axis == Axis.horizontal) {
      int start = templateWidth.toInt() + 6;

      ///+6:比实际尺寸多出几个刻度
      for (int i = start; i >= 0; i--) {
        /// 控制刻度线的高度
        var lineHeight = (start - i) % 10 == 0
            ? height
            : (start - i) % 5 == 0
                ? height / 2
                : height / 3;

        /// （画板宽度 + 画板偏移+刻度间隔）*画板缩放倍率
        canvas.drawLine(
            Offset(templateWidth.mm2px() + offset.dx + (i - start).mm2px(), 0) *
                scale,
            Offset(
                (templateWidth.mm2px() + offset.dx + (i - start).mm2px()) *
                    scale,
                lineHeight),
            linePaint);
        if (RatioUtils().ratio < 2.5
            ? (start - i) % 20 == 0
            : (start - i) % 10 == 0) {
          textPainter.text = TextSpan(
              text: "${start - i}",
              style: TextStyle(color: textColor, fontSize: 12));
          textPainter.layout();
          textPainter.paint(
              canvas,
              Offset(
                  (templateWidth.mm2px() +
                          offset.dx +
                          (i - start).mm2px() -
                          6) *
                      scale,
                  lineHeight));
        }
      }
    } else {
      for (int i = 0; i < templateHeight + 6; i++) {
        var lineHeight = i % 10 == 0
            ? height
            : i % 5 == 0
                ? height / 2
                : height / 3;
        canvas.drawLine(
            Offset(DisplayUtil.deviceSize.width - lineHeight,
                (offset.dy + i.mm2px()) * scale),
            Offset(
                DisplayUtil.deviceSize.width, (offset.dy + i.mm2px()) * scale),
            linePaint);
        if (RatioUtils().ratio < 2.5 ? i % 20 == 0 : i % 10 == 0) {
          int space = i / 10 > 9
              ? 10
              : i / 10 > 0
                  ? 5
                  : 0;
          textPainter.text = TextSpan(
              text: "$i", style: TextStyle(color: textColor, fontSize: 12));
          textPainter.layout();
          textPainter.paint(
              canvas,
              Offset(DisplayUtil.deviceSize.width - lineHeight - 12 - space,
                  (offset.dy + i.mm2px() - 6) * scale));
        }
      }
    }
  }

  @override
  bool shouldRepaint(covariant final CustomPainter oldDelegate) {
    var old = oldDelegate as RulerPainter;
    return old.width != width ||
        old.height != height ||
        old.scale != scale ||
        old.offset.dx != offset.dx ||
        old.offset.dy != offset.dy;
  }
}
