import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/box_transform/models/enums.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/interactive/draggable_point.dart';
import 'package:niimbot_ui/styles/theme.dart';

class ImageResizeLine extends HookWidget {
  final double rotate;
  final double width;
  final double height;
  final double lineWidth;
  final AxisDirection direction;
  final Function(double left, double top, double width, double height,
      {MouseCursor? cursor}) onDragSize;
  final VoidCallback onDragEnd;

  const ImageResizeLine({
    super.key,
    required this.rotate,
    required this.width,
    required this.height,
    required this.lineWidth,
    required this.direction,
    required this.onDragSize,
    required this.onDragEnd,
  });

  @override
  Widget build(final BuildContext context) {
    final niimbotTheme = NiimbotTheme.of(context);

    AxisDirection getDirection(
        final double angle, final AxisDirection direction) {
      if (angle == 90) {
        return direction == AxisDirection.up
            ? AxisDirection.right
            : direction == AxisDirection.right
                ? AxisDirection.down
                : direction == AxisDirection.down
                    ? AxisDirection.left
                    : AxisDirection.up;
      } else if (angle == 180) {
        return direction == AxisDirection.up
            ? AxisDirection.down
            : direction == AxisDirection.right
                ? AxisDirection.left
                : direction == AxisDirection.down
                    ? AxisDirection.up
                    : AxisDirection.right;
      } else if (angle == 270) {
        return direction == AxisDirection.up
            ? AxisDirection.left
            : direction == AxisDirection.right
                ? AxisDirection.up
                : direction == AxisDirection.down
                    ? AxisDirection.right
                    : AxisDirection.down;
      }
      return direction;
    }

    final directionState = useMemoized(() {
      final angle = rotate % 360;
      return getDirection(angle, direction);
    }, [rotate, direction]);

    final cursor = useMemoized(() {
      return [AxisDirection.up, AxisDirection.down].contains(directionState)
          ? SystemMouseCursors.resizeUp
          : SystemMouseCursors.resizeLeft;
    }, [directionState]);

    final viewAxisHor = useMemoized(() {
      return [
        AxisDirection.up,
        AxisDirection.down,
      ].contains(direction);
    }, [direction]);

    onDrag(final Offset offset) {
      double dx = offset.dx;
      double dy = offset.dy;
      final angle = rotate % 360;
      if (angle == 90) {
        dx = offset.dy;
        dy = -offset.dx;
      } else if (angle == 180) {
        dx = -offset.dx;
        dy = -offset.dy;
      } else if (angle == 270) {
        dx = -offset.dy;
        dy = offset.dx;
      }
      final originDirection = getDirection((360 - angle), directionState);
      if (originDirection == AxisDirection.up) {
        onDragSize(0, dy, 0, -dy, cursor: cursor);
      } else if (originDirection == AxisDirection.down) {
        onDragSize(0, 0, 0, dy, cursor: cursor);
      } else if (originDirection == AxisDirection.left) {
        onDragSize(dx, 0, -dx, 0, cursor: cursor);
      } else if (originDirection == AxisDirection.right) {
        onDragSize(0, 0, dx, 0, cursor: cursor);
      }
    }

    return MouseRegion(
      cursor: cursor,
      child: DraggablePoint(
        // mode: PositionMode.local,
        onDrag: onDrag,
        onDragEnd: onDragEnd,
        child: Wrap(
          direction: viewAxisHor ? Axis.vertical : Axis.horizontal,
          children: [
            SizedBox(
              width: width,
              height: height,
            ),
            Container(
              width: viewAxisHor ? width : lineWidth,
              height: viewAxisHor ? lineWidth : height,
              color: niimbotTheme.colors.systemFillColorSelected,
            ),
            SizedBox(
              width: width,
              height: height,
            ),
          ],
        ),
        // child: Container(
        //   width: width,
        //   height: height,
        //   decoration: BoxDecoration(
        //     border: Border(
        //       top: direction == AxisDirection.up
        //           ? BorderSide(
        //               color: niimbotTheme.colors.systemFillColorSelected,
        //               width: lineWidth,
        //             )
        //           : BorderSide.none,
        //       bottom: direction == AxisDirection.down
        //           ? BorderSide(
        //               color: niimbotTheme.colors.systemFillColorSelected,
        //               width: lineWidth)
        //           : BorderSide.none,
        //       left: direction == AxisDirection.left
        //           ? BorderSide(
        //               color: niimbotTheme.colors.systemFillColorSelected,
        //               width: lineWidth,
        //             )
        //           : BorderSide.none,
        //       right: direction == AxisDirection.right
        //           ? BorderSide(
        //               color: niimbotTheme.colors.systemFillColorSelected,
        //               width: lineWidth,
        //             )
        //           : BorderSide.none,
        //     ),
        //   ),
        // ),
      ),
    );
  }
}

// 图片大小拖拽点
class ImageResizePoint extends HookWidget {
  final Quadrant quadrant;
  final double rotate;
  final double size;
  final Function(double left, double top, double width, double height,
      {MouseCursor? cursor}) onDragSize;
  final VoidCallback onDragEnd;

  const ImageResizePoint({
    super.key,
    required this.quadrant,
    required this.rotate,
    required this.size,
    required this.onDragSize,
    required this.onDragEnd,
  });

  @override
  Widget build(final BuildContext context) {
    Quadrant getQuadrant(final double angle, final Quadrant quadrant) {
      if (angle == 90) {
        return quadrant == Quadrant.topLeft
            ? Quadrant.topRight
            : quadrant == Quadrant.topRight
                ? Quadrant.bottomRight
                : quadrant == Quadrant.bottomRight
                    ? Quadrant.bottomLeft
                    : Quadrant.topLeft;
      } else if (angle == 180) {
        return quadrant == Quadrant.topLeft
            ? Quadrant.bottomRight
            : quadrant == Quadrant.topRight
                ? Quadrant.bottomLeft
                : quadrant == Quadrant.bottomRight
                    ? Quadrant.topLeft
                    : Quadrant.topRight;
      } else if (angle == 270) {
        return quadrant == Quadrant.topLeft
            ? Quadrant.bottomLeft
            : quadrant == Quadrant.topRight
                ? Quadrant.topLeft
                : quadrant == Quadrant.bottomRight
                    ? Quadrant.topRight
                    : Quadrant.bottomRight;
      }
      return quadrant;
    }

    final quadrantState = useMemoized(() {
      final angle = rotate % 360;
      return getQuadrant(angle, quadrant);
    }, [rotate, quadrant]);

    final cursor = useMemoized(() {
      return quadrantState == Quadrant.topLeft
          ? SystemMouseCursors.resizeUpLeft
          : quadrantState == Quadrant.topRight
              ? SystemMouseCursors.resizeUpRight
              : quadrantState == Quadrant.bottomLeft
                  ? SystemMouseCursors.resizeDownLeft
                  : SystemMouseCursors.resizeDownRight;
    }, [quadrantState]);

    onDrag(final Offset offset) {
      double dx = offset.dx;
      double dy = offset.dy;
      final angle = rotate % 360;
      if (angle == 90) {
        dx = offset.dy;
        dy = -offset.dx;
      } else if (angle == 180) {
        dx = -offset.dx;
        dy = -offset.dy;
      } else if (angle == 270) {
        dx = -offset.dy;
        dy = offset.dx;
      }
      final originQuadrant = getQuadrant((360 - angle), quadrantState);
      if (originQuadrant == Quadrant.topLeft) {
        onDragSize(dx, dy, -dx, -dy, cursor: cursor);
      } else if (originQuadrant == Quadrant.topRight) {
        onDragSize(0, dy, dx, -dy, cursor: cursor);
      } else if (originQuadrant == Quadrant.bottomLeft) {
        onDragSize(dx, 0, -dx, dy, cursor: cursor);
      } else if (originQuadrant == Quadrant.bottomRight) {
        onDragSize(0, 0, dx, dy, cursor: cursor);
      }
    }

    return MouseRegion(
      cursor: cursor,
      child: DraggablePoint(
        // mode: PositionMode.local,
        onDrag: onDrag,
        onDragEnd: onDragEnd,
        child: Container(
          width: size,
          height: size,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
          ),
        ),
      ),
    );
  }
}
