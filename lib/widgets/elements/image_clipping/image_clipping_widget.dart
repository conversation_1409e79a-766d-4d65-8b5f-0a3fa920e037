import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_flutter_canvas/widgets/components/stack2.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/box_transform/models/enums.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/image_clipping/image_resize_line.dart';
import 'package:niimbot_ui/hooks/use_mounted.dart';
import 'package:provider/provider.dart';

class ImageClippingWidget extends HookWidget {
  final double rotate;
  final double scale;

  const ImageClippingWidget({
    super.key,
    required this.rotate,
    required this.scale,
  });

  @override
  Widget build(final BuildContext context) {
    return Positioned.fill(
      child: LayoutBuilder(
          builder: (final BuildContext ctx, final BoxConstraints constraints) {
        return _ImageClippingWidget(
          rotate: rotate,
          scale: scale,
          constraints: constraints,
        );
      }),
    );
  }
}

class _ImageClippingWidget extends HookWidget {
  final BoxConstraints constraints;
  final double rotate;
  final double scale;

  const _ImageClippingWidget({
    super.key,
    required this.constraints,
    required this.rotate,
    required this.scale,
  });

  @override
  Widget build(final BuildContext context) {

    /// 可剪裁最大区域
    final maxRect = useMemoized(() {
      return Rect.fromLTWH(0, 0, constraints.maxWidth, constraints.maxHeight);
    }, [constraints]);
    final rectState = useState<Rect>(maxRect);
    final draggingMouseCursors = useState<MouseCursor?>(null);

    useMounted((final _) {
      final clippingRect = context.read<CanvasStore>().clippingRect;
      if (clippingRect != null) {
        final maxWidth = constraints.maxWidth,
            maxHeight = constraints.maxHeight;
        rectState.value = Rect.fromLTRB(
            clippingRect.left * maxWidth,
            clippingRect.top * maxHeight,
            clippingRect.right * maxWidth,
            clippingRect.bottom * maxHeight);
      } else {
        rectState.value = maxRect;
      }
    }, [scale]);

    final rect = rectState.value;

    void onDragSize(final double left, final double top, final double width,
        final double height,
        {final MouseCursor? cursor}) {
      draggingMouseCursors.value = cursor;
      double newLeft = rectState.value.left + left / scale;
      double newTop = rectState.value.top + top / scale;
      double newWidth = rectState.value.width + width / scale;
      double newHeight = rectState.value.height + height / scale;

      // 确保宽高不小于最小值
      newWidth = newWidth.clamp(20.0 / scale, double.infinity);
      newHeight = newHeight.clamp(20.0 / scale, double.infinity);

      final newRect = Rect.fromLTWH(
        newLeft,
        newTop,
        newWidth,
        newHeight,
      );

      if (maxRect.topLeft <= newRect.topLeft &&
          maxRect.bottomRight >= newRect.bottomRight) {
        rectState.value = newRect;
        context.read<CanvasStore>().updateClippingRect(Rect.fromLTRB(
            newRect.left / maxRect.width,
            newRect.top / maxRect.height,
            newRect.right / maxRect.width,
            newRect.bottom / maxRect.height));
      }
    }

    void onDragEnd() {
      draggingMouseCursors.value = null;
    }

    final lineWidth = useMemoized(() {
      return 4 / scale;
    }, [scale]);

    /// 绘制变更大小的线
    Widget buildResizeLine({
      required final double width,
      required final double height,
      required final AxisDirection direction,
      required final double leftOffset,
      required final double topOffset,
    }) {
      return Positioned.directional(
        textDirection: TextDirection.ltr,
        start: leftOffset,
        top: topOffset,
        child: ImageResizeLine(
          rotate: rotate,
          width: width,
          height: height,
          lineWidth: lineWidth / 2,
          direction: direction,
          onDragSize: onDragSize,
          onDragEnd: onDragEnd,
        ),
      );
    }

    /// 绘制变更大小的点
    Widget buildResizePoint({
      required final Quadrant quadrant,
      required final double leftOffset,
      required final double topOffset,
    }) {
      return Positioned.directional(
        textDirection: TextDirection.ltr,
        start: leftOffset,
        top: topOffset,
        child: ImageResizePoint(
          size: 16 / scale,
          quadrant: quadrant,
          rotate: rotate,
          onDragSize: onDragSize,
          onDragEnd: onDragEnd,
        ),
      );
    }

    return Stack2(
      clipBehavior: Clip.none,
      children: [
        MouseRegion(
          cursor: draggingMouseCursors.value ?? SystemMouseCursors.basic,
          child: SizedBox(
            width: constraints.maxWidth,
            height: constraints.maxHeight,
            child: CustomPaint(
              painter: _HollowRectanglePainter(
                rect: rect,
                cornerSize: 10 / scale,
                strokeWidth: lineWidth,
                color: Colors.blue,
              ),
            ),
          ),
        ),

        Positioned.directional(
          textDirection: TextDirection.ltr,
          start: rect.left,
          top: rect.top,
          child: GestureDetector(
            /// 点击事件避免触发父级点击事件
            onTap: () {},
            child: Container(
              width: rect.width,
              height: rect.height,
              color: Colors.transparent,
              // decoration: BoxDecoration(
              //   border: Border.all(
              //     color: Colors.blue,
              //   )
              // ),
            ),
          ),
        ),

        /// 上边
        buildResizeLine(
          width: rect.width,
          height: lineWidth,
          direction: AxisDirection.up,
          leftOffset: rect.left,
          topOffset: rect.top - lineWidth,
        ),

        /// 下边
        buildResizeLine(
          width: rect.width,
          height: lineWidth,
          direction: AxisDirection.down,
          leftOffset: rect.left,
          topOffset: rect.bottom - lineWidth * 1.5,
        ),

        /// 左边
        buildResizeLine(
          width: lineWidth,
          height: rect.height,
          direction: AxisDirection.left,
          leftOffset: rect.left - lineWidth,
          topOffset: rect.top,
        ),

        /// 右边
        buildResizeLine(
          width: lineWidth,
          height: rect.height,
          direction: AxisDirection.right,
          leftOffset: rect.right - lineWidth * 1.5,
          topOffset: rect.top,
        ),

        buildResizePoint(
          quadrant: Quadrant.topLeft,
          leftOffset: rect.left - lineWidth,
          topOffset: rect.top - lineWidth,
        ),

        buildResizePoint(
          quadrant: Quadrant.topRight,
          leftOffset: rect.right - lineWidth * 3,
          topOffset: rect.top - lineWidth,
        ),

        buildResizePoint(
          quadrant: Quadrant.bottomLeft,
          leftOffset: rect.left - lineWidth,
          topOffset: rect.bottom - lineWidth * 3,
        ),

        buildResizePoint(
          quadrant: Quadrant.bottomRight,
          leftOffset: rect.right - lineWidth * 3,
          topOffset: rect.bottom - lineWidth * 3,
        ),
      ],
    );
  }
}

/// 绘制镂空区域
class _HollowRectanglePainter extends CustomPainter {
  /// 镂空区域的位置
  final Rect rect;
  final Color color;

  // 拐角的大小
  final double cornerSize;
  final double strokeWidth;

  const _HollowRectanglePainter({
    required this.rect,
    required this.color,
    required this.cornerSize,
    required this.strokeWidth,
  });

  void _drawCorner(final Path path, final Offset start, final Offset line1,
      final Offset line2, final Canvas canvas, final Paint paint) {
    path
      ..moveTo(start.dx, start.dy)
      ..lineTo(line1.dx, line1.dy)
      ..lineTo(line2.dx, line2.dy);
    canvas.drawPath(path, paint);
  }

  @override
  void paint(final Canvas canvas, final Size size) {
    final Paint paint = Paint()
      ..color = Colors.blue
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth;

    final double padding = strokeWidth / 2;

    final double left = rect.left + padding;
    final double top = rect.top + padding;
    final double right = rect.right - padding;
    final double bottom = rect.bottom - padding;

    // 左上角
    _drawCorner(
      Path(),
      Offset(left + cornerSize, top),
      Offset(left, top),
      Offset(left, top + cornerSize),
      canvas,
      paint,
    );

    // 右上角
    _drawCorner(
      Path(),
      Offset(right - cornerSize, top),
      Offset(right, top),
      Offset(right, top + cornerSize),
      canvas,
      paint,
    );

    // 左下角
    _drawCorner(
      Path(),
      Offset(left, bottom - cornerSize),
      Offset(left, bottom),
      Offset(left + cornerSize, bottom),
      canvas,
      paint,
    );

    // 右下角
    _drawCorner(
      Path(),
      Offset(right - cornerSize, bottom),
      Offset(right, bottom),
      Offset(right, bottom - cornerSize),
      canvas,
      paint,
    );

    // Define the outer rectangle path
    Rect outerRect = Rect.fromLTWH(0, 0, size.width, size.height);
    Path outerPath = Path()..addRect(outerRect);

    // Define the inner hollow area path
    Path innerPath = Path()..addRect(rect);

    // Combine paths using evenOdd fill type to create a hollow effect
    Path combinedPath =
        Path.combine(PathOperation.difference, outerPath, innerPath);

    // Paint the combined path
    Paint paintBg = Paint()
      ..color = Colors.black.withOpacity(0.3)
      ..style = PaintingStyle.fill;

    canvas.drawPath(combinedPath, paintBg);
  }

  @override
  bool shouldRepaint(covariant final CustomPainter oldDelegate) {
    var old = oldDelegate as _HollowRectanglePainter;
    return old.rect != rect;
  }
}
