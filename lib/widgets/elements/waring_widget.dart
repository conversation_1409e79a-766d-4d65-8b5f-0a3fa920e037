import 'package:flutter/material.dart';
import 'package:niimbot_intl/niimbot_intl.dart';

import 'package:niimbot_flutter_canvas/utils/theme_color.dart';

class WaringWidget extends StatelessWidget {
  final Size size;
  final bool showErrorIcon;

  const WaringWidget({super.key, required this.size, final bool? showErrorIcon})
      : showErrorIcon = showErrorIcon ?? false;

  double get warningTextW => 120;

  double get warningTextH => 26;

  @override
  Widget build(final BuildContext context) {
    if (!showErrorIcon && size.width > warningTextW) {
      double offsetX = (size.width - warningTextW) / 2;
      double offsetY = (size.height - warningTextH) / 2;
      return Positioned(
          left: offsetX,
          top: offsetY,
          child: Container(
            width: warningTextW,
            height: warningTextH,
            decoration: BoxDecoration(
                color: ThemeColor.COLOR_FB4B42,
                borderRadius: BorderRadius.circular(40)),
            child: Center(
              child: Text(NiimbotIntl.getIntlMessage("app01066", "不符合编码规范"),
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                      color: Colors.white,
                      fontSize: 13,
                      fontWeight: FontWeight.w400)),
            ),
          ));
    } else {
      double offsetX = (size.width - warningTextH) / 2;
      double offsetY = (size.height - warningTextH) / 2;
      return Positioned(
          left: offsetX,
          top: offsetY,
          child: Center(
            child: Image.asset(
              "assets/common/icon_warning.png",
              package: "niimbot_flutter_canvas",
              fit: BoxFit.cover,
              width: warningTextH,
              height: warningTextH,
            ),
          ));
    }
  }
}
