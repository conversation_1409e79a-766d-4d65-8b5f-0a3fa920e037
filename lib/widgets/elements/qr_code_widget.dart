import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:netal_plugin/netal_image_result.dart';
import 'package:netal_plugin/netal_plugin.dart';
import 'package:netal_plugin/utils/index.dart';
import 'package:netal_plugin/utils/ratio.dart';
import 'package:niimbot_template/models/elements/qr_code_element.dart';
import 'package:niimbot_template/models/template_data.dart';
import 'package:niimbot_template/extensions/template_data.dart';
import 'package:provider/provider.dart';

import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/base_widget.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/waring_widget.dart';

Logger _logger = Logger("QRCodeWidget", on: kDebugMode);

class QRCodeWidget extends BaseElementWidget<QRCodeElement> {
  const QRCodeWidget({
    super.key,
    required super.element,
    super.color,
    super.imageCache,
    super.updateElementNetalInfo,
  });

  @override
  NetalImageResult buildNetalResult(final TemplateData template) {
    return NetalPlugin().generateQRCodeElement(
      element: template.coverElementToNetal(element),
      ratio: RatioUtils().ratio,
      color: color,
    );
  }

  @override
  Widget build(final BuildContext context) {
    final template = context.read<CanvasStore>().canvasData;
    final valid =
        NetalUtils.qrCodeContentCheck(template.coverElementToNetal(element));
    bool deletedAdvanceQRCode = false;
    // if (element.isAdvanceQRCode) {
    /* 为高级二维码 */
    // final advanceQRCodeInfo =
    //     AdvanceQRCodeManager().getAdvanceQRCodeInfo(element);
    // if (advanceQRCodeInfo != null) {
    //   /* 存在高级二维码信息 */
    //   deletedAdvanceQRCode = advanceQRCodeInfo.deleted ?? false;
    // }
    // }
    if (super.imageCache != null) {
      if (deletedAdvanceQRCode || !valid) {
        return Stack(
          children: [
            super.build(context),
            WaringWidget(
              size: Size(
                element.width.mm2px().toDouble(),
                element.height.mm2px().toDouble(),
              ),
            )
          ],
        );
      }
      return super.build(context);
    } else {
      final data = buildNetalResult(template);
      if (data.pixels.isNotEmpty) {
        netalCallback(data, template);
      }
      if ((data.errorCode) > 0 || deletedAdvanceQRCode || !valid) {
        return Stack(
          children: [
            buildImage(data.pixels),
            WaringWidget(
              size: Size(
                element.width.mm2px().toDouble(),
                element.height.mm2px().toDouble(),
              ),
            )
          ],
        );
      }
      if (data.pixels.isNotEmpty) {
        return buildImage(data.pixels);
      }
      _logger.log(element, 'imageData is Empty');
      return const SizedBox.shrink();
      // return FutureBuilder(
      //     future: data,
      //     builder: (_, snapshot) {
      //       final data = snapshot.data;
      //       if (data == null) return Container();
      //       if (data.pixels.isNotEmpty) {
      //         netalCallback(data);
      //       }
      //       if ((data.errorCode) > 0 || deletedAdvanceQRCode || !valid) {
      //         return Stack(
      //           children: [
      //             buildImage(data.pixels),
      //             WaringWidget(
      //               size: Size(
      //                 element.width.mm2px().toDouble(),
      //                 element.height.mm2px().toDouble(),
      //               ),
      //             )
      //           ],
      //         );
      //       }
      //       if (data.pixels.isNotEmpty) {
      //         return buildImage(data.pixels);
      //       }
      //       _logger.log(element, 'imageData is Empty');
      //       return const SizedBox.shrink();
      //     });
    }
  }
}
