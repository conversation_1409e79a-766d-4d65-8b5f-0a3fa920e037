import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';

import 'package:netal_plugin/netal_image_result.dart';
import 'package:netal_plugin/netal_plugin.dart';
import 'package:netal_plugin/utils/ratio.dart';
import 'package:niimbot_template/extensions/template_data.dart';
import 'package:niimbot_template/models/elements/line_element.dart';

import 'package:niimbot_flutter_canvas/provider/models/base_element_updater.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/base_widget.dart';
import 'package:niimbot_template/models/template_data.dart';

Logger _logger = Logger("LineWidget", on: kDebugMode);

class LineWidget extends BaseElementWidget<LineElement> {
  const LineWidget({
    super.key,
    required super.element,
    super.color,
    super.imageCache,
    super.updateElementNetalInfo,
  });

  @override
  netalCallback(final NetalImageResult netalRes, final TemplateData template) {
    // double width = netalRes.width.px2mm().toDouble();
    WidgetsBinding.instance.addPostFrameCallback((final _) {
      updateElementNetalInfo?.call(
          image: netalRes,
          id: element.id,
          updater: BaseElementUpdater(height: element.height));
    });
  }

  @override
  NetalImageResult buildNetalResult(final TemplateData template) {
    return NetalPlugin().generateLineElement(
      element: template.coverElementToNetal(element),
      ratio: RatioUtils().ratio,
      color: color,
    );
  }
}
