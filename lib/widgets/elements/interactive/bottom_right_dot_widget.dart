import 'package:flutter/material.dart';

import 'package:niimbot_flutter_canvas/widgets/elements/interactive/constants.dart';

class BottomRightDotWidget extends StatelessWidget {
  final double scale;
  final Color? color;

  const BottomRightDotWidget(this.scale, {super.key, this.color});

  @override
  Widget build(final BuildContext context) {
    return Container(
      alignment: Alignment.center,
      width: InteractiveConstants.cornerDiameter / scale,
      height: InteractiveConstants.cornerDiameter / scale,
      decoration: const BoxDecoration(
        shape: BoxShape.circle,
        color: Colors.transparent,
      ),
      child: Container(
        alignment: Alignment.center,
        width: 12 / scale,
        height: 12 / scale,
        decoration: BoxDecoration(
          boxShadow: null,
          shape: BoxShape.circle,
          border: Border.all(
              color: Colors.white,
              width: 2,
              strokeAlign: BorderSide.strokeAlignOutside),
          color: color,
        ),
      ),
    );
  }
}
