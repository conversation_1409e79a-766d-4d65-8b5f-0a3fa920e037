import 'dart:ui';

enum DragType { nothing, angle, position, size }

class DragUpdate {
  const DragUpdate({
    required this.dragType,
    required this.rotate,
    required this.position,
    required this.size,
    required this.constraints,
    required this.delta,
  });

  final DragType dragType;

  final double rotate;

  final Offset position;
  final Offset delta;

  final Size size;

  final Size constraints;

  @override
  String toString() {
    return "[ angle = $rotate , position = $position ,size = $size ,constraints = $constraints , delta = $delta]";
  }
}
