import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';

import 'package:niimbot_flutter_canvas/widgets/detector/niimbot_gesture_recognizer_conflicter.dart';

Logger _logger = Logger("DraggablePoint", on: kDebugMode);

enum PositionMode { local, global }

class DraggablePoint extends StatefulWidget {
  const DraggablePoint({
    super.key,
    required this.child,
    this.onDrag,
    this.onScale,
    this.onRotate,
    this.onTap,
    this.onTapDown,
    this.mode = PositionMode.global,
    this.onDragEnd,
  });

  final Widget child;
  final PositionMode mode;
  final ValueSetter<Offset>? onDrag;
  final ValueSetter<double>? onScale;
  final ValueSetter<double>? onRotate;
  final VoidCallback? onTap;
  final GestureTapDownCallback? onTapDown;
  final VoidCallback? onDragEnd;

  @override
   createState() => _DraggablePointState();
}

class _DraggablePointState extends State<DraggablePoint> {
  late Offset initPoint;
  var baseScaleFactor = 1.0;
  var scaleFactor = 1.0;
  var baseAngle = 0.0;
  var angle = 0.0;

  @override
  Widget build(final BuildContext context) {
    return NiimbotCustomItemScaleGestureDetector(
      gestureSettings: widget.onDrag == null
          ? null
          : const DeviceGestureSettings(touchSlop: 2),
      behavior: (widget.mode == PositionMode.global
          ? HitTestBehavior.translucent
          : HitTestBehavior.opaque),
      mode: (widget.mode == PositionMode.global
          ? NiimbotPanGesturePositionMode.global
          : NiimbotPanGesturePositionMode.local),
      onTap: widget.onTap,
      onTapDown: widget.onTapDown,
      onScaleStart: (final details) {
        switch (widget.mode) {
          case PositionMode.global:
            initPoint = details.focalPoint;
            break;
          case PositionMode.local:
            initPoint = details.localFocalPoint;
            break;
        }
        if (details.pointerCount > 1) {
          // baseAngle = angle;
          // baseScaleFactor = scaleFactor;
          // widget.onRotate?.call(baseAngle);
          // widget.onScale?.call(baseScaleFactor);
        }
      },
      onScaleUpdate: (final details) {
        switch (widget.mode) {
          case PositionMode.global:
            final dx = details.focalPoint.dx - initPoint.dx;
            final dy = details.focalPoint.dy - initPoint.dy;
            // if (dx.abs() > 1.0 || dy.abs() > 1.0) {
            initPoint = details.focalPoint;
            widget.onDrag?.call(Offset(dx, dy));
            _logger.log("onScaleUpdate global dx=$dx dy=$dy");
            // }
            break;
          case PositionMode.local:
            final dx = details.localFocalPoint.dx - initPoint.dx;
            final dy = details.localFocalPoint.dy - initPoint.dy;
            // if (dx.abs() > 1.0 || dy.abs() > 1.0) {
            initPoint = details.localFocalPoint;
            widget.onDrag?.call(Offset(dx, dy));
            _logger.log("onScaleUpdate local dx=$dx dy=$dy");
            // }
            break;
        }
        if (details.pointerCount > 1) {
          // scaleFactor = baseScaleFactor * details.scale;
          // // widget.onScale?.call(scaleFactor);
          // angle = baseAngle + details.rotation;
          // widget.onRotate?.call(angle);
        }
      },
      onScaleEnd: (final details) {
        if (widget.mode == PositionMode.global ||
            widget.mode == PositionMode.local) {
          widget.onDragEnd?.call();
        }
      },
      child: widget.child,
    );
  }
}
