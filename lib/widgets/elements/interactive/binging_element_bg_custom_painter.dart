import 'dart:math';

import 'package:flutter/widgets.dart';

class BindingElementBGCustomPainter extends CustomPainter {
  BindingElementBGCustomPainter();

  final double lineWidth = 3;
  final double gapPadding = 6;

  @override
  void paint(final Canvas canvas, final Size size) {
    //画背景
    var paint = Paint()
      ..isAntiAlias = true
      ..strokeWidth = lineWidth
      ..color = const Color(0xFF444444).withOpacity(0.15)
      ..invertColors = false;
    double caliCount = (max(size.height, size.width) * 2) / gapPadding;
    for (int i = 0; i < caliCount; i++) {
      double x1 = (lineWidth + gapPadding) * (i + 1.0);
      double x2 = x1 - size.height;
      canvas.drawLine(Offset(x1, 0), Offset(x2, size.height), paint);
    }
  }

  // double getLastPointHeight(double x,double width){
  //    double xx = width-x;
  //    return xx*height/10 > height ? height : xx*height/10;
  // }

  @override
  bool shouldRepaint(final BindingElementBGCustomPainter oldDelegate) {
    return true;
  }
}
