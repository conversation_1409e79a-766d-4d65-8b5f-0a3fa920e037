import 'package:flutter/widgets.dart';

import 'package:niimbot_flutter_canvas/widgets/elements/interactive/bottom_right_dot_widget.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/interactive/constants.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/interactive/draggable_point.dart';

enum ResizePointType {
  // topLeft,
  // topRight,
  // bottomLeft,
  bottomRight,
  right,
  down,
}

MouseCursor _buildCursor(final ResizePointType type, final num rotate) {
  if (type == ResizePointType.right) {
    if (rotate == 90 || rotate == 270) {
      return SystemMouseCursors.resizeDown;
    }
    return SystemMouseCursors.resizeRight;
  }
  if (type == ResizePointType.bottomRight) {
    if (rotate == 90 || rotate == 270) {
      return SystemMouseCursors.resizeDownLeft;
    }
    return SystemMouseCursors.resizeDownRight;
  }
  if (type == ResizePointType.down) {
    if (rotate == 90 || rotate == 270) {
      return SystemMouseCursors.resizeLeft;
    }
    return SystemMouseCursors.resizeDown;
  }
  return SystemMouseCursors.none;
}

class ResizePoint extends StatelessWidget {
  const ResizePoint({
    super.key,
    this.onDrag,
    required this.type,
    this.onScale,
    this.onDragEnd,
    required this.scale,
    this.color,
    required this.rotate,
  });

  final ValueSetter<Offset>? onDrag;
  final ValueSetter<double>? onScale;
  final ResizePointType type;
  final VoidCallback? onDragEnd;
  final double scale;
  final Color? color;
  final num rotate;

  @override
  Widget build(final BuildContext context) {
    return MouseRegion(
      cursor: _buildCursor(type, rotate),
      child: DraggablePoint(
        mode: PositionMode.local,
        onDrag: onDrag,
        onScale: onScale,
        onDragEnd: onDragEnd,
        child: SizedBox(
          width: InteractiveConstants.cornerDiameter,
          height: InteractiveConstants.cornerDiameter,
          child: BottomRightDotWidget(
            scale,
            color: color,
          ),
        ),
      ),
    );
  }
}
