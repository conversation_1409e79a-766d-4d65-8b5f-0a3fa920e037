import 'package:flutter/cupertino.dart';

import 'package:niimbot_flutter_canvas/widgets/elements/interactive/binging_element_bg_custom_painter.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/interactive/constants.dart';

/// 绑定元素选中时背景
class InteractiveElementBindingBackground extends StatelessWidget {
  final Size size;

  const InteractiveElementBindingBackground({super.key, required this.size});

  @override
  Widget build(final BuildContext context) {
    return Positioned(
      left: 0,
      right: 0,
      top: 0,
      bottom: 0,
      child: Container(
        decoration: const BoxDecoration(),
        clipBehavior: Clip.hardEdge,
        height: size.height + InteractiveConstants.borderWidth * 2,
        width: size.width + InteractiveConstants.borderWidth * 2,
        child: CustomPaint(
          painter: BindingElementBGCustomPainter(),
        ),
      ),
    );
  }
}
