class InteractiveConstants {
  static const borderWidth = 1.0;
  static const floatingActionDiameter = 45.0;
  static const cornerDiameter = 30.0;
  static const floatingActionPadding = 30.0;
  static const itemMinHeight = 5.0;

  /// 最小可被点击的大小
  static const minClickableSize = 10.0;

// static const cornerDiameter = 16.0;
// static const floatingActionPadding = 16.0;

// static double get offsetX =>
//     (floatingActionPadding / 2) + (cornerDiameter / 2);

// static double get offsetY =>
//     (floatingActionPadding / 2) + (cornerDiameter / 2);
}
