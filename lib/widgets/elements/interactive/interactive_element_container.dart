import 'dart:math';

import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';

import 'package:niimbot_flutter_canvas/widgets/elements/interactive/binging_element_bg.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/interactive/constants.dart';

class InteractiveElementContainer extends StatelessWidget {
  final Size size;
  final bool isDashedBorder;
  final bool isSelected;
  final bool isBindingElement;
  final Color color;
  final Widget child;

  const InteractiveElementContainer({
    super.key,
    required this.size,
    required this.isDashedBorder,
    required this.isSelected,
    required this.isBindingElement,
    required this.color,
    required this.child,
  });

  @override
  Widget build(final BuildContext context) {
    return Container(
      key: const Key('draggableResizable_child_container'),
      alignment: Alignment.center,
      height: max(size.height, InteractiveConstants.minClickableSize) +
          InteractiveConstants.borderWidth * 2,
      width: max(size.width, InteractiveConstants.minClickableSize) +
          InteractiveConstants.borderWidth * 2,
      child: isDashedBorder
          ? _DashedBorderInteractiveElementContainer(
              size: size,
              isSelected: isSelected,
              isBindingElement: isBindingElement,
              color: color,
              child: child,
            )
          : _InteractiveElementContainer(
              size: size,
              isSelected: isSelected,
              isBindingElement: isBindingElement,
              color: color,
              child: child,
            ),
    );
  }
}

class _InteractiveElementContainer extends StatelessWidget {
  final Size size;
  final bool isSelected;
  final bool isBindingElement;
  final Color color;
  final Widget child;

  const _InteractiveElementContainer({
    required this.size,
    required this.isSelected,
    required this.isBindingElement,
    required this.color,
    required this.child,
  });

  @override
  Widget build(final BuildContext context) {
    return SizedBox(
      height: size.height + InteractiveConstants.borderWidth * 2,
      width: size.width + InteractiveConstants.borderWidth * 2,
      child: Stack(
        clipBehavior: Clip.hardEdge,
        children: [
          //设置在绘制内容的上层 防止反白后被覆盖
          if (isBindingElement && isSelected) InteractiveElementBindingBackground(size: size),
          Positioned(
            left: 0,
            right: 0,
            top: 0,
            bottom: 0,
            child: Container(
              /// +4 留出边框的占用位置
              height: size.height + InteractiveConstants.borderWidth * 2,
              width: size.width + InteractiveConstants.borderWidth * 2,
              decoration: BoxDecoration(
                border: Border.all(
                  width: InteractiveConstants.borderWidth,
                  color: isSelected ? color : Colors.transparent,
                ),
              ),
              child: child,
            ),
          ),
        ],
      ),
    );
  }
}

class _DashedBorderInteractiveElementContainer extends StatelessWidget {
  final Size size;
  final bool isSelected;
  final bool isBindingElement;
  final Color color;
  final Widget child;

  const _DashedBorderInteractiveElementContainer({
    required this.size,
    required this.isSelected,
    required this.isBindingElement,
    required this.color,
    required this.child,
  });

  @override
  Widget build(final BuildContext context) {
    return SizedBox(
      height: size.height,
      width: size.width,
      child: Stack(
        clipBehavior: Clip.hardEdge,
        children: [
          Positioned(
            left: 0,
            right: 0,
            top: 0,
            bottom: 0,
            child: DottedBorder(
                borderType: BorderType.Rect,
                dashPattern: const [
                  InteractiveConstants.borderWidth * 2,
                  InteractiveConstants.borderWidth * 2
                ],
                color: isSelected ? color : Colors.transparent,
                strokeWidth: InteractiveConstants.borderWidth,
                padding: const EdgeInsets.all(0.1),
                child: SizedBox(
                  height: size.height,
                  width: size.width,
                  child: child,
                )),
          ),
          //设置在绘制内容的上层 防止反白后被覆盖
          if (isBindingElement) InteractiveElementBindingBackground(size: size),
        ],
      ),
    );
  }
}
