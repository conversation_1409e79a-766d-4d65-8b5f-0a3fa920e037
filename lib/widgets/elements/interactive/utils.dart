import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';

import 'package:niimbot_flutter_canvas/widgets/components/svg_icon.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/interactive/constants.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/interactive/resize_point.dart';

final Logger _logger = Logger("InteractiveElementUtils", on: kDebugMode);

class InteractiveElementUtils {
  static Offset _buildResizeButtonOffset(
      final ResizePointType type, final Size size, final double cornerWidth) {
    switch (type) {
      case ResizePointType.bottomRight:
        return Offset(
            size.width -
                cornerWidth / 2 +
                InteractiveConstants.borderWidth * 1.5,
            (size.height < InteractiveConstants.itemMinHeight
                    ? InteractiveConstants.minClickableSize / 2
                    : size.height) -
                cornerWidth / 2 +
                InteractiveConstants.borderWidth);
      case ResizePointType.right:
        return Offset(
            (size.width -
                cornerWidth / 2 +
                InteractiveConstants.borderWidth * 1.5),
            ((size.height < InteractiveConstants.itemMinHeight
                    ? InteractiveConstants.minClickableSize / 2
                    : size.height / 2) -
                cornerWidth / 2 +
                InteractiveConstants.borderWidth));
      case ResizePointType.down:
        return Offset(
            size.width / 2 - cornerWidth / 2 + InteractiveConstants.borderWidth,
            size.height -
                cornerWidth / 2 +
                InteractiveConstants.borderWidth * 1.5);
    }
  }

  /// 构建调整元素大小按钮
  static List<Widget> buildResizeButton(
    final List<ResizePointType> resizeTypes,
    final Size size,
    final double scale,
    final double rotate,
    final Color color,
    final bool isLock,
    final VoidCallback onDragEnd,
    final VoidCallback? onUnlock,
    final void Function(ResizePointType type, Offset details) onDrag,
  ) {
    final cornerWidth = InteractiveConstants.floatingActionPadding / scale;
    return resizeTypes.map((final e) {
      final offset = _buildResizeButtonOffset(e, size, cornerWidth);

      return Positioned(
        top: offset.dy,
        left: offset.dx,
        child: SizedBox(
          width: cornerWidth,
          height: cornerWidth,
          child: isLock
              ? _LockedCorner(onUnlock: onUnlock)
              : ResizePoint(
                  type: e,
                  rotate: rotate,
                  onDrag: (final Offset details) {
                    onDrag(e, details);
                  },
                  onDragEnd: onDragEnd,
                  color: color,
                  scale: scale,
                ),
        ),
      );
    }).toList();
  }
}

class _LockedCorner extends StatelessWidget {
  final VoidCallback? onUnlock;

  const _LockedCorner({required this.onUnlock});

  @override
  Widget build(final BuildContext context) {
    return Center(
      child: SizedBox(
        width: 15,
        height: 15,
        child: InkWell(
            onTap: onUnlock,
            child: const SvgIcon('assets/common/icon_element_lock.svg',
                useDefaultColor: false)),
      ),
    );
  }
}
