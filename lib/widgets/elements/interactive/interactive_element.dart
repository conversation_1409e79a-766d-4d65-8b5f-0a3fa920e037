import 'dart:math';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:niimbot_flutter_canvas/extensions/rect.dart';

import 'package:niimbot_flutter_canvas/utils/keyboard_utils.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/image_clipping/image_clipping_widget.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/interactive/constants.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/interactive/drag_update.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/interactive/draggable_point.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/interactive/interactive_element_container.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/interactive/resize_point.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/interactive/utils.dart';
import 'package:niimbot_flutter_canvas/widgets/components/stack2.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/interactive/interactive_element_data_bind_widget.dart';

Logger _logger = Logger("InteractiveElement", on: kDebugMode);

@immutable
class InteractiveElement extends HookWidget {
  const InteractiveElement({
    super.key,
    required this.child,
    required this.rect,
    required this.rotate,
    this.isLock = false,
    this.isDashedBorder = false,
    this.isSelected = false,
    this.isClipping = false,
    required this.constraints,
    this.onUpdate,
    required this.onDragEnd,
    this.equalRatio = false,
    required this.scale,
    this.resizeEnable = true,
    required this.color,
    required this.isBindingElement,
    this.onUnlock,
    this.resizeTypes = const [],
    this.onBreakBind,
    this.onTapDown,
  });

  final Widget child;

  final ValueSetter<DragUpdate>? onUpdate;

  final Rect rect;
  final double rotate;
  final bool isLock;
  final bool isDashedBorder;
  final bool isSelected;
  final bool isClipping;
  final bool resizeEnable;

  final BoxConstraints constraints;

  final VoidCallback onDragEnd;
  final VoidCallback? onBreakBind;

  /// 等比放大
  final bool equalRatio;

  /// 当前画板缩放比例
  final double scale;

  final Color color;

  //是否是绑定元素
  final bool isBindingElement;

  final void Function()? onUnlock;

  final List<ResizePointType> resizeTypes;

  final GestureTapDownCallback? onTapDown;

  List<Widget> builder(final BuildContext context,
      [final ValueSetter<Offset>? onDrag, final VoidCallback? onDragEnd]) {
    return [
      DraggablePoint(
        onDrag: onDrag,
        onDragEnd: onDragEnd,
        onTapDown: onTapDown,
        child: InteractiveElementContainer(
          size: rect.size,
          isDashedBorder: isDashedBorder,
          isSelected: isSelected,
          isBindingElement: isBindingElement,
          color: color,
          child: child,
        ),
      )
    ];
  }

  @override
  Widget build(final BuildContext context) {
    final _rect = useState(rect);
    final _aspectRatio = _rect.value.width / _rect.value.height;
    final _isDraging = useRef(false);
    num _calcEqualRatioSize(final Offset details) {
      if (details.dx.abs() > details.dy.abs()) {
        return details.dx;
      }
      return details.dy;
    }

    void _onResizeDrag(final ResizePointType type, final Offset details) {
      Size? updatedSize;
      if (equalRatio || KeyboardUtils.isShiftPressed) {
        /* 等比拉伸 */
        final maxOffset = _calcEqualRatioSize(details);
        double newWidth = (_rect.value.width + maxOffset)
            .clamp(constraints.minWidth, constraints.maxWidth);
        double newHeight = newWidth / _aspectRatio;
        if (newHeight < constraints.minHeight) {
          newHeight = constraints.minHeight;
          newWidth = newHeight * _aspectRatio;
        }
        updatedSize = Size(newWidth, newHeight);
      } else {
        if (type == ResizePointType.right) {
          double newHeight = _rect.value.height;
          double newWidth = (_rect.value.width + details.dx)
              .clamp(constraints.minWidth, constraints.maxWidth);
          updatedSize = Size(newWidth, newHeight);
        }
        if (type == ResizePointType.down) {
          double newHeight = (_rect.value.height + details.dy)
              .clamp(constraints.minHeight, constraints.maxHeight);
          double newWidth = _rect.value.width;
          updatedSize = Size(newWidth, newHeight);
        }
        if (type == ResizePointType.bottomRight) {
          double newHeight = (_rect.value.height + details.dy)
              .clamp(constraints.minHeight, constraints.maxHeight);
          double newWidth = (_rect.value.width + details.dx)
              .clamp(constraints.minWidth, constraints.maxWidth);
          updatedSize = Size(newWidth, newHeight);
        }
      }
      if (updatedSize != null) {
        _isDraging.value = true;

        if (rotate == 90) {
          final viewRect = _rect.value.rotate(-rotate);
          _rect.value = Rect.fromLTRB(
                  viewRect.right - updatedSize.height,
                  viewRect.top,
                  viewRect.right,
                  viewRect.top + updatedSize.width)
              .rotate(-rotate);
        } else if (rotate == 180) {
          _rect.value = _rect.value.resize(
              size: updatedSize, fixedPointType: FixedPointType.bottomRight);
        } else if (rotate == 270) {
          final viewRect = _rect.value.rotate(-rotate);
          _rect.value =  Rect.fromLTRB(viewRect.right - updatedSize.height,
              viewRect.bottom - updatedSize.width, viewRect.right, viewRect.bottom)
              .rotate(-rotate);
        } else {
          _rect.value = _rect.value.resize(size: updatedSize);
        }

        onUpdate?.call(DragUpdate(
          dragType: DragType.size,
          position: _rect.value.topLeft,
          size: updatedSize,
          constraints: Size(constraints.maxWidth, constraints.maxHeight),
          rotate: rotate,
          delta: details,
        ));

        _logger.log('onResized => topLeft:${_rect.value.topLeft}');
      }
    }

    onDrag(final Offset d) {
      if (isLock) {
        return;
      }
      _isDraging.value = true;
      _logger.log('onDrag =>');
      _rect.value = _rect.value.translateTo(
          offset: _rect.value.topLeft.translate(d.dx / scale, d.dy / scale));
      final position = _rect.value.rotate(-rotate).topLeft;
      onUpdate?.call(DragUpdate(
        dragType: DragType.position,
        position: position,
        size: _rect.value.size,
        constraints: Size(constraints.maxWidth, constraints.maxHeight),
        rotate: rotate,
        delta: d,
      ));
    }

    _onDragEnd() {
      _isDraging.value = false;
      onDragEnd();
    }

    _logger.log('build => topLeft:${_rect.value.topLeft}');
    final _top = _rect.value.topLeft.dy - InteractiveConstants.borderWidth;

    final _left = _rect.value.topLeft.dx - InteractiveConstants.borderWidth;
    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((final _) {
        if (!_isDraging.value) {
          _rect.value = rect;
        }
      });
      return null;
    }, [rect]);
    return Positioned(
      top: _top,
      left: _left,
      child: Transform.rotate(
        alignment: Alignment.center,
        angle: pi * rotate / 180,
        child: Stack2(
          // fit: StackFit.loose,
          clipBehavior: Clip.none,
          children: [
            ...builder(context, onDrag, _onDragEnd),
            if (resizeEnable)
              ...InteractiveElementUtils.buildResizeButton(
                resizeTypes,
                rect.size,
                scale,
                rotate,
                color,
                isLock,
                _onDragEnd,
                onUnlock,
                _onResizeDrag,
              ),
            if (isBindingElement)
              InteractiveElementDataBindWidget(
                color: color,
                onBreakBind: onBreakBind,
              ),
            if (isClipping)
              ImageClippingWidget(
                rotate: rotate,
                scale: scale,
              ),
          ],
        ),
      ),
    );
  }
}
