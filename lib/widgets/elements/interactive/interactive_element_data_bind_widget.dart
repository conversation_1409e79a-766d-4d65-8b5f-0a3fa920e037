import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:niimbot_ui/icons/icons.dart';
import 'package:niimbot_ui/styles/theme.dart';

class InteractiveElementDataBindWidget extends StatefulWidget {
  final Color color;
  final VoidCallback? onBreakBind;

  const InteractiveElementDataBindWidget({
    required this.color,
    this.onBreakBind,
    super.key,
  });

  @override
  State<InteractiveElementDataBindWidget> createState() =>
      _InteractiveElementDataBindWidgetState();
}

class _InteractiveElementDataBindWidgetState
    extends State<InteractiveElementDataBindWidget> {
  bool isEnter = false;

  @override
  Widget build(final BuildContext context) {
    return Positioned(
      left: -9,
      top: -9,
      child: MouseRegion(
          cursor: SystemMouseCursors.click,
          onEnter: (final e) {
            isEnter = true;
            setState(() {});
          },
          onExit: (final e) {
            isEnter = false;
            setState(() {});
          },
          child: GestureDetector(
            onTap: () {
              if (widget.onBreakBind != null) {
                widget.onBreakBind!();
              }
            },
            child: Container(
              width: 18,
              height: 18,
              decoration: BoxDecoration(
                  color: isEnter
                      ? NiimbotTheme.of(context)
                          .colors
                          .solidBackgroundFillColorTertiary
                      : NiimbotTheme.of(context).colors.systemFillColorWhite,
                  borderRadius: const BorderRadius.all(Radius.circular(9.0)),
                  border: Border.all(
                      width: 1,
                      color: NiimbotTheme.of(context).colors.borderColorHover)),
              child: Center(
                child: NiimbotIcons.linked(
                  size: 11,
                  color: widget.color,
                ),
              ),
            ),
          )),
    );
  }
}

// class InteractiveElementDataBindWidget extends StatelessWidget {
//   final Color color;
//   final VoidCallback? onBreakBind;
//
//   const InteractiveElementDataBindWidget({
//     required this.color,
//     this.onBreakBind,
//     super.key,
//   });
//
//   @override
//   Widget build(BuildContext context) {
//     return Positioned(
//       left: -9,
//       top: -9,
//       child: MouseRegion(
//           cursor: SystemMouseCursors.click,
//           child: GestureDetector(
//             onTap: () {
//               if (onBreakBind != null) {
//                 onBreakBind!();
//               }
//             },
//             child: Container(
//               width: 18,
//               height: 18,
//               decoration: BoxDecoration(
//                   color: NiimbotTheme.of(context).colors.systemFillColorWhite,
//                   borderRadius: const BorderRadius.all(Radius.circular(9.0)),
//                   border: Border.all(
//                       width: 1,
//                       color: NiimbotTheme.of(context).colors.borderColorHover)),
//               child: Center(
//                 child: NiimbotIcons.linked(
//                   size: 11,
//                   color: color,
//                 ),
//               ),
//             ),
//           )),
//     );
//   }
// }
