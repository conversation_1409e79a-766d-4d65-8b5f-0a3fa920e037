import 'dart:ui';

import 'models/result.dart';


/// A convenient type alias for a [ResizeResult] with <PERSON><PERSON><PERSON>'s [Rect], [Offset]
/// and [Size] types.
typedef UIMoveResult = MoveResult<Rect, Offset, Size>;

/// A convenient type alias for a [MoveResult] with <PERSON>lut<PERSON>'s [Rect], [Offset]
/// and [Size] types.
typedef UIResizeResult = ResizeResult<Rect, Offset, Size>;

/// A convenient type alias for a [TransformResult] with <PERSON>lut<PERSON>'s [Rect],
/// [Offset] and [Size] types.
typedef UITransformResult = TransformResult<Rect, Offset, Size>;

/// A convenient type alias for a [RotateResult] with Flutter's [Rect], [Offset]
/// and [Size] types.
typedef UIRotateResult = RotateResult<Rect, Offset, Size>;
