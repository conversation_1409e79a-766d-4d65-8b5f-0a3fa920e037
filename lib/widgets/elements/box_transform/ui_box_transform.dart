import 'dart:ui' as ui;

import 'package:flutter/rendering.dart' as widgets;

import 'extensions.dart';
import 'models/enums.dart';
import 'models/transformer.dart';
import 'ui_result.dart';


/// A Flutter translation of [transform.BoxTransformer].
class UIBoxTransform {
  /// A private constructor to prevent instantiation.
  const UIBoxTransform._();

  /// The Flutter wrapper for [transform.BoxTransformer.resize].
  static UIResizeResult resize({
    required final ui.Rect initialRect,
    required final ui.Offset initialLocalPosition,
    required final ui.Offset localPosition,
    required final double rotation,
    required final HandlePosition handle,
    required final ResizeMode resizeMode,
    required final Flip initialFlip,
    final ui.Rect clampingRect = ui.Rect.largest,
    final widgets.BoxConstraints constraints = const widgets.BoxConstraints(),
    final bool allowFlipping = true,
    final BindingStrategy bindingStrategy = BindingStrategy.boundingBox,
  }) =>
      BoxTransformer.resize(
        initialRect: initialRect.toBox(),
        initialLocalPosition: initialLocalPosition.toVector2(),
        localPosition: localPosition.toVector2(),
        rotation: rotation,
        handle: handle,
        resizeMode: resizeMode,
        initialFlip: initialFlip,
        clampingRect: clampingRect.toBox(),
        constraints: constraints.toConstraints(),
        allowFlipping: allowFlipping,
        bindingStrategy: bindingStrategy,
      ).toUI();

  /// The Flutter wrapper for [BoxTransformer.move].
  static UIMoveResult move({
    required final ui.Rect initialRect,
    required final ui.Offset initialLocalPosition,
    required final ui.Offset localPosition,
    required final double rotation,
    final ui.Rect clampingRect = ui.Rect.largest,
    final BindingStrategy bindingStrategy = BindingStrategy.boundingBox,
  }) =>
      BoxTransformer.move(
        initialRect: initialRect.toBox(),
        initialLocalPosition: initialLocalPosition.toVector2(),
        localPosition: localPosition.toVector2(),
        rotation: rotation,
        clampingRect: clampingRect.toBox(),
        bindingStrategy: bindingStrategy,
      ).toUI();

  /// The Flutter wrapper for [BoxTransformer.rotate].
  static UIRotateResult rotate({
    required final ui.Rect rect,
    required final ui.Offset initialLocalPosition,
    required final ui.Offset localPosition,
    required final double initialRotation,
    final ui.Rect clampingRect = ui.Rect.largest,
    final BindingStrategy bindingStrategy = BindingStrategy.boundingBox,
  }) =>
      BoxTransformer.rotate(
        rect: rect.toBox(),
        initialLocalPosition: initialLocalPosition.toVector2(),
        localPosition: localPosition.toVector2(),
        clampingRect: clampingRect.toBox(),
        bindingStrategy: bindingStrategy,
        initialRotation: initialRotation,
      ).toUI();
}
