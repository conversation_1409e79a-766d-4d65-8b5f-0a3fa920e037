import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:netal_plugin/netal_image_result.dart';
import 'package:netal_plugin/utils/ratio.dart';
import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_flutter_canvas/provider/models/base_element_updater.dart';
import 'package:niimbot_flutter_canvas/widgets/canvas/canvas_interface.dart';
import 'package:niimbot_template/models/elements/base_element.dart';
import 'package:niimbot_template/models/elements/image_element.dart';
import 'package:niimbot_template/models/template_data.dart';
import 'package:provider/provider.dart';

Logger _logger = Logger("BaseElementWidget", on: kDebugMode);

abstract class BaseElementWidget<T extends BaseElement>
    extends StatelessWidget {
  final T element;
  final Color? color;
  final NetalImageResult? imageCache;
  final UpdateElementNetalInfo? updateElementNetalInfo;

  const BaseElementWidget({
    super.key,
    required this.element,
    this.color,
    this.imageCache,
    this.updateElementNetalInfo,
  });

  netalCallback(final NetalImageResult netalRes, final TemplateData template) {
    _logger.log(element.height, '元素原始高');
    _logger.log(element.width, '元素原始宽');
    final height = netalRes.height.px2mm(false).toDouble();
    final width = netalRes.width.px2mm(false).toDouble();
    _logger.log(height, '元素新高');
    _logger.log(width, '元素新宽');
    WidgetsBinding.instance.addPostFrameCallback((_) {
      updateElementNetalInfo?.call(
        image: netalRes,
        id: element.id,
        updater: element is! ImageElement
            ? BaseElementUpdater(
                width: width,
                height: height,
              )
            : null,
      );
    });
  }

  NetalImageResult? buildNetalResult(final TemplateData template);

  Uint8List _buildImageData(final BuildContext context) {
    Uint8List imageData = Uint8List(0);
    if (imageCache != null) {
      imageData = imageCache!.pixels;
    } else {
      _logger.log(element, '图像库生成元素');
      final template = context.read<CanvasStore>().canvasData;
      final netalRes = buildNetalResult(template);
      if (netalRes != null) {
        imageData = netalRes.pixels;
        if (imageData.isNotEmpty) {
          netalCallback(netalRes, template);
        }
      }
    }
    return imageData;
  }

  Widget buildImage(final Uint8List bytes) {
    if (bytes.isNotEmpty) {
      return Image.memory(
        width: element.width.mm2px().toDouble(),
        height: element.height.mm2px().toDouble(),
        bytes,
        /** 避免闪动 */
        gaplessPlayback: true,
        // scale: RatioUtils().ratioScale/2,
        fit: BoxFit.fill,
        // filterQuality: FilterQuality.high,
        // isAntiAlias: true,
      );
    }
    _logger.log(element.toJson(), 'imageData is Empty');
    return const SizedBox.shrink();
  }

  @override
  Widget build(final BuildContext context) {
    final data = _buildImageData(context);
    return buildImage(data);
    // return FutureBuilder<Uint8List>(
    //     future: data,
    //     builder: (_, snapshot) {
    //       if (snapshot.hasData) {
    //         return buildImage(snapshot.data!);
    //       }
    //       return const SizedBox.shrink();
    //     });
  }
}
