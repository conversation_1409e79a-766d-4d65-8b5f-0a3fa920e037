import 'package:flutter/foundation.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';


import 'package:netal_plugin/netal_image_result.dart';
import 'package:netal_plugin/netal_plugin.dart';
import 'package:netal_plugin/utils/ratio.dart';
import 'package:niimbot_template/extensions/template_data.dart';
import 'package:niimbot_template/models/elements/table_element.dart';

import 'package:niimbot_flutter_canvas/widgets/elements/base_widget.dart';
import 'package:niimbot_template/models/template_data.dart';

final Logger _logger = Logger("TableWidget", on: kDebugMode);

class TableWidget extends BaseElementWidget<TableElement> {
  const TableWidget({
    super.key,
    required super.element,
    super.color,
    super.imageCache,
    super.updateElementNetalInfo,
  });

  @override
  NetalImageResult buildNetalResult(final TemplateData template) {
    return NetalPlugin().generateTableElement(
      element: template.coverElementToNetal(element),
      ratio: RatioUtils().ratio,
      color: color,
    );
  }
}
