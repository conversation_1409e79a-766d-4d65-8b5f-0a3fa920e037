import 'package:flutter/cupertino.dart';
import 'package:netal_plugin/utils/ratio.dart';

import 'package:niimbot_flutter_canvas/widgets/elements/table/table_move/table_move_line.dart';

List<Widget> TableMoveArea({
  required final double width,
  required final double height,
  final double? tableBarSize,
  final ValueChanged<Offset>? onDrag,
  final VoidCallback? onDragEnd,
}) {
  double size = 0.6.mm2px().toDouble();
  double areaWidth = width + (tableBarSize ?? 0) + size * 2;
  double areaHeight = height + (tableBarSize ?? 0);
  return [
    Positioned(
      right: -size,
      bottom: areaHeight,
      child: TableMoveLine(
        width: areaWidth,
        height: size,
        onDrag: onDrag,
        onDragEnd: onDragEnd,
      ),
    ),
    Positioned(
      right: size,
      bottom: -size,
      child: TableMoveLine(
        width: areaWidth - size * 2,
        height: size,
        onDrag: onDrag,
        onDragEnd: onDragEnd,
      ),
    ),
    Positioned(
      right: areaWidth - size * 2,
      bottom: 0,
      child: TableMoveLine(
        width: size,
        height: areaHeight,
        onDrag: onDrag,
        onDragEnd: onDragEnd,
      ),
    ),
    Positioned(
      right: -size,
      bottom: size,
      child: TableMoveLine(
        width: size,
        height: areaHeight - size,
        onDrag: onDrag,
        onDragEnd: onDragEnd,
      ),
    ),
  ];
}
