import 'package:flutter/cupertino.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/interactive/draggable_point.dart';

class TableMoveLine extends StatelessWidget {
  final double? height;
  final double? width;
  final ValueChanged<Offset>? onDrag;
  final VoidCallback? onDragEnd;

  const TableMoveLine({
    super.key,
    this.height,
    this.width,
    this.onDrag,
    this.onDragEnd,
  });

  @override
  Widget build(final BuildContext context) {
    return MouseRegion(
      cursor: SystemMouseCursors.move,
      child: DraggablePoint(
        mode: PositionMode.local,
        onDrag: onDrag,
        onDragEnd: onDragEnd,
        child: Container(
          width: width,
          height: height,
          alignment: Alignment.center,
        ),
      ),
    );
  }
}
