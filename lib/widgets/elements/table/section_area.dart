import 'package:flutter/material.dart';
import 'package:netal_plugin/utils/ratio.dart';

import 'package:niimbot_flutter_canvas/extensions/table_element.dart';
import 'package:niimbot_template/models/elements/table_element.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:provider/provider.dart';
import 'package:niimbot_ui/widgets/context_menu/niimbot_context_menu.dart';

import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/context_menu/context_menu_utils.dart';

class TableSelectionArea extends StatelessWidget {
  final Rect rect;
  final TableElement table;
  final Color? color;
  final num scale;

  const TableSelectionArea({
    super.key,
    required this.rect,
    required this.table,
    this.color,
    required this.scale,
  });

  @override
  Widget build(final BuildContext context) {
    onTapDown(final TapDownDetails details) {
      final ids = context.read<CanvasStore>().getSelected(table.id);
      final selectedCells = table.getSelectedCells(ids);
      // if (table.selectedCells.isNotEmpty && table.selectedCells.length > 1) {
      if (selectedCells.isNotEmpty) {
        for (final cell in selectedCells) {
          Offset localPosition =
              details.localPosition + Offset(rect.left, rect.top);
          final cellRect = table.getCellRect(cell).mm2px();
          if (cellRect.contains(localPosition)) {
            context.read<CanvasStore>().clearSelected();
            context.read<CanvasStore>().focusElement(table.id, child: cell.id);
            context.read<CanvasStore>().startEdit(cell.id);
            break;
          }
        }
      }
    }

    return Positioned(
      left: rect.left,
      top: rect.top,
      child: NiimbotContextMenu(
        child: GestureDetector(
          onTapDown: onTapDown,
          child: Container(
            decoration: BoxDecoration(
              border: Border.all(color: Colors.blue, width: 2),
              color: color ??
                  NiimbotTheme.of(context)
                      .colors
                      .systemFillColorSelected
                      .withOpacity(0.1),
            ),
            width: rect.width,
            height: rect.height,
          ),
        ),
        builder: (final TapUpDetails details) {
          return ContextMenuUtils.buildContextMenu(
            context,
            details.localPosition / scale.toDouble(),
            ContextMenuType.element,
            element: table,
          );
        },
      ),
    );
  }
}
