import 'dart:math';

import 'package:flutter/cupertino.dart';
import 'package:netal_plugin/utils/ratio.dart';

import 'package:niimbot_template/models/elements/table_element.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:provider/provider.dart';

import 'package:niimbot_flutter_canvas/widgets/elements/interactive/constants.dart';
import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/interactive/draggable_point.dart';

class TableResizeLine extends StatefulWidget {
  final int index;
  final TableElement table;
  final num scale;
  final num rotate;
  final bool dragRow;
  final bool selected;
  final bool disabled;
  final double? height;
  final double? width;

  const TableResizeLine({
    super.key,
    required this.index,
    required this.table,
    required this.scale,
    this.dragRow = true,
    this.selected = false,
    this.disabled = false,
    this.height,
    this.width,
    this.rotate = 0,
  });

  @override
  State<TableResizeLine> createState() => _TableResizeLineState();
}

class _TableResizeLineState extends State<TableResizeLine> {
  bool _showHover = false;
  double lineWidth = 0.2.mm2px().toDouble();

  bool get firstLine {
    return widget.index == -1;
  }

  bool get lastLine {
    if (widget.dragRow) {
      return widget.index == widget.table.row - 1;
    } else {
      return widget.index == widget.table.column - 1;
    }
  }

  MouseCursor get cursor {
    if (widget.disabled) {
      return SystemMouseCursors.alias;
    }
    if (widget.rotate % 180 == 0) {
      if (widget.dragRow) {
        return SystemMouseCursors.resizeRow;
      }
      return SystemMouseCursors.resizeColumn;
    }
    if (!widget.dragRow) {
      return SystemMouseCursors.resizeRow;
    }
    return SystemMouseCursors.resizeColumn;
  }

  @override
  Widget build(final BuildContext context) {
    final themeColors = NiimbotTheme.of(context).colors;

    double high =
        widget.table.tableBarHeight + widget.table.lineWidth.mm2px().toDouble();
    double wide = 0.8.mm2px().toDouble();

    onDrag(final Offset offset) {
      if (widget.disabled == true) return;
      _showHover = true;
      final position = offset * widget.scale.toDouble();
      if (widget.dragRow) {
        context.read<CanvasStore>().tableController.updateRowSize(
            widget.table.id,
            widget.index,
            widget.table.rowHeight[widget.index] + position.dy.px2mm());
      } else {
        context.read<CanvasStore>().tableController.updateColumnSize(
            widget.table.id,
            widget.index,
            widget.table.columnWidth[widget.index] + position.dx.px2mm());
      }
    }

    onDragEnd() {
      if (widget.disabled == true) return;
      _showHover = false;
      context
          .read<CanvasStore>()
          .tableController
          .updateSizeEnd(widget.table.id);
    }

    return MouseRegion(
      cursor: cursor,
      onEnter: (final e) {
        if (widget.disabled == false) {
          setState(() {
            _showHover = true;
          });
        }
      },
      onExit: (final e) {
        if (widget.disabled == false) {
          setState(() {
            _showHover = false;
          });
        }
      },
      child: DraggablePoint(
        mode: PositionMode.local,
        onDrag: onDrag,
        onDragEnd: onDragEnd,
        child: Container(
          height: max(widget.height ?? (!widget.dragRow ? high : wide),
              InteractiveConstants.minClickableSize),
          width: max(widget.width ?? (!widget.dragRow ? wide : high),
              InteractiveConstants.minClickableSize),
          alignment: lastLine
              ? (widget.dragRow
                  ? Alignment.bottomCenter
                  : Alignment.centerRight)
              : firstLine
                  ? (widget.dragRow
                      ? Alignment.topCenter
                      : Alignment.centerLeft)
                  : Alignment.center,
          // color: themeColors.brandColor.withOpacity(0.5),
          child: Container(
            height: widget.height ?? (!widget.dragRow ? high : lineWidth),
            width: widget.width ?? (!widget.dragRow ? lineWidth : high),
            color: widget.selected || _showHover
                ? themeColors.systemFillColorSelected
                : null,
          ),
        ),
      ),
    );
  }
}
