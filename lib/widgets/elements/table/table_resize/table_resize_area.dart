import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:netal_plugin/utils/ratio.dart';

import 'package:niimbot_template/models/elements/table_element.dart';

import 'package:niimbot_flutter_canvas/widgets/elements/table/table_resize/table_resize_line.dart';

List<Widget> TableResizeArea(final TableElement table, final num scale,
    {final int? hoverCol, final int? hoverRow, final num rotate = 0}) {
  List<num> columnWidth = [0, ...table.columnWidth];
  List<num> rowHeight = [0, ...table.rowHeight];
  double lineWidth = table.lineWidth.toDouble();
  double lineOffset = (lineWidth - 0.8) / 2;
  return [
    ...columnWidth.foldIndexed<List<Widget>>([], (final i, final p, final e) {
      List<num> curList = columnWidth.sublist(0, i + 1);
      double left = (curList.reduce((final sum, final e) => sum + e) +
              (curList.length - 1) * lineWidth +
              (i == 0
                  ? 0
                  : i == table.column
                      ? lineOffset * 2
                      : lineOffset))
          .mm2px()
          .toDouble();
      p.add(
        Positioned(
          left: left,
          top: -28,
          child: TableResizeLine(
            index: i - 1,
            table: table,
            scale: scale,
            dragRow: false,
            disabled: i == 0,
            height: (table.height + 0.2).mm2px().toDouble() + 28,
            selected: hoverCol == i - 1,
            rotate: rotate,
          ),
        ),
      );
      return p;
    }),
    ...rowHeight.foldIndexed<List<Widget>>([], (final i, final p, final e) {
      List<num> curList = rowHeight.sublist(0, i + 1);
      double top = (curList.reduce((final sum, final e) => sum + e) +
              (curList.length - 1) * table.lineWidth +
              (i == 0
                  ? 0
                  : i == table.row
                      ? lineOffset * 2
                      : lineOffset))
          .mm2px()
          .toDouble();
      p.add(
        Positioned(
          left: -28,
          top: top,
          child: TableResizeLine(
            index: i - 1,
            table: table,
            scale: scale,
            disabled: i == 0,
            width: (table.width + 0.2).mm2px().toDouble() + 28,
            selected: hoverRow == i - 1,
            rotate: rotate,
          ),
        ),
      );
      return p;
    }),
  ];
}
