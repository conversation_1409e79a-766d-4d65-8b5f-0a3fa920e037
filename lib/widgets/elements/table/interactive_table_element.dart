import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';

import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:netal_plugin/utils/ratio.dart';
import 'package:niimbot_flutter_canvas/hooks/use_table_element_state.dart';
import 'package:niimbot_flutter_canvas/widgets/components/hover_builder.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/interactive/interactive_element.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/table/table_move/table_move_area.dart';
import 'package:niimbot_template/models/elements/table_element.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:niimbot_ui/widgets/context_menu/niimbot_context_menu.dart';

import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/table/section_area.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/table/table_resize/table_resize_area.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/context_menu/context_menu_utils.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/interactive/draggable_point.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/interactive/interactive_element_container.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/table/table_bar/table_column_bar.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/table/table_bar/table_row_bar.dart';
import 'package:niimbot_flutter_canvas/widgets/components/stack2.dart';
import 'package:provider/provider.dart';

Logger _logger = Logger("InteractiveTableElement", on: kDebugMode);

@immutable
class InteractiveTableElement extends InteractiveElement {
  const InteractiveTableElement({
    super.key,
    required super.child,
    required super.rect,
    required super.rotate,
    this.isSelection = false,
    super.isLock = false,
    super.isSelected = false,
    required super.constraints,
    super.onUpdate,
    required super.onDragEnd,
    super.equalRatio = false,
    required super.scale,
    super.resizeEnable = true,
    required super.color,
    required super.isBindingElement,
    super.onUnlock,
    super.resizeTypes = const [],
    required this.table,
  });

  final bool isSelection;
  final TableElement table;

  @override
  List<Widget> builder(final BuildContext context,
      [final ValueSetter<Offset>? onDrag, final VoidCallback? onDragEnd]) {
    return [
      InteractiveElementContainer(
        size: rect.size,
        isDashedBorder: isDashedBorder,
        isSelected: isSelected,
        isBindingElement: isBindingElement,
        color: color,
        child: child,
      ),
      if (isSelected)
        _InteractiveTableElementSelected(
          table: table,
          scale: scale,
          isSelection: isSelection,
          onDrag: onDrag,
          onDragEnd: onDragEnd,
          size: rect.size,
          rotate: rotate,
        ),
    ];
  }
}

class _InteractiveTableElementSelected extends HookWidget {
  const _InteractiveTableElementSelected({
    required this.table,
    this.scale = 1,
    this.isSelection = false,
    this.onDrag,
    this.onDragEnd,
    required this.size,
    this.rotate = 0,
  });

  final TableElement table;
  final double scale;
  final bool isSelection;
  final ValueChanged<Offset>? onDrag;
  final VoidCallback? onDragEnd;
  final Size size;
  final double rotate;

  @override
  Widget build(final BuildContext context) {
    final focusedCellsRect = useFocusedCellsRect(table);
    final deleteHovered = useState(false);
    final tableColIndex = useState<int?>(null);
    final tableRowIndex = useState<int?>(null);
    onDeleteHover(final bool hovered) {
      deleteHovered.value = hovered;
    }

    setTableColAddHover(final int? e) {
      tableColIndex.value = e;
    }

    setTableRowAddHover(final int? e) {
      tableRowIndex.value = e;
    }

    onDrag(final Offset d) {
      if (rotate == 90) {
        this.onDrag?.call(Offset(-d.dy, d.dx));
      } else if (rotate == 180) {
        this.onDrag?.call(Offset(-d.dx, -d.dy));
      } else if (rotate == 270) {
        this.onDrag?.call(Offset(d.dy, -d.dx));
      } else {
        this.onDrag?.call(d);
      }
    }

    return Stack2(
      clipBehavior: Clip.none,
      children: [
        SizedBox.fromSize(size: size),
        if (focusedCellsRect != null)
          TableSelectionArea(
            rect: focusedCellsRect.mm2px(),
            color: deleteHovered.value
                ? NiimbotTheme.of(context).colors.brandColor.withOpacity(0.1)
                : null,
            table: table,
            scale: scale,
          ),
        TableColumnBar(
          table: table,
          scale: scale,
          deleteHovered: deleteHovered.value,
          onDeleteHover: onDeleteHover,
          onAddHover: setTableColAddHover,
        ),
        TableRowBar(
          table: table,
          scale: scale,
          deleteHovered: deleteHovered.value,
          onDeleteHover: onDeleteHover,
          onAddHover: setTableRowAddHover,
        ),
        if (!isSelection)
          ...TableResizeArea(table, scale,
              hoverCol: tableColIndex.value,
              hoverRow: tableRowIndex.value,
              rotate: rotate),
        _InteractiveTableElementDot(
          table: table,
          onDrag: onDrag,
          onDragEnd: onDragEnd,
          scale: scale,
        ),
        ...TableMoveArea(
          width: table.width.mm2px().toDouble(),
          height: table.height.mm2px().toDouble(),
          tableBarSize: 28,
          onDrag: onDrag,
          onDragEnd: onDragEnd,
        )
      ],
    );
  }
}

class _InteractiveTableElementDot extends StatelessWidget {
  final ValueChanged<Offset>? onDrag;
  final VoidCallback? onDragEnd;
  final TableElement table;
  final num scale;

  const _InteractiveTableElementDot({
    this.onDrag,
    this.onDragEnd,
    required this.table,
    required this.scale,
  });

  @override
  Widget build(final BuildContext context) {
    final themeColors = NiimbotTheme.of(context).colors;
    onTap() {
      context.read<CanvasStore>().clearElementChildrenSelected(table.id);
      FocusScope.of(context).requestFocus();
    }


    return Positioned(
      left: -28,
      top: -28,
      child: HoverBuilder(
        cursor: SystemMouseCursors.move,
        builder: (final context, final hover) {
          return NiimbotContextMenu(
            builder: (final TapUpDetails details) {
              return ContextMenuUtils.buildContextMenu(
                  context,
                  details.localPosition / scale.toDouble(),
                  ContextMenuType.common,
                  element: table);
            },
            child: GestureDetector(
              onTap: onTap,
              onSecondaryTap: onTap,
              child: DraggablePoint(
                mode: PositionMode.local,
                onDrag: (final offset) {
                  onDrag?.call(offset * scale.toDouble());
                },
                onDragEnd: onDragEnd,
                child: Container(
                  width: 28,
                  height: 28,
                  decoration: BoxDecoration(
                    color: !hover
                        ? themeColors.solidBackgroundFillColorTertiary
                            .withOpacity(0.5)
                        : themeColors.translucentFillColorTertiary,
                    border: Border(
                      top: BorderSide(
                        color: themeColors.borderColorNormal,
                        width: 0.2.mm2px().toDouble(),
                      ),
                      left: BorderSide(
                        color: themeColors.borderColorNormal,
                        width: 0.2.mm2px().toDouble(),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
