import 'package:collection/collection.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';

import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:netal_plugin/utils/ratio.dart';
import 'package:niimbot_flutter_canvas/extensions/table_element.dart';
import 'package:niimbot_flutter_canvas/hooks/use_table_element_state.dart';
import 'package:niimbot_flutter_canvas/widgets/components/stack2.dart';
import 'package:niimbot_template/models/elements/table_element.dart';
import 'package:niimbot_ui/icons/icons.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:provider/provider.dart';

import 'package:niimbot_flutter_canvas/widgets/elements/table/table_bar/table_bar_button.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/table/table_bar/table_rect_size_box.dart';
import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';

Logger _logger = Logger("TableRowBar", on: kDebugMode);

class TableRowBar extends HookWidget {
  final TableElement table;
  final num scale;
  final bool deleteHovered;
  final ValueChanged<bool>? onDeleteHover;
  final ValueChanged<int?>? onAddHover;

  const TableRowBar({
    super.key,
    required this.table,
    required this.scale,
    this.deleteHovered = false,
    this.onDeleteHover,
    this.onAddHover,
  });

  @override
  Widget build(final BuildContext context) {
    final themeColors = NiimbotTheme.of(context).colors;
    final focusedCellsRect = useFocusedCellsRect(table) ?? Rect.zero;
    final deleteTop = (focusedCellsRect.top + focusedCellsRect.height / 2)
            .mm2px()
            .toDouble() +
        12;
    final boolShowDelete = useIsFullRow(table);
    final addHovered = useState(false);

    final addTop = useState(0.0);

    final inShowRow = useState(false);
    final inRow = useState(false);
    final inAddWidget = useState(false);

    final mouseIndex = useRef<int?>(null);

    final boolShowAdd = useMemoized(() {
      if (inShowRow.value) {
        return inShowRow.value;
      }
      if (inAddWidget.value) {
        return true;
      }
      if (inRow.value) {
        return true;
      }
      return false;
    }, [inShowRow.value, inAddWidget.value, inRow.value]);

    double getAddTop(final int? index) {
      List<num>? addList =
          index! > 0 ? (table.rowHeight.sublist(0, index)) : [];
      return (addList.isNotEmpty
              ? (((addList.reduce((final a, final c) => a + c)) +
                      table.lineWidth * (addList.length + 0.5))
                  .mm2px()
                  .toDouble())
              : 0) +
          12;
    }

    getMouseRowIndex(final double current) {
      for (int i = 0; i <= table.row; i++) {
        double top = i > 0 ? getAddTop(i - 1) + 16 : 16;
        double bottom = getAddTop(i) + 16;
        if (current >= top && current <= bottom) {
          int curIndex = current > bottom - (bottom - top) / 2 ? i : i - 1;
          mouseIndex.value = curIndex - 1;
          addTop.value = getAddTop(curIndex);
          return;
        }
      }
    }

    void onDelete() {
      final ids = context.read<CanvasStore>().getSelected(table.id);
      final fullRow = table.getSelectedFullRow(ids);
      context
          .read<CanvasStore>()
          .tableController
          .removeRowInRange(table.id, fullRow!.first, fullRow.last);
      onDeleteHover?.call(false);
    }

    void onAdd() {
      if (mouseIndex.value != null) {
        context
            .read<CanvasStore>()
            .tableController
            .addRowAfterRowIndex(table.id, mouseIndex.value!);
      }
    }

    if (boolShowDelete) {
      double num = addTop.value - deleteTop;
      if (num >= 0 && num < 26) {
        addTop.value = deleteTop + 26;
      } else if (num < 0 && num > -26) {
        addTop.value = deleteTop - 26;
      }
    }
    bool addDisabled = table.row >= 20;

    onAddHover(final bool hovered) {
      addHovered.value = hovered;
    }

    return Positioned.directional(
      textDirection: TextDirection.ltr,
      start: -60,
      top: -24,
      child: MouseRegion(
        onHover: (final e) {
          getMouseRowIndex(e.localPosition.dy);
        },
        onEnter: (final e) {
          inShowRow.value = true;
        },
        onExit: (final e) {
          inShowRow.value = false;
        },
        child: SizedBox(
          width: 60,
          height: table.height.mm2px().toDouble() + 48,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            textDirection: TextDirection.ltr,
            children: [
              /// 有全选行且不是全选表格才显示
              Stack2(
                textDirection: TextDirection.ltr,
                clipBehavior: Clip.none,
                children: [
                  if (boolShowDelete)
                    Positioned(
                      top: deleteTop,
                      child: Container(
                        padding: const EdgeInsets.only(right: 8.0),
                        child: TableBarButton(
                          onHover: onDeleteHover,
                          onTap: onDelete,
                          hovered: deleteHovered,
                          child: NiimbotIcons.delete(
                              size: 16.0,
                              color: !deleteHovered
                                  ? themeColors.systemFillColorBlack
                                  : themeColors.brandColor),
                        ),
                      ),
                    ),
                  if (boolShowAdd)
                    Positioned(
                      top: addTop.value,
                      child: MouseRegion(
                        onEnter: (final e) {
                          inAddWidget.value = true;
                        },
                        onHover: (final e) {},
                        onExit: (final e) {
                          inAddWidget.value = false;
                        },
                        child: Container(
                          padding: const EdgeInsets.only(right: 8),
                          child: TableBarButton(
                            onTap: onAdd,
                            onHover: onAddHover,
                            disabled: addDisabled,
                            hovered: addHovered.value,
                            child: NiimbotIcons.add(
                                size: 16.0,
                                color: addDisabled
                                    ? themeColors.textFillColorTertiary
                                    : (addHovered.value == false
                                        ? themeColors.systemFillColorBlack
                                        : themeColors.systemFillColorSelected)),
                          ),
                        ),
                      ),
                    ),
                  SizedBox(width: 32)
                ],
              ),
              MouseRegion(
                onEnter: (final e) {
                  inRow.value = true;
                },
                onExit: (final e) {
                  inRow.value = false;
                },
                child: Container(
                  padding: const EdgeInsets.only(top: 25.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: table.rowHeight.foldIndexed<List<Widget>>([],
                        (final i, final p, final e) {
                      p.add(_TableRowRect(
                          size: e,
                          index: i,
                          table: table,
                          hover: deleteHovered,
                          onMouseHover: (final int? index) {
                            mouseIndex.value = index;
                            addTop.value = getAddTop(index! + 1);
                          }));
                      return p;
                    }),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _TableRowRect extends HookWidget {
  final num size;
  final int index;
  final TableElement table;
  final bool hover;
  final ValueChanged<int?>? onMouseHover;

  const _TableRowRect({
    required this.size,
    required this.index,
    required this.table,
    this.hover = false,
    this.onMouseHover,
  });

  @override
  Widget build(final BuildContext context) {
    final themeColors = NiimbotTheme.of(context).colors;
    final isFullRow = useIsFullRow(table, index);
    onSelect() {
      context.read<CanvasStore>().tableController.selectedRow(table.id, index);
    }

    double lineWidth = 0.2.mm2px().toDouble();
    double width = 28;

    double lineOffset = (table.lineWidth) / 2;
    double height = (size +
            table.lineWidth +
            ((index == 0 || index == table.row - 1) ? lineOffset : 0))
        .mm2px()
        .toDouble();

    return Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: !isFullRow
              ? themeColors.solidBackgroundFillColorTertiary.withOpacity(0.5)
              : (hover
                      ? themeColors.brandColor
                      : themeColors.systemFillColorSelected)
                  .withOpacity(0.2),
          border: Border(
            top: BorderSide(
                color: themeColors.borderColorNormal,
                width: index != 0 ? 0 : lineWidth),
            left: BorderSide(
                color: themeColors.borderColorNormal, width: lineWidth),
            bottom: BorderSide(
                color: themeColors.borderColorNormal, width: lineWidth),
          ),
        ),
        child: Stack(
          children: [
            Container(
              alignment: Alignment.center,
              height: height,
              child: Text(
                (index + 1).toString(),
                style: const TextStyle(height: 1),
              ),
            ),
            Positioned(
              child: TableRectSizeBox(
                width: width,
                height: height,
                direction: Axis.vertical,
                onTap: onSelect,
                onMouseHover: (final int add) {
                  onMouseHover?.call(index + add);
                },
              ),
            )
          ],
        ));
  }
}
