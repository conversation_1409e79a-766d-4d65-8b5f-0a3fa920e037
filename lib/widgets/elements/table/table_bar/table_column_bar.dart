import 'package:collection/collection.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';

import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:netal_plugin/utils/ratio.dart';
import 'package:niimbot_flutter_canvas/extensions/table_element.dart';
import 'package:niimbot_flutter_canvas/hooks/use_table_element_state.dart';
import 'package:niimbot_flutter_canvas/widgets/components/stack2.dart';
import 'package:niimbot_template/models/elements/table_element.dart';
import 'package:niimbot_ui/icons/icons.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:provider/provider.dart';

import 'package:niimbot_flutter_canvas/widgets/elements/table/table_bar/table_bar_button.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/table/table_bar/table_rect_size_box.dart';
import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';

Logger _logger = Logger("TableColumnBar", on: kDebugMode);

class TableColumnBar extends HookWidget {
  final TableElement table;
  final num scale;
  final bool deleteHovered;
  final ValueChanged<bool>? onDeleteHover;
  final ValueChanged<int?>? onAddHover;

  const TableColumnBar({
    required this.table,
    required this.scale,
    this.deleteHovered = false,
    this.onDeleteHover,
    this.onAddHover,
    super.key,
  });

  @override
  Widget build(final BuildContext context) {
    final themeColors = NiimbotTheme.of(context).colors;
    final focusedCellsRect = useFocusedCellsRect(table) ?? Rect.zero;
    final deleteLeft = (focusedCellsRect.left + focusedCellsRect.width / 2)
            .mm2px()
            .toDouble() +
        12;

    final addLeft = useState(0.0);

    final inShowColum = useState(false);
    final inColumn = useState(false);
    final inAddWidget = useState(false);
    final addHovered = useState(false);

    final mouseIndex = useRef<int?>(null);
    final boolShowDelete = useIsFullColumn(table);

    final boolShowAdd = useMemoized(() {
      if (inShowColum.value) {
        return true;
      }
      if (inAddWidget.value) {
        return true;
      }
      if (inColumn.value) {
        return true;
      }
      return false;
    }, [inShowColum.value, inAddWidget.value, inColumn.value]);

    double getAddLeft(final int? index) {
      List<num>? addList =
          index! > 0 ? (table.columnWidth.sublist(0, index)) : [];
      return (addList.isNotEmpty
              ? (((addList.reduce((final a, final c) => a + c)) +
                      table.lineWidth * (addList.length + 0.5))
                  .mm2px()
                  .toDouble())
              : 0) +
          12;
    }

    getMouseColumnIndex(final double current) {
      for (int i = 0; i <= table.column; i++) {
        double left = i > 0 ? getAddLeft(i - 1) + 16 : 16;
        double right = getAddLeft(i) + 16;
        if (current >= left && current <= right) {
          int curIndex = current > right - (right - left) / 2 ? i : i - 1;
          mouseIndex.value = curIndex - 1;
          addLeft.value = getAddLeft(curIndex);
          return;
        }
      }
    }

    void onDelete() {
      final ids = context.read<CanvasStore>().getSelected(table.id);
      final fullColumn = table.getSelectedFullColumn(ids);
      context
          .read<CanvasStore>()
          .tableController
          .removeColumnInRange(table.id, fullColumn!.first, fullColumn.last);
      onDeleteHover?.call(false);
    }

    void onAdd() {
      if (mouseIndex.value != null) {
        context
            .read<CanvasStore>()
            .tableController
            .addColumnAfterColumnIndex(table.id, mouseIndex.value!);
      }
    }

    if (boolShowDelete) {
      double num = addLeft.value - deleteLeft;
      if (num >= 0 && num < 26) {
        addLeft.value = deleteLeft + 26;
      } else if (num < 0 && num > -26) {
        addLeft.value = deleteLeft - 26;
      }
    }

    bool addDisabled = table.column >= 20;

    onAddHover(final bool hovered) {
      addHovered.value = hovered;
    }

    return Positioned.directional(
      textDirection: TextDirection.ltr,
      start: -24,
      top: -60,
      child: MouseRegion(
        onHover: (final e) {
          getMouseColumnIndex(e.localPosition.dx);
        },
        onEnter: (final e) {
          inShowColum.value = true;
        },
        onExit: (final e) {
          inShowColum.value = false;
        },
        child: SizedBox(
          width: table.width.mm2px().toDouble() + 48,
          height: 60,
          child: Column(
            children: [
              Stack2(
                textDirection: TextDirection.ltr,
                clipBehavior: Clip.none,
                children: [
                  if (boolShowDelete)
                    Positioned(
                      left: deleteLeft,
                      child: Container(
                        padding: const EdgeInsets.only(bottom: 8),
                        child: TableBarButton(
                          onHover: onDeleteHover,
                          onTap: onDelete,
                          hovered: deleteHovered,
                          child: NiimbotIcons.delete(
                              size: 16.0,
                              color: !deleteHovered
                                  ? themeColors.systemFillColorBlack
                                  : themeColors.brandColor),
                        ),
                      ),
                    ),
                  if (boolShowAdd)
                    Positioned(
                      left: addLeft.value,
                      child: MouseRegion(
                        onEnter: (final e) {
                          inAddWidget.value = true;
                        },
                        onHover: (final e) {},
                        onExit: (final e) {
                          inAddWidget.value = false;
                        },
                        child: Container(
                          padding: const EdgeInsets.only(bottom: 8),
                          child: TableBarButton(
                            onTap: onAdd,
                            onHover: onAddHover,
                            disabled: addDisabled,
                            hovered: addHovered.value,
                            child: NiimbotIcons.add(
                                size: 16.0,
                                color: addDisabled
                                    ? themeColors.textFillColorTertiary
                                    : (addHovered.value == false
                                        ? themeColors.systemFillColorBlack
                                        : themeColors.systemFillColorSelected)),
                          ),
                        ),
                      ),
                    ),
                  SizedBox(
                    width: table.width.mm2px().toDouble() + 48,
                    height: 32,
                  )
                ],
              ),
              MouseRegion(
                  onEnter: (final e) {
                    inColumn.value = true;
                  },
                  onExit: (final e) {
                    inColumn.value = false;
                  },
                  child: Container(
                    padding: const EdgeInsets.only(left: 25.0),
                    child: Row(
                      textDirection: TextDirection.ltr,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: table.columnWidth.foldIndexed<List<Widget>>([],
                          (final i, final p, final e) {
                        p.add(_TableColumnRect(
                          size: e,
                          index: i,
                          table: table,
                          hover: deleteHovered,
                          onMouseHover: (final int? index) {
                            mouseIndex.value = index;
                            addLeft.value = getAddLeft(index! + 1);
                          },
                        ));
                        return p;
                      }),
                    ),
                  ))
            ],
          ),
        ),
      ),
    );
  }
}

class _TableColumnRect extends HookWidget {
  final num size;
  final int index;
  final TableElement table;
  final bool hover;
  final ValueChanged<int?>? onMouseHover;

  const _TableColumnRect({
    required this.size,
    required this.index,
    required this.table,
    this.hover = false,
    this.onMouseHover,
  });

  @override
  Widget build(final BuildContext context) {
    final themeColors = NiimbotTheme.of(context).colors;
    onSelect() {
      context.read<CanvasStore>().tableController.selectedColumn(table.id, index);
    }

    double lineWidth = 0.2.mm2px().toDouble();
    double lineOffset = (table.lineWidth) / 2;
    double width = (size +
            table.lineWidth +
            ((index == 0 || index == table.column - 1) ? lineOffset : 0))
        .mm2px()
        .toDouble();
    double height = 28;
    final isFullColumn = useIsFullColumn(table, index);

    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: !isFullColumn
            ? themeColors.solidBackgroundFillColorTertiary.withOpacity(0.5)
            : (hover
                    ? themeColors.brandColor
                    : themeColors.systemFillColorSelected)
                .withOpacity(0.2),
        border: Border(
          left: BorderSide(
            color: themeColors.borderColorNormal,
            width: index == 0 ? lineWidth : 0,
          ),
          top: BorderSide(
            color: themeColors.borderColorNormal,
            width: lineWidth,
          ),
          right: BorderSide(
            color: themeColors.borderColorNormal,
            width: lineWidth,
          ),
        ),
      ),
      child: Stack(
        children: [
          Center(
            child: Text(String.fromCharCode(index + 65)),
          ),
          Positioned(
            child: TableRectSizeBox(
              width: width,
              height: height,
              onTap: onSelect,
              onMouseHover: (final int add) {
                onMouseHover?.call(index + add);
              },
            ),
          ),
        ],
      ),
    );
  }
}
