import 'package:flutter/material.dart';
import 'package:niimbot_ui/styles/border_radius.dart';
import 'package:niimbot_ui/styles/theme.dart';

class TableBarButton extends StatelessWidget {
  final bool? disabled;
  final bool? hovered;
  final Widget child;
  final VoidCallback? onTap;
  final ValueChanged<bool>? onHover;

  const TableBarButton({
    this.disabled = false,
    this.hovered = false,
    required this.child,
    this.onTap,
    super.key,
    this.onHover,
  });

  @override
  Widget build(final BuildContext context) {
    final themeColors = NiimbotTheme.of(context).colors;

    return Row(
      children: [
        InkWell(
          onHover: disabled == false ? onHover : null,
          onTap: disabled == false ? onTap : null,
          mouseCursor: disabled == false
              ? SystemMouseCursors.click
              : SystemMouseCursors.noDrop,
          child: Container(
            padding: const EdgeInsets.all(3.0),
            decoration: BoxDecoration(
              color: themeColors.systemFillColorWhite,
              border:
                  Border.all(color: themeColors.borderColorNormal, width: 1),
              borderRadius: NiimbotRadius.small,
            ),
            child: child,
          ),
        )
      ],
    );
  }
}
