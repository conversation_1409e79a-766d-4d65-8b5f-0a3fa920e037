import 'package:flutter/material.dart';
import 'package:niimbot_ui/styles/theme.dart';

class TableRectSizeBox extends StatefulWidget {
  final VoidCallback onTap;
  final Axis direction;
  final double? width;
  final double? height;
  final ValueChanged<int>? onMouseHover;

  const TableRectSizeBox({
    super.key,
    required this.onTap,
    this.direction = Axis.horizontal,
    this.width,
    this.height,
    this.onMouseHover,
  });

  @override
  State<TableRectSizeBox> createState() => _TableRectSizeBoxState();
}

class _TableRectSizeBoxState extends State<TableRectSizeBox> {
  bool hovered = false;

  @override
  Widget build(final BuildContext context) {
    final themeColors = NiimbotTheme.of(context).colors;

    return InkWell(
      onTap: widget.onTap,
      onHover: (final e) {
        setState(() {
          hovered = e;
        });
      },
      child: Container(
        width: widget.width,
        height: widget.height,
        color: hovered
            ? themeColors.translucentFillColorTertiary
            : Colors.transparent,
        child: Flex(
          direction: widget.direction,
          children: [
            Expanded(
              child: MouseRegion(
                onEnter: (final e) {
                  widget.onMouseHover?.call(-1);
                },
                onHover: (final e) {
                  widget.onMouseHover?.call(-1);
                },
              ),
            ),
            Expanded(
              child: MouseRegion(
                onEnter: (final e) {
                  widget.onMouseHover?.call(0);
                },
                onHover: (final e) {
                  widget.onMouseHover?.call(0);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
