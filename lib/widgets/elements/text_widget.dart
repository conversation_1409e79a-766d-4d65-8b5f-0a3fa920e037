import 'dart:math';

import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:netal_plugin/models/netal_text_element.dart';
import 'package:netal_plugin/netal_image_result.dart';
import 'package:netal_plugin/netal_plugin.dart';
import 'package:netal_plugin/utils/ratio.dart';
import 'package:niimbot_flutter_canvas/extensions/template_data.dart';
import 'package:niimbot_template/extensions/template_data.dart';
import 'package:niimbot_template/models/elements/date_element.dart';
import 'package:niimbot_template/models/elements/element_enum.dart';

import 'package:niimbot_flutter_canvas/utils/netal_utils.dart';
import 'package:niimbot_flutter_canvas/provider/models/text_element_updater.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/base_widget.dart';
import 'package:niimbot_template/models/elements/text_element.dart';
import 'package:niimbot_template/models/template_data.dart';

Logger _logger = Logger("TextWidget", on: kDebugMode);

class TextWidget extends BaseElementWidget<TextElement> {
  const TextWidget({
    super.key,
    required super.element,
    super.color,
    super.imageCache,
    super.updateElementNetalInfo,
  });

  @override
  netalCallback(final NetalImageResult netalRes, final TemplateData template) {
    _logger.log(element.height, '元素原始高');
    _logger.log(element.width, '元素原始宽');

    final height = netalRes.height.px2mm(false).toDouble();
    final width = netalRes.width.px2mm(false).toDouble();
    TextElementUpdater updater = TextElementUpdater(
      height: height,
      width: width,
      boxStyle: NetalUtils.covertNetalTextBoxStyleValue(netalRes.boxStyle),
    );

    _logger.log(height, '元素新高');
    _logger.log(width, '元素新宽');
    if (width > template.size.width &&
        element.boxStyle == NetalTextBoxStyle.autoWidth) {
      _logger.log('元素进入文本模式相关渲染');
      updater = updater.copyWith(
          boxStyle: NetalTextBoxStyle.autoHeight, width: template.size.width);
      WidgetsBinding.instance.addPostFrameCallback((final _) {
        updateElementNetalInfo?.call(id: element.id, updater: updater);
      });
      return;
    }

    if (element is DateElement) {
      if ((element as DateElement).dateIsRefresh) {
        /* 开启了实时时间 */
        if (((element as DateElement).timeFormat == ElementTimeFormat.HMS ||
            (element as DateElement).timeFormat == ElementTimeFormat.MS)) {
          /* 每秒刷新 */
          int millisecond = DateTime.now().millisecond;
          Future.delayed(Duration(milliseconds: 1000 - millisecond), () {
            updateElementNetalInfo?.call(id: element.id);
          });
        } else {
          /* 每分钟刷新 */
          Future.delayed(const Duration(milliseconds: 1000 * 60), () {
            updateElementNetalInfo?.call(id: element.id);
          });
        }
      }
    }

    /// 更新元素信息
    WidgetsBinding.instance.addPostFrameCallback((final _) {
      updateElementNetalInfo?.call(
          image: netalRes, id: element.id, updater: updater);
    });
  }

  @override
  NetalImageResult? buildNetalResult(final TemplateData template) {
    final page = template.currentPageValue;
    final text = template.coverElementToNetal<NetalTextElement>(element, page);
    if (text.value.contains('\n') &&
        text.boxStyle == NetalTextBoxStyle.autoWidth) {
      WidgetsBinding.instance.addPostFrameCallback((final _) {
        updateElementNetalInfo?.call(
            id: element.id,
            updater: const TextElementUpdater(
                boxStyle: NetalTextBoxStyle.autoHeight));
      });
      return null;
    }
    return NetalPlugin().generateTextElement(
      element: text,
      ratio: RatioUtils().ratio,
      color: color,
      size: template.size,
    );
  }
}
