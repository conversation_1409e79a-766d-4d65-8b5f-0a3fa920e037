import 'package:flutter/cupertino.dart';
import 'package:niimbot_template/models/elements/base_element.dart';
import 'package:niimbot_template/models/elements/bind_element.dart';
import 'package:niimbot_template/models/elements/table_element.dart';
import 'package:niimbot_ui/widgets/niimbot_menu/niimbot_menu_item.dart';

import 'package:niimbot_flutter_canvas/utils/platform_utils.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/context_menu/common_context_menu_widget.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/context_menu/element_context_menu_widget.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/context_menu/table_common_context_menu_widget.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/context_menu/table_context_menu_widget.dart';

/// 右键菜单类型
enum ContextMenuType {
  /// 通用
  common,

  ///元素
  element,
}

class ContextMenuUtils {
  static List<NiimbotMenuItem>? buildContextMenu(
      final BuildContext context, final Offset position, final ContextMenuType type,
      {final BaseElement? element, final VoidCallback? callback}) {
    if (PlatformUtils.isDesktop) {
      if (element is TableElement) {
        if (type == ContextMenuType.common) {
          /// 通用右键菜单
          return TableCommonContextMenuWidget(
              context, position, element, callback);
        }
        if (type == ContextMenuType.element) {
          /// 表格专用右键菜单
          return TableContextMenuWidget(context, element, callback);
        }
      }
      if (type == ContextMenuType.element) {
        bool isBindElement = false;
        if (element != null &&
            (element is BindElement) &&
            element.dataBind != null &&
            element.dataBind!.isNotEmpty) {
          isBindElement = true;
        }

        /// 元素右键菜单
        return ElementContextMenuWidget(context,
            isBindElement: isBindElement, element: element);
      }
      if (type == ContextMenuType.common) {
        /// 通用右键菜单
        return CommonContextMenuWidget(context, position);
      }
    }
    return null;
  }
}
