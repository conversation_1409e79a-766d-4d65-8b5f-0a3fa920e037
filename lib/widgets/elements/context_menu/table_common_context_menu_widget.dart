import 'package:flutter/material.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_template/models/elements/table_element.dart';
import 'package:niimbot_ui/widgets/niimbot_menu/niimbot_menu_item.dart';
import 'package:provider/provider.dart';

import 'package:niimbot_flutter_canvas/provider/models/index.dart';
import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';

/// 通用右键菜单
List<NiimbotMenuItem> TableCommonContextMenuWidget(
    final BuildContext context,
    final Offset position,
    final TableElement element,
    final VoidCallback? callback) {
  return [
    NiimbotMenuItem(
      label: NiimbotIntl.getIntlMessage('app00361', '复制'),
      desc: 'Ctrl+C',
      onPressed: () {
        context.read<CanvasStore>().copyElements();
        callback?.call();
      },
    ),
    NiimbotMenuItem.divider(),
    NiimbotMenuItem(
      label: NiimbotIntl.getIntlMessage('pc0058', '删除表格'),
      desc: 'Delete/Backspace',
      onPressed: () {
        context.read<CanvasStore>().deleteElement();
        callback?.call();
      },
    ),
    if (element.hasContent) ...[
      NiimbotMenuItem.divider(),
      NiimbotMenuItem(
        label: NiimbotIntl.getIntlMessage('pc0059', '清除内容'),
        onPressed: () {
          context.read<CanvasStore>().tableController.clearContent(element.id);
          callback?.call();
        },
      ),
    ],
    NiimbotMenuItem.divider(),
    NiimbotMenuItem(
      label: NiimbotIntl.getIntlMessage('pc0056', '置顶'),
      onPressed: () {
        context
            .read<CanvasStore>()
            .updateSelectedElementsZIndex(ZIndexType.Top);
        callback?.call();
      },
    ),
    NiimbotMenuItem(
      label: NiimbotIntl.getIntlMessage('pc0057', '置底'),
      onPressed: () {
        context
            .read<CanvasStore>()
            .updateSelectedElementsZIndex(ZIndexType.Bottom);
        callback?.call();
      },
    ),
    NiimbotMenuItem.divider(),
    NiimbotMenuItem(
      label: NiimbotIntl.getIntlMessage('pc0277', '自适应标签纸大小'),
      onPressed: () {
        context.read<CanvasStore>().tableAutoLabelSize(element.id);
        callback?.call();
      },
    ),
  ];
}
