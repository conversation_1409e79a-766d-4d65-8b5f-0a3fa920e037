import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:niimbot_flutter_canvas/extensions/table_element.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_template/models/elements/table_cell_element.dart';
import 'package:niimbot_template/models/elements/table_element.dart';
import 'package:niimbot_ui/icons/icons.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:niimbot_ui/widgets/niimbot_menu/niimbot_menu_item.dart';
import 'package:provider/provider.dart';

import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';

final Logger _logger = Logger("TableContextMenuWidget", on: kDebugMode);

/// 表格右键菜单
List<NiimbotMenuItem> TableContextMenuWidget(final BuildContext context,
    final TableElement element, final VoidCallback? callback) {
  final themeColors = NiimbotTheme.of(context).colors;

  final ids = context.read<CanvasStore>().getSelected(element.id);
  final cells = element.getSelectedCells(ids);
  final canUnMerge = cells.length == 1 && cells.first is! TableCellElement;
  final selectedCellsHasContent = element.getSelectedCellsHasContent(ids);
  return [
    NiimbotMenuItem(
      icon: RotatedBox(
          quarterTurns: 2,
          child: NiimbotIcons.arrow(
            color: themeColors.textFillColorPrimary,
            size: 16,
          )),
      label: NiimbotIntl.getIntlMessage('pc0060', '向上插入{}行', param: ['1']),
      disabled: element.row >= 20,
      onPressed: () {
        final ids = context.read<CanvasStore>().getSelected(element.id);
        context.read<CanvasStore>().tableController.addRowAfterRowIndex(
            element.id, element.getSelectedTopRow(ids) - 1);
        callback?.call();
      },
    ),
    NiimbotMenuItem(
      icon: NiimbotIcons.arrow(
        color: themeColors.textFillColorPrimary,
        size: 16,
      ),
      label: NiimbotIntl.getIntlMessage('pc0061', '向下插入{}行', param: ['1']),
      disabled: element.row >= 20,
      onPressed: () {
        final ids = context.read<CanvasStore>().getSelected(element.id);
        context
            .read<CanvasStore>()
            .tableController
            .addRowAfterRowIndex(element.id, element.getSelectedBottomRow(ids));
        callback?.call();
      },
    ),
    NiimbotMenuItem(
      icon: RotatedBox(
          quarterTurns: 1,
          child: NiimbotIcons.arrow(
            color: themeColors.textFillColorPrimary,
            size: 16,
          )),
      label: NiimbotIntl.getIntlMessage('pc0062', '向左插入{}列', param: ['1']),
      disabled: element.column >= 20,
      onPressed: () {
        final ids = context.read<CanvasStore>().getSelected(element.id);
        context.read<CanvasStore>().tableController.addColumnAfterColumnIndex(
            element.id, element.getSelectedLeftColumn(ids) - 1);
        callback?.call();
      },
    ),
    NiimbotMenuItem(
      icon: RotatedBox(
          quarterTurns: 3,
          child: NiimbotIcons.arrow(
            color: themeColors.textFillColorPrimary,
            size: 16,
          )),
      label: NiimbotIntl.getIntlMessage('pc0063', '向右插入{}列', param: ['1']),
      disabled: element.column >= 20,
      onPressed: () {
        final ids = context.read<CanvasStore>().getSelected(element.id);
        context.read<CanvasStore>().tableController.addColumnAfterColumnIndex(
            element.id, element.getSelectedRightColumn(ids));
        callback?.call();
      },
    ),
    NiimbotMenuItem.divider(),
    NiimbotMenuItem(
      label: NiimbotIntl.getIntlMessage('pc0064', '删除所选行'),
      onPressed: () {
        final ids = context.read<CanvasStore>().getSelected(element.id);
        context.read<CanvasStore>().tableController.removeRowInRange(element.id,
            element.getSelectedTopRow(ids), element.getSelectedBottomRow(ids));
        callback?.call();
      },
    ),
    NiimbotMenuItem(
      label: NiimbotIntl.getIntlMessage('pc0065', '删除所选列'),
      onPressed: () {
        final ids = context.read<CanvasStore>().getSelected(element.id);
        context.read<CanvasStore>().tableController.removeColumnInRange(
            element.id,
            element.getSelectedLeftColumn(ids),
            element.getSelectedRightColumn(ids));
        callback?.call();
      },
    ),
    if (ids.length > 1 || canUnMerge) ...[
      NiimbotMenuItem.divider(),
      if (canUnMerge)
        NiimbotMenuItem(
          label: NiimbotIntl.getIntlMessage('pc0077', '拆分单元格'),
          onPressed: () {
            context
                .read<CanvasStore>()
                .tableController
                .unMergeSelectedCell(element.id);
            callback?.call();
          },
        ),
      if (!canUnMerge)
        NiimbotMenuItem(
          label: NiimbotIntl.getIntlMessage('pc0066', '合并单元格'),
          onPressed: () {
            context
                .read<CanvasStore>()
                .tableController
                .mergeSelectedCells(element.id);
            context.read<CanvasStore>().startEdit(ids.first);
            callback?.call();
          },
        )
    ],
    if (selectedCellsHasContent) ...[
      NiimbotMenuItem.divider(),
      NiimbotMenuItem(
        label: NiimbotIntl.getIntlMessage('pc0059', '清除内容'),
        onPressed: () {
          context
              .read<CanvasStore>()
              .tableController
              .clearSelectedContent(element.id);
          callback?.call();
        },
      )
    ],
  ];
}
