import 'package:flutter/material.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_template/models/elements/bar_code_element.dart';
import 'package:niimbot_template/models/elements/base_element.dart';
import 'package:niimbot_template/models/elements/qr_code_element.dart';
import 'package:niimbot_template/models/elements/text_element.dart';
import 'package:niimbot_template/models/template/template_data_source_modify.dart';
import 'package:niimbot_ui/widgets/niimbot_menu/niimbot_menu_item.dart';
import 'package:provider/provider.dart';

import 'package:niimbot_flutter_canvas/business/desktop/models/index.dart';
import 'package:niimbot_flutter_canvas/business/desktop/utils/canvas_bind_element_change_utils.dart';
import 'package:niimbot_flutter_canvas/model/template/template_utils.dart';
import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_flutter_canvas/provider/models/index.dart';
/// 通用右键菜单
List<NiimbotMenuItem> ElementContextMenuWidget(final BuildContext context,
    {final bool isBindElement = false, final BaseElement? element}) {
  NiimbotMenuItem? contextMenuItemFirst() {
    if (element is TextElement) {
      return NiimbotMenuItem(
        label: NiimbotIntl.getIntlMessage('pc0191', '生成为一维码'),
        onPressed: () {
          final element = context.read<CanvasStore>().focusedElement;
          if (element == null) {
            return;
          }
          final newElement = CanvasBindElementChangeTypeUtils.changeBindType(
              element, ExcelSourceType.barcode);
          if (newElement != null) {
            context.read<CanvasStore>().replaceElement(element, newElement);

            ///当是一维码时 取消打印列名勾选
            ///默认添加列名勾选上
            TemplateDataSourceModify templateDataSourceModify =
                TemplateDataSourceModify(
              useTitle: false,
            );
            final canvasData = context.read<CanvasStore>().canvasData;
            Map<String, Map<String, TemplateDataSourceModify>> originModify =
                TemplateUtils.cloneModify(canvasData.dataSourceModifies) ?? {};
            if (originModify.keys.contains(newElement.id)) {
              originModify[newElement.id] = {'0': templateDataSourceModify};
            }
            context
                .read<CanvasStore>()
                .dataSourceController
                .updateTemplateColumnName(originModify);
          }
        },
      );
    }
    if (element is BarCodeElement || element is QRCodeElement) {
      return NiimbotMenuItem(
        label: NiimbotIntl.getIntlMessage('pc0192', '生成为文本'),
        onPressed: () {
          final element = context.read<CanvasStore>().focusedElement;
          if (element == null) {
            return;
          }
          final newElement = CanvasBindElementChangeTypeUtils.changeBindType(
              element, ExcelSourceType.text);
          if (newElement != null) {
            context.read<CanvasStore>().replaceElement(element, newElement);
          }
        },
      );
    }
    return null;
  }

  NiimbotMenuItem? contextMenuItemSec() {
    if (element is TextElement) {
      return NiimbotMenuItem(
        label: NiimbotIntl.getIntlMessage('pc0193', '生成为二维码'),
        onPressed: () {
          final element = context.read<CanvasStore>().focusedElement;
          if (element == null) {
            return;
          }
          final newElement = CanvasBindElementChangeTypeUtils.changeBindType(
              element, ExcelSourceType.qrcode);
          if (newElement != null) {
            context.read<CanvasStore>().replaceElement(element, newElement);

            ///当是二维码时 取消打印列名勾选
            ///默认添加列名勾选上
            TemplateDataSourceModify templateDataSourceModify =
                TemplateDataSourceModify(
              useTitle: false,
            );
            final canvasData = context.read<CanvasStore>().canvasData;
            Map<String, Map<String, TemplateDataSourceModify>> originModify =
                TemplateUtils.cloneModify(canvasData.dataSourceModifies) ?? {};
            if (originModify.keys.contains(newElement.id)) {
              originModify[newElement.id] = {'0': templateDataSourceModify};
            }
            context
                .read<CanvasStore>()
                .dataSourceController
                .updateTemplateColumnName(originModify);
          }
        },
      );
    }
    if (element is QRCodeElement) {
      return NiimbotMenuItem(
        label: NiimbotIntl.getIntlMessage('pc0191', '生成为一维码'),
        onPressed: () {
          final element = context.read<CanvasStore>().focusedElement;
          if (element == null) {
            return;
          }
          final newElement = CanvasBindElementChangeTypeUtils.changeBindType(
              element, ExcelSourceType.barcode);
          if (newElement != null) {
            context.read<CanvasStore>().replaceElement(element, newElement);
            TemplateDataSourceModify templateDataSourceModify =
                TemplateDataSourceModify(
              useTitle: false,
            );
            final canvasData = context.read<CanvasStore>().canvasData;
            Map<String, Map<String, TemplateDataSourceModify>> originModify =
                TemplateUtils.cloneModify(canvasData.dataSourceModifies) ?? {};
            if (originModify.keys.contains(newElement.id)) {
              originModify[newElement.id] = {'0': templateDataSourceModify};
            }
            context
                .read<CanvasStore>()
                .dataSourceController
                .updateTemplateColumnName(originModify);
          }
        },
      );
    }
    if (element is BarCodeElement) {
      return NiimbotMenuItem(
        label: NiimbotIntl.getIntlMessage('pc0193', '生成为二维码'),
        onPressed: () {
          final element = context.read<CanvasStore>().focusedElement;
          if (element == null) {
            return;
          }
          final newElement = CanvasBindElementChangeTypeUtils.changeBindType(
              element, ExcelSourceType.qrcode);
          if (newElement != null) {
            context.read<CanvasStore>().replaceElement(element, newElement);
          }
        },
      );
    }
    return null;
  }

  List<NiimbotMenuItem> dataMenuList() {
    List<NiimbotMenuItem> list = [];
    list.add(NiimbotMenuItem(
      label: NiimbotIntl.getIntlMessage('pc0194', '取消关联数据源'),
      onPressed: () {
        if (element?.id == null) {
          return;
        }
        context
            .read<CanvasStore>()
            .dataSourceController
            .upDateBreakBind(element!.id);
      },
    ));
    if (contextMenuItemFirst() != null) {
      list.add(contextMenuItemFirst()!);
      list.add(NiimbotMenuItem.divider());
    }
    if (contextMenuItemFirst() != null) {
      list.add(contextMenuItemSec()!);
      list.add(NiimbotMenuItem.divider());
    }
    return list;
  }

  return [
    NiimbotMenuItem(
      label: NiimbotIntl.getIntlMessage('app01059', '删除'),
      danger: true,
      desc: 'Delete/Backspace',
      onPressed: context.read<CanvasStore>().deleteElement,
    ),
    NiimbotMenuItem.divider(),
    if (isBindElement) ...dataMenuList(),
    NiimbotMenuItem(
      label: NiimbotIntl.getIntlMessage('app00361', '复制'),
      desc: 'Ctrl+C',
      onPressed: () {
        if (!context.mounted) return;
        context.read<CanvasStore>().copyElements();
      },
    ),
    NiimbotMenuItem(
      label: NiimbotIntl.getIntlMessage('pc0055', '粘贴'),
      desc: 'Ctrl+V',
      onPressed: () async {
        if (!context.mounted) return;
        final rect = context.read<CanvasStore>().getSelectRect();
        if (rect != null) {
          context
              .read<CanvasStore>()
              .pasteElements(offset: Offset(rect.left, rect.bottom + 1));
        }
      },
    ),
    NiimbotMenuItem.divider(),
    NiimbotMenuItem(
      label: NiimbotIntl.getIntlMessage('pc0056', '置顶'),
      onPressed: () => context
          .read<CanvasStore>()
          .updateSelectedElementsZIndex(ZIndexType.Top),
    ),
    NiimbotMenuItem(
      label: NiimbotIntl.getIntlMessage('pc0057', '置底'),
      onPressed: () => context
          .read<CanvasStore>()
          .updateSelectedElementsZIndex(ZIndexType.Bottom),
    ),
  ];
}
