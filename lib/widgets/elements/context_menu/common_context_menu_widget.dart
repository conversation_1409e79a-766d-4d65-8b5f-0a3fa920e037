import 'package:flutter/material.dart';
import 'package:netal_plugin/utils/ratio.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_ui/widgets/niimbot_menu/niimbot_menu_item.dart';
import 'package:provider/provider.dart';

import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';

/// 通用右键菜单
List<NiimbotMenuItem> CommonContextMenuWidget(
    final BuildContext context, final Offset position) {
  return [
    NiimbotMenuItem(
      label: NiimbotIntl.getIntlMessage('pc0055', '粘贴'),
      desc: 'Ctrl+V',
      onPressed: () async {
        if (!context.mounted) return;
        context.read<CanvasStore>().pasteElements(offset: position.px2mm());
      },
    )
  ];
}
