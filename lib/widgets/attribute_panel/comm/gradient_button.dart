import 'package:flutter/material.dart';
import 'package:niimbot_flutter_canvas/widgets/components/hover_builder.dart';
import 'package:niimbot_ui/styles/theme.dart';

class GradientButton extends StatelessWidget {
  const GradientButton({
    super.key,

    /// 按钮宽度
    this.width,

    /// 按钮高度
    this.height,

    /// 按钮文案
    required this.text,

    /// 是否禁用
    this.enable = true,

    /// 点击事件
    required this.onPressed,
    this.focusNode,
  });

  final double? width;
  final double? height;
  final String? text;
  final bool enable;
  final VoidCallback? onPressed;
  final FocusNode? focusNode;

  @override
  Widget build(final BuildContext context) {
    final themeColors = NiimbotTheme.of(context).colors;

    return HoverBuilder(
      builder: (final BuildContext context, final bool hover) {
        return Container(
            width: width,
            height: height,
            decoration: BoxDecoration(
                borderRadius: const BorderRadius.all(Radius.circular(10.0)),
                gradient: hover
                    ? const LinearGradient(colors: [
                        Color.fromRGBO(251, 75, 66, 0),
                        Color.fromRGBO(255, 122, 115, 0),
                      ], begin: Alignment.topLeft, end: Alignment.bottomRight)
                    : const LinearGradient(colors: [
                        Color.fromRGBO(251, 75, 66, 1),
                        Color.fromRGBO(255, 122, 115, 1),
                      ], begin: Alignment.topLeft, end: Alignment.bottomRight),
                boxShadow: const [
                  BoxShadow(
                    color: Color.fromRGBO(225, 40, 40, 0.23),
                    offset: Offset(0, 3),
                    blurRadius: 12,
                    spreadRadius: 0,
                  )
                ]),
            child: TextButton(
                focusNode: focusNode,
                onPressed: enable ? onPressed : null,
                style: ButtonStyle(
                    padding: WidgetStateProperty.all(
                        const EdgeInsets.symmetric(horizontal: 20)),
                    shape: WidgetStateProperty.all(RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10.0),
                    )),
                    backgroundColor:
                        WidgetStateProperty.resolveWith((final states) {
                      if (enable) {
                        if (states.contains(WidgetState.pressed)) {
                          return themeColors.brandColorPressed;
                        }
                        if (states.contains(WidgetState.hovered)) {
                          return themeColors.brandColorHover;
                        }
                        return Colors.transparent;
                      } else {
                        return themeColors.brandColorDisabled;
                      }
                    })),
                child: Center(
                  child: Text('$text',
                      style: NiimbotTheme.of(context)
                          .typography
                          .bodyStrong
                          ?.copyWith(color: themeColors.systemFillColorWhite)),
                )));
      },
    );
  }
}
