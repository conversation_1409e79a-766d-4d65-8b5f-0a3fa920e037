import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';

import 'package:niimbot_flutter_canvas/widgets/detector/system_scale.dart';

enum NiimbotPanGesturePositionMode { local, global }

Logger _logger = Logger("NiimbotGestureRecognizerConflicter");

class NiimbotGestureRecognizerConflicter {
  final List<int> _pointerQueue = <int>[];
  bool isScaleMode = false;
  PointerDownEvent? itemDownEvent;
  PointerDownEvent? bgDownEvent;

  // PointerDownEvent localItemDownEvent;
  PointerEvent? itemMoveEvent;
  static NiimbotGestureRecognizerConflicter? _instance;

  static get instance {
    _instance ??= NiimbotGestureRecognizerConflicter._internal();
    return _instance;
  }

  NiimbotGestureRecognizerConflicter._internal();

  void addPointer(final int pointer) {
    if (!_pointerQueue.contains(pointer)) {
      _pointerQueue.add(pointer);
    }
  }

  void removePointer(final int poiter) {
    _pointerQueue.remove(poiter);
  }

  int pointerCount() {
    return _pointerQueue.length;
  }
}

class NiimbotCustomItemScaleGestureRecognizer
    extends SystemScaleGestureRecognizer {
  NiimbotPanGesturePositionMode mode;

  final List<int> _pointerQueue = <int>[];
  final Map<int, Offset> _pointerLocations = <int, Offset>{};

  NiimbotCustomItemScaleGestureRecognizer(
      {this.mode = NiimbotPanGesturePositionMode.global});

  @override
  void rejectGesture(final int pointer) {
    switch (mode) {
      case NiimbotPanGesturePositionMode.global:
        super.acceptGesture(pointer);
        break;
      case NiimbotPanGesturePositionMode.local:
        super.rejectGesture(pointer);
        break;
    }
  }

  @override
  void handleEvent(final PointerEvent event) {
    handleGlobalEvent(event);
    // switch (mode) {
    //   case NiimbotPanGesturePositionMode.global:
    //     handleGlobalEvent(event);
    //     break;
    //   case NiimbotPanGesturePositionMode.local:
    //     handleGlobalEvent(event);
    //     break;
    // }
  }

  // void handleLocalEvent(PointerEvent event) {
  //   if (event is PointerDownEvent) {
  //     NiimbotGestureRecognizerConflicter.instance.localItemDownEvent = event;
  //     super.handleEvent(event);
  //   } else if (event is PointerUpEvent || event is PointerCancelEvent) {
  //     NiimbotGestureRecognizerConflicter.instance.localItemDownEvent = null;
  //     super.handleEvent(event);
  //   } else {
  //     super.handleEvent(event);
  //   }
  // }

  void handleGlobalEvent(final PointerEvent event) {
    print("handleEvent: ${event.pointer} : $event");
    if (event is PointerDownEvent) {
      if (isPointerOnItem()) {
        super.rejectGesture(event.pointer);
        return;
      }
      _pointerQueue.add(event.pointer);
      _pointerLocations[event.pointer] = event.position;
      NiimbotGestureRecognizerConflicter.instance.itemDownEvent = event;
      NiimbotGestureRecognizerConflicter.instance.addPointer(event.pointer);
      super.handleEvent(event);
    } else if (event is PointerUpEvent || event is PointerCancelEvent) {
      _pointerQueue.remove(event.pointer);
      _pointerLocations.remove(event.pointer);
      NiimbotGestureRecognizerConflicter.instance.itemDownEvent = null;
      NiimbotGestureRecognizerConflicter.instance.removePointer(event.pointer);
      super.handleEvent(event);
    } else {
      if (event is PointerMoveEvent) {
        _pointerLocations[event.pointer] = event.position;
      }
      if ((isPointerOnItem() &&
              !NiimbotGestureRecognizerConflicter.instance.isScaleMode) ||
          (event is PointerUpEvent || event is PointerCancelEvent)) {
        pointerLocations.clear();
        pointerLocations.addAll(_pointerLocations);
        pointerQueue.clear();
        pointerQueue.addAll(_pointerQueue);
        super.handleEvent(event);
      }
    }
  }

  bool isPointerOnItem() {
    return (NiimbotGestureRecognizerConflicter.instance.pointerCount() == 1 &&
        NiimbotGestureRecognizerConflicter.instance.itemDownEvent != null);
  }
}

class NiimbotCustomBgScaleGestureRecognizer
    extends SystemScaleGestureRecognizer {
  late PointerEvent itemMoveEvent;

  final List<int> _pointerQueue = <int>[];
  final Map<int, Offset> _pointerLocations = <int, Offset>{};

  @override
  void rejectGesture(final int pointer) {
    super.acceptGesture(pointer);
  }

  @override
  void handleEvent(final PointerEvent event) {
    print("handleEvent: ${event.pointer} : $event");
    if (event is PointerDownEvent) {
      _pointerQueue.add(event.pointer);
      _pointerLocations[event.pointer] = event.position;
      NiimbotGestureRecognizerConflicter.instance.addPointer(event.pointer);
      if (NiimbotGestureRecognizerConflicter.instance.pointerCount() > 1) {
        NiimbotGestureRecognizerConflicter.instance.isScaleMode = true;
        currentFocalPoint = null;
      }
      pointerLocations.clear();
      pointerLocations.addAll(_pointerLocations);
      super.handleEvent(event);
    } else if (event is PointerUpEvent || event is PointerCancelEvent) {
      _pointerQueue.remove(event.pointer);
      _pointerLocations.remove(event.pointer);
      NiimbotGestureRecognizerConflicter.instance.removePointer(event.pointer);
      if (NiimbotGestureRecognizerConflicter.instance.pointerCount() == 0) {
        NiimbotGestureRecognizerConflicter.instance.isScaleMode = false;
      }
      pointerLocations.clear();
      pointerLocations.addAll(_pointerLocations);
      pointerQueue.clear();
      pointerQueue.addAll(_pointerQueue);
      super.handleEvent(event);
    } else {
      if (event is PointerMoveEvent) {
        _pointerLocations[event.pointer] = event.position;
      }
      if (_pointerQueue.length > 1) {
        pointerLocations.clear();
        pointerLocations.addAll(_pointerLocations);
        pointerQueue.clear();
        pointerQueue.addAll(_pointerQueue);
        super.handleEvent(event);
      } else if (!isPointerOnItem()) {
        pointerLocations.clear();
        pointerLocations.addAll(_pointerLocations);
        pointerQueue.clear();
        pointerQueue.addAll(_pointerQueue);
        super.handleEvent(event);
      }
    }
  }

  bool isPointerOnItem() {
    return (NiimbotGestureRecognizerConflicter.instance.pointerCount() == 1 &&
        NiimbotGestureRecognizerConflicter.instance.itemDownEvent != null);
  }
}

class NiimbotCustomItemScaleGestureDetector extends StatelessWidget {
  final DragStartBehavior? dragStartBehavior;
  final GestureScaleEndCallback? onScaleEnd;
  final GestureScaleStartCallback? onScaleStart;
  final GestureScaleUpdateCallback? onScaleUpdate;
  final HitTestBehavior behavior;
  final GestureTapCallback? onTap;
  final GestureTapDownCallback? onTapDown;
  final NiimbotPanGesturePositionMode mode;
  final DeviceGestureSettings? gestureSettings;

  final Widget child;

  const NiimbotCustomItemScaleGestureDetector({
    super.key,
    required this.behavior,
    this.dragStartBehavior,
    this.onScaleEnd,
    this.onScaleStart,
    this.onScaleUpdate,
    required this.child,
    this.onTap,
    this.onTapDown,
    required this.mode,
    this.gestureSettings,
  });

  @override
  Widget build(final BuildContext context) {
    final Map<Type, GestureRecognizerFactory> gestures =
        <Type, GestureRecognizerFactory>{};
    if (onScaleStart != null || onScaleUpdate != null || onScaleEnd != null) {
      gestures[NiimbotCustomItemScaleGestureRecognizer] =
          GestureRecognizerFactoryWithHandlers<
              NiimbotCustomItemScaleGestureRecognizer>(() {
        return NiimbotCustomItemScaleGestureRecognizer();
      }, (final NiimbotCustomItemScaleGestureRecognizer instance) {
        instance
          ..mode = mode
          ..onUpdate = onScaleUpdate!
          ..dragStartBehavior = dragStartBehavior
          ..onEnd = onScaleEnd
          ..onStart = onScaleStart
          ..gestureSettings = gestureSettings;
      });
    }
    if (onTap != null) {
      gestures[TapGestureRecognizer] =
          GestureRecognizerFactoryWithHandlers<TapGestureRecognizer>(() {
        return TapGestureRecognizer();
      }, (final TapGestureRecognizer instance) {
        instance.onTap = onTap;
      });
    }
    if (onTapDown != null) {
      gestures[TapGestureRecognizer] =
          GestureRecognizerFactoryWithHandlers<TapGestureRecognizer>(() {
            return TapGestureRecognizer();
          }, (final TapGestureRecognizer instance) {
            instance.onTapDown = onTapDown;
          });
    }
    return RawGestureDetector(
      behavior: behavior,
      gestures: gestures,
      child: child,
    );
  }
}

class NiimbotCustomBgScaleGestureDetector extends StatelessWidget {
  final DragStartBehavior dragStartBehavior;
  final GestureScaleEndCallback onScaleEnd;
  final GestureScaleStartCallback onScaleStart;
  final GestureScaleUpdateCallback onScaleUpdate;
  final HitTestBehavior behavior;
  final DeviceGestureSettings? gestureSettings;

  final Widget child;

  const NiimbotCustomBgScaleGestureDetector({
    super.key,
    required this.behavior,
    required this.dragStartBehavior,
    required this.onScaleEnd,
    required this.onScaleStart,
    required this.onScaleUpdate,
    required this.child,
    this.gestureSettings,
  });

  @override
  Widget build(final BuildContext context) {
    final Map<Type, GestureRecognizerFactory> gestures =
        <Type, GestureRecognizerFactory>{};
    gestures[NiimbotCustomBgScaleGestureRecognizer] =
        GestureRecognizerFactoryWithHandlers<
            NiimbotCustomBgScaleGestureRecognizer>(() {
      return NiimbotCustomBgScaleGestureRecognizer();
    }, (final NiimbotCustomBgScaleGestureRecognizer instance) {
      instance
        ..onUpdate = onScaleUpdate
        ..dragStartBehavior = dragStartBehavior
        ..onEnd = onScaleEnd
        ..onStart = onScaleStart
        ..gestureSettings = gestureSettings;
    });

    return RawGestureDetector(
      behavior: behavior,
      gestures: gestures,
      child: child,
    );
  }
}
