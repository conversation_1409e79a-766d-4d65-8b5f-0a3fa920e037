import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:niimbot_flutter_canvas/business/desktop/models/canvas_drag_model.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/canvas_desktop_common_widget/canvas_drag_element_widget.dart';
import 'package:niimbot_ui/styles/theme.dart';

class MenuModel {
  final Widget icon;
  final String label;
  final String identifier;
  final dynamic remark;
  final bool showCircular;

  const MenuModel(
    this.icon,
    this.label, {
    required this.identifier,
    this.remark,
    this.showCircular = false,
  });

  copyWith({
    final Widget? icon,
    final String? label,
    final String? identifier,
    final dynamic remark,
    final bool? showCircular,
  }) {
    return MenuModel(
      icon ?? this.icon,
      label ?? this.label,
      identifier: identifier ?? this.identifier,
      remark: remark ?? this.remark,
      showCircular: showCircular ?? this.showCircular,
    );
  }
}

class Menu extends StatelessWidget {
  final List<MenuModel> itemData;
  final Widget Function(int, Widget) itemBuilder;

  const Menu({
    super.key,
    required this.itemData,
    required this.itemBuilder,
  });

  @override
  Widget build(final BuildContext context) {
    return Container(
        height: double.infinity,
        padding: const EdgeInsets.only(top: 12.0, left: 13.0, right: 13.0),
        child: GridView.count(
            crossAxisSpacing: 4.0,
            mainAxisSpacing: 12.0,
            crossAxisCount: 3,
            children: itemData.map((final item) {
              int index = itemData
                  .indexWhere((final entry) => entry.label == item.label);
              final String identifier = item.identifier;
              Widget railMenu = itemBuilder(
                index,
                _RailMenu(
                  icon: item.icon,
                  label: item.label,
                  showCircular: item.showCircular,
                  identifier: identifier,
                ),
              );
              CanvasDragModel canvasDragModel = CanvasDragModel(
                  canvasDragModelType:
                      CanvasDragModelType.canvasBaseElementType,
                  data: itemData[index]);
              return Draggable(
                  rootOverlay: true,
                  feedback: item.identifier == 'text'
                      ? CanvasDragElementWidget(model: item)
                      : railMenu,
                  ignoringFeedbackSemantics: false,
                  data: canvasDragModel,
                  onDragStarted: () {},
                  onDragEnd: (final DraggableDetails details) {},
                  onDragCompleted: () {},
                  onDraggableCanceled:
                      (final Velocity velocity, final Offset offset) {},
                  child: railMenu);
            }).toList()));
  }
}

class _RailMenu extends HookWidget {
  final Widget icon;
  final String label;
  final String identifier;
  final bool showCircular;

  const _RailMenu({
    required this.icon,
    required this.label,
    required this.identifier,
    this.showCircular = false,
  });

  @override
  Widget build(final BuildContext context) {
    final niimbotColors = NiimbotTheme.of(context).colors;
    final isHovered = useState(false);
    final backHoveredColor = isHovered.value
        ? niimbotColors.solidBackgroundFillColorSecondary
        : Colors.transparent;
    return MouseRegion(
        cursor: SystemMouseCursors.click,
        onEnter: (final _) {
          isHovered.value = true; // 鼠标进入时更新状态
        },
        onExit: (final _) {
          isHovered.value = false; // 鼠标离开时更新状态
        },
        child: Wrap(
          direction: Axis.horizontal,
          crossAxisAlignment: WrapCrossAlignment.center,
          runAlignment: WrapAlignment.start,
          alignment: WrapAlignment.center,
          children: [
            Stack(children: [
              Container(
                  decoration: BoxDecoration(
                    color: backHoveredColor,
                    borderRadius: const BorderRadius.all(Radius.circular(6.0)),
                  ),
                  child: icon),
              if (showCircular)
                Positioned(
                    top: 0,
                    right: 0,
                    child: Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: niimbotColors.brandColor,
                        borderRadius:
                            const BorderRadius.all(Radius.circular(4.0)),
                      ),
                    ))
            ]),
            SizedBox(
              width: 65,
              child: Text(
                textAlign: TextAlign.center,
                softWrap: true,
                label,
                style: NiimbotTheme.of(context).typography.hint?.copyWith(
                    color: isHovered.value
                        ? niimbotColors.textFillColorPrimary
                        : niimbotColors.textFillColorSecondary),
              ),
            )
          ],
        ));
  }
}
