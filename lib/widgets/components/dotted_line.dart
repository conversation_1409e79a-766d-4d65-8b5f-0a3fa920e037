import 'package:flutter/material.dart';

class DottedLine extends StatelessWidget {
  final double width;
  final Color color;
  final Axis axis;
  final int length;
  final int? dottedLength;
  final int? dottedGap;

  const DottedLine({
    super.key,
    required this.width,
    required this.color,
    required this.axis,
    required this.length,
    this.dottedLength,
    this.dottedGap,
  });

  @override
  Widget build(final BuildContext context) {
    return CustomPaint(
      painter: _DottedLinePainter(
          width: width,
          color: color,
          axis: axis,
          length: length,
          dottedLength: dottedLength,
          dottedGap: dottedGap),
    );
  }
}

class _DottedLinePainter extends CustomPainter {
  final double width;
  final Color color;
  final Axis axis;
  int length;
  int dottedLength;
  int dottedGap;
  final Paint _paint;

  _DottedLinePainter({
    final int? length,
    required this.color,
    required this.width,
    required this.axis,
    final int? dottedLength,
    final int? dottedGap,
  })  : length = length ?? 2000,
        dottedLength = dottedLength ?? 3,
        dottedGap = dottedGap ?? 3,
        _paint = Paint()
          ..style = PaintingStyle.stroke
          ..strokeWidth = width
          ..color = color;

  @override
  void paint(final Canvas canvas, final Size size) {
    if (axis == Axis.horizontal) {
      final dottedPath = _covertDotterPath(Path()
        ..moveTo(0, 0)
        ..lineTo(length.toDouble(), 0));
      canvas.drawPath(dottedPath, _paint);
    } else {
      final dottedPath = _covertDotterPath(Path()
        ..moveTo(0, 0)
        ..lineTo(0, length.toDouble()));
      canvas.drawPath(dottedPath, _paint);
    }
  }

  Path _covertDotterPath(final Path path) {
    Path targetPath = Path();
    for (var metric in path.computeMetrics()) {
      double distance = 0;
      bool isDrawDotted = true;
      while (distance < metric.length) {
        if (isDrawDotted) {
          targetPath.addPath(
              metric.extractPath(distance, distance + dottedLength),
              Offset.zero);
          distance += dottedLength;
        } else {
          distance += dottedGap;
        }
        isDrawDotted = !isDrawDotted;
      }
    }
    return targetPath;
  }

  @override
  bool shouldRepaint(covariant final CustomPainter oldDelegate) {
    return false;
  }
}
