import 'dart:math' as math;

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/physics.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:vector_math/vector_math_64.dart' show Quad, Vector3, Matrix4;

import 'package:niimbot_flutter_canvas/utils/keyboard_utils.dart';
import 'package:niimbot_flutter_canvas/utils/platform_utils.dart';
import 'package:niimbot_flutter_canvas/widgets/detector/niimbot_gesture_recognizer_conflicter.dart';

class NiimbotInteractiveViewer extends StatefulHookWidget {
  NiimbotInteractiveViewer({
    super.key,
    this.clipBehavior = Clip.hardEdge,
    this.alignPanAxis = false,
    this.boundaryMargin = EdgeInsets.zero,
    this.constrained = true,
    this.maxScale = 2.5,
    this.minScale = 0.8,
    this.onInteractionEnd,
    this.onInteractionStart,
    this.onInteractionUpdate,
    this.panEnabled = true,
    this.scaleEnabled = true,
    this.transformationController,
    required this.child,
    this.onSelection,
    this.onPointerSignal,
  })  : assert(minScale > 0),
        assert(minScale.isFinite),
        assert(maxScale > 0),
        assert(!maxScale.isNaN),
        assert(maxScale >= minScale),
        // boundaryMargin must be either fully infinite or fully finite, but not
        // a mix of both.
        assert(
          (boundaryMargin.horizontal.isInfinite &&
                  boundaryMargin.vertical.isInfinite) ||
              (boundaryMargin.top.isFinite &&
                  boundaryMargin.right.isFinite &&
                  boundaryMargin.bottom.isFinite &&
                  boundaryMargin.left.isFinite),
        );

  final Clip clipBehavior;

  final bool alignPanAxis;

  final EdgeInsets boundaryMargin;

  final Widget child;
  final bool constrained;

  final bool panEnabled;

  final bool scaleEnabled;

  final double maxScale;

  final double minScale;

  final GestureScaleEndCallback? onInteractionEnd;

  final GestureScaleStartCallback? onInteractionStart;

  final GestureScaleUpdateCallback? onInteractionUpdate;

  final TransformationController? transformationController;

  /// 桌面端框选事件
  final void Function(Rect? rect)? onSelection;
  final PointerSignalEventListener? onPointerSignal;

  /// Returns the closest point to the given point on the given line segment.
  @visibleForTesting
  static Vector3 getNearestPointOnLine(
      final Vector3 point, final Vector3 l1, final Vector3 l2) {
    final double lengthSquared = math.pow(l2.x - l1.x, 2.0).toDouble() +
        math.pow(l2.y - l1.y, 2.0).toDouble();

    // In this case, l1 == l2.
    if (lengthSquared == 0) {
      return l1;
    }

    // Calculate how far down the line segment the closest point is and return
    // the point.
    final Vector3 l1P = point - l1;
    final Vector3 l1L2 = l2 - l1;
    final double fraction =
        (l1P.dot(l1L2) / lengthSquared).clamp(0.0, 1.0).toDouble();
    return l1 + l1L2 * fraction;
  }

  /// Given a quad, return its axis aligned bounding box.
  @visibleForTesting
  static Quad getAxisAlignedBoundingBox(final Quad quad) {
    final double minX = math.min(
      quad.point0.x,
      math.min(
        quad.point1.x,
        math.min(
          quad.point2.x,
          quad.point3.x,
        ),
      ),
    );
    final double minY = math.min(
      quad.point0.y,
      math.min(
        quad.point1.y,
        math.min(
          quad.point2.y,
          quad.point3.y,
        ),
      ),
    );
    final double maxX = math.max(
      quad.point0.x,
      math.max(
        quad.point1.x,
        math.max(
          quad.point2.x,
          quad.point3.x,
        ),
      ),
    );
    final double maxY = math.max(
      quad.point0.y,
      math.max(
        quad.point1.y,
        math.max(
          quad.point2.y,
          quad.point3.y,
        ),
      ),
    );
    return Quad.points(
      Vector3(minX, minY, 0),
      Vector3(maxX, minY, 0),
      Vector3(maxX, maxY, 0),
      Vector3(minX, maxY, 0),
    );
  }

  /// Returns true iff the point is inside the rectangle given by the Quad,
  /// inclusively.
  /// Algorithm from https://math.stackexchange.com/a/190373.
  @visibleForTesting
  static bool pointIsInside(final Vector3 point, final Quad quad) {
    final Vector3 aM = point - quad.point0;
    final Vector3 aB = quad.point1 - quad.point0;
    final Vector3 aD = quad.point3 - quad.point0;

    final double aMAB = aM.dot(aB);
    final double aBAB = aB.dot(aB);
    final double aMAD = aM.dot(aD);
    final double aDAD = aD.dot(aD);

    return 0 <= aMAB && aMAB <= aBAB && 0 <= aMAD && aMAD <= aDAD;
  }

  /// Get the point inside (inclusively) the given Quad that is nearest to the
  /// given Vector3.
  @visibleForTesting
  static Vector3 getNearestPointInside(final Vector3 point, final Quad quad) {
    // If the point is inside the axis aligned bounding box, then it's ok where
    // it is.
    if (pointIsInside(point, quad)) {
      return point;
    }

    // Otherwise, return the nearest point on the quad.
    final List<Vector3> closestPoints = <Vector3>[
      NiimbotInteractiveViewer.getNearestPointOnLine(
          point, quad.point0, quad.point1),
      NiimbotInteractiveViewer.getNearestPointOnLine(
          point, quad.point1, quad.point2),
      NiimbotInteractiveViewer.getNearestPointOnLine(
          point, quad.point2, quad.point3),
      NiimbotInteractiveViewer.getNearestPointOnLine(
          point, quad.point3, quad.point0),
    ];
    double minDistance = double.infinity;
    Vector3? closestOverall;
    for (final Vector3 closePoint in closestPoints) {
      final double distance = math.sqrt(
        math.pow(point.x - closePoint.x, 2) +
            math.pow(point.y - closePoint.y, 2),
      );
      if (distance < minDistance) {
        minDistance = distance;
        closestOverall = closePoint;
      }
    }
    return closestOverall!;
  }

  @override
  createState() => _InteractiveViewerState2();
}

class _InteractiveViewerState2 extends State<NiimbotInteractiveViewer> {
  late TransformationController _transformationController;

  final GlobalKey _childKey = GlobalKey();
  final GlobalKey _parentKey = GlobalKey();

  Animation<Offset>? _animation;
  Axis? _panAxis; // Used with alignPanAxis.
  Offset? _referenceFocalPoint; // Point where the current gesture began.
  double? _scaleStart; // Scale value at start of scaling gesture.
  double? _rotationStart = 0.0; // Rotation at start of rotation gesture.
  double _currentRotation = 0.0; // Rotation of _transformationController.value.
  _GestureType? _gestureType;
  Offset? _selectionStart;

  // /// 是否识别滑动手势，处理在标签纸内滑动手势无需生效问题
  // bool isRecognizerPanGesture = true;

  // TODO(justinmc): Add rotateEnabled parameter to the widget and remove this
  // hardcoded value when the rotation feature is implemented.
  // https://github.com/flutter/flutter/issues/57698
  final bool _rotateEnabled = false;

  // Used as the coefficient of friction in the inertial translation animation.
  // This value was eyeballed to give a feel similar to Google Photos.
  static const double _kDrag = 0.0000135;

  // The _boundaryRect is calculated by adding the boundaryMargin to the size of
  // the child.
  Rect get _boundaryRect {
    assert(_childKey.currentContext != null);
    assert(!widget.boundaryMargin.left.isNaN);
    assert(!widget.boundaryMargin.right.isNaN);
    assert(!widget.boundaryMargin.top.isNaN);
    assert(!widget.boundaryMargin.bottom.isNaN);

    final RenderBox childRenderBox =
        _childKey.currentContext!.findRenderObject() as RenderBox;
    final Size childSize = childRenderBox.size;
    final Rect boundaryRect =
        widget.boundaryMargin.inflateRect(Offset.zero & childSize);
    // Boundaries that are partially infinite are not allowed because Matrix4's
    // rotation and translation methods don't handle infinites well.
    assert(
      boundaryRect.isFinite ||
          (boundaryRect.left.isInfinite &&
              boundaryRect.top.isInfinite &&
              boundaryRect.right.isInfinite &&
              boundaryRect.bottom.isInfinite),
      'boundaryRect must either be infinite in all directions or finite in all directions.',
    );
    return boundaryRect;
  }

  // The Rect representing the child's parent.
  Rect get _viewport {
    assert(_parentKey.currentContext != null);
    final RenderBox parentRenderBox =
        _parentKey.currentContext!.findRenderObject() as RenderBox;
    return Offset.zero & parentRenderBox.size;
  }

  // Return a new matrix representing the given matrix after applying the given
  // translation.
  Matrix4 _matrixTranslate(final Matrix4 matrix, final Offset translation) {
    if (translation == Offset.zero) {
      return matrix.clone();
    }

    final Offset alignedTranslation = widget.alignPanAxis && _panAxis != null
        ? _alignAxis(translation, _panAxis!)
        : translation;

    final Matrix4 nextMatrix = matrix.clone()
      ..translate(
        alignedTranslation.dx,
        alignedTranslation.dy,
      );

    // Transform the viewport to determine where its four corners will be after
    // the child has been transformed.
    final Quad nextViewport = _transformViewport(nextMatrix, _viewport);

    // If the boundaries are infinite, then no need to check if the translation
    // fits within them.
    if (_boundaryRect.isInfinite) {
      return nextMatrix;
    }

    // Expand the boundaries with rotation. This prevents the problem where a
    // mismatch in orientation between the viewport and boundaries effectively
    // limits translation. With this approach, all points that are visible with
    // no rotation are visible after rotation.
    final Quad boundariesAabbQuad = _getAxisAlignedBoundingBoxWithRotation(
      _boundaryRect,
      _currentRotation,
    );

    // If the given translation fits completely within the boundaries, allow it.
    final Offset offendingDistance =
        _exceedsBy(boundariesAabbQuad, nextViewport);
    if (offendingDistance == Offset.zero) {
      return nextMatrix;
    }

    // Desired translation goes out of bounds, so translate to the nearest
    // in-bounds point instead.
    final Offset nextTotalTranslation = _getMatrixTranslation(nextMatrix);
    final double currentScale = matrix.getMaxScaleOnAxis();
    final Offset correctedTotalTranslation = Offset(
      nextTotalTranslation.dx - offendingDistance.dx * currentScale,
      nextTotalTranslation.dy - offendingDistance.dy * currentScale,
    );
    // TODO(justinmc): This needs some work to handle rotation properly. The
    // idea is that the boundaries are axis aligned (boundariesAabbQuad), but
    // calculating the translation to put the viewport inside that Quad is more
    // complicated than this when rotated.
    // https://github.com/flutter/flutter/issues/57698
    final Matrix4 correctedMatrix = matrix.clone()
      ..setTranslation(Vector3(
        correctedTotalTranslation.dx,
        correctedTotalTranslation.dy,
        0.0,
      ));

    // Double check that the corrected translation fits.
    final Quad correctedViewport =
        _transformViewport(correctedMatrix, _viewport);
    final Offset offendingCorrectedDistance =
        _exceedsBy(boundariesAabbQuad, correctedViewport);
    if (offendingCorrectedDistance == Offset.zero) {
      return correctedMatrix;
    }

    // If the corrected translation doesn't fit in either direction, don't allow
    // any translation at all. This happens when the viewport is larger than the
    // entire boundary.
    if (offendingCorrectedDistance.dx != 0.0 &&
        offendingCorrectedDistance.dy != 0.0) {
      return matrix.clone();
    }

    // Otherwise, allow translation in only the direction that fits. This
    // happens when the viewport is larger than the boundary in one direction.
    final Offset unidirectionalCorrectedTotalTranslation = Offset(
      offendingCorrectedDistance.dx == 0.0 ? correctedTotalTranslation.dx : 0.0,
      offendingCorrectedDistance.dy == 0.0 ? correctedTotalTranslation.dy : 0.0,
    );
    return matrix.clone()
      ..setTranslation(Vector3(
        unidirectionalCorrectedTotalTranslation.dx,
        unidirectionalCorrectedTotalTranslation.dy,
        0.0,
      ));
  }

  // Return a new matrix representing the given matrix after applying the given
  // scale.
  Matrix4 _matrixScale(final Matrix4 matrix, final double scale) {
    if (scale == 1.0) {
      return matrix.clone();
    }
    assert(scale != 0.0);

    // Don't allow a scale that results in an overall scale beyond min/max
    // scale.
    final currentScale = _transformationController.value.getMaxScaleOnAxis();
    final double totalScale = math.max(
      currentScale * scale,
      // Ensure that the scale cannot make the child so big that it can't fit
      // inside the boundaries (in either direction).
      math.max(
        _viewport.width / _boundaryRect.width,
        _viewport.height / _boundaryRect.height,
      ),
    );
    final double clampedTotalScale = totalScale.clamp(
      widget.minScale,
      widget.maxScale,
    );
    final double clampedScale = clampedTotalScale / currentScale;
    return matrix.clone()..scale(clampedScale);
  }

  // Return a new matrix representing the given matrix after applying the given
  // rotation.
  Matrix4 _matrixRotate(
      final Matrix4 matrix, final double rotation, final Offset focalPoint) {
    if (rotation == 0) {
      return matrix.clone();
    }
    final Offset focalPointScene = _transformationController.toScene(
      focalPoint,
    );
    return matrix.clone()
      ..translate(focalPointScene.dx, focalPointScene.dy)
      ..rotateZ(-rotation)
      ..translate(-focalPointScene.dx, -focalPointScene.dy);
  }

  // Returns true iff the given _GestureType is enabled.
  bool _gestureIsSupported(final _GestureType? gestureType) {
    switch (gestureType) {
      case _GestureType.rotate:
        return _rotateEnabled;

      case _GestureType.scale:
        return widget.scaleEnabled;

      case _GestureType.pan:
      default:
        return widget.panEnabled;
    }
  }

  _GestureType _getGestureType(final ScaleUpdateDetails details) {
    final double scale = !widget.scaleEnabled ? 1.0 : details.scale;
    final double rotation = !_rotateEnabled ? 0.0 : details.rotation;
    if ((scale - 1).abs() > rotation.abs()) {
      return _GestureType.scale;
    } else if (rotation != 0.0) {
      return _GestureType.rotate;
    } else {
      return _GestureType.pan;
    }
  }

  // Handle an update to an ongoing gesture. All of pan, scale, and rotate are
  // handled with GestureDetector's scale gesture.
  void _onScaleUpdate(final ScaleUpdateDetails details) {
    // if (!isRecognizerPanGesture) {
    //   return;
    // }
    if (_selectionStart != null && !KeyboardUtils.isControlPressed) {
      widget.onSelection
          ?.call(Rect.fromPoints(_selectionStart!, details.localFocalPoint));
    }
    if (PlatformUtils.isDesktop && !KeyboardUtils.isControlPressed) {
      /* 桌面端没有按需CTRL键 不响应 */
      return;
    }
    if (_gestureType == _GestureType.pan) {
      // When a gesture first starts, it sometimes has nos change in scale and
      // rotation despite being a two-finger gesture. Here the gesture is
      // allowed to be reinterpreted as its correct type after originally
      // being marked as a pan.
      _gestureType = _getGestureType(details);
    } else {
      _gestureType ??= _getGestureType(details);
    }
    if (!_gestureIsSupported(_gestureType)) {
      widget.onInteractionUpdate?.call(details);
      return;
    }
    final double scale = _transformationController.value.getMaxScaleOnAxis();
    final Offset focalPointScene = _transformationController.toScene(
      details.localFocalPoint,
    );

    // print("_gestureType ====== > $_gestureType , scale = $scale");
    switch (_gestureType) {
      case _GestureType.scale:
        assert(_scaleStart != null);
        final double desiredScale = _scaleStart! * details.scale;
        final double scaleChange = desiredScale / scale;
        _transformationController.value = _matrixScale(
          _transformationController.value,
          scaleChange,
        );

        final Offset focalPointSceneScaled = _transformationController.toScene(
          details.localFocalPoint,
        );
        _transformationController.value = _matrixTranslate(
          _transformationController.value,
          focalPointSceneScaled - _referenceFocalPoint!,
        );

        final Offset focalPointSceneCheck = _transformationController.toScene(
          details.localFocalPoint,
        );
        if (_round(_referenceFocalPoint!) != _round(focalPointSceneCheck)) {
          _referenceFocalPoint = focalPointSceneCheck;
        }
        break;

      case _GestureType.rotate:
        if (details.rotation == 0.0) {
          widget.onInteractionUpdate?.call(details);
          return;
        }
        final double desiredRotation = _rotationStart! + details.rotation;
        _transformationController.value = _matrixRotate(
          _transformationController.value,
          _currentRotation - desiredRotation,
          details.localFocalPoint,
        );
        _currentRotation = desiredRotation;
        break;

      case _GestureType.pan:
        assert(_referenceFocalPoint != null);
        // details may have a change in scale here when scaleEnabled is false.
        // In an effort to keep the behavior similar whether or not scaleEnabled
        // is true, these gestures are thrown away.
        // print("_gestureType ====== > details.scale =  ${details.scale}");
        if (details.scale != 1.0) {
          widget.onInteractionUpdate?.call(details);
          return;
        }
        _panAxis ??= _getPanAxis(_referenceFocalPoint!, focalPointScene);
        // Translate so that the same point in the scene is underneath the
        // focal point before and after the movement.
        final Offset translationChange =
            focalPointScene - _referenceFocalPoint!;
        _transformationController.value = _matrixTranslate(
          _transformationController.value,
          translationChange,
        );
        _referenceFocalPoint = _transformationController.toScene(
          details.localFocalPoint,
        );
        break;
      case null:
      // TODO: Handle this case.
    }
    widget.onInteractionUpdate?.call(details);
  }

  // Handle mouseWheel scroll events.
  void _receivedPointerSignal(final PointerSignalEvent event) {
    if (KeyboardUtils.isControlPressed) {
      if (event is PointerScrollEvent) {
        // Ignore left and right scroll.
        if (event.scrollDelta.dy == 0.0) {
          return;
        }
        widget.onInteractionStart?.call(
          ScaleStartDetails(
            focalPoint: event.position,
            localFocalPoint: event.localPosition,
          ),
        );
        final scaleVal = -event.scrollDelta.dy / 1000;
        final double scaleChange =
            (_transformationController.value.getMaxScaleOnAxis() + scaleVal)
                .clamp(
          widget.minScale,
          widget.maxScale,
        );
        _onScaleViewer(scaleChange, event.localPosition);
      }
    } else if (PlatformUtils.isDesktop) {
      widget.onPointerSignal?.call(event);
    }
  }

  void _onScaleViewer(final double val, final Offset point) {
    if (!_gestureIsSupported(_GestureType.scale)) {
      widget.onInteractionUpdate?.call(ScaleUpdateDetails(
        focalPoint: point,
        localFocalPoint: point,
        rotation: 0.0,
        scale: val.toDouble(),
        horizontalScale: 1.0,
        verticalScale: 1.0,
      ));
      widget.onInteractionEnd?.call(ScaleEndDetails());
      return;
    }

    final Offset focalPointScene = _transformationController.toScene(
      point,
    );

    _transformationController.value = (Matrix4.identity()..scale(val));

    // After scaling, translate such that the event's position is at the
    // same scene point before and after the scale.
    final Offset focalPointSceneScaled = _transformationController.toScene(
      point,
    );
    _transformationController.value = _matrixTranslate(
      _transformationController.value,
      focalPointSceneScaled - focalPointScene,
    );

    widget.onInteractionUpdate?.call(ScaleUpdateDetails(
      focalPoint: point,
      localFocalPoint: point,
      rotation: 0.0,
      scale: val.toDouble(),
      horizontalScale: 1.0,
      verticalScale: 1.0,
    ));
    widget.onInteractionEnd?.call(ScaleEndDetails());
  }

  void _onTransformationControllerChange() {
    // A change to the TransformationController's value is a change to the
    // state.
    setState(() {});
  }

  @override
  void initState() {
    super.initState();
    _transformationController =
        widget.transformationController ?? TransformationController();
    _transformationController.addListener(_onTransformationControllerChange);
  }

  @override
  void didUpdateWidget(final NiimbotInteractiveViewer oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Handle all cases of needing to dispose and initialize
    // transformationControllers.
    if (oldWidget.transformationController == null) {
      if (widget.transformationController != null) {
        _transformationController
            .removeListener(_onTransformationControllerChange);
        _transformationController.dispose();
        _transformationController = widget.transformationController!;
        _transformationController
            .addListener(_onTransformationControllerChange);
      }
    } else {
      if (widget.transformationController == null) {
        _transformationController
            .removeListener(_onTransformationControllerChange);
        _transformationController = TransformationController();
        _transformationController
            .addListener(_onTransformationControllerChange);
      } else if (widget.transformationController !=
          oldWidget.transformationController) {
        _transformationController
            .removeListener(_onTransformationControllerChange);
        _transformationController = widget.transformationController!;
        _transformationController
            .addListener(_onTransformationControllerChange);
      }
    }
  }

  @override
  void dispose() {
    _transformationController.removeListener(_onTransformationControllerChange);
    if (widget.transformationController == null) {
      _transformationController.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(final BuildContext context) {
    final _controller = useAnimationController();
    // Handle inertia drag animation.
    void _onAnimate() {
      if (!_controller.isAnimating) {
        _panAxis = null;
        _animation?.removeListener(_onAnimate);
        _animation = null;
        _controller.reset();
        return;
      }
      // Translate such that the resulting translation is _animation.value.
      final Vector3 translationVector =
          _transformationController.value.getTranslation();
      final Offset translation =
          Offset(translationVector.x, translationVector.y);
      final Offset translationScene = _transformationController.toScene(
        translation,
      );
      final Offset animationScene = _transformationController.toScene(
        _animation!.value,
      );
      final Offset translationChangeScene = animationScene - translationScene;
      _transformationController.value = _matrixTranslate(
        _transformationController.value,
        translationChangeScene,
      );
    }

    // Handle the end of a gesture of _GestureType. All of pan, scale, and rotate
    // are handled with GestureDetector's scale gesture.
    void _onScaleEnd(final ScaleEndDetails details) {
      if (_selectionStart != null) {
        widget.onSelection?.call(null);
      }
      widget.onInteractionEnd?.call(details);
      _scaleStart = null;
      _rotationStart = null;
      _referenceFocalPoint = null;

      _animation?.removeListener(_onAnimate);
      _controller.reset();

      if (!_gestureIsSupported(_gestureType)) {
        _panAxis = null;
        return;
      }

      // if (!isRecognizerPanGesture) {
      //   // 恢复默认值
      //   isRecognizerPanGesture = true;
      //   return;
      // }

      // If the scale ended with enough velocity, animate inertial movement.
      if (_gestureType != _GestureType.pan ||
          details.velocity.pixelsPerSecond.distance < kMinFlingVelocity) {
        _panAxis = null;
        return;
      }

      final Vector3 translationVector =
          _transformationController.value.getTranslation();
      final Offset translation =
          Offset(translationVector.x, translationVector.y);
      final FrictionSimulation frictionSimulationX = FrictionSimulation(
        _kDrag,
        translation.dx,
        details.velocity.pixelsPerSecond.dx,
      );
      final FrictionSimulation frictionSimulationY = FrictionSimulation(
        _kDrag,
        translation.dy,
        details.velocity.pixelsPerSecond.dy,
      );
      final double tFinal = _getFinalTime(
        details.velocity.pixelsPerSecond.distance,
        _kDrag,
      );
      _animation = Tween<Offset>(
        begin: translation,
        end: Offset(frictionSimulationX.finalX, frictionSimulationY.finalX),
      ).animate(CurvedAnimation(
        parent: _controller,
        curve: Curves.decelerate,
      ));
      _controller.duration = Duration(milliseconds: (tFinal * 1000).round());
      _animation?.addListener(_onAnimate);
      _controller.forward();
    }

    // Handle the start of a gesture. All of pan, scale, and rotate are handled
    // with GestureDetector's scale gesture.
    void _onScaleStart(final ScaleStartDetails details) {
      if (PlatformUtils.isDesktop) {
        _selectionStart = details.localFocalPoint;
      }
      widget.onInteractionStart?.call(details);

      // /// 后续是否识别此手势
      // isRecognizerPanGesture = widget.isSupportRecognizerGesture(
      //     details.focalPoint,
      //     _gestureType,
      //     details.pointerCount,
      //     _transformationController.value);

      if (_controller.isAnimating) {
        _controller.stop();
        _controller.reset();
        _animation?.removeListener(_onAnimate);
        _animation = null;
      }

      _gestureType = null;
      _panAxis = null;
      _scaleStart = _transformationController.value.getMaxScaleOnAxis();
      _referenceFocalPoint = _transformationController.toScene(
        details.localFocalPoint,
      );
      _rotationStart = _currentRotation;
    }

    Widget child = Transform(
      transform: _transformationController.value,
      child: KeyedSubtree(
        key: _childKey,
        child: widget.child,
      ),
    );
    //
    // if (!widget.constrained) {
    //   child = OverflowBox(
    //     alignment: Alignment.topLeft,
    //     minWidth: 0.0,
    //     minHeight: 0.0,
    //     maxWidth: double.infinity,
    //     maxHeight: double.infinity,
    //     child: child,
    //   );
    // }
    //
    // if (widget.clipBehavior != Clip.none) {
    //   child = ClipRect(
    //     clipBehavior: widget.clipBehavior,
    //     child: child,
    //   );
    // }

    // A GestureDetector allows the detection of panning and zooming gestures on
    // the child.
    return Listener(
      key: _parentKey,
      onPointerSignal: _receivedPointerSignal,
      child: NiimbotCustomBgScaleGestureDetector(
        behavior: HitTestBehavior.opaque,
        // Necessary when panning off screen.
        dragStartBehavior: DragStartBehavior.start,
        onScaleEnd: _onScaleEnd,
        onScaleStart: _onScaleStart,
        onScaleUpdate: _onScaleUpdate,
        child: ClipRect(
          child: SizedBox(
            width: double.infinity,
            height: double.infinity,
            child: child,
          ),
        ),
      ),
    );
  }
}

// A classification of relevant user gestures. Each contiguous user gesture is
// represented by exactly one _GestureType.
enum _GestureType {
  pan,
  scale,
  rotate,
}

// Given a velocity and drag, calculate the time at which motion will come to
// a stop, within the margin of effectivelyMotionless.
double _getFinalTime(final double velocity, final double drag) {
  const double effectivelyMotionless = 10.0;
  return math.log(effectivelyMotionless / velocity) / math.log(drag / 100);
}

// Return the translation from the given Matrix4 as an Offset.
Offset _getMatrixTranslation(final Matrix4 matrix) {
  final Vector3 nextTranslation = matrix.getTranslation();
  return Offset(nextTranslation.x, nextTranslation.y);
}

// Transform the four corners of the viewport by the inverse of the given
// matrix. This gives the viewport after the child has been transformed by the
// given matrix. The viewport transforms as the inverse of the child (i.e.
// moving the child left is equivalent to moving the viewport right).
Quad _transformViewport(final Matrix4 matrix, final Rect viewport) {
  final Matrix4 inverseMatrix = matrix.clone()..invert();
  return Quad.points(
    inverseMatrix.transform3(Vector3(
      viewport.topLeft.dx,
      viewport.topLeft.dy,
      0.0,
    )),
    inverseMatrix.transform3(Vector3(
      viewport.topRight.dx,
      viewport.topRight.dy,
      0.0,
    )),
    inverseMatrix.transform3(Vector3(
      viewport.bottomRight.dx,
      viewport.bottomRight.dy,
      0.0,
    )),
    inverseMatrix.transform3(Vector3(
      viewport.bottomLeft.dx,
      viewport.bottomLeft.dy,
      0.0,
    )),
  );
}

// Find the axis aligned bounding box for the rect rotated about its center by
// the given amount.
Quad _getAxisAlignedBoundingBoxWithRotation(
    final Rect rect, final double rotation) {
  final Matrix4 rotationMatrix = Matrix4.identity()
    ..translate(rect.size.width / 2, rect.size.height / 2)
    ..rotateZ(rotation)
    ..translate(-rect.size.width / 2, -rect.size.height / 2);
  final Quad boundariesRotated = Quad.points(
    rotationMatrix.transform3(Vector3(rect.left, rect.top, 0.0)),
    rotationMatrix.transform3(Vector3(rect.right, rect.top, 0.0)),
    rotationMatrix.transform3(Vector3(rect.right, rect.bottom, 0.0)),
    rotationMatrix.transform3(Vector3(rect.left, rect.bottom, 0.0)),
  );
  return NiimbotInteractiveViewer.getAxisAlignedBoundingBox(boundariesRotated);
}

// Return the amount that viewport lies outside of boundary. If the viewport
// is completely contained within the boundary (inclusively), then returns
// Offset.zero.
Offset _exceedsBy(final Quad boundary, final Quad viewport) {
  final List<Vector3> viewportPoints = <Vector3>[
    viewport.point0,
    viewport.point1,
    viewport.point2,
    viewport.point3,
  ];
  Offset largestExcess = Offset.zero;
  for (final Vector3 point in viewportPoints) {
    final Vector3 pointInside =
        NiimbotInteractiveViewer.getNearestPointInside(point, boundary);
    final Offset excess = Offset(
      pointInside.x - point.x,
      pointInside.y - point.y,
    );
    if (excess.dx.abs() > largestExcess.dx.abs()) {
      largestExcess = Offset(excess.dx, largestExcess.dy);
    }
    if (excess.dy.abs() > largestExcess.dy.abs()) {
      largestExcess = Offset(largestExcess.dx, excess.dy);
    }
  }

  return _round(largestExcess);
}

// Round the output values. This works around a precision problem where
// values that should have been zero were given as within 10^-10 of zero.
Offset _round(final Offset offset) {
  return Offset(
    double.parse(offset.dx.toStringAsFixed(9)),
    double.parse(offset.dy.toStringAsFixed(9)),
  );
}

// Align the given offset to the given axis by allowing movement only in the
// axis direction.
Offset _alignAxis(final Offset offset, final Axis axis) {
  switch (axis) {
    case Axis.horizontal:
      return Offset(offset.dx, 0.0);
    case Axis.vertical:
      return Offset(0.0, offset.dy);
  }
}

// Given two points, return the axis where the distance between the points is
// greatest. If they are equal, return null.
Axis? _getPanAxis(final Offset point1, final Offset point2) {
  if (point1 == point2) {
    return null;
  }
  final double x = point2.dx - point1.dx;
  final double y = point2.dy - point1.dy;
  return x.abs() > y.abs() ? Axis.horizontal : Axis.vertical;
}
