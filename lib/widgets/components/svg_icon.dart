import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:niimbot_ui/icons/icon.dart';

import 'package:niimbot_flutter_canvas/widgets/canvas/canvas_theme.dart';

class SvgIcon extends StatelessWidget {
  final String asset;
  final String? package;
  final Color? color;
  final double? side;
  final double? width;
  final double? height;
  final BoxFit fit;
  final bool useDefaultColor;
  final bool matchTextDirection;

  const SvgIcon(this.asset,
      {super.key,
      this.package,
      this.color,
      this.side,
      this.width,
      this.height,
      this.fit = BoxFit.none,
      this.useDefaultColor = true,
      this.matchTextDirection = false});

  @override
  Widget build(final BuildContext context) {
    double? _width = side ?? width;
    double? _height = side ?? height;
    return NiimbotIcon(asset,
        package: package ?? 'niimbot_flutter_canvas',
        size: side,
        // matchTextDirection: matchTextDirection,
        color: color ??
            (useDefaultColor ? CanvasTheme.of(context).iconColor : null),
        // width: _width,
        // height: _height,
        fit: fit);
  }
}
