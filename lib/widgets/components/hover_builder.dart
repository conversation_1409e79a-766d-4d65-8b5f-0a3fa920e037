import 'package:flutter/cupertino.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

class HoverBuilder extends HookWidget {
  final MouseCursor cursor;
  final Widget Function(BuildContext context, bool hover) builder;

  const HoverBuilder({
    super.key,
    required this.builder,
    this.cursor = MouseCursor.defer,
  });

  @override
  Widget build(final BuildContext context) {
    final hovered = useState(false);
    return MouseRegion(
        cursor: cursor,
        onEnter: (final e) {
          hovered.value = true;
        },
        onExit: (final e) {
          hovered.value = false;
        },
        child: builder(context, hovered.value));
  }
}
