import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:niimbot_flutter_canvas/business/desktop/models/canvas_element_type_model.dart';
import 'package:niimbot_flutter_canvas/business/desktop/utils/canvas_upload_utils.dart';
import 'package:niimbot_flutter_canvas/business/desktop/utils/canvas_widget_manager_utils.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/canvas_desktop_common_widget/canvas_tabs_child_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/excel/import_excel.dart';
import 'package:niimbot_flutter_canvas/extensions/template_data.dart';
import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_flutter_canvas/provider/utils/canvas_store_utils.dart';
import 'package:niimbot_flutter_canvas/utils/buried_utils.dart';
import 'package:niimbot_flutter_canvas/utils/debounce_util.dart';
import 'package:niimbot_flutter_canvas/widgets/components/menu.dart';
import 'package:niimbot_http/business/apis/rest/models/material_item.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_template/models/elements/base_element.dart';
import 'package:niimbot_template/models/template/file_data_source.dart';
import 'package:niimbot_template/models/template/file_data_source_rule.dart';
import 'package:niimbot_template/models/template/template_data_source.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:niimbot_ui/widgets/niimbot_popover/niimbot_popover.dart';
import 'package:niimbot_ui/widgets/niimbot_tabs/niimbot_tabs.dart';
import 'package:provider/provider.dart';

class CanvasBoxFrameDesktopLeft extends StatefulWidget {
  const CanvasBoxFrameDesktopLeft({super.key, this.onImportDataSource});

  final Future<TemplateDataSource?> Function(TemplateDataSource dataSource)?
      onImportDataSource;

  @override
  createState() => _CanvasBoxFrameDesktopLeftState();
}

class _CanvasBoxFrameDesktopLeftState extends State<CanvasBoxFrameDesktopLeft>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  int currentIndex = 0;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 1, vsync: this);
  }

  @override
  void dispose() {
    super.dispose();
    _tabController.dispose();
    _scrollController.dispose();
  }

  Future<TemplateDataSource?> _onImportDataSource(
      final TemplateDataSource dataSource) async {
    final maxScrollExtent = _scrollController.position.maxScrollExtent;
    final animateToOffset = maxScrollExtent / 2;
    final scrollOffset = _scrollController.offset;
    if (scrollOffset < maxScrollExtent && scrollOffset < animateToOffset) {
      _scrollController.animateTo(animateToOffset,
          duration: const Duration(milliseconds: 200), curve: Curves.linear);
    }
    return await widget.onImportDataSource?.call(dataSource);
  }

  @override
  Widget build(final BuildContext context) {
    final niimbotColors = NiimbotTheme.of(context).colors;
    return Column(
      children: [
        Container(
            alignment: AlignmentDirectional.topStart,
            child: NiimbotTabBar(
              isScrollable: true,
              controller: _tabController,
              dividerColor: Colors.transparent,
              onTap: (final int index) {
                setState(() {
                  currentIndex = index;
                });
              },
              tabs: [
                Tab(
                  height: 48.0,
                  // maxWidth: 100,
                  child: CanvasTabsChildWidget(
                    text: NiimbotIntl.getIntlMessage('pc0047', '元素'),
                    isSelect: _tabController.animation?.value == 0,
                  ),
                ),
              ],
            )),
        Expanded(
          child: Container(
            decoration: BoxDecoration(
                color: niimbotColors.solidBackgroundFillColorBase,
                border: Border(
                    top: BorderSide(
                        color: niimbotColors.dividerColor, width: 1.0))),
            child: TabBarView(
              controller: _tabController,
              children: [
                SingleChildScrollView(
                  controller: _scrollController,
                  child: Column(
                    children: [
                      const _CanvasElementTypeWidget(),
                      Divider(
                        height: 1,
                        color: niimbotColors.dividerColor,
                      ),
                      ImportExcel(onImportDataSource: _onImportDataSource),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

class _CanvasElementTypeWidget extends HookWidget {
  const _CanvasElementTypeWidget({
    super.key,
  });

  @override
  Widget build(final BuildContext context) {
    final niimbotColors = NiimbotTheme.of(context).colors;
    final initModels =
        useMemoized(() => CanvasElementTypeModels.typeModels(niimbotColors));
    final models = useState(initModels);

    /// models
    void modelsChange(final String identifier, final bool showCircular) {
      models.value = models.value.map<MenuModel>((final e) {
        if (e.identifier == identifier) {
          return e.copyWith(showCircular: showCircular);
        }
        return e;
      }).toList();
    }

    ///添加元素
    void onItemClick(final MenuModel model) async {
      final identifier = model.identifier;
      if (identifier == 'material') {
        return;
      }
      if (identifier == 'pdf') {
        if (context.read<CanvasStore>().canvasData.pdfInfoModel != null) {
          CanvasUploadUtils.showPdfTip(context);
          return;
        }
        BuriedUtils().track('click', '007_014');
        final result = await CanvasUploadUtils.pickPDF(context);
        if (result.$1 != null) {
          final firstElement = result.$2;
          if (firstElement != null) {
            if (!context.mounted) return;
            context.read<CanvasStore>().addElements([firstElement],
                isViewOffset: true,
                fileDataSource: result.$1,
                isAddImage: true,
                ignore: false);

            /// 去除红点、显示第一次剪裁
            modelsChange(identifier, false);
          }
        }
        return;
      }
      if (identifier == 'image') {
        final fileDataSources =
            context.read<CanvasStore>().canvasData.fileDataSources;
        final res = await CanvasUploadUtils.pickImage(context, fileDataSources);
        List<String>? imagePathList = res.$1 ?? [];
        if (!context.mounted || res.$2 == null) return;
        BaseElement element = res.$2!;

        ///单图或者多图判定
        if (imagePathList.length > 1) {
          element = element.copyWith(hasVipRes: true);
          final isVip = await CanvasWidgetManager.sharedInstance()
                  .isLoginVipFunction
                  ?.call() ??
              false;
          BuriedUtils().track('click', '007_070_085',
              ext: {'state': 2, 'is_vip': isVip ? 1 : 0});
          if (!isVip) {
            return;
          }
        } else {
          BuriedUtils()
              .track('click', '007_070_085', ext: {'state': 1, 'is_vip': 2});
        }
        FileDataSource? fileDataSource = imagePathList.length > 1
            ? FileDataSource(
                pageCount: 1,
                pageImages: imagePathList,
                type: 'image',
                rule: [
                  FileDataSourceRule(elementId: element.id, pages: []),
                ],
              )
            : null;
        context.read<CanvasStore>().addElements([element],
            isViewOffset: true,
            isAddImage: true,
            fileDataSource: fileDataSource);
      } else {
        final element = await CanvasStoreUtils.generateElementByIdentifier(
            context, identifier);
        if (element != null) {
          if (!context.mounted) return;
          context.read<CanvasStore>().addElements([element],
              isViewOffset: true, isAddBorder: identifier == 'graph');
        }
      }
    }

    Widget itemChildBuilder(final int index, final Widget itemWidget) {
      if (index == 2 &&
          CanvasWidgetManager.sharedInstance().materialWidget != null) {
        return NiimbotPopover(
          position: NiimbotPopoverPositionEnum.end,
          showArrow: true,
          content: CanvasWidgetManager.sharedInstance().materialWidget!(
              (final MaterialItem item) async {
            CanvasWidgetManager.sharedInstance().onMaterialChanged(
              item,
              false,
              context,
            );
          }),
          child: itemWidget,
        );
      }
      if (index == 7 &&
          CanvasWidgetManager.sharedInstance().borderWidget != null) {
        return NiimbotPopover(
          position: NiimbotPopoverPositionEnum.end,
          showArrow: true,
          content: CanvasWidgetManager.sharedInstance().borderWidget!(
              (final MaterialItem item) async {
            CanvasWidgetManager.sharedInstance().onMaterialChanged(
              item,
              true,
              context,
            );
          }),
          child: itemWidget,
        );
      }
      return itemWidget;
    }

    Widget itemBuilder(final int index, final Widget itemWidget) {
      return GestureDetector(
        child: itemChildBuilder(index, itemWidget),
        onTap: () async {
          if (!DebounceUtil.checkClick()) return;
          onItemClick(models.value[index]);
        },
      );
    }

    return SizedBox(
      height: 320,
      child: Menu(
        itemData: models.value,
        itemBuilder: itemBuilder,
      ),
    );
  }
}
