import 'package:flutter/material.dart';

import 'package:niimbot_ui/styles/theme.dart';
import 'package:provider/provider.dart';

import 'package:niimbot_flutter_canvas/business/desktop/widgets/top_bar/history_actions.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/top_bar/top_bar_right_actions.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/top_bar/top_bar_right_more.dart';
import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';

@immutable
class TopBar extends StatelessWidget {
  final VoidCallback? popBack;
  final VoidCallback? onSave;
  final VoidCallback? onSaveAs;
  final VoidCallback? onPrint;

  const TopBar({
    super.key,
    this.popBack,
    this.onSave,
    this.onSaveAs,
    this.onPrint,
  });

  @override
  Widget build(final BuildContext context) {
    final themeColors = NiimbotTheme.of(context).colors;

    /// 对齐、镜像、旋转 锁定禁用控制
    final showActions = context.select<CanvasStore, bool>((final value) =>
        value.selectedElement.isNotEmpty || value.focusedElement != null);
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 18.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          const Expanded(flex: 1, child: SizedBox(width: 0)),
          const HistoryActions(),
          if (showActions)
            Container(
              width: 1,
              height: 20,
              color: themeColors.dividerColor,
              margin: const EdgeInsets.symmetric(horizontal: 30),
            ),
          if (showActions) const TopBarRightActions(),
          TopBarRightMore(
            onSave: onSave,
            onSaveAs: onSaveAs,
            onPrint: onPrint,
          ),
        ],
      ),
    );
  }
}
