import 'package:flutter/cupertino.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_ui/icons/icons.dart';
import 'package:niimbot_ui/styles/theme.dart';

import 'package:niimbot_flutter_canvas/business/desktop/widgets/canvas_desktop_common_widget/canvas_desktop_ink_icon_widget.dart';

class TopBarLeft extends StatelessWidget {
  final VoidCallback? onBack;

  const TopBarLeft({super.key, this.onBack});

  @override
  Widget build(final BuildContext context) {
    final themeColors = NiimbotTheme.of(context).colors;
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 18.0),
      child: Row(
        children: [
          CanvasDesktopInkIconWidget(
            text: NiimbotIntl.getIntlMessage('app01179', '返回'),
            onTap: () {
              onBack?.call();
            },
            child: NiimbotIcons.redo(
              size: 20.0,
              color: themeColors.textFillColorPrimary,
            ),
          ),
          const SizedBox(width: 10),
          CanvasDesktopInkIconWidget(
            text: NiimbotIntl.getIntlMessage('app00270', '新建'),
            child: NiimbotIcons.create(
              size: 20.0,
              color: themeColors.textFillColorPrimary,
            ),
          ),
          const SizedBox(width: 10),
          CanvasDesktopInkIconWidget(
            text: NiimbotIntl.getIntlMessage('app00400', '打开'),
            child: NiimbotIcons.open(
              size: 20.0,
              color: themeColors.textFillColorPrimary,
            ),
          ),
        ],
      ),
    );
  }
}
