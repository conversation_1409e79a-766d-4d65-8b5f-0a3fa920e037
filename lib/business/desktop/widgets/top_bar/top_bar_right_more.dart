import 'package:flutter/material.dart';
import 'package:niimbot_flutter_canvas/business/desktop/utils/canvas_widget_manager_utils.dart';
import 'package:niimbot_flutter_canvas/extensions/template_data.dart';
import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_flutter_canvas/utils/buried_utils.dart';
import 'package:niimbot_flutter_canvas/widgets/attribute_panel/comm/gradient_button.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_ui/icons/icons.dart';
import 'package:niimbot_ui/styles/border_radius.dart';
import 'package:niimbot_ui/styles/shadow.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:niimbot_ui/widgets/niimbot_dialog/niimbot_dialog_controller.dart';
import 'package:niimbot_ui/widgets/niimbot_menu/niimbot_menu_item.dart';
import 'package:niimbot_ui/widgets/niimbot_popover/niimbot_popover.dart';
import 'package:provider/provider.dart';

class TopBarRightMore extends StatefulWidget {
  final VoidCallback? onSave;
  final VoidCallback? onSaveAs;
  final VoidCallback? onPrint;

  const TopBarRightMore({
    super.key,
    this.onSave,
    this.onSaveAs,
    this.onPrint,
  });

  @override
  State<TopBarRightMore> createState() => _TopBarRightMoreState();
}

class _TopBarRightMoreState extends State<TopBarRightMore> {
  final GlobalKey<NiimbotPopoverState> _popoverKey = GlobalKey();

  @override
  Widget build(final BuildContext context) {
    final themeColors = NiimbotTheme.of(context).colors;

    final saveEnable =
        context.select<CanvasStore, bool>((final v) => v.saveEnable);
    final isIndustryTemplate =
        context.select<CanvasStore, bool>((final v) => v.isIndustryTemplate);
    final showCanvasDataSaveAs =
        context.select<CanvasStore, bool>((final v) => v.showCanvasDataSaveAs);

    saveButtonStyle(final bool enable, {final bool isLeft = true}) {
      const borderRadius = Radius.circular(8.0);
      return ButtonStyle(
        mouseCursor: WidgetStateProperty.all(
            enable ? SystemMouseCursors.click : SystemMouseCursors.noDrop),
        padding: WidgetStateProperty.all(
            const EdgeInsets.symmetric(horizontal: 0.0)),
        backgroundColor: WidgetStateProperty.resolveWith((final states) {
          if (enable) {
            if (states.contains(WidgetState.pressed)) {
              return themeColors.translucentFillColorSecondary;
            }
            if (states.contains(WidgetState.hovered)) {
              return themeColors.translucentFillColorQuarternary;
            }
          }
          return Colors.transparent;
        }),
        overlayColor: WidgetStateProperty.resolveWith(
          (final states) {
            if (enable) {
              if (states.contains(WidgetState.hovered) ||
                  states.contains(WidgetState.pressed)) {
                return Colors.transparent;
              }
            } else {
              return Colors.transparent;
            }
            return null;
          },
        ),
        shape: WidgetStateProperty.all(
          RoundedRectangleBorder(
            borderRadius: showCanvasDataSaveAs
                ? BorderRadius.circular(0.0)
                : (isLeft
                    ? const BorderRadius.only(
                        topLeft: borderRadius,
                        bottomLeft: borderRadius,
                      )
                    : const BorderRadius.only(
                        topRight: borderRadius,
                        bottomRight: borderRadius,
                      )),
          ),
        ),
      );
    }

    return Expanded(
        flex: 1,
        child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 6),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                const SizedBox(
                  width: 10,
                ),
                SizedBox(
                  height: 36.0,
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: themeColors.borderColorNormal),
                      borderRadius: NiimbotRadius.middle,
                    ),
                    child: Row(
                      children: [
                        TextButton(
                          onPressed: () {
                            BuriedUtils()
                                .track('click', '007_013_019', ext: {});
                            if (context
                                        .read<CanvasStore>()
                                        .canvasData
                                        .pdfInfoModel !=
                                    null &&
                                CanvasWidgetManager().getPdfFirstSave() ==
                                    false) {
                              BuriedUtils().track('show', '007_050');
                              NiimbotDialogController().show(
                                width: 400,
                                messageDialog: true,
                                messageIcon: NiimbotIcons.warning(
                                  color: NiimbotTheme.of(context)
                                      .colors
                                      .warningColorNormal,
                                  size: 20,
                                ),
                                title: NiimbotIntl.getIntlMessage(
                                    'pc0399', '仅保存第一页的PDF内容，下次打印时请重新选择PDF文件'),
                                confirmText: NiimbotIntl.getIntlMessage(
                                    'app00707', '我知道了'),
                                onClose: (final tipsDone) {
                                  CanvasWidgetManager().setPdfFirstSave(true);
                                  if (isIndustryTemplate
                                      ? isIndustryTemplate
                                      : saveEnable) widget.onSave?.call();
                                  tipsDone();
                                },
                                onConfirm: (final tipsDone) {
                                  CanvasWidgetManager().setPdfFirstSave(true);
                                  if (isIndustryTemplate
                                      ? isIndustryTemplate
                                      : saveEnable) widget.onSave?.call();
                                  tipsDone();
                                },
                              );
                            } else {
                              if (isIndustryTemplate
                                  ? isIndustryTemplate
                                  : saveEnable) widget.onSave?.call();
                            }
                          },
                          style: saveButtonStyle(isIndustryTemplate
                              ? isIndustryTemplate
                              : saveEnable),
                          child: Container(
                            height: 36.0,
                            alignment: Alignment.center,
                            padding:
                                const EdgeInsets.symmetric(horizontal: 20.0),
                            child: Text(
                              NiimbotIntl.getIntlMessage('app00017', '保存'),
                              // textAlign: TextAlign.center,
                              style: NiimbotTheme.of(context)
                                  .typography
                                  .bodyStrong
                                  ?.copyWith(
                                      color: (isIndustryTemplate
                                              ? isIndustryTemplate
                                              : saveEnable)
                                          ? null
                                          : themeColors.textFillColorTertiary),
                            ),
                          ),
                        ),
                        if (showCanvasDataSaveAs) ...[
                          Container(
                            width: 1,
                            color: themeColors.borderColorNormal,
                          ),
                          NiimbotPopover(
                            key: _popoverKey,
                            trigger: NiimbotPopoverTrigger.manual,
                            position: NiimbotPopoverPositionEnum.bottom,
                            offset: 5,
                            content: Container(
                              width: 176,
                              padding: const EdgeInsets.all(8.0),
                              decoration: BoxDecoration(
                                  color: themeColors.systemFillColorWhite,
                                  borderRadius: NiimbotRadius.middle,
                                  boxShadow: NiimbotShadow.higherShadow),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  NiimbotMenuItem(
                                    label: NiimbotIntl.getIntlMessage(
                                        'app00039', '另存为'),
                                    onPressed: () {
                                      BuriedUtils().track(
                                          'click', '007_013_020',
                                          ext: {});
                                      _popoverKey.currentState?.hide();
                                      widget.onSaveAs?.call();
                                    },
                                  ),
                                ],
                              ),
                            ),
                            child: Container(
                              height: 36.0,
                              constraints: const BoxConstraints(maxWidth: 32.0),
                              child: TextButton(
                                onPressed: () {
                                  _popoverKey.currentState?.show();
                                },
                                style: saveButtonStyle(true),
                                child: RotatedBox(
                                    quarterTurns: 3,
                                    child: NiimbotIcons.chevron(
                                      size: 18,
                                      color: themeColors.textFillColorSecondary,
                                    )),
                              ),
                            ),
                          ),
                        ]
                      ],
                    ),
                  ),
                ),
                const SizedBox(
                  width: 10,
                ),
                GradientButton(
                  height: 36.0,
                  text: NiimbotIntl.getIntlMessage('app00016', '打印'),
                  onPressed: () {
                    widget.onPrint?.call();
                  },
                )
              ],
            )));
  }
}
