import 'package:flutter/cupertino.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:niimbot_flutter_canvas/business/desktop/utils/canvas_widget_manager_utils.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/canvas_desktop_common_widget/canvas_desktop_align_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/canvas_desktop_common_widget/canvas_desktop_ink_icon_widget.dart';
import 'package:niimbot_flutter_canvas/hooks/use_table_element_state.dart';
import 'package:niimbot_flutter_canvas/mixin/memo_mixin.dart';
import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_flutter_canvas/provider/utils/store_status_utils.dart';
import 'package:niimbot_flutter_canvas/utils/buried_utils.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_template/models/elements/base_element.dart';
import 'package:niimbot_template/models/elements/table_element.dart';
import 'package:niimbot_ui/hooks/use_mounted.dart';
import 'package:niimbot_ui/icons/icons.dart';
import 'package:niimbot_ui/styles/border_radius.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:niimbot_ui/widgets/niimbot_popover/niimbot_popover.dart';
import 'package:provider/provider.dart';

class TopBarRightActions extends StatelessWidget {
  const TopBarRightActions({super.key});

  @override
  Widget build(final BuildContext context) {
    final selectedElement = context
        .select<CanvasStore, List<BaseElement>>((final v) => v.selectedElement);
    final focusedElement = context
        .select<CanvasStore, BaseElement?>((final v) => v.focusedElement);
    final mirrorDisabled = !StoreStatusUtils.canMirror(selectedElement);
    final rotateDisabled = !StoreStatusUtils.canRotate(selectedElement);

    /// 是否打开镜像
    bool mirrorOpen =
        StoreStatusUtils.isMirrorOpen(selectedElement, focusedElement);

    /// 是否显示锁定模式
    final isLockIcon =
        !StoreStatusUtils.canLock(selectedElement, focusedElement);
    final onlyTable =
        (selectedElement.length == 1 && selectedElement.first is TableElement)
            ? selectedElement.first as TableElement
            : null;

    /// 是否显示裁剪
    final showCrop = StoreStatusUtils.canCrop(selectedElement);

    return _TopBarRightActionsContainer(
      onlyTable: onlyTable,
      isLockIcon: isLockIcon,
      rotateDisabled: rotateDisabled,
      mirrorDisabled: mirrorDisabled,
      mirrorOpen: mirrorOpen,
      showCrop: showCrop,
    );
  }
}

class _TopBarRightActionsContainer extends HookWidget with MemoWidgetMixin {
  final TableElement? onlyTable;
  final bool isLockIcon;
  final bool rotateDisabled;
  final bool mirrorDisabled;
  final bool mirrorOpen;
  final bool showCrop;

  const _TopBarRightActionsContainer({
    required this.onlyTable,
    required this.isLockIcon,
    required this.rotateDisabled,
    required this.mirrorDisabled,
    required this.mirrorOpen,
    required this.showCrop,
  });

  @override
  List<Object?> get props => [
        onlyTable,
        isLockIcon,
        rotateDisabled,
        mirrorDisabled,
        mirrorOpen,
        showCrop
      ];

  @override
  Widget build(final BuildContext context) {
    final themeColors = NiimbotTheme.of(context).colors;
    final niimbotTypography = NiimbotTheme.of(context).typography;
    final cropPopoverKey = useMemoized(() => GlobalKey<NiimbotPopoverState>());

    final canMergeCell = useCanMergeCell(onlyTable);
    final canUnMergeCell = useCanUnMergeCell(onlyTable);

    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((final _) {
        if (showCrop && !CanvasWidgetManager().getPdfEnter()) {
          cropPopoverKey.currentState?.show();
          CanvasWidgetManager().setPdfEnter(true);
          Future.delayed(Duration(seconds: 3), () {
            cropPopoverKey.currentState?.hide();
          });
        }
      });
    });

    void onRotate() {
      BuriedUtils().track('click', '007_013_014', ext: {});
      context.read<CanvasStore>().baseController.rotateSelectedElements();
    }

    void onMirror() {
      BuriedUtils()
          .track('click', '007_013_015', ext: {'source': mirrorOpen ? 0 : 1});
      context.read<CanvasStore>().baseController.mirrorSelectedElements();
    }

    void onLock() {
      BuriedUtils().track('click', '007_013_016',
          ext: {'source': isLockIcon ? 1 : 0, 'type': 1});
      context.read<CanvasStore>().baseController.updateElementsLock();
    }

    void onCrop() {
      context.read<CanvasStore>().updateElementsCrop();
    }

    void onMergeCells() {
      if (onlyTable != null) {
        BuriedUtils().track('click', '007_013_017', ext: {});
        final id = context.read<CanvasStore>().getSelected(onlyTable!.id).first;
        context
            .read<CanvasStore>()
            .tableController
            .mergeSelectedCells(onlyTable!.id);
        context.read<CanvasStore>().startEdit(id);
      }
    }

    void onUnMergeCells() {
      if (onlyTable != null) {
        BuriedUtils().track('click', '007_013_018', ext: {});
        context
            .read<CanvasStore>()
            .tableController
            .unMergeSelectedCell(onlyTable!.id);
      }
    }

    final cropText = NiimbotIntl.getIntlMessage('pc0396', '裁剪');
    final List<String> cropTextList = NiimbotIntl.getIntlMessage(
      'pc0398',
      '点击可\$图片~',
      param: [cropText],
    ).split(cropText);

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        CanvasDesktopAlignWidget(
          disabled: !isLockIcon,
        ),
        const SizedBox(width: 10),
        CanvasDesktopInkIconWidget(
          disabled: !isLockIcon || rotateDisabled,
          text: NiimbotIntl.getIntlMessage('app00358', '旋转'),
          onTap: onRotate,
          child: NiimbotIcons.rotate(
            size: 20.0,
            color: isLockIcon && !rotateDisabled
                ? themeColors.textFillColorPrimary
                : themeColors.textFillColorTertiary,
          ),
        ),
        const SizedBox(width: 10),
        CanvasDesktopInkIconWidget(
          disabled: !isLockIcon || mirrorDisabled,
          text: mirrorOpen
              ? NiimbotIntl.getIntlMessage('pc0054', '取消镜像')
              : NiimbotIntl.getIntlMessage('app01424', '镜像'),
          onTap: onMirror,
          child: NiimbotIcons.mirrorImage(
            size: 20.0,
            color: isLockIcon && !mirrorDisabled
                ? themeColors.textFillColorPrimary
                : themeColors.textFillColorTertiary,
          ),
        ),
        const SizedBox(width: 10),
        CanvasDesktopInkIconWidget(
          text: isLockIcon
              ? NiimbotIntl.getIntlMessage('app00362', '锁定')
              : NiimbotIntl.getIntlMessage('app01063', '解锁'),
          onTap: onLock,
          child: isLockIcon
              ? NiimbotIcons.lock(
                  size: 20.0, color: themeColors.textFillColorPrimary)
              : NiimbotIcons.unlock(
                  size: 20.0, color: themeColors.textFillColorPrimary),
        ),
        const SizedBox(width: 10),
        if (showCrop)
          NiimbotPopover(
            key: cropPopoverKey,
            trigger: NiimbotPopoverTrigger.manual,
            showArrow: true,
            content: Container(
              padding: EdgeInsets.symmetric(vertical: 10, horizontal: 16),
              decoration: BoxDecoration(
                color: themeColors.systemFillColorWhite,
                borderRadius: NiimbotRadius.middle,
              ),
              child: Row(
                children: [
                  Text(
                    cropTextList.firstOrNull ?? '',
                    style: niimbotTypography.bodyStrong?.copyWith(
                      fontSize: 15,
                    ),
                  ),
                  Text(
                    cropText,
                    style: niimbotTypography.bodyStrong?.copyWith(
                      fontSize: 15,
                      fontWeight: FontWeight.w600,
                      color: themeColors.brandColor,
                    ),
                  ),
                  Text(
                    cropTextList.lastOrNull ?? '',
                    style: niimbotTypography.bodyStrong?.copyWith(
                      fontSize: 15,
                      color: themeColors.textFillColorPrimary,
                    ),
                  ),
                ],
              ),
            ),
            child: CanvasDesktopInkIconWidget(
              text: cropText,
              onTap: onCrop,
              child: NiimbotIcons.crop(
                  size: 20.0, color: themeColors.textFillColorPrimary),
            ),
          ),
        if (onlyTable != null) ...[
          if (canMergeCell) ...[
            const SizedBox(width: 10),
            CanvasDesktopInkIconWidget(
              text: NiimbotIntl.getIntlMessage('app100000860', '合并'),
              onTap: onMergeCells,
              child: NiimbotIcons.merge(
                  size: 20.0, color: themeColors.textFillColorPrimary),
            ),
          ],
          if (canUnMergeCell) ...[
            const SizedBox(width: 10),
            CanvasDesktopInkIconWidget(
              text: NiimbotIntl.getIntlMessage('app100000861', '拆分'),
              onTap: onUnMergeCells,
              child: NiimbotIcons.split(
                  size: 20.0, color: themeColors.textFillColorPrimary),
            )
          ]
        ],
      ],
    );
  }
}
