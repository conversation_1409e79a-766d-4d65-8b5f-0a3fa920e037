import 'package:flutter/material.dart';

import 'package:flutter/cupertino.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_ui/icons/icons.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:provider/provider.dart';

import 'package:niimbot_flutter_canvas/utils/buried_utils.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/canvas_desktop_common_widget/canvas_desktop_ink_icon_widget.dart';
import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';

class HistoryActions extends StatelessWidget {
  const HistoryActions({super.key});

  @override
  Widget build(final BuildContext context) {
    final themeColors = NiimbotTheme.of(context).colors;

    void onUndo() {
      BuriedUtils().track('click', '007_013_011', ext: {});
      context.read<CanvasStore>().undo();
    }

    void oRedo() {
      BuriedUtils().track('click', '007_013_012', ext: {});
      context.read<CanvasStore>().redo();
    }

    return Row(
      children: [
        Selector<CanvasStore, bool>(
            selector: (final _, final v) => v.canUndo,
            builder: (final _, final canUndo, final __) {
              return CanvasDesktopInkIconWidget(
                disabled: !canUndo,
                text: NiimbotIntl.getIntlMessage('app00359', '撤销'),
                onTap: onUndo,
                child: NiimbotIcons.redo(
                  size: 20.0,
                  color: canUndo
                      ? themeColors.textFillColorPrimary
                      : themeColors.textFillColorTertiary,
                ),
              );
            }),
        const SizedBox(width: 10),
        Selector<CanvasStore, bool>(
            selector: (final _, final v) => v.canRedo,
            builder: (final _, final canRedo, final __) {
              return CanvasDesktopInkIconWidget(
                disabled: !canRedo,
                text: NiimbotIntl.getIntlMessage('app00360', '恢复'),
                onTap: oRedo,
                child: NiimbotIcons.undo(
                  size: 20.0,
                  color: canRedo
                      ? themeColors.textFillColorPrimary
                      : themeColors.textFillColorTertiary,
                ),
              );
            }),
      ],
    );
  }
}
