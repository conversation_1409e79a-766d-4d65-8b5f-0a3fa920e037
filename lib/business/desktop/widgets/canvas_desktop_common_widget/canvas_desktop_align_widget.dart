import 'package:flutter/material.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_ui/icons/icons.dart';
import 'package:niimbot_ui/styles/border_radius.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:niimbot_ui/widgets/niimbot_popover/niimbot_popover.dart';
import 'package:niimbot_ui/widgets/niimbot_tooltip/niimbot_tooltip.dart';
import 'package:provider/provider.dart';

import 'package:niimbot_flutter_canvas/business/desktop/widgets/canvas_desktop_common_widget/canvas_desktop_ink_icon_widget.dart';
import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_flutter_canvas/provider/models/index.dart';
import 'package:niimbot_flutter_canvas/utils/buried_utils.dart';

class CanvasDesktopAlignWidget extends StatelessWidget {
  final bool disabled;

  const CanvasDesktopAlignWidget({
    this.disabled = false,
    super.key,
  });

  @override
  Widget build(final BuildContext context) {
    final NiimbotColors = NiimbotTheme.of(context).colors;
    int selectedLength =
        context.select<CanvasStore, int>((final v) => v.selectedElement.length);

    onAlignLeft() {
      BuriedUtils().track('click', '007_013_013', ext: {});
      context
          .read<CanvasStore>()
          .baseController
          .alignSelectedElements(AlignType.HorizontalLeft);
      ContextMenuController.removeAny();
    }

    onAlignHorizontalCenter() {
      BuriedUtils().track('click', '007_013_013', ext: {});
      context
          .read<CanvasStore>()
          .baseController
          .alignSelectedElements(AlignType.HorizontalCenter);
      ContextMenuController.removeAny();
    }

    onAlignRight() {
      BuriedUtils().track('click', '007_013_013', ext: {});
      context
          .read<CanvasStore>()
          .baseController
          .alignSelectedElements(AlignType.HorizontalRight);
      ContextMenuController.removeAny();
    }

    onAlignTop() {
      BuriedUtils().track('click', '007_013_013', ext: {});
      context
          .read<CanvasStore>()
          .baseController
          .alignSelectedElements(AlignType.VerticalTop);
      ContextMenuController.removeAny();
    }

    onAlignVerticalCenter() {
      BuriedUtils().track('click', '007_013_013', ext: {});
      context
          .read<CanvasStore>()
          .baseController
          .alignSelectedElements(AlignType.VerticalCenter);
      ContextMenuController.removeAny();
    }

    onAlignBottom() {
      BuriedUtils().track('click', '007_013_013', ext: {});
      context
          .read<CanvasStore>()
          .baseController
          .alignSelectedElements(AlignType.VerticalBottom);
      ContextMenuController.removeAny();
    }

    onIsometricHorizontal() {
      BuriedUtils().track('click', '007_013_013', ext: {});
      context
          .read<CanvasStore>()
          .baseController
          .isometricSelectedElements(IsometricType.Horizontal);
      ContextMenuController.removeAny();
    }

    onIsometricVertical() {
      BuriedUtils().track('click', '007_013_013', ext: {});
      context
          .read<CanvasStore>()
          .baseController
          .isometricSelectedElements(IsometricType.Vertical);
      ContextMenuController.removeAny();
    }

    return CanvasDesktopInkIconWidget(
      disabled: disabled,
      padding: const EdgeInsets.symmetric(vertical: 2.0),
      text: NiimbotIntl.getIntlMessage("app00168", "对齐"),
      child: NiimbotPopover(
        disabled: disabled,
        trigger: NiimbotPopoverTrigger.both,
        position: NiimbotPopoverPositionEnum.bottomStart,
        content: Container(
          width: 200.0,
          padding: const EdgeInsets.all(4.0),
          decoration: BoxDecoration(
            borderRadius: NiimbotRadius.middle,
          ),
          child: Column(
            children: [
              MouseRegionWidget(
                text: NiimbotIntl.getIntlMessage("pc0010", "左对齐"),
                onTap: onAlignLeft,
                child: NiimbotIcons.alignLeft(
                  size: 16.0,
                  color: NiimbotColors.textFillColorPrimary,
                ),
              ),
              MouseRegionWidget(
                text: NiimbotIntl.getIntlMessage("pc0011", "水平居中"),
                onTap: onAlignHorizontalCenter,
                child: NiimbotIcons.alignHorizontalCenter(
                  size: 16.0,
                  color: NiimbotColors.textFillColorPrimary,
                ),
              ),
              MouseRegionWidget(
                text: NiimbotIntl.getIntlMessage("pc0012", "右对齐"),
                onTap: onAlignRight,
                child: NiimbotIcons.alignRight(
                  size: 16.0,
                  color: NiimbotColors.textFillColorPrimary,
                ),
              ),
              MouseRegionWidget(
                text: NiimbotIntl.getIntlMessage("pc0013", "顶对齐"),
                onTap: onAlignTop,
                child: NiimbotIcons.alignTop(
                  size: 16.0,
                  color: NiimbotColors.textFillColorPrimary,
                ),
              ),
              MouseRegionWidget(
                text: NiimbotIntl.getIntlMessage("pc0014", "垂直居中"),
                onTap: onAlignVerticalCenter,
                child: NiimbotIcons.alignVerticalCenter(
                  size: 16.0,
                  color: NiimbotColors.textFillColorPrimary,
                ),
              ),
              MouseRegionWidget(
                text: NiimbotIntl.getIntlMessage("pc0015", "底对齐"),
                onTap: onAlignBottom,
                child: NiimbotIcons.alignDown(
                  size: 16.0,
                  color: NiimbotColors.textFillColorPrimary,
                ),
              ),
              MouseRegionWidget(
                disabled: selectedLength < 3,
                text: NiimbotIntl.getIntlMessage("pc0016", "水平等间距"),
                onTap: onIsometricHorizontal,
                child: NiimbotIcons.horizontalDistribute(
                  size: 16.0,
                  color: selectedLength >= 3
                      ? NiimbotColors.textFillColorPrimary
                      : NiimbotColors.textFillColorSecondary,
                ),
              ),
              MouseRegionWidget(
                disabled: selectedLength < 3,
                text: NiimbotIntl.getIntlMessage("pc0017", "垂直等间距"),
                onTap: onIsometricVertical,
                child: NiimbotIcons.verticalDistribute(
                  size: 16.0,
                  color: selectedLength >= 3
                      ? NiimbotColors.textFillColorPrimary
                      : NiimbotColors.textFillColorSecondary,
                ),
              ),
            ],
          ),
        ),
        child: Row(
          children: [
            Directionality.of(context) == TextDirection.rtl
                ? NiimbotIcons.alignRight(
                    size: 20.0,
                    color: !disabled
                        ? NiimbotColors.textFillColorPrimary
                        : NiimbotColors.textFillColorTertiary,
                  )
                : NiimbotIcons.alignLeft(
                    size: 20.0,
                    color: !disabled
                        ? NiimbotColors.textFillColorPrimary
                        : NiimbotColors.textFillColorTertiary,
                  ),
            RotatedBox(
                quarterTurns: 3,
                child: NiimbotIcons.chevron(
                  size: 14.0,
                  color: !disabled
                      ? NiimbotColors.textFillColorPrimary
                      : NiimbotColors.textFillColorTertiary,
                ))
          ],
        ),
      ),
    );
  }
}

class MouseRegionWidget extends StatefulWidget {
  final Widget child;
  final String text;
  final bool disabled;
  final void Function()? onTap;

  const MouseRegionWidget({
    super.key,
    required this.child,
    required this.text,
    this.disabled = false,
    this.onTap,
  });

  @override
  State<MouseRegionWidget> createState() => _MouseRegionWidgetState();
}

class _MouseRegionWidgetState extends State<MouseRegionWidget> {
  bool focusBool = false;

  @override
  Widget build(final BuildContext context) {
    final NiimbotColors = NiimbotTheme.of(context).colors;

    return MouseRegion(
        cursor: !widget.disabled
            ? SystemMouseCursors.click
            : SystemMouseCursors.noDrop,
        onEnter: (final e) {
          if (!widget.disabled) {
            setState(() {
              focusBool = true;
            });
          }
        },
        onExit: (final e) {
          if (!widget.disabled) {
            setState(() {
              focusBool = false;
            });
          }
        },
        child: GestureDetector(
          onTap: () {
            if (!widget.disabled) widget.onTap!();
          },
          child: Container(
            decoration: BoxDecoration(
              color: focusBool
                  ? NiimbotColors.translucentFillColorQuarternary
                  : Colors.transparent,
              borderRadius: NiimbotRadius.small,
            ),
            child: Row(
              children: [
                const SizedBox(
                  width: 8,
                  height: 32,
                ),
                widget.child,
                const SizedBox(width: 8),
                Expanded(
                  flex: 1,
                  child: NiimbotToolTip(
                    text: widget.text,
                    textStyle: NiimbotTheme.of(context)
                        .typography
                        .body
                        ?.copyWith(
                            color: !widget.disabled
                                ? NiimbotColors.textFillColorPrimary
                                : NiimbotColors.textFillColorTertiary),
                    position: NiimbotPopoverPositionEnum.top,
                  ),
                ),
              ],
            ),
          ),
        ));
  }
}
