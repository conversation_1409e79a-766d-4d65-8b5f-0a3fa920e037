import 'package:flutter/cupertino.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_ui/styles/theme.dart';

import 'package:niimbot_flutter_canvas/widgets/components/menu.dart';

///拖拽视图 当前只针对文本
class CanvasDragElementWidget extends StatelessWidget {
  final MenuModel model;

  const CanvasDragElementWidget({required this.model, super.key});

  @override
  Widget build(final BuildContext context) {
    return Text(
      NiimbotIntl.getIntlMessage(
        'pc0371',
        '双击编辑',
      ),
      style: NiimbotTheme.maybeOf(context)
          ?.typography
          .bodyStrong
          ?.copyWith(fontSize: 20),
    );
  }
}
