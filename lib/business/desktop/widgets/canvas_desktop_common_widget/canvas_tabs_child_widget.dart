import 'package:flutter/cupertino.dart';
import 'package:niimbot_ui/styles/theme.dart';

class CanvasTabsChildWidget extends StatefulWidget {
  final String text;
  final bool isSelect;

  const CanvasTabsChildWidget({
    required this.text,
    required this.isSelect,
    super.key,
  });

  @override
  State<CanvasTabsChildWidget> createState() => _CanvasTabsChildWidgetState();
}

class _CanvasTabsChildWidgetState extends State<CanvasTabsChildWidget> {
  bool isHover = false;

  @override
  Widget build(final BuildContext context) {
    final niimbotColors = NiimbotTheme.of(context).colors;
    final niimbotTypography = NiimbotTheme.of(context).typography;

    return MouseRegion(
      onExit: (final e) {
        isHover = false;
        setState(() {});
      },
      onEnter: (final e) {
        isHover = true;
        setState(() {});
      },
      child: Text(
        widget.text,
        softWrap: true,
        style: TextStyle(
            color: (isHover || widget.isSelect)
                ? niimbotColors.textFillColorPrimary
                : niimbotColors.textFillColorSecondary,
            fontSize: niimbotTypography.titleStrong?.fontSize,),
      ),
    );
  }
}
