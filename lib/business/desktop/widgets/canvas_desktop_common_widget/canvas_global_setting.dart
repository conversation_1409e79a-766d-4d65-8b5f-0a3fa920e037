import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_ui/icons/icons.dart';
import 'package:niimbot_ui/model/niimbot_dropdown_model.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:niimbot_ui/widgets/niimbot_toast/niimbot_toast_controller.dart';

import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/check_box_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/number_input_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/select_contain_title_widget.dart';
import 'package:niimbot_flutter_canvas/model/template/template_utils.dart';

class CanvasGlobalSetting extends StatefulWidget {
  final List<NiimbotDropDownModel<int?>> consumableList;
  final List<NiimbotDropDownModel<int?>> paperTypeList;
  final num width;
  final num height;
  final int consumableType;
  final int paperType;
  final num rotate;
  final bool isCable;
  final NetalCableDirection? cableDirection;
  final num cableLength;
  final bool readonly;
  final bool showRotate;
  final bool showCable;
  final Function({num? width, num? height}) onSizeChanged;
  final ValueChanged<int?> onCustomizeChanged;
  final ValueChanged<int?> onPaperTypeChanged;
  final ValueChanged<num?>? onRotateChanged;
  final ValueChanged<bool> onIsCableChanged;
  final ValueChanged<NetalCableDirection?> onCableDirectionChanged;
  final ValueChanged<num> onCableLengthChanged;
  final List<Widget>? children;

  const CanvasGlobalSetting({
    super.key,
    required this.consumableList,
    required this.paperTypeList,
    required this.width,
    required this.height,
    required this.consumableType,
    required this.paperType,
    required this.rotate,
    required this.isCable,
    this.cableDirection,
    required this.cableLength,
    this.readonly = false,
    this.showRotate = false,
    this.showCable = false,
    required this.onSizeChanged,
    required this.onCustomizeChanged,
    required this.onPaperTypeChanged,
    this.onRotateChanged,
    required this.onIsCableChanged,
    required this.onCableDirectionChanged,
    required this.onCableLengthChanged,
    this.children,
  });

  @override
  State<CanvasGlobalSetting> createState() => _CanvasGlobalSettingState();
}

class _CanvasGlobalSettingState extends State<CanvasGlobalSetting> {
  @override
  Widget build(final BuildContext context) {
    /// 出纸方向
    List<NiimbotDropDownModel<num>> rotateList = [
      NiimbotDropDownModel(
          label: NiimbotIntl.getIntlMessage('app100000597', '向上'), value: 0),
      NiimbotDropDownModel(
          label: NiimbotIntl.getIntlMessage('app100000598', '向右'), value: 270),
      NiimbotDropDownModel(
          label: NiimbotIntl.getIntlMessage('app100000599', '向下'), value: 180),
      NiimbotDropDownModel(
          label: NiimbotIntl.getIntlMessage('app100000600', '向左'), value: 90),
    ];

    /// 尾巴方向
    final List<NiimbotDropDownModel<NetalCableDirection>> cableDirectionList =
        NetalCableDirection.values
            .map((final e) => NiimbotDropDownModel(
                value: e, label: TemplateUtils.getCableDirectionName(e)))
            .toList();

    /// 宽高校验
    num sizeChecked(num value, {final num? width, final num? height}) {
      if (value < 5 || value > 350) {
        String msg = width != null
            ? NiimbotIntl.getIntlMessage('app100000715', '宽度范围5mm~350mm')
            : NiimbotIntl.getIntlMessage('app100000714', '高度范围5mm~350mm');
        NiimbotToastController().show(msg,
            icon: NiimbotIcons.tips(
              color: NiimbotTheme.of(context).colors.textFillColorPrimary,
              size: 20.00,
            ));
        value = (width ?? height)!;
      }
      return value;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        ...?widget.children,
        NumberInputWidget(
          leftTitle: NiimbotIntl.getIntlMessage('app100000544', '标签纸宽度'),
          digit: 2,
          value: widget.width,
          readonly: widget.readonly,
          max: 99999,
          onBlur: (final num value) {
            final val = sizeChecked(value, width: widget.width);
            if (val == widget.width) {
              setState(() {});
            }
            widget.onSizeChanged(width: val);
          },
        ),
        const SizedBox(height: 12),
        NumberInputWidget(
          leftTitle: NiimbotIntl.getIntlMessage('app100000545', '标签纸高度'),
          digit: 2,
          value: widget.height,
          readonly: widget.readonly,
          max: 99999,
          onBlur: (final num value) {
            final val = sizeChecked(value, height: widget.height);
            if (val == widget.height) {
              setState(() {});
            }
            widget.onSizeChanged(height: val);
          },
        ),
        const SizedBox(height: 12),
        SelectContainTitleWidget(
          leftTitle: NiimbotIntl.getIntlMessage('pc0046', '标签纸材质'),
          title: widget.consumableList
                  .firstWhereOrNull(
                      (final element) => element.value == widget.consumableType)
                  ?.label ??
              '',
          dataList: widget.consumableList,
          value: widget.consumableType,
          isDropDown: !widget.readonly,
          readonly: widget.readonly,
          onTapSelect: (final e) {
            if (e != null) {
              widget.onCustomizeChanged.call(e.value);
            }
          },
        ),
        const SizedBox(height: 12),
        SelectContainTitleWidget(
          leftTitle: NiimbotIntl.getIntlMessage('app01213', '走纸类型'),
          title: widget.paperTypeList
                  .firstWhereOrNull(
                      (final element) => element.value == widget.paperType)
                  ?.label ??
              '',
          dataList: widget.paperTypeList,
          value: widget.paperType,
          isDropDown: !widget.readonly,
          readonly: widget.readonly,
          onTapSelect: (final e) {
            if (e != null) {
              widget.onPaperTypeChanged.call(e.value);
            }
          },
        ),
        if (widget.showRotate) ...[
          const SizedBox(height: 12),
          SelectContainTitleWidget(
            leftTitle: NiimbotIntl.getIntlMessage('app01137', '出纸方向'),
            title: rotateList
                .firstWhereOrNull((final e) => e.value == widget.rotate)!
                .label,
            dataList: rotateList,
            value: widget.rotate,
            isDropDown: !widget.readonly,
            readonly: widget.readonly,
            onTapSelect: (final e) {
              if (e != null) {
                widget.onRotateChanged?.call(e.value);
              }
            },
          ),
        ],
        if (widget.showCable) ...[
          const SizedBox(height: 12),
          CheckBoxWidget(
            title: NiimbotIntl.getIntlMessage('app00286', '线缆标签'),
            value: widget.isCable,
            enable: !widget.readonly,
            valueChanged: widget.onIsCableChanged,
          )
        ],
        const SizedBox(height: 12),
        if (widget.isCable && widget.showCable) ...[
          SelectContainTitleWidget(
            leftTitle: NiimbotIntl.getIntlMessage('app00231', '尾巴方向'),
            title: TemplateUtils.getCableDirectionName(widget.cableDirection!),
            dataList: cableDirectionList,
            value: widget.cableDirection,
            readonly: widget.readonly,
            isDropDown: !widget.readonly,
            onTapSelect: (final e) {
              if (e != null) {
                widget.onCableDirectionChanged.call(e.value);
              }
            },
          ),
          const SizedBox(height: 12),
          NumberInputWidget(
            leftTitle: NiimbotIntl.getIntlMessage('app00232', '尾巴长度'),
            value: widget.cableLength,
            digit: 2,
            min: 0,
            max: 100,
            readonly: widget.readonly,
            // onChange: widget.onCableLengthChanged,
            onBlur: (num value) {
              // if (value < 0 || value > 100) {
              //   value = widget.cableLength;
              // }
              // if (value == widget.cableLength) {
              //   setState(() {});
              // }
              widget.onCableLengthChanged(value);
            },
          )
        ],
      ],
    );
  }
}
