import 'package:flutter/material.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:niimbot_ui/widgets/niimbot_popover/niimbot_popover.dart';
import 'package:niimbot_ui/widgets/niimbot_tooltip/niimbot_tooltip.dart';

class CanvasDesktopInkIconWidget extends StatefulWidget {
  final void Function()? onTap;
  final Widget child;
  final String text;
  final bool disabled;
  final EdgeInsetsGeometry? padding;

  const CanvasDesktopInkIconWidget({
    super.key,
    this.onTap,
    required this.child,
    required this.text,
    final bool? disabled,
    this.padding,
  }) : disabled = disabled ?? false;

  @override
  State<CanvasDesktopInkIconWidget> createState() =>
      _CanvasDesktopInkIconWidgetState();
}

class _CanvasDesktopInkIconWidgetState
    extends State<CanvasDesktopInkIconWidget> {
  bool isHover = false;

  @override
  Widget build(final BuildContext context) {
    final niimbotColors = NiimbotTheme.of(context).colors;
    return InkWell(
      onTap: () => {if (!widget.disabled) widget.onTap?.call()},
      mouseCursor: !widget.disabled
          ? SystemMouseCursors.click
          : SystemMouseCursors.noDrop,
      child: MouseRegion(
        onHover: (final event) {
          if (!widget.disabled) {
            setState(() {
              isHover = true;
            });
          }
        },
        onExit: (final event) {
          setState(() {
            isHover = false;
          });
        },
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 4.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                  padding: widget.padding ??
                      const EdgeInsets.symmetric(
                          vertical: 2.0, horizontal: 7.0),
                  decoration: BoxDecoration(
                      color: isHover
                          ? niimbotColors.solidBackgroundFillColorTertiary
                          : null,
                      borderRadius: BorderRadius.circular(5.0)),
                  child: widget.child),
              Container(
                constraints: const BoxConstraints(maxWidth: 80),
                child: NiimbotToolTip(
                  text: widget.text,
                  textStyle: NiimbotTheme.of(context)
                      .typography
                      .smallBody
                      ?.copyWith(
                          color: !widget.disabled
                              ? niimbotColors.textFillColorSecondary
                              : niimbotColors.textFillColorTertiary),
                  position: NiimbotPopoverPositionEnum.bottom,
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
