import 'dart:io';

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_flutter_canvas/utils/compute_semaphore.dart';
import 'package:niimbot_flutter_canvas/widgets/canvas/widgets/preview_template_widget.dart';
import 'package:niimbot_template/models/template_data.dart';
import 'package:niimbot_ui/styles/border_radius.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:provider/provider.dart';
import 'package:should_rebuild/should_rebuild.dart' as sr;

class CanvasDataSourceSingleWidget extends StatelessWidget {
  final ConcurrentCompute<(int?, num?, String?), TemplateData>?
      concurrentCompute;
  final bool scrolling;
  final int index;
  final int page;
  final TemplateData template;
  final Function(int index)? onClickItem;
  final String? backgroundImage;

  const CanvasDataSourceSingleWidget({
    this.concurrentCompute,
    this.scrolling = false,
    required this.index,
    required this.page,
    required this.template,
    this.onClickItem,
    this.backgroundImage,
    super.key,
  });

  @override
  Widget build(final BuildContext context) {
    final niimbotTheme = NiimbotTheme.of(context);

    return InkWell(
        onTap: () {
          onClickItem?.call(index);
        },
        child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            child: Column(children: [
              Container(
                width: 166,
                height: 124,
                padding:
                    const EdgeInsets.symmetric(horizontal: 9, vertical: 13),
                alignment: Alignment.center,
                decoration: BoxDecoration(
                    color: niimbotTheme.colors.translucentFillColorQuarternary,
                    border: index == page
                        ? Border.all(
                            width: 2.0, color: niimbotTheme.colors.brandColor)
                        : null,
                    borderRadius: NiimbotRadius.large),
                child: !scrolling
                    ? _PreviewContainer(
                        concurrentCompute: concurrentCompute,
                        template: template,
                        index: index,
                        backgroundImage: backgroundImage)
                    : _EmptyPreview(backgroundImage: backgroundImage),
              ),
              const SizedBox(height: 5),
              Text(
                '$index',
                style: index == page //dataSourceBindInfoPage
                    ? niimbotTheme.typography.hint
                        ?.copyWith(color: niimbotTheme.colors.brandColor)
                    : niimbotTheme.typography.hint,
              )
            ])));
  }
}

class _EmptyPreview extends StatelessWidget {
  final String? backgroundImage;

  const _EmptyPreview({this.backgroundImage});

  String get _path => backgroundImage ?? '';

  @override
  Widget build(final BuildContext context) {
    final empty = Container(color: Colors.white);
    if (_path.isEmpty) return empty;
    return Image.file(
      File(_path),
      errorBuilder: (final _, final __, final ___) => empty,
    );
  }
}

class _PreviewContainer extends StatelessWidget {
  final ConcurrentCompute<(int?, num?, String?), TemplateData>?
      concurrentCompute;
  final TemplateData template;
  final int index;

  final String? backgroundImage;

  const _PreviewContainer({
    this.concurrentCompute,
    required this.template,
    this.backgroundImage,
    required this.index,
  });

  @override
  Widget build(final BuildContext context) {
    final printColor =
        context.select<CanvasStore, Color?>((final v) => v.printColor);
    return sr.ShouldRebuild(
        shouldRebuild: (final o, final n) {
          const de = DeepCollectionEquality();
          if (!de.equals(o.page, n.page) ||
              !de.equals(
                  o.localBackgroundImageUrl, n.localBackgroundImageUrl) ||
              !de.equals(o.template, n.template) ||
              !de.equals(o.color, n.color)) {
            return true;
          }
          return false;
        },
        child: PreviewTemplateWidget(
          concurrentCompute: concurrentCompute,
          template: template,
          page: index,
          color: printColor,
          localBackgroundImageUrl: backgroundImage,
        ));
  }
}
