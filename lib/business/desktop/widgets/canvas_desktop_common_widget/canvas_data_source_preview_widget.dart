import 'package:async/src/restartable_timer.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:niimbot_flutter_canvas/business/desktop/utils/canvas_widget_manager_utils.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/canvas_desktop_common_widget/canvas_data_source_single_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/excel/canvas_mouse_hover_widget.dart';
import 'package:niimbot_flutter_canvas/extensions/template_data.dart';
import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_flutter_canvas/utils/compute_semaphore.dart';
import 'package:niimbot_template/models/template_data.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:provider/provider.dart';
import 'package:should_rebuild/should_rebuild.dart' as sr;

Logger _logger = Logger("CanvasDataSourcePreviewWidget", on: kDebugMode);

///数据源底部滑动视图
class CanvasDataSourcePreviewWidget extends StatefulWidget {
  final BoxConstraints constraints;
  final int? total;
  final int? page;

  const CanvasDataSourcePreviewWidget({
    required this.constraints,
    this.total,
    this.page,
    super.key,
  });

  @override
  State<CanvasDataSourcePreviewWidget> createState() =>
      _CanvasDataSourcePreviewWidgetState();
}

class _CanvasDataSourcePreviewWidgetState
    extends State<CanvasDataSourcePreviewWidget> {
  int currentPage = 1;
  int pageSize = 10;
  bool isShowAll = false;

  int get totalPage =>
      ((widget.total ?? 1) / (pageSize - (isShowAll ? 0 : 1))).ceil();

  int get startPage => (currentPage - 1) * (pageSize - (isShowAll ? 0 : 1)) + 1;

  final PageController _pageController = PageController(keepPage: false);

  RestartableTimer? uploadRestartableTimer;
  bool scrolling = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((final _) {
      _loadData();
    });
  }

  @override
  didUpdateWidget(final CanvasDataSourcePreviewWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.constraints != widget.constraints ||
        oldWidget.page != widget.page ||
        oldWidget.total != widget.total) {
      _initPage(animate: false);
    }
  }

  _initPage({final bool animate = true}) {
    double pageSizeDouble = (widget.constraints.maxWidth - 120) / 186;
    pageSize = pageSizeDouble.ceil();
    double pageSizeDoubleDiff = pageSize - pageSizeDouble;
    if (pageSizeDoubleDiff >= 0.9) {
      pageSize = pageSizeDouble.floor();
      pageSizeDoubleDiff = pageSizeDouble - pageSize;
    }
    isShowAll = pageSizeDoubleDiff < 0.1;
    currentPage =
        ((widget.page ?? 1) / (pageSize - (isShowAll ? 0 : 1))).ceil();
    if (animate) {
      _animateToPage(currentPage);
    } else {
      setState(() {});
    }
  }

  _animateToPage(final int current) {
    const duration = Duration(milliseconds: 600);
    _pageController.animateToPage(current - 1,
        duration: duration, curve: Curves.easeInOut);
    if (uploadRestartableTimer == null) {
      scrolling = true;
      uploadRestartableTimer = RestartableTimer(duration, () {
        scrolling = false;
        setState(() {});
      });
    } else {
      scrolling = true;
      uploadRestartableTimer?.reset();
    }
  }

  _loadData() async {
    _initPage();
    if (CanvasWidgetManager().firstLoadDataBindAction()) {}
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  _currentPageChange() {
    if (currentPage < 1) {
      currentPage = 1;
    } else if (currentPage > totalPage) {
      currentPage = totalPage;
    }
    _animateToPage(currentPage);
  }

  @override
  Widget build(final BuildContext context) {
    _logger.log(pageSize, '数据源单页展示条数');
    final template = context
        .select<CanvasStore, TemplateData>((final v) => v.originCanvasData);

    final themeColors = NiimbotTheme.of(context).colors;

    /// 上页下页是否可用
    bool prevPageEnable = currentPage > 1;
    bool nextPageEnable = currentPage < totalPage;

    return Row(
      children: [
        Container(
          width: 60,
          height: 150,
          alignment: Alignment.topCenter,
          padding: const EdgeInsetsDirectional.only(top: 35, end: 20),
          child: InkWell(
            onTap: () {
              if (prevPageEnable) {
                currentPage -= 1;
                _currentPageChange();
              }
            },
            mouseCursor: prevPageEnable
                ? SystemMouseCursors.click
                : SystemMouseCursors.noDrop,
            child: CanvasMouseHoverWidget(
              type: CanvasMouseHoverIconType.prevPage,
              disabled: !prevPageEnable,
            ),
          ),
        ),
        Expanded(
          child: Container(
            color: themeColors.solidBackgroundFillColorBase,
            height: 170,
            child: PageView.builder(
              allowImplicitScrolling: false,
              itemCount: totalPage,
              controller: _pageController,
              itemBuilder: (final context, final index) {
                // return SizedBox.shrink();
                return ScrollPreviewSingleWidget(
                  scrolling: scrolling,
                  template: template,
                  startPage: startPage,
                  pageSize: pageSize,
                  page: widget.page ?? 1,
                  total: widget.total ?? 1,
                );
              },
            ),
          ),
        ),
        Container(
          width: 60,
          height: 150,
          alignment: Alignment.topCenter,
          padding: const EdgeInsetsDirectional.only(top: 35, start: 20),
          child: InkWell(
            onTap: () {
              if (nextPageEnable) {
                currentPage += 1;
                _currentPageChange();
              }
            },
            mouseCursor: nextPageEnable
                ? SystemMouseCursors.click
                : SystemMouseCursors.noDrop,
            child: CanvasMouseHoverWidget(
              type: CanvasMouseHoverIconType.nextPage,
              disabled: !nextPageEnable,
            ),
          ),
        ),
      ],
    );
  }
}

class ScrollPreviewSingleWidget extends HookWidget {
  // 滚动进行中
  final bool scrolling;
  final TemplateData template;
  final int startPage;
  final int pageSize;
  final int page;
  final int total;

  const ScrollPreviewSingleWidget({
    this.scrolling = false,
    required this.template,
    required this.startPage,
    required this.pageSize,
    required this.page,
    required this.total,
    super.key,
  });

  @override
  Widget build(final BuildContext context) {
    final concurrentCompute = useMemoized(
        () => ConcurrentCompute<(int?, num?, String?), TemplateData>());
    final isDragging = context.select<CanvasStore, bool>((final v) =>
        (v.clippingElementId == null
            ? (v.isMoving || v.isResizing || v.isImageSliderIng)
            : true));
    final localBG = context.select<CanvasStore, Future<String?>>(
        (final v) => v.getLoadBackgroundImage(template: template));

    useEffect(() {
      concurrentCompute.queueClear();
      return null;
    }, [isDragging]);

    void onClickItem(final int index, final int currentIndex) {
      final canvasData = context.read<CanvasStore>().canvasData;
      if (!canvasData.isHavePdf && (canvasData.fileDataSources ?? []).isEmpty) {
        final dataSource = template.dataSources?.firstOrNull;
        context
            .read<CanvasStore>()
            .dataSourceController
            .updateTemplateDataSource(dataSource, page: index, refresh: false);
        return;
      } else {
        context.read<CanvasStore>().updatePdfInfo(index);
      }
    }

    return FutureBuilder<String?>(
      future: localBG,
      builder: (final _, final snapshot) {
        return sr.ShouldRebuild(
          shouldRebuild: (final _, final __) {
            return !isDragging;
          },
          child: SizedBox(
            height: 170,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: pageSize,
              itemBuilder: (final context, final index) {
                final currentIndex = startPage + index;
                return currentIndex <= total
                    ? CanvasDataSourceSingleWidget(
                        concurrentCompute: concurrentCompute,
                        scrolling: scrolling,
                        backgroundImage: snapshot.data,
                        index: currentIndex,
                        page: page,
                        template: template,
                        onClickItem: (final int pageIndex) {
                          onClickItem(pageIndex, index);
                        },
                      )
                    : const SizedBox.shrink();
              },
            ),
          ),
        );
      },
    );
  }
}
