import 'package:flutter/material.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_ui/icons/icons.dart';
import 'package:niimbot_ui/model/niimbot_dropdown_model.dart';
import 'package:niimbot_ui/styles/border_radius.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:niimbot_ui/widgets/niimbot_direction.dart';
import 'package:niimbot_ui/widgets/niimbot_popover/niimbot_popover.dart';
import 'package:niimbot_ui/widgets/niimbot_tooltip/niimbot_tooltip.dart';
import 'package:provider/provider.dart';

import 'package:niimbot_flutter_canvas/business/desktop/utils/canvas_widget_manager_utils.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/canvas_desktop_common_widget/canvas_global_setting.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/check_box_widget.dart';
import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_flutter_canvas/utils/buried_utils.dart';

class CanvasGlobalSettingWidget extends StatelessWidget {
  const CanvasGlobalSettingWidget({
    super.key,
  });

  @override
  Widget build(final BuildContext context) {
    final themeColors = NiimbotTheme.of(context).colors;

    final width =
        context.select<CanvasStore, num>((final v) => v.canvasData.width);
    final height =
        context.select<CanvasStore, num>((final v) => v.canvasData.height);
    final consumableType = context
        .select<CanvasStore, int>((final v) => v.canvasData.consumableType);
    final paperType =
        context.select<CanvasStore, int>((final v) => v.canvasData.paperType);
    final rotate =
        context.select<CanvasStore, num>((final v) => v.canvasData.rotate);
    final isCable =
        context.select<CanvasStore, bool>((final v) => v.canvasData.isCable);
    final cableDirection = context.select<CanvasStore, NetalCableDirection?>(
        (final v) => v.canvasData.cableDirection);
    final cableLength =
        context.select<CanvasStore, num>((final v) => v.canvasData.cableLength);

    final includedLabel =
        context.select<CanvasStore, bool>((final v) => v.includedLabel);
    final showCable =
        context.select<CanvasStore, bool>((final v) => v.showCable);

    final simpleInfo =
        context.select<CanvasStore, String>((final v) => v.simpleInfo);

    final consumableList =
        context.select<CanvasStore, List<NiimbotDropDownModel<int?>>>(
            (final v) => v.consumableList);
    final paperTypeList =
        context.select<CanvasStore, List<NiimbotDropDownModel<int>>>(
            (final v) => v.paperTypeList);

    final isShowPrintArea =
        context.select<CanvasStore, bool>((final v) => v.showPrintArea);

    onLabelSizeChange({final num? width, final num? height}) {
      BuriedUtils()
          .track('click', width != null ? '007_037_061' : '007_037_062');
      context
          .read<CanvasStore>()
          .updateTemplateSize(width: width, height: height);
    }

    onLabelPaperTypeChange(final int? val) {
      if (val != null) {
        context.read<CanvasStore>().updateTemplatePaperType(val);
      }
    }

    onLabelIsCableChange(final bool val) {
      context.read<CanvasStore>().updateTemplateIsCable(val);
    }

    onLabelCableDirectionChange(final NetalCableDirection? val) {
      if (val != null) {
        context.read<CanvasStore>().updateTemplateCableDirection(val);
      }
    }

    onLabelCableLengthChange(final num val) {
      context.read<CanvasStore>().updateTemplateCableLength(val);
    }

    onShowPrintAreaChange(final bool val) {
      context.read<CanvasStore>().updateShowTemplatePrintArea(val);
    }

    return SingleChildScrollView(
      child: Container(
        color: themeColors.systemFillColorWhite,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(
                  top: 10, right: 20, bottom: 12, left: 20),
              child: CanvasGlobalSetting(
                consumableList: consumableList,
                paperTypeList: paperTypeList,
                width: width,
                height: height,
                consumableType: consumableType,
                paperType: paperType,
                rotate: rotate,
                isCable: isCable,
                cableDirection: cableDirection,
                cableLength: cableLength,
                readonly: includedLabel,
                showCable: showCable,
                // showRotate: true,
                onSizeChanged: onLabelSizeChange,
                onCustomizeChanged: (final int? value) {
                  // if (value != null) {
                  //   context.read<CanvasStore>().updateConsumablesType(value);
                  // }
                  CanvasWidgetManager.sharedInstance()
                      .updateConsumablesType
                      ?.call(value);
                },
                onPaperTypeChanged: onLabelPaperTypeChange,
                onIsCableChanged: onLabelIsCableChange,
                onCableDirectionChanged: onLabelCableDirectionChange,
                onCableLengthChanged: onLabelCableLengthChange,
                children: [
                  Text(NiimbotIntl.getIntlMessage('pc0045', '当前标签纸'),
                      style: NiimbotTheme.maybeOf(context)
                          ?.typography
                          .titleStrong),
                  const SizedBox(height: 12),
                  InkWell(
                    onTap:
                        CanvasWidgetManager.sharedInstance().onLabelSelectTap,
                    child: Container(
                      height: 32,
                      padding:
                          const EdgeInsetsDirectional.only(start: 8.0, end: 0),
                      decoration: BoxDecoration(
                        border: Border.all(
                            color: themeColors.borderColorNormal, width: 1.0),
                        borderRadius: NiimbotRadius.small,
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: NiimbotToolTip(
                              text: simpleInfo,
                              textStyle:
                                  NiimbotTheme.of(context).typography.body,
                              position: NiimbotPopoverPositionEnum.top,
                            ),
                          ),
                          Container(
                            margin: const EdgeInsetsDirectional.only(end: 6),
                            child: NiimbotDirection(
                              child: RotatedBox(
                                  quarterTurns: 2,
                                  child: NiimbotIcons.chevron(
                                    size: 12.0,
                                    color: NiimbotTheme.of(context)
                                        .colors
                                        .textFillColorSecondary,
                                  )),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 12),
                ],
              ),
            ),
            Divider(
              height: 1,
              color: themeColors.borderColorNormal,
            ),
            Padding(
              padding: const EdgeInsets.only(
                  top: 12, right: 20, bottom: 12, left: 20),
              child: CheckBoxWidget(
                  title: NiimbotIntl.getIntlMessage('app01421', '显示打印范围'),
                  value: isShowPrintArea,
                  valueChanged: onShowPrintAreaChange),
            )
          ],
        ),
      ),
    );
  }
}
