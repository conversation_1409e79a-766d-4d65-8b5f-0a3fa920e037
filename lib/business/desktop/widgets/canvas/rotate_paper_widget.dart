import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/canvas_clipping_marker.dart';
import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_flutter_canvas/utils/buried_utils.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_ui/icons/icons.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:niimbot_ui/widgets/niimbot_checkbox/niimbot_checkbox.dart';
import 'package:niimbot_ui/widgets/niimbot_dialog/niimbot_dialog_controller.dart';
import 'package:provider/provider.dart';

class RotatePaperWidget extends StatelessWidget {
  const RotatePaperWidget({super.key});

  static bool? _IsRotateByView;

  @override
  Widget build(final BuildContext context) {
    final themeColors = NiimbotTheme.of(context).colors;
    onRotate() {
      final hasElements = context.read<CanvasStore>().elements.isNotEmpty;
      BuriedUtils()
          .track('click', '007_038_030', ext: {'type': hasElements ? 2 : 1});
      if (hasElements) {
        if (_IsRotateByView == null) {
          /* 是否视图旋转 */
          var isRemember = false;
          onRemember(final bool val) {
            isRemember = val;
          }

          BuriedUtils().track('show', '007_016');
          NiimbotDialogController().show(
            width: 520,
            messageDialog: true,
            messageIcon: NiimbotIcons.warning(
              color: NiimbotTheme.of(context).colors.warningColorNormal,
              size: 20,
            ),
            title: NiimbotIntl.getIntlMessage("pc0387", "是否将内容一起旋转"),
            cancelText: NiimbotIntl.getIntlMessage("login0052", "否"),
            confirmText: NiimbotIntl.getIntlMessage("login0051", "是"),
            child: _RotatePaperTips(onChanged: onRemember),
            onClose: (final done) {
              context.read<CanvasStore>().rotateTemplate(isView: false);
              done();
            },
            onCancel: (final done) {
              BuriedUtils().track('click', '007_016_034');
              context.read<CanvasStore>().rotateTemplate(isView: false);
              done();
            },
            onConfirm: (final done) {
              BuriedUtils().track('click', '007_016_032',
                  ext: {'type': isRemember ? 1 : 2});
              context.read<CanvasStore>().rotateTemplate(isView: true);
              if (isRemember) {
                _IsRotateByView = true;
              }
              done();
            },
          );
        } else {
          context.read<CanvasStore>().rotateTemplate(isView: _IsRotateByView!);
        }
      } else {
        context.read<CanvasStore>().rotateTemplate(isView: false);
      }
    }

    return Positioned.directional(
      start: 170,
      bottom: 24,
      textDirection: Directionality.of(context),
      child: CanvasClippingMarker(
        child: Container(
          height: 32,
          padding: const EdgeInsets.symmetric(
            vertical: 4,
            horizontal: 12,
          ),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(64),
            color: themeColors.systemFillColorWhite,
          ),
          child: InkWell(
              onTap: onRotate,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  NiimbotIcons.labelRotate(
                    color: themeColors.systemFillColorBlack,
                    size: 20,
                  ),
                  const SizedBox(width: 6),
                  Container(
                      padding: const EdgeInsets.only(top: 2.0),
                      child: Text(NiimbotIntl.getIntlMessage("pc0009", "旋转标签纸"),
                          style: NiimbotTheme.of(context).typography.body)),
                ],
              )),
        ),
      ),
    );
  }
}

class _RotatePaperTips extends HookWidget {
  const _RotatePaperTips({super.key, this.onChanged});

  final ValueChanged<bool>? onChanged;

  @override
  Widget build(final BuildContext context) {
    final themeColors = NiimbotTheme.of(context).colors;
    final isRotateByView = useState<bool>(false);
    return Padding(
      padding: EdgeInsets.only(top: 10),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(width: 28),
          Padding(
            padding: EdgeInsets.only(top: 4),
            child: NiimbotCheckbox(
              value: isRotateByView.value,
              onChanged: (final bool? val) {
                if (val != null) {
                  isRotateByView.value = val;
                  onChanged?.call(val);
                }
              },
            ),
          ),
          SizedBox(width: 5),
          Expanded(
              child: Text(
            NiimbotIntl.getIntlMessage('pc0388', '以后默认一起旋转'),
            style: NiimbotTheme.of(context)
                .typography
                .body
                ?.copyWith(color: themeColors.textFillColorSecondary),
            maxLines: 2,
          )),
          SizedBox(width: 20),
        ],
      ),
    );
  }
}
