import 'package:flutter/material.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_ui/icons/icons.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:provider/provider.dart';

import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';

class OutputDirectionWidget extends StatelessWidget {
  const OutputDirectionWidget({super.key});

  @override
  Widget build(final BuildContext context) {
    final themeColors = NiimbotTheme.of(context).colors;
    var rotate = context.select<CanvasStore, num>((final v) {
      return v.canvasData.rotate + v.rotate;
    });

    return Positioned(
      top: 44,
      left: 0,
      right: 0,
      child: Padding(
        padding: const EdgeInsets.only(top: 12.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              NiimbotIntl.getIntlMessage('app01137', '出纸方向'),
              style: NiimbotTheme.of(context)
                  .typography
                  .hint
                  ?.copyWith(color: themeColors.textFillColorSecondary),
            ),
            const SizedBox(
              width: 2.0,
            ),
            RotatedBox(
                quarterTurns: ((360 - rotate) / 90).toInt() + 2,
                child: NiimbotIcons.arrow(
                  size: 12,
                  color: themeColors.textFillColorSecondary,
                ))
          ],
        ),
      ),
    );
  }
}
