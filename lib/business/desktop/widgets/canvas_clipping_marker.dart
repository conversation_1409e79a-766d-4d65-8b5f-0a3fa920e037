import 'package:flutter/material.dart';
import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:provider/provider.dart';

class CanvasClippingMarker extends StatelessWidget {
  final Widget child;

  const CanvasClippingMarker({
    super.key,
    required this.child,
  });

  @override
  Widget build(final BuildContext context) {
    final isClipping = context
        .select<CanvasStore, bool>((final v) => v.clippingElementId != null);
    return Stack(
      clipBehavior: Clip.none,
      children: [
        child,
        Positioned.fill(
          child: isClipping
              ? GestureDetector(
                  onTapDown: (final details) {
                    context.read<CanvasStore>().clippedElement();
                  },
                  child: Container(
                    color: Colors.transparent,
                  ),
                )
              : const SizedBox.shrink(),
        )
      ],
    );
  }
}
