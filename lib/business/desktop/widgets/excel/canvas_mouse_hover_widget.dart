import 'package:flutter/material.dart';
import 'package:niimbot_ui/icons/icons.dart';
import 'package:niimbot_ui/styles/theme.dart';

import 'package:niimbot_flutter_canvas/widgets/chevron_icon.dart';

enum CanvasMouseHoverIconType {
  prevPage,
  nextPage,
}

class CanvasMouseHoverWidget extends StatefulWidget {
  final CanvasMouseHoverIconType type;
  final bool disabled;

  const CanvasMouseHoverWidget({
    required this.type,
    this.disabled = false,
    super.key,
  });

  @override
  State<CanvasMouseHoverWidget> createState() => _CanvasMouseHoverWidgetState();
}

class _CanvasMouseHoverWidgetState extends State<CanvasMouseHoverWidget> {
  bool isPrePageHover = false;

  @override
  void didUpdateWidget(covariant CanvasMouseHoverWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.disabled != widget.disabled) {
      isPrePageHover = false;
    }
  }

  @override
  Widget build(final BuildContext context) {
    return MouseRegion(
      onHover: (final e) {
        if (widget.disabled) return;
        setState(() {
          isPrePageHover = true;
        });
      },
      onExit: (final e) {
        if (widget.disabled) return;
        setState(() {
          isPrePageHover = false;
        });
      },
      child: _backIconWidget(),
    );
  }

  Widget _backIconWidget() {
    final themeColors = NiimbotTheme.of(context).colors;

    final currentColor = widget.disabled
        ? themeColors.translucentFillColorQuarternary.withAlpha(10)
        : (!isPrePageHover
            ? Colors.transparent
            : themeColors.translucentFillColorQuarternary);

    switch (widget.type) {
      case CanvasMouseHoverIconType.prevPage:
        return ChevronIcon(
            icon: NiimbotIcons.prevPage(
          color: currentColor,
          size: 40.00,
        ));
      case CanvasMouseHoverIconType.nextPage:
        return ChevronIcon(
          icon: NiimbotIcons.nextPage(
            color: currentColor,
            size: 40.00,
          ),
        );
      default:
        return Container();
    }
  }
}
