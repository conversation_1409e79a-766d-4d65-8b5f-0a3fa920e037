import 'package:collection/collection.dart';
import 'package:excel/excel.dart' as excel;
import 'package:flutter/material.dart';
import 'package:niimbot_excel/niimbot_excel_utils.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_template/models/elements/bind_element.dart';
import 'package:niimbot_template/models/template/template_data_source.dart';
import 'package:niimbot_ui/icons/icons.dart';
import 'package:niimbot_ui/styles/border_radius.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:niimbot_ui/styles/typography_style.dart';
import 'package:niimbot_ui/widgets/niimbot_direction.dart';
import 'package:niimbot_ui/widgets/niimbot_popover/niimbot_popover.dart';
import 'package:provider/provider.dart';

import 'package:niimbot_flutter_canvas/business/desktop/models/canvas_drag_model.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/excel/canvas_excel_widget/canvas_excel_list_widget.dart';

import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';

class CanvasExcelDataWidget extends StatelessWidget {
  final void Function({
    required TemplateDataSource dataSource,
    int totalPage,
    List<int> selectRow,
  })? showChooseExcelDialog;
  final ValueChanged<TemplateDataSource>? onConfirm;
  final VoidCallback? onOpenFilePicker;

  const CanvasExcelDataWidget({
    this.showChooseExcelDialog,
    this.onConfirm,
    this.onOpenFilePicker,
    super.key,
  });

  @override
  Widget build(final BuildContext context) {
    final themeColors = NiimbotTheme.of(context).colors;
    final dataSource = context.select<CanvasStore, TemplateDataSource?>(
        (final v) => v.canvasData.dataSources?.firstOrNull);

    final totalPage = context.select<CanvasStore, int?>(
        (final v) => v.canvasData.dataSourceBindInfo?.total);
    onCanvasExcelDataTap() {
      if (dataSource == null) return;
      List<int> columnList = [];
      for (var element in dataSource.range!) {
        if (element.e > element.s) {
          List<int> numbers = List.generate(
              (element.e - element.s + 1), (final index) => index + element.s);
          columnList.addAll(numbers);
        } else {
          columnList.add(element.s);
        }
      }
      showChooseExcelDialog?.call(
        totalPage: totalPage ?? 1,
        dataSource: dataSource,
        selectRow: columnList,
      );
    }

    ///选的excel行数
    int _chooseExcelRow() {
      if (dataSource == null) return 0;
      final List<List<String>> dataList = dataSource.rowData;
      if ((dataSource.range ?? []).isEmpty) {
        return dataList.length - 1;
      }
      num total = 0;
      for (var element in dataSource.range ?? []) {
        total += element.e - element.s + 1;
      }
      return total.toInt();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        NiimbotPopover(
          position: NiimbotPopoverPositionEnum.end,
          showArrow: true,
          content: CanvasExcelListWidget(
            onTap: onOpenFilePicker,
            propsCtx: context,
            onConfirm: onConfirm,
            dataSource: dataSource,
          ),
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 8),
            decoration: BoxDecoration(
              borderRadius: NiimbotRadius.small,
              border: Border.all(color: themeColors.dividerColor),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  flex: 1,
                  child: Text(
                    dataSource?.name ?? '',
                    style: NiimbotTheme.of(context)
                        .typography
                        .body
                        ?.copyWith(color: themeColors.textFillColorPrimary),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                NiimbotDirection(
                  child: NiimbotIcons.swap(
                    size: 16,
                    color: themeColors.textFillColorSecondary,
                  ),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(
          height: 8,
        ),
        Container(
          padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 8),
          decoration: BoxDecoration(
            borderRadius: NiimbotRadius.small,
            border: Border.all(color: themeColors.dividerColor),
          ),
          child: InkWell(
            onTap: onCanvasExcelDataTap,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  flex: 1,
                  child: Text(
                    NiimbotIntl.getIntlMessage(
                        'app100001110', '已选${_chooseExcelRow()}行',
                        param: [_chooseExcelRow().toString()]),
                    style: NiimbotTheme.of(context)
                        .typography
                        .body
                        ?.copyWith(color: themeColors.textFillColorPrimary),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                NiimbotDirection(
                  child: RotatedBox(
                      quarterTurns: 2,
                      child: NiimbotIcons.chevron(
                        size: 16,
                        color: themeColors.textFillColorSecondary,
                      )),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(
          height: 12,
        ),
        Text(
          NiimbotIntl.getIntlMessage('pc0197', '拖拽字段到模版中'),
          style: NiimbotTheme.maybeOf(context)?.typography.smallBody?.copyWith(
              fontSize: 12, color: themeColors.textFillColorSecondary),
        ),
        const SizedBox(
          height: 8,
        ),
        if (dataSource != null) _DragListWidget(dataSource: dataSource),
        const SizedBox(height: 5),
      ],
    );
  }
}

class _DragListWidget extends StatelessWidget {
  final TemplateDataSource dataSource;

  const _DragListWidget({
    required this.dataSource,
  });

  @override
  Widget build(final BuildContext context) {
    final selectedList = context.select<CanvasStore, List<String>>(
      (final v) {
        final List<String> res = [];
        for (var element in v.canvasData.elements) {
          if (element is BindElement) {
            if (element.value != null && element.dataBind?.isNotEmpty == true) {
              res.add(element.value!);
              continue;
            }
            final elementVal = v.canvasData.values
                ?.firstWhereOrNull((final e) => e.elementId == e.id);
            if (elementVal != null) {
              res.add(elementVal.value);
              continue;
            }
          }
        }
        return res;
      },
    );
    final List<List<String>> dataList = dataSource.rowData;

    ///表头数据
    final sheetName = dataSource.collections.firstOrNull ?? "Sheet1";
    final headers = dataSource.headers?[sheetName];
    final headerInfoInt = dataList.first;
    final headerInfoList = headers is List
        ? headers.map((final e) => e.toString()).toList()
        : null;
    final header = headerInfoList ?? headerInfoInt;

    return Column(
      children: header.mapIndexed((final index, final e) {
        final indexByColumnRow =
            excel.CellIndex.indexByColumnRow(columnIndex: index, rowIndex: 0)
                .cellId;
        final RegExp regex = RegExp(r'\d+$');
        final selected = selectedList.any((final e) =>
            e.replaceAll(regex, '') == indexByColumnRow.replaceAll(regex, ''));
        String text = e;
        if (e.trim().isEmpty) {
          text =
              '${NiimbotIntl.getIntlMessage('app100001121', '列')}${NiimbotExcelUtils.indexToLetters(index + 1)}';
        }

        CanvasExcelDragSendDataModel sendDataModel =
            CanvasExcelDragSendDataModel(
          sheetName: sheetName,
          hash: dataSource.hash,
          column: index,
        );
        CanvasDragModel canvasDragModel = CanvasDragModel(
            canvasDragModelType: CanvasDragModelType.canvasBaseDataSourceType,
            data: sendDataModel);
        return Draggable(
            rootOverlay: true,
            feedback: _DragItemWidget(selected: selected, text: text),
            ignoringFeedbackSemantics: false,
            data: canvasDragModel,
            onDragStarted: () {},
            onDragEnd: (final DraggableDetails details) {},
            onDragCompleted: () {},
            onDraggableCanceled:
                (final Velocity velocity, final Offset offset) {},
            child: _DragItemWidget(selected: selected, text: text));
      }).toList(),
    );
  }
}

class _DragItemWidget extends StatelessWidget {
  final bool selected;
  final String text;

  const _DragItemWidget({required this.selected, required this.text});

  @override
  Widget build(final BuildContext context) {
    final themeColors = NiimbotTheme.of(context).colors;
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      onEnter: (final event) {},
      onExit: (final event) {},
      child: Container(
        width: 200,
        padding: const EdgeInsets.only(top: 8, bottom: 8, left: 10, right: 6),
        margin: const EdgeInsets.only(bottom: 8),
        decoration: BoxDecoration(
          borderRadius: NiimbotRadius.small,
          color: !selected
              ? themeColors.solidBackgroundFillColorSecondary
              : themeColors.systemFillColorSelected.withOpacity(0.06),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            if (selected) ...[
              NiimbotIcons.linked(
                size: 16,
                color: themeColors.systemFillColorSelected,
              ),
              const SizedBox(width: 4)
            ],
            Expanded(
              flex: 1,
              child: Text(
                text,
                style: NiimbotTheme.of(context).typography.body?.copyWith(
                      fontWeight: !selected ? null : NiimbotFontWeight.medium,
                    ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            NiimbotIcons.draggable(
              size: 16,
              color: themeColors.textFillColorTertiary,
            ),
          ],
        ),
      ),
    );
  }
}
