import 'package:flutter/material.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_template/models/template/template_data_source.dart';
import 'package:niimbot_ui/icons/icons.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:niimbot_ui/widgets/niimbot_button.dart';
import 'package:niimbot_ui/widgets/niimbot_popover/niimbot_popover.dart';
import 'package:niimbot_ui/widgets/niimbot_popover/niimbot_popover_controller.dart';
import 'package:niimbot_ui/widgets/niimbot_tooltip/niimbot_tooltip.dart';
import 'package:niimbot_ui/widgets/niimbot_toast/niimbot_toast.dart';
import 'package:niimbot_ui/widgets/niimbot_toast/niimbot_toast_controller.dart';

import 'package:niimbot_flutter_canvas/business/desktop/utils/canvas_widget_manager_utils.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/excel/excel_utils.dart';

class CanvasExcelListWidget extends StatefulWidget {
  const CanvasExcelListWidget({
    super.key,
    this.onTap,
    required this.propsCtx,
    this.onConfirm,
    this.dataSource,
  });

  final TemplateDataSource? dataSource;
  final VoidCallback? onTap;
  final ValueChanged<TemplateDataSource>? onConfirm;
  final BuildContext propsCtx;

  @override
  State<CanvasExcelListWidget> createState() => _CanvasExcelListWidgetState();
}

class _CanvasExcelListWidgetState extends State<CanvasExcelListWidget> {
  List dataSourceList = [];
  int hoverId = -1;
  int hoverDeleteId = -1;

  @override
  void initState() {
    super.initState();
    _getDataSources();
  }

  _getDataSources() async {
    dataSourceList =
        await CanvasWidgetManager.sharedInstance().getdataSources?.call() ?? [];
    setState(() {});
  }

  Future<TemplateDataSource?> _buildDataSourceByItem(final dynamic item) async {
    LocalExcelInfo localExcelInfo = await ExcelUtils.coverOssExcel(
        item['path'], item['id'], item['md5'], item['name']);

    return ExcelUtils.buildDataSourceByPath(localExcelInfo.savePath,
        fileName: item['name'],
        hash: item['md5'],
        uri: localExcelInfo.savePath.isNotEmpty
            ? localExcelInfo.savePath
            : null);
  }

  void _onTapDataSource(final dynamic item) async {
    final templateDataSource = await _buildDataSourceByItem(item);
    if (templateDataSource != null) {
      NiimbotPopoverController.removeAny();
      widget.onConfirm?.call(templateDataSource);
    }
  }

  void _onTapDeleteDataSource(final dynamic item) async {
    final result = await CanvasWidgetManager.sharedInstance()
        .onDeleteDataSource
        ?.call(item['id']);
    if (result == true) {
      hoverId = -1;
      hoverDeleteId = -1;
      _getDataSources();
    }
  }

  @override
  Widget build(final BuildContext ctx) {
    final niimbotTheme = NiimbotTheme.of(context);
    return Material(
      child: SizedBox(
        width: 240,
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
              alignment: AlignmentDirectional.centerStart,
              child: Text(
                NiimbotIntl.getIntlMessage('app100000033', '请选择'),
                style: NiimbotTheme.of(context).typography.bodyStrong,
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Divider(
                height: 1,
                color: niimbotTheme.colors.dividerColor,
              ),
            ),
            SizedBox(
              height: 270,
              // padding: const EdgeInsets.symmetric(vertical: 8),
              child: ListView.builder(
                  itemCount: dataSourceList.length,
                  physics: const ClampingScrollPhysics(),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  itemBuilder: (final context, final index) {
                    bool isHover = index == hoverId;
                    bool isHoverDelete = index == hoverDeleteId;
                    final item = dataSourceList[index];
                    bool isActivity = widget.dataSource != null
                        ? item['md5'] == widget.dataSource?.hash
                        : false;
                    return InkWell(
                      onTap: () {
                        _onTapDataSource(item);
                      },
                      onHover: (final e) {
                        setState(() {
                          hoverId = e ? index : -1;
                        });
                      },
                      hoverColor: Colors.transparent,
                      highlightColor: Colors.transparent,
                      splashColor: Colors.transparent,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            vertical: 5, horizontal: 6),
                        margin: const EdgeInsets.only(bottom: 5),
                        decoration: BoxDecoration(
                          color: isHover || isActivity
                              ? niimbotTheme
                                  .colors.solidBackgroundFillColorSecondary
                              : Colors.transparent,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Row(
                          children: [
                            NiimbotIcons.excel(
                              size: 16,
                              color: niimbotTheme.colors.successColorNormal,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: NiimbotToolTip(
                                text: item['name'], // 文本内容
                                textStyle:
                                    NiimbotTheme.of(context).typography.body,
                                position: NiimbotPopoverPositionEnum.top,
                              ),
                            ),
                            const SizedBox(width: 8),
                            isHover
                                ? InkWell(
                                    onTap: () {
                                      _onTapDeleteDataSource(item);
                                    },
                                    onHover: (final e) {
                                      setState(() {
                                        hoverDeleteId = e ? index : -1;
                                      });
                                    },
                                    child: NiimbotIcons.delete(
                                      size: 18,
                                      color: !isHoverDelete
                                          ? niimbotTheme
                                              .colors.textFillColorPrimary
                                          : niimbotTheme.colors.brandColor,
                                    ),
                                  )
                                : const SizedBox(width: 18),
                          ],
                        ),
                      ),
                    );
                  }),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Divider(
                height: 1,
                color: niimbotTheme.colors.dividerColor,
              ),
            ),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 7, vertical: 10),
              // margin: const EdgeInsets.only(bottom: 12, top: 10),
              child: NiimbotButton(
                type: ButtonType.secondary,
                text: NiimbotIntl.getIntlMessage('app100001071', '导入Excel'),
                onPressed: () {
                  if (dataSourceList.length < 50) {
                    if (widget.onTap != null) {
                      widget.onTap?.call();
                      NiimbotPopoverController.removeAny();
                    }
                  } else {
                    NiimbotToastController().show(
                      type: ToastType.warning,
                      NiimbotIntl.getIntlMessage(
                          'pc0243', '已达资源上限：\$，请使用打印APP清理后再试',
                          param: ['50']),
                      icon: NiimbotIcons.warning(
                        color:
                            NiimbotTheme.of(context).colors.warningColorNormal,
                        size: 20.00,
                      ),
                    );
                  }
                },
              ),
            )
          ],
        ),
      ),
    );
  }
}
