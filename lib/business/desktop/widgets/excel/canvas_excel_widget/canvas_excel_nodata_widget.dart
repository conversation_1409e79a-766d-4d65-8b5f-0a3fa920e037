import 'package:flutter/material.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_template/models/template/template_data_source.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:niimbot_ui/widgets/niimbot_popover/niimbot_popover.dart';
import 'package:url_launcher/url_launcher.dart';

import 'package:niimbot_flutter_canvas/business/desktop/utils/canvas_widget_manager_utils.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/excel/canvas_excel_widget/canvas_excel_list_widget.dart';

class CanvasExcelNoDataWidget extends StatefulWidget {
  final ValueChanged<TemplateDataSource>? onConfirm;
  final VoidCallback? onOpenFilePicker;

  const CanvasExcelNoDataWidget({
    super.key,
    this.onConfirm,
    this.onOpenFilePicker,
  });

  @override
  State<CanvasExcelNoDataWidget> createState() =>
      _CanvasExcelNoDataWidgetState();
}

class _CanvasExcelNoDataWidgetState extends State<CanvasExcelNoDataWidget> {
  final GlobalKey<NiimbotPopoverState> _popoverKey = GlobalKey();

  void _launchURL(final String url) async {
    final uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      throw 'Could not launch $url';
    }
  }

  @override
  Widget build(final BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        FutureBuilder(
            future:
                CanvasWidgetManager.sharedInstance().isLoginFunction!(false),
            builder: (final context, final AsyncSnapshot<bool> snapshot) {
              return NiimbotPopover(
                key: _popoverKey,
                trigger: NiimbotPopoverTrigger.manual,
                position: NiimbotPopoverPositionEnum.end,
                showArrow: true,
                content: CanvasExcelListWidget(
                  onTap: widget.onOpenFilePicker,
                  propsCtx: context,
                  onConfirm: widget.onConfirm,
                ),
                child: GestureDetector(
                  onTap: () async {
                    if (await CanvasWidgetManager.sharedInstance()
                            .isLoginFunction
                            ?.call(true) ==
                        true) {
                      if (CanvasWidgetManager.sharedInstance().getdataSources !=
                          null) {
                        final data = await CanvasWidgetManager.sharedInstance()
                            .getdataSources
                            ?.call();
                        if (data?.isEmpty ?? true) {
                          widget.onOpenFilePicker?.call();
                        } else {
                          _popoverKey.currentState?.show();
                        }
                      } else {
                        widget.onOpenFilePicker?.call();
                      }
                    }
                  },
                  child: Container(
                    width: 200,
                    height: 32,
                    decoration: BoxDecoration(
                      borderRadius:
                          const BorderRadius.all(Radius.circular(6.0)),
                      color: NiimbotTheme.of(context)
                          .colors
                          .translucentFillColorQuarternary,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const SizedBox(
                          width: 3,
                        ),
                        Text(
                          NiimbotIntl.getIntlMessage('pc0279', '导入Excel数据源'),
                          style: NiimbotTheme.maybeOf(context)
                              ?.typography
                              .smallBody,
                        ),
                        const SizedBox(
                          width: 3,
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }),
        const SizedBox(
          height: 12,
        ),
        InkWell(
          overlayColor: WidgetStateProperty.resolveWith(
            (final states) {
              if (states.contains(WidgetState.hovered)) {
                return Colors.transparent;
              }
              return Colors.transparent;
            },
          ),
          hoverColor: Colors.transparent,
          highlightColor: Colors.transparent,
          splashColor: Colors.transparent,
          onTap: () {
            _launchURL(NiimbotIntl.getCurrentLocale().languageCode == 'zh'
                ? 'https://whjc.yuque.com/gu3evw/qf2u1b/hycgfbfxty04s49o'
                : 'https://print.niimbot.com/h5#/customDocument/101092798');
          },
          child: Text(NiimbotIntl.getIntlMessage('pc0370', '查看教程和下载模板'),
              textAlign: TextAlign.center,
              style: NiimbotTheme.of(context)
                  .typography
                  .hint
                  ?.copyWith(color: NiimbotTheme.of(context).colors.smogBlue)),
        ),
      ],
    );
  }
}
