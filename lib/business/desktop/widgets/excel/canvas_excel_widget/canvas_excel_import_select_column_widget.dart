import 'package:flutter/material.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:niimbot_ui/widgets/niimbot_checkbox/niimbot_checkbox.dart';

import 'package:niimbot_flutter_canvas/business/desktop/utils/canvas_widget_manager_utils.dart';

class CanvasExcelImportSelectColumnWidget extends StatefulWidget {
  final Function(bool select) onSelectColumnTypeChange;

  const CanvasExcelImportSelectColumnWidget(
      {super.key, required this.onSelectColumnTypeChange});

  @override
  createState() => _CanvasExcelImportSelectColumnWidgetState();
}

class _CanvasExcelImportSelectColumnWidgetState
    extends State<CanvasExcelImportSelectColumnWidget> {
  bool select = true;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((final _) {
      _getLocalData();
    });
  }

  void _getLocalData() {
    final isSelect = CanvasWidgetManager().getExcelSelectColumn();
    if (isSelect != null) {
      setState(() {
        select = isSelect;
        widget.onSelectColumnTypeChange(select);
      });
    }
  }

  @override
  Widget build(final BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        NiimbotCheckbox(
          value: select,
          onChanged: (final bool? value) {
            select = !select;
            widget.onSelectColumnTypeChange(select);
            setState(() {});
          },
        ),
        InkWell(
            hoverColor: Colors.transparent,
            highlightColor: Colors.transparent,
            splashColor: Colors.transparent,
            onTap: () {
              select = !select;
              widget.onSelectColumnTypeChange(select);
              setState(() {});
            },
            child: Padding(
              padding: const EdgeInsetsDirectional.only(start: 8, bottom: 2),
              child: Text(
                NiimbotIntl.getIntlMessage('app100001155', '打印列名'),
                style: NiimbotTheme.of(context).typography.body,
              ),
            )),
      ],
    );
  }
}
