import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:lottie/lottie.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_ui/icons/icons.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:niimbot_ui/widgets/niimbot_popover/niimbot_popover_controller.dart';

class LottieJsonWidget extends HookWidget {
  const LottieJsonWidget({super.key});

  @override
  Widget build(final BuildContext context) {
    final animationController = useAnimationController();

    onAnimationStatusChange(final AnimationStatus status) {
      if (status == AnimationStatus.completed) {
        Future.delayed(const Duration(seconds: 1)).then((final value) {
          NiimbotPopoverController.removeLast();
        });
      }
    }

    onConfirm() {
      NiimbotPopoverController.removeLast();
    }

    useEffect(() {
      animationController.addStatusListener(onAnimationStatusChange);
      return () {
        animationController.removeStatusListener(onAnimationStatusChange);
      };
    });

    return Container(
      width: 736 * 1.2,
      color: Colors.transparent,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Stack(
            children: [
              SizedBox(
                width: 736 * 1.2,
                height: 413 * 1.2,
                child: Lottie.asset(
                    Directionality.of(context) == TextDirection.rtl
                        ? 'assets/lottie/canvas_data_bind_r.json'
                        : 'assets/lottie/canvas_data_bind_l.json',
                    controller: animationController,
                    onLoaded: (final compostion) {
                  animationController
                    ..duration = compostion.duration
                    ..forward();
                },
                    repeat: false,
                    animate: true,
                    package: 'niimbot_flutter_canvas',
                    fit: BoxFit.fill),
              ),
              Positioned(
                right: 13,
                top: 13,
                child: InkWell(
                  onTap: onConfirm,
                  child: NiimbotIcons.closeWindow(
                      size: 18,
                      color:
                          NiimbotTheme.of(context).colors.textFillColorPrimary),
                ),
              )
            ],
          ),
          Container(
              color: Colors.white,
              padding: const EdgeInsets.all(23.0),
              child: Column(
                children: [
                  Text(
                    '【${NiimbotIntl.getIntlMessage('app100000098', '操作指南')}】 ${NiimbotIntl.getIntlMessage('pc0242', '拖到元素上可更换或绑定数据源')}',
                    style: NiimbotTheme.maybeOf(context)
                        ?.typography
                        .bodyStrong
                        ?.copyWith(fontSize: 19, fontWeight: FontWeight.w700),
                  ),
                  const SizedBox(
                    height: 12,
                  ),
                  InkWell(
                    onTap: onConfirm,
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8.0),
                        color: NiimbotTheme.of(context).colors.brandColor,
                      ),
                      alignment: Alignment.center,
                      width: 160,
                      height: 40,
                      child: Text(
                        NiimbotIntl.getIntlMessage('app00707', '我知道了'),
                        style: NiimbotTheme.maybeOf(context)
                            ?.typography
                            .bodyStrong
                            ?.copyWith(
                                fontSize: 16,
                                fontWeight: FontWeight.w700,
                                color: Colors.white),
                      ),
                    ),
                  ),
                ],
              )),
        ],
      ),
    );
  }
}
