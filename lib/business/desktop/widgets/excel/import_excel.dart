import 'package:collection/collection.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_template/models/template/template_data_source.dart';
import 'package:niimbot_template/models/template/template_data_source_modify.dart';
import 'package:niimbot_template/models/template_data.dart';
import 'package:niimbot_ui/icons/icons.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:niimbot_ui/widgets/niimbot_dialog/niimbot_dialog_controller.dart';
import 'package:niimbot_ui/widgets/niimbot_toast/niimbot_loading_controller.dart';
import 'package:niimbot_ui/widgets/niimbot_toast/niimbot_toast_controller.dart';
import 'package:provider/provider.dart';

import 'package:niimbot_flutter_canvas/business/desktop/models/canvas_drag_model.dart';
import 'package:niimbot_flutter_canvas/business/desktop/models/excel_model.dart';
import 'package:niimbot_flutter_canvas/business/desktop/utils/canvas_widget_manager_utils.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/excel/canvas_excel_widget/canvas_excel_data_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/excel/canvas_excel_widget/canvas_excel_nodata_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/excel/canvas_excel_widget/canvas_lottile_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/excel/excel_utils.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/excel/import_excel_modal.dart';
import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_flutter_canvas/provider/history/history_store.dart';
import 'package:niimbot_flutter_canvas/provider/utils/canvas_store_utils.dart';
import 'package:niimbot_flutter_canvas/utils/buried_utils.dart';

class ImportExcel extends StatelessWidget {
  const ImportExcel({super.key, this.onImportDataSource});

  final Future<TemplateDataSource?> Function(TemplateDataSource dataSource)?
      onImportDataSource;

  _showLottieJson() {
    if (CanvasWidgetManager().firstLoadDataBindAction.call()) {
      NiimbotDialogController().showPure(child: const LottieJsonWidget());
    }
  }

  @override
  Widget build(final BuildContext context) {
    final niimbotColors = NiimbotTheme.of(context).colors;

    final hasDataSource = context.select<CanvasStore, bool>((final v) =>
        v.canvasData.dataSources?.firstOrNull?.rowData.isNotEmpty ?? false);

    Future<void> handleExcelAddElement(final TemplateDataSource dataSource,
        final TemplateData canvasData) async {
      final isSelect = CanvasWidgetManager().getExcelSelectColumn();
      List<String> header = [];
      final sheetName = dataSource.collections.firstOrNull ?? "Sheet1";
      if (dataSource.headers?[sheetName] is int) {
        header = dataSource.rowData.first;
      } else if (dataSource.headers?[sheetName] is List<String>) {
        header = dataSource.headers?[sheetName];
      }
      final addElement = [];
      final headerLength = header.length;
      final canvasRect = context.read<CanvasStore>().canvasViewRect;
      final left = canvasRect.left + 1;
      double y = canvasRect.top + 1;
      for (int i = 0; i < headerLength; i++) {
        ///空列情况下不添加
        if (!ExcelUtils.isEmptyColumn(i, dataSource)) {
          final sendDataModel = CanvasExcelDragSendDataModel(
            sheetName: sheetName,
            hash: dataSource.hash,
            column: i,
          );
          final element =
              await CanvasStoreUtils.generateElementByExcel(sendDataModel);
          if (element != null) {
            addElement.add(element);
            context.read<CanvasStore>().addElements(
              [element],
              offset: Offset(left, y),
              type: !isSelect && i + 1 == headerLength
                  ? SnapshotType.ignore
                  : SnapshotType.ignore,
            );
          }
          y += element!.height.toDouble() + 1;
        }
      }

      if (isSelect == true) {
        ///默认添加列名勾选上
        TemplateDataSourceModify templateDataSourceModify =
            TemplateDataSourceModify(
          useTitle: true,
        );
        Map<String, Map<String, TemplateDataSourceModify>> update = {};
        for (var element in addElement) {
          update[element.id] = {"0": templateDataSourceModify};
        }
        if (!context.mounted) return;
        context
            .read<CanvasStore>()
            .dataSourceController
            .updateTemplateColumnName(update, type: SnapshotType.ignore);
      }
    }

    Future<void> onConfirm(final TemplateDataSource dataSource) async {
      final templateDataSource =
          await onImportDataSource?.call(dataSource) ?? dataSource;
      if (!context.mounted) return;
      final canvasData = context.read<CanvasStore>().canvasData;

      /// 第一次导入数据源时显示动画
      _showLottieJson();
      int page = canvasData.dataSourceBindInfo?.page ?? 1;
      context.read<CanvasStore>().dataSourceController.updateTemplateDataSource(
            templateDataSource,
            page: page,
            refresh: true,
            type: canvasData.elements.isEmpty
                ? SnapshotType.ignore
                : SnapshotType.create,
          );
      if (canvasData.elements.isEmpty) {
        handleExcelAddElement(dataSource, canvasData);
      }
    }

    void showChooseExcelDialog({
      required final TemplateDataSource dataSource,
      final int totalPage = 1,
      final List<int> selectRow = const [],
    }) {
      NiimbotDialogController().show(
        width: 1036,
        showFooter: false,
        title: NiimbotIntl.getIntlMessage('pc0279', '导入数据源'),
        child: ImportExcelModal(
          totalPage: totalPage,
          dataSource: dataSource,
          selectRow: selectRow,
          onConfirm: onConfirm,
        ),
      );
    }

    void importExcel(final TemplateDataSource dataSource) async {
      /// 最少显示1s的正在解析， 然后导入成功
      if (dataSource.rowData.isNotEmpty) {
        NiimbotLoadingController()
            .show(NiimbotIntl.getIntlMessage('pc0249', '正在解析...'));
        NiimbotLoadingController().close();
        showChooseExcelDialog(dataSource: dataSource);
        NiimbotToastController()
            .showSuccess(NiimbotIntl.getIntlMessage('pc0198', '导入成功'));
      } else {
        NiimbotToastController()
            .show(NiimbotIntl.getIntlMessage('pc0199', 'excel为空'),
                icon: NiimbotIcons.warning(
                  color: NiimbotTheme.of(context).colors.textFillColorPrimary,
                  size: 20.00,
                ));
      }
    }

    /// 选择本地Excel文件
    void openFilePicker() async {
      try {
        final filePickerResult = await FilePicker.platform.pickFiles(
          type: FileType.custom, // 可以根据需要更改文件类型
          lockParentWindow: true,
          allowedExtensions: [
            'xls',
            'xlsx',
          ],
        );
        if (filePickerResult != null) {
          if (ExcelModel.checkExcel(filePickerResult)) {
            // 文件大小超过300KB，显示错误提示
            NiimbotToastController().showWarning(
                NiimbotIntl.getIntlMessage('app00568', '导入文件请勿超过300KB'));
            return; // 退出方法，不再继续处理文件
          }
          String? path = filePickerResult.files.first.path;
          // 处理选中的文件
          if (path != null) {
            LocalExcelInfo localExcelInfo = await ExcelUtils.coverExcel(path);
            if (localExcelInfo.savePath != "") {
              final dataSource = ExcelUtils.buildDataSourceByPath(
                  localExcelInfo.savePath,
                  fileName: filePickerResult.files.first.name,
                  hash: localExcelInfo.md5);
              if (dataSource != null) importExcel(dataSource);
            }
          }
        }
      } catch (e) {
        print('Error picking file: $e');
      }
    }

    ///断开关联
    void onTapBreakBind() {
      NiimbotDialogController().show(
        messageDialog: true,
        messageIcon: NiimbotIcons.warning(
            size: 20, color: niimbotColors.warningColorNormal),
        title: NiimbotIntl.getIntlMessage('pc0278', '确定要取消Excel数据源吗？'),
        onConfirm: (final done) {
          context.read<CanvasStore>().dataSourceController.breakBindData();
          BuriedUtils().track('click', '007_015_040', ext: {});
          done();
        },
        onCancel: (final done) {
          done();
        },
        child: Container(
          padding: const EdgeInsets.only(left: 28, top: 4),
          alignment: Alignment.centerLeft,
          child: Text(
              NiimbotIntl.getIntlMessage('pc0200', '取消后，所有字段将断开数据源链接并变为固定内容。'),
              style: NiimbotTheme.maybeOf(context)?.typography.body?.copyWith(
                  color:
                      NiimbotTheme.of(context).colors.textFillColorSecondary)),
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.only(top: 10, right: 16, bottom: 16, left: 16),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Text(NiimbotIntl.getIntlMessage('app100000757', '数据源'),
                      style: NiimbotTheme.maybeOf(context)
                          ?.typography
                          .titleStrong),
                  const SizedBox(
                    width: 2,
                  ),
                ],
              ),
              if (hasDataSource)
                MouseRegion(
                  cursor: SystemMouseCursors.click,
                  child: GestureDetector(
                    onTap: onTapBreakBind,
                    child: Text(NiimbotIntl.getIntlMessage('app00030', '取消'),
                        style: NiimbotTheme.maybeOf(context)
                            ?.typography
                            .body
                            ?.copyWith(color: niimbotColors.brandColor)),
                  ),
                )
            ],
          ),
          const SizedBox(height: 16),
          hasDataSource
              ? CanvasExcelDataWidget(
                  showChooseExcelDialog: showChooseExcelDialog,
                  onOpenFilePicker: openFilePicker,
                  onConfirm: importExcel,
                )
              : CanvasExcelNoDataWidget(
                  onOpenFilePicker: openFilePicker,
                  onConfirm: importExcel,
                )
        ],
      ),
    );
  }
}
