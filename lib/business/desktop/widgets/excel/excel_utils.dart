import 'dart:io';
import 'dart:typed_data';

import 'package:collection/collection.dart';
import 'package:crypto/src/md5.dart' show md5;
import 'package:dio/dio.dart';
import 'package:niimbot_excel/niimbot_data_source_utils.dart';
import 'package:niimbot_excel/niimbot_excel.dart';
import 'package:niimbot_excel/niimbot_excel_utils.dart';
import 'package:niimbot_template/models/template/template_data_source.dart';
import 'package:niimbot_template/models/template/template_enum.dart';
import 'package:path/path.dart' as p;

import 'package:niimbot_flutter_canvas/business/desktop/models/excel_model.dart';
import 'package:niimbot_flutter_canvas/business/desktop/utils/canvas_widget_manager_utils.dart';
import 'package:niimbot_flutter_canvas/utils/file_utils.dart';

class ExcelUtils {
  static TemplateDataSource? buildDataSourceByPath(
    final String path, {
    final String? uri,
    final String? fileName,
    required final String hash,
  }) {
    if (path.isNotEmpty) {
      try {
        final sheets = NiimbotExcelUtils.getSheets(path);
        final sheetName = sheets.firstOrNull ?? "Sheet1";
        final excelName =
            fileName ?? FileUtils.getFileNameWithoutExtension(path);
        final rowData = NiimbotExcelUtils.getContent(path, sheetName);
        return TemplateDataSource(
          type: TemplateDataSourceType.excel,
          uri: uri ?? path,
          hash: hash,
          name: excelName,
          collections: sheets,
          headers: {sheetName: 1},
          rowData: ExcelModel.buildRowData(rowData),
        );
      } catch (e) {
        NiimbotExcel.freedExcel();
        return null;
      }
    }
    return null;
  }

  /// 本地excel转换成本地path
  static Future<LocalExcelInfo> coverExcel(final String path) async {
    Uint8List fileData = await File(path).readAsBytes();

    /// 生成hash
    final String md5Hash = md5.convert(fileData).toString();
    var savePath =
        await NiimbotDataSourceUtils.buildLocalDataSourcePath(md5Hash);
    final ret = NiimbotExcel.validExcel(path);
    if (ret == 0) {
      /// 可以解析的也把文件存到本地
      final file = await File(savePath).create(recursive: true);
      file.writeAsBytesSync(fileData);
    } else {
      final ext = p.extension(path);

      /// 本地文件转换成oss文件
      String excelName = '$md5Hash$ext';
      String oldOssUrl = await CanvasWidgetManager.sharedInstance()
          .uploadExcelToOss!(path, excelName);
      if (File(savePath).existsSync()) {
        File(savePath).deleteSync();
      }

      /// 转换excel
      bool isCover = await CanvasWidgetManager.sharedInstance()
          .transformOldExcelToNewExcel!(oldOssUrl, savePath);

      if (isCover) {
        return LocalExcelInfo(savePath, md5Hash);
      } else {
        return const LocalExcelInfo("", "");
      }
    }

    return LocalExcelInfo(savePath, md5Hash);
  }

  /// 云端oss地址转换，处理xls的部分
  static Future<LocalExcelInfo> coverOssExcel(final String excelDownloadUrl,
      final int id, final String excelMd5, final String fileName) async {
    final savePath =
        await NiimbotDataSourceUtils.buildLocalDataSourcePath(excelMd5);
    final file = File(savePath);
    if (!file.existsSync()) {
      await Dio().download(excelDownloadUrl, savePath);
    }
    final ret = NiimbotExcel.validExcel(savePath);
    if (ret != 0) {
      if (File(savePath).existsSync()) {
        File(savePath).deleteSync();
      }

      /// 转换excel
      await CanvasWidgetManager.sharedInstance().transformOldExcelToNewExcel!(
          excelDownloadUrl, savePath);

      Uint8List fileData = await File(savePath).readAsBytes();

      String newOssUrl = await CanvasWidgetManager.sharedInstance()
          .uploadExcelToOss!(savePath, fileName);
      final md5Hash = md5.convert(fileData).toString();
      final fileSize = fileData.length;

      /// 更新cloud
      if (id != 0) {
        await CanvasWidgetManager.sharedInstance().updateCloudFile!(
          cloudId: id.toString(),
          downloadUrl: newOssUrl,
          md5: md5Hash,
          fileSize: fileSize,
        );
      }
      return LocalExcelInfo(savePath, excelMd5);
    }

    return LocalExcelInfo(savePath, excelMd5);
  }

  ///判断是否某列整体是否不存在
  static bool isEmptyColumn(
      final int index, final TemplateDataSource dataSource) {
    return !(dataSource.rowData.any((final element) {
      if (element.length <= index) {
        return false;
      }
      return element[index].trim().isNotEmpty;
    }));
  }
}

class LocalExcelInfo {
  final String savePath;
  final String md5;

  const LocalExcelInfo(this.savePath, this.md5);
}
