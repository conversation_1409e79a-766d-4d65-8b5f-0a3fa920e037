import 'dart:math';

import 'package:collection/collection.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_template/models/template/template_data_source.dart';
import 'package:niimbot_template/models/template/template_data_source_range.dart';
import 'package:niimbot_ui/icons/icons.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:niimbot_ui/widgets/niimbot_button.dart';
import 'package:niimbot_ui/widgets/niimbot_checkbox/niimbot_checkbox.dart';
import 'package:niimbot_ui/widgets/niimbot_popover/niimbot_popover.dart';
import 'package:niimbot_ui/widgets/niimbot_popover/niimbot_popover_controller.dart';
import 'package:niimbot_ui/widgets/niimbot_tooltip/niimbot_tooltip.dart';
import 'package:niimbot_ui/widgets/niimbot_toast/niimbot_toast.dart';
import 'package:niimbot_ui/widgets/niimbot_toast/niimbot_toast_controller.dart';
import 'package:two_dimensional_scrollables/two_dimensional_scrollables.dart';
import 'package:url_launcher/url_launcher.dart';

import 'package:niimbot_flutter_canvas/business/desktop/utils/canvas_widget_manager_utils.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/excel/canvas_excel_widget/canvas_excel_import_select_column_widget.dart';
import 'package:niimbot_flutter_canvas/utils/buried_utils.dart';

class DataItem extends Equatable {
  final List<String> contents;
  final bool selected;
  final int key;

  const DataItem({
    required this.contents,
    this.selected = false,
    this.key = -1,
  });

  @override
  List<Object?> get props => [contents, selected, key];

  DataItem copyWith({
    final List<String>? contents,
    final bool? selected,
    final int? key,
  }) {
    return DataItem(
      contents: contents ?? this.contents,
      selected: selected ?? this.selected,
      key: key ?? this.key,
    );
  }
}

const Color borderColor = Color.fromRGBO(0, 0, 0, 0.06);

class ImportExcelModal extends HookWidget {
  final int totalPage;
  final TemplateDataSource dataSource;
  final Function(TemplateDataSource)? onConfirm;
  final List<int> selectRow;

  const ImportExcelModal({
    super.key,
    required this.totalPage,
    required this.dataSource,
    this.selectRow = const [],
    this.onConfirm,
  });

  void _launchURL(final String url) async {
    final uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      throw 'Could not launch $url';
    }
  }

  @override
  Widget build(final BuildContext context) {
    final niimbotTheme = NiimbotTheme.of(context);
    final niimbotColors = niimbotTheme.colors;

    final _horizontalScrollController = useScrollController();
    final _verticalControllerHeader = useScrollController();

    /// 表头信息
    final headerInfo = useMemoized(() {
      final sheetName = dataSource.collections.firstOrNull ?? "Sheet1";
      return dataSource.headers?[sheetName];
    });

    final contentList = useMemoized(() {
      return headerInfo is List
          ? dataSource.rowData
          : dataSource.rowData.sublist(1, dataSource.rowData.length);
    });
    final rawHeaders = useMemoized<List<String>>(() {
      if (headerInfo is List<String>) {
        return headerInfo;
      }
      return dataSource.rowData.first;
    });

    final _isSelectColumn = useRef<bool>(true);

    final selectedIndex = useState<List<int>>(selectRow);

    // 选中/反选 某一行
    void selectChanged(final int key, final bool isSelected) {
      if (isSelected) {
        selectedIndex.value = [...selectedIndex.value, key];
      } else {
        selectedIndex.value = selectedIndex.value
            .where((final element) => element != key)
            .toList();
      }
    }

    // 全选/反选
    void _selectAllChanged(final bool isSelected) {
      final List<int> selected = [];
      for (int i = 0; i < contentList.length; i++) {
        if (isSelected) {
          selected.add(headerInfo is List ? i : i + 1);
        }
      }
      selectedIndex.value = selected;
    }

    /// 构建datasource
    void _buildExcelDataSource() async {
      if (onConfirm != null) {
        final ranges =
            arrToRange(selectedIndex.value.map((final e) => e + 1).toList());
        onConfirm?.call(dataSource.copyWith(range: ranges));
      }
    }

    _onSelected() async {
      if (selectedIndex.value.isEmpty) {
        NiimbotToastController().show(
          NiimbotIntl.getIntlMessage('pc0233', '请选择打印内容'),
          type: ToastType.warning,
          icon: NiimbotIcons.warning(
            color: NiimbotTheme.of(context).colors.warningColorNormal,
            size: 20.00,
          ),
        );
        return;
      }
      BuriedUtils().track('click', '007_015_039', ext: {});
      await CanvasWidgetManager().setExcelSelectColumn(_isSelectColumn.value);
      _buildExcelDataSource();
      NiimbotPopoverController.removeLast();
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 32.0),
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Text(
                    NiimbotIntl.getIntlMessage('pc0233', '请选择打印内容'),
                    style: NiimbotTheme.of(context).typography.body,
                  ),
                  Text(
                    '（${NiimbotIntl.getIntlMessage('pc0236', '已选')}${selectedIndex.value.length}/${contentList.length}）',
                    style: NiimbotTheme.of(context)
                        .typography
                        .smallBody
                        ?.copyWith(color: niimbotColors.textFillColorSecondary),
                  )
                ],
              ),
              CanvasExcelImportSelectColumnWidget(
                onSelectColumnTypeChange: (final bool isSelect) {
                  _isSelectColumn.value = isSelect;
                },
              ),
            ],
          ),
          const SizedBox(height: 16),
          SizedBox(
              height: 400,
              child: Scrollbar(
                  thickness: 8,
                  interactive: true,
                  trackVisibility: true,
                  thumbVisibility: true,
                  controller: _horizontalScrollController,
                  child: Scrollbar(
                    thickness: 8,
                    interactive: true,
                    trackVisibility: true,
                    thumbVisibility: true,
                    controller: _verticalControllerHeader,
                    child: TableView.builder(
                      pinnedRowCount: 1,
                      // pinnedColumnCount: 1,
                      rowCount: contentList.length + 1,
                      columnCount: rawHeaders.length + 1,
                      // diagonalDragBehavior: DiagonalDragBehavior.free,
                      horizontalDetails: ScrollableDetails.horizontal(
                        controller: _horizontalScrollController,
                      ),
                      verticalDetails: ScrollableDetails.vertical(
                        controller: _verticalControllerHeader,
                      ),
                      columnBuilder: (final int index) {
                        final decoration = TableSpanDecoration(
                          border: TableSpanBorder(
                            trailing: BorderSide(color: borderColor),
                            leading: index == 0
                                ? BorderSide(color: borderColor)
                                : BorderSide.none,
                          ),
                        );

                        return TableSpan(
                            backgroundDecoration: decoration,
                            extent: index == 0
                                ? FixedTableSpanExtent(50)
                                : _RemainingSpanExtent(
                                    count: rawHeaders.length,
                                    exclude: 50,
                                    min: 100));
                      },
                      rowBuilder: (final int index) {
                        final TableSpanDecoration decoration =
                            TableSpanDecoration(
                          color: index == 0
                              ? niimbotColors.solidBackgroundFillColorTertiary
                              : null,
                          border: TableSpanBorder(
                            trailing: BorderSide(color: borderColor),
                            leading: index == 0
                                ? BorderSide(color: borderColor)
                                : BorderSide.none,
                          ),
                        );
                        return TableSpan(
                            backgroundDecoration: decoration,
                            extent: FixedTableSpanExtent(50));
                      },
                      cellBuilder: (final BuildContext context,
                          final TableVicinity vicinity) {
                        if (vicinity.column == 0) {
                          /* 第一列 都是选择框 */
                          if (vicinity.row == 0) {
                            /* 第一行 全选选择框 */
                            return TableViewCell(
                              child: Center(
                                child: NiimbotCheckbox(
                                    value: selectedIndex.value.length ==
                                        contentList.length,
                                    onChanged: (final isSelected) {
                                      _selectAllChanged(isSelected ?? false);
                                    }),
                              ),
                            );
                          }
                          final key = vicinity.row;
                          final selected = selectedIndex.value.contains(key);
                          return TableViewCell(
                            child: Center(
                              child: NiimbotCheckbox(
                                value: selected,
                                onChanged: (final isSelected) {
                                  selectChanged(key, isSelected ?? false);
                                },
                              ),
                            ),
                          );
                        }
                        return TableViewCell(
                          child: Padding(
                              padding: const EdgeInsets.symmetric(
                                  vertical: 4.0, horizontal: 8),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  NiimbotToolTip(
                                    text: vicinity.row == 0
                                        ? rawHeaders[vicinity.column - 1]
                                        : contentList[vicinity.row - 1]
                                            [vicinity.column - 1],
                                    textStyle: vicinity.row == 0
                                        ? niimbotTheme.typography.bodyStrong
                                            ?.copyWith(
                                                color: niimbotTheme.colors
                                                    .textFillColorPrimary)
                                        : niimbotTheme.typography.smallBody
                                            ?.copyWith(height: 1.2),
                                    maxLines: vicinity.row == 0 ? 1 : 2,
                                    position: NiimbotPopoverPositionEnum.bottom,
                                  )
                                ],
                              )),
                        );
                      },
                    ),
                  ))),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              InkWell(
                overlayColor: WidgetStateProperty.resolveWith(
                  (final states) {
                    if (states.contains(WidgetState.hovered)) {
                      return Colors.transparent;
                    }
                    return Colors.transparent;
                  },
                ),
                hoverColor: Colors.transparent,
                highlightColor: Colors.transparent,
                splashColor: Colors.transparent,
                onTap: () {
                  _launchURL(NiimbotIntl.getCurrentLocale().languageCode == 'zh'
                      ? 'https://whjc.yuque.com/gu3evw/qf2u1b/hycgfbfxty04s49o'
                      : 'https://print.niimbot.com/h5#/customDocument/101092798');
                },
                child: Row(
                  children: [
                    Container(
                      margin: const EdgeInsetsDirectional.only(end: 4),
                      child: NiimbotIcons.help(
                          size: 20, color: niimbotColors.smogBlue),
                    ),
                    Text(NiimbotIntl.getIntlMessage('pc0250', '查看教程'),
                        style: NiimbotTheme.of(context)
                            .typography
                            .body
                            ?.copyWith(color: niimbotColors.smogBlue))
                  ],
                ),
              ),
              SizedBox(
                height: 32,
                child: Row(
                  children: [
                    NiimbotButton(
                      text: NiimbotIntl.getIntlMessage('app100000692', '取消'),
                      height: 32,
                      onPressed: () {
                        NiimbotPopoverController.removeLast();
                      },
                      type: ButtonType.secondary,
                    ),
                    const SizedBox(width: 8),
                    NiimbotButton(
                        text: NiimbotIntl.getIntlMessage('login0080', '确认'),
                        height: 32,
                        onPressed: _onSelected),
                  ],
                ),
              )
            ],
          )
        ],
      ),
    );
  }
}

class _RemainingSpanExtent extends SpanExtent {
  final int count;
  final double exclude;
  final double min;

  /// Creates a [_RemainingSpanExtent].
  const _RemainingSpanExtent(
      {required this.count, this.exclude = 0, this.min = 0});

  @override
  double calculateExtent(final SpanExtentDelegate delegate) {
    return max(min, (delegate.viewportExtent - exclude) / count);
  }
}
