import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/shared/canvas_font_data.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_template/models/elements/element_enum.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:niimbot_ui/widgets/niimbot_range_slider.dart';

import 'package:niimbot_flutter_canvas/business/desktop/utils/canvas_widget_manager_utils.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/check_box_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/color_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/font_height_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/font_size_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/text_align_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/text_direction_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/text_style_widget.dart';

final Logger _logger = Logger("TextStyleAttributeWidget", on: kDebugMode);

/// 文本属性设置
class TextStyleAttributeWidget extends StatelessWidget {
  final String? fontCode;
  final String? fontFamily;
  final ValueChanged<CanvasFontData>? onFontFamilyChange;
  final num? fontSize;
  final ValueChanged<num>? onFontSizeChange;
  final NetalTextAlign? textAlign;
  final ValueChanged<NetalTextAlign>? onTextAlignChange;
  final List<NetalTextFontStyle>? fontStyle;
  final ValueChanged<List<NetalTextFontStyle>>? onFontStyleChange;
  final num? letterSpacing;
  final ValueChanged<num>? onLetterSpacingChange;
  final num? lineSpacing;
  final ValueChanged<num>? onLineSpacingChange;
  final TextTypesettingMode? typesettingMode;
  final ValueChanged<TextTypesettingMode>? onTypesettingModeChange;
  final List<num>? typesettingParam;
  final ValueChanged<List<int>>? onTypesettingParamChange;
  final int? colorChannel;
  final Color? elementColor;
  final void Function(int channel, Color color)? onColorChannelChange;
  final bool colorReverse;
  final ValueChanged<bool>? onColorReverseChange;
  final NetalTextLineBreakMode? lineBreakMode;
  final ValueChanged<NetalTextLineBreakMode>? onLineBreakModeChange;
  final bool showTypesettingMode;
  final bool showColor;
  final bool showColorReverse;
  final bool showWorkBreak;

  const TextStyleAttributeWidget({
    this.fontCode,
    this.fontFamily,
    this.onFontFamilyChange,
    this.fontSize,
    this.onFontSizeChange,
    this.textAlign,
    this.onTextAlignChange,
    this.fontStyle,
    this.onFontStyleChange,
    this.letterSpacing,
    this.onLetterSpacingChange,
    this.lineSpacing,
    this.onLineSpacingChange,
    this.typesettingMode,
    this.onTypesettingModeChange,
    this.typesettingParam,
    this.onTypesettingParamChange,
    this.colorChannel,
    this.elementColor,
    this.onColorChannelChange,
    final bool? colorReverse,
    this.onColorReverseChange,
    this.lineBreakMode,
    this.onLineBreakModeChange,
    this.showTypesettingMode = true,
    this.showColor = true,
    this.showColorReverse = true,
    this.showWorkBreak = true,
    super.key,
  }) : colorReverse = colorReverse ?? false;

  @override
  Widget build(final BuildContext context) {
    return Column(
      children: [
        Container(
          alignment: AlignmentDirectional.centerStart,
          child: Text(
            NiimbotIntl.getIntlMessage('pc0043', '文本样式'),
            style: NiimbotTheme.of(context).typography.titleStrong,
          ),
        ),
        const SizedBox(height: 12),
        if (CanvasWidgetManager.sharedInstance().fontFamilyWidget != null)
          CanvasWidgetManager.sharedInstance().fontFamilyWidget!(
              (final CanvasFontData? val) {
            onFontFamilyChange?.call(val!);
          }, fontCode),
        const SizedBox(height: 16),
        FontSizeWidget(
          value: fontSize,
          valueChanged: onFontSizeChange,
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 32,
          child: Row(
            children: [
              Expanded(
                  flex: 1,
                  child: TextAlignWidget(
                    isVertical: typesettingMode == TextTypesettingMode.Vertical,
                    disabled: typesettingMode == TextTypesettingMode.Arc,
                    value: textAlign,
                    valueChanged: onTextAlignChange,
                  )),
              const SizedBox(
                width: 16,
              ),
              Expanded(
                flex: 1,
                child: TextStyleWidget(
                  disabledList: typesettingMode == TextTypesettingMode.Arc
                      ? [NetalTextFontStyle.underline]
                      : [],
                  value: fontStyle ?? [],
                  valueChanged: onFontStyleChange,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 14),
        FontHeightWidget(
          title: NiimbotIntl.getIntlMessage('app01011', '字距'),
          number: letterSpacing,
          max: 5,
          min: -5,
          isNegative: true,
          valueChanged: onLetterSpacingChange,
        ),
        const SizedBox(height: 16),
        FontHeightWidget(
          title: NiimbotIntl.getIntlMessage('app01010', '行距'),
          max: 5,
          min: -5,
          isNegative: true,
          number: lineSpacing,
          valueChanged: onLineSpacingChange,
        ),
        if (showTypesettingMode)
          Column(
            children: [
              const SizedBox(height: 22),
              Container(
                alignment: AlignmentDirectional.centerStart,
                child: Text(
                  NiimbotIntl.getIntlMessage('app100000761', '文本方向'),
                  style: NiimbotTheme.of(context).typography.body,
                ),
              ),
              const SizedBox(height: 10),
              SizedBox(
                width: double.infinity,
                height: 32,
                child: TextDirectionWidget(

                    value: typesettingMode,
                    onChange: onTypesettingModeChange),
              ),
              typesettingMode == TextTypesettingMode.Arc
                  ? NiimbotRangeSlider(
                      width: 270,
                      values: [
                        typesettingParam!.map((final e) => e.toDouble()).toList().last
                      ],
                      min: -360.0,
                      max: 360.0,
                      onDragging: (final handlerIndex, final lowerValue, final upperValue) {
                        onTypesettingParamChange?.call([0, lowerValue.toInt()]);
                      },
                      divisions: 720,
                    )
                  : const SizedBox(height: 12),
            ],
          ),
        const SizedBox(height: 4),
        if (showColor)
          Column(
            children: [
              ColorWidget(
                value: colorChannel,
                color: elementColor,
                onChange: onColorChannelChange,
              ),
            ],
          ),

        if (showColorReverse)
          Column(children: [
            const SizedBox(height: 22),
            CheckBoxWidget(
              title: NiimbotIntl.getIntlMessage('app100000420', '反白'),
              isShowHelpIcon: true,
              value: colorReverse,
              valueChanged: onColorReverseChange,
              isOverWhite: true,
            ),
          ]),
        if (showWorkBreak)
          Column(
            children: [
              const SizedBox(height: 22),
              CheckBoxWidget(
                title: NiimbotIntl.getIntlMessage('app01591', '按单词换行'),
                enable: typesettingMode == TextTypesettingMode.Horizontal,
                value: lineBreakMode == NetalTextLineBreakMode.word,
                valueChanged: (final value) {
                  onLineBreakModeChange?.call(value
                      ? NetalTextLineBreakMode.word
                      : NetalTextLineBreakMode.char);
                },
              ),
            ],
          )
      ],
    );
  }
}

