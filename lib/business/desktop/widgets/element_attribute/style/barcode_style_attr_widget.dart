import 'package:flutter/cupertino.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_ui/icons/icons.dart';
import 'package:niimbot_ui/model/niimbot_mouse_enum.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:niimbot_ui/widgets/niimbot_radio/niimbot_radio_button.dart';

import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/color_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/font_size_widget.dart';

class BarCodeStyleAttributeWidget extends StatelessWidget {
  final num? fontSize;
  final ValueChanged<num>? onFontSizeChange;
  final int? colorChannel;
  final void Function(int channel, Color color)? onColorChannelChange;
  final NetalBarcodeTextPosition? textPosition;
  final ValueChanged<NetalBarcodeTextPosition>? onTextPositionChange;
  final NetalBarcodeType? type;
  final Color? elementColor;

  const BarCodeStyleAttributeWidget({
    super.key,
    this.fontSize,
    this.onFontSizeChange,
    this.colorChannel,
    this.onColorChannelChange,
    this.textPosition,
    this.onTextPositionChange,
    this.type,
    this.elementColor,
  });

  ///部分编码格式（ UPC_A(21)、UPC_E(22)、EAN8(23)、EAN13(24)）应只支持数字在下的样式，无数字和数字在上置灰不可点
  bool _boolIsOnlyApplyBarcodeNumberBelow() {
    return [21, 22, 23, 24].contains(type?.value);
  }

  @override
  Widget build(final BuildContext context) {
    final themeColors = NiimbotTheme.of(context).colors;

    return Column(
      children: [
        Container(
          alignment: AlignmentDirectional.centerStart,
          child: Text(
            NiimbotIntl.getIntlMessage('pc0039', '一维码样式'),
            style: NiimbotTheme.of(context).typography.titleStrong,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              NiimbotIntl.getIntlMessage('app01006', '样式'),
              style: NiimbotTheme.of(context).typography.body,
            ),
            NiimbotRadioButton(
              width: 146,
              height: 32,
              itemBuild:
                  (final dynamic item, final NiimbotMouseEnum mouseType) {
                Color color = mouseType == NiimbotMouseEnum.active
                    ? themeColors.brandColor
                    : themeColors.textFillColorPrimary;
                if (item == NetalBarcodeTextPosition.bottom) {
                  return NiimbotIcons.barcodeNumberBelow(
                    color: color,
                    size: 24.00,
                  );
                } else if (item == NetalBarcodeTextPosition.none) {
                  return NiimbotIcons.barCode(
                    color: color,
                    size: 24.00,
                  );
                } else {
                  return NiimbotIcons.barcodeNumberAbove(
                    color: color,
                    size: 24.00,
                  );
                }
              },
              value: textPosition,
              valueChanged: (final dynamic value) {
                onTextPositionChange?.call(value);
              },
              children: const [
                NetalBarcodeTextPosition.bottom,
                NetalBarcodeTextPosition.none,
                NetalBarcodeTextPosition.top
              ],
              disabledList: _boolIsOnlyApplyBarcodeNumberBelow()
                  ? [
                      NetalBarcodeTextPosition.none,
                      NetalBarcodeTextPosition.top
                    ]
                  : [],
            ),
          ],
        ),
        const SizedBox(height: 16),
        FontSizeWidget(
          value: fontSize,
          valueChanged: onFontSizeChange,
        ),
        const SizedBox(height: 16),
        ColorWidget(
          value: colorChannel,
          onChange: onColorChannelChange,
          color: elementColor,
        ),
      ],
    );
  }
}
