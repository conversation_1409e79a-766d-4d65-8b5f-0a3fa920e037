import 'package:flutter/cupertino.dart';
import 'package:niimbot_intl/niimbot_intl.dart';

import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/color_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/content_box_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/font_height_widget.dart';

class TableStyleAttrWidget extends StatelessWidget {
  final num? row;
  final ValueChanged<num>? onRowChange;
  final num? column;
  final ValueChanged<num>? onColumnChange;
  final num? lineWidth;
  final ValueChanged<num>? onLineWidthChange;
  final int? contentColorChannel;
  final void Function(int channel, Color color)? onContentColorChannelChange;
  final int? lineColorChannel;
  final void Function(int channel, Color color)? onLineColorChannelChange;
  final Color? contentColor;
  final Color? lineColor;

  const TableStyleAttrWidget({
    this.row,
    this.onRowChange,
    this.column,
    this.onColumnChange,
    this.lineWidth,
    this.onLineWidthChange,
    this.contentColorChannel,
    this.onContentColorChannelChange,
    this.lineColorChannel,
    this.onLineColorChannelChange,
    required this.contentColor,
    required this.lineColor,
    super.key,
  });

  void _onRowChange(final num val) {
    onRowChange?.call(val);
  }

  @override
  Widget build(final BuildContext context) {
    return ContentBoxWidget(
      title: NiimbotIntl.getIntlMessage('pc0395', '表格样式'),
      children: [
        FontHeightWidget(
          title: NiimbotIntl.getIntlMessage('app100000777', '行数'),
          number: row,
          min: 1,
          max: 20,
          stepper: 1,
          valueChanged: _onRowChange,
        ),
        const SizedBox(height: 16),
        FontHeightWidget(
          title: NiimbotIntl.getIntlMessage('app100000778', '列数'),
          number: column,
          min: 1,
          max: 20,
          stepper: 1,
          valueChanged: onColumnChange,
        ),
        const SizedBox(height: 16),
        FontHeightWidget(
          title: NiimbotIntl.getIntlMessage('app00154', '线粗'),
          number: lineWidth,
          min: 0.2,
          max: 3,
          valueChanged: onLineWidthChange,
        ),
        const SizedBox(height: 16),
        ColorWidget(
          title: NiimbotIntl.getIntlMessage('app100000430', '文本颜色'),
          value: contentColorChannel,
          onChange: onContentColorChannelChange,
          color: contentColor,
        ),
        const SizedBox(height: 16),
        ColorWidget(
          title: NiimbotIntl.getIntlMessage('app100000431', '线条颜色'),
          value: lineColorChannel,
          onChange: onLineColorChannelChange,
          color: lineColor,
        ),
      ],
    );
  }
}
