import 'package:flutter/cupertino.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_ui/styles/theme.dart';

import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/graph_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/style/line_style_attr_widget.dart';

class GraphStyleAttrCommonWidget extends StatelessWidget {
  final bool showTitle;
  final NetalLineType? lineType;
  final ValueChanged<NetalLineType?> onLineTypeChange;
  final num lineWidth;
  final ValueChanged<num>? onLineWidthChange;
  final int? colorChannel;
  final void Function(int channel, Color color)? onColorChannelChange;
  final NetalGraphType? value;
  final ValueChanged<NetalGraphType>? valueChanged;
  final Color? elementColor;

  const GraphStyleAttrCommonWidget({
    super.key,
    this.showTitle = true,
    this.lineType,
    required this.onLineTypeChange,
    required this.lineWidth,
    this.onLineWidthChange,
    this.colorChannel,
    this.onColorChannelChange,
    this.value,
    this.valueChanged,
    this.elementColor,
  });

  @override
  Widget build(final BuildContext context) {
    return Column(
      children: [
        Container(
          alignment: AlignmentDirectional.centerStart,
          child: Text(
            NiimbotIntl.getIntlMessage("pc0038", "形状样式"),
            style: NiimbotTheme.maybeOf(context)?.typography.titleStrong,
          ),
        ),
        const SizedBox(height: 19),
        Container(
          alignment: AlignmentDirectional.centerStart,
          child: Text(
            NiimbotIntl.getIntlMessage("app00011", "形状"),
            style: NiimbotTheme.of(context).typography.body,
          ),
        ),
        const SizedBox(height: 6),
        SizedBox(
          width: double.infinity,
          height: 32,
          child: GraphWidget(value: value, valueChanged: valueChanged),
        ),
        LineStyleAttributeWidget(
          showTitle: false,
          lineType: lineType,
          lineWidth: lineWidth,
          colorChannel: colorChannel,
          onLineTypeChange: onLineTypeChange,
          onColorChannelChange: onColorChannelChange,
          onLineWidthChange: onLineWidthChange,
          elementColor: elementColor,
        ),
      ],
    );
  }
}
