import 'package:flutter/material.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/multiple/color_style_attr_widget.dart';
import 'package:niimbot_intl/niimbot_intl.dart';


class QRCodeStyleAttributeWidget extends StatelessWidget {
  final int? colorChannel;
  final void Function(int channel, Color color)? onColorChannelChange;
  final Color? elementColor;

  const QRCodeStyleAttributeWidget({
    super.key,
    this.colorChannel,
    this.onColorChannelChange,
    this.elementColor,
  });

  @override
  Widget build(final BuildContext context) {
    return ColorStyleAttributeWidget(
      title: NiimbotIntl.getIntlMessage('pc0042', '二维码样式'),
      colorChannel: colorChannel,
      onColorChannelChange: onColorChannelChange,
      elementColor: elementColor,
    );
  }
}
