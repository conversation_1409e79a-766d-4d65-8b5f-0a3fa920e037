import 'package:flutter/cupertino.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_template/models/elements/element_enum.dart';
import 'package:niimbot_template/models/elements/material_element.dart';
import 'package:niimbot_ui/styles/theme.dart';

import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/check_box_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/base_material_attr.dart';

class MaterialStyleAttrWidget extends StatelessWidget {
  final MaterialElement element;
  final ValueChanged<List<num>>? onImageProcessingValue;
  final ValueChanged<bool>? onImageAllowFreeZoomChange;
  final Function(int value, Color color)? onImageColorChange;
  final ValueChanged<bool>? onColorReverseChange;

  const MaterialStyleAttrWidget({
    required this.element,
    this.onImageProcessingValue,
    this.onImageAllowFreeZoomChange,
    this.onImageColorChange,
    this.onColorReverseChange,
    super.key,
  });

  @override
  Widget build(final BuildContext context) {
    return Column(
      children: [
        Container(
          alignment: AlignmentDirectional.centerStart,
          child: Text(
            element.materialType == MaterialElementType.border
                ? NiimbotIntl.getIntlMessage('pc0261', '边框样式')
                : NiimbotIntl.getIntlMessage('pc0041', '图标样式'),
            style: NiimbotTheme.of(context).typography.titleStrong,
          ),
        ),
        const SizedBox(height: 16),
        ...BaseMaterialAttribute(
          processingValue: element.imageProcessingValue,
          imageProcessingType: element.imageProcessingType,
          onProcessingValueChange: onImageProcessingValue,
          allowFreeZoom: element.allowFreeZoom,
          onAllowFreeZoomChange: onImageAllowFreeZoomChange,
          colorChannel: element.colorChannel,
          onColorChannelChange: onImageColorChange,
          elementColor: element.elementColor,
        ).build(context),
        const SizedBox(height: 16),
        CheckBoxWidget(
          title: NiimbotIntl.getIntlMessage('app100000420', '反白'),
          isShowHelpIcon: true,
          value: element.colorReverse == 1,
          valueChanged: onColorReverseChange,
          isOverWhite: true,
        ),
      ],
    );
  }
}
