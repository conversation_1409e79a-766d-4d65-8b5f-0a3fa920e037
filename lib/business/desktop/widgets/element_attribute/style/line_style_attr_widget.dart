import 'package:flutter/material.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_ui/icons/icons.dart';
import 'package:niimbot_ui/model/niimbot_mouse_enum.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:niimbot_ui/widgets/niimbot_radio/niimbot_radio_button.dart';


import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/color_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/font_height_widget.dart';

class LineStyleAttributeWidget extends StatelessWidget {
  final bool showTitle;
  final NetalLineType? lineType;
  final ValueChanged<NetalLineType?> onLineTypeChange;
  final num lineWidth;
  final ValueChanged<num>? onLineWidthChange;
  final int? colorChannel;
  final void Function(int channel, Color color)? onColorChannelChange;
  final Color? elementColor;

  const LineStyleAttributeWidget({
    super.key,
    this.showTitle = true,
    this.lineType,
    required this.onLineTypeChange,
    required this.lineWidth,
    this.onLineWidthChange,
    this.colorChannel,
    this.onColorChannelChange,
    this.elementColor,
  });

  @override
  Widget build(final BuildContext context) {
    final themeColors = NiimbotTheme.of(context).colors;
    return Column(
      children: [
        if (showTitle)
          Container(
            alignment: AlignmentDirectional.centerStart,
            child: Text(
              NiimbotIntl.getIntlMessage('app100000773', '线条样式'),
              style: NiimbotTheme.of(context).typography.titleStrong,
            ),
          ),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              NiimbotIntl.getIntlMessage('app01006', '样式'),
              style: NiimbotTheme.of(context).typography.body,
            ),
            NiimbotRadioButton(
              width: 146,
              height: 32,
              itemBuild: (final dynamic item, final NiimbotMouseEnum mouseType) {
                Color color = mouseType == NiimbotMouseEnum.active
                    ? themeColors.brandColor
                    : themeColors.textFillColorPrimary;
                if (item == NetalLineType.solid) {
                  return NiimbotIcons.horizontalLine(
                    color: color,
                    size: 24.00,
                  );
                } else {
                  return NiimbotIcons.lineDashed(
                    color: color,
                    size: 24.00,
                  );
                }
              },
              value: lineType,
              valueChanged: onLineTypeChange,
              children: const [
                NetalLineType.solid,
                NetalLineType.dash,
              ],
            ),
          ],
        ),
        const SizedBox(height: 16),
        FontHeightWidget(
          title: NiimbotIntl.getIntlMessage('app00154', '线粗'),
          max: 3,
          min: 0.2,
          number: lineWidth,
          valueChanged: onLineWidthChange,
        ),
        const SizedBox(height: 16),
        ColorWidget(
          value: colorChannel,
          onChange: onColorChannelChange,
          color: elementColor,
        ),
      ],
    );
  }
}
