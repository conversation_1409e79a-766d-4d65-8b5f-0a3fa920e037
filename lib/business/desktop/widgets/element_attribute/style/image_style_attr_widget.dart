import 'package:async/async.dart';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/base_material_attr.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/image_style_widget.dart';
import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_template/models/elements/image_element.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:provider/provider.dart';

class ImageStyleAttributeWidget extends HookWidget {
  final ImageElement element;
  final ValueChanged<NetalImageRenderType>? onImageRenderTypeChange;
  final ValueChanged<List<num>>? onProcessingValueChange;
  final ValueChanged<bool>? onAllowFreeZoomChange;
  final void Function(int channel, Color color)? onColorChannelChange;

  final List<num>? processingValue;
  final bool? allowFreeZoom;
  final int? colorChannel;
  final NetalImageRenderType? imageProcessingType;
  final Color? elementColor;
  final bool isMultiChoose;

  const ImageStyleAttributeWidget({
    super.key,
    required this.element,
    this.onImageRenderTypeChange,
    this.onProcessingValueChange,
    this.onAllowFreeZoomChange,
    this.onColorChannelChange,
    this.processingValue,
    this.allowFreeZoom,
    this.colorChannel,
    this.imageProcessingType,
    this.elementColor,
    this.isMultiChoose = false,
  });

  @override
  Widget build(final BuildContext context) {
    final pdfModel = context
        .read<CanvasStore>()
        .canvasData
        .fileDataSources
        ?.firstWhereOrNull((final e) => e.type == 'pdf')
        ?.rule
        .firstOrNull;

    final boolShow = isMultiChoose
        ? true
        : (pdfModel != null && (pdfModel.elementId == element.id))
            ? false
            : true;
    final uploadRestartableTimer = useState<RestartableTimer?>(null);
    // final processingValueTemp = useState(processingValue);
    onImageRenderTypeChangeTemp(final List<num> value) {
      onProcessingValueChange?.call(value);
      const duration = Duration(milliseconds: 600);
      if (uploadRestartableTimer.value == null) {
        context.read<CanvasStore>().setImageSliderIng(true);
        uploadRestartableTimer.value = RestartableTimer(duration, () {
          context.read<CanvasStore>().setImageSliderIng(false);
        });
      } else {
        uploadRestartableTimer.value?.reset();
      }
    }

    return Column(
      children: [
        Container(
          alignment: AlignmentDirectional.centerStart,
          child: Text(
            NiimbotIntl.getIntlMessage('pc0040', '图片样式'),
            style: NiimbotTheme.of(context).typography.titleStrong,
          ),
        ),
        ImageStyleWidget(
          element: element,
          imageProcessingType: imageProcessingType,
          imageProcessingTypeChange: onImageRenderTypeChange,
        ),
        const SizedBox(height: 16),
        ...BaseMaterialAttribute(
          imageProcessingType: imageProcessingType,
          processingValue: processingValue,
          onProcessingValueChange: onImageRenderTypeChangeTemp,
          allowFreeZoom: allowFreeZoom,
          onAllowFreeZoomChange: onAllowFreeZoomChange,
          colorChannel: colorChannel,
          onColorChannelChange: onColorChannelChange,
          elementColor: elementColor,
          showBlackRed: boolShow,
        ).build(context),
      ],
    );
  }
}
