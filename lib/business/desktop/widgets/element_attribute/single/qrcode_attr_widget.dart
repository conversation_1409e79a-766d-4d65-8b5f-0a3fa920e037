import 'package:collection/collection.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_template/models/template/template_data_source_modify.dart';
import 'package:niimbot_ui/model/niimbot_dropdown_model.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:provider/provider.dart';

import 'package:niimbot_flutter_canvas/model/element/qr_code_element.dart';
import 'package:niimbot_flutter_canvas/business/desktop/models/index.dart';
import 'package:niimbot_flutter_canvas/business/desktop/utils/canvas_bind_element_change_utils.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/content/data_bind_content_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/content/qrcode_content_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/style/qrcode_style_attr_widget.dart';
import 'package:niimbot_flutter_canvas/provider/models/qr_code_element_updater.dart';
import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_flutter_canvas/business/desktop/models/canvas_element_text_content_choose_model.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/select_contain_title_widget.dart';

final Logger _logger = Logger("CanvasElementSetQrCodeWidget", on: kDebugMode);

class QRCodeAttributeWidget extends StatelessWidget {
  final CanvasQRCodeElement element;

  const QRCodeAttributeWidget({super.key, required this.element});

  @override
  Widget build(final BuildContext context) {
    ///二维码文本内容
    onQrCodeValueChange(final String? val) {
      context
          .read<CanvasStore>()
          .qrcodeController
          .updateQRCodeElements({element.id: QRCodeElementUpdater(value: val)});
    }

    ///二维码编码格式
    onQrCodeNetalTypeValueChange(final NetalQRCodeType val) {
      context.read<CanvasStore>().qrcodeController.updateQRCodeElements(
          {element.id: QRCodeElementUpdater(codeType: val)});
    }

    ///二维码容错率
    onQRCodeCorrectLevelChange(final NetalQRCodeCorrectLevel correctLevel) {
      context.read<CanvasStore>().qrcodeController.updateQRCodeElements(
          {element.id: QRCodeElementUpdater(correctLevel: correctLevel)});
    }

    ///二维码颜色
    onQrCodeColorChange(final int value, final Color color) {
      context.read<CanvasStore>().qrcodeController.updateQRCodeElements({
        element.id: QRCodeElementUpdater(
          colorChannel: value,
          elementColor: color,
        )
      });
    }

    ///上传二维码逻辑
    onQrCodeImageChange(final String value, final Uint8List imageList) {
      context.read<CanvasStore>().qrcodeController.updateQRCodeElements({
        element.id: QRCodeElementUpdater(
          value: value,
          qrCodeImageData: imageList,
        )
      });
    }

    ///当前转换值
    final bindElementValue = context
        .select<CanvasStore, String?>((final v) => v.bindElementValue(element.id));
    bool bindElementUseTitle = context
        .select<CanvasStore, bool>((final v) => v.bindElementUseTitle(element.id));

    ///切换是否绑定列名
    void onChangeShowCurrentColumnChange(final bool check) {
      TemplateDataSourceModify templateDataSourceModify =
          TemplateDataSourceModify(
        useTitle: check,
      );
      final canvasData = context.read<CanvasStore>().canvasData;
      final elementVal = element.value ??
          context.read<CanvasStore>().getElementValue(element.id)?.value;
      if (elementVal == null) return;
      context.read<CanvasStore>().dataSourceController.updateTemplateModify(
          canvasData, element.id, elementVal, templateDataSourceModify);
    }

    ///切换绑定元素
    void onGenerateTypeChange(final ExcelSourceType type) {
      final focusedElement = context.read<CanvasStore>().focusedElement;
      final newElement =
          CanvasBindElementChangeTypeUtils.changeBindType(focusedElement??element, type);
      if (newElement != null) {
        context.read<CanvasStore>().replaceElement(element, newElement);
      }
    }

    return SingleChildScrollView(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 20, right: 22),
            child: (element.dataBind?.isNotEmpty ?? false)
                ? Column(
                    children: [
                      DataBindContentAttributeWidget(
                        value: bindElementValue,
                        generateType: ExcelSourceType.qrcode,
                        onGenerateTypeChange: onGenerateTypeChange,
                        printColumnNames: bindElementUseTitle,
                        onPrintColumnNames: onChangeShowCurrentColumnChange,
                      ),
                      const SizedBox(height: 5),
                      SelectContainTitleWidget(
                        leftTitle:
                            NiimbotIntl.getIntlMessage('app100000762', '编码格式'),
                        title: CanvasElementTextContentChooseModel
                                    .qrCodeStyleList()
                                .firstWhereOrNull(
                                    (final e) => e.value == element.codeType)
                                ?.label ??
                            '',
                        isDropDown: true,
                        value: element.codeType,
                        dataList: CanvasElementTextContentChooseModel
                            .qrCodeStyleList(),
                        onTapSelect: (final NiimbotDropDownModel? model) {
                          if (model != null) {
                            onQrCodeNetalTypeValueChange.call(
                                NetalQRCodeType.byValue(
                                    int.parse(model.value.value.toString()))!);
                          }
                        },
                      ),
                      const SizedBox(height: 12),
                      if (![NetalQRCodeType.DATA_MATRIX, NetalQRCodeType.PDF417]
                          .contains(element.codeType))
                        SelectContainTitleWidget(
                          leftTitle:
                              NiimbotIntl.getIntlMessage('pc0032', '容错率'),
                          isShowHelpIcon: true,
                          helpContent: NiimbotIntl.getIntlMessage(
                              'pc0051', '容错率越高，二维码越容易被识别'),
                          title: CanvasElementTextContentChooseModel
                                      .qRCodeCorrectLevel()
                                  .firstWhereOrNull((final e) =>
                                      e.value.toString() ==
                                      element.correctLevel.value.toString())
                                  ?.label ??
                              '',
                          isDropDown: true,
                          value: CanvasElementTextContentChooseModel
                                      .qRCodeCorrectLevel()
                                  .firstWhereOrNull((final e) =>
                                      e.value.toString() ==
                                      element.correctLevel.value.toString())
                                  ?.value ??
                              '',
                          dataList: CanvasElementTextContentChooseModel
                              .qRCodeCorrectLevel(),
                          onTapSelect: (final NiimbotDropDownModel? model) {
                            if (model != null) {
                              onQRCodeCorrectLevelChange.call(
                                  NetalQRCodeCorrectLevel.byValue(
                                      int.parse(model.value.toString()))!);
                            }
                          },
                        ),
                    ],
                  )
                : QRCodeContentWidget(
                    value: element.value,
                    onValueChange: onQrCodeValueChange,
                    type: element.codeType,
                    onTypeChange: onQrCodeNetalTypeValueChange,
                    correctLevel: element.correctLevel,
                    onCorrectLevelChange: onQRCodeCorrectLevelChange,
                    qrCodeImageData: element.qrCodeImageData,
                    qrCodeImageChange: onQrCodeImageChange,
                  ),
          ),
          const SizedBox(height: 26),
          Divider(
              color: NiimbotTheme.of(context).colors.dividerColor, height: 1),
          const SizedBox(height: 12),
          Padding(
            padding: const EdgeInsets.only(left: 20, right: 22, bottom: 12),
            child: QRCodeStyleAttributeWidget(
              colorChannel: element.colorChannel,
              onColorChannelChange: onQrCodeColorChange,
              elementColor: element.elementColor,
            ),
          ),
        ],
      ),
    );
  }
}
