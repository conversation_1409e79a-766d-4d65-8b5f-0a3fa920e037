import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/shared/canvas_font_data.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:niimbot_template/models/elements/element_enum.dart';
import 'package:niimbot_template/models/elements/text_element.dart';
import 'package:niimbot_template/models/template/template_data_source_modify.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:provider/provider.dart';

import 'package:niimbot_flutter_canvas/business/desktop/models/index.dart';
import 'package:niimbot_flutter_canvas/business/desktop/utils/canvas_bind_element_change_utils.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/content/data_bind_content_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/content/text_content_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/style/text_style_attr_widget.dart';
import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_flutter_canvas/provider/history/history_store.dart';
import 'package:niimbot_flutter_canvas/provider/models/text_element_updater.dart';

final Logger _logger = Logger("TextAttributeWidget", on: kDebugMode);

///文本元素属性设置
class TextAttributeWidget extends StatelessWidget {
  final TextElement element;

  const TextAttributeWidget({
    required this.element,
    super.key,
  });

  @override
  Widget build(final BuildContext context) {
    final themeColors = NiimbotTheme.of(context).colors;
    bool isVertical = element.typesettingMode == NetalTypesettingMode.vertical;

    onTextValueChange(final String? val) {
      context
          .read<CanvasStore>()
          .textController
          .updateTextElements({element.id: TextElementUpdater(value: val)});
    }

    onTextBlur(final String? val) {
      context.read<CanvasStore>().textController.updateTextElements(
          {element.id: TextElementUpdater(value: val)},
          type: SnapshotType.ignoreEnd);
    }

    ///更换字体
    onTextChangeFontFamily(final CanvasFontData fontItem) {
      context.read<CanvasStore>().textController.updateTextElements({
        element.id: TextElementUpdater(
          fontFamily: fontItem.code,
          fontCode: fontItem.code,
          hasVipRes: fontItem.isVip,
        )
      });
    }

    ///文本对齐方式
    onTextAlignChange(final NetalTextAlign val) {
      if (!isVertical) {
        context.read<CanvasStore>().textController.updateTextElements(
            {element.id: TextElementUpdater(horizontalAlign: val)});
      } else {
        context.read<CanvasStore>().textController.updateTextElements(
            {element.id: TextElementUpdater(verticalAlign: val)});
      }
    }

    ///粗体、下划线、斜体 fontStyle
    onTextFontStyleChange(final List<NetalTextFontStyle> fontStyle) {
      context.read<CanvasStore>().textController.updateTextElements(
          {element.id: TextElementUpdater(fontStyle: fontStyle)});
    }

    ///文本字距
    onTextLetterSpacingChange(final num val) {
      context
          .read<CanvasStore>()
          .textController
          .updateTextLetterSpacing(val, ids: [element.id]);
    }

    ///文本行距
    onTextLineSpacingChange(final num val) {
      context.read<CanvasStore>().textController.updateTextElements(
          {element.id: TextElementUpdater(lineSpacing: val)});
    }

    ///文本方向
    onTextNetalTypesettingModeChange(final TextTypesettingMode mode) {
      context
          .read<CanvasStore>()
          .textController
          .updateTextTypesettingMode([element.id], mode);
    }

    /// 按单词换行
    onTextNetalTextLineBreakModeChange(final NetalTextLineBreakMode val) {
      context.read<CanvasStore>().textController.updateTextElements(
          {element.id: TextElementUpdater(lineBreakMode: val)});
    }

    ///调整字体大小fontSize
    onTextFontSizeChange(final num val) {
      context
          .read<CanvasStore>()
          .textController
          .updateTextElements({element.id: TextElementUpdater(fontSize: val)});
    }

    ///是否反白
    onTextColorWhiteChange(final bool value) {
      context.read<CanvasStore>().textController.updateTextElements(
          {element.id: TextElementUpdater(colorReverse: value)});
    }

    ///颜色
    onTextColorChange(final int value, final Color color) {
      context.read<CanvasStore>().textController.updateTextElements({
        element.id: TextElementUpdater(
          colorChannel: value,
          elementColor: color,
        )
      });
    }

    ///文本弧度
    onTextTypesettingParamChange(final List<int>? typesettingParam) async {
      context.read<CanvasStore>().textController.updateTextElements({
        element.id: TextElementUpdater(
          typesettingParam: typesettingParam,
        )
      });
    }

    ///当前转换值
    final bindElementValue = context.select<CanvasStore, String?>(
        (final v) => v.bindElementValue(element.id));
    final bindElementUseTitle = context.select<CanvasStore, bool>(
        (final v) => v.bindElementUseTitle(element.id));

    ///切换是否绑定列名
    void onChangeShowCurrentColumnChange(final bool check) {
      TemplateDataSourceModify templateDataSourceModify =
          TemplateDataSourceModify(
        useTitle: check,
      );
      final canvasData = context.read<CanvasStore>().canvasData;
      final elementVal = element.value ??
          context.read<CanvasStore>().getElementValue(element.id)?.value;
      if (elementVal == null) return;
      context.read<CanvasStore>().dataSourceController.updateTemplateModify(
          canvasData, element.id, elementVal, templateDataSourceModify);
    }

    ///切换绑定元素
    void onGenerateTypeChange(final ExcelSourceType type) {
      final focusedElement = context.read<CanvasStore>().focusedElement;
      final newElement = CanvasBindElementChangeTypeUtils.changeBindType(
          focusedElement ?? element, type);
      if (newElement != null) {
        TemplateDataSourceModify templateDataSourceModify =
            TemplateDataSourceModify(
          useTitle: false,
        );
        final canvasData = context.read<CanvasStore>().canvasData;
        final elementVal = element.value ??
            context.read<CanvasStore>().getElementValue(element.id)?.value;
        if (elementVal == null) return;
        context.read<CanvasStore>().dataSourceController.updateTemplateModify(
            canvasData, element.id, elementVal, templateDataSourceModify);
        context.read<CanvasStore>().replaceElement(element, newElement);
      }
    }

    return SingleChildScrollView(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Padding(
              padding: const EdgeInsets.only(left: 20, right: 22),
              child: (element.dataBind?.isNotEmpty ?? false)
                  ? DataBindContentAttributeWidget(
                      value: bindElementValue,
                      generateType: ExcelSourceType.text,
                      onGenerateTypeChange: onGenerateTypeChange,
                      printColumnNames: bindElementUseTitle,
                      onPrintColumnNames: onChangeShowCurrentColumnChange,
                      showColumn: true,
                    )
                  : TextContentAttributeWidget(
                      value: element.value,
                      onValueChange: onTextValueChange,
                    )),
          const SizedBox(height: 10),
          Divider(color: themeColors.dividerColor, height: 1),
          const SizedBox(height: 14),
          Padding(
            padding: const EdgeInsets.only(left: 20, right: 22, bottom: 12),
            child: TextStyleAttributeWidget(
              fontCode: element.fontCode,
              fontFamily: element.fontFamily,
              onFontFamilyChange: onTextChangeFontFamily,
              fontSize: element.fontSize,
              onFontSizeChange: onTextFontSizeChange,
              textAlign: !isVertical
                  ? element.textAlignHorizontal
                  : element.textAlignVertical,
              onTextAlignChange: onTextAlignChange,
              fontStyle: element.fontStyle,
              onFontStyleChange: onTextFontStyleChange,
              letterSpacing: element.letterSpacing,
              onLetterSpacingChange: onTextLetterSpacingChange,
              lineSpacing: element.lineSpacing,
              onLineSpacingChange: onTextLineSpacingChange,
              typesettingMode: element.textTypesettingMode,
              onTypesettingModeChange: onTextNetalTypesettingModeChange,
              typesettingParam: element.typesettingParam,
              onTypesettingParamChange: onTextTypesettingParamChange,
              colorChannel: element.colorChannel,
              elementColor: element.elementColor,
              onColorChannelChange: onTextColorChange,
              colorReverse: element.colorReverse,
              onColorReverseChange: onTextColorWhiteChange,
              lineBreakMode: element.lineBreakMode,
              onLineBreakModeChange: onTextNetalTextLineBreakModeChange,
            ),
          ),
        ],
      ),
    );
  }
}
