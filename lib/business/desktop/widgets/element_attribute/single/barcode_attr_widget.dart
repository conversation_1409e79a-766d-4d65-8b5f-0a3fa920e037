import 'package:collection/collection.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';

import 'package:netal_plugin/models/netal_enum.dart';
import 'package:netal_plugin/utils/ratio.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_template/models/elements/bar_code_element.dart';
import 'package:niimbot_template/models/template/template_data_source_modify.dart';
import 'package:niimbot_ui/icons/icons.dart';
import 'package:niimbot_ui/model/niimbot_dropdown_model.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:niimbot_ui/widgets/niimbot_toast/niimbot_toast.dart';
import 'package:niimbot_ui/widgets/niimbot_toast/niimbot_toast_controller.dart';
import 'package:provider/provider.dart';

import 'package:niimbot_flutter_canvas/business/desktop/models/canvas_element_text_content_choose_model.dart';
import 'package:niimbot_flutter_canvas/business/desktop/models/index.dart';
import 'package:niimbot_flutter_canvas/business/desktop/utils/canvas_bind_element_change_utils.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/select_contain_title_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/content/barcode_content_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/content/data_bind_content_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/style/barcode_style_attr_widget.dart';
import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_flutter_canvas/provider/models/bar_code_element_updater.dart';
import 'package:niimbot_flutter_canvas/utils/input_utils.dart';

final Logger _logger = Logger("BarcodeAttributeWidget", on: kDebugMode);

/// 一维码元素属性组件
class BarcodeAttributeWidget extends StatelessWidget {
  final BarCodeElement element;

  const BarcodeAttributeWidget({
    super.key,
    required this.element,
  });

  @override
  Widget build(final BuildContext context) {
    final themeColors = NiimbotTheme.of(context).colors;

    ///一维码文本内容
    onBarCodeValueChange(final String val) {
      context
          .read<CanvasStore>()
          .barcodeController
          .updateBarCodes({element.id: BarCodeElementUpdater(value: val)});
    }

    ///一维码编码格式
    onBarCodeNetalTypeValueChange(final NetalBarcodeType val) {
      bool notSupportOnBottom = [21, 22, 23, 24].contains(val.value);
      NetalBarcodeTextPosition textPosition = element.textPosition;
      if (notSupportOnBottom &&
          element.textPosition != NetalBarcodeTextPosition.bottom) {
        textPosition = NetalBarcodeTextPosition.bottom;
      }
      context.read<CanvasStore>().barcodeController.updateBarCodes({
        element.id:
            BarCodeElementUpdater(codeType: val, textPosition: textPosition)
      });
    }

    ///一维码字体
    onBarCodeFontSizeValueChange(final num val) {
      //一维码文字宽或者高超出一维码限制判断
      final configFontSize = val.mm2px().toDouble();
      final elementVal = element.value ??
          context.read<CanvasStore>().getElementValue(element.id)?.value;
      // if (elementVal == null) return;
      Size textSize = InputUtils.boundingTextSize(
          elementVal??'', TextStyle(fontSize: configFontSize),
          maxLines: 1);
      if ((textSize.width + 1) > element.width.mm2px() ||
          (configFontSize + 3.mm2px()) >= element.height.mm2px()) {
        // LoadingMix.showToast(NiimbotIntl.getIntlMessage("app01065", '请拉伸条码'));
        NiimbotToastController().show(
          NiimbotIntl.getIntlMessage("app01065", '请拉伸条码'),
          type: ToastType.warning,
          icon: NiimbotIcons.warning(
            color: NiimbotTheme.of(context).colors.warningColorNormal,
            size: 20.00,
          ),
        );
        // setState(() {
        //   //刷新加减控件初始值
        // });
        return;
      }
      context.read<CanvasStore>().barcodeController.updateBarCodes({
        element.id: BarCodeElementUpdater(fontSize: val, textHeight: val + 0.2)
      });
    }

    ///一维码颜色
    onBarCodeColorChange(final int value, final Color color) {
      context.read<CanvasStore>().barcodeController.updateBarCodes({
        element.id: BarCodeElementUpdater(
          colorChannel: value,
          elementColor: color,
        )
      });
    }

    ///一维码样式
    onBarCodeTextPositionChange(final NetalBarcodeTextPosition textPosition) {
      context.read<CanvasStore>().barcodeController.updateBarCodes(
          {element.id: BarCodeElementUpdater(textPosition: textPosition)});
    }

    ///当前转换值
    final bindElementValue = context.select<CanvasStore, String?>(
        (final v) => v.bindElementValue(element.id));
    final bindElementUseTitle = context.select<CanvasStore, bool>(
        (final v) => v.bindElementUseTitle(element.id));

    ///切换是否绑定列名
    void onChangeShowCurrentColumnChange(final bool check) {
      final canvasData = context.read<CanvasStore>().canvasData;
      TemplateDataSourceModify templateDataSourceModify =
          TemplateDataSourceModify(
        useTitle: check,
      );
      final elementVal = element.value ??
          context.read<CanvasStore>().getElementValue(element.id)?.value;
      if (elementVal == null) return;
      context.read<CanvasStore>().dataSourceController.updateTemplateModify(
          canvasData, element.id, elementVal, templateDataSourceModify);
    }

    ///切换绑定元素
    void onGenerateTypeChange(final ExcelSourceType type) {
      final focusedElement = context.read<CanvasStore>().focusedElement;
      final newElement = CanvasBindElementChangeTypeUtils.changeBindType(
          focusedElement ?? element, type);
      if (newElement != null) {
        context.read<CanvasStore>().replaceElement(element, newElement);
      }
    }

    return SingleChildScrollView(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 20, right: 22),
            child: (element.dataBind?.isNotEmpty ?? false)
                ? Column(
                    children: [
                      DataBindContentAttributeWidget(
                        value: bindElementValue,
                        generateType: ExcelSourceType.barcode,
                        onGenerateTypeChange: onGenerateTypeChange,
                        printColumnNames: bindElementUseTitle,
                        onPrintColumnNames: onChangeShowCurrentColumnChange,
                      ),
                      const SizedBox(height: 8),
                      SelectContainTitleWidget(
                        leftTitle:
                            NiimbotIntl.getIntlMessage('app100000762', '编码格式'),
                        title: CanvasElementTextContentChooseModel
                                    .barCodeStyleList()
                                .firstWhereOrNull(
                                    (final e) => e.value == element.codeType)
                                ?.label ??
                            '',
                        isDropDown: true,
                        value: element.codeType,
                        dataList: CanvasElementTextContentChooseModel
                            .barCodeStyleList(),
                        onTapSelect: (final NiimbotDropDownModel? model) {
                          if (model != null) {
                            onBarCodeNetalTypeValueChange.call(
                                NetalBarcodeType.byValue(
                                    int.parse(model.value.value.toString()))!);
                          }
                        },
                      ),
                    ],
                  )
                : BarCodeContentWidget(
                    value: element.value,
                    onValueChange: onBarCodeValueChange,
                    type: element.codeType,
                    onTypeChange: onBarCodeNetalTypeValueChange,
                  ),
          ),
          const SizedBox(height: 24),
          Divider(color: themeColors.dividerColor, height: 1),
          const SizedBox(height: 12),
          Padding(
            padding: const EdgeInsets.only(left: 20, right: 22, bottom: 12),
            child: BarCodeStyleAttributeWidget(
              type: element.codeType,
              textPosition: element.textPosition,
              onTextPositionChange: onBarCodeTextPositionChange,
              fontSize: element.fontSize,
              onFontSizeChange: onBarCodeFontSizeValueChange,
              colorChannel: element.colorChannel,
              onColorChannelChange: onBarCodeColorChange,
              elementColor: element.elementColor,
            ),
          ),
        ],
      ),
    );
  }
}
