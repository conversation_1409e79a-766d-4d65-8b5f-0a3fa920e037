import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:niimbot_template/models/elements/bar_code_element.dart';
import 'package:niimbot_template/models/elements/base_element.dart';
import 'package:niimbot_template/models/elements/date_element.dart';
import 'package:niimbot_template/models/elements/graph_element.dart';
import 'package:niimbot_template/models/elements/image_element.dart';
import 'package:niimbot_template/models/elements/line_element.dart';
import 'package:niimbot_template/models/elements/material_element.dart';
import 'package:niimbot_template/models/elements/qr_code_element.dart';
import 'package:niimbot_template/models/elements/serial_element.dart';
import 'package:niimbot_template/models/elements/table_element.dart';
import 'package:niimbot_template/models/elements/text_element.dart';
import 'package:provider/provider.dart';
import 'package:should_rebuild/should_rebuild.dart' as sr;

import 'package:niimbot_flutter_canvas/model/element/qr_code_element.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/single/material_attr_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/single/barcode_attr_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/single/date_attr_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/single/graph_attr_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/single/image_attr_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/single/line_attr_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/single/qrcode_attr_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/single/serial_attr_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/single/table_attr_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/single/text_attr_widget.dart';
import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_flutter_canvas/extensions/base_element.dart';

final Logger _logger = Logger("SingleElementAttributeWidget", on: kDebugMode);

class SingleElementAttributeWidget extends StatelessWidget {
  const SingleElementAttributeWidget({super.key});

  @override
  Widget build(final BuildContext context) {
    final element = context
        .select<CanvasStore, BaseElement?>((final v) => v.focusedElement);
    if (element == null) return const SizedBox.shrink();
    return sr.ShouldRebuild(
      shouldRebuild: (final o, final n) {
        final keys = o.element.diffKeys(n.element);
        final ignore = [
          'x',
          'y',
          'width',
          if (n.element is! LineElement) 'height'
        ];
        keys.removeWhere((final v) => ignore.contains(v));
        if (keys.isEmpty && n.element is ImageElement) {
          return (n.element as ImageElement).allowFreeZoom !=
              (o.element as ImageElement).allowFreeZoom;
        }
        return keys.isNotEmpty;
      },
      child: _SingleElementAttributeWidget(element: element),
    );
  }
}

class _SingleElementAttributeWidget extends StatelessWidget {
  final BaseElement element;

  const _SingleElementAttributeWidget({super.key, required this.element});

  @override
  Widget build(final BuildContext context) {
    _logger.log(element.type, '聚焦元素类型');
    switch (element.type) {
      case NetalElementType.text:
        return TextAttributeWidget(
          key: Key('text_attr_${element.id}'),
          element: element as TextElement,
        );
      case NetalElementType.barcode:
        return BarcodeAttributeWidget(
            key: Key('barcode_attr_${element.id}'),
            element: element as BarCodeElement);
      case NetalElementType.qrcode:
        final qrcode = element is CanvasQRCodeElement
            ? element as CanvasQRCodeElement
            : CanvasQRCodeElement.fromQRCodeElement(element as QRCodeElement);
        return QRCodeAttributeWidget(
            key: Key('qrCode_attr_${element.id}'), element: qrcode);
      case NetalElementType.date:
        return DateAttributeWidget(
            key: Key('date_attr_${element.id}'),
            element: element as DateElement);
      case NetalElementType.line:
        return LineAttributeWidget(
            key: Key('line_attr_${element.id}'),
            element: element as LineElement);
      case NetalElementType.image:
        if (element is MaterialElement) {
          return MaterialAttributeWidget(
              key: Key('material_attr_${element.id}'),
              element: element as MaterialElement);
        } else if (element is ImageElement) {
          return ImageAttributeWidget(
              key: Key('image_attr_${element.id}'),
              element: element as ImageElement);
        } else {
          return const SizedBox.shrink();
        }
      case NetalElementType.serial:
        return SerialAttrWidget(
            key: Key('serial_attr_${element.id}'),
            element: element as SerialElement);
      case NetalElementType.graph:
        return GraphAttributeWidget(
            key: Key('graph_attr_${element.id}'),
            element: element as GraphElement);
      case NetalElementType.table:
        return TableAttrWidget(
            key: Key('table_attr_${element.id}'),
            element: element as TableElement);
      default:
        return const SizedBox.shrink();
    }
  }
}
