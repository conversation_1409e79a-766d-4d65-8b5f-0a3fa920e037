import 'package:flutter/material.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/style/image_style_attr_widget.dart';
import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_flutter_canvas/provider/models/image_element_updater.dart';
import 'package:niimbot_flutter_canvas/utils/buried_utils.dart';
import 'package:niimbot_template/models/elements/image_element.dart';
import 'package:provider/provider.dart';

class ImageAttributeWidget extends StatelessWidget {
  final ImageElement element;

  const ImageAttributeWidget({required this.element, super.key});

  @override
  Widget build(final BuildContext context) {
    ///颜色
    onImageColorChange(final int value, final Color color) {
      context.read<CanvasStore>().imageController.updateImageElements({
        element.id: ImageElementUpdater(
          colorChannel: value,
          elementColor: color,
        )
      });
    }

    ///等比拉伸
    onImageAllowFreeZoomChange(final bool allowFreeZoom) {
      final fileDataSources =
          context.read<CanvasStore>().canvasData.fileDataSources;
      final isHaveMultipleImage = fileDataSources?.any(
              (final e) => e.type == 'image' && e.pageImages.length > 1) ??
          false;
      if (isHaveMultipleImage) {
        BuriedUtils().track('click', '007_071_093',
            ext: {'state': allowFreeZoom ? 1 : 2});
      }
      context.read<CanvasStore>().imageController.updateImageElements({
        element.id: ImageElementUpdater(
          allowFreeZoom: !allowFreeZoom,
        )
      });
    }

    ///浓度深浅
    onImageProcessingValueChange(final List<num> imageProcessingValue) {
      context
          .read<CanvasStore>()
          .imageController
          .updateImageElementImageProcessingValue({
        element.id:
            ImageElementUpdater(imageProcessingValue: imageProcessingValue)
      });
    }

    ///灰阶打印   /// 5灰 127正常
    onImageProcessingTypeChange(
        final NetalImageRenderType imageProcessingType) {
      context.read<CanvasStore>().imageController.updateImageElements({
        element.id: ImageElementUpdater(
            imageProcessingType: imageProcessingType,
            imageProcessingValue: [
              imageProcessingType == NetalImageRenderType.grayscale ? 5 : 127
            ])
      });
    }

    return SingleChildScrollView(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(20, 10, 22, 12),
            child: ImageStyleAttributeWidget(
              element: element,
              processingValue: element.imageProcessingValue,
              allowFreeZoom: element.allowFreeZoom,
              colorChannel: element.colorChannel,
              elementColor: element.elementColor,
              imageProcessingType: element.imageProcessingType,
              onImageRenderTypeChange: onImageProcessingTypeChange,
              onProcessingValueChange: onImageProcessingValueChange,
              onAllowFreeZoomChange: onImageAllowFreeZoomChange,
              onColorChannelChange: onImageColorChange,
            ),
          ),
        ],
      ),
    );
  }
}
