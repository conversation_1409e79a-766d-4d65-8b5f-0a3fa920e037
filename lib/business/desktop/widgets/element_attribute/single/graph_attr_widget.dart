import 'package:flutter/material.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:niimbot_template/models/elements/graph_element.dart';
import 'package:provider/provider.dart';

import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/style/graph_style_attr_common_widget.dart';
import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_flutter_canvas/provider/models/graph_element_updater.dart';

class GraphAttributeWidget extends StatelessWidget {
  final GraphElement element;

  const GraphAttributeWidget({required this.element, super.key});

  @override
  Widget build(final BuildContext context) {
    /// 形状 todo 切回圆形暂时高等于宽
    onGraphTypeChange(final NetalGraphType? graphType) {
      num width = element.width;
      num height;
      if (graphType == NetalGraphType.round) {
        height = width;
      } else if (graphType == NetalGraphType.ellipse) {
        height = width * 2 / 3;
      } else {
        height = element.height;
      }
      context.read<CanvasStore>().lineController.updateGraphElements({
        element.id: GraphElementUpdater(
            graphType: graphType, width: width, height: height)
      });
    }

    /// 颜色
    onGraphColorChange(final int value, final Color color) {
      context.read<CanvasStore>().lineController.updateGraphElements({
        element.id: GraphElementUpdater(
          colorChannel: value,
          elementColor: color,
        )
      });
    }

    /// 线粗
    onGraphLineHeightChange(final num lineWidth) {
      context.read<CanvasStore>().lineController.updateGraphElements(
          {element.id: GraphElementUpdater(lineWidth: lineWidth)});
    }

    ///实线虚线
    void onGraphLineTypeChange(final lineType) {
      context.read<CanvasStore>().lineController.updateGraphElements(
          {element.id: GraphElementUpdater(lineType: lineType)});
    }

    return SingleChildScrollView(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(20, 10, 22, 12),
            child: GraphStyleAttrCommonWidget(
              showTitle: false,
              lineType: element.lineType,
              lineWidth: element.lineWidth,
              colorChannel: element.colorChannel,
              onLineTypeChange: onGraphLineTypeChange,
              onColorChannelChange: onGraphColorChange,
              onLineWidthChange: onGraphLineHeightChange,
              value: element.graphType,
              valueChanged: onGraphTypeChange,
              elementColor: element.elementColor,
            ),
          ),
        ],
      ),
    );
  }
}
