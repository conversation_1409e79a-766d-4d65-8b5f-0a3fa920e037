import 'package:flutter/material.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:niimbot_template/models/elements/material_element.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:provider/provider.dart';


import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_flutter_canvas/provider/models/image_element_updater.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/content/material_content_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/style/material_style_attr_widget.dart';


class MaterialAttributeWidget extends StatelessWidget {
  final MaterialElement element;

  const MaterialAttributeWidget({required this.element, super.key});

  @override
  Widget build(final BuildContext context) {
    ///颜色
    onImageColorChange(final int value, final Color color) {
      context.read<CanvasStore>().imageController.updateImageElements({
        element.id: ImageElementUpdater(
          colorChannel: value,
          elementColor: color,
        )
      });
    }

    ///等比拉伸
    onImageAllowFreeZoomChange(final bool allowFreeZoom) {
      context.read<CanvasStore>().imageController.updateImageElements(
          {element.id: ImageElementUpdater(allowFreeZoom: !allowFreeZoom)});
    }

    ///浓度深浅
    onImageProcessingValueChange(final List<num> imageProcessingValue) {
      context
          .read<CanvasStore>()
          .imageController
          .updateImageElementImageProcessingValue({
        element.id: ImageElementUpdater(
            imageProcessingValue: imageProcessingValue,
            imageProcessingType: NetalImageRenderType.threshold)
      });
    }

    onImageColorWhiteChange(final bool value) {
      context.read<CanvasStore>().imageController.updateImageElements(
          {element.id: ImageElementUpdater(colorReverse: value)});
    }

    final themeColors = NiimbotTheme.of(context).colors;
    return SingleChildScrollView(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 20, right: 22),
            child: MaterialContentWidget(
              element: element,
            ),
          ),
          const SizedBox(height: 29),
          Divider(color: themeColors.dividerColor, height: 1),
          Padding(
            padding: const EdgeInsets.fromLTRB(20, 12, 22, 12),
            child: MaterialStyleAttrWidget(
              element: element,
              onImageProcessingValue: onImageProcessingValueChange,
              onImageAllowFreeZoomChange: onImageAllowFreeZoomChange,
              onImageColorChange: onImageColorChange,
              onColorReverseChange: onImageColorWhiteChange,
            ),
          ),
        ],
      ),
    );
  }
}
