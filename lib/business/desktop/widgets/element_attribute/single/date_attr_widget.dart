import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/shared/canvas_font_data.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:niimbot_template/models/elements/date_element.dart';
import 'package:niimbot_template/models/elements/element_enum.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:provider/provider.dart';

import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/content/date_content_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/style/text_style_attr_widget.dart';
import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_flutter_canvas/provider/models/date_element_updater.dart';

///时间元素属性组件
class DateAttributeWidget extends StatelessWidget {
  final DateElement element;

  const DateAttributeWidget({super.key, required this.element});

  @override
  Widget build(final BuildContext context) {
    final themeColors = NiimbotTheme.of(context).colors;

    bool isVertical = element.typesettingMode == NetalTypesettingMode.vertical;

    ///调整字体 fontFamily
    onDateChangeFontFamily(final CanvasFontData fontItem) {
      context.read<CanvasStore>().textController.updateDateByAssociateId(
          element.id,
          element.associateId,
          DateElementUpdater(
            fontFamily: fontItem.code,
            fontCode: fontItem.code,
            hasVipRes: fontItem.isVip,
          ));
    }

    ///调整字体大小fontSize
    onDateFontSizeChange(final num val) {
      context.read<CanvasStore>().textController.updateDateByAssociateId(
          element.id, element.associateId, DateElementUpdater(fontSize: val));
    }

    ///文本对齐方式
    onDateAlignChange(final NetalTextAlign val) {
      if (!isVertical) {
        context.read<CanvasStore>().textController.updateDateByAssociateId(
            element.id,
            element.associateId,
            DateElementUpdater(horizontalAlign: val));
      } else {
        context.read<CanvasStore>().textController.updateDateByAssociateId(
            element.id,
            element.associateId,
            DateElementUpdater(verticalAlign: val));
      }
    }

    ///粗体、下划线、斜体 fontStyle
    onDateFontStyleChange(final List<NetalTextFontStyle> fontStyle) {
      context.read<CanvasStore>().textController.updateDateByAssociateId(
          element.id,
          element.associateId,
          DateElementUpdater(fontStyle: fontStyle));
    }

    ///字距
    onDateLetterSpacingChange(final num val) {
      context
          .read<CanvasStore>()
          .textController
          .updateTextLetterSpacing(val, ids: [element.id]);
    }

    ///行距
    onDateLineSpacingChange(final num val) {
      context.read<CanvasStore>().textController.updateDateByAssociateId(
          element.id,
          element.associateId,
          DateElementUpdater(lineSpacing: val));
    }

    ///文本方向
    onDateNetalTypesettingModeChange(final TextTypesettingMode mode) {
      context
          .read<CanvasStore>()
          .textController
          .updateTextTypesettingMode([element.id], mode);
    }

    ///是否反白
    onDateColorWhiteChange(final bool value) {
      context.read<CanvasStore>().textController.updateDateByAssociateId(
          element.id,
          element.associateId,
          DateElementUpdater(colorReverse: value));
    }

    /// 按单词换行
    onTextNetalTextLineBreakModeChange(final NetalTextLineBreakMode val) {
      context.read<CanvasStore>().textController.updateDateByAssociateId(
          element.id,
          element.associateId,
          DateElementUpdater(lineBreakMode: val));
    }

    ///颜色
    onDateColorChange(final int value, final Color color) {
      context.read<CanvasStore>().textController.updateDateByAssociateId(
          element.id,
          element.associateId,
          DateElementUpdater(
            colorChannel: value,
            elementColor: color,
          ));
    }

    ///文本弧度
    onDateTypesettingParamChange(final List<int>? typesettingParam) {
      context.read<CanvasStore>().textController.updateDateByAssociateId(
          element.id,
          element.associateId,
          DateElementUpdater(
            typesettingParam: typesettingParam,
          ));
    }

    return SingleChildScrollView(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 20, right: 22),
            child: DateContentWidget(
              element: element,
            ),
          ),
          const SizedBox(height: 20),
          Divider(color: themeColors.dividerColor, height: 1),
          const SizedBox(height: 10),
          Padding(
            padding: const EdgeInsets.only(left: 20, right: 22, bottom: 12),
            child: TextStyleAttributeWidget(
              // showWorkBreak: false,
              fontCode: element.fontCode,
              fontFamily: element.fontFamily,
              onFontFamilyChange: onDateChangeFontFamily,
              fontSize: element.fontSize,
              onFontSizeChange: onDateFontSizeChange,
              textAlign: !isVertical
                  ? element.textAlignHorizontal
                  : element.textAlignVertical,
              onTextAlignChange: onDateAlignChange,
              fontStyle: element.fontStyle,
              onFontStyleChange: onDateFontStyleChange,
              letterSpacing: element.letterSpacing,
              onLetterSpacingChange: onDateLetterSpacingChange,
              lineSpacing: element.lineSpacing,
              onLineSpacingChange: onDateLineSpacingChange,
              typesettingMode: element.textTypesettingMode,
              onTypesettingModeChange: onDateNetalTypesettingModeChange,
              typesettingParam: element.typesettingParam,
              onTypesettingParamChange: onDateTypesettingParamChange,
              colorChannel: element.colorChannel,
              onColorChannelChange: onDateColorChange,
              colorReverse: element.colorReverse,
              elementColor: element.elementColor,
              onColorReverseChange: onDateColorWhiteChange,
              lineBreakMode: element.lineBreakMode,
              onLineBreakModeChange: onTextNetalTextLineBreakModeChange,
            ),
          ),
        ],
      ),
    );
  }
}
