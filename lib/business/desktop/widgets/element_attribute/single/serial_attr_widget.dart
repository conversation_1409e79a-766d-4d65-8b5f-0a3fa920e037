import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/shared/canvas_font_data.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:niimbot_template/models/elements/element_enum.dart';
import 'package:niimbot_template/models/elements/serial_element.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:provider/provider.dart';

import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/content/serial_content_widget.dart';
import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_flutter_canvas/provider/models/serial_element_updater.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/style/text_style_attr_widget.dart';

class SerialAttrWidget extends StatelessWidget {
  final SerialElement element;

  const SerialAttrWidget({super.key, required this.element});

  @override
  Widget build(final BuildContext context) {
    final themeColors = NiimbotTheme.of(context).colors;
    bool isVertical = element.typesettingMode == NetalTypesettingMode.vertical;

    ///调整字体 fontFamily
    onSerialChangeFontFamily(final CanvasFontData fontItem) {
      context.read<CanvasStore>().textController.updateTextElements({
        element.id: SerialElementUpdater(
            fontFamily: fontItem.code,
            fontCode: fontItem.code,
            hasVipRes: fontItem.isVip)
      });
    }

    ///调整字体大小fontSize
    onSerialFontSizeChange(final num val) {
      context.read<CanvasStore>().textController.updateTextElements(
          {element.id: SerialElementUpdater(fontSize: val)});
    }

    ///文本对齐方式
    onSerialAlignChange(final NetalTextAlign val) {
      if (!isVertical) {
        context.read<CanvasStore>().textController.updateTextElements(
            {element.id: SerialElementUpdater(horizontalAlign: val)});
      } else {
        context.read<CanvasStore>().textController.updateTextElements(
            {element.id: SerialElementUpdater(verticalAlign: val)});
      }
    }

    ///粗体、下划线、斜体 fontStyle
    onSerialFontStyleChange(final List<NetalTextFontStyle> fontStyle) {
      context.read<CanvasStore>().textController.updateTextElements(
          {element.id: SerialElementUpdater(fontStyle: fontStyle)});
    }

    ///字距
    onSerialLetterSpacingChange(final num val) {
      context
          .read<CanvasStore>()
          .textController
          .updateTextLetterSpacing(val, ids: [element.id]);
    }

    ///行距
    onSerialLineSpacingChange(final num val) {
      context.read<CanvasStore>().textController.updateTextElements(
          {element.id: SerialElementUpdater(lineSpacing: val)});
    }

    ///文本方向
    onSerialNetalTypesettingModeChange(final TextTypesettingMode mode) {
      context
          .read<CanvasStore>()
          .textController
          .updateTextTypesettingMode([element.id], mode);
    }

    ///是否反白
    onSerialColorWhiteChange(final bool value) {
      context.read<CanvasStore>().textController.updateTextElements(
          {element.id: SerialElementUpdater(colorReverse: value)});
    }

    /// 按单词换行
    onSerialNetalTextLineBreakModeChange(final NetalTextLineBreakMode val) {
      context.read<CanvasStore>().textController.updateTextElements(
          {element.id: SerialElementUpdater(lineBreakMode: val)});
    }

    ///颜色
    onSerialColorChange(final int value, final Color color) {
      context.read<CanvasStore>().textController.updateTextElements({
        element.id: SerialElementUpdater(
          colorChannel: value,
          elementColor: color,
        )
      });
    }

    ///文本弧度
    onSerialTypesettingParamChange(final List<int>? typesettingParam) {
      context
          .read<CanvasStore>()
          .textController
          .updateTextElements({
        element.id: SerialElementUpdater(
          typesettingParam: typesettingParam,
        )
      });
    }

    return SingleChildScrollView(
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 20, right: 22),
            child: SerialContentWidget(element: element),
          ),
          const SizedBox(height: 8),
          Divider(color: themeColors.dividerColor, height: 1),
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.only(left: 20, right: 22, bottom: 12),
            child: TextStyleAttributeWidget(
              // showWorkBreak: false,
              fontCode: element.fontCode,
              fontFamily: element.fontFamily,
              onFontFamilyChange: onSerialChangeFontFamily,
              fontSize: element.fontSize,
              onFontSizeChange: onSerialFontSizeChange,
              textAlign: !isVertical
                  ? element.textAlignHorizontal
                  : element.textAlignVertical,
              // textAlign: element.textAlignHorizontal,
              onTextAlignChange: onSerialAlignChange,
              fontStyle: element.fontStyle,
              onFontStyleChange: onSerialFontStyleChange,
              letterSpacing: element.letterSpacing,
              onLetterSpacingChange: onSerialLetterSpacingChange,
              lineSpacing: element.lineSpacing,
              onLineSpacingChange: onSerialLineSpacingChange,
              typesettingMode: element.textTypesettingMode,
              onTypesettingModeChange: onSerialNetalTypesettingModeChange,
              typesettingParam: element.typesettingParam,
              onTypesettingParamChange: onSerialTypesettingParamChange,
              colorChannel: element.colorChannel,
              elementColor: element.elementColor,
              onColorChannelChange: onSerialColorChange,
              colorReverse: element.colorReverse,
              onColorReverseChange: onSerialColorWhiteChange,
              lineBreakMode: element.lineBreakMode,
              onLineBreakModeChange: onSerialNetalTextLineBreakModeChange,
            ),
          ),
        ],
      ),
    );
  }
}
