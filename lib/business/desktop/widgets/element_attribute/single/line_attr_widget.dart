import 'package:flutter/material.dart';
import 'package:niimbot_template/models/elements/line_element.dart';
import 'package:provider/provider.dart';

import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/style/line_style_attr_widget.dart';
import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_flutter_canvas/provider/models/line_element_updater.dart';

class LineAttributeWidget extends StatelessWidget {
  final LineElement element;

  const LineAttributeWidget({required this.element, super.key});

  @override
  Widget build(final BuildContext context) {
    ///线条颜色
    onLineColorChange(final int value, final Color color) {
      context.read<CanvasStore>().lineController.updateLineElements({
        element.id: LineElementUpdater(
          colorChannel: value,
          elementColor: color,
        )
      });
    }

    ///线粗
    onLineHeightChange(final num height) {
      context
          .read<CanvasStore>()
          .lineController
          .updateLineWidth(height, ids: [element.id]);
    }

    ///实线虚线
    onLineTypeChange(final lineType) {
      context.read<CanvasStore>().lineController.updateLineElements(
          {element.id: LineElementUpdater(lineType: lineType)});
    }

    return SingleChildScrollView(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(20, 10, 22, 12),
            child: LineStyleAttributeWidget(
              lineType: element.lineType,
              lineWidth: element.height,
              colorChannel: element.colorChannel,
              onLineTypeChange: onLineTypeChange,
              onColorChannelChange: onLineColorChange,
              onLineWidthChange: onLineHeightChange,
              elementColor: element.elementColor,
            ),
          ),
        ],
      ),
    );
  }
}
