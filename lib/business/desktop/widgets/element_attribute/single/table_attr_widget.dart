import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/shared/canvas_font_data.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:niimbot_flutter_canvas/hooks/use_table_element_state.dart';
import 'package:niimbot_template/models/elements/element_enum.dart';
import 'package:niimbot_template/models/elements/table_element.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:provider/provider.dart';

import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/content/text_content_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/style/table_style_attr_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/style/text_style_attr_widget.dart';
import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_flutter_canvas/provider/models/table_cell_element_updater.dart';
import 'package:niimbot_flutter_canvas/provider/models/table_element_updater.dart';

// 表格元素
class TableAttrWidget extends HookWidget {
  final TableElement element;

  const TableAttrWidget({
    required this.element,
    super.key,
  });

  @override
  Widget build(final BuildContext context) {
    final themeColors = NiimbotTheme.of(context).colors;
    final focusedCells = useSelectedCells(element);

    /// 单元格内容
    final cellData = useSelectedCellStyleAttr(element);

    /// 更新表格内容
    onCellValueChange(final String? val) {
      context.read<CanvasStore>().tableController.updateTableCellElements(
          element.id,
          {focusedCells.first.id: TableCellElementUpdater(value: val)});
    }

    /// 表格样式
    onRowChange(final num val) {
      context
          .read<CanvasStore>()
          .tableController
          .updateTableElementsRow(element.id, val.toInt());
    }

    onColumnChange(final num val) {
      context
          .read<CanvasStore>()
          .tableController
          .updateTableElementsColumn(element.id, val.toInt());
    }

    onLineWidthChange(final num val) {
      context.read<CanvasStore>().tableController.updateTableElements(
          {element.id: TableElementUpdater(lineWidth: val.toDouble())});
    }

    onContentColorChannelChange(final int val, final Color color) {
      context.read<CanvasStore>().tableController.updateTableElements({
        element.id:
            TableElementUpdater(contentColorChannel: val, contentColor: color)
      });
    }

    onLineColorChannelChange(final int val, final Color color) {
      context.read<CanvasStore>().tableController.updateTableElements({
        element.id: TableElementUpdater(lineColorChannel: val, lineColor: color)
      });
    }

    /// 表格文本字体及样式
    onTextChangeFontFamily(final CanvasFontData fontItem) {
      context.read<CanvasStore>().tableController.updateTableCellElementsStyle(
            element.id,
            TableCellElementUpdater(
              fontFamily: fontItem.code,
              fontCode: fontItem.code,
              hasVipRes: fontItem.isVip,
            ),
          );
    }

    /// 字体大小
    onTextFontSizeChange(final num val) {
      context.read<CanvasStore>().tableController.updateTableCellElementsStyle(
          element.id, TableCellElementUpdater(fontSize: val));
    }

    ///文本对齐方式
    onTextAlignChange(final NetalTextAlign val) {
      context.read<CanvasStore>().tableController.updateTableCellElementsStyle(
          element.id, TableCellElementUpdater(textAlignHorizontal: val));
    }

    ///粗体、下划线、斜体 fontStyle
    onTextFontStyleChange(final List<NetalTextFontStyle> fontStyle) {
      final focusedElement = context.read<CanvasStore>().focusedElement;
      if (focusedElement is TableElement) {
        context
            .read<CanvasStore>()
            .tableController
            .updateTableCellElementsStyle(focusedElement.id,
                TableCellElementUpdater(fontStyle: fontStyle));
      }
    }

    ///文本字距
    onTextLetterSpacingChange(final num val) {
      context.read<CanvasStore>().tableController.updateTableCellElementsStyle(
          element.id, TableCellElementUpdater(letterSpacing: val));
    }

    ///文本行距
    onTextLineSpacingChange(final num val) {
      context.read<CanvasStore>().tableController.updateTableCellElementsStyle(
          element.id, TableCellElementUpdater(lineSpacing: val));
    }

    /// 按单词换行
    onTextNetalTextLineBreakModeChange(final NetalTextLineBreakMode val) {
      context.read<CanvasStore>().tableController.updateTableCellElementsStyle(
          element.id, TableCellElementUpdater(lineBreakMode: val));
    }

    return SingleChildScrollView(
      child: Column(
        children: [
          if (focusedCells.length == 1)
            Column(
              children: [
                Padding(
                  padding: const EdgeInsets.only(left: 20, right: 22),
                  child: TextContentAttributeWidget(
                    value: focusedCells.first.value,
                    onValueChange: onCellValueChange,
                  ),
                ),
                const SizedBox(height: 8),
                Divider(color: themeColors.dividerColor, height: 1),
                const SizedBox(height: 8),
              ],
            ),
          Padding(
            padding: const EdgeInsets.only(left: 20, right: 22),
            child: TableStyleAttrWidget(
              row: element.row,
              onRowChange: onRowChange,
              column: element.column,
              onColumnChange: onColumnChange,
              lineWidth: element.lineWidth,
              onLineWidthChange: onLineWidthChange,
              contentColorChannel: element.contentColorChannel,
              onContentColorChannelChange: onContentColorChannelChange,
              lineColorChannel: element.lineColorChannel,
              onLineColorChannelChange: onLineColorChannelChange,
              contentColor: element.contentColor,
              lineColor: element.lineColor,
            ),
          ),
          const SizedBox(height: 18),
          Divider(color: themeColors.dividerColor, height: 1),
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.only(left: 20, right: 22, bottom: 12),
            child: TextStyleAttributeWidget(
              key:
                  Key('table_attr_widget_${element.id}_${focusedCells.length}'),
              showTypesettingMode: false,
              showColor: false,
              showColorReverse: false,
              fontCode: cellData?.fontCode,
              fontFamily: cellData?.fontFamily,
              onFontFamilyChange: onTextChangeFontFamily,
              fontSize: cellData?.fontSize,
              onFontSizeChange: onTextFontSizeChange,
              typesettingMode: TextTypesettingMode.Horizontal,
              textAlign: cellData?.textAlignHorizontal,
              onTextAlignChange: onTextAlignChange,
              fontStyle: cellData?.fontStyle,
              onFontStyleChange: onTextFontStyleChange,
              letterSpacing: cellData?.letterSpacing,
              onLetterSpacingChange: onTextLetterSpacingChange,
              lineSpacing: cellData?.lineSpacing,
              onLineSpacingChange: onTextLineSpacingChange,
              lineBreakMode: cellData?.lineBreakMode,
              onLineBreakModeChange: onTextNetalTextLineBreakModeChange,
            ),
          ),
        ],
      ),
    );
  }
}
