import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/multiple/multiple_element_attr_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/single/single_element_attr_widget.dart';
import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';

class ElementAttributePanelWidget extends StatelessWidget {
  const ElementAttributePanelWidget({super.key});

  @override
  Widget build(final BuildContext context) {
    final multiSelect =
        context.select<CanvasStore, bool>((final v) => v.multiSelect);
    if (multiSelect) {
      return const Padding(
        padding:
            EdgeInsets.only(left: 20.0, top: 8.0, right: 22.0, bottom: 12.0),
        child: MultipleElementAttributeWidget(),
      );
    }
    return const SingleElementAttributeWidget();
  }
}
