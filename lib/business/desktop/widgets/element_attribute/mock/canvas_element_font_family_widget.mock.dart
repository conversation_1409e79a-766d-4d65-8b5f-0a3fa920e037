import 'dart:async';
import 'package:flutter/material.dart';
import 'package:niimbot_ui/icons/icons.dart';
import 'package:niimbot_ui/model/niimbot_dropdown_model.dart';
import 'package:niimbot_ui/styles/theme.dart';


import 'package:niimbot_flutter_canvas/business/desktop/models/canvas_element_font_download_model.dart';

class CanvasCommonFontFamilyDownloadWidget extends StatefulWidget {
  final List<NiimbotDropDownModel> list;
  final String? currentValue;
  final Function(NiimbotDropDownModel) selectFunction;

  const CanvasCommonFontFamilyDownloadWidget({
    required this.list,
    this.currentValue,
    required this.selectFunction,
    super.key,
  });

  @override
  State<CanvasCommonFontFamilyDownloadWidget> createState() =>
      _CanvasCommonFontFamilyDownloadWidgetState();
}

class _CanvasCommonFontFamilyDownloadWidgetState
    extends State<CanvasCommonFontFamilyDownloadWidget> {
  @override
  Widget build(final BuildContext context) {
    return _popupBottomWidget();
  }

  SingleChildScrollView _popupBottomWidget() {
    List<Widget> widgetList = [];
    for (int i = 0; i < widget.list.length; i++) {
      widgetList.add(_singleSelectWidget(i));
    }
    return SingleChildScrollView(
      padding: const EdgeInsets.all(5.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: widgetList,
      ),
    );
  }

  int _mouseIndex = -1;
  int _tapIndex = -1;
  List hasDownloadFont = []; //'PingFang','Harmony'
  MouseRegion _singleSelectWidget(final int index) {
    String label = widget.list[index].label.toString();
    String value = widget.list[index].value.toString();
    bool? disabled = widget.list[index].disabled;
    bool isDisabled = disabled != null && disabled== true;
    bool downloading = false;
    double? progressValue = 0.0;
    bool needDownload = !hasDownloadFont.contains(value) && !isDisabled;
    if (downloadingList.map((final e) => e.value).toList().contains(value)) {
      downloading = true;
      progressValue = downloadingList
              .firstWhere((final element) => element.value == value)
              .process ??
          0.0;
    }
    Widget textWidget = Text(
      _backStr(label),
      overflow: TextOverflow.ellipsis,
      maxLines: 1,
      style: TextStyle(
          fontSize: 14, color: _fontColorHandle(value, index, isDisabled)),
    );

    return MouseRegion(
      cursor:
          !isDisabled ? SystemMouseCursors.click : SystemMouseCursors.noDrop,
      onEnter: (final event) {
        if (!isDisabled) {
          _mouseIndex = index;
          setState(() {});
        }
      },
      onExit: (final event) {
        if (!isDisabled) {
          _mouseIndex = -1;
          setState(() {});
        }
      },
      child: GestureDetector(
        onTapDown: (final TapDownDetails detail) {
          if (!isDisabled) {
            _tapIndex = index;
            setState(() {});
          }
        },
        onTapCancel: () {
          if (!isDisabled) {
            _tapIndex = -1;
            setState(() {});
          }
        },
        onTapUp: (final details) {
          if (!isDisabled) {
            _tapIndex = -1;
            setState(() {});
          }
        },
        onTap: () {
          if (!isDisabled) {
            Navigator.pop(context);
            widget.selectFunction(widget.list[index]);
          }
        },
        child: Container(
          width: 260,
          margin: const EdgeInsets.only(top: 3),
          padding: const EdgeInsets.symmetric(vertical: 6.0, horizontal: 6.0),
          decoration: BoxDecoration(
            borderRadius: const BorderRadius.all(Radius.circular(5.0)),
            color: _bgColorHandle(value, index),
          ),
          child: Row(
            children: [
              _isChoose(value)
                  ? SizedBox(
                      width: 16,
                      child: NiimbotIcons.check(
                          size: 16,
                          color: NiimbotTheme.of(context).colors.brandColor),
                    )
                  : const SizedBox(
                      width: 16,
                    ),
              Expanded(
                  child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 6.0),
                child: textWidget,
              )),
              needDownload
                  ? downloading
                      ? SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            backgroundColor: NiimbotTheme.of(context)
                                .colors
                                .brandColorDisabled,
                            valueColor: AlwaysStoppedAnimation(
                                NiimbotTheme.of(context).colors.brandColor),
                            value: progressValue,
                            strokeWidth: 2.0,
                          ),
                        )
                      : InkWell(
                          onTap: () {
                            _onDownload(widget.list[index].value ?? '');
                            // widget.downloadFunction(widget.list[index]);
                          },
                          child: NiimbotIcons.download(
                            color: NiimbotTheme.of(context)
                                .colors
                                .textFillColorSecondary,
                            size: 16,
                          ),
                        )
                  : Container(),
            ],
          ),
        ),
      ),
    );
  }

  List<CanvasElementFontDownloadModel> downloadingList = [];

  void _onDownload(final String val) async {
    if (downloadingList.map((final e) => e.value).toList().contains(val)) {
      return;
    }
    CanvasElementFontDownloadModel model =
        CanvasElementFontDownloadModel(value: val, process: 0.0);
    downloadingList.add(model);
    _downloadFontFamily(model.value ?? '');
  }

  ///模拟下载
  _downloadFontFamily(final String val) {
    Timer.periodic(const Duration(milliseconds: 300), (final timer) {
      CanvasElementFontDownloadModel model =
          downloadingList.firstWhere((final element) => val == element.value);
      model.process = (model.process! * 100 + 0.1 * 100) / 100;
      if (model.process! >= 1) {
        model.process = 1.0;
        downloadingList.remove(model);
        hasDownloadFont.add(model.value);
        timer.cancel();
      }
      setState(() {});
    });
  }

  _backStr(final String word) {
    if (word.isEmpty) {
      return word;
    }
    String breakword = '';
    for (var element in word.runes) {
      breakword += String.fromCharCode(element);
      breakword += '\u200B';
    }
    return breakword;
  }

  Color _bgColorHandle(final String value, final int index) {
    if (_isChoose(value)) {
      return NiimbotTheme.of(context).colors.translucentFillColorQuarternary;
    }
    if (_tapIndex == index) {
      return NiimbotTheme.of(context).colors.translucentFillColorTertiary;
    }
    if (_mouseIndex == index) {
      return NiimbotTheme.of(context).colors.translucentFillColorQuarternary;
    }

    return NiimbotTheme.of(context).colors.solidBackgroundFillColorBase;
  }

  Color _fontColorHandle(final String value, final int index, final bool disabled) {
    if (_isChoose(value)) {
      if (disabled) {
        return NiimbotTheme.of(context).colors.brandColorDisabled;
      } else {
        return NiimbotTheme.of(context).colors.brandColor;
      }
    }
    if (disabled) {
      return NiimbotTheme.of(context).colors.textFillColorSecondary;
    } else {
      return NiimbotTheme.of(context).colors.textFillColorPrimary;
    }
  }

  ///当前是否选择
  bool _isChoose(final String value) {
    if (widget.currentValue == null) {
      return false;
    }
    return widget.currentValue == value;
  }
}
