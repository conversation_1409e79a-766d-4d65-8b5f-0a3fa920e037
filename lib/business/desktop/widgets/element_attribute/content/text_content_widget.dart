import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';

import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/content_box_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/content_source_widget.dart';

class TextContentAttributeWidget extends StatelessWidget {
  final String? value;
  final ValueChanged<String?>? onValueChange;

  const TextContentAttributeWidget({
    super.key,
    this.value,
    this.onValueChange,
  });

  @override
  Widget build(final BuildContext context) {
    return ContentBoxWidget(
      children: [
        ContentSourceWidget(
          value: value,
          onValueChange: onValueChange,
        ),
      ],
    );
  }
}
