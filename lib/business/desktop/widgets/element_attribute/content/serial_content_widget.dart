import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:netal_plugin/models/copy_wrapper.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_template/models/elements/serial_element.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:niimbot_ui/widgets/niimbot_popover/niimbot_popover.dart';
import 'package:provider/provider.dart';

import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_flutter_canvas/provider/history/history_store.dart';
import 'package:niimbot_flutter_canvas/provider/models/serial_element_updater.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/content_box_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/font_height_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/input_widget.dart';

class SerialContentWidget extends StatelessWidget {
  final SerialElement element;

  const SerialContentWidget({super.key, required this.element});

  @override
  Widget build(final BuildContext context) {
    onChangePrefix(final String value) {
      context.read<CanvasStore>().textController.updateTextElements(
          {element.id: SerialElementUpdater(prefix: CopyWrapper.value(value))},
          type: SnapshotType.ignore);
    }

    onChangeSuffix(final String value) {
      context.read<CanvasStore>().textController.updateTextElements(
          {element.id: SerialElementUpdater(suffix: CopyWrapper.value(value))},
          type: SnapshotType.ignore);
    }

    onBlur(final String val) {
      context.read<CanvasStore>().textController.updateTextElements(
          {element.id: SerialElementUpdater(prefix: CopyWrapper.value(val))},
          type: SnapshotType.ignoreEnd);
    }

    onBlurSuffix(final String val) {
      context.read<CanvasStore>().textController.updateTextElements(
          {element.id: SerialElementUpdater(suffix: CopyWrapper.value(val))},
          type: SnapshotType.ignoreEnd);
    }

    onBlurStartNumber(final String value) {
      context.read<CanvasStore>().textController.updateTextElements({
        element.id:
            SerialElementUpdater(startNumber: value, fixLength: value.length)
      }, type: SnapshotType.ignoreEnd);
    }

    onChangeStartNumber(final String? value) {
      if (value != null) {
        context.read<CanvasStore>().textController.updateTextElements({
          element.id:
              SerialElementUpdater(startNumber: value, fixLength: value.length)
        }, type: SnapshotType.ignore);
      }
    }

    onChangeIncrementValue(final num value) {
      context.read<CanvasStore>().textController.updateTextElements(
          {element.id: SerialElementUpdater(incrementValue: value.toInt())});
    }

    return ContentBoxWidget(children: [
      InputWidget(
        leftTitle: NiimbotIntl.getIntlMessage('app00124', '前缀'),
        maxLength: 15,
        value: element.prefix ?? '',
        onChange: onChangePrefix,
        onBlur: onBlur,
      ),
      const SizedBox(
        height: 12,
      ),
      InputWidget(
        leftTitle: NiimbotIntl.getIntlMessage('app00127', '后缀'),
        maxLength: 15,
        value: element.suffix ?? '',
        onChange: onChangeSuffix,
        onBlur: onBlurSuffix,
      ),
      const SizedBox(height: 12),
      InputWidget(
        leftTitle: NiimbotIntl.getIntlMessage('app01069', '起始值'),
        maxLength: 8,
        inputFormatters: [
          /// 数字
          FilteringTextInputFormatter.allow(RegExp('[0-9]')),
        ],
        allowClear: false,
        clearValue: '0',
        value: element.startNumber.toString(),
        onChange: onChangeStartNumber,
        onBlur: onBlurStartNumber,
      ),
      const SizedBox(height: 12),
      FontHeightWidget(
        title: NiimbotIntl.getIntlMessage('app00509', '递增值'),
        stepper: 1,
        min: 0,
        max: 999,
        digit: 0,
        number: element.incrementValue,
        valueChanged: onChangeIncrementValue,
      ),
      const SizedBox(
        height: 8,
      ),
      Container(
        alignment: AlignmentDirectional.centerStart,
        child: Text(
            '${NiimbotIntl.getIntlMessage('app100000766', '预览示例')}：${element.buildValue(1)}、${element.buildValue(2)}、${element.buildValue(3)}...',
            style: NiimbotTheme.of(context).typography.hint?.copyWith(
                color: NiimbotTheme.of(context).colors.textFillColorSecondary)),
      ),
      const SizedBox(height: 16),
      Center(
        child: NiimbotPopover(
          trigger: NiimbotPopoverTrigger.hover,
          showArrow: true,
          position: NiimbotPopoverPositionEnum.bottom,
          content: Container(
            width: 200,
            padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(NiimbotIntl.getIntlMessage('pc0033', '如果你导入了Excel表格'),
                    style: NiimbotTheme.of(context).typography.hintStrong),
                const SizedBox(height: 6),
                Text(NiimbotIntl.getIntlMessage('pc0034', '序列号将以导入的条数结束'),
                    style: NiimbotTheme.of(context).typography.hint?.copyWith(
                        color: NiimbotTheme.of(context)
                            .colors
                            .textFillColorSecondary)),
                const SizedBox(height: 14),
                Text(NiimbotIntl.getIntlMessage('pc0035', '如果你未导入Excel表格'),
                    style: NiimbotTheme.of(context).typography.hintStrong),
                const SizedBox(height: 6),
                Text(
                    NiimbotIntl.getIntlMessage(
                        'pc0036', '序列号将默认生成5个，可在打印设置页设置其持续数量'),
                    style: NiimbotTheme.of(context).typography.hint?.copyWith(
                        color: NiimbotTheme.of(context)
                            .colors
                            .textFillColorSecondary)),
              ],
            ),
          ),
          child: Text(
            NiimbotIntl.getIntlMessage('pc0037', '如何设置序列号数量？'),
            style: NiimbotTheme.of(context)
                .typography
                .hint
                ?.copyWith(color: NiimbotTheme.of(context).colors.smogBlue),
          ),
        ),
      ),
      const SizedBox(height: 16.0),
    ]);
  }
}
