import 'dart:typed_data';

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_ui/model/niimbot_dropdown_model.dart';
import 'package:niimbot_ui/model/niimbot_mouse_enum.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:niimbot_ui/widgets/niimbot_tooltip/niimbot_tooltip.dart';
import 'package:niimbot_ui/widgets/niimbot_radio/niimbot_radio_button.dart';

import 'package:niimbot_flutter_canvas/business/desktop/models/canvas_element_text_content_choose_model.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/content_box_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/content_source_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/select_contain_title_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/upload_qrcode_widget.dart';

class QRCodeContentWidget extends StatefulWidget {
  final String? value;
  final ValueChanged<String?>? onValueChange;
  final NetalQRCodeType type;
  final ValueChanged<NetalQRCodeType>? onTypeChange;
  final NetalQRCodeCorrectLevel correctLevel;
  final ValueChanged<NetalQRCodeCorrectLevel>? onCorrectLevelChange;
  final Uint8List? qrCodeImageData;
  final Function(String value, Uint8List list)? qrCodeImageChange;

  const QRCodeContentWidget({
    super.key,
    this.value,
    this.onValueChange,
    required this.type,
    this.onTypeChange,
    required this.correctLevel,
    this.onCorrectLevelChange,
    this.qrCodeImageData,
    this.qrCodeImageChange,
  });

  @override
  createState() => _QRCodeContentWidgetState();
}

class _QRCodeContentWidgetState extends State<QRCodeContentWidget> {
  String _currentValueType = '1';

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(final BuildContext context) {
    final themeColors = NiimbotTheme.of(context).colors;
    return ContentBoxWidget(
      children: [
        Column(
          children: [
            NiimbotRadioButton(
              width: double.infinity,
              itemBuild:
                  (final dynamic item, final NiimbotMouseEnum mouseType) {
                Color color = mouseType == NiimbotMouseEnum.active
                    ? themeColors.brandColor
                    : themeColors.textFillColorPrimary;
                if (item == '1') {
                  return NiimbotToolTip(
                    text: NiimbotIntl.getIntlMessage('pc0030', '制作二维码'),
                    textStyle: NiimbotTheme.of(context)
                        .typography
                        .bodyStrong
                        ?.copyWith(color: color),
                  );
                } else {
                  return NiimbotToolTip(
                    text: NiimbotIntl.getIntlMessage('pc0023', '上传二维码'),
                    textStyle: NiimbotTheme.of(context)
                        .typography
                        .bodyStrong
                        ?.copyWith(color: color),
                  );
                }
              },
              value: _currentValueType,
              valueChanged: (final dynamic value) {
                setState(() {
                  _currentValueType = value;
                });
              },
              children: const ["1", '2'],
            ),
            const SizedBox(height: 12)
          ],
        ),
        _currentValueType == '1'
            ? ContentSourceWidget(
                value: widget.value,
                onValueChange: widget.onValueChange,
              )
            : UploadQRCodeWidget(
                currentQrImageData: widget.qrCodeImageData,
                qrcodeRead: (final Map<String, dynamic> map) {
                  //todo 识别到图片二维码后 后续操作 接口
                  print('========$map');
                  // {'resultCode': resultCode, 'resultFileData': data};
                  widget.qrCodeImageChange!(
                      map['resultCode'] ?? '', map['resultFileData']);
                },
              ),
        if (_currentValueType == '1')
          Column(children: [
            const SizedBox(height: 5),
            SelectContainTitleWidget(
              leftTitle: NiimbotIntl.getIntlMessage('app100000762', '编码格式'),
              title: CanvasElementTextContentChooseModel.qrCodeStyleList()
                      .firstWhereOrNull((final e) => e.value == widget.type)
                      ?.label ??
                  '',
              isDropDown: true,
              value: widget.type,
              dataList: CanvasElementTextContentChooseModel.qrCodeStyleList(),
              onTapSelect: (final NiimbotDropDownModel? model) {
                if (model != null) {
                  widget.onTypeChange?.call(NetalQRCodeType.byValue(
                      int.parse(model.value.value.toString()))!);
                }
              },
            ),
            const SizedBox(height: 12),
            if (![NetalQRCodeType.DATA_MATRIX, NetalQRCodeType.PDF417]
                .contains(widget.type))
              SelectContainTitleWidget(
                leftTitle: NiimbotIntl.getIntlMessage('pc0032', '容错率'),
                isShowHelpIcon: true,
                helpContent:
                    NiimbotIntl.getIntlMessage('pc0051', '容错率越高，二维码越容易被识别'),
                title: CanvasElementTextContentChooseModel.qRCodeCorrectLevel()
                        .firstWhereOrNull((final e) =>
                            e.value.toString() ==
                            widget.correctLevel.value.toString())
                        ?.label ??
                    '',
                isDropDown: true,
                value: CanvasElementTextContentChooseModel.qRCodeCorrectLevel()
                        .firstWhereOrNull((final e) =>
                            e.value.toString() ==
                            widget.correctLevel.value.toString())
                        ?.value ??
                    '',
                dataList:
                    CanvasElementTextContentChooseModel.qRCodeCorrectLevel(),
                onTapSelect: (final NiimbotDropDownModel? model) {
                  if (model != null) {
                    widget.onCorrectLevelChange?.call(
                        NetalQRCodeCorrectLevel.byValue(
                            int.parse(model.value.toString()))!);
                  }
                },
              ),
          ]),
      ],
    );
  }
}
