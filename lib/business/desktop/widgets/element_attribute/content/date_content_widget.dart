import 'dart:async';

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:netal_plugin/models/copy_wrapper.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_template/models/elements/date_element.dart';
import 'package:niimbot_template/models/elements/element_enum.dart';
import 'package:niimbot_ui/model/niimbot_dropdown_model.dart';
import 'package:niimbot_ui/styles/border_radius.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:niimbot_ui/styles/typography_style.dart';
import 'package:niimbot_ui/widgets/niimbot_number_input.dart';
import 'package:niimbot_ui/widgets/niimbot_popover/niimbot_popover.dart';
import 'package:niimbot_ui/widgets/niimbot_select.dart';
import 'package:provider/provider.dart';

import 'package:niimbot_flutter_canvas/business/desktop/utils/canvas_widget_manager_utils.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/check_box_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/content_box_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/custom_select_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/date_checkbox_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/date_prefix_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/date_select_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/date_time_refresh.dart';
import 'package:niimbot_flutter_canvas/model/date_element_helper.dart';
import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_flutter_canvas/provider/models/date_element_updater.dart';

class DateContentWidget extends StatelessWidget {
  final DateElement element;

  const DateContentWidget({super.key, required this.element});

  ElementDateFormat get dateFormat =>
      element.dateFormat ?? DateElementHelper.getDateFormat();

  ElementTimeFormat get timeFormat =>
      element.timeFormat ?? ElementTimeFormat.HMS;

  @override
  Widget build(final BuildContext context) {
    final niimbotTheme = NiimbotTheme.of(context);

    /// 后期若全量适配语言动态刷新需要把这些移到build内
    final locale = NiimbotIntl.getCurrentLocale();
    String languageCode = locale.languageCode;
    if (languageCode == 'zh') {
      if (NiimbotIntl.getCurrentLocale().scriptCode == 'Hant') {
        languageCode = 'zh-cn-t';
      } else {
        languageCode = 'zh-cn';
      }
    } else if (languageCode == 'zh_TW' || languageCode == 'zh_Hant') {
      languageCode = 'zh-cn-t';
    }
    final List<String> addPrefix = languageCode == 'zh-cn'
        ? ['开封时间']
        : languageCode == 'zh-cn-t'
            ? ['有效期']
            : languageCode == 'ko'
                ? ['유통기한', '소비기한']
                : languageCode == 'en'
                    ? ['Exp', 'Best Before']
                    : languageCode == 'ru'
                        ? ['Годен до']
                        : languageCode == 'pt'
                            ? ['Validade']
                            : [];
    final List<String> prefix = [
      NiimbotIntl.getIntlMessage('app100001406', '生产日期'),
      NiimbotIntl.getIntlMessage('app100001407', '上架日期'),
      NiimbotIntl.getIntlMessage('app100001459', '制作日期'),
      ...addPrefix,
    ];
    final List<String> associatedPrefix = [
      NiimbotIntl.getIntlMessage('app100001408', '保质期至'),
      NiimbotIntl.getIntlMessage('app100001457', '有效期至'),
      NiimbotIntl.getIntlMessage('app100001458', '到期时间'),
    ];

    final originElement = context
            .read<CanvasStore>()
            .textController
            .getAssociateOriginElement(element.associateId) ??
        element;

    final associateElement = context
        .read<CanvasStore>()
        .textController
        .getAssociateElement(element.associateId);

    final contentTitle = context.select<CanvasStore, String?>((final v) {
      if (element.isAssociate) {
        return v.textController
            .getAssociateOriginContentTitle(element.associateId);
      }
      return element.contentTitle;
    });
    final associateContentTitle =
        context.select<CanvasStore, String?>((final v) {
      if (element.isAssociate) {
        return element.contentTitle;
      }
      return v.textController.getAssociateContentTitle(element.associateId);
    });

    final dateElementHelper = DateElementHelper.getDateElementDefault();
    final customPrefix = dateElementHelper.dateElementPrefix;
    final customAssociatePrefix = dateElementHelper.dateAssociatePrefix;

    /// 实时时间
    onDateChangeDateIsRefresh(final bool dateIsRefresh) {
      final time = DateTime.now().millisecondsSinceEpoch;
      context.read<CanvasStore>().textController.updateDateByAssociateId(
          element.id,
          element.associateId,
          DateElementUpdater(
            dateIsRefresh: dateIsRefresh,
            hasVipRes: dateIsRefresh,
            time: time,
            value: time.toString(),
          ));
      CanvasWidgetManager()
          .setDateElementDefault({'dateIsRefresh': dateIsRefresh});
    }

    /// 自定义存储到缓存
    Future<bool?> onCustomSaveCache(final String value,
        {final bool isOneDate = true}) async {
      if (isOneDate) {
        return await CanvasWidgetManager()
            .setDateElementDefault({'dateElementPrefix': value});
      } else {
        return await CanvasWidgetManager()
            .setDateElementDefault({'dateAssociatePrefix': value});
      }
    }

    /// 第一时间前缀变更
    onDateChangeDatePrefix(final String? value) {
      final originId = (element.isAssociate
              ? context
                  .read<CanvasStore>()
                  .textController
                  .getAssociateOriginId(element.associateId)
              : null) ??
          element.id;
      context.read<CanvasStore>().textController.updateTextElements({
        originId: DateElementUpdater(
          contentTitle: CopyWrapper.value(value),
        )
      });
      CanvasWidgetManager().setDateElementDefault({'contentTitle': value});
    }

    /// 选择日期
    onDateChange({
      final bool? use12Hours,
      final ElementDateFormat? format,
      final int? time,
      final bool checked = false,
    }) {
      final dateFormat = format ?? DateElementHelper.getDateFormat();
      context.read<CanvasStore>().textController.updateDateByAssociateId(
          element.id,
          element.associateId,
          DateElementUpdater(
            time: time,
            value: time.toString(),
            dateFormat: CopyWrapper.value(checked ? dateFormat : null),
          ));
      CanvasWidgetManager().setDateElementDefault(
          {'dateFormat': dateFormat.value, 'dateFormatOpen': checked});
    }

    /// 时间变更
    onDateTimeChange({
      final bool? use12Hours,
      final ElementTimeFormat? format,
      final int? time,
      final bool checked = false,
    }) {
      final timeFormat = format ?? ElementTimeFormat.HMS;
      bool isAmTime = DateFormat('${timeFormat.value} a')
          .format(DateTime.fromMillisecondsSinceEpoch(time ?? element.time))
          .contains('AM');
      context.read<CanvasStore>().textController.updateDateByAssociateId(
          element.id,
          element.associateId,
          DateElementUpdater(
            time: time,
            value: time.toString(),
            timeFormat: CopyWrapper.value(checked ? timeFormat : null),
            timeUnit: CopyWrapper.value(use12Hours == true
                ? (isAmTime
                    ? ElementTimeUnit.morning
                    : ElementTimeUnit.afternoon)
                : null),
          ));
      CanvasWidgetManager().setDateElementDefault({
        'timeFormat': timeFormat.value,
        'timeFormatOpen': checked,
        'use12Hours': use12Hours == true,
      });
    }

    /// 时间偏移
    onTimeOffsetChange(final int timeOffset) {
      context.read<CanvasStore>().textController.updateDateByAssociateId(
          element.id,
          element.associateId,
          DateElementUpdater(
            timeOffset: timeOffset,
          ));
    }

    /// 是否开启关联元素
    openDateAssociate(final bool associated) {
      context
          .read<CanvasStore>()
          .textController
          .openDateAssociate(element.id, associated);
    }

    onValidityPeriodChange(final num validityPeriod) {
      context.read<CanvasStore>().textController.updateDateByAssociateId(
          element.id,
          element.associateId,
          DateElementUpdater(
            validityPeriod: CopyWrapper.value(validityPeriod.toInt()),
          ));
    }

    onValidityPeriodUnitChange(final ElementDateAssociatedUnit unit) {
      context.read<CanvasStore>().textController.updateDateByAssociateId(
          element.id,
          element.associateId,
          DateElementUpdater(validityPeriodUnit: CopyWrapper.value(unit)));
    }

    return ContentBoxWidget(
      children: [
        CheckBoxWidget(
          title: NiimbotIntl.getIntlMessage('app01464', '实时时间'),
          isCheckbox: false,
          isShowVipIcon: true,
          isShowHelpIcon: true,
          isOverWhite: true,
          helpPopoverWidth: 240,
          popoverPosition: NiimbotPopoverPositionEnum.bottom,
          popoverWidget: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  NiimbotIntl.getIntlMessage(
                      'app100001470', '开启实时时间后，打印时不用再手动调整时间'),
                  style: niimbotTheme.typography.hintStrong?.copyWith(
                      color: niimbotTheme.colors.systemFillColorBlack),
                ),
                const SizedBox(height: 8.0),
                const _DateTimeRefresh(),
              ],
            ),
          ),
          value: originElement.dateIsRefresh,
          valueChanged: onDateChangeDateIsRefresh,
        ),
        const SizedBox(height: 12),
        Divider(
          height: 1,
          color: niimbotTheme.colors.dividerColor,
        ),
        const SizedBox(height: 12),
        DateCheckboxWidget(
          label: NiimbotIntl.getIntlMessage('app00124', '前缀'),
          showCheckBox: false,
          child: DatePrefixWidget(
            customPrefix: customPrefix,
            dataList: prefix,
            value: contentTitle,
            onChanged: onDateChangeDatePrefix,
            onCustomSave: onCustomSaveCache,
          ),
        ),
        const SizedBox(height: 12),
        DateCheckboxWidget(
          label: NiimbotIntl.getIntlMessage('app01020', '日期'),
          checked: originElement.dateFormat != null,
          checkedEnable: originElement.dateFormat == null ||
              (originElement.dateFormat != null &&
                  originElement.timeFormat != null),
          onCheckedChange: (final bool checked) {
            onDateChange(format: dateFormat, checked: checked);
          },
          child: DateSelectWidget(
            realtime: originElement.dateIsRefresh,
            value: originElement.time,
            label: originElement.buildValue(dateFormat: dateFormat),
            format: dateFormat,
            disabled: originElement.dateFormat == null,
            onChanged: ({
              required final ElementDateFormat format,
              required final bool use12Hours,
              final int? time,
            }) {
              onDateChange(
                use12Hours: use12Hours,
                format: format,
                time: time,
                checked: true,
              );
            },
          ),
        ),
        const SizedBox(height: 12),
        DateCheckboxWidget(
          label: NiimbotIntl.getIntlMessage('app00010', '时间'),
          checked: originElement.timeFormat != null,
          checkedEnable: originElement.timeFormat == null ||
              (originElement.timeFormat != null &&
                  originElement.dateFormat != null),
          onCheckedChange: (final bool checked) {
            onDateTimeChange(format: timeFormat, checked: checked);
          },
          child: DateSelectWidget(
            realtime: originElement.dateIsRefresh,
            value: originElement.time,
            use12Hours: originElement.timeUnit != null,
            label: originElement.buildValue(
              timeFormat: timeFormat,
              timeUnit: originElement.timeUnit,
            ),
            format: timeFormat,
            disabled: originElement.timeFormat == null,
            onChanged: ({
              required final ElementTimeFormat? format,
              required final bool use12Hours,
              final int? time,
            }) {
              onDateTimeChange(
                use12Hours: use12Hours,
                format: format,
                time: time,
                checked: true,
              );
            },
          ),
        ),
        const SizedBox(height: 12),
        DateCheckboxWidget(
          label: NiimbotIntl.getIntlMessage('pc0270', '日期/时间偏移'),
          showCheckBox: false,
          childWidth: 100,
          child: _DateTimeOffset(
            value: originElement.timeOffset,
            onChanged: onTimeOffsetChange,
          ),
        ),
        const SizedBox(height: 12),
        Divider(
          height: 1,
          color: niimbotTheme.colors.dividerColor,
        ),
        const SizedBox(height: 12),
        Column(
          children: [
            CheckBoxWidget(
              title: NiimbotIntl.getIntlMessage('pc0271', '关联时间'),
              value: element.associated || element.isAssociate,
              valueChanged: openDateAssociate,
              isCheckbox: true,
              isShowHelpIcon: false,
              helpPopoverWidth: 240,
            ),
            Container(
              width: double.infinity,
              padding: const EdgeInsetsDirectional.only(top: 12, start: 16),
              child: Row(
                children: [
                  SizedBox(
                    width: 145,
                    child: DatePrefixWidget(
                      customPrefix: customAssociatePrefix,
                      disabled: !(element.associated || element.isAssociate),
                      dataList: associatedPrefix,
                      value: associateContentTitle,
                      onChanged: (final String? value) {
                        context
                            .read<CanvasStore>()
                            .textController
                            .updateDateAssociateContentTitle(element.id, value);
                      },
                      onCustomSave: (final String value) async {
                        await onCustomSaveCache(value, isOneDate: false);
                      },
                    ),
                  ),
                  const SizedBox(width: 6),
                  Expanded(
                    child: _AssociateOffset(
                      disabled: !(element.associated || element.isAssociate),
                      value: associateElement?.validityPeriod ??
                          element.validityPeriod,
                      unit: associateElement?.validityPeriodUnit ??
                          element.validityPeriodUnit,
                      onValidityPeriodChange: onValidityPeriodChange,
                      onValidityPeriodUnitChange: onValidityPeriodUnitChange,
                    ),
                  )
                ],
              ),
            )
          ],
        )
      ],
    );
  }
}

/// 实时时间视图
class _DateTimeRefresh extends StatelessWidget {
  const _DateTimeRefresh({super.key});

  @override
  Widget build(final BuildContext context) {
    final niimbotTheme = NiimbotTheme.of(context);
    return DateTimeRefresh(
        format: 'yyyy-MM-dd HH:mm:ss',
        builder: (final String dateTime) {
          return Container(
            width: double.infinity,
            height: 76,
            decoration: BoxDecoration(
              borderRadius: NiimbotRadius.small,
              color: niimbotTheme.colors.solidBackgroundFillColorSecondary,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  NiimbotIntl.getIntlMessage('pc0269', '示例'),
                  style: niimbotTheme.typography.hint,
                ),
                const SizedBox(height: 4),
                Text(
                  dateTime,
                  style: niimbotTheme.typography.title?.copyWith(
                    fontWeight: NiimbotFontWeight.bold,
                  ),
                )
              ],
            ),
          );
        });
  }
}

/// 时间偏移
class _DateTimeOffset extends StatelessWidget {
  final int value;
  final ValueChanged<int>? onChanged;

  const _DateTimeOffset({
    super.key,
    this.value = 0,
    this.onChanged,
  });

  @override
  Widget build(final BuildContext context) {
    final valueAbs = value.abs();
    int day = (valueAbs / 1440).floor();
    int hour = (valueAbs % 1440 / 60).floor();
    int min = (valueAbs % 1440 % 60).floor();

    List<int> dateTime = [day, hour, min];
    String mathType = value < 0 ? '-' : '+';

    String getLabelText() {
      if (valueAbs == 0) {
        return NiimbotIntl.getIntlMessage('app100001425', '\$天',
            param: [valueAbs.toString()]);
      }
      if (valueAbs >= 1440) {
        return NiimbotIntl.getIntlMessage('app100001422', '\$天\$时\$分',
            param: dateTime.map((final e) => e.toString()).toList());
      } else if (valueAbs >= 60) {
        return NiimbotIntl.getIntlMessage('app100001423', '\$时\$分',
            param: dateTime
                .getRange(1, 3)
                .map((final e) => e.toString())
                .toList());
      } else {
        return NiimbotIntl.getIntlMessage('app100001424', '\$分',
            param: [dateTime.last.toString()]);
      }
    }

    return CustomSelectWidget(
      label: '$mathType${getLabelText()}',
      child: _DateTimeOffsetWidget(
        mathType: mathType,
        dateTime: dateTime,
        onChanged: onChanged,
      ),
    );
  }
}

class _DateTimeOffsetWidget extends StatefulWidget {
  final String mathType;
  final List<int> dateTime;
  final ValueChanged<int>? onChanged;

  const _DateTimeOffsetWidget({
    super.key,
    this.mathType = '+',
    required this.dateTime,
    this.onChanged,
  });

  @override
  State<_DateTimeOffsetWidget> createState() => _DateTimeOffsetWidgetState();
}

class _DateTimeOffsetWidgetState extends State<_DateTimeOffsetWidget> {
  late List<int> _dateTime;
  late String _mathType;

  @override
  void initState() {
    super.initState();
    initPageData();
  }

  initPageData() {
    _dateTime = widget.dateTime;
    _mathType = widget.mathType;
    if (mounted) {
      setState(() {});
    }
  }

  @override
  void didUpdateWidget(covariant final _DateTimeOffsetWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.dateTime != widget.dateTime ||
        oldWidget.mathType != widget.mathType) {
      initPageData();
    }
  }

  @override
  Widget build(final BuildContext context) {
    final niimbotTheme = NiimbotTheme.of(context);

    List<String> listTime = [
      NiimbotIntl.getIntlMessage('app100001420', '天'),
      NiimbotIntl.getIntlMessage('app100001427', '时'),
      NiimbotIntl.getIntlMessage('app100001428', '分'),
    ];

    actionChanged(final List<int> timeData, {final bool add = true}) {
      List<int> stepList = [1440, 60, 1];
      int sum =
          timeData.foldIndexed(0, (final index, final result, final item) {
        return result + stepList[index] * item;
      });
      if (!add) sum = 0 - sum;
      widget.onChanged?.call(sum);
    }

    itemChanged(final num? val, final int idx) {
      List<int> timeData = [..._dateTime];
      timeData[idx] = (val ?? 0).toInt();
      actionChanged(timeData, add: _mathType == '+');
    }

    return Container(
      width: 350,
      padding: const EdgeInsets.symmetric(vertical: 18, horizontal: 14),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  NiimbotIntl.getIntlMessage('app100001421', '时间偏移'),
                ),
              ),
              InkWell(
                hoverColor: Colors.transparent,
                highlightColor: Colors.transparent,
                splashColor: Colors.transparent,
                child: Text(
                  NiimbotIntl.getIntlMessage('pc0273', '清零'),
                  style: niimbotTheme.typography.body
                      ?.copyWith(color: niimbotTheme.colors.brandColor),
                ),
                onTap: () {
                  _dateTime = [0, 0, 0];
                  setState(() {});
                  actionChanged(_dateTime);
                },
              )
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: NiimbotSelect(
                  list: [
                    NiimbotDropDownModel(
                        value: '-',
                        label: NiimbotIntl.getIntlMessage('pc0275', '提前')),
                    NiimbotDropDownModel(
                        value: '+',
                        label: NiimbotIntl.getIntlMessage('pc0274', '推迟')),
                  ],
                  value: _mathType,
                  onChanged: (final e) {
                    _mathType = e != '-' ? '+' : '-';
                    setState(() {});
                    if (_dateTime.any((final v) => v != 0)) {
                      actionChanged(_dateTime, add: e == '+');
                    }
                  },
                ),
              ),
              ...listTime.mapIndexed(
                (final index, final item) => Container(
                  padding: const EdgeInsetsDirectional.only(start: 8),
                  width: 70,
                  child: NiimbotNumberInput(
                    suffix: Padding(
                        padding: const EdgeInsets.all(8),
                        child: Text(
                          item,
                          style: niimbotTheme.typography.hint?.copyWith(
                            color: niimbotTheme.colors.textFillColorSecondary,
                          ),
                        )),
                    value: _dateTime[index],
                    min: 0,
                    max: [99, 23, 59][index],
                    digit: 0,
                    onChanged: (final num? val) {
                      itemChanged(val, index);
                    },
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

/// 关联时间
class _AssociateOffset extends StatelessWidget {
  final bool disabled;
  final int value;
  final ElementDateAssociatedUnit unit;
  final ValueChanged<num>? onValidityPeriodChange;

  final ValueChanged<ElementDateAssociatedUnit>? onValidityPeriodUnitChange;

  const _AssociateOffset({
    this.disabled = false,
    this.value = 1,
    this.unit = ElementDateAssociatedUnit.month,
    this.onValidityPeriodChange,
    this.onValidityPeriodUnitChange,
  });

  @override
  Widget build(final BuildContext context) {
    final List<NiimbotDropDownModel<ElementDateAssociatedUnit>> dataList = [
      NiimbotDropDownModel(
          value: ElementDateAssociatedUnit.hour,
          label: NiimbotIntl.getIntlMessage('app100001435', '小时')),
      NiimbotDropDownModel(
          value: ElementDateAssociatedUnit.day,
          label: NiimbotIntl.getIntlMessage('app100001420', '天')),
      NiimbotDropDownModel(
          value: ElementDateAssociatedUnit.month,
          label: NiimbotIntl.getIntlMessage('app100001436', '个月')),
      NiimbotDropDownModel(
          value: ElementDateAssociatedUnit.year,
          label: NiimbotIntl.getIntlMessage('app100000680', '年')),
    ];

    return CustomSelectWidget(
      label:
          '+$value${dataList.firstWhereOrNull((final e) => e.value == unit)?.label}',
      disabled: disabled,
      child: Container(
        width: 200,
        padding: const EdgeInsets.symmetric(vertical: 18, horizontal: 14),
        child: Column(
          children: [
            Container(
              alignment: Alignment.centerLeft,
              child: Text(
                NiimbotIntl.getIntlMessage('pc0271', '关联时间'),
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                NiimbotNumberInput(
                  width: 70,
                  min: 1,
                  max: 99,
                  digit: 0,
                  value: value,
                  onChanged: onValidityPeriodChange,
                ),
                const SizedBox(width: 6),
                Expanded(
                  child: NiimbotSelect(
                    list: dataList,
                    value: unit,
                    onChanged: onValidityPeriodUnitChange,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
