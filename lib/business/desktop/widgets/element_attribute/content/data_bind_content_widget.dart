import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';

import 'package:niimbot_flutter_canvas/business/desktop/models/index.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/content_box_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/data_bind_generate_widget.dart';

class DataBindContentAttributeWidget extends StatelessWidget {
  final String? value;
  final ExcelSourceType? generateType;
  final ValueChanged<ExcelSourceType>? onGenerateTypeChange;
  final bool printColumnNames;
  final ValueChanged<bool>? onPrintColumnNames;
  final bool showColumn;

  const DataBindContentAttributeWidget({
    super.key,
    this.value,
    this.generateType,
    this.onGenerateTypeChange,
    final bool? printColumnNames,
    this.onPrintColumnNames,
    this.showColumn=false,
  }) : printColumnNames = printColumnNames ?? false;

  @override
  Widget build(final BuildContext context) {
    return ContentBoxWidget(
      children: [
        DataBindGenerateWidget(
          value: value,
          generateType: generateType,
          onGenerateTypeChange: onGenerateTypeChange,
          printColumnNames: printColumnNames,
          onPrintColumnNames: onPrintColumnNames,
          showColumn: showColumn,
        ),
      ],
    );
  }
}
