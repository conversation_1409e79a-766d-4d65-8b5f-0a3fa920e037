import 'package:flutter/material.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_template/models/elements/element_enum.dart';
import 'package:niimbot_template/models/elements/material_element.dart';
import 'package:niimbot_ui/icons/icons.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:niimbot_ui/widgets/niimbot_popover/niimbot_popover.dart';
import 'package:niimbot_http/business/apis/rest/models/material_item.dart';

import 'package:niimbot_flutter_canvas/utils/canvas_image_util.dart';
import 'package:niimbot_flutter_canvas/business/desktop/utils/canvas_widget_manager_utils.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/content_box_widget.dart';

class MaterialContentWidget extends StatefulWidget {
  final MaterialElement element;

  const MaterialContentWidget({required this.element, super.key});

  @override
  State<MaterialContentWidget> createState() => _MaterialContentWidgetState();
}

class _MaterialContentWidgetState extends State<MaterialContentWidget> {
  ///素材和边框视图
  Widget _canvasMaterWidget() {
    return widget.element.materialType == MaterialElementType.border &&
            CanvasWidgetManager.sharedInstance().borderWidget != null
        ? CanvasWidgetManager.sharedInstance().borderWidget!(
            (final MaterialItem item) async {
            CanvasWidgetManager.sharedInstance().onMaterialChanged(
                item, true, context,
                currentMaterialElement: widget.element);
          })
        : CanvasWidgetManager.sharedInstance().materialWidget != null
            ? CanvasWidgetManager.sharedInstance().materialWidget!(
                (final MaterialItem item) async {
                CanvasWidgetManager.sharedInstance().onMaterialChanged(
                    item, false, context,
                    currentMaterialElement: widget.element);
              })
            : Container();
  }

  @override
  Widget build(final BuildContext context) {
    // CanvasMaterialElement cloneElement = widget.element.copyWith(
    //     imageProcessingValue: [
    //       127
    //     ],
    //     colorReverse: false,
    //     colorChannel: 0,
    //     elementColor: Colors.black,
    //     height: widget.element.materialType == MaterialElementType.border
    //         ? 12.5 //widget.element.height * (widget.element.height / (109.px2mm()))
    //         : 12.5,
    //     width: widget.element.materialType == MaterialElementType.border
    //         ? 109
    //             .px2mm() //widget.element.width * (widget.element.height / (109.px2mm()))
    //         : 12.5 //* (widget.element.width / widget.element.height),
    //     );
    return ContentBoxWidget(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              NiimbotIntl.getIntlMessage('pc0029', '当前使用'),
              style: NiimbotTheme.of(context).typography.body,
            ),
            const Spacer(),
            NiimbotPopover(
              position: NiimbotPopoverPositionEnum.start,
              showArrow: true,
              content: _canvasMaterWidget(),
              child: Row(
                children: [
                  Stack(
                    children: [
                      Container(
                        width: widget.element.materialType ==
                                MaterialElementType.border
                            ? 109
                            : 48,
                        height: 48,
                        padding: const EdgeInsets.all(6.0),
                        decoration: BoxDecoration(
                          borderRadius:
                              const BorderRadius.all(Radius.circular(8)),
                          border: Border.all(
                              width: 1,
                              style: BorderStyle.solid,
                              color: NiimbotTheme.of(context)
                                  .colors
                                  .borderColorNormal),
                        ),
                        child: Center(
                            child: CanvasImageUtils.imageWidget(
                          widget.element.materialType ==
                                  MaterialElementType.border
                              ? 100
                              : 48,
                          48,
                          url: widget.element.imageUrl ?? '',
                          localPath: widget.element.localImageUrl,
                          fit: BoxFit.contain,
                        )
                            // sr.ShouldRebuild(
                            //     shouldRebuild:
                            //         (ImageWidget pre, ImageWidget cur) {
                            //       if (pre.element.imageData !=
                            //           cur.element.imageData) {
                            //         return true;
                            //       }
                            //       return false;
                            //     },
                            //     child: ImageWidget(
                            //       element: cloneElement,
                            //       templateSize: context
                            //           .read<CanvasStore>()
                            //           .canvasData
                            //           .size,
                            //     )),
                            ),
                      ),
                      if (widget.element.hasVipRes == true)
                        Positioned(
                          right: 0,
                          top: 0,
                          child: NiimbotIcons.vipBadges(size: 12.0),
                        ),
                    ],
                  ),
                  const SizedBox(
                    width: 5,
                  ),
                  RotatedBox(
                      quarterTurns: 3,
                      child: NiimbotIcons.chevron(
                        size: 13,
                        color: NiimbotTheme.of(context)
                            .colors
                            .textFillColorSecondary,
                      )),
                ],
              ),
            ),
          ],
        )
      ],
    );
  }
}
