import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_ui/model/niimbot_dropdown_model.dart';


import 'package:niimbot_flutter_canvas/business/desktop/models/canvas_element_text_content_choose_model.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/content_box_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/content_source_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/select_contain_title_widget.dart';

class BarCodeContentWidget extends StatelessWidget {
  final String? value;
  final ValueChanged<String>? onValueChange;
  final NetalBarcodeType type;
  final ValueChanged<NetalBarcodeType>? onTypeChange;

  const BarCodeContentWidget({
    super.key,
    this.value,
    this.onValueChange,
    required this.type,
    this.onTypeChange,
  });

  @override
  Widget build(final BuildContext context) {
    return ContentBoxWidget(
      children: [
        ContentSourceWidget(
          value: value,
          onValueChange: onValueChange,
        ),
        const SizedBox(height: 8),
        SelectContainTitleWidget(
          leftTitle: NiimbotIntl.getIntlMessage('app100000762', '编码格式'),
          title: CanvasElementTextContentChooseModel.barCodeStyleList()
                  .firstWhereOrNull((final e) => e.value == type)
                  ?.label ??
              '',
          isDropDown: true,
          value: type,
          dataList: CanvasElementTextContentChooseModel.barCodeStyleList(),
          onTapSelect: (final NiimbotDropDownModel? model) {
            if (model != null) {
              onTypeChange?.call(NetalBarcodeType.byValue(
                  int.parse(model.value.value.toString()))!);
            }
          },
        ),
      ],
    );
  }
}
