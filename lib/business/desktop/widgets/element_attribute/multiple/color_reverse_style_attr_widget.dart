import 'package:flutter/material.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_ui/styles/theme.dart';

import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/check_box_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/color_widget.dart';

class ColorReverseStyleAttributeWidget extends StatelessWidget {
  final int? colorChannel;
  final void Function(int channel, Color color)? onColorChannelChange;
  final bool colorReverse;
  final ValueChanged<bool>? onColorReverseChange;
  final Color? elementColor;

  const ColorReverseStyleAttributeWidget({
    super.key,
    this.colorChannel,
    this.onColorChannelChange,
    final bool? colorReverse,
    this.onColorReverseChange,
    this.elementColor,
  }) : colorReverse = colorReverse ?? false;

  @override
  Widget build(final BuildContext context) {
    return Column(
      children: [
        Container(
          alignment: Alignment.centerLeft,
          child: Text(NiimbotIntl.getIntlMessage('app01006', '样式'),
              style: NiimbotTheme.of(context).typography.titleStrong),
        ),
        const SizedBox(height: 19),
        ColorWidget(
          value: colorChannel,
          onChange: onColorChannelChange,
          color: elementColor,
        ),
        const SizedBox(height: 24),
        CheckBoxWidget(
          title: NiimbotIntl.getIntlMessage('app100000420', '反白'),
          isShowHelpIcon: true,
          value: colorReverse,
          valueChanged: onColorReverseChange,
          isOverWhite: true,
        ),
      ],
    );
  }
}
