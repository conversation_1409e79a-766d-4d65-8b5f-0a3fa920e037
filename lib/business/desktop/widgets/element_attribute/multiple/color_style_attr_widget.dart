import 'package:flutter/material.dart';
import 'package:niimbot_ui/styles/theme.dart';


import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/color_widget.dart';

class ColorStyleAttributeWidget extends StatelessWidget {
  final String title;
  final int? colorChannel;
  final void Function(int channel, Color color)? onColorChannelChange;
  final Color? elementColor;

  const ColorStyleAttributeWidget({
    super.key,
    required this.title,
    this.colorChannel,
    this.onColorChannelChange,
    this.elementColor,
  });

  @override
  Widget build(final BuildContext context) {
    return Column(
      children: [
        Container(
          alignment: AlignmentDirectional.centerStart,
          child: Text(title,
              style: NiimbotTheme.of(context).typography.titleStrong),
        ),
        const SizedBox(height: 16),
        ColorWidget(
          value: colorChannel,
          onChange: onColorChannelChange,
          color: elementColor,
        ),
      ],
    );
  }
}
