import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_canvas_plugins_interface/shared/canvas_font_data.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';

import 'package:netal_plugin/models/netal_enum.dart';
import 'package:netal_plugin/utils/ratio.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_template/models/elements/base_element.dart';
import 'package:niimbot_template/models/elements/element_enum.dart';
import 'package:niimbot_template/models/elements/graph_element.dart';
import 'package:niimbot_template/models/elements/image_element.dart';
import 'package:niimbot_template/models/elements/material_element.dart';
import 'package:niimbot_template/models/elements/text_element.dart';
import 'package:niimbot_ui/icons/icons.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:niimbot_ui/widgets/niimbot_toast/niimbot_toast.dart';
import 'package:niimbot_ui/widgets/niimbot_toast/niimbot_toast_controller.dart';
import 'package:provider/provider.dart';

import 'package:niimbot_flutter_canvas/provider/models/graph_element_updater.dart';
import 'package:niimbot_flutter_canvas/provider/models/image_element_updater.dart';
import 'package:niimbot_flutter_canvas/provider/models/line_element_updater.dart';
import 'package:niimbot_flutter_canvas/provider/models/text_element_updater.dart';
import 'package:niimbot_flutter_canvas/utils/input_utils.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/multiple/color_reverse_style_attr_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/multiple/color_style_attr_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/multiple/material_style_attr_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/style/barcode_style_attr_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/style/graph_style_attr_common_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/style/image_style_attr_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/style/line_style_attr_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/style/text_style_attr_widget.dart';
import 'package:niimbot_flutter_canvas/model/element/style_attr/barcode_style_attr.dart';
import 'package:niimbot_flutter_canvas/model/element/style_attr/color_reverse_style_attr.dart';
import 'package:niimbot_flutter_canvas/model/element/style_attr/color_style_attr.dart';
import 'package:niimbot_flutter_canvas/model/element/style_attr/graph_style_attr.dart';
import 'package:niimbot_flutter_canvas/model/element/style_attr/image_style_attr.dart';
import 'package:niimbot_flutter_canvas/model/element/style_attr/line_style_attr.dart';
import 'package:niimbot_flutter_canvas/model/element/style_attr/material_style_attr.dart';
import 'package:niimbot_flutter_canvas/model/element/style_attr/text_style_attr.dart';
import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_flutter_canvas/provider/models/bar_code_element_updater.dart';
import 'package:niimbot_flutter_canvas/provider/models/color_element_updater.dart';

final Logger _logger = Logger("MultipleElementAttributeWidget", on: kDebugMode);

class MultipleElementAttributeWidget extends StatelessWidget {
  const MultipleElementAttributeWidget({super.key});

  @override
  Widget build(final BuildContext context) {
    final attr = context.select<CanvasStore, ColorStyleAttribute?>(
        (final v) => v.selectedElementStyleAttribute);
    final selectedIds = context
        .select<CanvasStore, List<String>>((final v) => v.selectedElementIds);
    final selectedElements = context
        .select<CanvasStore, List<BaseElement>>((final v) => v.selectedElement);
    _logger.log(attr, '多选元素类型');
    onColorChannelChange(final int colorChannel, final Color color) {
      context
          .read<CanvasStore>()
          .baseController
          .updateElementsColors(ColorElementUpdater(
            colorChannel: colorChannel,
            elementColor: color,
          ));
    }

    onColorReverseChange(final bool colorReverse) {
      context
          .read<CanvasStore>()
          .baseController
          .updateElementsColorReverse(colorReverse);
    }

    if (attr is TextStyleAttribute) {
      return TextStyleAttributeWidget(
        fontFamily: attr.fontFamily,
        fontCode: attr.fontFamily,
        onFontFamilyChange: (final CanvasFontData fontItem) {
          context
              .read<CanvasStore>()
              .textController
              .updateSelectedTextElement(TextElementUpdater(
                fontFamily: fontItem.code,
                fontCode: fontItem.code,
                hasVipRes: fontItem.isVip,
              ));
        },
        fontSize: attr.fontSize,
        onFontSizeChange: (final num val) {
          context
              .read<CanvasStore>()
              .textController
              .updateSelectedTextElement(TextElementUpdater(fontSize: val));
        },
        textAlign: attr.textAlign,
        onTextAlignChange:
            context.read<CanvasStore>().textController.updateTextAlign,
        fontStyle: attr.fontStyle,
        onFontStyleChange: (final List<NetalTextFontStyle> fontStyle) {
          context.read<CanvasStore>().textController.updateSelectedTextElement(
              TextElementUpdater(fontStyle: fontStyle));
        },
        letterSpacing: attr.letterSpacing,
        onLetterSpacingChange: (final num val) {
          context
              .read<CanvasStore>()
              .textController
              .updateTextLetterSpacing(val, ids: selectedIds);
        },
        lineSpacing: attr.lineSpacing,
        onLineSpacingChange: (final num val) {
          context
              .read<CanvasStore>()
              .textController
              .updateSelectedTextElement(TextElementUpdater(lineSpacing: val));
        },
        typesettingMode: selectedElements.every((final element) =>
                (element as TextElement).textTypesettingMode ==
                attr.textTypesettingMode)
            ? attr.textTypesettingMode
            : null,
        onTypesettingModeChange: (final TextTypesettingMode mode) {
          context
              .read<CanvasStore>()
              .textController
              .updateTextTypesettingMode(selectedIds, mode);
        },
        typesettingParam: attr.typesettingParam,
        onTypesettingParamChange: (final List<int>? typesettingParam) {
          context.read<CanvasStore>().textController.updateSelectedTextElement(
              TextElementUpdater(typesettingParam: typesettingParam));
        },
        colorChannel: attr.colorChannel,
        onColorChannelChange: (final int value, final Color color) {
          context
              .read<CanvasStore>()
              .textController
              .updateSelectedTextElement(TextElementUpdater(
                colorChannel: value,
                elementColor: color,
              ));
        },
        colorReverse: attr.colorReverse,
        onColorReverseChange: (final bool value) {
          context
              .read<CanvasStore>()
              .textController
              .updateSelectedTextElement(TextElementUpdater(
                colorReverse: value,
              ));
        },
        lineBreakMode: attr.lineBreakMode,
        onLineBreakModeChange: (final NetalTextLineBreakMode val) {
          context.read<CanvasStore>().textController.updateSelectedTextElement(
              TextElementUpdater(lineBreakMode: val));
        },
        elementColor: attr.elementColor,
      );
    }
    if (attr is BarCodeStyleAttribute) {
      return BarCodeStyleAttributeWidget(
        fontSize: attr.fontSize,
        onFontSizeChange: (final num val) {
          //一维码文字宽或者高超出一维码限制判断
          double configFontSize = val.mm2px().toDouble();
          Size textSize = InputUtils.boundingTextSize(
              attr.value!, TextStyle(fontSize: configFontSize),
              maxLines: 1);
          if ((textSize.width + 1) > attr.width!.mm2px() ||
              (configFontSize + 3.mm2px()) >= attr.height!.mm2px()) {
            // LoadingMix.showToast(intlanguage("app01065", '请拉伸条码'));
            NiimbotToastController().show(
              NiimbotIntl.getIntlMessage("app01065", '请拉伸条码'),
              type: ToastType.warning,
              icon: NiimbotIcons.warning(
                color: NiimbotTheme.of(context).colors.warningColorNormal,
                size: 20.00,
              ),
            );
            return;
          }
          context.read<CanvasStore>().barcodeController.updateBarCodes({
            for (final e in selectedIds)
              e: BarCodeElementUpdater(fontSize: val, textHeight: val + 0.2)
          });
        },
        type: attr.type,
        textPosition: attr.textPosition,
        onTextPositionChange: (final NetalBarcodeTextPosition textPosition) {
          context.read<CanvasStore>().barcodeController.updateBarCodes({
            for (final e in selectedIds)
              e: BarCodeElementUpdater(
                textPosition: textPosition,
              )
          });
        },
        colorChannel: attr.colorChannel,
        onColorChannelChange: (final int value, final Color color) {
          context.read<CanvasStore>().barcodeController.updateBarCodes({
            for (final e in selectedIds)
              e: BarCodeElementUpdater(
                colorChannel: value,
                elementColor: color,
              )
          });
        },
        elementColor: attr.elementColor,
      );
    }
    if (attr is GraphStyleAttribute) {
      num lineWidth = attr.lineWidth;
      for (var element in selectedElements) {
        if (element is GraphElement) {
          final temp = element;
          if (lineWidth > temp.lineWidth) {
            lineWidth = temp.lineWidth;
          }
        }
      }
      return GraphStyleAttrCommonWidget(
        showTitle: false,
        lineType: attr.lineType,
        lineWidth: lineWidth,
        colorChannel: attr.colorChannel,
        onLineTypeChange: (final dynamic lineType) {
          context.read<CanvasStore>().lineController.updateGraphElements({
            for (final e in selectedIds)
              e: GraphElementUpdater(
                lineType: lineType,
              )
          });
        },
        onColorChannelChange: (final int value, final Color color) {
          context.read<CanvasStore>().lineController.updateGraphElements({
            for (final e in selectedIds)
              e: GraphElementUpdater(
                colorChannel: value,
                elementColor: color,
              )
          });
        },
        onLineWidthChange: (final num lineWidth) {
          context.read<CanvasStore>().lineController.updateGraphElements({
            for (final e in selectedIds)
              e: GraphElementUpdater(
                lineWidth: lineWidth,
              )
          });
        },
        value: attr.graphType,
        valueChanged: (final NetalGraphType? graphType) {
          for (var element in selectedElements) {
            num width = element.width;
            num height;
            if (graphType == NetalGraphType.round) {
              height = width;
            } else if (graphType == NetalGraphType.ellipse) {
              height = width * 2 / 3;
            } else {
              height = element.height;
            }
            context.read<CanvasStore>().lineController.updateGraphElements({
              element.id: GraphElementUpdater(
                graphType: graphType,
                width: width,
                height: height,
              )
            });
          }
        },
        elementColor: attr.elementColor,
      );
    }
    if (attr is LineStyleAttribute) {
      return LineStyleAttributeWidget(
        lineWidth: attr.lineWidth,
        onLineWidthChange: (final num lineWidth) {
          context.read<CanvasStore>().lineController.updateLineWidth(lineWidth);
        },
        colorChannel: attr.colorChannel,
        onColorChannelChange: (final int colorChannel, final Color color) {
          context.read<CanvasStore>().lineController.updateLineElements({
            for (final e in selectedIds)
              e: LineElementUpdater(
                colorChannel: colorChannel,
                elementColor: color,
              )
          });
        },
        lineType: attr.lineType,
        onLineTypeChange: (final dynamic lineType) {
          context.read<CanvasStore>().lineController.updateLineElements({
            for (final e in selectedIds)
              e: LineElementUpdater(lineType: lineType)
          });
        },
        elementColor: attr.elementColor,
      );
    }
    if (attr is MaterialStyleAttribute) {
      List<num> processingValue = attr.processingValue;
      for (var element in selectedElements) {
        if (element is ImageElement) {
          if (processingValue[0] > element.imageProcessingValue[0]) {
            processingValue = element.imageProcessingValue;
          }
        }
      }
      NetalImageRenderType renderType = NetalImageRenderType.threshold;
      if (!(selectedElements
          .every((final element) => element is MaterialElement))) {
        for (var e in selectedElements) {
          if ((e is ImageElement) &&
              (e is! MaterialElement) &&
              e.imageProcessingType == NetalImageRenderType.grayscale) {
            renderType = NetalImageRenderType.grayscale;
          }
        }
      }

      return MaterialStyleAttributeWidget(
        elementColor: attr.elementColor,
        showColorSlider: false,
        haveImageElement: !(selectedElements
            .every((final element) => element is MaterialElement)),
        renderType: renderType,
        processingValue: processingValue,
        onProcessingValueChange: (final List<num> imageProcessingValue) {
          context
              .read<CanvasStore>()
              .imageController
              .updateImageElementImageProcessingValue({
            for (final e in selectedIds)
              e: ImageElementUpdater(imageProcessingValue: imageProcessingValue)
          });
        },
        allowFreeZoom: attr.allowFreeZoom,
        onAllowFreeZoomChange: (final bool allowFreeZoom) {
          context.read<CanvasStore>().imageController.updateImageElements({
            for (final e in selectedIds)
              e: ImageElementUpdater(allowFreeZoom: !allowFreeZoom)
          });
        },
        colorChannel: attr.colorChannel,
        onColorChannelChange: (final int value, final Color color) {
          context.read<CanvasStore>().imageController.updateImageElements({
            for (final e in selectedIds)
              e: ImageElementUpdater(
                colorChannel: value,
                elementColor: color,
              )
          });
        },
        colorReverse: attr.colorReverse,
        onColorReverseChange: (final bool value) {
          context.read<CanvasStore>().imageController.updateImageElements({
            for (final e in selectedIds)
              e: ImageElementUpdater(
                colorReverse: value,
              )
          });
        },
      );
    }
    if (attr is ImageStyleAttribute) {
      final element = context.select<CanvasStore, BaseElement>(
          (final v) => v.selectedElement.first);

      NetalImageRenderType? renderType = NetalImageRenderType.threshold;
      if (selectedElements.every((final e) => e is ImageElement)) {
        if (selectedElements.every((final e) =>
            (e as ImageElement).imageProcessingType ==
            NetalImageRenderType.threshold)) {
          renderType = NetalImageRenderType.threshold;
        } else if (selectedElements.every((final e) =>
            (e as ImageElement).imageProcessingType ==
            NetalImageRenderType.grayscale)) {
          renderType = NetalImageRenderType.grayscale;
        } else {
          renderType = null;
        }
      }
      if (element is ImageElement) {
        return ImageStyleAttributeWidget(
          element: element,
          isMultiChoose: true,
          processingValue: attr.processingValue,
          allowFreeZoom: attr.allowFreeZoom,
          colorChannel: attr.colorChannel,
          imageProcessingType: renderType,
          onImageRenderTypeChange:
              (final NetalImageRenderType imageProcessingType) {
            context.read<CanvasStore>().imageController.updateImageElements({
              for (final e in selectedIds)
                e: ImageElementUpdater(
                    imageProcessingType: imageProcessingType,
                    imageProcessingValue: [
                      imageProcessingType == NetalImageRenderType.grayscale
                          ? 5
                          : 127
                    ])
            });
          },
          onProcessingValueChange: (final List<num> imageProcessingValue) {
            context.read<CanvasStore>().imageController.updateImageElements({
              for (final e in selectedIds)
                e: ImageElementUpdater(
                    imageProcessingValue: imageProcessingValue)
            });
          },
          onAllowFreeZoomChange: (final bool allowFreeZoom) {
            context.read<CanvasStore>().imageController.updateImageElements({
              for (final e in selectedIds)
                e: ImageElementUpdater(allowFreeZoom: !allowFreeZoom)
            });
          },
          onColorChannelChange: (final int value, final Color color) {
            context.read<CanvasStore>().imageController.updateImageElements({
              for (final e in selectedIds)
                e: ImageElementUpdater(
                  colorChannel: value,
                  elementColor: color,
                )
            });
          },
          elementColor: attr.elementColor,
        );
      }
    }

    if (attr is ColorReverseStyleAttribute) {
      return ColorReverseStyleAttributeWidget(
        colorChannel: attr.colorChannel,
        colorReverse: attr.colorReverse,
        onColorChannelChange: onColorChannelChange,
        onColorReverseChange: onColorReverseChange,
        elementColor: attr.elementColor,
      );
    }
    if (attr is ColorStyleAttribute) {
      return ColorStyleAttributeWidget(
        title: NiimbotIntl.getIntlMessage('app01006', '样式'),
        colorChannel: attr.colorChannel,
        onColorChannelChange: onColorChannelChange,
        elementColor: attr.elementColor,
      );
    }
    return Container();
  }
}
