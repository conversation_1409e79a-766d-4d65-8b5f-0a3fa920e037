import 'package:flutter/material.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:niimbot_intl/niimbot_intl.dart';

import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/base_material_attr.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/check_box_widget.dart';

class MaterialStyleAttributeWidget extends StatelessWidget {
  final List<num> processingValue;
  final ValueChanged<List<num>>? onProcessingValueChange;
  final bool allowFreeZoom;
  final ValueChanged<bool>? onAllowFreeZoomChange;
  final int? colorChannel;
  final void Function(int channel, Color color)? onColorChannelChange;
  final bool? colorReverse;
  final ValueChanged<bool>? onColorReverseChange;
  final bool haveImageElement;
  final NetalImageRenderType renderType;
  final Color? elementColor;
  final bool showColorSlider;

  const MaterialStyleAttributeWidget({
    super.key,
    final List<num>? processingValue,
    this.onProcessingValueChange,
    final bool? allowFreeZoom,
    this.onAllowFreeZoomChange,
    this.colorChannel,
    this.onColorChannelChange,
    this.colorReverse,
    this.onColorReverseChange,
    this.haveImageElement = false,
    this.renderType = NetalImageRenderType.threshold,
    this.elementColor,
    this.showColorSlider = true,
  })  : processingValue = processingValue ?? const [127],
        allowFreeZoom = allowFreeZoom ?? false;

  @override
  Widget build(final BuildContext context) {
    return Column(
      children: [
        ...BaseMaterialAttribute(
          processingValue: processingValue,
          imageProcessingType: renderType,
          onProcessingValueChange: onProcessingValueChange,
          allowFreeZoom: allowFreeZoom,
          onAllowFreeZoomChange: onAllowFreeZoomChange,
          colorChannel: colorChannel,
          onColorChannelChange: onColorChannelChange,
          elementColor: elementColor,
          showColorSlider: showColorSlider,
        ).build(context),
        if (!haveImageElement) ...[
          const SizedBox(height: 16),
          CheckBoxWidget(
            title: NiimbotIntl.getIntlMessage('app100000420', '反白'),
            isShowHelpIcon: true,
            value: colorReverse,
            valueChanged: onColorReverseChange,
            isOverWhite: true,
          ),
        ]
      ],
    );
  }
}
