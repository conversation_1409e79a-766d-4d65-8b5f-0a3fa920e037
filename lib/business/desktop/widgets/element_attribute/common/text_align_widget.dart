import 'package:flutter/cupertino.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:niimbot_ui/icons/icons.dart';
import 'package:niimbot_ui/model/niimbot_mouse_enum.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:niimbot_ui/widgets/niimbot_radio/niimbot_radio_button.dart';

class TextAlignWidget extends StatelessWidget {
  final NetalTextAlign? value;
  final ValueChanged<NetalTextAlign>? valueChanged;
  final bool disabled;
  final bool isVertical;

  const TextAlignWidget({
    required this.value,
    required this.valueChanged,
    this.disabled = false,
    this.isVertical = false,
    super.key,
  });

  @override
  Widget build(final BuildContext context) {
    final themeColors = NiimbotTheme.of(context).colors;

    return NiimbotRadioButton(
      disabled: disabled,
      itemBuild: (final dynamic item, final NiimbotMouseEnum mouseType) {
        Color color = mouseType == NiimbotMouseEnum.active
            ? themeColors.brandColor
            : themeColors.textFillColorPrimary;

        if (item == NetalTextAlign.start) {
          return !isVertical
              ? NiimbotIcons.textLeftAlign(
                  color: color,
                  size: 18.00,
                )
              : NiimbotIcons.verticalTextLeftAlign(
                  color: color,
                  size: 18.00,
                );
        } else if (item == NetalTextAlign.center) {
          return !isVertical
              ? NiimbotIcons.textCenterAlign(
                  color: color,
                  size: 18.00,
                )
              : NiimbotIcons.verticalTextCenterAlign(
                  color: color,
                  size: 18.00,
                );
        } else {
          return !isVertical
              ? NiimbotIcons.textRightAlign(
                  color: color,
                  size: 18.00,
                )
              : NiimbotIcons.verticalTextRightAlign(
                  color: color,
                  size: 18.00,
                );
        }
      },
      value: value,
      valueChanged: (final dynamic val) {
        valueChanged?.call(val);
      },
      children: const [
        NetalTextAlign.start,
        NetalTextAlign.center,
        NetalTextAlign.end
      ],
    );
  }
}
