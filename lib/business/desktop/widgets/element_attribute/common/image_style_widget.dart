import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_flutter_canvas/widgets/elements/image_widget.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_template/models/elements/image_element.dart';
import 'package:niimbot_template/models/template/file_data_source.dart';
import 'package:niimbot_ui/widgets/niimbot_tooltip/niimbot_tooltip.dart';
import 'package:provider/provider.dart';
import 'package:should_rebuild/should_rebuild.dart' as sr;

class ImageStyleWidget extends StatefulWidget {
  final ImageElement element;
  final ValueChanged<NetalImageRenderType>? imageProcessingTypeChange;
  final NetalImageRenderType? imageProcessingType;

  const ImageStyleWidget({
    required this.element,
    this.imageProcessingTypeChange,
    this.imageProcessingType,
    super.key,
  });

  @override
  State<ImageStyleWidget> createState() => _ImageStyleWidgetState();
}

class _ImageStyleWidgetState extends State<ImageStyleWidget> {
  @override
  Widget build(final BuildContext context) {
    final fileDataSource = context.select<CanvasStore, FileDataSource?>(
        (final v) => v.canvasData.fileDataSources?.firstWhereOrNull(
            (final item) => item.rule
                .any((final val) => val.elementId == widget.element.id)));
    return SizedBox(
        height: 76,
        child: Row(children: [
          Expanded(
            flex: 1,
            child: NiimbotToolTip(
              text: NiimbotIntl.getIntlMessage("app100001166", "风格（体验版）"),
              maxLines: 2,
            ),
          ),
          // const Spacer(),
          _imageProcessingWidget(3, fileDataSource),
          const SizedBox(width: 12),
          _imageProcessingWidget(
              fileDataSource != null && fileDataSource.type == 'pdf' ? 4 : 1,
              fileDataSource),
        ]));
  }

  Widget _imageProcessingWidget(
      final int renderType, final FileDataSource? fileDataSource) {
    String pdfImagePath = widget.element.localImageUrl;
    if (fileDataSource != null && fileDataSource.pageImages.isNotEmpty) {
      // final index = pdfModel.pdfCurrentPage ?? 1;
      // if (index <= (pdfModel.pdfImagePaths?.length ?? 1)) {
      //   final path = pdfModel.pdfImagePaths?[index - 1] ?? '';
      //   if (path.isNotEmpty) {
      //     pdfImagePath = path;
      //   }
      // } else {
      //   context.read<CanvasStore>().clearSelected();
      // }
    }
    ImageElement cloneElement = widget.element.copyWith(
      imageProcessingType: NetalImageRenderType.byValue(renderType),
      imageProcessingValue: [(renderType == 1 || renderType == 4) ? 127 : 5],
      colorChannel: 0,
      elementColor: Colors.black,
      width: 56,
      height: 56,
      localImageUrl: pdfImagePath,
    );
    return InkWell(
      onTap: () {
        bool isPdf = fileDataSource != null && fileDataSource.type == 'pdf';
        widget.imageProcessingTypeChange!(renderType == 3
            ? NetalImageRenderType.grayscale
            : isPdf
                ? NetalImageRenderType.pdfMode
                : NetalImageRenderType.threshold);
      },
      child: Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          color: Colors.transparent,
          borderRadius: const BorderRadius.all(Radius.circular(10)),
          border: Border.all(
              width: 1.5,
              style: BorderStyle.solid,
              color: widget.imageProcessingType?.value == renderType
                  ? Colors.red
                  : Colors.transparent),
        ),
        child: Center(
          child: ClipRRect(
              borderRadius: const BorderRadius.all(Radius.circular(8)),
              child: SizedBox(
                height: 54,
                width: 54,
                child: sr.ShouldRebuild(
                    shouldRebuild:
                        (final ImageWidget pre, final ImageWidget cur) {
                      if (pre.element.imageData != cur.element.imageData ||
                          pre.element.localImageUrl !=
                              cur.element.localImageUrl) {
                        return true;
                      }
                      return false;
                    },
                    child: ImageWidget(
                      element: cloneElement,
                    )),
              )),
        ),
      ),
    );
  }
}
