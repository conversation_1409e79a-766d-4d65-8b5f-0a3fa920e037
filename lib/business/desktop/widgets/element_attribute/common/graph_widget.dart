import 'package:flutter/cupertino.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:niimbot_ui/icons/icons.dart';
import 'package:niimbot_ui/model/niimbot_mouse_enum.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:niimbot_ui/widgets/niimbot_radio/niimbot_radio_button.dart';

class GraphWidget extends StatelessWidget {
  final NetalGraphType? value;
  final ValueChanged<NetalGraphType>? valueChanged;

  const GraphWidget({
    this.value,
    this.valueChanged,
    super.key,
  });

  @override
  Widget build(final BuildContext context) {
    final themeColors = NiimbotTheme.of(context).colors;

    return NiimbotRadioButton(
      disabled: false,
      itemBuild: (final dynamic item, final NiimbotMouseEnum mouseType) {
        Color color = mouseType == NiimbotMouseEnum.active
            ? themeColors.brandColor
            : themeColors.textFillColorPrimary;
        if (item == NetalGraphType.rectangle) {
          return NiimbotIcons.rectangle(
            color: color,
            size: 24.00,
          );
        } else if (item == NetalGraphType.roundedRectangle) {
          return NiimbotIcons.roundedRectangle(
            color: color,
            size: 24.00,
          );
        } else if (item == NetalGraphType.round) {
          return NiimbotIcons.circle(
            color: color,
            size: 24.00,
          );
        } else {
          return NiimbotIcons.oval(
            color: color,
            size: 24.00,
          );
        }
      },
      value: value,
      valueChanged: (final dynamic val) {
        valueChanged?.call(val);
      },
      children: const [
        NetalGraphType.rectangle,
        NetalGraphType.roundedRectangle,
        NetalGraphType.round,
        NetalGraphType.ellipse
      ],
    );
  }
}
