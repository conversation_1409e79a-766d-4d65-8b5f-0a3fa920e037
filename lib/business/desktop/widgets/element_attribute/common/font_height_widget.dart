import 'package:flutter/material.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:niimbot_ui/widgets/niimbot_digital_stepper/niimbot_digital_stepper.dart';

///行距 间距
class FontHeightWidget extends StatelessWidget {
  final String title;
  final num number;
  final ValueChanged<num>? valueChanged;
  final num max;
  final num min;
  final bool isNegative;
  final double stepper;
  final int? digit;

  const FontHeightWidget({
    required this.title,
    final num? number,
    this.valueChanged,
    this.max = double.infinity,
    this.min = double.negativeInfinity,
    this.isNegative = false,
    this.stepper = 0.1,
    this.digit = 1,
    super.key,
  }) : number = number ?? 0.1;

  @override
  Widget build(final BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Flexible(
          flex: 1,
          child: Text(
            title,
            style: NiimbotTheme.of(context).typography.body,
            overflow: TextOverflow.ellipsis,
            maxLines: 2,
          ),
        ),
        NiimbotDigitalStepper(
          value: number,
          stepper: stepper,
          min: min,
          max: max,
          digit: digit,
          onChanged: (final count) {
            valueChanged?.call(count);
          },
        ),
      ],
    );
  }
}
