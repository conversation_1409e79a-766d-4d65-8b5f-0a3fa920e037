import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:niimbot_template/extensions/template_data.dart';
import 'package:niimbot_ui/icons/icons.dart';
import 'package:niimbot_ui/model/niimbot_dropdown_model.dart';
import 'package:niimbot_ui/styles/border_radius.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:niimbot_ui/widgets/niimbot_select.dart';
import 'package:provider/provider.dart';


import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_flutter_canvas/model/font_size_config.dart';
///字体大小
class FontSizeWidget extends StatefulWidget {
  final num? value;
  final ValueChanged<num>? valueChanged;

  const FontSizeWidget({
    this.value,
    this.valueChanged,
    super.key,
  });

  @override
  State<FontSizeWidget> createState() => _FontSizeWidgetState();
}

class _FontSizeWidgetState extends State<FontSizeWidget> {
  String hoverValue = '';

  @override
  Widget build(final BuildContext context) {
    final themeColors = NiimbotTheme.of(context).colors;
    final templateSize =
        context.select<CanvasStore, Size>((final v) => v.canvasData.size);
    final fontList = calcMaxFontSize(templateSize.width, templateSize.height)
        .map((final e) => NiimbotDropDownModel(label: e.title, value: e.mm))
        .toList();

    bool isMin = fontList.first.value == widget.value;
    bool isMax = fontList.last.value == widget.value;

    return Row(
      children: [
        Expanded(
          child: Row(
            children: [
              InkWell(
                mouseCursor: isMin
                    ? SystemMouseCursors.noDrop
                    : SystemMouseCursors.click,
                onTap: () {
                  for (int i = 0; i < fontList.length; i++) {
                    if (widget.value == fontList[i].value) {
                      if ((i - 1) >= 0) {
                        if (widget.valueChanged != null) {
                          widget.valueChanged!(fontList[i - 1].value);
                        }
                      }
                    }
                  }
                },
                child: MouseRegion(
                  onEnter: (final e) {
                    setState(() {
                      hoverValue = 'add';
                    });
                  },
                  onExit: (final e) {
                    setState(() {
                      hoverValue = '';
                    });
                  },
                  child: Container(
                    width: 40,
                    height: 32,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: themeColors.borderColorNormal,
                        width: 1,
                      ),
                      borderRadius: NiimbotRadius.middle,
                      color: hoverValue == 'add' && !isMin
                          ? themeColors.translucentFillColorQuarternary
                          : null,
                    ),
                    child: NiimbotIcons.textFontSizeAdd(
                      size: 20,
                      color: isMin
                          ? themeColors.textFillColorTertiary
                          : themeColors.systemFillColorBlack,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 4),
              InkWell(
                mouseCursor: isMax
                    ? SystemMouseCursors.noDrop
                    : SystemMouseCursors.click,
                onTap: () {
                  for (int i = 0; i < fontList.length; i++) {
                    if (widget.value == fontList[i].value) {
                      if ((i + 1) < fontList.length) {
                        if (widget.valueChanged != null) {
                          widget.valueChanged!(fontList[i + 1].value);
                        }
                      }
                    }
                  }
                },
                child: MouseRegion(
                  onEnter: (final e) {
                    setState(() {
                      hoverValue = 'subtract';
                    });
                  },
                  onExit: (final e) {
                    setState(() {
                      hoverValue = '';
                    });
                  },
                  child: Container(
                    width: 40,
                    height: 32,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: themeColors.borderColorNormal,
                        width: 1,
                      ),
                      borderRadius: NiimbotRadius.middle,
                      color: hoverValue == 'subtract' && !isMax
                          ? themeColors.translucentFillColorQuarternary
                          : null,
                    ),
                    child: NiimbotIcons.textFontSizeSubtract(
                      size: 20,
                      color: isMax
                          ? themeColors.textFillColorTertiary
                          : themeColors.systemFillColorBlack,
                    ),
                  ),
                ),
              )
            ],
          ),
        ),
        const SizedBox(width: 6),
        SizedBox(
          width: 180,
          child: NiimbotSelect(
            list: fontList,
            needChooseIcon: false,
            value: fontList.isEmpty
                ? ''
                : fontList
                        .firstWhereOrNull((final e) => e.value == widget.value)
                        ?.value ??
                    '',
            onChanged: (final model) {
              if (widget.valueChanged != null) {
                widget.valueChanged!(double.parse(model.toString() ?? ''));
              }
            },
          ),
        ),
      ],
    );
  }
}
