import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:niimbot_ui/icons/icons.dart';
import 'package:niimbot_ui/model/niimbot_dropdown_model.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:niimbot_ui/widgets/niimbot_popover/niimbot_popover.dart';
import 'package:niimbot_ui/widgets/niimbot_tooltip/niimbot_tooltip.dart';
import 'package:niimbot_ui/widgets/niimbot_select.dart';

import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/border_choose_widget.dart';

///文本左右内容来源- 及右边
class SelectContainTitleWidget<T> extends StatelessWidget {
  final String title;
  final String leftTitle;
  final bool isDropDown;
  final bool readonly;
  final Function(NiimbotDropDownModel<T>? value)? onTapSelect;
  final T? value;
  final List<NiimbotDropDownModel<T>>? dataList;
  final bool isShowHelpIcon;
  final String? helpContent;

  const SelectContainTitleWidget({
    required this.leftTitle,
    required this.title,
    this.onTapSelect,
    this.value,
    this.isDropDown = true,
    this.readonly = false,
    this.dataList = const [],
    this.isShowHelpIcon = false,
    this.helpContent = '',
    super.key,
  });

  @override
  Widget build(final BuildContext context) {
    final themeColors = NiimbotTheme.of(context).colors;
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          flex: 1,
          child: Row(
            children: [
              Container(
                constraints: const BoxConstraints(
                  maxWidth: 100,
                ),
                child: NiimbotToolTip(
                    text: leftTitle, // 文本内容
                    position: NiimbotPopoverPositionEnum.top,
                    maxLines: 2),
              ),
              if (isShowHelpIcon)
                const SizedBox(
                  width: 4,
                ),
              if (isShowHelpIcon)
                NiimbotPopover(
                  trigger: NiimbotPopoverTrigger.hover,
                  showArrow: true,
                  position: NiimbotPopoverPositionEnum.bottom,
                  content: Container(
                    constraints: const BoxConstraints(maxWidth: 240),
                    margin: const EdgeInsets.symmetric(
                        horizontal: 10, vertical: 12),
                    child: Text(
                      '$helpContent',
                      style: NiimbotTheme.of(context).typography.hint,
                    ),
                  ),
                  child: NiimbotIcons.help(
                    size: 16,
                    color: themeColors.textFillColorTertiary,
                  ),
                ),
            ],
          ),
        ),
        isDropDown
            ? SizedBox(
                width: 146,
                child: NiimbotSelect<T>(
                    list: dataList ?? [],
                    needChooseIcon: false,
                    value: value,
                    onChanged: (final v) {
                      NiimbotDropDownModel<T>? model =
                          dataList?.firstWhereOrNull((final e) => e.value == v);
                      if (onTapSelect != null) {
                        onTapSelect?.call(model);
                      }
                    }),
              )
            : InkWell(
                mouseCursor: SystemMouseCursors.basic,
                onTap: () {
                  if (onTapSelect != null) {
                    onTapSelect!(null);
                  }
                },
                child: SizedBox(
                  width: 146,
                  child: BorderChooseWidget(
                    title: title,
                    readonly: readonly,
                  ),
                ),
              )
      ],
    );
  }
}
