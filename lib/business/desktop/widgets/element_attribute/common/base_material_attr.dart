import 'package:flutter/cupertino.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/check_box_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/color_widget.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:niimbot_ui/widgets/niimbot_slider/niimbot_slider.dart';

class BaseMaterialAttribute {
  final List<num> processingValue;
  final ValueChanged<List<num>>? onProcessingValueChange;
  final bool allowFreeZoom;
  final ValueChanged<bool>? onAllowFreeZoomChange;
  final int? colorChannel;
  final void Function(int channel, Color color)? onColorChannelChange;
  final NetalImageRenderType? imageProcessingType;
  final Color? elementColor;
  final bool showBlackRed;

  BaseMaterialAttribute({
    final List<num>? processingValue,
    this.onProcessingValueChange,
    final bool? allowFreeZoom,
    this.onAllowFreeZoomChange,
    this.colorChannel,
    this.onColorChannelChange,
    this.imageProcessingType,
    this.elementColor,
    this.showBlackRed = true,
  })  : processingValue = processingValue ?? const [127],
        allowFreeZoom = allowFreeZoom ?? false;

  List<Widget> build(final BuildContext context) {
    return [
      Column(
        children: [
          Container(
            alignment: AlignmentDirectional.centerStart,
            child: Text(
              NiimbotIntl.getIntlMessage("app100000774", "对比度"),
              style: NiimbotTheme.of(context).typography.body,
            ),
          ),
          Container(
            alignment: AlignmentDirectional.centerStart,
            child: NiimbotSlider(
              value: processingValue[0].toDouble(),
              min: 0,
              max: (imageProcessingType == null ||
                      imageProcessingType == NetalImageRenderType.grayscale)
                  ? 99.0
                  : 255.0,
              width: 238,
              onChanged: (final value) {
                onProcessingValueChange?.call([value.toInt()]);
              },
            ),
          ),
          const SizedBox(height: 16),
        ],
      ),
      CheckBoxWidget(
        title: NiimbotIntl.getIntlMessage("app100000416", "等比拉伸"),
        value: !allowFreeZoom,
        valueChanged: onAllowFreeZoomChange,
      ),
      const SizedBox(height: 16),
      if (showBlackRed)
        ColorWidget(
          value: colorChannel,
          onChange: onColorChannelChange,
          color: elementColor,
        ),
    ];
  }
}
