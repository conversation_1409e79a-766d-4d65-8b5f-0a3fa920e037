import 'package:flutter/material.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_ui/icons/icons.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:niimbot_ui/widgets/niimbot_button.dart';

void canvasAlertShow(
  final BuildContext context,
  final bool showRight, {
  final String leftTitle = '',
  final Color leftTitleColor = Colors.black,
  final Color leftBgColor = const Color.fromRGBO(116, 116, 128, 0.08),
  final String rightTitle = '',
  final Color rightTitleColor = Colors.white,
  final Color rightBgColor = Colors.red,
  required final VoidCallback leftCallback,
  required final VoidCallback rightCallback,
  required final String title,
  required final Widget msgWidget,
}) {
  final themeColors = NiimbotTheme.of(context).colors;

  leftWidget(final StateSetter stateSetter, final context1) {
    return NiimbotButton(
        type: ButtonType.secondary,
        text: leftTitle.isEmpty
            ? NiimbotIntl.getIntlMessage("login0062", "取消")
            : leftTitle,
        onPressed: () {
          Navigator.of(context1).pop();
          leftCallback();
        });
  }

  rightWidget(final StateSetter stateSetter, final context1) {
    return NiimbotButton(
        type: ButtonType.primary,
        text: rightTitle.isEmpty
            ? NiimbotIntl.getIntlMessage("login0061", "确认")
            : rightTitle,
        onPressed: () {
          Navigator.of(context1).pop();
          rightCallback();
        });
  }

  showDialog(
      context: context,
      barrierDismissible: false,
      barrierColor: const Color.fromARGB(90, 0, 0, 0),
      builder: (final context) {
        return StatefulBuilder(builder: (final context1, final state) {
          return Dialog(
              shape: const RoundedRectangleBorder(
                  borderRadius: BorderRadius.all(Radius.circular(12))),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 400,
                    padding: const EdgeInsets.only(
                      left: 28,
                      right: 16,
                    ),
                    child: Column(
                      children: [
                        Container(
                          margin: const EdgeInsets.only(top: 16),
                          alignment: Alignment.topRight,
                          child: InkWell(
                              overlayColor: WidgetStateProperty.resolveWith(
                                (final states) {
                                  if (states.contains(WidgetState.hovered)) {
                                    // 悬浮时的颜色，这里设置为透明
                                    return Colors.transparent;
                                  }
                                  // 默认情况下返回null，表示不设置悬浮时的颜色
                                  return null;
                                },
                              ),
                              onTap: () {
                                Navigator.of(context1).pop();
                                leftCallback();
                              },
                              child: NiimbotIcons.closeWindow(
                                  size: 18,
                                  color: themeColors.textFillColorSecondary)),
                        ),
                        Container(
                            alignment: Alignment.centerLeft,
                            child: Row(
                              children: [
                                NiimbotIcons.warning(
                                  color: themeColors.warningColorNormal,
                                  size: 20.00,
                                ),
                                const SizedBox(
                                  width: 8,
                                ),
                                Text(
                                  title,
                                  style: NiimbotTheme.of(context)
                                      .typography
                                      .titleStrong,
                                ),
                              ],
                            )),
                        Container(
                          alignment: Alignment.centerLeft,
                          margin: const EdgeInsets.only(
                              top: 8, bottom: 10, left: 28),
                          child: msgWidget,
                        ),
                        Container(
                          alignment: Alignment.centerRight,
                          margin: const EdgeInsets.only(
                            top: 16,
                            bottom: 16,
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              const SizedBox(width: 8),
                              leftWidget(state, context1),
                              const SizedBox(width: 8),
                              if (showRight) rightWidget(state, context1),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ));
        });
      });
}
