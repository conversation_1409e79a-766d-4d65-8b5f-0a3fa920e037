import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:niimbot_ui/widgets/niimbot_input.dart';


import 'package:niimbot_flutter_canvas/utils/string_utils.dart';

/// 输入框内容及label
class InputWidget extends StatefulWidget {
  final String leftTitle;
  final bool readonly;
  final bool allowClear;
  final int? maxLength;
  final String? placeholder;
  final List<TextInputFormatter>? inputFormatters;
  final String clearValue;
  final String? value;
  final ValueChanged<String>? onChange;
  final Function(String)? onBlur;

  const InputWidget({
    required this.leftTitle,
    this.readonly = false,
    this.allowClear = true,
    this.maxLength,
    this.placeholder,
    this.inputFormatters,
    this.clearValue = '',
    this.value,
    this.onChange,
    this.onBlur,
    super.key,
  });

  @override
   createState() => _InputWidgetState();
}

class _InputWidgetState extends State<InputWidget> {
  TextEditingController controller = TextEditingController();

  /// 对输入内容重新赋值并保持光标在最后
  void setValueController(final String e) {
    controller.value = TextEditingValue(
      text: e,
      selection: TextSelection.fromPosition(
        TextPosition(
          affinity: TextAffinity.downstream,
          offset: e.length,
        ),
      ),
    );
  }

  @override
  Widget build(final BuildContext context) {
    final themeColors = NiimbotTheme.of(context).colors;

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Flexible(
            flex: 1,
            child: Text(
              widget.leftTitle,
              style: NiimbotTheme.maybeOf(context)?.typography.body,
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            )),
        widget.readonly == false
            ? NiimbotInput(
                width: 146,
                height: 32,
                allowClear: widget.allowClear,
                maxLength: widget.maxLength,
                value: StringUtils.covertNewLineChar(widget.value),
                placeholder: widget.placeholder ??
                    NiimbotIntl.getIntlMessage("pc0074", "请输入内容"),
                controller: controller,
                inputFormatters: widget.inputFormatters,
                onChanged: widget.onChange,
                onBlur: () {
                  if (widget.value!.isEmpty && !widget.allowClear) {
                    setValueController(widget.clearValue);
                    widget.onChange!(widget.clearValue);
                  }
                  if (widget.onBlur != null) {
                    widget.onBlur!(controller.text ?? '');
                  }
                },
              )
            : Text('${widget.value}',
                style: NiimbotTheme.maybeOf(context)
                    ?.typography
                    .hint
                    ?.copyWith(color: themeColors.textFillColorSecondary))
      ],
    );
  }
}
