import 'package:flutter/material.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:niimbot_ui/widgets/niimbot_checkbox/niimbot_checkbox.dart';

class DateCheckboxWidget extends StatelessWidget {
  final String label;
  final Widget child;
  final double childWidth;
  final TextStyle? LabelStyle;
  final bool showCheckBox;
  final bool checked;
  final bool checkedEnable;
  final ValueChanged<bool>? onCheckedChange;

  const DateCheckboxWidget({
    required this.label,
    required this.child,
    this.childWidth = 160,
    this.LabelStyle,
    this.showCheckBox = true,
    this.checked = false,
    this.checkedEnable = true,
    this.onCheckedChange,
    super.key,
  });

  @override
  Widget build(final BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Row(
            children: [
              if (showCheckBox)
                NiimbotCheckbox(
                  value: checked,
                  enable: checkedEnable,
                  onChanged: (final bool? value) {
                    onCheckedChange?.call(value == true);
                  },
                ),
              InkWell(
                mouseCursor: showCheckBox
                    ? SystemMouseCursors.click
                    : SystemMouseCursors.alias,
                onTap: () {
                  if (showCheckBox && checkedEnable)
                    onCheckedChange?.call(!checked);
                },
                child: Container(
                  padding: EdgeInsetsDirectional.only(
                      start: showCheckBox ? 6 : 0, end: 3),
                  constraints: const BoxConstraints(maxWidth: 110),
                  child: Text(label,
                      style: LabelStyle ??
                          NiimbotTheme.of(context).typography.body),
                ),
              ),
            ],
          ),
        ),
        SizedBox(
          width: childWidth,
          child: child,
        ),
      ],
    );
  }
}
