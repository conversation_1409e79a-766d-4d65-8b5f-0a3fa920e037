import 'package:flutter/material.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_ui/styles/theme.dart';

///内容盒子视图
class ContentBoxWidget extends StatelessWidget {
  final String? title;
  final List<Widget> children;

  const ContentBoxWidget({
    super.key,
    this.title,
    required this.children,
  });

  @override
  Widget build(final BuildContext context) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.only(top: 15),
          alignment: AlignmentDirectional.centerStart,
          child: Text(
            title ?? NiimbotIntl.getIntlMessage("app100000810", "内容"),
            style: NiimbotTheme.of(context).typography.titleStrong,
          ),
        ),
        const SizedBox(height: 12),
        ...children,
      ],
    );
  }
}
