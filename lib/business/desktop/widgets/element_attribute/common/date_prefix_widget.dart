import 'package:flutter/material.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_ui/icons/icons.dart';
import 'package:niimbot_ui/styles/border_radius.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:niimbot_ui/widgets/niimbot_dialog/niimbot_dialog_controller.dart';
import 'package:niimbot_ui/widgets/niimbot_input.dart';
import 'package:niimbot_ui/widgets/niimbot_popover/niimbot_popover_controller.dart';
import 'package:niimbot_ui/widgets/niimbot_tooltip/niimbot_tooltip.dart';
import 'package:niimbot_ui/widgets/niimbot_toast/niimbot_toast_controller.dart';


import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/custom_select_widget.dart';

class DatePrefixWidget extends StatelessWidget {
  final bool disabled;
  final List<String> dataList;
  final String? customPrefix;
  final String? value;
  final ValueChanged<String?> onChanged;
  final Future<bool?> Function(String) onCustomSave;

  const DatePrefixWidget({
    super.key,
    this.disabled = false,
    required this.dataList,
    this.customPrefix,
    this.value,
    required this.onChanged,
    required this.onCustomSave,
  });

  @override
  Widget build(final BuildContext context) {
    final notPrefixText = NiimbotIntl.getIntlMessage('app100001405', '无前缀');

    /// 自定义内容
    onTapCustom() {
      NiimbotPopoverController.removeLast();
      TextEditingController inputController =
          TextEditingController(text: customPrefix);
      Future.delayed(const Duration(milliseconds: 100), () {
        inputController.selection = TextSelection(
          baseOffset: 0,
          extentOffset: inputController.text.length,
        );
      });

      customConfirm(final done) async {
        if (inputController.text.isEmpty) {
          NiimbotToastController()
              .showWarning(NiimbotIntl.getIntlMessage('app01113', '保存内容不能为空'));
          return;
        }
        await onCustomSave(inputController.text);
        onChanged(inputController.text);
        done();
      }

      NiimbotDialogController().show(
        width: 360,
        confirmText: NiimbotIntl.getIntlMessage("app00017", "保存"),
        title: NiimbotIntl.getIntlMessage('app100000966', '自定义'),
        child: Padding(
          padding: const EdgeInsets.symmetric(
            vertical: 24.0,
            horizontal: 20.0,
          ),
          child: NiimbotInput(
            autofocus: true,
            height: 32,
            maxLength: 40,
            textAlignVertical: TextAlignVertical.center,
            defaultValue: customPrefix,
            controller: inputController,
            placeholder: NiimbotIntl.getIntlMessage('app01177', '请输入内容'),
            onSubmitted: (final val) {
              customConfirm(NiimbotPopoverController.removeLast);
            },
          ),
        ),
        onCancel: (final done) => done(),
        onConfirm: customConfirm,
      );
    }

    return CustomSelectWidget(
      disabled: disabled,
      label: value?.isNotEmpty ?? false ? value : notPrefixText,
      child: _DatePrefixChildWidget(
        disabled: disabled,
        customPrefix: customPrefix,
        dataList: dataList,
        value: value,
        onChanged: onChanged,
        onCustom: onTapCustom,
      ),
    );
  }
}

class _DatePrefixChildWidget extends StatefulWidget {
  final bool disabled;
  final List<String> dataList;
  final String? customPrefix;
  final String? value;
  final ValueChanged<String?> onChanged;
  final VoidCallback onCustom;

  const _DatePrefixChildWidget({
    super.key,
    this.disabled = false,
    required this.dataList,
    this.customPrefix,
    this.value,
    required this.onChanged,
    required this.onCustom,
  });

  @override
  State<_DatePrefixChildWidget> createState() => _DatePrefixChildWidgetState();
}

class _DatePrefixChildWidgetState extends State<_DatePrefixChildWidget> {
  final notPrefixText = NiimbotIntl.getIntlMessage('app100001405', '无前缀');
  final customPrefixText = NiimbotIntl.getIntlMessage('app100000966', '自定义');
  String? _selectedValue;

  @override
  void initState() {
    super.initState();
    _initStatePage();
  }

  @override
  void didUpdateWidget(covariant final _DatePrefixChildWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.value != widget.value) {
      _initStatePage();
    }
  }

  _initStatePage() {
    _selectedValue = widget.value?.isNotEmpty ?? false ? widget.value : null;
    if (mounted) {
      setState(() {});
    }
  }

  /// 点击选择
  _onTapSelect(final String? val) {
    NiimbotPopoverController.removeLast();
    _selectedValue = val;
    setState(() {});
    widget.onChanged(val);
  }

  /// 点击自定义选择
  _onTapSelectCustom(final String? val) {
    if (val != null) {
      _onTapSelect(val);
    } else {
      widget.onCustom();
    }
  }

  @override
  Widget build(final BuildContext context) {
    final themeColors = NiimbotTheme.of(context).colors;

    return Container(
      width: 180,
      padding: const EdgeInsets.all(5),
      child: Column(
        children: [
          _SingleSelectWidget(
            label: notPrefixText,
            selected: _selectedValue?.isNotEmpty != true,
            onTap: () => _onTapSelect(null),
          ),
          Divider(color: themeColors.dividerColor),
          ...widget.dataList.map(
            (final e) => _SingleSelectWidget(
              label: e,
              selected: _selectedValue == e,
              onTap: () => _onTapSelect(e),
            ),
          ),
          Divider(color: themeColors.dividerColor),
          _SingleSelectWidget(
            label: widget.customPrefix ?? customPrefixText,
            selected:
                _selectedValue != null && _selectedValue == widget.customPrefix,
            custom: widget.customPrefix?.isNotEmpty == true,
            onTap: () => _onTapSelectCustom(widget.customPrefix),
            onTapCustom: widget.onCustom,
          ),
        ],
      ),
    );
  }
}

class _SingleSelectWidget extends StatelessWidget {
  final String label;
  final bool selected;
  final bool custom;
  final VoidCallback onTap;
  final VoidCallback? onTapCustom;

  const _SingleSelectWidget({
    super.key,
    required this.label,
    this.selected = false,
    this.custom = false,
    required this.onTap,
    this.onTapCustom,
  });

  @override
  Widget build(final BuildContext context) {
    final niimbotTheme = NiimbotTheme.of(context);

    Color fontColorHandle(final bool selected) {
      if (selected) {
        return niimbotTheme.colors.brandColor;
      }
      return niimbotTheme.colors.textFillColorPrimary;
    }

    return InkWell(
      onTap: onTap,
      hoverColor: niimbotTheme.niimbotDropdownMenu.hoverColor,
      highlightColor: niimbotTheme.niimbotDropdownMenu.hoverColor,
      splashColor: Colors.transparent,
      borderRadius: NiimbotRadius.small,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 6.0, horizontal: 6.0),
        child: Row(
          children: [
            selected
                ? NiimbotIcons.check(
                    size: 20,
                    color: niimbotTheme.colors.brandColor,
                  )
                : const SizedBox(width: 20),
            const SizedBox(width: 4.0),
            Expanded(
              flex: 1,
              child: NiimbotToolTip(
                popoverWidth: 200,
                text: label,
                textStyle: NiimbotTheme.of(context)
                    .typography
                    .body
                    ?.copyWith(color: fontColorHandle(selected)),
              ),
            ),
            const SizedBox(width: 4.0),
            if (custom)
              InkWell(
                onTap: onTapCustom,
                child: NiimbotIcons.edit(
                    size: 16,
                    color: niimbotTheme.colors.textFillColorSecondary),
              )
          ],
        ),
      ),
    );
  }
}
