import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:niimbot_ui/styles/theme.dart';

import 'package:niimbot_ui/icons/icons.dart';

const basicInputHeight = 32.0;

class NiimbotInput2 extends HookWidget {
  const NiimbotInput2({
    super.key,

    /// 宽度
    this.width,

    /// 高度
    this.height = basicInputHeight,

    /// 键盘类型
    this.keyboardType,

    /// 文本样式
    this.style,

    /// 提示词样式
    this.hintStyle,

    /// 前缀图标颜色
    this.prefixIconColor,

    /// 前缀图标
    this.prefixIcon,

    /// 默认值
    this.defaultValue,

    /// 当前值
    this.value,

    /// 最小行数
    this.minLines = 1,

    /// 最大行数
    this.maxLines = 1,

    /// 最大输入字符
    this.maxLength,
    this.textAlign,
    this.textAlignVertical,

    /// 是否启用
    this.enabled = true,

    /// 错误提示词
    this.errorText,

    /// 是否可清除
    this.allowClear = true,

    /// 后缀组件
    this.suffix,

    /// 输入框提示词
    this.placeholder,

    /// 输入框控制器
    this.controller,

    /// 限制输入类型
    this.inputFormatters,

    /// 值变化时的回调函数
    this.onChanged,

    /// 清除事件
    this.onClear,

    /// 失去焦点事件
    this.onBlur,

    /// 获取焦点事件
    this.onFocus,

    /// 输入完回车监听
    this.onSubmitted,

    /// 字段聚焦
    this.autofocus = false,

    ///
    this.isEmpty = false,

    /// 隐藏字符
    this.obscureText = false,

    /// 外部焦点
    this.externalFocusNode,
    
    /// 是否忽略外部value变化
    this.ignoreValueChanges = false,
    
    /// 强制获取焦点
    this.forceFocus = false,
    
    /// 调试标识
    this.debugLabel,
  })  : assert(maxLines == null || maxLines > 0),
        assert(minLines == null || minLines > 0),
        assert(
          (maxLines == null) || (minLines == null) || (maxLines >= minLines),
          "minLines can't be greater than maxLines",
        ),
        assert(!obscureText || maxLines == 1,
            'Obscured fields cannot be multiline.'),
        assert(maxLength == null ||
            maxLength == TextField.noMaxLength ||
            maxLength > 0);

  final double? width;
  final double? height;
  final TextInputType? keyboardType;
  final TextStyle? style;
  final TextStyle? hintStyle;
  final Color? prefixIconColor;
  final Widget? prefixIcon;
  final String? defaultValue;
  final String? value;
  final int? minLines;
  final int? maxLines;
  final int? maxLength;
  final TextAlign? textAlign;
  final TextAlignVertical? textAlignVertical;
  final bool enabled;
  final String? errorText;
  final bool allowClear;
  final Widget? suffix;
  final String? placeholder;
  final TextEditingController? controller;
  final List<TextInputFormatter>? inputFormatters;
  final ValueChanged<String>? onChanged;
  final VoidCallback? onClear;
  final Function()? onBlur;
  final Function()? onFocus;
  final ValueChanged<String>? onSubmitted;
  final bool autofocus;
  final bool isEmpty;
  final bool obscureText;
  final FocusNode? externalFocusNode;
  final bool ignoreValueChanges;
  final bool forceFocus;
  final String? debugLabel;

  double? get _inputHeight =>
      maxLines == null || maxLines! < 2 ? height : 24.0 * maxLines!;

  @override
  Widget build(BuildContext context) {
    print("====================NiimbotInput2 inner forcusTest========================>");

    final niimbotTheme = NiimbotTheme.of(context);
    final themeColors = niimbotTheme.colors;
    final focusNode = externalFocusNode ?? useFocusNode();
    final borderColor = useState<Color>(themeColors.borderColorNormal);
    final textController = controller ?? useTextEditingController();
    final isUserEditing = useState(false);
    final uniqueId = useRef(DateTime.now().millisecondsSinceEpoch.toString());

    // 调试日志函数 - 仅在有debugLabel时输出
    void logDebug(String message) {
      if (debugLabel != null) {
        print('[$debugLabel] $message (id: ${uniqueId.value})');
      }
    }

    onCancel() {
      // 保证在组件build的第一帧时才去触发取消清空内
      WidgetsBinding.instance.addPostFrameCallback((_) {
        logDebug('清除内容');
        textController.clear();
      });
      onClear?.call();
      onChanged?.call('');
    }

    final isShowError = useMemoized(() {
      if ((!focusNode.hasFocus &&
              textController.text.isNotEmpty &&
              errorText != null) ||
          (!focusNode.hasFocus &&
              textController.text.isEmpty &&
              isEmpty == true)) {
        return true;
      }
      return false;
    }, [
      focusNode.hasFocus,
      textController.text.isNotEmpty,
      errorText,
      isEmpty
    ]);

    useEffect(() {
      if (defaultValue != null && (textController.text.isEmpty || textController.text != defaultValue)) {
        logDebug('设置默认值: $defaultValue');
        textController.text = defaultValue ?? '';
      }
      return null;
    }, [defaultValue]);

    // 处理外部value变更
    useEffect(() {
      if (value != null && !ignoreValueChanges && !isUserEditing.value) {
        if (value != textController.text) {
          logDebug('外部value变更: $value');
          
          // 保存当前选择状态
          final currentSelection = textController.selection;
          
          textController.value = TextEditingValue(
            text: value!,
            selection: currentSelection.isValid && currentSelection.baseOffset <= value!.length
                ? currentSelection  // 尝试保持原始选择
                : TextSelection.fromPosition(
                    TextPosition(
                      affinity: TextAffinity.downstream,
                      offset: value!.length,
                    ),
                  ),
          );
        }
      }
      return null;
    }, [value]);

    // 处理焦点和初始化
    useEffect(() {
      logDebug('组件挂载');
      
      // 处理自动聚焦
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (autofocus || forceFocus) {
          logDebug('自动获取焦点');
          FocusScope.of(context).requestFocus(focusNode);
        }
      });
      
      // 焦点监听器
      focusNode.addListener(() {
        if (focusNode.hasFocus) {
          logDebug('获取焦点');
          onFocus?.call();
          isUserEditing.value = true;
        } else {
          logDebug('失去焦点');
          onBlur?.call();
          // 延迟重置编辑状态，避免在状态更新期间相互干扰
          Future.delayed(Duration(milliseconds: 50), () {
            isUserEditing.value = false;
          });
        }
      });
      
      // 清理函数
      return () {
        logDebug('组件卸载');
        // 如果是内部创建的focusNode，这里不需要清理
        // 因为hooks会自动处理
      };
    }, []);

    // 内部变更处理
    void handleTextChanged(String text) {
      logDebug('文本变更: $text');
      
      isUserEditing.value = true;
      
      // 先触发回调，让外部有机会更新值
      onChanged?.call(text);
      
      // 延迟重置编辑状态
      Future.delayed(Duration(milliseconds: 50), () {
        isUserEditing.value = false;
      });
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SizedBox(
            width: width,
            height: _inputHeight,
            child: MouseRegion(
              onHover: (event) {
                borderColor.value = themeColors.borderColorHover;
              },
              onExit: (event) {
                borderColor.value = themeColors.borderColorNormal;
              },
              child: GestureDetector(
                onDoubleTap: () {
                  textController.selection = TextSelection(
                      baseOffset: 0, extentOffset: textController.text.length);
                  FocusScope.of(context).requestFocus(focusNode);
                },
                child: TextField(
                  keyboardType: keyboardType,
                  enabled: enabled,
                  controller: textController,
                  minLines: minLines,
                  inputFormatters: inputFormatters,
                  maxLines: maxLines,
                  maxLength: maxLength,
                  obscureText: obscureText,
                  onChanged: handleTextChanged,
                  onSubmitted: onSubmitted,
                  style: style ??
                      TextStyle(
                          height: 1.25,
                          fontSize: NiimbotTheme.of(context)
                              .typography
                              .body
                              ?.fontSize,
                          color: enabled
                              ? themeColors.textFillColorPrimary
                              : themeColors.textFillColorQuarternary,
                          fontFamily: NiimbotTheme.of(context)
                              .typography
                              .body
                              ?.fontFamily),
                  cursorWidth: 1,
                  cursorColor: themeColors.textFillColorPrimary,
                  textAlign: textAlign ?? TextAlign.start,
                  textAlignVertical:
                      textAlignVertical ?? TextAlignVertical.center,
                  decoration: InputDecoration(
                    isCollapsed: true,
                    prefixIconColor:
                        prefixIconColor ?? themeColors.textFillColorSecondary,
                    prefixIcon: prefixIcon != null
                        ? Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 6.0),
                            child: prefixIcon)
                        : const SizedBox(width: 8),
                    prefixIconConstraints: const BoxConstraints(),
                    counterText: "",
                    hintText: placeholder,
                    hintStyle: hintStyle ??
                        TextStyle(
                            height: 1.25,
                            fontSize: NiimbotTheme.of(context)
                                .typography
                                .body
                                ?.fontSize,
                            color: themeColors.textFillColorSecondary,
                            fontFamily: NiimbotTheme.of(context)
                                .typography
                                .body
                                ?.fontFamily),
                    suffixIcon: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (allowClear &&
                            textController.text.isNotEmpty == true &&
                            enabled)
                          SizedBox(
                              width: 28.0,
                              child: IconButton(
                                style: const ButtonStyle(),
                                alignment: AlignmentDirectional.centerEnd,
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 6.0),
                                icon: NiimbotIcons.close(
                                    size: 18.0,
                                    color: niimbotTheme
                                        .colors.textFillColorSecondary),
                                hoverColor: Colors.transparent,
                                splashColor: Colors.transparent,
                                highlightColor: Colors.transparent,
                                onPressed: onCancel,
                              )),
                        suffix != null
                            ? Container(child: suffix)
                            : const SizedBox(),
                      ],
                    ),
                    suffixIconConstraints: BoxConstraints(
                        minHeight: _inputHeight ?? 32, minWidth: 6),
                    contentPadding: maxLines == null || maxLines! < 2
                        ? const EdgeInsets.symmetric(vertical: 7)
                        : EdgeInsets.zero,
                    filled: true,
                    fillColor: enabled
                        ? themeColors.solidBackgroundFillColorBase
                        : themeColors.solidBackgroundFillColorSecondary,
                    hoverColor: Colors.transparent,
                    enabledBorder: OutlineInputBorder(
                        borderSide: BorderSide(
                            color: isShowError
                                ? themeColors.brandColor
                                : focusNode.hasFocus
                                    ? themeColors.borderColorEnter
                                    : borderColor.value),
                        borderRadius:
                            const BorderRadius.all(Radius.circular(6.0))),
                    focusedBorder: OutlineInputBorder(
                        borderSide:
                            BorderSide(color: themeColors.borderColorEnter),
                        borderRadius:
                            const BorderRadius.all(Radius.circular(6.0))),
                    disabledBorder: OutlineInputBorder(
                        borderSide:
                            BorderSide(color: themeColors.borderColorNormal),
                        borderRadius:
                            const BorderRadius.all(Radius.circular(6.0))),
                    errorBorder: OutlineInputBorder(
                        borderSide:
                            BorderSide(color: themeColors.errorColorNormal),
                        borderRadius:
                            const BorderRadius.all(Radius.circular(6.0))),
                    focusedErrorBorder: OutlineInputBorder(
                        borderSide:
                            BorderSide(color: themeColors.errorColorEnter),
                        borderRadius:
                            const BorderRadius.all(Radius.circular(6.0))),
                  ),
                  focusNode: focusNode,
                ),
              ),
            )),
        if (isShowError)
          Container(
              padding: const EdgeInsets.only(top: 3),
              child: Row(children: [
                NiimbotIcons.warning(size: 13, color: themeColors.brandColor),
                const SizedBox(width: 2),
                Text(
                    '${errorText == null && isEmpty == true ? "字段不能为空" : errorText}',
                    style: niimbotTheme.typography.hint
                        ?.copyWith(color: themeColors.brandColor))
              ]))
      ],
    );
  }
}
