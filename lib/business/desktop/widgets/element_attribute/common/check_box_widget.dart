import 'package:flutter/material.dart';
import 'package:niimbot_ui/icons/icons.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:niimbot_ui/widgets/niimbot_checkbox/niimbot_checkbox.dart';
import 'package:niimbot_ui/widgets/niimbot_popover/niimbot_popover.dart';
import 'package:niimbot_ui/widgets/niimbot_tooltip/niimbot_tooltip.dart';
import 'package:niimbot_ui/widgets/niimbot_switch.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/over_white_tips_widget.dart';

///复选框
class CheckBoxWidget extends StatefulWidget {
  final String title;
  final bool? value;
  final bool enable;
  final ValueChanged<bool>? valueChanged;
  final bool isCheckbox;
  final bool isShowHelpIcon;
  final bool isShowVipIcon;
  final bool isOverWhite;
  final double helpPopoverWidth;
  final Widget? popoverWidget;
  final NiimbotPopoverPositionEnum? popoverPosition;

  const CheckBoxWidget({
    required this.title,
    this.value = false,
    this.enable = true,
    this.isCheckbox = true,
    this.isShowHelpIcon = false,
    this.isShowVipIcon = false,
    this.valueChanged,
    this.isOverWhite = false,
    this.popoverWidget,
    this.helpPopoverWidth = 220,
    this.popoverPosition,
    super.key,
  });

  @override
  State<CheckBoxWidget> createState() => _CheckBoxWidgetState();
}

class _CheckBoxWidgetState extends State<CheckBoxWidget> {
  final GlobalKey<NiimbotPopoverState> _globalKey = GlobalKey();

  @override
  Widget build(final BuildContext context) {
    final themeColors = NiimbotTheme.of(context).colors;

    return Stack(
      clipBehavior: Clip.none,
      children: [
        Row(
          children: [
            if (widget.isCheckbox)
              NiimbotCheckbox(
                value: widget.value,
                enable: widget.enable,
                onChanged: (final bool? value) {
                  if (widget.valueChanged != null) {
                    _click(context);
                  }
                },
              ),
            InkWell(
              mouseCursor: widget.isCheckbox
                  ? SystemMouseCursors.click
                  : SystemMouseCursors.alias,
              onTap: () {
                if (!widget.enable || !widget.isCheckbox) {
                  return;
                }
                _click(context);
              },
              child: Container(
                padding: EdgeInsetsDirectional.only(start: widget.isCheckbox ? 8 : 0),
                child: NiimbotToolTip(
                  text: widget.title,
                  textStyle: NiimbotTheme.maybeOf(context)?.typography.body,
                  position: NiimbotPopoverPositionEnum.top,
                ),
              ),
            ),
            if (widget.isShowVipIcon)
              Row(
                children: [
                  const SizedBox(
                    width: 4,
                  ),
                  NiimbotIcons.vipLabel(
                    size: 16,
                  ),
                ],
              ),
            if (widget.isShowHelpIcon)
              NiimbotPopover(
                  key: _globalKey,
                  showArrow: true,
                  position:
                  widget.popoverPosition ?? NiimbotPopoverPositionEnum.top,
                  trigger: NiimbotPopoverTrigger.hover,
                  content: SizedBox(
                    width: widget.helpPopoverWidth,
                    child: widget.popoverWidget ?? const OverWhiteTipsWidget(),
                  ),
                  child: SizedBox(
                    width: 24,
                    child: NiimbotIcons.help(
                      size: 16,
                      color: themeColors.textFillColorTertiary,
                    ),
                  )),
          ],
        ),
        if (!widget.isCheckbox) Positioned.directional(
          textDirection: Directionality.of(context),
          top: 0,
          end: -10,
          bottom: 0,
          child: NiimbotSwitch(
            scale: 0.6,
            value: widget.value,
            enable: widget.enable,
            onChanged: (final bool? value) {
              if (widget.valueChanged != null) {
                _click(context);
              }
            },
          ),
        ),
      ],
    );
  }

  void _click(final context) {
    if (widget.isOverWhite && widget.isShowHelpIcon && !widget.value!) {
      _showFirstHighLightTip();
    }
    widget.valueChanged!(!widget.value!);
  }

  _showFirstHighLightTip() {
    SharedPreferences.getInstance().then((final value) {
      final hasfirstTip = value.getBool("has_first_use_hight_light_tip");
      if (hasfirstTip == null || !hasfirstTip) {
        _globalKey.currentState?.show();
        value.setBool("has_first_use_hight_light_tip", true);
      }
    });
  }
}
