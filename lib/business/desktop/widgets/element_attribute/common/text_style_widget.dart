import 'package:flutter/cupertino.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:niimbot_ui/icons/icons.dart';
import 'package:niimbot_ui/model/niimbot_mouse_enum.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:niimbot_ui/widgets/niimbot_checkbox/niimbot_checkbox_button.dart';

class TextStyleWidget extends StatelessWidget {
  final List<NetalTextFontStyle> value;
  final ValueChanged<List<NetalTextFontStyle>>? valueChanged;
  final List<NetalTextFontStyle>? disabledList;

  const TextStyleWidget({
    required this.value,
    required this.valueChanged,
    this.disabledList,
    super.key,
  });

  @override
  Widget build(final BuildContext context) {
    final themeColors = NiimbotTheme.of(context).colors;

    return NiimbotCheckBoxButton<NetalTextFontStyle>(
      itemBuild: (final NetalTextFontStyle item, final NiimbotMouseEnum mouseType) {
        Color color = mouseType == NiimbotMouseEnum.active
            ? themeColors.brandColor
            : themeColors.textFillColorPrimary;
        if (item == NetalTextFontStyle.bold) {
          return NiimbotIcons.bold(
            color: color,
            size: 18.00,
          );
        } else if (item == NetalTextFontStyle.underline) {
          return NiimbotIcons.underline(
            color: color,
            size: 18.00,
          );
        } else {
          return NiimbotIcons.italic(
            color: color,
            size: 18.00,
          );
        }
      },
      disabledList: disabledList,
      value: value,
      valueChanged: (final List<NetalTextFontStyle> val) {
        valueChanged?.call([...val]);
      },
      children: const [
        NetalTextFontStyle.bold,
        NetalTextFontStyle.underline,
        NetalTextFontStyle.italic
      ],
    );
  }
}
