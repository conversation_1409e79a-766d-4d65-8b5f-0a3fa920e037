import 'package:flutter/material.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_ui/widgets/niimbot_input.dart';

import 'package:niimbot_flutter_canvas/utils/string_utils.dart';

class ContentSourceWidget extends StatelessWidget {
  final String? value;
  final ValueChanged<String>? onValueChange;

  const ContentSourceWidget({
    super.key,
    this.value,
    this.onValueChange,
  });

  @override
  Widget build(final BuildContext context) {
    return Column(
      children: [
        Column(
          children: [
            NiimbotInput(
              width: 270,
              minLines: 3,
              maxLines: 3,
              allowClear: false,
              value: StringUtils.covertNewLineChar(value),
              placeholder: NiimbotIntl.getIntlMessage("pc0020", "输入内容"),
              onChanged: onValueChange,
            ),
            const SizedBox(height: 8),
          ],
        )
      ],
    );
  }
}
