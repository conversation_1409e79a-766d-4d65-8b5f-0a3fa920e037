import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:niimbot_flutter_canvas/business/desktop/models/canvas_element_text_content_choose_model.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/canvas_desktop_common_widget/canvas_tabs_child_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/custom_select_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/date_time_refresh.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_template/models/elements/element_enum.dart';
import 'package:niimbot_ui/model/niimbot_dropdown_model.dart';
import 'package:niimbot_ui/model/niimbot_format_model.dart';
import 'package:niimbot_ui/styles/border_radius.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:niimbot_ui/widgets/niimbot_button.dart';
import 'package:niimbot_ui/widgets/niimbot_date_picker/niim_calendar_date_picker.dart';
import 'package:niimbot_ui/widgets/niimbot_date_picker/niimbot_date_time_picker.dart';
import 'package:niimbot_ui/widgets/niimbot_dropdown_menu.dart';
import 'package:niimbot_ui/widgets/niimbot_popover/niimbot_popover_controller.dart';
import 'package:niimbot_ui/widgets/niimbot_tabs/niimbot_tabs.dart';

enum DateSelectMode { date, time }

class DateSelectWidget<T> extends StatelessWidget {
  final bool disabled;
  final bool realtime;
  final String? label;
  final int value;
  final bool use12Hours;
  final T format;
  final Function({
    required T format,
    required bool use12Hours,
    int? time,
  }) onChanged;

  const DateSelectWidget({
    super.key,
    this.disabled = false,
    this.realtime = false,
    this.label,
    required this.value,
    this.use12Hours = false,
    required this.format,
    required this.onChanged,
  });

  @override
  Widget build(final BuildContext context) {
    return CustomSelectWidget(
      disabled: disabled,
      label: label,
      child: _DateSelectContent<T>(
        initialDate: DateTime.fromMillisecondsSinceEpoch(value),
        format: format,
        use12Hours: use12Hours,
        realtime: realtime,
        onChanged: onChanged,
      ),
    );
  }
}

/// 弹出层
class _DateSelectContent<T> extends StatefulWidget {
  final DateTime initialDate;
  final T format;
  final bool use12Hours;
  final bool realtime;
  final Function({required T format, required bool use12Hours, int? time})
      onChanged;

  const _DateSelectContent({
    super.key,
    required this.initialDate,
    required this.format,
    this.use12Hours = false,
    required this.realtime,
    required this.onChanged,
  });

  @override
  State<_DateSelectContent<T>> createState() => _DateSelectContentState();
}

class _DateSelectContentState<T> extends State<_DateSelectContent<T>>
    with TickerProviderStateMixin {
  late TabController _tabController;
  int _currentIndex = 0;
  bool _use12Hours = false;
  late DateTime _currentDate;
  late DateTime _panelDate;
  late T _currentFormat;

  bool get modeIsDate => _currentFormat is! ElementTimeFormat;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _currentDate = widget.realtime ? DateTime.now() : widget.initialDate;
    _panelDate = _currentDate;
    _currentFormat = widget.format;
    _use12Hours = widget.use12Hours;
  }

  @override
  void didUpdateWidget(covariant final _DateSelectContent<T> oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.realtime != widget.realtime) {
      _currentDate = widget.realtime ? DateTime.now() : widget.initialDate;
      _panelDate = _currentDate;
      setState(() {});
    }
    if (oldWidget.initialDate != widget.initialDate) {
      _currentDate = widget.initialDate;
      _panelDate = widget.initialDate;
      setState(() {});
    }
    if (oldWidget.format != widget.format) {
      _currentFormat = widget.format;
      setState(() {});
    }
    if (oldWidget.use12Hours != widget.use12Hours) {
      _use12Hours = widget.use12Hours;
    }
  }

  @override
  void dispose() {
    super.dispose();
    _tabController.dispose();
  }

  /// 日期选择变更
  _onDateChangeDate(final DateTime date) {
    String dateTime = DateFormat('yyyy-MM-dd').format(date);
    String time = DateFormat('HH:mm:ss').format(widget.initialDate);
    final dateValue = DateTime.parse('$dateTime $time');
    _panelDate = dateValue;
  }

  /// 24小时制变更
  _use12HoursChanged(final bool checked) {
    _use12Hours = checked;
    setState(() {});
  }

  /// 时间选择变更
  _onTimeChanged(final DateTime time) {
    _panelDate = time;
  }

  /// 格式变更
  _onFormatChanged(final NiimbotDropDownModel model) {
    _currentFormat = model.value;
    setState(() {});
  }

  _onPressedNow() {
    _panelDate = DateTime.now();
    _onConfirm();
  }

  _onConfirm() {
    _currentDate = _panelDate;
    widget.onChanged.call(
      format: _currentFormat,
      use12Hours: _use12Hours,
      time: _currentDate.millisecondsSinceEpoch,
    );
    NiimbotPopoverController.removeLast();
  }

  @override
  Widget build(final BuildContext context) {
    final themeColor = NiimbotTheme.of(context).colors;
    double width = modeIsDate ? 280 : 240;
    double height = modeIsDate ? 350 : 332;

    /// 选择面板内容
    Widget selectPanelWidget() {
      if (modeIsDate) {
        return !widget.realtime
            ? NiimCalendarDatePicker(
                initialDate: _currentDate,
                firstDate: DateTime(1900),
                lastDate: DateTime(3001, 1, 0),
                onDateChanged: _onDateChangeDate,
              )
            : DateTimeRefresh(
                format: (_currentFormat as ElementDateFormat).value,
              );
      } else {
        return !widget.realtime
            ? DateTimePicker(
                showAction: false,
                format: NiimbotTimePickerFormat.HMS,
                value: _panelDate,
                use12Hours: _use12Hours,
                use12HoursChanged: _use12HoursChanged,
                onPanelChanged: _onTimeChanged,
              )
            : DateTimeRefresh(
                format: (_currentFormat as ElementTimeFormat).value,
                use12Hours: _use12Hours,
                use12HoursChanged: _use12HoursChanged,
              );
      }
    }

    /// 日期时间格式
    List<NiimbotDropDownModel> formatList = (modeIsDate
            ? CanvasElementTextContentChooseModel.dateFormatList(
                NiimbotIntl.getCurrentLocale().languageCode)
            : CanvasElementTextContentChooseModel.dateTimeFormatList())
        .map((final e) {
      String format = e.value is ElementDateFormat
          ? (e.value as ElementDateFormat).value
          : (e.value as ElementTimeFormat).value;
      String label = DateFormat(
              format, e.value == ElementDateFormat.YMDE_C ? 'zh_CN' : null)
          .format(_panelDate);
      return NiimbotDropDownModel(value: e.value, label: label);
    }).toList();

    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        borderRadius: NiimbotRadius.middle,
        color: themeColor.systemFillColorWhite,
      ),
      child: Column(
        children: [
          NiimbotTabBar(
            isScrollable: true,
            dividerColor: Colors.transparent,
            onTap: (final int index) {
              _currentIndex = index;
              setState(() {});
            },
            controller: _tabController,
            tabs: [
              Tab(
                height: 40,
                // maxWidth: 108,
                child: CanvasTabsChildWidget(
                  text: modeIsDate
                      ? NiimbotIntl.getIntlMessage('app01020', '日期')
                      : NiimbotIntl.getIntlMessage('app00010', '时间'),
                  isSelect: _currentIndex == 0,
                ),
              ),
              Tab(
                height: 40,
                // maxWidth: 150,
                child: CanvasTabsChildWidget(
                  text: NiimbotIntl.getIntlMessage('app01398', '格式'),
                  isSelect: _currentIndex == 1,
                ),
              ),
            ],
          ),
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                border: Border(
                  top: BorderSide(
                    color: themeColor.dividerColor,
                    width: 1.0,
                  ),
                ),
              ),
              child: TabBarView(
                controller: _tabController,
                children: [
                  _currentIndex == 0 ? selectPanelWidget() : Container(),
                  NiimbotDropDownGroupWidget(
                    itemWidth: width,
                    dataList: formatList,
                    value: _currentFormat,
                    onChange: _onFormatChanged,
                    style: NiimbotTheme.of(context).niimbotDropdownMenu,
                    needChooseIcon: true,
                  ),
                ],
              ),
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 10.0),
            height: 40,
            decoration: BoxDecoration(
                border: Border(
              top: BorderSide(
                color: NiimbotTheme.of(context).colors.dividerColor,
                width: 1.0,
              ),
            )),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                (_currentIndex == 0 && !widget.realtime)
                    ? NiimbotButton(
                        height: 28,
                        type: ButtonType.link,
                        text: modeIsDate
                            ? NiimbotIntl.getIntlMessage('pc0071', '今天')
                            : NiimbotIntl.getIntlMessage('pc0072', '此刻'),
                        onPressed: _onPressedNow,
                      )
                    : Container(),
                NiimbotButton(
                  text: NiimbotIntl.getIntlMessage('app00048', '确定'),
                  height: 28,
                  onPressed: _onConfirm,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
