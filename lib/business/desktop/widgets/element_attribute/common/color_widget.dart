import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_ui/icons/icons.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:niimbot_ui/widgets/niimbot_popover/niimbot_popover.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ColorWidget extends StatefulWidget {
  ///0黑 1红
  final String? title;
  final int? value;
  final Color? color;
  final void Function(int channel, Color color)? onChange;

  const ColorWidget({
    this.title,
    this.value,
    required this.color,
    this.onChange,
    super.key,
  });

  @override
  State<ColorWidget> createState() => _ColorWidgetState();
}

class _ColorWidgetState extends State<ColorWidget> {
  final GlobalKey<NiimbotPopoverState> _globalKey = GlobalKey();

  @override
  Widget build(final BuildContext context) {
    final themeColors = NiimbotTheme.of(context).colors;
    final consumablesColor = context
        .select<CanvasStore, List<Color>>((final v) => v.consumablesColor);

    final printColor =
        context.select<CanvasStore, Color?>((final v) => v.printColor);

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            Container(
              margin: const EdgeInsetsDirectional.only(end: 4),
              child: Text(
                widget.title ??
                    NiimbotIntl.getIntlMessage("app100000419", "颜色"),
                style: NiimbotTheme.of(context).typography.body,
              ),
            ),
            NiimbotPopover(
              key: _globalKey,
              trigger: NiimbotPopoverTrigger.hover,
              showArrow: true,
              position: NiimbotPopoverPositionEnum.bottom,
              content: Container(
                constraints: const BoxConstraints(maxWidth: 240),
                margin:
                    const EdgeInsets.symmetric(horizontal: 17, vertical: 12),
                child: Text(
                  NiimbotIntl.getIntlMessage('pc0018', '需要双色打印机和双色标签纸搭配使用'),
                  style: NiimbotTheme.of(context).typography.hint,
                ),
              ),
              child: NiimbotIcons.help(
                size: 16,
                color: themeColors.textFillColorTertiary,
              ),
            ),
          ],
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: _buildColorButtons(consumablesColor, printColor),
        )
      ],
    );
  }

  void _showFirstMultiColorTip() {
    SharedPreferences.getInstance().then((final value) {
      final hasfirstTip = value.getBool("has_first_use_multi_color_tip");
      if (hasfirstTip == null || !hasfirstTip) {
        _globalKey.currentState?.show();
        value.setBool("has_first_use_multi_color_tip", true);
      }
    });
  }

  List<Widget> _buildColorButtons(
      final List<Color> consumablesColor, final Color? printColor) {
    // 判断是否为 [黑, 红] 渲染红黑选择按钮
    final isDualColor =
        context.select<CanvasStore, bool>((final v) => v.isRedAndBlack);

    final isMoreThanOne = consumablesColor.length > 1;
    if (isDualColor) {
      // 只渲染一个双色按钮,不开启选中模式
      return [
        _DualColorRadioButton(
          leftColor: Colors.black,
          rightColor: Colors.red,
          // onTap: () {
          //   _showFirstMultiColorTip();
          //   widget.onChange?.call(0, consumablesColor[0]);
          // },
        )
      ];
    }
    // 原有逻辑
    return consumablesColor.mapIndexed((final int index, final Color color) {
      return _ColorRadioButton(
        checked: color == (printColor ?? widget.color),
        isLast: index == (consumablesColor.length - 1),
        color: color,
        onTap: isMoreThanOne
            ? () {
                if (index != 0) {
                  _showFirstMultiColorTip();
                }
                widget.onChange?.call(index, color);
              }
            : null,
      );
    }).toList();
  }
}

///颜色选择按钮,单个时候禁止点击
class _ColorRadioButton extends StatelessWidget {
  final bool checked;
  final Color color;
  final VoidCallback? onTap;
  final bool isLast;

  const _ColorRadioButton({
    required this.checked,
    required this.color,
    required this.isLast,
    this.onTap,
  });

  @override
  Widget build(final BuildContext context) {
    final themeColors = NiimbotTheme.of(context).colors;
    return InkWell(
      onTap: onTap,
      mouseCursor: onTap != null
          ? SystemMouseCursors.click
          : SystemMouseCursors.forbidden,
      child: Stack(
        children: [
          Container(
            width: 24,
            height: 24,
            margin: EdgeInsets.only(right: isLast ? 0 : 4),
            decoration: BoxDecoration(
                color: themeColors.systemFillColorWhite,
                borderRadius: const BorderRadius.all(Radius.circular(12)),
                border: checked
                    ? Border.all(color: themeColors.brandColor, width: 2)
                    : null),
          ),
          Positioned(
            left: 3,
            top: 3,
            child: Container(
              width: 18,
              height: 18,
              decoration: BoxDecoration(
                color: color,
                borderRadius: const BorderRadius.all(Radius.circular(9)),
              ),
            ),
          )
        ],
      ),
    );
  }
}

// 拼色 widget
class _DualColorRadioButton extends StatelessWidget {
  final Color leftColor;
  final Color rightColor;
  final VoidCallback? onTap;

  const _DualColorRadioButton({
    this.leftColor = Colors.black,
    this.rightColor = Colors.red,
    this.onTap,
  });

  @override
  Widget build(final BuildContext context) {
    final themeColors = NiimbotTheme.of(context).colors;
    return InkWell(
      onTap: onTap,
      mouseCursor: onTap != null
          ? SystemMouseCursors.click
          : SystemMouseCursors.forbidden,
      child: Container(
        width: 24,
        height: 24,
        decoration: BoxDecoration(
          color: themeColors.systemFillColorWhite,
          borderRadius: const BorderRadius.all(Radius.circular(12)),
          border: Border.all(
            color: themeColors.brandColor,
            width: 2,
          ),
        ),
        child: CustomPaint(
          painter: _DualColorCirclePainter(leftColor, rightColor),
          child: const SizedBox(width: 24, height: 24),
        ),
      ),
    );
  }
}

class _DualColorCirclePainter extends CustomPainter {
  final Color leftColor;
  final Color rightColor;

  _DualColorCirclePainter(this.leftColor, this.rightColor);

  @override
  void paint(final Canvas canvas, final Size size) {
    final radius = 9.0;
    final center = Offset(size.width / 2, size.height / 2);
    final rect = Rect.fromCircle(center: center, radius: radius);
    // 左半圆
    final leftPaint = Paint()..color = leftColor;
    canvas.drawArc(rect, 0.5 * 3.1415926, 3.1415926, true, leftPaint);
    // 右半圆
    final rightPaint = Paint()..color = rightColor;
    canvas.drawArc(rect, -0.5 * 3.1415926, 3.1415926, true, rightPaint);
  }

  @override
  bool shouldRepaint(covariant final CustomPainter oldDelegate) => false;
}
