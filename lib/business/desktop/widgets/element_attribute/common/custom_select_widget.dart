import 'package:flutter/material.dart';
import 'package:niimbot_ui/styles/border_radius.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:niimbot_ui/widgets/niimbot_popover/niimbot_popover.dart';
import 'package:niimbot_ui/widgets/niimbot_select.dart';

class CustomSelectWidget extends StatefulWidget {
  final bool disabled;
  final Widget child;
  final String? label;

  const CustomSelectWidget({
    super.key,
    this.disabled = false,
    required this.child,
    this.label,
  });

  @override
  State<CustomSelectWidget> createState() => _CustomSelectWidgetState();
}

class _CustomSelectWidgetState extends State<CustomSelectWidget> {
  final GlobalKey<NiimbotPopoverState> _globalKey = GlobalKey();
  bool _dropdownVisible = false;

  _onVisibleChange(final bool visible) {
    _dropdownVisible = visible;
    setState(() {});
  }

  _onTap() {
    if (_dropdownVisible) {
      _globalKey.currentState?.hide();
    }
  }

  @override
  Widget build(final BuildContext context) {
    final themeColors = NiimbotTheme.of(context).colors;

    return NiimbotPopover(
      key: _globalKey,
      disabled: widget.disabled,
      position: NiimbotPopoverPositionEnum.bottomEnd,
      onVisibleChange: _onVisibleChange,
      content: Material(
          color: themeColors.solidBackgroundFillColorBase,
          borderRadius: NiimbotRadius.middle,
          child: widget.child,
      ),
      child: GestureDetector(
        onTap: _dropdownVisible ? _onTap : null,
        child: CommonBorderChooseWidget(
          dropdownVisible: _dropdownVisible,
          currentLabel: widget.label,
          disabled: widget.disabled,
        ),
      ),
    );
  }
}
