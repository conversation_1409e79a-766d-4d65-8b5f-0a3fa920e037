import 'dart:async';
import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:image_compression/image_compression.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_qbarcode/niimbot_zxing_result.dart';
import 'package:niimbot_ui/icons/icons.dart';
import 'package:niimbot_ui/styles/border_radius.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:niimbot_ui/widgets/niimbot_button.dart';
import 'package:niimbot_ui/widgets/niimbot_toast/niimbot_toast.dart';
import 'package:niimbot_qbarcode/niimbot_qbarcode.dart' as niimbot_qbarcode;
import 'package:niimbot_ui/widgets/niimbot_toast/niimbot_toast_controller.dart';

final Logger _logger = Logger("CanvasCommonUploadQrcodeWidget", on: kDebugMode);

class UploadQRCodeWidget extends StatefulWidget {
  final ValueChanged<Map<String, dynamic>>? qrcodeRead;
  final Uint8List? currentQrImageData;

  const UploadQRCodeWidget(
      {super.key, this.qrcodeRead, this.currentQrImageData});

  @override
  State<UploadQRCodeWidget> createState() => _UploadQRCodeWidgetState();
}

class _UploadQRCodeWidgetState extends State<UploadQRCodeWidget> {
  ///控制不能同时弹出两个
  bool _isChooseQrImageShow = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(final BuildContext context) {
    final themeColors = NiimbotTheme.of(context).colors;
    return Container(
      height: 196,
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: NiimbotRadius.small,
        border: Border.all(
          color: themeColors.borderColorNormal,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Expanded(
            flex: 1,
            child: _isHaveQrImage()
                ? Container(
                    margin: const EdgeInsets.all(12),
                    child: Image.memory(
                      widget.currentQrImageData!,
                      gaplessPlayback: true,
                    ))
                : Container(
                    height: 196,
                    alignment: Alignment.center,
                    child: InkWell(
                      child: NiimbotIcons.add(
                        color: themeColors.textFillColorTertiary,
                        size: 45.00,
                      ),
                      onTap: () {
                        upLoadQrCodeImage();
                      },
                    ),
                  ),
          ),
          Container(
            margin: const EdgeInsets.only(
              bottom: 16,
            ),
            child: NiimbotButton(
                width: 220,
                height: 30,
                dense: true,
                type: ButtonType.secondary,
                text: _isHaveQrImage()
                    ? NiimbotIntl.getIntlMessage('pc0022', '重新上传二维码')
                    : NiimbotIntl.getIntlMessage('pc0023', '上传二维码'),
                onPressed: () {
                  upLoadQrCodeImage();
                }),
          ),
        ],
      ),
    );
  }

  bool _isHaveQrImage() {
    return widget.currentQrImageData != null;
  }

  Future upLoadQrCodeImage() async {
    if (_isChooseQrImageShow) {
      return;
    }
    _isChooseQrImageShow = true;
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      lockParentWindow: true,
      allowedExtensions: [
        'jpg',
        'png',
        'jpeg',
        'heif',
        'heic',
      ],
    );
    _isChooseQrImageShow = false;
    if (result != null) {
      File file = File(result.files.single.path!);
      Uint8List data = file.readAsBytesSync();
      final image = await decodeImageFromList(data);
      // final image = await img.decodeImage(data);

      if ((data.lengthInBytes / (1024 * 1024)) > 5) {
        NiimbotToastController()
            .show(NiimbotIntl.getIntlMessage('pc0024', '请上传5M以内的二维码进行识别'),
                icon: NiimbotIcons.warning(
                  color: NiimbotTheme.of(context).colors.textFillColorPrimary,
                  size: 20.00,
                ));
        return null;
      }
      if (image.width >= 5000 || image.height >= 5000) {
        NiimbotToastController()
            .show(NiimbotIntl.getIntlMessage('pc0050', '上传失败,请上传宽高5000像素以内的图片'),
                icon: NiimbotIcons.warning(
                  color: NiimbotTheme.of(context).colors.textFillColorPrimary,
                  size: 20.00,
                ));
        return null;
      }
      final ImageFile input = ImageFile(filePath: file.path, rawBytes: data);
      final ImageFile output = await compressInQueue(ImageFileConfiguration(
          input: input,
          config: const Configuration(
            pngCompression: PngCompression.defaultCompression,
            jpgQuality: 80,
          )));

      NiimbotZxingResult results = niimbot_qbarcode.identifier(file.path);
      if (results.error != 0 || results.text.isEmpty) {
        NiimbotToastController()
            .show(NiimbotIntl.getIntlMessage('pc0025', '未识别到二维码'),
                type: ToastType.warning,
                icon: NiimbotIcons.warning(
                  color: NiimbotTheme.of(context).colors.warningColorNormal,
                  size: 20.00,
                ));
      } else {
        widget.qrcodeRead!({
          'resultCode': results.text,
          'resultFileData': output.rawBytes,
        });
      }
    } else {}
  }
}
