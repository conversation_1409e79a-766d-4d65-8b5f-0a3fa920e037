import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_ui/model/niimbot_dropdown_model.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:niimbot_ui/widgets/niimbot_popover/niimbot_popover.dart';
import 'package:niimbot_ui/widgets/niimbot_tooltip/niimbot_tooltip.dart';


import 'package:niimbot_flutter_canvas/business/desktop/models/canvas_element_text_content_choose_model.dart';
import 'package:niimbot_flutter_canvas/business/desktop/models/index.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/check_box_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/select_contain_title_widget.dart';

class DataBindGenerateWidget extends StatelessWidget {
  final String? value;
  final ExcelSourceType? generateType;
  final ValueChanged<ExcelSourceType>? onGenerateTypeChange;
  final bool printColumnNames;
  final ValueChanged<bool>? onPrintColumnNames;
  final bool showColumn;

  const DataBindGenerateWidget({
    super.key,
    required this.value,
    this.generateType,
    this.onGenerateTypeChange,
    final bool? printColumnNames,
    this.onPrintColumnNames,
    this.showColumn = false,
  }) : printColumnNames = printColumnNames ?? false;

  @override
  Widget build(final BuildContext context) {
    final niimbotTheme = NiimbotTheme.of(context);
    return Column(
      children: [
        Row(
          children: [
            Text(
              NiimbotIntl.getIntlMessage('app100001101', '当前内容'),
              style: niimbotTheme.typography.body,
            ),
            const SizedBox(
              width: 10,
            ),
            Expanded(
                child: Container(
              alignment: Alignment.centerRight,
              child: NiimbotToolTip(
                text: value!, // 文本内容
                maxLines: 2,
                textStyle: niimbotTheme.typography.body?.copyWith(
                    color: niimbotTheme.colors.textFillColorSecondary),
                position: NiimbotPopoverPositionEnum.top,
              ),
            ))
          ],
        ),
        const SizedBox(height: 18),
        SelectContainTitleWidget(
          leftTitle: NiimbotIntl.getIntlMessage('app100000822', '生成为'),
          title: CanvasElementTextContentChooseModel.excelSourceTypeChooseList()
                  .firstWhereOrNull((final e) => e.value == generateType)
                  ?.label ??
              '',
          isDropDown: true,
          // value: CanvasElementTextContentChooseModel.excelSourceTypeChooseList()
          //     .firstWhere((e) => e.value == generateType),
          value: generateType,
          dataList:
              CanvasElementTextContentChooseModel.excelSourceTypeChooseList(),
          onTapSelect: (final NiimbotDropDownModel? model) {
            if (model != null) {
              onGenerateTypeChange?.call(model.value);
            }
          },
        ),
        const SizedBox(height: 18),
        if (showColumn)
          CheckBoxWidget(
            title: NiimbotIntl.getIntlMessage("app100001155", "打印列名"),
            value: printColumnNames,
            valueChanged: onPrintColumnNames,
          ),
        if (showColumn) const SizedBox(height: 19),
      ],
    );
  }
}
