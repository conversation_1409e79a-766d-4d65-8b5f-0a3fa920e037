import 'package:flutter/material.dart';
import 'package:niimbot_ui/icons/icons.dart';
import 'package:niimbot_ui/styles/border_radius.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:niimbot_ui/widgets/niimbot_popover/niimbot_popover.dart';
import 'package:niimbot_ui/widgets/niimbot_tooltip/niimbot_tooltip.dart';

///下拉框 四周有线框
class BorderChooseWidget extends StatelessWidget {
  final bool dropdownVisible;
  final String title;
  final bool readonly;
  final Widget? titleRight;

  const BorderChooseWidget({
    this.dropdownVisible = false,
    required this.title,
    this.readonly = false,
    this.titleRight,
    super.key,
  });

  @override
  Widget build(final BuildContext context) {
    final themeColors = NiimbotTheme.of(context).colors;

    final labelWidget = Container(
        alignment: !readonly
            ? AlignmentDirectional.centerStart
            : AlignmentDirectional.centerEnd,
        child: NiimbotToolTip(
            text: title,
            textStyle: NiimbotTheme.of(context).typography.body?.copyWith(
                color: !readonly
                    ? themeColors.textFillColorPrimary
                    : themeColors.textFillColorSecondary),
            position: NiimbotPopoverPositionEnum.top));

    return Container(
      padding: EdgeInsets.symmetric(vertical: 6, horizontal: !readonly ? 8 : 0),
      width: 270,
      decoration: !readonly
          ? BoxDecoration(
              borderRadius: NiimbotRadius.small,
              border: Border.all(color: themeColors.borderColorNormal),
            )
          : null,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            flex: 1,
            child: titleRight != null
                ? Row(
                    children: [
                      labelWidget,
                      const SizedBox(width: 6.0),
                      titleRight!
                    ],
                  )
                : labelWidget,
          ),
          if (!readonly)
            RotatedBox(
                quarterTurns: dropdownVisible ? 1 : 3,
                child: NiimbotIcons.chevron(
                  size: 14,
                  color: themeColors.textFillColorSecondary,
                )),
        ],
      ),
    );
  }
}
