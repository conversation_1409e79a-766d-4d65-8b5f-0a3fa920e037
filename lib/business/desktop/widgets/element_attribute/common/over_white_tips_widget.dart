import 'package:flutter/cupertino.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_ui/icons/icons.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:niimbot_ui/styles/typography_style.dart';
import 'package:niimbot_ui/widgets/niimbot_direction.dart';

class OverWhiteTipsWidget extends StatelessWidget {
  const OverWhiteTipsWidget({super.key});

  @override
  Widget build(final BuildContext context) {
    return Container(
        padding:
            const EdgeInsets.only(left: 16, top: 12, right: 16, bottom: 18),
        child: Column(
          children: [
            Text(
              NiimbotIntl.getIntlMessage("pc0021", "开启反白效果，更突出展示内容"),
              style: NiimbotTheme.of(context)
                  .typography
                  .hintStrong
                  ?.copyWith(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 6),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Column(
                  children: [
                    SizedBox(
                      width: 60,
                      height: 80,
                      child: Image.asset("assets/common/high_light_bf.png",
                          package: 'niimbot_flutter_canvas', fit: BoxFit.cover),
                    ),
                    SizedBox(
                      width: 75,
                      child: Text(
                        NiimbotIntl.getIntlMessage('app100000426', '反白前'),
                        style: NiimbotTheme.of(context)
                            .typography
                            .hint
                            ?.copyWith(
                                fontSize: 11,
                                color: NiimbotTheme.of(context)
                                    .colors
                                    .textFillColorSecondary),
                        maxLines: null,
                        textAlign: TextAlign.center,
                      ),
                    )
                  ],
                ),
                NiimbotDirection(
                  child: RotatedBox(
                      quarterTurns: 3,
                      child: NiimbotIcons.arrow(
                          size: 16,
                          color: NiimbotTheme.of(context)
                              .colors
                              .textFillColorSecondary)),
                ),
                Column(
                  children: [
                    SizedBox(
                      width: 60,
                      height: 80,
                      child: Image.asset("assets/common/high_light_af.png",
                          package: 'niimbot_flutter_canvas', fit: BoxFit.cover),
                    ),
                    SizedBox(
                      width: 75,
                      child: Text(
                        NiimbotIntl.getIntlMessage('app100000425', '反白后'),
                        style: NiimbotTheme.of(context)
                            .typography
                            .hint
                            ?.copyWith(
                                fontSize: 11,
                                color: NiimbotTheme.of(context)
                                    .colors
                                    .textFillColorSecondary),
                        maxLines: null,
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),
            RichText(
              text: TextSpan(
                text: NiimbotIntl.getIntlMessage('app100000763', '注意:'),
                style: NiimbotTheme.of(context).typography.body?.copyWith(
                    fontSize: 12, fontWeight: NiimbotFontWeight.bold),
                children: [
                  TextSpan(
                    text: NiimbotIntl.getIntlMessage(
                        'app100000764', '大量使用反白，可能会使打印设备过热，降低打印速度。'),
                    style: NiimbotTheme.of(context)
                        .typography
                        .body
                        ?.copyWith(fontSize: 12),
                  )
                ],
              ),
            ),
          ],
        ));
  }
}
