import 'package:flutter/material.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:niimbot_ui/widgets/niimbot_number_input.dart';

/// 标签纸宽高、尾巴长度
class NumberInputWidget extends StatefulWidget {
  final String leftTitle;
  final bool readonly;
  final String? suffixText;
  final int digit;
  final int? min;
  final int? max;
  final bool isNegative;
  final num? value;
  final Function(num)? onChange;
  final Function(num)? onBlur;

  const NumberInputWidget({
    required this.leftTitle,
    this.readonly = false,
    this.suffixText = 'mm',
    this.digit = 1,
    this.min,
    this.max,
    this.isNegative = false,
    this.value = 0,
    this.onChange,
    this.onBlur,
    super.key,
  });

  @override
  State<NumberInputWidget> createState() => _NumberInputWidgetState();
}

class _NumberInputWidgetState extends State<NumberInputWidget> {
  final TextEditingController _textController = TextEditingController();

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _textController.text = widget.value.toString();
  }

  @override
  void didUpdateWidget(covariant final NumberInputWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    _textController.text = widget.value.toString();
  }

  @override
  Widget build(final BuildContext context) {
    final themeColors = NiimbotTheme.of(context).colors;
    print('-------------${widget.value}');

    return Container(
        padding: EdgeInsets.symmetric(
            vertical: widget.readonly ? 6 : 0, horizontal: 0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Flexible(
              flex: 1,
              child: Text(
                widget.leftTitle,
                style: NiimbotTheme.maybeOf(context)?.typography.body,
                overflow: TextOverflow.ellipsis,
                maxLines: 2,
              ),
            ),
            widget.readonly == false
                ? NiimbotNumberInput(
                    width: 146,
                    height: 32,
                    defaultValue: widget.value,
                    digit: widget.digit,
                    min: widget.min ?? double.negativeInfinity,
                    max: widget.max ?? double.infinity,
                    suffix: widget.suffixText != null
                        ? Padding(
                            padding: const EdgeInsets.all(8),
                            child: Text(
                              widget.suffixText!,
                              style: NiimbotTheme.maybeOf(context)
                                  ?.typography
                                  .hint
                                  ?.copyWith(
                                      color:
                                          themeColors.textFillColorSecondary),
                            ),
                          )
                        : null,
                    controller: _textController,
                    onChanged: widget.onChange,
                    onBlur: widget.onBlur,
                  )
                : Text('${widget.value}${widget.suffixText}',
                    style: NiimbotTheme.maybeOf(context)
                        ?.typography
                        .hint
                        ?.copyWith(color: themeColors.textFillColorSecondary))
          ],
        ));
  }
}
