import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:intl/intl.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_template/models/elements/element_enum.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:niimbot_ui/styles/typography_style.dart';
import 'package:niimbot_ui/widgets/niimbot_checkbox/niimbot_checkbox.dart';

class DateTimeRefresh<T> extends StatefulWidget {
  final T format;
  final bool? use12Hours;
  final ValueChanged<bool>? use12HoursChanged;
  final Widget Function(String value)? builder;

  const DateTimeRefresh({
    super.key,
    required this.format,
    this.use12Hours,
    this.use12HoursChanged,
    this.builder,
  });

  @override
  State<DateTimeRefresh> createState() => _DateTimeRefreshState();
}

class _DateTimeRefreshState extends State<DateTimeRefresh> {
  late String dateTime;
  Timer? timer;
  final bool _beforeAmPm = ['zh', 'zh_Hant', 'ko', 'ja']
      .contains(NiimbotIntl.getCurrentLocale().toString());

  _getNowDateTime() {
    final bool isZh = widget.format == ElementDateFormat.YMDE_C.value;
    if (widget.use12Hours != true) {
      dateTime = DateFormat(widget.format, isZh ? 'zh_CN' : null)
          .format(DateTime.now());
    } else {
      final List<String> amPmList = [
        NiimbotIntl.getIntlMessage('app100001461', '上午', param: ['']),
        NiimbotIntl.getIntlMessage('app100001460', '下午', param: [''])
      ];
      dateTime = DateFormat(
              '${widget.format} a'.replaceAll('HH', 'h'), isZh ? 'zh_CN' : null)
          .format(DateTime.now());
      dateTime = dateTime
          .replaceAll('AM', amPmList.first)
          .replaceAll('PM', amPmList.last);
      if (_beforeAmPm) {
        dateTime = dateTime.split(' ').reversed.join(' ');
      }
    }
    if (mounted) {
      setState(() {});
    }
  }

  @override
  void initState() {
    super.initState();
    _getNowDateTime();
    final timeOffset = DateTime.now().millisecondsSinceEpoch % 1000;
    Future.delayed(Duration(milliseconds: 1000 - timeOffset), () {
      timer = Timer.periodic(const Duration(seconds: 1), (final Timer t) {
        _getNowDateTime();
      });
    });
  }

  @override
  void dispose() {
    super.dispose();
    if (timer != null) {
      timer?.cancel();
    }
  }

  @override
  Widget build(final BuildContext context) {
    final niimbotTheme = NiimbotTheme.of(context);
    return widget.builder?.call(dateTime) ??
        Column(
          children: [
            if (widget.use12Hours != null)
              Container(
                width: double.infinity,
                height: 36,
                padding: const EdgeInsets.symmetric(horizontal: 10.0),
                decoration: BoxDecoration(
                    border: Border(
                  bottom: BorderSide(
                    color: niimbotTheme.colors.dividerColor,
                    width: 1.0,
                  ),
                )),
                child: Row(
                  children: [
                    NiimbotCheckbox(
                      value: widget.use12Hours == false,
                      onChanged: (final bool? checked) {
                        widget.use12HoursChanged?.call(checked != true);
                      },
                    ),
                    const SizedBox(width: 6),
                    Text(
                      NiimbotIntl.getIntlMessage('app100001418', '24小时制'),
                    ),
                  ],
                ),
              ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 10),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      dateTime,
                      style: niimbotTheme.typography.title?.copyWith(
                        fontWeight: NiimbotFontWeight.bold,
                        color: niimbotTheme.colors.textFillColorPrimary,
                      ),
                    ),
                    const SizedBox(height: 6),
                    Text(
                      NiimbotIntl.getIntlMessage('app100001415', '已开启实时时间'),
                      style: niimbotTheme.typography.body?.copyWith(
                          color: niimbotTheme.colors.textFillColorPrimary),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      NiimbotIntl.getIntlMessage(
                          'app100001416', '打印时会自动更新为系统当前时间'),
                      textAlign: TextAlign.center,
                      style: niimbotTheme.typography.hint?.copyWith(
                          color: niimbotTheme.colors.textFillColorSecondary),
                    ),
                  ],
                ),
              ),
            )
          ],
        );
  }
}
