import 'dart:async';
import 'dart:io';

import 'package:collection/collection.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:image/image.dart' as image;
import 'package:netal_plugin/utils/ratio.dart';
import 'package:niimbot_flutter_canvas/business/desktop/canvas_pdf_calculator.dart';
import 'package:niimbot_flutter_canvas/business/desktop/utils/canvas_semaphore_utils.dart';
import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_flutter_canvas/provider/utils/canvas_store_utils.dart';
import 'package:niimbot_flutter_canvas/provider/utils/image_clipped_utils.dart';
import 'package:niimbot_flutter_canvas/utils/buried_utils.dart';
import 'package:niimbot_flutter_canvas/utils/canvas_image_util.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_template/extensions/template_data.dart';
import 'package:niimbot_template/models/elements/base_element.dart';
import 'package:niimbot_template/models/elements/image_element.dart';
import 'package:niimbot_template/models/template/file_data_source.dart';
import 'package:niimbot_template/models/template/file_data_source_rule.dart';
import 'package:niimbot_ui/icons/icons.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:niimbot_ui/widgets/niimbot_dialog/niimbot_dialog_controller.dart';
import 'package:niimbot_ui/widgets/niimbot_toast/niimbot_loading_controller.dart';
import 'package:niimbot_ui/widgets/niimbot_toast/niimbot_toast_controller.dart';
import 'package:pdfx/pdfx.dart';
import 'package:provider/provider.dart';
import 'package:uuid/uuid.dart';

import 'canvas_upload_image_utils.dart';

///针对canvas上传图片、pdf的方法封装
final Logger _logger = Logger("CanvasUploadUtils", on: kDebugMode);

class CanvasUploadUtils {
  ///桌面端选取照片 更改为多选
  static Future<(List<String>?, BaseElement?)> pickImage(
    final BuildContext context,
    final List<FileDataSource>? fileDataSources,
  ) async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['jpg', 'png', 'jpeg', 'heif', 'heic'],
        lockParentWindow: true,
        allowMultiple: true);

    if (result != null) {
      if (result.paths.length > 50) {
        BuriedUtils().track('show', '007_070_086');
        NiimbotToastController().showWarning(
            NiimbotIntl.getIntlMessage('pc0411', '单次上传图片数量不能超过50张'));
        return (null, null);
      }
      final isHaveMultipleImage = fileDataSources?.any(
              (final e) => e.type == 'image' && e.pageImages.length > 1) ??
          false;
      if (result.paths.length > 1 && isHaveMultipleImage) {
        BuriedUtils().track('show', '007_070_092');
        await showTips(context, 'image');
        return (null, null);
      }

      NiimbotLoadingController().show(
          NiimbotIntl.getIntlMessage('pc0372', '上传中...'),
          showMask: true,
          duration: Duration.zero);
      List<String>? imageList;
      try {
        final List<File> files =
            result.paths.map((final path) => File(path!)).toList();
        final firstImageResult =
            await onIsolateFirstUploadImages([files.first], context);
        if (files.length > 1) {
          double? targetRatio = (firstImageResult.$2?.height != null &&
                  firstImageResult.$2?.width != null)
              ? firstImageResult.$2!.width / firstImageResult.$2!.height
              : null;
          final imageResult = await onIsolateUploadImages(
              files.sublist(1), context, targetRatio);
          NiimbotLoadingController().close();
          final firstList = firstImageResult.$1 ?? [];
          final tem = imageResult ?? [];
          return (
            [...firstList, ...tem].whereType<String>().toList(),
            firstImageResult.$2
          );
        } else {
          NiimbotLoadingController().close();
          return (firstImageResult.$1, firstImageResult.$2);
        }
        // final res = await onImportPhotos(result, context);
        // imageList = res.$1;
        // return (imageList?.whereType<String>().toList(), res.$2);
      } catch (e, s) {
        if (e is String) {
          BuriedUtils().track('show', '007_070_087');
        }
        String error = NiimbotIntl.getIntlMessage('app100001149', '上传失败');
        if (e is String) {
          if (e.contains('image size over')) {
            error = NiimbotIntl.getIntlMessage('pc0076', '请上传5M以内的图片进行识别');
          } else if (e.contains('image width or height over')) {
            error =
                NiimbotIntl.getIntlMessage('pc0075', '上传失败，建议上传宽高5000像素以内的图片');
          }
        }
        NiimbotToastController().showWarning(error);
      } finally {
        NiimbotLoadingController().close();
      }
    }
    return (null, null);
  }

  ///处理第一张图获取需要的targetRatio
  static Future<(List<String>?, BaseElement?)> onIsolateFirstUploadImages(
      final List<File> files, final BuildContext context) async {
    final completer = Completer<(List<String>, BaseElement?)>();
    List<String?> resultPaths =
        List.filled(files.length, null, growable: false);
    List<String> tempPathList = [];
    bool isError = false;
    bool completed = false;
    BaseElement? tempImageElement;
    await ImageUpLoadUtils.init(1);
    ImageUpLoadUtils.cropImages(
      files,
      null,
      onProgress: (final progress) {
        // _updateLoading(progress);
      },
      onResult: (final UpLoadImageResult result) async {
        if (isError) return;
        final res = result.bytes;
        final imagePath = result.outPutPath;
        resultPaths[result.index] = imagePath;
        if (result.index == 0) {
          tempImageElement = await generateElement(res!, context, imagePath!);
        }
        tempPathList.add(imagePath ?? "");
        if (!completed && tempPathList.length == files.length) {
          completed = true;
          completer.complete(
              (resultPaths.whereType<String>().toList(), tempImageElement));
        }
      },
      onError: (final error) {
        if (isError) return;
        _logger.log('Error in batch cropping image: $error');
        isError = true;
        ImageUpLoadUtils.dispose();
        completer.completeError(error);
      },
    );
    return completer.future;
  }

  static Future<List<String>?> onIsolateUploadImages(final List<File> files,
      final BuildContext context, final double? targetRatio) async {
    final completer = Completer<List<String>>();
    List<String?> resultPaths =
        List.filled(files.length, null, growable: false);
    int taskLength = (Platform.numberOfProcessors / 4).ceil();
    if (files.length < taskLength) {
      taskLength = (files.length / 2).ceil() - 1;
    }
    if (taskLength < 1) {
      taskLength = 1;
    }
    List<String> tempPathList = [];
    bool isError = false;
    bool completed = false;
    await ImageUpLoadUtils.init(taskLength);
    ImageUpLoadUtils.cropImages(
      files,
      targetRatio,
      onProgress: (final progress) {
        print('=======progress============${progress}');
        // _updateLoading(progress);
      },
      onResult: (final UpLoadImageResult result) async {
        if (isError) return;
        final imagePath = result.outPutPath;
        resultPaths[result.index] = imagePath;
        tempPathList.add(imagePath ?? "");
        if (!completed && tempPathList.length == files.length) {
          completed = true;
          completer.complete(resultPaths.whereType<String>().toList());
        }
      },
      onError: (final error) {
        if (isError) return;
        _logger.log('Error in batch cropping image: $error');
        isError = true;
        ImageUpLoadUtils.dispose();
        completer.completeError(error);
      },
    );
    return completer.future;
  }

  ///上传图片处理
  static Future<(List<String>?, BaseElement?)> onImportPhotos(
    final FilePickerResult result,
    final BuildContext context,
  ) async {
    final cancelToken = CancellationToken();
    final List<File> files =
        result.paths.map((final path) => File(path!)).toList();
    BaseElement? geneElement;

    int taskLength = (Platform.numberOfProcessors / 2).ceil();
    if (files.length < taskLength) {
      taskLength = (files.length / 2).ceil() - 1;
    }
    if (taskLength < 1) {
      taskLength = 1;
    }
    final completer = Completer<(List<String>?, BaseElement?)>();

    final semaphore = InterruptibleSemaphore(taskLength);
    final results = List<String?>.filled(files.length, null);
    try {
      // 使用索引映射保持结果顺序
      double? targetRatio;
      final [fileFirst, ...restFiles] = files;
      final res = await _processImageSafe(
          fileFirst, semaphore, cancelToken, context, 0,
          targetRatio: targetRatio);
      results[0] = res.$1;
      geneElement = res.$2 is! ImageElement ? res.$2 : (res.$2 as ImageElement);
      targetRatio =
          geneElement != null ? geneElement.width / geneElement.height : null;
      final futures =
          restFiles.asMap().map((final index, final file) => MapEntry(
                index,
                _processImageSafe(file, semaphore, cancelToken, context, index,
                        targetRatio: targetRatio)
                    .then((
                  final res,
                ) {
                  results[index + 1] = res.$1;
                }).catchError((final e, final stack) {
                  if (e is! TaskCanceledException) {
                    cancelToken.cancel(); // 触发全局取消
                    semaphore.cancelAll(); // 中断所有任务
                    throw e; // 传播原始异常
                  }
                  return null;
                }),
              ));
      await Future.wait(futures.values);
      completer.complete((results.whereType<String>().toList(), geneElement));
      // return (results.whereType<String>().toList(), geneElement);
    } on TaskCanceledException {
      completer.complete((null, null));
      // return (null, null); // 返回空列表避免空指针
    } finally {
      semaphore.cancelAll(); // 确保信号量释放
      cancelToken.cancel(); // 确保令牌失效
      NiimbotLoadingController().close();
    }
    return completer.future;
  }

  /// 安全图片处理（支持取消检查）
  static Future<(String?, BaseElement?)> _processImageSafe(
    final File file,
    final InterruptibleSemaphore sema,
    final CancellationToken token,
    final BuildContext context,
    final int index, {
    final double? targetRatio,
  }) async {
    bool acquired = false;
    try {
      // 前置取消检查
      if (token.isCancelled) throw TaskCanceledException();
      await sema.acquire();
      acquired = true;
      // 二次取消检查
      if (token.isCancelled) throw TaskCanceledException();
      final data = await file.readAsBytes();
      final res = await CanvasImageUtils.preProcessImage(data,
          showToast: false, targetRatio: targetRatio);
      final imagePath =
          await context.read<CanvasStore>().localFileSave?.call(res!);
      BaseElement? tempImageElement;
      if (index == 0) {
        tempImageElement = await generateElement(res!, context, imagePath!);
      }
      // 处理完成后检查取消
      if (token.isCancelled) throw TaskCanceledException();
      return (imagePath, index == 0 ? tempImageElement : null);
    } on TaskCanceledException {
      return (null, null); // 忽略主动取消
    } finally {
      if (acquired) sema.release();
    }
  }

  static Future<BaseElement?> generateElement(final Uint8List result,
      final BuildContext context, final String imagePath) async {
    final imageDecode = image.decodeImage(result);
    if (imageDecode != null) {
      final resultSize = CanvasUploadUtils.handleImageSize(
          context, imageDecode.width.toDouble(), imageDecode.height.toDouble());
      if (!context.mounted) return null;
      final element = await CanvasStoreUtils.generateElementByIdentifier(
          context, 'image', imageList: result,
          localFileSave: (final bytes) async {
        return imagePath;
      }, labelSize: Size(resultSize.$1, resultSize.$2));
      return element;
    }
    return null;
  }

  ///当前是否有pdf提示
  static void showPdfTip(final BuildContext context) {
    final pdfId = context
        .read<CanvasStore>()
        .canvasData
        .fileDataSources
        ?.firstWhereOrNull((final e) => e.type == 'pdf')
        ?.rule
        .firstOrNull
        ?.elementId;
    final isEmptyId = (pdfId ?? '').isNotEmpty;
    BuriedUtils().track('show', '007_052');
    showTips(context, 'pdf', onClose: () {
      if (isEmptyId) {
        context.read<CanvasStore>().focusElement(pdfId!);
      }
    });
  }

  static Future<void> showTips(final BuildContext context, final String type,
      {final Function()? onClose}) async {
    final completer = Completer();
    NiimbotDialogController().show(
      width: 400,
      messageDialog: true,
      messageIcon: NiimbotIcons.warning(
        color: NiimbotTheme.of(context).colors.warningColorNormal,
        size: 20,
      ),
      title: type == 'pdf'
          ? NiimbotIntl.getIntlMessage(
              'app100001862', '当前仅支持选择一个PDF文件，请删除模板中的PDF内容后，再重新选择。')
          : NiimbotIntl.getIntlMessage('pc0423', '当前仅支持导入一批图片，请删除这批图片后，再重新选择。'),
      confirmText: NiimbotIntl.getIntlMessage('app00707', '我知道了'),
      onClose: (final tipsDone) {
        onClose?.call();
        tipsDone();
        completer.complete();
      },
      onConfirm: (final tipsDone) {
        onClose?.call();
        tipsDone();
        completer.complete();
      },
    );
    return completer.future;
  }

  ///桌面端选取PDF
  static Future<(FileDataSource?, BaseElement? element)> pickPDF(
    final BuildContext context,
  ) async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowedExtensions: ['pdf'],
      lockParentWindow: true,
    );
    final data = result?.files.first.size;
    if (data != null && (data / (1024 * 1024)) > 10) {
      NiimbotToastController().showWarning(
          NiimbotIntl.getIntlMessage('app100001863', 'pdf文件上传至多10M'));
      BuriedUtils().track('show', '007_049');
      return (null, null);
    }
    NiimbotLoadingController().show(
      NiimbotIntl.getIntlMessage('pc0372', '上传中...'),
      showMask: true,
      duration: Duration(hours: 2),
    );
    if (result != null) {
      final res = await _onImportPdf(context, result);
      NiimbotLoadingController().close();
      return res;
    } else {
      NiimbotLoadingController().close();
      return (null, null);
    }
  }

  ///pdf导入
  static Future<(FileDataSource?, BaseElement? element)> _onImportPdf(
      final BuildContext context, final FilePickerResult result) async {
    final completer = Completer<(FileDataSource?, BaseElement? element)>();
    File file = File(result.files.single.path ?? '');
    List<String> pdfImageList = [];
    final pathKey = const Uuid().v4();
    BaseElement? firstElement;
    PdfDocument? pdfDocument;
    Semaphore? semaphore;
    try {
      pdfDocument = await PdfDocument.openFile(file.path);
      final canvasData = context.read<CanvasStore>().canvasData;
      int taskLength = (Platform.numberOfProcessors / 2).ceil();
      if (pdfDocument.pagesCount < taskLength) {
        taskLength = (pdfDocument.pagesCount / 2).ceil() - 1;
      }
      if (taskLength < 1) {
        taskLength = 1;
      }
      semaphore = Semaphore(taskLength);
      List<Future<void>> futures = [];
      for (int i = 0; i < pdfDocument.pagesCount; i++) {
        Future<void> future() async {
          PdfPage? page;
          try {
            await semaphore!.acquire();
            page = await pdfDocument?.getPage(i + 1);
            final PdfPageImage? image =
                await CanvasUploadUtils.handleChoosePdfSize(
                    page!, canvasData.size);
            if (image == null) throw Exception('image is null');
            final imagePath = await context
                .read<CanvasStore>()
                .localFileSave
                ?.call(image!.bytes, fileName: '${pathKey}_${i + 1}');
            pdfImageList.add(imagePath!);
            if (i == 0) {
              final result = CanvasUploadUtils.handleImageSize(
                  context, image.width!.toDouble(), image.height!.toDouble());
              if (!context.mounted) throw Exception('image is null');
              firstElement = await CanvasStoreUtils.generateElementByIdentifier(
                  context, 'image', imageList: image!.bytes, isPdf: true,
                  localFileSave: (final bytes) async {
                return imagePath;
              }, labelSize: Size(result.$1, result.$2));
            }
          } catch (e, stackTrace) {
            rethrow;
          } finally {
            page?.close();
            semaphore?.release();
          }
        }

        futures.add(future());
      }
      await Future.wait(futures);
      completer.complete((
        FileDataSource(
          pageCount: 1,
          sourceFileId: pathKey,
          sourceFilePath: file.path,
          pageImages: pdfImageList,
          type: 'pdf',
          rule: [
            FileDataSourceRule(elementId: firstElement!.id, pages: []),
          ],
        ),
        firstElement
      ));
    } catch (e) {
      if ((e is PlatformException) && e.message == 'Document failed to open') {
        NiimbotToastController()
            .showError(NiimbotIntl.getIntlMessage('app100001872', 'PDF文件打开失败'));
      } else {
        NiimbotToastController().showError(
            NiimbotIntl.getIntlMessage('pc0409', '上传失败，建议上传400页以内的PDF'));
      }
      completer.complete((null, null));
    } finally {
      pdfDocument?.close();
      semaphore?.release();
    }
    return completer.future;
  }

  ///计算当前图片适应画板的宽高是多少
  static (double, double) handleImageSize(final BuildContext context,
      final double tempWidth, final double tempHeight) {
    final canvasData = context.read<CanvasStore>().canvasData;
    double screenScale = View.of(context).devicePixelRatio;
    double width = tempWidth;
    double height = tempHeight;
    if (width > (canvasData.width.mm2px() * screenScale)) {
      double scale = (canvasData.width.mm2px()) / width;
      width = (width * scale * screenScale).toDouble();
      height = (height * scale * screenScale).toDouble();
    }
    if (height > (canvasData.height.mm2px() * screenScale)) {
      double scale = (canvasData.height.mm2px()) / height;
      width = (width * scale * screenScale).toDouble();
      height = (height * scale * screenScale).toDouble();
    }
    return (width, height);
  }

  ///处理pdf导入时的倍率计算
  static Future<PdfPageImage?> handleChoosePdfSize(
      final PdfPage pdfPage, final Size size) async {
    final PdfScalingCalculator scalingCalculator =
        PdfScalingCalculator.printer203(
      labelWidthMm: size.width.toDouble(),
      labelHeightMm: size.height.toDouble(),
    );
    final result = scalingCalculator.calculateOptimalSize(
      originalWidth: pdfPage.width,
      originalHeight: pdfPage.height,
      pdfBaseDpi: 1,
    );
    final pdfImage = await pdfPage.render(
      width: result.width,
      height: result.height,
      format: PdfPageImageFormat.png,
    );
    return pdfImage;
  }
}
