import 'dart:async';
import 'dart:collection';
import 'dart:io';
import 'dart:isolate';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pointycastle/digests/sha1.dart';
import 'package:image/image.dart' as imgPlus;
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'dart:ui' as ui;

///处理上传图片

class ImageUpLoadUtils {
  static UpLoadIsolatePool _pool =
      UpLoadIsolatePool(Platform.numberOfProcessors);

  /// 初始化线程池
  static Future<void> init(final int size) async {
    _pool = UpLoadIsolatePool(size);
    _pool.initialize();
  }

  /// 释放线程池资源
  static void dispose() => _pool.dispose();

  /// 批量裁剪图片
  static Future<void> cropImages(
    final List<File> paths,
    final double? targetRatio, {
    final Function(double)? onProgress,
    final Function(UpLoadImageResult)? onResult,
    final Function(String)? onError,
  }) async {
    final tasks = paths.asMap().entries.map((final entry) {
      return _createTask(entry.value, entry.key, targetRatio);
    }).toList();
    final resultSub = _pool.resultStream.listen((final result) {
      onResult?.call(result);
    });
    final progressSub = _pool.progressStream.listen(onProgress);
    final errorSub = _pool.errorStream.listen((final error) {
      onError?.call(error);
    });
    await _pool.runTasks(tasks);
    await _pool.awaitAllTasks();
    resultSub.cancel();
    progressSub.cancel();
    errorSub.cancel();
  }

  /// 创建上传任务
  static Map<String, dynamic> _createTask(
      final File file, final int index, final double? targetRatio) {
    return {
      'type': 'upload',
      'inputPath': file.path,
      'index': index,
      'targetRatio': targetRatio,
    };
  }
}

/// 上传结果数据模型
class UpLoadImageResult {
  final Uint8List bytes;
  final String outPutPath;
  final int index;

  UpLoadImageResult(this.bytes, this.outPutPath, this.index);
}

class UpLoadIsolatePool {
  final List<Isolate> _isolates = [];
  final List<SendPort> _workerPorts = [];
  final List<ReceivePort> _receivePorts = [];
  final int _size;
  final Queue<Map<String, dynamic>> _taskQueue = Queue();
  late Completer<void> _initCompleter;

  late StreamController<UpLoadImageResult> _resultController;
  late StreamController<double> _progressController;
  late StreamController<String> _errorController;

  int _completedTasks = 0;
  int _totalTasks = 0;
  Completer<void> _allTasksCompleter = Completer();

  UpLoadIsolatePool(this._size) {
    _initControllers();
  }

  void _initControllers() {
    _resultController = StreamController<UpLoadImageResult>.broadcast();
    _progressController = StreamController<double>.broadcast();
    _errorController = StreamController<String>.broadcast();
    _initCompleter = Completer();
  }

  /// 初始化线程池
  Future<void> initialize() async {
    if (_isolates.isNotEmpty) {
      dispose();
    }
    if (_resultController.isClosed) {
      _resultController = StreamController<UpLoadImageResult>.broadcast();
      _progressController = StreamController<double>.broadcast();
      _errorController = StreamController<String>.broadcast();
    }
    final token = RootIsolateToken.instance!;
    for (int i = 0; i < _size; i++) {
      final receivePort = ReceivePort();
      _receivePorts.add(receivePort);
      final isolate = await Isolate.spawn(
        _workerMain,
        [token, receivePort.sendPort],
        debugName: 'IsolateWorker${i + 1}',
        errorsAreFatal: false,
      );

      ///receivePort.first  也会产生监听 第一次 listen 也会监听第二次
      final workerPortCompleter = Completer<SendPort>();
      receivePort.listen((final message) {
        if (message is SendPort) {
          workerPortCompleter.complete(message); // 初始SendPort
        } else if (message is Map && message['type'] == 'error') {
          _errorController.add('${message['message']}\n${message['stack']}');
        }
      });
      final workerPort = await workerPortCompleter.future;
      _workerPorts.add(workerPort);
      _isolates.add(isolate);
    }
    _initCompleter.complete();
  }

  /// 结果流
  Stream<UpLoadImageResult> get resultStream => _resultController.stream;

  /// 进度流（0.0 - 1.0）
  Stream<double> get progressStream => _progressController.stream;

  /// 错误流
  Stream<String> get errorStream => _errorController.stream;

  /// 执行批量任务
  Future<void> runTasks(final List<Map<String, dynamic>> tasks) async {
    await _initCompleter.future;
    _totalTasks = tasks.length;
    _completedTasks = 0;
    _allTasksCompleter = Completer();
// 添加任务到队列
    _taskQueue.addAll(tasks);
// 启动任务处理
    for (var port in _workerPorts) {
      if (_taskQueue.isNotEmpty) {
        _sendNextTask(port);
      }
    }
    await _allTasksCompleter.future;
  }

  void _sendNextTask(final SendPort workerPort) {
    if (_taskQueue.isEmpty) return;
    final task = _taskQueue.removeFirst();
    // 创建专用通信端口
    final taskPort = ReceivePort();
    taskPort.listen((final result) {
      if (result is UpLoadImageResult) {
        _resultController.add(result); // 转发结果
        _markTaskCompleted();
      } else if (result is Map && result['type'] == 'error') {
        _errorController.add(result['message']);
        _markTaskCompleted();
      }
      taskPort.close(); // 关闭临时端口
    });
    // 仅传递可序列化数据 传递 SendPort 而非 StreamSink
    workerPort.send({...task, 'replyPort': taskPort.sendPort});
  }

  /// 标记任务完成（成功/失败都调用）
  void _markTaskCompleted() {
    _completedTasks++;
    final progress = _completedTasks / _totalTasks;
    _progressController.add(progress);
    if (_completedTasks == _totalTasks) {
      _allTasksCompleter.complete();
    }
// 继续分配新任务
    for (var port in _workerPorts) {
      if (_taskQueue.isNotEmpty) {
        _sendNextTask(port);
      }
    }
  }

  Future<void> awaitAllTasks() => _allTasksCompleter.future;

  /// 释放资源
  void dispose() {
    for (var isolate in _isolates) {
      isolate.kill(priority: Isolate.immediate);
    }
// 关闭接收端口
    for (var port in _receivePorts) {
      port.close();
    }
// 关闭流控制器
    if (!_resultController.isClosed) {
      _resultController.close();
    }
    if (!_progressController.isClosed) {
      _progressController.close();
    }
    if (!_errorController.isClosed) {
      _errorController.close();
    }
// 清理资源
    _isolates.clear();
    _workerPorts.clear();
    _receivePorts.clear();
    _taskQueue.clear();
    _initControllers();
  }

  /// Isolate工作线程主函数
  static void _workerMain(final List<dynamic> initArgs) {
    final token = initArgs[0] as RootIsolateToken;
    final mainPort = initArgs[1] as SendPort;
    BackgroundIsolateBinaryMessenger.ensureInitialized(token);
    final receivePort = ReceivePort();
    mainPort.send(receivePort.sendPort); // 向主线程发送通信端口
    receivePort.listen((final dynamic message) async {
      try {
        if (message is Map<String, dynamic>) {
          final replyPort = message['replyPort'] as SendPort;
          if (message['type'] == 'upload') {
            final result = await _processCropTask(message);
            replyPort.send(result);
          }
        }
      } catch (e, stack) {
        mainPort.send({
          'type': 'error',
          // 'index': message['index'],
          'message': e.toString(), // 'Isolate error: ${e.toString()}',
          // 'stack': stack.toString()
        });
      }
    });
  }

  /// 处理裁剪任务
  static Future<UpLoadImageResult> _processCropTask(
      final Map<String, dynamic> task) async {
    final path = task['inputPath'] as String;

    final targetRatio =
        (task['targetRatio'] is double) ? task['targetRatio'] : null;
    File file = await _getFile(path);
    final data = await file.readAsBytes();
    final res = await isolatedPreProcessImage(data, targetRatio, task['index']);
    final imageFile = await isolatedProcessImage(res!);
    return UpLoadImageResult(res, imageFile.path, task['index']);
  }

  // 上传图片前置处理及校验
  static Future<Uint8List?> isolatedPreProcessImage(
      final Uint8List data, final double? targetRatio, final int index) async {
    imgPlus.Image? thumbnail;
    try {
      if ((data.lengthInBytes / (1024 * 1024)) > 5) {
        throw 'image size over'; //NiimbotIntl.getIntlMessage('pc0076', '请上传5M以内的图片进行识别');
      }
      thumbnail = imgPlus.decodeImage(data);
      // 将图片编码为 JPG 格式
      if (thumbnail == null) return null;
      if (thumbnail.width >= 5000 || thumbnail.height >= 5000) {
        throw 'image width or height over'; //NiimbotIntl.getIntlMessage('pc0075', '上传失败，建议上传宽高5000像素以内的图片');
      }
    } catch (err) {
      rethrow;
    }

    if (thumbnail.numChannels < 3) {
      final rgbaImage = imgPlus.Image(
        width: thumbnail.width,
        height: thumbnail.height,
        numChannels: 4,
        backgroundColor: imgPlus.ColorFloat32.rgba(255, 255, 255, 0),
      );
      // for (var y = 0; y < thumbnail.height; y++) {
      //   for (var x = 0; x < thumbnail.width; x++) {
      //     if (thumbnail.getPixel(x, y).a == 0) {
      //       rgbaImage.setPixelRgba(x, y, 255, 255, 255, 0);
      //     }
      //   }
      // }
      // for (var y = 0; y < thumbnail.height; y++) {
      //   for (var x = 0; x < thumbnail.width; x++) {
      //     final pixel = thumbnail.getPixel(x, y);
      //     final alpha = pixel.a;
      //     if (alpha == 0) {
      //       rgbaImage.setPixelRgba(x, y, 255, 255, 255, 0);
      //       continue;
      //     }
      //
      //     if (thumbnail.numChannels == 1) {
      //       // 单通道直接复制
      //       final gray = (pixel.r == 1) ? 255 : 0;
      //       rgbaImage.setPixelRgba(x, y, gray, gray, gray, alpha);
      //     } else {
      //       final normAlpha = alpha / 255.0;
      //       final rMix = (pixel.r * normAlpha + 255 * (1 - normAlpha)).round();
      //       final gMix = (pixel.g * normAlpha + 255 * (1 - normAlpha)).round();
      //       final bMix = (pixel.b * normAlpha + 255 * (1 - normAlpha)).round();
      //       final gray = ((rMix * 299 + gMix * 587 + bMix * 114) / 1000).round();
      //       rgbaImage.setPixelRgba(x, y, gray, gray, gray, alpha);
      //     }
      //   }
      // }
      for (var y = 0; y < thumbnail.height; y++) {
        for (var x = 0; x < thumbnail.width; x++) {
          final singleValue = thumbnail.getPixel(x, y).r;
          rgbaImage.setPixel(
              x,
              y,
              imgPlus.ColorFloat32.rgba(
                singleValue,
                singleValue,
                singleValue,
                255,
              ));
        }
      }
      if (targetRatio != null) {
        final tempImage =
            resizeWithContain(original: rgbaImage, targetRatio: targetRatio!);
        final processedBytes = imgPlus.encodePng(tempImage);
        return await _compressImage(processedBytes);
      }
      final processedBytes = imgPlus.encodePng(rgbaImage);
      return await _compressImage(processedBytes);
    } else {
      if (targetRatio != null) {
        final tempImage =
            resizeWithContain(original: thumbnail, targetRatio: targetRatio!);
        final processedBytes = imgPlus.encodePng(tempImage);
        return await _compressImage(processedBytes);
      }
      final processedBytes = imgPlus.encodePng(thumbnail);
      return await _compressImage(processedBytes);
    }

    // ui.Image image;
    // Uint8List? processedBytes;
    // if (targetRatio != null) {
    final resizeImg =
        resizeWithContain(original: thumbnail, targetRatio: targetRatio!);
    //   final resizeBytes = imgPlus.encodePng(resizeImg);
    //   // image = await decodeImageFromList(resizeBytes);
    //   final dstImage = imgPlus.Image(
    //     width: thumbnail.width,
    //     height: thumbnail.height,
    //     numChannels: 4,  // 关键：指定4通道
    //   );
    //   imgPlus.fill(dstImage, color: imgPlus.ColorFloat32.rgba(255, 255, 255, 0));
    //   imgPlus.compositeImage(
    //     dstImage,
    //     thumbnail,
    //     dstX: 0,
    //     dstY: 0,
    //   );
    //   processedBytes = imgPlus.encodePng(dstImage);
    // } else {
    //   // image = await decodeImageFromList(data);
    //   final dstImage = imgPlus.Image(
    //     width: thumbnail.width,
    //     height: thumbnail.height,
    //     numChannels: 4,  // 关键：指定4通道
    //   );
    //   imgPlus.fill(dstImage, color: imgPlus.ColorFloat32.rgba(255, 255, 255, 0));
    //
    //   imgPlus.compositeImage(
    //     dstImage,
    //     thumbnail,
    //     dstX: 0,
    //     dstY: 0,
    //   );
    //   processedBytes = imgPlus.encodePng(dstImage);
    // }
    // Uint8List pngBytes = await image
    //     .toByteData(format: ui.ImageByteFormat.png)
    //     .then((final byteData) => byteData!.buffer.asUint8List());
    // return await _compressImage(processedBytes);
  }

  /// 压缩图片
  static Future<Uint8List?> _compressImage(final Uint8List data) async {
    try {
      imgPlus.Image? thumbnail = imgPlus.decodeImage(data);
      // 将图片编码为 JPG 格式
      if (thumbnail == null) return null;
      final dataBytes = data.lengthInBytes;
      if (1 * 1024 * 1024 < dataBytes && thumbnail.getPixel(0, 0).a != 0) {
        double quality = (1 * 1024 * 1024) / data.lengthInBytes;
        if (quality > 0 && quality < 100) {
          final jpgThumbnail = imgPlus.decodeImage(data);
          if (jpgThumbnail == null) return null;
          return imgPlus.encodeJpg(
            jpgThumbnail,
            quality: quality.toInt(),
          );
        }
      } else {
        return data;
      }
    } catch (e) {
      return null;
    }
    return data;
  }

  /// 非等比放大但保持内容完整（类似BoxFit.contain效果）
  static imgPlus.Image resizeWithContain({
    required final imgPlus.Image original,
    required final double targetRatio,
    final Color? backgroundColor, // 白色背景
    final imgPlus.Interpolation interpolation = imgPlus.Interpolation.cubic,
  }) {
    // 计算原始和目标宽高比
    final originalRatio = original.width / original.height;
    // final targetRatio = targetWidth / targetHeight;

    // 计算等比放大后的实际尺寸
    int actualWidth, actualHeight;
    // 目标宽高
    int targetWidth, targetHeight;
    if (targetRatio > 1) {
      targetHeight = original.height;
      targetWidth = (targetHeight * targetRatio).toInt();
    } else {
      targetWidth = original.width;
      targetHeight = (targetWidth / targetRatio).toInt();
    }

    if (originalRatio > targetRatio) {
      // 以宽度为基准
      actualWidth = targetWidth;
      actualHeight = (targetWidth / originalRatio).round();
    } else {
      // 以高度为基准
      actualHeight = targetHeight;
      actualWidth = (targetHeight * originalRatio).round();
    }

    // 创建目标画布
    final canvas =
        imgPlus.Image(width: targetWidth, height: targetHeight, numChannels: 4);

    // 填充背景色
    imgPlus.fill(canvas, color: imgPlus.ColorFloat32.rgba(255, 255, 255, 0));

    // 等比放大图片
    final resized = imgPlus.copyResize(
      original,
      width: actualWidth,
      height: actualHeight,
      interpolation: interpolation,
    );

    // 将放大后的图片绘制到画布中央
    final offsetX = (targetWidth - actualWidth) ~/ 2;
    final offsetY = (targetHeight - actualHeight) ~/ 2;

    imgPlus.compositeImage(
      canvas,
      resized,
      dstX: offsetX,
      dstY: offsetY,
    );

    return canvas;
  }

  static Future<File> isolatedProcessImage(final data) async {
    final res = data;
    final file = await _getFile(_buildFileKey(res));
    final exit = await file.exists();
    if (exit) {
      return file;
    }
    await file.create(recursive: true);
    return file.writeAsBytes(res, mode: FileMode.writeOnly, flush: true);
  }

  static String _buildFileKey(final Uint8List bytes) {
    final digest = SHA1Digest();
    digest.update(bytes, 0, bytes.length);
    final hash = Uint8List(digest.digestSize);
    digest.doFinal(hash, 0);
    return hash
        .map((final byte) => byte.toRadixString(16).padLeft(2, '0'))
        .join();
  }

  /// 获取文件
  static Future<File> _getFile(final String key) async {
    final dir = await _getLocalCacheDir();
    return File(join(dir.path, key));
  }

  static Future<Directory> _getLocalCacheDir() async {
    final dir = await getFileCachesDirectory();
    return dir;
  }

  static Future<Directory> getFileCachesDirectory() async {
    final dir = await getApplicationSupportDirectory();
    final cacheDir = Directory(join(dir.path, 'file_caches'));
    if (!await cacheDir.exists()) await cacheDir.create(recursive: true);
    return cacheDir;
  }
}
