import 'package:netal_plugin/models/netal_enum.dart';
import 'package:niimbot_template/models/elements/bar_code_element.dart';
import 'package:niimbot_template/models/elements/base_element.dart';
import 'package:niimbot_template/models/elements/qr_code_element.dart';
import 'package:niimbot_template/models/elements/text_element.dart';

import 'package:niimbot_flutter_canvas/business/desktop/models/index.dart';

class CanvasBindElementChangeTypeUtils {
  static BaseElement? changeBindType(
      final BaseElement baseElement, final ExcelSourceType newType) {
    if (baseElement is TextElement) {
      if (newType == ExcelSourceType.text) {
        return null;
      }
      if (newType == ExcelSourceType.qrcode) {
        final qrElement = QRCodeElement(
          value: baseElement.value,
          dataBind: baseElement.dataBind,
          id: baseElement.id,
          x: baseElement.x,
          y: baseElement.y,
          colorChannel: baseElement.colorChannel,
          elementColor: baseElement.elementColor,
        );
        return qrElement;
      }
      if (newType == ExcelSourceType.barcode) {
        final qrElement = BarCodeElement(
          value: baseElement.value,
          dataBind: baseElement.dataBind,
          id: baseElement.id,
          x: baseElement.x,
          y: baseElement.y,
          colorChannel: baseElement.colorChannel,
          elementColor: baseElement.elementColor,
        );
        return qrElement;
      }
    }
    if (baseElement is QRCodeElement) {
      if (newType == ExcelSourceType.qrcode) {
        return null;
      }
      if (newType == ExcelSourceType.text) {
        final qrElement = TextElement(
          value: baseElement.value,
          dataBind: baseElement.dataBind,
          id: baseElement.id,
          x: baseElement.x,
          y: baseElement.y,
          boxStyle: NetalTextBoxStyle.autoWidth,
          colorChannel: baseElement.colorChannel,
          elementColor: baseElement.elementColor,
        );
        return qrElement;
      }
      if (newType == ExcelSourceType.barcode) {
        final qrElement = BarCodeElement(
          value: baseElement.value,
          dataBind: baseElement.dataBind,
          id: baseElement.id,
          x: baseElement.x,
          y: baseElement.y,
          colorChannel: baseElement.colorChannel,
          elementColor: baseElement.elementColor,
        );
        return qrElement;
      }
    }
    if (baseElement is BarCodeElement) {
      if (newType == ExcelSourceType.barcode) {
        return null;
      }
      if (newType == ExcelSourceType.text) {
        final qrElement = TextElement(
          value: baseElement.value,
          dataBind: baseElement.dataBind,
          id: baseElement.id,
          x: baseElement.x,
          y: baseElement.y,
          boxStyle: NetalTextBoxStyle.autoWidth,
          colorChannel: baseElement.colorChannel,
          elementColor: baseElement.elementColor,
        );
        return qrElement;
      }
      if (newType == ExcelSourceType.qrcode) {
        final qrElement = QRCodeElement(
          value: baseElement.value,
          dataBind: baseElement.dataBind,
          id: baseElement.id,
          x: baseElement.x,
          y: baseElement.y,
          colorChannel: baseElement.colorChannel,
          elementColor: baseElement.elementColor,
        );
        return qrElement;
      }
    }
    return null;
  }
}
