import 'dart:async';
import 'dart:collection';

///可中断信号量并发处理
// 1. 取消令牌（用于全局任务中断）
class CancellationToken {
  bool _isCancelled = false;

  bool get isCancelled => _isCancelled;

  void cancel() => _isCancelled = true;
}

// 2. 可中断信号量（集成取消机制）
class InterruptibleSemaphore {
  int _tokens;
  final Queue<Completer<void>> _waitingQueue = Queue();
  bool _isCancelled = false;

  InterruptibleSemaphore(this._tokens);

  Future<void> acquire() async {
    if (_isCancelled) throw TaskCanceledException();
    if (_tokens > 0) {
      _tokens--;
      return;
    }
    final completer = Completer<void>();
    _waitingQueue.add(completer);
    await completer.future; // 等待唤醒或取消
  }

  void release() {
    if (_isCancelled) return;
    if (_waitingQueue.isNotEmpty) {
      _waitingQueue.removeFirst().complete(); // 唤醒等待任务
    } else {
      _tokens++;
    }
  }

  void cancelAll() {
    _isCancelled = true;
    while (_waitingQueue.isNotEmpty) {
      _waitingQueue.removeFirst().completeError(TaskCanceledException());
    }
  }
}

// 3. 自定义取消异常（区分业务错误）
class TaskCanceledException implements Exception {
  @override
  String toString() => "任务已被主动取消";
}
