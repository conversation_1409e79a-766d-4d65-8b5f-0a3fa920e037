import 'package:flutter/cupertino.dart';
import 'package:flutter_canvas_plugins_interface/shared/canvas_font_data.dart';
import 'package:niimbot_http/business/apis/rest/models/material_item.dart';
import 'package:niimbot_template/extensions/template_data.dart';
import 'package:niimbot_template/models/elements/element_enum.dart';
import 'package:niimbot_template/models/elements/material_element.dart';
import 'package:provider/provider.dart';

import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_flutter_canvas/provider/utils/canvas_store_utils.dart';

///与主工程的widget桥接单例
class CanvasWidgetManager {
  factory CanvasWidgetManager() => _instance;
  static final CanvasWidgetManager _instance = CanvasWidgetManager._internal();

  CanvasWidgetManager._internal();

  static CanvasWidgetManager sharedInstance() => _instance;
  Widget Function(Function(MaterialItem item) callback)? materialWidget;
  Widget Function(Function(MaterialItem item) callback)? borderWidget;
  Widget Function(Function(CanvasFontData? fontItem) onChanged, String? value)?
      fontFamilyWidget;
  VoidCallback? onLabelSelectTap;

  // Widget Function(Future Function(ExcelItem item) callback, Function() importCallBack)? datasourcesWidget;
  ValueChanged<int?>? updateConsumablesType;
  Future<bool> Function(bool showLoginDialog)? isLoginFunction;
  Future<bool> Function()? isLoginVipFunction;
  Future<List> Function()? getdataSources;
  Future<String> Function(String path, String name)? uploadExcelToOss;
  Future Function(
      {required String cloudId,
      required String downloadUrl,
      required String md5,
      required int fileSize})? updateCloudFile;
  Future Function(String ossUrl, String savePath)? transformOldExcelToNewExcel;
  Future<bool> Function(int id)? onDeleteDataSource;

  /// 本地缓存管理
  T Function<T>(String key, {bool encrypt, required T defaultValue})?
      getLocalStorage;
  Future<bool> Function<T>(String key, T value, {bool encrypt})?
      setLocalStorage;

  /// 更新图标
  onMaterialChanged(
    final MaterialItem item,
    final bool isBorder,
    final BuildContext context, {
    final MaterialElement? currentMaterialElement,
    final Offset? offset,
    final bool isAdd = false,
    final bool isClick = true,
  }) async {
    Size canvasSize = context.read<CanvasStore>().canvasData.size;
    final element = await CanvasStoreUtils.generateElementByIdentifier(
      context,
      'material',
      item: item,
      isBorder: isBorder,
      localFileSave: context.read<CanvasStore>().localFileSave,
      canvasSize: canvasSize,
    );
    if (element != null) {
      final focusElement =
          currentMaterialElement ?? context.read<CanvasStore>().focusedElement;
      if (!isAdd &&
          ((focusElement is MaterialElement &&
                  isBorder &&
                  focusElement.materialType == MaterialElementType.border) ||
              (focusElement is MaterialElement &&
                  !isBorder &&
                  focusElement.materialType == MaterialElementType.icon))) {
        context.read<CanvasStore>().replaceElement(
              focusElement,
              (element as MaterialElement).copyWith(
                  x: focusElement.x,
                  y: focusElement.y,
                  width: element.width,
                  height: element.height,
                  isOpenMirror: focusElement.isOpenMirror,
                  colorReverse: focusElement.colorReverse,
                  allowFreeZoom: focusElement.allowFreeZoom,
                  colorChannel: focusElement.colorChannel,
                  elementColor: focusElement.elementColor,
                  imageProcessingValue: focusElement.imageProcessingValue,
                  isLock: focusElement.isLock,
                  rotate: focusElement.rotate,
                  hasVipRes: item.vip),
              isClick: isClick,
            );
      } else {
        context.read<CanvasStore>().addElements(
          [element.copyWith(hasVipRes: item.vip)],
          offset: offset,
          isClick: isClick,
          isAddBorder: isClick ? isBorder : false,
        );
      }
      if (element is MaterialElement &&
          element.imageUrl != null &&
          element.imageUrl != element.localImageUrl) {
        setCanvasMaterialOssLocalMapUse(
            {element.imageUrl!: element.localImageUrl});
      }
    }
  }

  /// 第一次绑定数据源
  bool firstLoadDataBindAction() {
    final firstLoadDataBind = getLocalStorage?.call('firstLoadDataSourceBind',
        defaultValue: 0, encrypt: false);
    if (firstLoadDataBind != 1) {
      setLocalStorage?.call('firstLoadDataSourceBind', 1, encrypt: false);
    }
    return firstLoadDataBind != 1;
  }

  /// 时间默认值及开启状态缓存
  Map<String, dynamic> getDateElementDefault() {
    return getLocalStorage?.call('dateElementDefault', defaultValue: {}) ?? {};
  }

  Future<bool?> setDateElementDefault(final Map<String, dynamic> val) async {
    Map mapJson = getDateElementDefault();
    final res =
        await setLocalStorage?.call('dateElementDefault', {...mapJson, ...val});
    return res;
  }

  /// 勾选列名
  bool getExcelSelectColumn() {
    return getLocalStorage?.call('excelSelectColumn', defaultValue: true) ??
        true;
  }

  Future<bool?> setExcelSelectColumn(final bool isSelectColumn) async {
    final res =
        await setLocalStorage?.call('excelSelectColumn', isSelectColumn);
    return res;
  }

  /// 获取画板边框素材本地映射
  Map<String, dynamic> getCanvasMaterialOssLocalMapUse() {
    return getLocalStorage
            ?.call('canvasMaterialOssLocalMapUse', defaultValue: {}) ??
        {};
  }

  /// 设置画板边框素材本地映射
  Future<bool?> setCanvasMaterialOssLocalMapUse(
      final Map<String, dynamic> map) async {
    final mapJson = getCanvasMaterialOssLocalMapUse();
    mapJson.addAll(map);
    final res =
        await setLocalStorage?.call('canvasMaterialOssLocalMapUse', mapJson);
    return res;
  }

  /// pdf是否进入过
  bool getPdfEnter() {
    final a =
        getLocalStorage?.call('canvasPdfEnter', defaultValue: false) ?? false;
    return a;
  }

  Future<bool?> setPdfEnter(final bool isEnter) async {
    final res = await setLocalStorage?.call('canvasPdfEnter', isEnter);
    return res;
  }

  /// pdf是否第一次保存
  bool getPdfFirstSave() {
    final a =
        getLocalStorage?.call('canvasPdfFirstSave', defaultValue: false) ??
            false;
    return a;
  }

  Future<bool?> setPdfFirstSave(final bool isFirstSave) async {
    final res = await setLocalStorage?.call('canvasPdfFirstSave', isFirstSave);
    return res;
  }
}
