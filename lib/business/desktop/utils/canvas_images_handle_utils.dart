import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';

class CanvasImagesHandleUtils {
  Future<Size> canvasImageElementGenerate(
      final Uint8List? result, final Size canvasData) async {
    ui.Image image = await decodeImageFromList(result!);
    int width = image.width;
    int height = image.height;
    if (width > canvasData.width * 8) {
      double scale = (canvasData.width * 8) / width;
      width = (width * scale).toInt();
      height = (height * scale).toInt();
    }
    if (height > canvasData.height * 8) {
      double scale = (canvasData.height * 8) / height;
      width = (width * scale).toInt();
      height = (height * scale).toInt();
    }
    return Size(width.toDouble(), height.toDouble());
  }
}
