import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/config/canvas_theme_data.dart';
import 'package:flutter_canvas_plugins_interface/shared/canvas_font_data.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:netal_plugin/models/copy_wrapper.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:netal_plugin/utils/ratio.dart';
import 'package:niimbot_excel/niimbot_excel_utils.dart';
import 'package:niimbot_flutter_canvas/business/desktop/canvas_box_frame_desktop_app_bar.dart';
import 'package:niimbot_flutter_canvas/business/desktop/canvas_box_frame_desktop_left.dart';
import 'package:niimbot_flutter_canvas/business/desktop/canvas_box_frame_desktop_right.dart';
import 'package:niimbot_flutter_canvas/business/desktop/data_source_preview_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/models/canvas_drag_model.dart';
import 'package:niimbot_flutter_canvas/business/desktop/utils/canvas_widget_manager_utils.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/canvas/output_direction.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/canvas/rotate_paper_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/canvas_clipping_marker.dart';
import 'package:niimbot_flutter_canvas/extensions/rect.dart';
import 'package:niimbot_flutter_canvas/extensions/template_data.dart';
import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_flutter_canvas/provider/utils/canvas_store_utils.dart';
import 'package:niimbot_flutter_canvas/widgets/canvas/canvas_core.dart';
import 'package:niimbot_flutter_canvas/widgets/canvas/canvas_item_builder.dart';
import 'package:niimbot_http/business/apis/rest/models/material_item.dart';
import 'package:niimbot_template/extensions/template_data.dart';
import 'package:niimbot_template/models/elements/bar_code_element.dart';
import 'package:niimbot_template/models/elements/bind_element.dart';
import 'package:niimbot_template/models/elements/qr_code_element.dart';
import 'package:niimbot_template/models/template/template_data_source.dart';
import 'package:niimbot_template/models/template/template_data_source_modify.dart';
import 'package:niimbot_template/models/template_data.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:provider/provider.dart';

Logger _logger = Logger("CanvasBoxFrameDeskTop", on: kDebugMode);

/// 桌面端画板组合
class CanvasBoxFrameDesktop extends StatefulWidget {
  /// 当前语言（只区分zh-cn，en两种）
  final String language;

  /// 主题色
  final CanvasThemeData? themeData;

  /// 画板国际化配置
  // final LocalizationConfig? localizationConfig;
  final void Function(BuildContext context, TemplateData canvasData) onPrint;
  final VoidCallback? popback;
  final void Function(TemplateData canvasData)? onChanged;
  final Future<TemplateDataSource?> Function(TemplateDataSource dataSource)?
      onImportDataSource;
  final void Function(TemplateData canvasData, {bool? saveAs})? onSave;

  // onSaveAs
  final Widget Function(Function(MaterialItem item) callback)? materialWidget;
  final Widget Function(Function(MaterialItem item) callback)? borderWidget;

  final Widget Function(
          Function(CanvasFontData? fontItem) callback, String? value)?
      fontFamilyWidget;
  final VoidCallback? onLabelSelectTap;

  final Future<List> Function()? getdataSources;
  final Future<String> Function(String path, String name)? uploadExcelToOss;
  final Future Function(
      {required String cloudId,
      required String downloadUrl,
      required String md5,
      required int fileSize})? updateCloudFile;
  final Future Function(String ossUrl, String savePath)?
      transformOldExcelToNewExcel;
  final Future<bool> Function(int id)? onDeleteDataSource;
  final ValueChanged<int?>? updateConsumablesType;
  final bool wantKeepAlive;
  final bool activated;
  final CanvasStore controller;
  final Future<bool> Function(bool showLoginDialog)? isLoginFunction;
  final Future<bool> Function()? isLoginVipFunction;

  /// 本地缓存管理
  final T Function<T>(String key, {bool encrypt, required T defaultValue})?
      getLocalStorage;
  final Future<bool> Function<T>(String key, T value, {bool encrypt})?
      setLocalStorage;

  const CanvasBoxFrameDesktop({
    super.key,
    this.themeData,
    required this.language,
    required this.onPrint,
    this.popback,
    this.onChanged,
    this.onImportDataSource,
    this.onSave,
    this.materialWidget,
    this.borderWidget,
    this.fontFamilyWidget,
    this.onLabelSelectTap,
    this.uploadExcelToOss,
    this.updateCloudFile,
    this.getdataSources,
    this.transformOldExcelToNewExcel,
    this.onDeleteDataSource,
    this.updateConsumablesType,
    this.wantKeepAlive = true,
    this.activated = true,
    required this.controller,
    this.isLoginFunction,
    this.isLoginVipFunction,
    this.getLocalStorage,
    this.setLocalStorage,
  });

  @override
  CanvasBoxFrameStateDesktop createState() => CanvasBoxFrameStateDesktop();
}

class CanvasBoxFrameStateDesktop extends State<CanvasBoxFrameDesktop>
    with
        SingleTickerProviderStateMixin,
        AutomaticKeepAliveClientMixin<CanvasBoxFrameDesktop> {
  @override
  void initState() {
    _logger.log("=========canvas_box_frame_desktop的initState方法被调用");
    super.initState();
    CanvasWidgetManager.sharedInstance().materialWidget = widget.materialWidget;
    CanvasWidgetManager.sharedInstance().borderWidget = widget.borderWidget;
    CanvasWidgetManager.sharedInstance().fontFamilyWidget =
        widget.fontFamilyWidget;
    CanvasWidgetManager.sharedInstance().isLoginFunction =
        widget.isLoginFunction;
    CanvasWidgetManager.sharedInstance().isLoginVipFunction =
        widget.isLoginVipFunction;
    CanvasWidgetManager.sharedInstance().onLabelSelectTap =
        widget.onLabelSelectTap;
    CanvasWidgetManager.sharedInstance().uploadExcelToOss =
        widget.uploadExcelToOss;
    CanvasWidgetManager.sharedInstance().updateCloudFile =
        widget.updateCloudFile;
    CanvasWidgetManager.sharedInstance().transformOldExcelToNewExcel =
        widget.transformOldExcelToNewExcel;
    CanvasWidgetManager.sharedInstance().onDeleteDataSource =
        widget.onDeleteDataSource;
    CanvasWidgetManager.sharedInstance().updateConsumablesType =
        widget.updateConsumablesType;
    CanvasWidgetManager.sharedInstance().getdataSources = widget.getdataSources;
    CanvasWidgetManager.sharedInstance().getLocalStorage =
        widget.getLocalStorage;
    CanvasWidgetManager.sharedInstance().setLocalStorage =
        widget.setLocalStorage;
  }

  @override
  void didUpdateWidget(covariant final CanvasBoxFrameDesktop oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.getdataSources != widget.getdataSources) {
      CanvasWidgetManager.sharedInstance().getdataSources =
          widget.getdataSources;
    }
    if (oldWidget.isLoginFunction != widget.isLoginFunction) {
      CanvasWidgetManager.sharedInstance().isLoginFunction =
          widget.isLoginFunction;
    }
    if (oldWidget.isLoginVipFunction != widget.isLoginVipFunction) {
      CanvasWidgetManager.sharedInstance().isLoginVipFunction =
          widget.isLoginVipFunction;
    }
  }

  @override
  Widget build(final BuildContext context) {
    super.build(context);
    final niimbotColors = NiimbotTheme.of(context).colors;
    _logger.log("=========canvas_box_frame_desktop的build方法被调用");
    return ChangeNotifierProvider.value(
      value: widget.controller,
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: niimbotColors.systemFillColorWhite,
        appBar: CanvasBoxFrameDesktopAppBar(
            popBack: widget.popback,
            onPrint: (final ctx) {
              _logger.log("onPrint: 打印");
              TemplateData canvasData = ctx.read<CanvasStore>().originCanvasData;
              widget.onPrint.call(context, canvasData);
            },
            onSave: (final ctx) {
              TemplateData canvasData = ctx.read<CanvasStore>().canvasData;
              widget.onSave?.call(canvasData);
            },
            onSaveAs: (final ctx) {
              TemplateData canvasData = ctx.read<CanvasStore>().canvasData;
              // widget.onSave?.call(canvasData.copyWith(
              //   fileDataSources: canvasData.fileDataSources?.map((final e) {
              //     return e.copyWith(
              //       id: const CopyWrapper.value(null),
              //       templateId: const CopyWrapper.value(null),
              //     );
              //   }).toList(),
              // ), saveAs: true);
            }),
        body: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CanvasClippingMarker(
              child: Container(
                width: 228,
                height: double.infinity,
                decoration: BoxDecoration(
                    color: niimbotColors.solidBackgroundFillColorBase,
                    border: BorderDirectional(
                        end: BorderSide(
                            color: niimbotColors.dividerColor, width: 1.0))),
                child: CanvasBoxFrameDesktopLeft(
                  key: GlobalKey(),
                  onImportDataSource: widget.onImportDataSource,
                ),
              ),
            ),
            Expanded(
                child: _CanvasCoreState(
              activated: widget.activated,
            )),
            CanvasClippingMarker(
              child: Container(
                width: 312,
                height: double.infinity,
                color: niimbotColors.systemFillColorWhite,
                child: const CanvasBoxFrameDeskTopRight(),
              ),
            )
          ],
        ),
      ),
    );
  }

  @override
  bool get wantKeepAlive => widget.wantKeepAlive;
}

class _CanvasCoreState extends StatefulWidget {
  final bool activated;

  const _CanvasCoreState({
    super.key,
    this.activated = true,
  });

  @override
  State<_CanvasCoreState> createState() => _CanvasCoreStateState();
}

class _CanvasCoreStateState extends State<_CanvasCoreState> {
  bool isShow = true;
  final _canvasKey = GlobalKey();

  ///默认勾选打印列名
  void _addHeader(final element) {
    ///默认添加列名勾选上
    final isSelect = CanvasWidgetManager().getExcelSelectColumn();
    if (isSelect == false) {
      return;
    }

    final templateDataSourceModify = TemplateDataSourceModify(
      useTitle: true,
    );
    final canvasData = context.read<CanvasStore>().canvasData;
    Map<String, Map<String, TemplateDataSourceModify>> update =
        canvasData.dataSourceModifies ?? {};
    update[element.id] = {"0": templateDataSourceModify};
    context
        .read<CanvasStore>()
        .dataSourceController
        .updateTemplateColumnName(update);
  }

  (Size, Offset) _getOffset(final Offset offset) {
    final box = _canvasKey.currentContext!.findRenderObject() as RenderBox;
    final scale = box.getTransformTo(null).getMaxScaleOnAxis();
    final boxPos = box.localToGlobal(Offset.zero);
    final boxRect =
        Rect.fromLTWH(boxPos.dx, boxPos.dy, box.size.width, box.size.height);
    final rotate = context.read<CanvasStore>().rotate;
    final canvasSize = context.read<CanvasStore>().canvasData.size;
    final position = boxRect.rotate(rotate, boxRect.center).topLeft;
    var pos = (offset - position) / scale.toDouble();
    final rect = Rect.fromLTWH(
      pos.dx,
      pos.dy,
      canvasSize.width.mm2px().toDouble(),
      canvasSize.height.mm2px().toDouble(),
    );
    pos = rect.rotate(-rotate).topLeft;
    return (canvasSize, Offset(pos.dx, pos.dy).px2mm());
  }

  ///数据源
  void _onBindExcelAddElement(
      final CanvasExcelDragSendDataModel model, final Offset offset) async {
    final (canvasSize, pos) = _getOffset(offset);
    final canvasData = context.read<CanvasStore>().canvasData;

    ///扩大接受范围  当调整了屏幕分辨率 会出现问题
    const num = 0.2;
    var inDragList = canvasData.elements
        .where((final element) => (pos.dx >= (element.x - num) &&
            pos.dy >= (element.y - num) &&
            pos.dx <= (element.x + element.width + num) &&
            pos.dy <= (element.y + element.height + num)))
        .toList();
    if (inDragList.isNotEmpty) {
      if (((inDragList.last.type == NetalElementType.text)) ||
          (inDragList.last is QRCodeElement) ||
          (inDragList.last is BarCodeElement)) {
        final newElement = (inDragList.last as BindElement).copyWith(
          value: '${NiimbotExcelUtils.indexToLetters(model.column + 1)}0',
          dataBind: CopyWrapper.value([model.hash, model.sheetName]),
        );
        context
            .read<CanvasStore>()
            .replaceElement(inDragList.last, newElement, ignore: false);
        if (inDragList.last.type == NetalElementType.text) {
          _addHeader(newElement);
        }
      } else {
        final element = await CanvasStoreUtils.generateElementByExcel(model);
        if (element != null) {
          if (!mounted) return;
          context
              .read<CanvasStore>()
              .addElements([element], offset: pos, ignore: false);
          _addHeader(element);
        }
      }
    } else {
      final element = await CanvasStoreUtils.generateElementByExcel(model);
      if (element != null) {
        if (!mounted) return;
        context
            .read<CanvasStore>()
            .addElements([element], offset: pos, ignore: false);
        _addHeader(element);
      }
    }
  }

  void _onDraggedElement(final String? identifier, final Offset offset,
      {final MaterialItem? materialItem, final bool isBorder = false}) async {
    final (canvasSize, pos) = _getOffset(offset);
    if (identifier != null && mounted) {
      if (identifier != 'material' && identifier != 'border') {
        final element = await CanvasStoreUtils.generateElementByIdentifier(
            context, identifier,
            localFileSave: context.read<CanvasStore>().localFileSave,
            canvasSize: canvasSize);
        if (element != null && mounted) {
          context.read<CanvasStore>().addElements(
              [element.copyWith(hasVipRes: materialItem?.vip)],
              offset: pos, isClick: false);
        }
      } else if (materialItem != null) {
        CanvasWidgetManager.sharedInstance().onMaterialChanged(
          materialItem,
          isBorder,
          context,
          offset: pos,
          isAdd: true,
          isClick: false,
        );
        context
            .read<CanvasStore>()
            .addLocalRecent
            ?.call(materialItem, isBorder);
      }
    }
  }

  @override
  Widget build(final BuildContext context) {
    final isPDFOrExcel = context.select<CanvasStore, bool>((final v) =>
        v.canvasData.dataSources?.isNotEmpty == true ||
        v.canvasData.fileDataSources?.any((final e) =>
                !(e.type == 'image' && e.pageImages.length == 1)) ==
            true);
    final backgroundImage = context
        .select<CanvasStore, String>((final v) => v.canvasData.backgroundImage);
    final multipleBackIndex = context
        .select<CanvasStore, int>((final v) => v.canvasData.multipleBackIndex);
    final localBackground = context.select<CanvasStore, List<String>>(
        (final v) => v.canvasData.localBackground);

    final result = context.select<CanvasStore, (int?, int?)>(
        (final v) => v.canvasData.backPageAndTotal);

    final child = MouseRegion(

        ///此处在元素拖动时 鼠标应更改手势 需自定义  todo
        cursor: SystemMouseCursors.basic,
        child: DragTarget(
          onAcceptWithDetails: (final DragTargetDetails<CanvasDragModel> e) {
            if (e.data.canvasDragModelType ==
                CanvasDragModelType.canvasBaseElementType) {
              _onDraggedElement(
                e.data.data.identifier,
                e.offset,
                materialItem: e.data.data.remark,
                isBorder: e.data.data.identifier == 'border',
              );
            } else if (e.data.canvasDragModelType ==
                CanvasDragModelType.canvasBaseDataSourceType) {
              _onBindExcelAddElement(e.data.data, e.offset);
            }
          },
          builder: (final context, final accepted, final reject) {
            return CanvasCore(
              canvasBgKey: _canvasKey,
              activated: widget.activated,
              showBatchPreview: !isShow && isPDFOrExcel,
              batchInfo: isPDFOrExcel ? [result.$1 ?? 1, result.$2 ?? 1] : null,
              onBatchPreviewTap: () {
                // context.read<CanvasStore>().updateShowDataSourcePreview();
                setState(() {
                  isShow = !isShow;
                });
              },
              itemBuilder: DefaultCanvasItemBuilder,
              backgroundImage: backgroundImage,
              multipleBackIndex: multipleBackIndex,
              localBackgrounds: localBackground,
              rfidBind: () {},
              children: const [
                OutputDirectionWidget(),
                RotatePaperWidget(),
              ],
            );
          },
        ));
    if (isShow && isPDFOrExcel) {
      return Column(
        children: [
          Expanded(child: child),
          CanvasClippingMarker(
            child: DataSourcePreviewWidget(
              page: result.$1 ?? 1, //dataSourceBindInfo?.page,
              total: result.$2 ?? 1, //dataSourceBindInfo?.total,
              onClosePreview: () {
                setState(() {
                  isShow = false;
                });
              },
            ),
          )
        ],
      );
    }
    return child;
  }
}
