//左边往右边拖拽时的  拖拽基本元素添加  拖拽带有数据源的添加
enum CanvasDragModelType {
  canvasBaseElementType,
  canvasBaseDataSourceType,
  canvasBaseScrollType,
}

class CanvasDragModel {
  CanvasDragModelType canvasDragModelType;
  dynamic data;

  CanvasDragModel({required this.canvasDragModelType, required this.data});
}

class CanvasExcelDragSendDataModel {
  String sheetName;
  String hash;
  int column;

  CanvasExcelDragSendDataModel({
    required this.sheetName,
    required this.hash,
    required this.column,
  });
}
