// 定义Range类
class Range {
  int s;
  int e;

  Range({required this.s, required this.e});

  // 将Range对象转换为数字列表的方法
  List<int> toList() {
    return List.generate(e - s + 1, (final i) => s + i);
  }

  // 重写toString方法，以便更好地显示Range对象
  @override
  String toString() {
    return '{s: $s, e: $e}';
  }
}

// 定义DataSourceRange类型别名
typedef DataSourceRange = List<Range>;

// 选择的数组生成Range的函数
DataSourceRange arrToRange(final List<int> rows) {
  // 去重
  List<int> uniqueRows = List.from(rows.toSet());
  // 排序
  uniqueRows.sort();

  List<Range> result = [];
  int? start = uniqueRows.first;

  for (int i = 1; i < uniqueRows.length; i++) {
    if (uniqueRows[i] == uniqueRows[i - 1] + 1) {
      continue;
    } else {
      result.add(Range(s: start!, e: uniqueRows[i - 1]));
      start = uniqueRows[i];
    }
  }

  // 添加最后一个范围
  result.add(Range(s: start!, e: uniqueRows.last));

  return result;
}

// range生成数组的函数
List<int> rangeToArr(final DataSourceRange range) {
  List<int> result = [];

  for (var obj in range) {
    result.addAll(obj.toList());
  }

  return result;
}