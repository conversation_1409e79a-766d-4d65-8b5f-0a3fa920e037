import 'package:netal_plugin/models/netal_enum.dart';
import 'package:niimbot_flutter_canvas/business/desktop/models/index.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_template/models/elements/element_enum.dart';
import 'package:niimbot_ui/model/niimbot_dropdown_model.dart';

///文本框内容下拉选择 手动输入/Excel导入
class CanvasElementTextContentChooseModel {
  ///文本方式
  static List<NiimbotDropDownModel<ContentSourceType>> textContentChooseList() {
    return [
      NiimbotDropDownModel(
          value: ContentSourceType.Manual,
          label: NiimbotIntl.getIntlMessage("app100001098", "手动输入")),
      NiimbotDropDownModel(
          value: ContentSourceType.DataSource,
          label: NiimbotIntl.getIntlMessage("app00009", "Excel导入"))
    ];
  }

  ///转换的文本样式
  static List<NiimbotDropDownModel<ExcelSourceType>>
      excelSourceTypeChooseList() {
    return [
      NiimbotDropDownModel(
          value: ExcelSourceType.text,
          label: NiimbotIntl.getIntlMessage("app00002", "文本")),
      NiimbotDropDownModel(
          value: ExcelSourceType.barcode,
          label: NiimbotIntl.getIntlMessage("app00003", "一维码")),
      NiimbotDropDownModel(
          value: ExcelSourceType.qrcode,
          label: NiimbotIntl.getIntlMessage("app00004", "二维码"))
    ];
  }

  ///一维码编码格式
  static List<NiimbotDropDownModel<NetalBarcodeType>> barCodeStyleList() {
    return NetalBarcodeType.values
        .map((final e) => NiimbotDropDownModel(value: e, label: e.name))
        .toList();
  }

  ///二维码编码格式
  static List<NiimbotDropDownModel<NetalQRCodeType>> qrCodeStyleList() {
    return NetalQRCodeType.values
        .map((final e) => NiimbotDropDownModel(value: e, label: e.name))
        .toList();
  }

  ///二维码容错率
  static List<NiimbotDropDownModel> qRCodeCorrectLevel() {
    return [
      NiimbotDropDownModel(
          value: 0, label: NiimbotIntl.getIntlMessage("app01226", "低")),
      NiimbotDropDownModel(
          value: 1, label: NiimbotIntl.getIntlMessage("app100000879", "中")),
      NiimbotDropDownModel(
          value: 2, label: NiimbotIntl.getIntlMessage("pc0007", "较高")),
      NiimbotDropDownModel(
          value: 3, label: NiimbotIntl.getIntlMessage("app01224", "高")),
    ];
  }

  ///日期格式
  static List<NiimbotDropDownModel<ElementDateFormat>> dateFormatList(
      final String languageCode) {
    bool isZh = languageCode == "zh" ||
        languageCode == "zh-cn" ||
        languageCode == "zh-cn-t";
    List<ElementDateFormat> list = isZh
        ? ElementDateFormat.values
            .where((final element) => element != ElementDateFormat.MDYE_S)
            .toList()
        : ElementDateFormat.values
            .where((final element) =>
                element != ElementDateFormat.YMD_C &&
                element != ElementDateFormat.YMDE_C &&
                element != ElementDateFormat.YM_C &&
                element != ElementDateFormat.MD_C)
            .toList();
    return list
        .map((final e) => NiimbotDropDownModel(
              value: e,
              label: e.label,
            ))
        .toList();
    // return list.map((e) => NiimbotDropDownModel.fromJson(e)).toList();
  }

  ///时间格式
  static List<NiimbotDropDownModel<ElementTimeFormat>> dateTimeFormatList() {
    return ElementTimeFormat.values
        .map((final e) => NiimbotDropDownModel(value: e, label: e.value))
        .toList();
  }
}
