import 'package:file_picker/file_picker.dart';

class ExcelModel {
  /// 切割需要的excel数量
  static List<List<String>> buildRowData(final List<List<String>> list) {
    if (list.length > 2001) {
      return list.sublist(0, 2001);
    }
    return list;
  }

  /// 校验excel大小
  static bool checkExcel(final FilePickerResult filePickerResult) {
    PlatformFile pickedFile = filePickerResult.files.first;
    int fileSizeInBytes = pickedFile.size; // 获取文件大小（字节）
    int maxSizeInBytes = 300 * 1024; // 300KB转换为字节
    /// 判断是否超过限制大小
    return fileSizeInBytes > maxSizeInBytes;
  }
}
