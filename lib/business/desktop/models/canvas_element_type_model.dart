import 'package:flutter_canvas_plugins_interface/config/toolkit_button.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:niimbot_flutter_canvas/business/desktop/utils/canvas_widget_manager_utils.dart';
import 'package:niimbot_flutter_canvas/widgets/components/menu.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_ui/icons/icons.dart';
import 'package:niimbot_ui/styles/colors.dart';

class CanvasElementTypeModels {
  static List<MenuModel> typeModels(final NiimbotColors niimbotColors) {
    List<MenuModel> models = [
      MenuModel(
        NiimbotIcons.text(
          size: 36.0,
          color: niimbotColors.textFillColorPrimary,
        ),
        NiimbotIntl.getIntlMessage("app00002", "文本"),
        identifier: 'text',
      ),
      MenuModel(
        NiimbotIcons.picture(
          size: 36.0,
          color: niimbotColors.textFillColorPrimary,
        ),
        NiimbotIntl.getIntlMessage("app00006", "图片"),
        identifier: 'image',
      ),
      MenuModel(
        NiimbotIcons.stickers(
          size: 36.0,
          color: niimbotColors.textFillColorPrimary,
        ),
        NiimbotIntl.getIntlMessage("pc0008", "图标"),
        identifier: 'material',
      ),
      MenuModel(
        NiimbotIcons.barCode(
          size: 36.0,
          color: niimbotColors.textFillColorPrimary,
        ),
        NiimbotIntl.getIntlMessage("app00003", "一维码"),
        identifier: 'barcode',
      ),
      MenuModel(
        NiimbotIcons.qrCode(
          size: 36.0,
          color: niimbotColors.textFillColorPrimary,
        ),
        NiimbotIntl.getIntlMessage("app00004", "二维码"),
        identifier: NetalElementType.qrcode.name,
      ),
      MenuModel(
        NiimbotIcons.date(
          size: 36.0,
          color: niimbotColors.textFillColorPrimary,
        ),
        NiimbotIntl.getIntlMessage("app00010", "时间"),
        identifier: 'date',
      ),
      MenuModel(
        NiimbotIcons.table(
          size: 36.0,
          color: niimbotColors.textFillColorPrimary,
        ),
        NiimbotIntl.getIntlMessage("app00005", "表格"),
        identifier: NetalElementType.table.name,
      ),
      MenuModel(
        NiimbotIcons.border(
          size: 36.0,
          color: niimbotColors.textFillColorPrimary,
        ),
        NiimbotIntl.getIntlMessage("app100001204", "边框"),
        identifier: 'border',
      ),
      MenuModel(
        NiimbotIcons.line(
          size: 36.0,
          color: niimbotColors.textFillColorPrimary,
        ),
        NiimbotIntl.getIntlMessage("app00013", "线条"),
        identifier: NetalElementType.line.name,
      ),
      MenuModel(
        NiimbotIcons.shapes(
          size: 36.0,
          color: niimbotColors.textFillColorPrimary,
        ),
        NiimbotIntl.getIntlMessage("app00011", "形状"),
        identifier: 'graph',
      ),
      MenuModel(
        NiimbotIcons.snNumber(
          size: 36.0,
          color: niimbotColors.textFillColorPrimary,
        ),
        NiimbotIntl.getIntlMessage("app100000107", "序列号"),
        identifier: NetalElementType.serial.name,
      ),
      MenuModel(
        NiimbotIcons.pdf(
          size: 36.0,
          color: niimbotColors.textFillColorPrimary,
        ),
        NiimbotIntl.getIntlMessage("app100001848", "PDF打印"),
        showCircular: CanvasWidgetManager().getPdfEnter() != true,
        identifier: 'pdf',
      ),
    ];
    return models;
  }
}
