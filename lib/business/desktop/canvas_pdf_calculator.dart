import 'dart:math';

/// 倍率计算模式
enum ScalingMode {
  physicalSize, // 基于物理尺寸比例
  pixelDensity, // 基于像素密度
  printerDpi, // 基于打印机DPI精确计算
}

/// PDF倍率计算器配置
class PdfScalingConfig {
  /// 标签纸尺寸(毫米)
  final double labelWidthMm;
  final double labelHeightMm;

  /// 默认像素密度倍数
  final double defaultPixelRatio;

  /// 计算模式
  final ScalingMode scalingMode;

  /// 打印机点数配置
  final double printerDots;
  final double printerRatio;

  /// 内存控制：最大允许的图片像素数
  final int maxPixels;

  /// 实测基准数据
  final Map<String, double> testDataSmall;
  final Map<String, double> testDataLarge;

  const PdfScalingConfig({
    this.labelWidthMm = 50.0,
    this.labelHeightMm = 30.0,
    this.defaultPixelRatio = 2.0,
    this.scalingMode = ScalingMode.printerDpi,
    this.printerDots = 203.0,
    this.printerRatio = 8.0,
    this.maxPixels = 15000000,
    this.testDataSmall = const {'pixels': 8662, 'bestRatio': 8.0},
    this.testDataLarge = const {'pixels': 501090, 'bestRatio': 2.0},
  });

  /// 创建203点数配置
  static const PdfScalingConfig printer203 = PdfScalingConfig(
    printerDots: 203.0,
    printerRatio: 8.0,
  );

  /// 创建300点数配置
  static const PdfScalingConfig printer300 = PdfScalingConfig(
    printerDots: 300.0,
    printerRatio: 11.0,
  );

  /// 自定义配置
  PdfScalingConfig copyWith({
    final double? labelWidthMm,
    final double? labelHeightMm,
    final double? defaultPixelRatio,
    final ScalingMode? scalingMode,
    final double? printerDots,
    final double? printerRatio,
    final int? maxPixels,
    final Map<String, double>? testDataSmall,
    final Map<String, double>? testDataLarge,
  }) {
    return PdfScalingConfig(
      labelWidthMm: labelWidthMm ?? this.labelWidthMm,
      labelHeightMm: labelHeightMm ?? this.labelHeightMm,
      defaultPixelRatio: defaultPixelRatio ?? this.defaultPixelRatio,
      scalingMode: scalingMode ?? this.scalingMode,
      printerDots: printerDots ?? this.printerDots,
      printerRatio: printerRatio ?? this.printerRatio,
      maxPixels: maxPixels ?? this.maxPixels,
      testDataSmall: testDataSmall ?? this.testDataSmall,
      testDataLarge: testDataLarge ?? this.testDataLarge,
    );
  }
}

/// PDF倍率计算结果
class PdfScalingResult {
  /// 计算出的最佳宽度
  final double width;

  /// 计算出的最佳高度
  final double height;

  /// 使用的倍率
  final double ratio;

  /// 计算详情日志
  final List<String> logs;

  const PdfScalingResult({
    required this.width,
    required this.height,
    required this.ratio,
    required this.logs,
  });

  @override
  String toString() {
    return 'PdfScalingResult(width: $width, height: $height, ratio: $ratio)';
  }
}

/// PDF倍率计算工具类
class PdfScalingCalculator {
  static const double _mmToInch = 25.4;

  final PdfScalingConfig _config;
  final List<String> _logs = [];

  PdfScalingCalculator(this._config);

  /// 便捷构造函数 - 203点数打印机
  PdfScalingCalculator.printer203({
    final double labelWidthMm = 50.0,
    final double labelHeightMm = 30.0,
  }) : _config = PdfScalingConfig.printer203.copyWith(
          labelWidthMm: labelWidthMm,
          labelHeightMm: labelHeightMm,
        );

  /// 便捷构造函数 - 300点数打印机
  PdfScalingCalculator.printer300({
    final double labelWidthMm = 50.0,
    final double labelHeightMm = 30.0,
  }) : _config = PdfScalingConfig.printer300.copyWith(
          labelWidthMm: labelWidthMm,
          labelHeightMm: labelHeightMm,
        );

  /// 计算最佳渲染尺寸
  ///
  /// [originalWidth] PDF页面原始宽度
  /// [originalHeight] PDF页面原始高度
  /// [pdfBaseDpi] PDF基准DPI值（可选，默认72.0）
  /// 返回优化后的渲染尺寸
  PdfScalingResult calculateOptimalSize({
    required final double originalWidth,
    required final double originalHeight,
    final double pdfBaseDpi = 72.0,
  }) {
    _logs.clear();

    double intelligentRatio;

    switch (_config.scalingMode) {
      case ScalingMode.pixelDensity:
        intelligentRatio =
            _calculatePixelDensityRatio(originalWidth, originalHeight);
        _log("=== 像素密度模式 ===");
        break;
      case ScalingMode.printerDpi:
        intelligentRatio = _calculatePrinterDpiRatio(
            originalWidth, originalHeight, pdfBaseDpi);
        _log("=== 打印机DPI精确模式 ===");
        break;
      case ScalingMode.physicalSize:
        intelligentRatio = _calculatePhysicalSizeRatio(
            originalWidth, originalHeight, pdfBaseDpi);
        _log("=== 物理尺寸模式 ===");
        break;
    }

    // 确保最小倍率不低于默认倍率
    double finalRatio = intelligentRatio > _config.defaultPixelRatio
        ? intelligentRatio
        : _config.defaultPixelRatio;

    double finalWidth = originalWidth * finalRatio;
    double finalHeight = originalHeight * finalRatio;

    _log("智能计算倍率: ${intelligentRatio.toStringAsFixed(2)}x");
    _log("最终使用倍率: ${finalRatio.toStringAsFixed(2)}x");
    _log("输出尺寸: ${finalWidth.toInt()}x${finalHeight.toInt()}");

    return PdfScalingResult(
      width: finalWidth,
      height: finalHeight,
      ratio: finalRatio,
      logs: List.from(_logs),
    );
  }

  /// 基于打印机点数的精确倍率计算
  double _calculatePrinterDpiRatio(final double originalWidth,
      final double originalHeight, final double pdfBaseDpi) {
    _log("当前打印机点数: ${_config.printerDots.toInt()}");
    _log("对应基准倍率: ${_config.printerRatio}x");
    _log("PDF原始尺寸: ${originalWidth.toInt()}x${originalHeight.toInt()}");

    // 基于打印机点数的基准倍率
    double baseRatio = _config.printerRatio;

    // 根据PDF尺寸和标签纸尺寸进行调整
    double totalPixels = originalWidth * originalHeight;
    _log("PDF像素总数: ${totalPixels.toInt()}");

    // 基于PDF大小的调整系数
    double sizeAdjustment = _calculateSizeAdjustment(totalPixels);

    // 基于标签纸的调整系数
    double labelAdjustment = _calculateLabelAdjustment();

    // 结合实测数据校准
    double calibratedRatio = _calibrateWithPrinterTestData(
        totalPixels, baseRatio * sizeAdjustment * labelAdjustment);

    // 内存控制检查
    double finalRatio =
        _applyMemoryControl(originalWidth, originalHeight, calibratedRatio);

    _log("打印机基准倍率: ${baseRatio}x");
    _log("尺寸调整系数: ${sizeAdjustment.toStringAsFixed(2)}");
    _log("标签纸调整系数: ${labelAdjustment.toStringAsFixed(2)}");
    _log("校准后倍率: ${calibratedRatio.toStringAsFixed(2)}x");

    return finalRatio;
  }

  /// 基于像素密度的倍率计算
  double _calculatePixelDensityRatio(
      final double originalWidth, final double originalHeight) {
    double totalPixels = originalWidth * originalHeight;
    _log("PDF像素总数: ${totalPixels.toInt()}");
    _log("PDF尺寸: ${originalWidth.toInt()}x${originalHeight.toInt()}");

    double baseRatio;
    if (totalPixels <= 10000) {
      baseRatio = 8.0;
      _log("像素密度等级: 极小PDF -> 基础倍率8.0x (实测优化)");
    } else if (totalPixels <= 50000) {
      baseRatio = 6.0;
      _log("像素密度等级: 小PDF -> 基础倍率6.0x");
    } else if (totalPixels <= 150000) {
      baseRatio = 4.0;
      _log("像素密度等级: 中小PDF -> 基础倍率4.0x");
    } else if (totalPixels <= 400000) {
      baseRatio = 3.0;
      _log("像素密度等级: 中等PDF -> 基础倍率3.0x");
    } else if (totalPixels <= 800000) {
      baseRatio = 2.0;
      _log("像素密度等级: 较大PDF -> 基础倍率2.0x (实测优化)");
    } else {
      baseRatio = 1.5;
      _log("像素密度等级: 超大PDF -> 基础倍率1.5x");
    }

    double adjustmentFactor = _calculateLabelAdjustment();
    double finalRatio = baseRatio * adjustmentFactor;

    _log("标签纸调整系数: ${adjustmentFactor.toStringAsFixed(2)}");

    return finalRatio;
  }

  /// 基于物理尺寸的倍率计算
  double _calculatePhysicalSizeRatio(final double originalWidth,
      final double originalHeight, final double pdfBaseDpi) {
    double pdfWidthMm, pdfHeightMm;

    if (pdfBaseDpi == 1.0) {
      const assumedDisplayDpi = 96.0;
      pdfWidthMm = (originalWidth / assumedDisplayDpi) * _mmToInch;
      pdfHeightMm = (originalHeight / assumedDisplayDpi) * _mmToInch;
      _log("像素映射模式: 假设显示DPI $assumedDisplayDpi");
    } else {
      pdfWidthMm = (originalWidth / pdfBaseDpi) * _mmToInch;
      pdfHeightMm = (originalHeight / pdfBaseDpi) * _mmToInch;
    }

    _log(
        "PDF物理尺寸: ${pdfWidthMm.toStringAsFixed(1)}mm x ${pdfHeightMm.toStringAsFixed(1)}mm");
    _log("标签纸尺寸: ${_config.labelWidthMm}mm x ${_config.labelHeightMm}mm");

    return _calculateIntelligentRatio(pdfWidthMm, pdfHeightMm);
  }

  /// 计算智能比例倍率
  double _calculateIntelligentRatio(
      final double pdfWidthMm, final double pdfHeightMm) {
    double widthRatio = _config.labelWidthMm / pdfWidthMm;
    double heightRatio = _config.labelHeightMm / pdfHeightMm;

    double sizeRatio = widthRatio < heightRatio ? widthRatio : heightRatio;

    _log(
        "尺寸比例 - 宽度: ${widthRatio.toStringAsFixed(2)}, 高度: ${heightRatio.toStringAsFixed(2)}");
    _log("选择比例: ${sizeRatio.toStringAsFixed(2)}");

    double qualityMultiplier;

    if (sizeRatio >= 1.0) {
      qualityMultiplier = _calculateEnlargementQuality(sizeRatio);
    } else {
      qualityMultiplier = _calculateReductionQuality(sizeRatio);
    }

    return qualityMultiplier;
  }

  /// 计算放大场景的质量倍率
  double _calculateEnlargementQuality(final double ratio) {
    if (ratio >= 4.0) {
      return ratio * 2.0;
    } else if (ratio >= 2.0) {
      return ratio * 1.5;
    } else {
      return ratio * 1.2;
    }
  }

  /// 计算缩小场景的质量倍率
  double _calculateReductionQuality(final double ratio) {
    if (ratio <= 0.25) {
      return 8.0;
    } else if (ratio <= 0.5) {
      return 6.0;
    } else if (ratio <= 0.75) {
      return 4.0;
    } else {
      return 3.0;
    }
  }

  /// 基于PDF尺寸计算调整系数
  double _calculateSizeAdjustment(final double totalPixels) {
    if (totalPixels <= 10000) {
      return 1.0;
    } else if (totalPixels <= 100000) {
      return 0.8;
    } else if (totalPixels <= 500000) {
      return 0.4;
    } else {
      return 0.25;
    }
  }

  /// 基于标签纸尺寸计算调整系数
  double _calculateLabelAdjustment() {
    double labelArea = _config.labelWidthMm * _config.labelHeightMm;

    if (labelArea < 500) {
      return 1.2;
    } else if (labelArea < 1500) {
      return 1.0;
    } else if (labelArea < 3000) {
      return 0.9;
    } else {
      return 0.8;
    }
  }

  /// 基于打印机实测数据校准
  double _calibrateWithPrinterTestData(
      final double totalPixels, final double baseRatio) {
    double smallPixels = _config.testDataSmall['pixels']!;
    double smallBestRatio = _config.testDataSmall['bestRatio']!;
    double largePixels = _config.testDataLarge['pixels']!;
    double largeBestRatio = _config.testDataLarge['bestRatio']!;

    if (totalPixels <= smallPixels * 1.2) {
      _log("校准参考: 小PDF实测 → 目标${smallBestRatio}x");
      return smallBestRatio;
    } else if (totalPixels >= largePixels * 0.8) {
      _log("校准参考: 大PDF实测 → 目标${largeBestRatio}x");
      return largeBestRatio;
    } else {
      double ratio = (totalPixels - smallPixels) / (largePixels - smallPixels);
      double interpolatedRatio =
          smallBestRatio + (largeBestRatio - smallBestRatio) * ratio;
      _log("校准参考: 插值计算 → 目标${interpolatedRatio.toStringAsFixed(2)}x");
      return interpolatedRatio;
    }
  }

  /// 应用内存控制
  double _applyMemoryControl(final double originalWidth,
      final double originalHeight, final double proposedRatio) {
    double finalWidth = originalWidth * proposedRatio;
    double finalHeight = originalHeight * proposedRatio;
    int finalPixels = (finalWidth * finalHeight).round();

    _log("预期输出尺寸: ${finalWidth.toInt()}x${finalHeight.toInt()}");
    _log("预期像素总数: $finalPixels");
    _log("内存限制: ${_config.maxPixels}像素");

    if (finalPixels > _config.maxPixels) {
      double maxRatio =
          sqrt(_config.maxPixels / (originalWidth * originalHeight));
      _log(
          "⚠️ 超出内存限制，调整倍率: ${proposedRatio.toStringAsFixed(2)}x -> ${maxRatio.toStringAsFixed(2)}x");
      return maxRatio;
    }

    _log("✅ 内存控制通过");
    return proposedRatio;
  }

  /// 添加日志
  void _log(final String message) {
    _logs.add(message);
  }

  /// 获取配置
  PdfScalingConfig get config => _config;
}
