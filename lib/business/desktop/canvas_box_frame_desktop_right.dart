import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:niimbot_ui/widgets/niimbot_tabs/niimbot_tabs.dart';
import 'package:provider/provider.dart';

import 'package:niimbot_flutter_canvas/business/desktop/widgets/canvas_global_settings_widget.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/element_attr_widget.dart';
import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/canvas_desktop_common_widget/canvas_tabs_child_widget.dart';

final Logger _logger = Logger("CanvasBoxFrameDeskTopRight", on: kDebugMode);

class CanvasBoxFrameDeskTopRight extends StatefulWidget {
  const CanvasBoxFrameDeskTopRight({
    super.key,
  });

  @override
  State<CanvasBoxFrameDeskTopRight> createState() =>
      _CanvasBoxFrameDeskTopRightState();
}

class _CanvasBoxFrameDeskTopRightState extends State<CanvasBoxFrameDeskTopRight>
    with TickerProviderStateMixin {
  List<String> _tabLists = [NiimbotIntl.getIntlMessage('pc0048', '全局设置')];
  late TabController _tabController;
  int currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabLists.length, vsync: this);
  }

  @override
  void dispose() {
    super.dispose();
    _tabController.dispose();
  }

  @override
  Widget build(final BuildContext context) {
    final show =
        context.select<CanvasStore, bool>((final v) => v.showElementAttributePanel);
    if (show && _tabLists.length == 1) {
      _tabLists = [
        NiimbotIntl.getIntlMessage('pc0048', '全局设置'),
        NiimbotIntl.getIntlMessage('pc0067', '元素设置')
      ];
      currentIndex = 1;
      _tabController =
          TabController(length: _tabLists.length, vsync: this, initialIndex: 1);
    } else if (!show && _tabLists.length == 2) {
      _tabLists = [NiimbotIntl.getIntlMessage('pc0048', '全局设置')];
      currentIndex = 0;
      _tabController =
          TabController(length: _tabLists.length, vsync: this, initialIndex: 0);
    }
    final themeColors = NiimbotTheme.of(context).colors;
    return Container(
      width: 320,
      height: double.infinity,
      color: themeColors.systemFillColorWhite,
      child: Column(
        children: [
          Container(
              alignment: AlignmentDirectional.centerStart,
              child: NiimbotTabBar(
                  isScrollable: true,
                  dividerColor: Colors.transparent,
                  onTap: (final int index) {
                    currentIndex = index;
                    setState(() {});
                  },
                  controller: _tabController,
                  tabs: [
                    Tab(
                      height: 50,
                      // maxWidth: 108,
                      child: CanvasTabsChildWidget(
                        text: NiimbotIntl.getIntlMessage('pc0073', '标签设置'),
                        isSelect: currentIndex == 0,
                      ),
                    ),
                    if (show)
                      Tab(
                        height: 50,
                        // maxWidth: 150,
                        child: CanvasTabsChildWidget(
                          text: NiimbotIntl.getIntlMessage('pc0067', '元素设置'),
                          isSelect: currentIndex == 1,
                        ),
                      ),
                  ])),
          Expanded(
              flex: 1,
              child: Container(
                  decoration: BoxDecoration(
                      border: Border(
                          top: BorderSide(
                              color: themeColors.dividerColor, width: 1.0))),
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      const CanvasGlobalSettingWidget(),
                      if (show) const ElementAttributePanelWidget(),
                    ],
                  )))
        ],
      ),
    );
  }
}
