import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/canvas_clipping_marker.dart';
import 'package:niimbot_ui/styles/theme.dart';

import 'package:niimbot_flutter_canvas/business/desktop/widgets/top_bar/top_bar.dart';
import 'package:niimbot_flutter_canvas/model/interface.dart';

Logger _logger = Logger("CanvasBoxFrameDesktopAppBar", on: kDebugMode);

@immutable
class CanvasBoxFrameDesktopAppBar extends StatelessWidget
    implements PreferredSizeWidget {
  final TracerCallback? onTrace;
  final VoidCallback? popBack;
  final ValueChanged<BuildContext>? onSave;
  final ValueChanged<BuildContext>? onSaveAs;
  final ValueChanged<BuildContext>? onPrint;

  const CanvasBoxFrameDesktopAppBar({
    this.onTrace,
    this.popBack,
    this.onSave,
    this.onSaveAs,
    this.onPrint,
    super.key,
  });

  @override
  Widget build(final BuildContext context) {
    final themeColors = NiimbotTheme.of(context).colors;

    return CanvasClippingMarker(
      child: Container(
        height: 56,
        alignment: Alignment.centerLeft,
        decoration: BoxDecoration(
          color: themeColors.systemFillColorWhite,
          border: Border(
            bottom: BorderSide(
              color: themeColors.dividerColor,
              width: 1.0,
            ),
          ),
        ),
        child: TopBar(
          popBack: popBack,
          onSave: () {
            onSave?.call(context);
          },
          onSaveAs: () {
            onSaveAs?.call(context);
          },
          onPrint: () {
            onPrint?.call(context);
          },
        ),
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
