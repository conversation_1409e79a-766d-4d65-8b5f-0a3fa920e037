# niimbot_flutter_canvas

## 简介
Flutter画板组件，当前为全新设计版本，已完成基本元素与属性设置编码工作；

## 设计文档
https://whjc.yuque.com/xr5745/km1krz/wmndqk

## 接入文档
https://whjc.yuque.com/xr5745/km1krz/bvcqis

## 插件依赖项
### flutter_canvas_plugins_interface
https://git.jc-ai.cn/print/foundation/flutter_canvas_plugins/flutter_canvas_plugins_interface.git
- 画板对外数据结构与接口定义于此，便于业务工程在接入画板时，对画板接口的实现包含在另外的插件工程中。规避各插件对画板业务的重复引入；

### drawboard_dart_sdk
https://git.jc-ai.cn/architect/kalimdor/drawboard-dart-sdk.git
- 架构组提供画板通用能力接口支持，当前包含：1.ASR 服务的 token 获取逻辑的封装；2.OCR服务相关能力接口的封装；

### ifly_speech_recognition
https://git.jc-ai.cn/print/foundation/flutter_canvas_plugins/ifly_speech_recognition.git
- 根据科大讯飞语音听写（流式版）WebAPI文档，实现60s的语音识别功能。token 从 drawboard_dart_sdk 获取；

### netal_plugin
https://git.jc-ai.cn/architect/dboard/netal_plugin.git
- 利用 FFI 方式封装的原生图像库插件，后续升级需要更新插件中图像库包与接口定义；

### nety
https://git.jc-ai.cn/architect/dboard/nety.git
- 连接打印插件，利用 Flutter 插件特性，将精臣打印机连接打印功能抽象接口定义，分平台各自实现；当前此插件的实现阻碍在于 PC、Android、iOS 端 SDK 提供的能力接口不对等，无法优雅封装为统一接口定义；接口定义参见 https://git.jc-ai.cn/architect/dboard/nety/-/blob/master/nety/lib/nety.dart ；