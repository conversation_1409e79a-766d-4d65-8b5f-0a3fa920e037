//
//  JCWKWebView.h
//  MallPackage
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/6/18.
//  Copyright © 2020 luxiguang. All rights reserved.
//

/*
    v1.0.0      2020-06-22
*/
 #import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@protocol JCWKWebViewDelegate <NSObject>
/// 必须实现的方法
@required

/// 显示隐藏底部tab
/// @param type type = 1 ：显示底部tab  ; type = 0 隐藏tab ;
- (void)jcdy_Show_TabbarType:(NSInteger)type;

/// 修改状态栏颜色
/// @param bgColor  color: 状态栏背景色（例如#FEFEFE）
/// @param type type：文字颜色 1 文字深色 0 文字浅色
- (void)jcdy_StatusBar_Color:(NSString *)bgColor withType:(NSInteger)type;

/// 开启扫一扫功能
- (void)jcdy_shopScan;

/// 返回原生app
- (void)jcdy_backHome;

/// 分享URL
/// @param url 链接
- (void)jcdy_share:(NSString *)url;

///图片加载完成
- (void)jcdy_imageComplete;

///点击广告跳转
- (void)jcdy_adSkipper;

/// 可选实现的方法
@optional


/// 提示
/// @param content 提示内容
- (void)weakHint:(NSString *)content;

@end

@interface JCWKWebView : UIView
///代理
@property (nonatomic, weak) id <JCWKWebViewDelegate> delegate;

///是否显示log
@property (nonatomic, assign) BOOL showLog;

///加载的H5的链接----(必传项)
@property (nonatomic, copy) NSString *url;

///base URL---(必传项)
@property (nonatomic, copy) NSString *baseURL;

///重定向地址必须与info中URL Types中设置的一致(必传项)
@property (nonatomic, copy) NSString *schemesURL;

///用户token----(必传项)
@property (nonatomic, copy) NSString *userShopToken;

///打印机ID
@property (nonatomic, copy) NSString *machine_id;

///加载url
- (void)startLoadUrl;

/// 扫一扫返回结果
/// @param code 扫一扫返回的一维码
- (void)returnScanResult:(NSString *)code;

///取消扫一扫
- (void)cancelScan;

///商城埋点
- (void)appUserClickEvent;

/// 微信支付成功回调
- (void)h5PayFinishedGoback;

@end

NS_ASSUME_NONNULL_END
