//
//  JCADPopViewController.m
//  MallPackage
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/7/6.
//  Copyright © 2020 luxiguang. All rights reserved.
//
#import <WebKit/WebKit.h>
#import "UIColor+Extension.h"
#import "JCADPopViewController.h"

///广告点击事件
#define JCDY_AD_SKIPPER @"adSkipper"
///图片加载完成
#define JCDY_IMAGE_COMPLETE @"imgComplete"

#define JC_SCREEN_WEB_WIDTH [UIScreen mainScreen].bounds.size.width

#define JC_SCREEN_WEB_HEIGHT [UIScreen mainScreen].bounds.size.height

@interface JCADPopViewController ()<WKUIDelegate,WKNavigationDelegate,WKScriptMessageHandler>

/// web 配置
@property (nonatomic, strong)  WKWebViewConfiguration *configuration;

///web
@property (nonatomic, strong)  WKWebView *contentWebView;

///取消按钮
@property (nonatomic, strong)  UIButton *refreshBtn;

///虚线
@property (nonatomic, strong)  UIImageView *verLineView;

///加载进度条
@property (nonatomic, strong) UIActivityIndicatorView * indicatorView;
///广告URL
@property (nonatomic, copy)  NSString *adURL;

///点击广告回调
@property(nonatomic,copy) ClickBlock clickBlock;


@end

@implementation JCADPopViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setUp];
}

+(void)showAlterWith:(UIViewController *)superClass withWebURL:(NSString *)url  withClickBlock:(ClickBlock)clickBlock{
    for (UIViewController *controller in superClass.childViewControllers) {
        if ([controller isKindOfClass:[JCADPopViewController class]]) {
            [controller removeFromParentViewController];
            [controller.view removeFromSuperview];
        }
    }
    JCADPopViewController *alter = [[JCADPopViewController alloc] init];
    alter.clickBlock = clickBlock;
    alter.adURL = url;
    [superClass addChildViewController:alter];
    [superClass.view addSubview:alter.view];
}

- (void)cancelAction{
    [self.view removeFromSuperview];
    [self removeFromParentViewController];
}

-(void)setUp{
    self.view.backgroundColor = [UIColor jc_colorWithHexString:@"#000000" alpha:0.6];
    [self initWKWebView];
    [self.view addSubview:self.verLineView];
    [self.view addSubview:self.refreshBtn];
    [self.contentWebView addSubview:self.indicatorView];
    self.indicatorView.center = CGPointMake(CGRectGetWidth(self.contentWebView.frame)/2, CGRectGetHeight(self.contentWebView.frame)/2);
    self.verLineView.frame = CGRectMake((JC_SCREEN_WEB_WIDTH-2)/2, CGRectGetMaxY(self.contentWebView.frame), 2, 40);
    self.refreshBtn.frame = CGRectMake((JC_SCREEN_WEB_WIDTH-60)/2, CGRectGetMaxY(self.verLineView.frame), 60, 60);
    [self.configuration.userContentController addScriptMessageHandler:self name:JCDY_AD_SKIPPER];
    ///图片加载完成通知
    [self.configuration.userContentController addScriptMessageHandler:self name:JCDY_IMAGE_COMPLETE];
    //self.contentWebView.hidden = self.verLineView.hidden = self.refreshBtn.hidden = YES;
    [self.indicatorView startAnimating];
}

- (void)initWKWebView{
    self.configuration = [[WKWebViewConfiguration alloc] init];
    //实例化对象
    self.configuration.userContentController = [WKUserContentController new];
    //接下来我们来进行配置WKWebView的偏好设置WKPreferences:
    WKPreferences *preferences = [WKPreferences new];
    preferences.javaScriptCanOpenWindowsAutomatically = YES;
    self.configuration.preferences = preferences;
    
    self.contentWebView = [[WKWebView alloc]initWithFrame:CGRectMake((JC_SCREEN_WEB_WIDTH*0.2)/2, 100, JC_SCREEN_WEB_WIDTH*0.8, (JC_SCREEN_WEB_HEIGHT * 0.8 * 674/549)-300) configuration:self.configuration];
    [self.contentWebView setOpaque:NO];
    self.contentWebView.layer.cornerRadius = 18;
    self.contentWebView.clipsToBounds = YES;
    self.contentWebView.backgroundColor = [UIColor whiteColor];
    self.contentWebView.scrollView.backgroundColor = [UIColor clearColor];
    self.contentWebView.navigationDelegate = self;
    self.contentWebView.scrollView.scrollEnabled = NO;
    self.contentWebView.UIDelegate = self;
    [self.view addSubview:self.contentWebView];
//    NSString *adPopUrl =[NSString stringWithFormat:@"%@/#/adPopup", self.adURL.length>0?self.adURL:ServerURL1];
//    [self.contentWebView loadRequest:[NSURLRequest requestWithURL:[NSURL URLWithString:adPopUrl]]];
}

#pragma mark - WKNavigationDelegate
- (void)userContentController:(WKUserContentController *)userContentController didReceiveScriptMessage:(WKScriptMessage *)message{
    NSLog(@"body:%@",message.body);
    if ([message.name isEqualToString:JCDY_AD_SKIPPER]) {
        if(self.clickBlock){
            self.clickBlock(message.body);
        }
        [self cancelAction];
    }else if([message.name isEqualToString:JCDY_IMAGE_COMPLETE]){
        //self.contentWebView.hidden = self.verLineView.hidden = self.refreshBtn.hidden = NO;
        [self.indicatorView stopAnimating];
    }
}

- (void)webView:(WKWebView *)webView didFailProvisionalNavigation:(WKNavigation *)navigation withError:(NSError *)error{
    NSLog(@"请求失败");
    [self.indicatorView stopAnimating];
}


#pragma mark - get
-(UIButton *)refreshBtn{
    if(!_refreshBtn){
        _refreshBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_refreshBtn setImage:[UIImage imageNamed:@"AD关闭"] forState:UIControlStateNormal];
        [_refreshBtn addTarget:self action:@selector(cancelAction) forControlEvents:UIControlEventTouchUpInside];
    }
    return _refreshBtn;
}

-(UIImageView *)verLineView{
    if(!_verLineView){
        _verLineView = [[UIImageView alloc] initWithFrame:CGRectZero];
        _verLineView.image = [UIImage imageNamed:@"AD关闭直线"];
    }
    return _verLineView;
}

- (UIActivityIndicatorView *)indicatorView{
    if (!_indicatorView) {
        _indicatorView = [[UIActivityIndicatorView alloc]init];
        _indicatorView.frame = CGRectMake(200, 100, 40, 40);
        _indicatorView.activityIndicatorViewStyle = UIActivityIndicatorViewStyleGray;
    }
    return _indicatorView;
}

@end
