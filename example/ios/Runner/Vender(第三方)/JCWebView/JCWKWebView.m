//
//  JCWKWebView.m
//  MallPackage
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/6/18.
//  Copyright © 2020 luxiguang. All rights reserved.
//

#import "JCWKWebView.h"
#import <WebKit/WebKit.h>
#import "JCWebProgress.h"
#import "UIColor+Extension.h"
#import "JCWebViewMacros.h"
#import "JCApplicationManager.h"

///显示隐藏底部tab
#define JCDY_SHOW_TABBAR @"jcdy_Show_Tabbar"
///修改顶部状态栏背景色跟文字颜色
#define JCDY_STATUSBA_COLOR @"jcdy_StatusBar_Color"
///扫一扫功能
#define JCDY_SHOW_SCAN @"jcdy_shopScan"
///返回原生app
#define JCDY_BACK_HOME @"backHome"
///分享功能
#define JCDY_SHARE_WEB @"jcdy_Share_Web"
///广告点击事件
#define JCDY_AD_SKIPPER @"adSkipper"
///图片加载完成
#define JCDY_IMAGE_COMPLETE @"imgComplete"

#define AI_STR_DEFAULT(str) ([str isKindOfClass:[NSNull class]] || str == nil || [str length] < 1 ? @"" : str )

@interface JCWKWebView()<WKNavigationDelegate,WKScriptMessageHandler,UIScrollViewDelegate>{
    NSMutableURLRequest *webRequest;
    WKWebViewConfiguration *wkConfig;
    ///存储JS方法名数组
    NSArray *configArray;
    NSMutableDictionary *headers;
    

}

@property (nonatomic, strong) WKWebView *wkWebView;

///进度条
@property (nonatomic, strong) JCWebProgress *loadingView;

///自定义的UA，默认为""
@property (nonatomic, copy) NSString *userAgent;

///url的requestHeader里的内容，string形式的key和value，默认@{}
@property (nonatomic, copy) NSDictionary *headerParams;

@end

@implementation JCWKWebView

#pragma -mark init
- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        self.frame = frame;
        configArray = @[JCDY_SHARE_WEB,JCDY_SHOW_TABBAR,JCDY_STATUSBA_COLOR,JCDY_SHOW_SCAN,JCDY_BACK_HOME];
        headers = [NSMutableDictionary dictionary];
        _url = @"";
        _userAgent = @"";
        [self initWeb:frame];
    }
    return self;
}

- (void)initWeb:(CGRect)frame{
    wkConfig = [[WKWebViewConfiguration alloc] init];
    wkConfig.allowsInlineMediaPlayback = YES;
    wkConfig.preferences = [WKPreferences new];
    WKUserContentController *userContentController = [[WKUserContentController alloc] init];
    for (NSString *jsName in configArray) {
        [userContentController addScriptMessageHandler:self name:AI_STR_DEFAULT(jsName)];
    }
    wkConfig.userContentController = userContentController;
    _wkWebView = [[WKWebView alloc]initWithFrame:CGRectMake(0, 0, frame.size.width, frame.size.height) configuration:wkConfig];
    _wkWebView.scrollView.scrollEnabled = NO;
    _wkWebView.navigationDelegate = self;
    _wkWebView.scrollView.delegate = self;
    [self addSubview:_wkWebView];
}

#pragma -mark setter
- (void)setUserAgent:(NSString *)userAgent{
    _userAgent = userAgent;
    if (@available(iOS 12.0, *)){
        NSString *baseAgent = [_wkWebView valueForKey:@"applicationNameForUserAgent"];
        NSString *userAgent = [NSString stringWithFormat:@"%@ %@",baseAgent,_userAgent];
        [_wkWebView setValue:userAgent forKey:@"applicationNameForUserAgent"];
    }
    [_wkWebView evaluateJavaScript:@"navigator.userAgent" completionHandler:^(id _Nullable result, NSError * _Nullable error) {
        NSString *newUA = [NSString stringWithFormat:@"%@ %@",result,self->_userAgent];
        self->_wkWebView.customUserAgent = newUA;
    }];
    //在WKWebView上禁用WKActionSheet
    //[self.wkWebView evaluateJavaScript:@"document.body.style.webkitTouchCallout='none';" completionHandler:nil];

}

- (void)setUrl:(NSString *)url{
    NSString *urlString = [url stringByReplacingOccurrencesOfString:@"\r" withString:@""];
    urlString = [urlString stringByReplacingOccurrencesOfString:@"\r" withString:@""];
    urlString = [urlString stringByReplacingOccurrencesOfString:@"\r" withString:@""];
    _url = urlString;
}

- (void)setHeaderParams:(NSDictionary *)headerParams{
    _headerParams = headerParams;
    [headers setValuesForKeysWithDictionary:_headerParams];
}

#pragma mark - webView加载
- (void)startLoadUrl{
    if (_url && _url.length > 0){
        webRequest = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:_url] cachePolicy:NSURLRequestReloadIgnoringCacheData timeoutInterval:20];
        NSArray *headerKeys = headers.allKeys;
        for (NSInteger i=0;i<headerKeys.count;i++) {
            NSString *key = AI_STR_DEFAULT(headerKeys[i]);
            NSString *value = AI_STR_DEFAULT(headers[key]);
            [webRequest setValue:value forHTTPHeaderField:key];
        }
        [_wkWebView loadRequest:webRequest];
    }
}

#pragma mark - WKScriptMessageHandler
///  WKWebView收到ScriptMessage时回调此方法
- (void)userContentController:(WKUserContentController *)userContentController didReceiveScriptMessage:(WKScriptMessage *)message {
    if (self.showLog) {
        NSLog(@"js call native:%@===message:%@",message.name,message.body);
    }
    if ([message.name caseInsensitiveCompare:JCDY_STATUSBA_COLOR] == NSOrderedSame) {
        NSDictionary *dict = [self jcdy_dictionaryWithJsonString:message.body];
        if([[dict allKeys] containsObject:@"color"]&& [[dict allKeys] containsObject:@"type"]) {
            if (self.delegate) {
                [self.delegate jcdy_StatusBar_Color:[dict objectForKey:@"color"] withType:[[dict objectForKey:@"type"] integerValue]];
            }
        }else{
            if (self.showLog) {
                NSLog(@"字典不存在key：color 或 type");
            }
        }
    }else if ([message.name caseInsensitiveCompare:JCDY_SHOW_TABBAR] == NSOrderedSame) {
        if (self.delegate) {
            [self.delegate jcdy_Show_TabbarType:[[NSString stringWithFormat:@"%@",message.body]integerValue]];
        }
    }else if ([message.name caseInsensitiveCompare:JCDY_BACK_HOME] == NSOrderedSame) {
        if (self.delegate) {
            [self.delegate jcdy_backHome];
        }
    }else if ([message.name caseInsensitiveCompare:JCDY_SHOW_SCAN] == NSOrderedSame) {
        if (self.delegate) {
            [self.delegate jcdy_shopScan];
        }
    }else if ([message.name caseInsensitiveCompare:JCDY_SHARE_WEB] == NSOrderedSame) {
        if (self.delegate) {
            [self.delegate jcdy_share:[NSString stringWithFormat:@"%@",message.body]];
        }
    }else if ([message.name caseInsensitiveCompare:JCDY_AD_SKIPPER] == NSOrderedSame) {
        if (self.delegate) {
            [self.delegate jcdy_adSkipper];
        }
    }else if ([message.name caseInsensitiveCompare:JCDY_IMAGE_COMPLETE] == NSOrderedSame) {
        if (self.delegate) {
            [self.delegate jcdy_imageComplete];
        }
    }
}

#pragma mark - WKWebview
- (void)webView:(WKWebView *)webView decidePolicyForNavigationAction:(WKNavigationAction *)navigationAction decisionHandler:(void (^)(WKNavigationActionPolicy))decisionHandler{
    if (self.showLog) {
//        NSLog(@"捕抓当前页面来源的url=%@",webView.URL.absoluteString);
        NSLog(@"铺抓当前页面的请求URL=%@",[navigationAction.request.URL absoluteString]);
    }
    __block  WKNavigationActionPolicy actionPolicy = WKNavigationActionPolicyAllow;
    NSString *strURL = [navigationAction.request.URL absoluteString];
    NSString *wxPayRedirectUrl = [NSString stringWithFormat:@"%@://",SchemesWX];
    NSString *redirect_Url = [NSString stringWithFormat:@"redirect_url=%@",wxPayRedirectUrl];
    if([strURL hasPrefix:@"https://wx.tenpay.com/cgi-bin/mmpayweb-bin/checkmweb"] && ![strURL hasSuffix:redirect_Url]){
        ///微信支付重定向地址
        decisionHandler(WKNavigationActionPolicyCancel);
        NSString *redirectUrl = nil;
        if([strURL containsString:@"redirect_url="]){
            NSRange range = [strURL rangeOfString:@"redirect_url="];
            redirectUrl = [[strURL substringToIndex:range.location] stringByAppendingString:redirect_Url];
        }else{
            redirectUrl = [strURL stringByAppendingString:redirect_Url];
        }
        NSMutableURLRequest *newRequest = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:redirectUrl] cachePolicy:NSURLRequestUseProtocolCachePolicy timeoutInterval:30];
        newRequest.allHTTPHeaderFields = navigationAction.request.allHTTPHeaderFields;
        NSLog(@"微信支付referer:%@",wxPayRedirectUrl);
        [newRequest setValue:wxPayRedirectUrl forHTTPHeaderField:@"Referer"];
        newRequest.URL = [NSURL URLWithString:redirectUrl];
        [webView loadRequest:newRequest];
        return;
    }
    if ([@"jcydy://com.suofang.jcbqdy/orderlist" isEqualToString:strURL] || [navigationAction.request.URL.scheme isEqualToString:SchemesWX]) {
        actionPolicy = WKNavigationActionPolicyCancel;
        NSString *url = [NSString stringWithFormat:@"%@/h5_project/index.html#/paySuccess?", JCMALLURL];
        [self payResultWithURL:url];
    }
    
    
    BOOL isIntercepted = NO;
    isIntercepted = [[AlipaySDK defaultService] payInterceptorWithUrl:[navigationAction.request.URL absoluteString] fromScheme:SchemesWX callback:^(NSDictionary *result) {
        // 处理支付结果
        // isProcessUrlPay 代表 支付宝已经处理该URL
        /**
         9000
         认证通过。
         9001
         唤起支付宝成功，可以不做处理。
         6002
         网络异常。
         6001
         用户取消了业务流程，主动退出。
         6003
         传入 bizcode 为 kAPVerifySDKBizCodeNew，但无法打开支付宝。
         4000
         系统异常。
         */
        if ([result[@"isProcessUrlPay"] boolValue]) {//此处没有做状态码判断,统一跳转结果查询页面
            actionPolicy = WKNavigationActionPolicyCancel;
//            NSString *url = [NSString stringWithFormat:@"%@/h5_project/#/?", JCMALLURL];
            NSString *url = [NSString stringWithFormat:@"%@/h5_project/index.html#/paySuccess?", JCMALLURL];
            [self payResultWithURL:url];
        }
    }];
    if(isIntercepted){
        if ([[UIApplication sharedApplication] canOpenURL:[NSURL URLWithString:@"alipay:"]]) {//如果安装了支付宝,跳转支付宝的时候,后台将界面调到结果页
            NSString *url = [NSString stringWithFormat:@"%@/h5_project/index.html#/paySuccess?", JCMALLURL];
            [self payResultWithURL:url];
        }
        actionPolicy =WKNavigationActionPolicyCancel;
    }
    
    
    NSString *urlString = [[navigationAction.request.URL absoluteString] stringByReplacingPercentEscapesUsingEncoding:NSUTF8StringEncoding];
    NSLog(@"微信打开url:%@",urlString);
    if ([urlString containsString:@"weixin://wap/pay?"]) {
        actionPolicy =WKNavigationActionPolicyCancel;
        //解决wkwebview weixin://无法打开微信客户端的处理
        NSURL*url = [NSURL URLWithString:urlString];
        if(![[UIApplication sharedApplication] canOpenURL:url]) {
            if(self.delegate){
                [self.delegate weakHint:@"未检测到微信APP，请您先安装"];
            }
        } else {
            [[UIApplication sharedApplication] openURL:url options:@{UIApplicationOpenURLOptionUniversalLinksOnly: @NO} completionHandler:^(BOOL success) {
                
                
            }];
        }
    }

    decisionHandler(actionPolicy);
}

- (void)payResultWithURL:(NSString *)urlString {
    //TODO:根据场景选择entrance_type_id, platform_system_id是做啥的
   NSString *url = [NSString stringWithFormat:@"%@entrance_type_id=%@&token=%@&version=%@&platform_system_id=1",urlString,@"1",kJC_Mall_Token,XY_APP_VERSION];
    [self.wkWebView loadRequest:[NSURLRequest requestWithURL:[NSURL URLWithString:url]]];
}

- (void)webView:(WKWebView *)webView decidePolicyForNavigationResponse:(WKNavigationResponse *)navigationResponse decisionHandler:(void (^)(WKNavigationResponsePolicy))decisionHandler {
    if ([navigationResponse.response isKindOfClass:[NSHTTPURLResponse class]]) {
        NSHTTPURLResponse * response = (NSHTTPURLResponse *)navigationResponse.response;
        if (self.showLog) {
            NSLog(@"网络状态码：%zd",response.statusCode);
        }
    }
    decisionHandler(WKNavigationResponsePolicyAllow);
}
///当Web内容开始在Web视图中加载时调用
- (void)webView:(WKWebView *)webView didStartProvisionalNavigation:(WKNavigation *)navigation{
    _loadingView = [JCWebProgress layerWithFrame:CGRectMake(0, 0, self.frame.size.width, 2)];
    [self.layer addSublayer:_loadingView];
    [_loadingView startLoad];

}
///当Web视图开始接收Web内容时调用
- (void)webView:(WKWebView *)webView didCommitNavigation:(WKNavigation *)navigation{
    //[_loadingView finishedLoad];
}
///导航完成时调用
- (void)webView:(WKWebView *)webView didFinishNavigation:(WKNavigation *)navigation{
    [_loadingView finishedLoad];
}
///加载错误时候才调用
- (void)webView:(WKWebView *)webView didFailProvisionalNavigation:(WKNavigation *)navigation withError:(NSError *)error{
    [_loadingView finishedLoad];
    if(error.code == NSURLErrorCancelled)  {
        return;
    }
    if (error.code == NSURLErrorUnsupportedURL) {
        return;
    }
}

#pragma mark - UIScrollViewDelegate
///WKWebView中禁用放大手势
- (void)scrollViewDidZoom:(UIScrollView *)scrollView;{
    [scrollView setZoomScale:1.0 animated:NO];
}

#pragma mark - 外部方法
/// 扫一扫返回结果
/// @param code 扫一扫返回的一维码
- (void)returnScanResult:(NSString *)code{
    [self.wkWebView evaluateJavaScript:[NSString stringWithFormat:@"returnScanResult('%@')",code] completionHandler:^(id response, NSError *error) {
        if (error && self.showLog) {
            NSLog(@"%@",error);
        }
    }];
}
///取消扫一扫
- (void)cancelScan{
    [self.wkWebView evaluateJavaScript:@"cancelScan()" completionHandler:^(id response, NSError *error) {
        if (error && self.showLog) {
            NSLog(@"%@",error);
        }
    }];
}
///商城埋点
- (void)appUserClickEvent{
    [self.wkWebView evaluateJavaScript:@"appUserClickEvent()" completionHandler:^(id response, NSError *error) {
        if (error && self.showLog) {
            NSLog(@"%@",error);
        }
    }];
}
/// 微信支付成功回调
- (void)h5PayFinishedGoback{
    NSURL *url = [NSURL URLWithString:JumpResultView];
    NSURLRequest *req = [NSURLRequest requestWithURL:url ];
    [self.wkWebView loadRequest:req];
}
#pragma mark - 工具方法
/// JSON字符串转化为字典
- (NSDictionary *)jcdy_dictionaryWithJsonString:(NSString *)jsonString{
    if (jsonString == nil) {
        return nil;
    }
    NSData *jsonData = [jsonString dataUsingEncoding:NSUTF8StringEncoding];
    NSError *err;
    NSDictionary *dic = [NSJSONSerialization JSONObjectWithData:jsonData
                                                        options:NSJSONReadingMutableContainers
                                                          error:&err];
    if(err){
        if (self.showLog) {
            NSLog(@"json解析失败：%@",err);
        }
        return nil;
    }
    return dic;
}
///跳转到支付结果页
- (void)loadPayResultView{
    NSString *url = [NSString stringWithFormat:@"%@/h5_project/index.html#/paySuccess?", self.baseURL];
    NSString *versionComplateString = [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleShortVersionString"];
    if([url rangeOfString:@"?"].location != NSNotFound){
        url = [NSString stringWithFormat:@"%@&back_app=1&token=%@&version=%@&platform_system_id=1",url,self.userShopToken,versionComplateString];
    }else{
        url = [NSString stringWithFormat:@"%@?back_app=1&token=%@&version=%@&platform_system_id=1" ,url,self.userShopToken,versionComplateString];
    }
    [self.wkWebView loadRequest:[NSURLRequest requestWithURL:[NSURL URLWithString:url]  cachePolicy:NSURLRequestUseProtocolCachePolicy timeoutInterval:20]];
}

- (void)dealloc{
    [wkConfig.userContentController removeAllUserScripts];
    _wkWebView.navigationDelegate = nil;
    _wkWebView.scrollView.delegate = nil;
}

@end
