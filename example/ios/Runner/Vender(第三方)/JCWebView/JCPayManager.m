//
//  JCPayManager.m
//  Runner
//
//  Created by long he on 2021/5/20.
//

#import "JCPayManager.h"
#import <UIKit/UIKit.h>
#import "JCWebViewMacros.h"
#import "JCApplicationManager.h"
#import "DCRequestMacro.h"
#import <AlipaySDK/AlipaySDK.h>
//#import "AppEventChannel.h"
@implementation JCPayManager

+ (instancetype)singleton {
    static JCPayManager *manager;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        manager = [[JCPayManager alloc] init];
    });
    return  manager;
}
- (instancetype)init
{
    self = [super init];
    if (self) {
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(h5PayFinishedGoback) name:JC_PaySuccess object:nil];
    }
    return self;
}
- (void)h5PayFinishedGoback {
    NSLog(@"支付结果返回");
//    [AppEventChannel.shareInstance eventData:@{@"pay":@"1",@"payResult":JumpResultView}];
}
- (NSDictionary *)payWithURL:(NSDictionary *)param completion:(void (^)(NSDictionary * _Nonnull))completion{
    NSString *str = param[@"url"];
   __block NSString *shouldCancel = @"0";
    NSString *wxPayRedirectUrl = [NSString stringWithFormat:@"%@://iwantbacktoapp/",SchemesWX];
    NSString *redirect_Url = [NSString stringWithFormat:@"redirect_url=%@",wxPayRedirectUrl];
    NSLog(@"xxxxxxxxxxxx>>>>>%@",str);
    if([str hasPrefix:@"https://wx.tenpay.com/cgi-bin/mmpayweb-bin/checkmweb"] && ![str hasSuffix:redirect_Url]){
        shouldCancel = @"1";
        NSString *redirectUrl = nil;
        if([str containsString:@"redirect_url="]){
            NSRange range = [str rangeOfString:@"redirect_url="];
            redirectUrl = [[str substringToIndex:range.location] stringByAppendingString:redirect_Url];
        }else{
            redirectUrl = [str stringByAppendingString:redirect_Url];
        }
        NSDictionary *param = @{
            @"Referer":wxPayRedirectUrl
        };
        return @{@"url":redirectUrl,@"cancel":shouldCancel,@"param":param};
    }
    if ([@"jcydy://com.suofang.jcbqdy/orderlist" isEqualToString:str] || [str hasPrefix:SchemesWX]) {
        shouldCancel = @"0";
        NSString *url = [self getPaySuccessURL];
        return @{@"url":url,@"cancel":shouldCancel};
    }
    
    BOOL isIntercepted = NO;
    isIntercepted = [[AlipaySDK defaultService] payInterceptorWithUrl:str fromScheme:SchemesWX callback:^(NSDictionary *result) {
        shouldCancel = @"1";
        // 处理支付结果
        // isProcessUrlPay 代表 支付宝已经处理该URL
        if ([result[@"isProcessUrlPay"]boolValue]) {
            NSString *url = [self getPaySuccessURL];
            if (completion) {
                completion(@{@"url":url,@"cancel":@"1"});
            }
        }
    }];
    if(isIntercepted){
//        if ([[UIApplication sharedApplication] canOpenURL:[NSURL URLWithString:@"alipay:"]]) {//如果安装了支付宝,跳转支付宝的时候,后台将界面调到结果页
        shouldCancel = @"1";
        NSString *url = [self getPaySuccessURL];
        return @{@"url":url,@"cancel":shouldCancel};
//        }
        
//        shouldCancel = @"0";
//        return @{@"cancel":@"1"};
    }
    
    NSString *urlString = [str stringByReplacingPercentEscapesUsingEncoding:NSUTF8StringEncoding];
    if ([urlString containsString:@"weixin://wap/pay?"]) {
        shouldCancel = @"1";
        //解决wkwebview weixin://无法打开微信客户端的处理
        NSURL*url = [NSURL URLWithString:urlString];
        BOOL bSucc = [[UIApplication sharedApplication] canOpenURL:url];
        if(!bSucc) {
            NSLog(@"未检测到微信APP，请您先安装");
            return @{@"cancel":@"0",@"errCode":@"-1"};
        } else {
            [[UIApplication sharedApplication] openURL:url options:@{UIApplicationOpenURLOptionUniversalLinksOnly: @NO} completionHandler:^(BOOL success) {
            }];
        }
    }
    return @{@"cancel":shouldCancel};
}

- (NSString *)getPaySuccessURL {
    NSString *urlString = [NSString stringWithFormat:@"%@/h5_project/index.html#/paySuccess?", JCMALLURL];
    NSString *url = [NSString stringWithFormat:@"%@entrance_type_id=%@&token=%@&version=%@&platform_system_id=1",urlString,@"1",kJC_Mall_Token,XY_APP_VERSION];
    return url;
}


- (NSString *)getResultURL{
    NSString *url = [NSString stringWithFormat:@"%@/h5_project/index.html#/paySuccess?", self.baseURL];
    NSString *versionComplateString = [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleShortVersionString"];
    if([url rangeOfString:@"?"].location != NSNotFound){
        url = [NSString stringWithFormat:@"%@&back_app=1&token=%@&version=%@&platform_system_id=1",url,self.userShopToken,versionComplateString];
    }else{
        url = [NSString stringWithFormat:@"%@?back_app=1&token=%@&version=%@&platform_system_id=1" ,url,self.userShopToken,versionComplateString];
    }
    return  url;
}

@end
