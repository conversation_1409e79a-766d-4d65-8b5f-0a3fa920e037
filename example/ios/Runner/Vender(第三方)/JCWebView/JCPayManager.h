//
//  JCPayManager.h
//  Runner
//
//  Created by long he on 2021/5/20.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface JCPayManager : NSObject
@property(nonatomic,copy)NSString *schemesURL;
///用户token----(必传项)
@property (nonatomic, copy) NSString *userShopToken;
///base URL---(必传项)
@property (nonatomic, copy) NSString *baseURL;

+ (instancetype)singleton;
- (NSDictionary *)payWithURL:(NSDictionary *)param completion:(void(^)(NSDictionary *result))completion;
@end

NS_ASSUME_NONNULL_END
