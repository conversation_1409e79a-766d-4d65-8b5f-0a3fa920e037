//
//  JCWKWebViewController.h
//  MallPackage
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/6/24.
//  Copyright © 2020 luxiguang. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN
@class JCWKWebView;
@interface JCWKWebViewController : UIViewController

#pragma mark ---- 需要初始化参数 ------

///是否显示log
@property (nonatomic, assign) BOOL showLog;

///完整URL---(商城首页必传项)
@property (nonatomic, copy) NSString *completeURL;

///base URL---(商城首页必传项) eg:@"https://shop.jc-saas.com"
@property (nonatomic, copy) NSString *baseURL;

///重定向地址必须与info中URL Types中设置的一致(必传项)
@property (nonatomic, copy) NSString *schemesURL;

///用户token----(商城首页必传项)
@property (nonatomic, copy) NSString *userShopToken;

///打印机ID----(商城首页必传项)
@property (nonatomic, copy) NSString *machine_id;

///分享平台数组 @[@(UMSocialPlatformType_WechatSession),@(UMSocialPlatformType_Sina),@(UMSocialPlatformType_QQ),@(UMSocialPlatformType_DingDing)]
@property (nonatomic,strong) NSArray*preDefinePlatforms;

#pragma mark ---- 方法 ------

@property (strong, nonatomic) JCWKWebView *webView;
- (instancetype)initWithFrame:(CGRect)frame;
/// 加载webview
- (void)preloadingVC;

///刷新webView
- (void)loadWKWebView;

///刷新根据Url路径刷新商城
- (void)refreshWebViewWith:(NSString *)myUrlStr;

///添加埋点
- (void)appUserClickEvent;

@end

NS_ASSUME_NONNULL_END
