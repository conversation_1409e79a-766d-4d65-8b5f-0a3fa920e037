//
//  JCWebViewMacros.h
//  MallPackage
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/7/2.
//  Copyright © 2020 luxiguang. All rights reserved.
//


#ifndef JCWebViewMacros_h
#define JCWebViewMacros_h

#import <AlipaySDK/AlipaySDK.h>
//#import <UMShare/UMShare.h>
//#import <UShareUI/UShareUI.h>
//#import <UMCommon/UMCommon.h>
#import "JCWKWebViewController.h"
//#import "AppDelegate+Share.h"

//LBXScan 如果需要使用LBXScanViewController控制器代码，那么下载了那些模块，请定义对应的宏
#define LBXScan_Define_Native  //包含native库
#define LBXScan_Define_ZXing   //包含ZXing库
#define LBXScan_Define_ZBar   //包含ZBar库
#define LBXScan_Define_UI     //包含界面库

///友盟
#define UMAppkey @"5ea7f3f0167edd77a9000148"
///微信
#define WXAppkey @"wx3e8fa98f8dbda7ec"
#define WXAppSecret @"d937c6f8558ab6cad1b8e34e1b7fe460"
///QQ
#define QQAppkey @"1110400767"
#define QQAppSecret @"o0uE3E2Acly0HtyM"
///微博
#define WBAppkey @"3131827243"
#define WBAppSecret @"bc63dcf6056a2d074d0eebdc85d8da90"
///钉钉
#define DDAppkey @"dingoarxelfqtqo2qqamd0"
#define DDAppSecret @"TCrQWfxpkUKSckAA3vfIhr2gOC6T_YOneNdIOh7FoKm5ute1xmpHE23TUPvXhnBQ"

///广告显示页面
#define ServerURL1 @"http://print.web3.jc-saas.com"//正式环境 @"http://print.web3.jc-saas.com" 测试环境 @"http://print.web3.p.jc-test.cn"

///网页Base URL
#define JC_WkWebBaseURL @"https://shop.jc-saas.com" //正式环境 @"https://shop.jc-saas.com"    开发环境 @"http://shop.jc-dev.cn"  测试环境 @"https://m.shop.jc-test.cn/#"

///调用支付的app注册在info.plist中的scheme，规则配置: https://www.jianshu.com/p/f27ffbf44f4d    
#define JC_SchemesURL @"www.jc-saas.com" // 正式环境 @"www.jc-saas.com" 开发环境  @"www.jc-dev.cn"

///支付成功跳转标志
#define JumpResultView @"jcydy://com.suofang.jcbqdy/orderlist"

///支付成功通知
#define JC_PaySuccess @"H5PayFinishedGoback"

#endif /* JCWebViewMacros_h */
