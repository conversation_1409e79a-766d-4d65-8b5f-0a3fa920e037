////
////  AppDelegate+Share.m
////  MallPackage
////
////  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/7/1.
////  Copyright © 2020 luxiguang. All rights reserved.
////
//
//#import "AppDelegate+Share.h"
//#import <UMShare/UMShare.h>
//#import "JCWebViewMacros.h"
//
//@implementation AppDelegate (Share)
//
//
//
//- (void)confitUShareSettings
//{
//    //https://www.jianshu.com/p/695bea006c78
//    [UMConfigure initWithAppkey:UMAppkey channel:@"App Store"];
//    /* 打开调试日志 */
//    [[UMSocialManager defaultManager] openLog:YES];
//}
//
//- (void)configUSharePlatforms
//{
//    /* 设置微信的appKey和appSecret */
//    [[UMSocialManager defaultManager] setPlaform:UMSocialPlatformType_WechatSession appKey:WXAppkey appSecret:WXAppSecret redirectURL:@"http://mobile.umeng.com/social"];
//    /*设置小程序回调app的回调*/
//        [[UMSocialManager defaultManager] setLauchFromPlatform:(UMSocialPlatformType_WechatSession) completion:^(id userInfoResponse, NSError *error) {
//        NSLog(@"setLauchFromPlatform:userInfoResponse:%@",userInfoResponse);
//    }];
//    /*
//     * 移除相应平台的分享，如微信收藏
//     */
//    //[[UMSocialManager defaultManager] removePlatformProviderWithPlatformTypes:@[@(UMSocialPlatformType_WechatFavorite)]];
//
//    /* 设置分享到QQ互联的appID
//     * U-Share SDK为了兼容大部分平台命名，统一用appKey和appSecret进行参数设置，而QQ平台仅需将appID作为U-Share的appKey参数传进即可。
//    */
//    [[UMSocialManager defaultManager] setPlaform:UMSocialPlatformType_QQ appKey:QQAppkey/*设置QQ平台的appID*/  appSecret:nil redirectURL:@"http://mobile.umeng.com/social"];
//
//    /* 设置新浪的appKey和appSecret */
//    [[UMSocialManager defaultManager] setPlaform:UMSocialPlatformType_Sina appKey:WBAppkey  appSecret:WBAppSecret redirectURL:@"https://sns.whalecloud.com/sina2/callback"];
//
//    /* 钉钉的appKey */
//    [[UMSocialManager defaultManager] setPlaform: UMSocialPlatformType_DingDing appKey:DDAppkey appSecret:nil redirectURL:nil];
//
//   
//
//}
//@end
