//
//  JCADPopViewController.h
//  MallPackage
//
//  Created by xiaoyao on 2020/7/6.
//  Copyright © 2020 luxiguang. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN


typedef void(^ClickBlock)(NSString*);

typedef void(^LoadBlock)(NSString*);

@interface JCADPopViewController : UIViewController


+(void)showAlterWith:(UIViewController *)superClass withWebURL:(NSString *)url  withClickBlock:(ClickBlock)clickBlock;

@end

NS_ASSUME_NONNULL_END
