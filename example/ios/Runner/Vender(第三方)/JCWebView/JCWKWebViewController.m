//
//  JCWKWebViewController.m
//  MallPackage
//
//  Created by x<PERSON><PERSON><PERSON> on 2020/6/24.
//  Copyright © 2020 luxiguang. All rights reserved.
//
#import "StyleDIY.h"
#import "Global.h"
#import "JCWKWebView.h"
#import "JCWebViewMacros.h"
#import "UIColor+Extension.h"
#import "UIView+JCCategory.h"
#import "JCWKWebViewController.h"
#import "QQLBXScanViewController.h"
#import <AVFoundation/AVFoundation.h>
#import "NotificationMacro.h"
#import "UIView+PlaceHolder.h"

@interface JCWKWebViewController ()<JCWKWebViewDelegate>

///主view
//@property (strong, nonatomic) JCWKWebView *webView;

///状态栏风格
@property (nonatomic,assign) UIStatusBarStyle statusBarStyle;

@property(nonatomic,assign)CGRect currentFrame;

@end

@implementation JCWKWebViewController

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super init];
    if (self) {
        self.currentFrame = frame;
    }
    return self;
}
- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = [UIColor whiteColor];
    XYWeakSelf;
    if (NETWORK_STATE_ERROR) {
        [self.view showNoNetWorkView:@"当前网络不佳，请稍候再试哦~" Action:JCNoDataActionBackforward completion:^(JCNoDataAction type) {
            if (type == JCNoDataActionBackforward) {
                [weakSelf.navigationController popViewControllerAnimated:true];
            }
        }];
        
    }else{
        self.statusBarStyle = UIStatusBarStyleDefault;
        [self initUI];
        [self requesWebViewURL];
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(h5PayFinishedGoback) name:JC_PaySuccess object:nil];
    }
  
}


- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    [self.navigationController setNavigationBarHidden:YES animated:NO];
}

- (void)viewWillDisappear:(BOOL)animated{
    [super viewWillDisappear:animated];
//    [self.navigationController setNavigationBarHidden:NO animated:NO];
}

#pragma mark - UI
- (void)initUI{
    if (!_webView) {
        [self.view addSubview:self.webView];
    }
}


#pragma mark - get
- (JCWKWebView *)webView{
    if (!_webView) {
        if (self.currentFrame.size.width != 0) {
            _webView = [[JCWKWebView alloc] initWithFrame:self.currentFrame];
        }else{
        _webView = [[JCWKWebView alloc] initWithFrame:CGRectMake(0, 0, UIScreen.mainScreen.bounds.size.width, UIScreen.mainScreen.bounds.size.height)];
        }
        _webView.delegate = self;
    }
    return _webView;
}

#pragma mark - JCWKWebViewDelegate
///设置状态栏颜色及背景
- (void)jcdy_StatusBar_Color:(NSString *)bgColor withType:(NSInteger)type{
    self.view.backgroundColor = [UIColor jc_colorWithHexString:bgColor];
    if (type == 1) {
        self.statusBarStyle = UIStatusBarStyleDefault;
    }else{
        self.statusBarStyle = UIStatusBarStyleLightContent;
    }
    [self setNeedsStatusBarAppearanceUpdate];
}
///显示隐藏底部tab
- (void)jcdy_Show_TabbarType:(NSInteger)type{

//    if (type == 1) {
    
//        _webView.jc_height = UIScreen.mainScreen.bounds.size.height-20 - ([self isIPhoneX] ? 86 : 49);
//        self.tabBarController.tabBar.hidden = NO;
//    }else{

//        _webView.jc_height = UIScreen.mainScreen.bounds.size.height-20;
//        self.tabBarController.tabBar.hidden = YES;
//    }

}
///二维码扫描
- (void)jcdy_shopScan{
    __weak typeof(self) weakSelf = self;
    NSString *mediaType = AVMediaTypeVideo;//读取媒体类型
    AVAuthorizationStatus authStatus = [AVCaptureDevice authorizationStatusForMediaType:mediaType];//读取设备授权状态
    if(authStatus == AVAuthorizationStatusRestricted || authStatus == AVAuthorizationStatusDenied){
        UIAlertController* alert = [UIAlertController alertControllerWithTitle:@"提示" message:@"请前往系统应用设置中，允许访问您的相机" preferredStyle:UIAlertControllerStyleAlert];
        UIAlertAction* defaultAction = [UIAlertAction actionWithTitle:@"设置" style:UIAlertActionStyleDefault handler:^(UIAlertAction * action) {
            NSURL * url = [NSURL URLWithString:UIApplicationOpenSettingsURLString];
            if([[UIApplication sharedApplication] canOpenURL:url]) {
                NSURL*url =[NSURL URLWithString:UIApplicationOpenSettingsURLString];
                [[UIApplication sharedApplication] openURL:url options:@{UIApplicationOpenURLOptionUniversalLinksOnly: @NO} completionHandler:^(BOOL success) {}];
            }
         }];
        UIAlertAction* cancelAction = [UIAlertAction actionWithTitle:@"取消" style:UIAlertActionStyleDefault handler:^(UIAlertAction * action) {}];
        [alert addAction:defaultAction];
        [alert addAction:cancelAction];
        [self presentViewController:alert animated:YES completion:nil];
        return;
    }
    QQLBXScanViewController *vc = [QQLBXScanViewController new];
    vc.scanCreateType = 4;
    vc.libraryType = [Global sharedManager].libraryType;
    vc.scanCodeType = [Global sharedManager].scanCodeType;
    vc.style = [StyleDIY qqStyle];
    vc.scanBlock = ^(NSString *x){
        if(x.length == 0){
            [weakSelf.webView cancelScan];
            return;
        }
        if (x.length > 0) {
            [weakSelf.webView returnScanResult:x];
        };
    };
    //镜头拉远拉近功能
    vc.isVideoZoom = YES;
    [self presentViewController:vc animated:YES completion:^{}];
}
///返回原生app
- (void)jcdy_backHome{
    [self.navigationController popViewControllerAnimated:true];
    
//    NSArray *arr = self.tabBarController.viewControllers;
//    UINavigationController *navi = arr[1];
//    UIViewController *vc = navi.visibleViewController;
//    if([vc isKindOfClass:[self class]]){
//        self.tabBarController.selectedIndex = 1;
//        [self.navigationController popToRootViewControllerAnimated:NO];
//    }
}
///分享
- (void)jcdy_share:(NSString *)url{
//    if(self.preDefinePlatforms.count > 0){
//        [UMSocialUIManager setPreDefinePlatforms:@[@(UMSocialPlatformType_WechatSession),@(UMSocialPlatformType_Sina),@(UMSocialPlatformType_QQ),@(UMSocialPlatformType_DingDing)]];
//        [UMSocialUIManager showShareMenuViewInWindowWithPlatformSelectionBlock:^(UMSocialPlatformType platformType, NSDictionary *userInfo) {
//            [self shareWebPageToPlatformType:platformType];
//        }];
//    }else{
//        if (self.showLog) {
//            NSLog(@"请检查分享图片及标题是否正确");
//        }
//    }
}

- (void)weakHint:(NSString *)content {
    UIAlertController *alertVC = [UIAlertController alertControllerWithTitle:@"提示" message:content preferredStyle:UIAlertControllerStyleAlert];
    UIAlertAction *action = [UIAlertAction actionWithTitle:@"确定" style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
        
    }];
    [alertVC addAction:action];
    [self presentViewController:alertVC animated:true completion:nil];
}

#pragma mark - 公开方法
- (void)preloadingVC{
    if (!NETWORK_STATE_ERROR) {
        [self initUI];
    }
}

///刷新webView
- (void)loadWKWebView{
    [self requesWebViewURL];
}

//刷新根据Url路径刷新商城
- (void)refreshWebViewWith:(NSString *)myUrlStr{
    NSLog(@"开始加载URL：%@",myUrlStr);
    NSString *lastUrl = @"";
    if([myUrlStr rangeOfString:@"#/"].location != NSNotFound){
        if([myUrlStr rangeOfString:@"?"].location != NSNotFound){
            lastUrl = [NSString stringWithFormat:@"%@&", myUrlStr];
        }else{
            lastUrl = [NSString stringWithFormat:@"%@?", myUrlStr];
        }
    }else{
        lastUrl = [NSString stringWithFormat:@"%@#/?", myUrlStr];
    }
    _webView.url = lastUrl;
    [self.webView startLoadUrl];
}

- (void)appUserClickEvent{
    if (_webView) {
        [_webView appUserClickEvent];
    }
}

#pragma mark - 工具
///判断是否异型屏幕
- (BOOL)isIPhoneX {
    if (@available(iOS 11.0, *)) {
        CGFloat height = [[UIApplication sharedApplication] delegate].window.safeAreaInsets.bottom;
        return (height > 0);
    } else {
        return NO;
    }
}
///初始化参数
- (void)initWebParameter{
    if (_webView) {
        _webView.showLog = self.showLog;
        _webView.baseURL = self.baseURL;//正式环境 @"https://shop.jc-saas.com"    开发环境 @"http://shop.jc-dev.cn"
        _webView.schemesURL = self.schemesURL;// 正式环境 @"www.jc-saas.com" 开发环境  @"www.jc-dev.cn"
        _webView.userShopToken = self.userShopToken;
        _webView.machine_id = self.machine_id;
    }else{
        if (self.showLog) {
            NSLog(@"请先初始化URL相关参数webview");
        }
    }
}

/// 开始请求web
- (void)requesWebViewURL{
    [self initWebParameter];
    _webView.url = self.completeURL;
    [self.webView startLoadUrl];
}
///分享内容
//- (void)shareWebPageToPlatformType:(UMSocialPlatformType)platformType{
//    //创建分享消息对象
//    UMSocialMessageObject *messageObject = [UMSocialMessageObject messageObject];
//    //创建网页内容对象
//    UMShareWebpageObject *shareObject = [UMShareWebpageObject shareObjectWithTitle:@"分享标题" descr:@"分享内容描述" thumImage:[UIImage imageNamed:@"icon"]];
//    //设置网页地址
//    shareObject.webpageUrl =@"http://mobile.umeng.com/social";
//    //分享消息对象设置分享内容对象
//    messageObject.shareObject = shareObject;
//    //调用分享接口
//    __weak typeof(self) weakSelf = self;
//    [[UMSocialManager defaultManager] shareToPlatform:platformType messageObject:messageObject currentViewController:self completion:^(id data, NSError *error) {
//        if (weakSelf.showLog) {
//            if (error) {
//                NSLog(@"************Share fail with error %@*********",error);
//            }else{
//                NSLog(@"response data is %@",data);
//            }
//        }
//    }];
//}
///支付成功回调
- (void)h5PayFinishedGoback{
    [_webView h5PayFinishedGoback];
}

- (UIStatusBarStyle)preferredStatusBarStyle{
    return self.statusBarStyle;
}


@end
