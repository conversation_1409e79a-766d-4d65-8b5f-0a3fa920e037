//
//  JCShopProductVC.m
//  XYFrameWork
//
//  Created by xy on 2018/10/17.
//  Copyright © 2018年 xiaoyao. All rights reserved.
//

#import "JCShopAliPay.h"
#import "AppDelegate.h"
@interface JCShopAliPay ()<WKScriptMessageHandler>
@end

@implementation JCShopAliPay

- (id)init {
    self = [super init];
    if(self){
        self.isNewCreate = YES;
    }
    return self;
}

- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    UIStatusBarStyle statusBarStyle = UIStatusBarStyleDefault;
    if (@available(iOS 13.0, *)) {
        statusBarStyle = UIStatusBarStyleDarkContent;
    }
    [UIApplication sharedApplication].statusBarStyle = statusBarStyle;
    [self.navigationController setNavigationBarHidden:YES animated:YES];
}

- (void)viewWillDisappear:(BOOL)animated{
    [super viewWillDisappear:animated];
    [UIApplication sharedApplication].statusBarStyle = UIStatusBarStyleLightContent;
    [self.navigationController setNavigationBarHidden:NO animated:YES];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    [self shopDetailStateView];
    [self.view bringSubviewToFront:self.progressView];
    // Do any additional setup after loading the view from its nib.
    float stateViewheight = iPhoneX?44:20;
    [self.webView setFrame:CGRectMake(0, stateViewheight, kScreenWidth, kScreenHeight - stateViewheight)];
    [self loadUrl:[NSString stringWithFormat:@"%@/h5_project/index.html#/paySuccess?", JCMALLURL]];
    //调用JS方法
    [self.configuration.userContentController addScriptMessageHandler:self name:@"back_home"];
    [self.configuration.userContentController addScriptMessageHandler:self name:@"jcdy_Enter_Detail"];
    [self.configuration.userContentController addScriptMessageHandler:self name:@"jcdy_Leave_Detail"];
    AppDelegate *appDelegate = (AppDelegate *)[[UIApplication sharedApplication] delegate];
//    [appDelegate resetLocation];
    
} 


#pragma mark - WKScriptMessageHandler
- (void)userContentController:(WKUserContentController *)userContentController didReceiveScriptMessage:(WKScriptMessage *)message
{
    //    message.body  --  Allowed types are NSNumber, NSString, NSDate, NSArray,NSDictionary, and NSNull.
    NSLog(@"body:%@",message.body);
    if ([message.name isEqualToString:@"back_home"]) {//点击返回
        [self.navigationController popViewControllerAnimated:YES];
    }else if ([message.name isEqualToString:@"jcdy_Enter_Detail"]) {
        [UIApplication sharedApplication].statusBarStyle = UIStatusBarStyleLightContent;
        self.detailStateView.hidden = NO;
    }else if ([message.name isEqualToString:@"jcdy_Leave_Detail"]) {
        UIStatusBarStyle statusBarStyle = UIStatusBarStyleDefault;
        if (@available(iOS 13.0, *)) {
            statusBarStyle = UIStatusBarStyleDarkContent;
        }
        [UIApplication sharedApplication].statusBarStyle = statusBarStyle;
        self.detailStateView.hidden = YES;
    }
}
- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

/*
 #pragma mark - Navigation
 
 // In a storyboard-based application, you will often want to do a little preparation before navigation
 - (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
 // Get the new view controller using [segue destinationViewController].
 // Pass the selected object to the new view controller.
 }
 */

@end
