//
//  JCShopBaseWebViewController.m
//  XYFrameWork
//
//  Created by xy on 2018/6/13.
//  Copyright © 2018年 xiaoyao. All rights reserved.
//

#import "JCShopBaseWebViewController.h"
#import "JCShopMallsViewController.h"
#import "JCApplicationManager.h"
#import <WebKit/WebKit.h>
#import <AlipaySDK/AlipaySDK.h>
#import "JCShopAliPay.h"
#import "sys/utsname.h"

@interface JCShopBaseWebViewController ()<WKUIDelegate,WKNavigationDelegate>
@end

@implementation JCShopBaseWebViewController

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.statusBackColor = 0xFFFFFF;
        self.configuration = [[WKWebViewConfiguration alloc] init];
        self.configuration.userContentController = [WKUserContentController new];
        WKPreferences *preferences = [WKPreferences new];
        preferences.javaScriptCanOpenWindowsAutomatically = YES;
        self.configuration.preferences = preferences;
        self.webView = [[WKWebView alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth, kScreenHeight  -  iPhoneX?88:64) configuration:self.configuration];
        self.webView.scrollView.scrollEnabled = NO;
        self.webView.navigationDelegate = self;
        self.webView.UIDelegate = self;
        [self.webView addObserver:self forKeyPath:@"estimatedProgress" options:NSKeyValueObservingOptionOld | NSKeyValueObservingOptionNew context:nil];
        self.progressView = [[UIProgressView alloc]initWithFrame:CGRectMake(0, 0,kScreenWidth, 2)];
        CGAffineTransform transform = CGAffineTransformMakeScale(1.0f, 2.0f);
        self.progressView.transform = transform;//设定宽高
        self.progressView.progressTintColor = [UIColor jk_colorWithHex:0x537FB7];
        [self resetWKWebViewUA];
    }
    return self;
}
- (void)refreshWebViewWith:(NSString *)myUrlStr{};
// 使用浅色模式
- (UIUserInterfaceStyle)overrideUserInterfaceStyle
{
    // 使用浅色模式
    return UIUserInterfaceStyleLight;
}

- (void)resetWKWebViewUA{
    XYWeakSelf
    [self.webView evaluateJavaScript:@"navigator.userAgent" completionHandler:^(id _Nullable result, NSError * _Nullable error) {
        NSString *oldUA = result;
        NSString *appId = [NSString stringWithFormat:@"AppId/%@",[[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleIdentifier"]];
        NSString *os = [NSString stringWithFormat:@"OS/%@",@"ios"];
        NSString *appVersionName = [NSString stringWithFormat:@"AppVersionName/%@",[[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleShortVersionString"]];
        NSString *model = [NSString stringWithFormat:@"Model/%@",[weakSelf deviceName]];
        NSString *systemVersion = [NSString stringWithFormat:@"SystemVersion/%@",[UIDevice currentDevice].systemVersion];
//        NSString *deviceId = [NSString stringWithFormat:@"DeviceId/%@",[JCKeychainTool getDeviceIDInKeychain]];
        NSString *boundleId = [NSString stringWithFormat:@"boundleId/%@",[[NSBundle mainBundle] bundleIdentifier]];
        NSString *referer = @"referer/CP001Mobile";
        NSString *newUA =[NSString stringWithFormat:@"%@ %@ %@ %@ %@ %@ %@ %@", oldUA,appId,os,appVersionName,model,systemVersion,boundleId,referer];
        weakSelf.webView.customUserAgent = newUA;
        [[NSUserDefaults standardUserDefaults] setObject:newUA forKey:@"niimbot-user-agent"];
        [[NSUserDefaults standardUserDefaults] synchronize];
        NSLog(@"UA:%@",newUA);
    }];
}
- (void)loadUrl:(NSString *)url
{
//    NSString *versionComplateString = [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleShortVersionString"];
    NSString *versionComplateString = [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleShortVersionString"];
    if([self isKindOfClass:[JCShopMallsViewController class]]/* || [self isKindOfClass:[JCLabelCustomizeViewController class]]*/){
        if([url rangeOfString:@"?"].location != NSNotFound){
            url = [NSString stringWithFormat:@"%@entrance_type_id=%@&token=%@&version=%@&platform_system_id=1",url,self.entrance_type_id,kJC_Mall_Token,versionComplateString];
        }else{
            url = [NSString stringWithFormat:@"%@?entrance_type_id=%@&token=%@&version=%@&platform_system_id=1" ,url,self.entrance_type_id,kJC_Mall_Token,versionComplateString];
        }
        
    }else{
        if([url rangeOfString:@"?"].location != NSNotFound){
            url = [NSString stringWithFormat:@"%@&back_app=1&entrance_type_id=%@&token=%@&version=%@&platform_system_id=1",url,self.entrance_type_id,kJC_Mall_Token,versionComplateString];
        }else{
            url = [NSString stringWithFormat:@"%@?back_app=1&entrance_type_id=%@&token=%@&version=%@&platform_system_id=1" ,url,self.entrance_type_id,kJC_Mall_Token,versionComplateString];
        }
    }
    
    [self.progressView setAlpha:1.0f];
    [self.webView loadRequest:[NSURLRequest requestWithURL:[NSURL URLWithString:url]  cachePolicy:NSURLRequestUseProtocolCachePolicy timeoutInterval:20]];
}
- (void)viewDidLoad {
    [super viewDidLoad];
    [self setVCBackImageView];
    [self.view addSubview:self.webView];
    [self.view addSubview:self.progressView];
}

- (void)shopDetailStateView{
    float stateViewheight = iPhoneX?44:20;
    UIView *navBKView = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, kScreenWidth, stateViewheight)];
    navBKView.backgroundColor = [UIColor jk_colorWithHex:0xFFFFFF];
    [self.view addSubview:navBKView];
    self.detailStateView = navBKView;
}

- (void)statusBarBackgroundColor:(NSString *)colorValueString{
//    NSDictionary *colorValueDic = [colorValueString xy_toDictionary];
//    NSString *typeValue = [colorValueDic objectForKey:@"type"];
//    NSString *colorValue = [colorValueDic objectForKey:@"color"];
//    NSString *textColor = [NSString stringWithFormat:@"0x%@",[colorValue stringByReplacingOccurrencesOfString:@"#" withString:@""]];
//    unsigned long red = strtoul([textColor UTF8String],0,16);
//    if([typeValue isEqualToString:@"1"]){
//        AppDelegate *appDelegate = (AppDelegate *)[[UIApplication sharedApplication] delegate];
//        XYTabBarController *currentTabbarVC = appDelegate.mainVC;
//        XYNavigationController *navVC = currentTabbarVC.selectedViewController;
//        UIStatusBarStyle statusBarStyle = UIStatusBarStyleDefault;
//        if (@available(iOS 13.0, *)) {
//            statusBarStyle = UIStatusBarStyleDarkContent;
//        }
//        self.statusBarStyle = statusBarStyle;
//        if([navVC.visibleViewController isKindOfClass:[self class]]){
//            [UIApplication sharedApplication].statusBarStyle = self.statusBarStyle;
//        }
//    }else{
//        self.statusBarStyle = UIStatusBarStyleLightContent;
//        [UIApplication sharedApplication].statusBarStyle = self.statusBarStyle;
//    }
//    self.detailStateView.backgroundColor = HEX_RGB(red);
//    self.statusBackColor = red;
}

-(void)addObserverNotification{
    [[NSNotificationCenter defaultCenter]addObserver:self selector:@selector(windowDidBecomeHidden:) name:UIWindowDidBecomeHiddenNotification object:nil];
}

-(void)windowDidBecomeHidden:(NSNotification *)noti{
    UIWindow * win = (UIWindow *)noti.object;
    if(win){
        UIViewController *rootVC = win.rootViewController;
        NSArray<__kindof UIViewController *> *vcs = rootVC.childViewControllers;
        if([vcs.firstObject isKindOfClass:NSClassFromString(@"AVPlayerViewController")]){
            [[UIApplication sharedApplication] setStatusBarHidden:NO withAnimation:UIStatusBarAnimationNone];
        }
    }
}

- (void)setVCBackImageView{
//    UIImageView *backImageView = [[UIImageView alloc] init];
//    backImageView.backgroundColor = [UIColor clearColor];
//    backImageView.image = [UIImage imageNamed:@"背景图"];
//    [self.view addSubview:backImageView];
//    [backImageView mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.leading.top.trailing.bottom.equalTo(self.view).offset(0);
//    }];
//    [self.view sendSubviewToBack:backImageView];
}

// 页面开始加载时调用
-(void)webView:(WKWebView *)webView didStartProvisionalNavigation:(WKNavigation *)navigation{
    NSLog(@"didStartProvisionalNavigation");
    NSLog(@"当前加载URL：%@",webView.URL);
}

// 当内容开始返回时调用
- (void)webView:(WKWebView *)webView didCommitNavigation:(WKNavigation *)navigation{
    NSLog(@"内容开始返回:%@ 当前加载URL：%@",[XYTool getCurrentTimes],webView.URL);
}

// 页面加载完成之后调用
- (void)webView:(WKWebView *)webView didFinishNavigation:(WKNavigation *)navigation{//这里修改导航栏的标题，动态改变
    NSLog(@"开始H5完毕:%@  当前加载URL：%@",[XYTool getCurrentTimes],webView.URL);
    NSLog(@"%@",self.view);
    self.loadSuccess = YES;
    [self.webView evaluateJavaScript:@"navigator.userAgent" completionHandler:^(id _Nullable result, NSError * _Nullable error) {
        NSLog(@"UA:%@",result);
    }];
}

// 页面加载失败时调用
-(void)webView:(WKWebView *)webView didFailProvisionalNavigation:(WKNavigation *)navigation withError:(NSError *)error{
    NSLog(@"请求失败，不显示webView");
    self.loadSuccess = NO;
}

- (void)webView:(WKWebView *)webView didFailNavigation:(null_unspecified WKNavigation *)navigation withError:(NSError *)error{
    
}

// 接收到服务器跳转请求之后再执行
- (void)webView:(WKWebView *)webView didReceiveServerRedirectForProvisionalNavigation:(WKNavigation *)navigation{
    NSLog(@"didReceiveServerRedirectForProvisionalNavigation");
}

- (void)webView:(WKWebView *)webView decidePolicyForNavigationResponse:(WKNavigationResponse *)navigationResponse decisionHandler:(void (^)(WKNavigationResponsePolicy))decisionHandler {
    if (((NSHTTPURLResponse *)navigationResponse.response).statusCode == 200) {
        decisionHandler (WKNavigationResponsePolicyAllow);
    }else {
        decisionHandler(WKNavigationResponsePolicyCancel);
    }
}

//支付宝支付完成或取消时候回到App回调
- (void)webView:(WKWebView*)webView decidePolicyForNavigationAction:(WKNavigationAction*)navigationAction decisionHandler:(void(^)(WKNavigationActionPolicy))decisionHandler{
    WKNavigationActionPolicy actionPolicy = WKNavigationActionPolicyAllow;
    NSString *str = [navigationAction.request.URL absoluteString];
    if ([str containsString:@"alipayurl"]) {
        [self performSelector:@selector(gotoAliPay1) withObject:nil afterDelay:1];
    }
    BOOL isIntercepted = NO;
    isIntercepted = [[AlipaySDK defaultService] payInterceptorWithUrl:[navigationAction.request.URL absoluteString] fromScheme:@"JCYDY" callback:^(NSDictionary *result) {
        // 处理支付结果
        // isProcessUrlPay 代表 支付宝已经处理该URL
        if ([result[@"isProcessUrlPay"]boolValue]) {
            
        }
    }];
    
    if (isIntercepted) {
        actionPolicy = WKNavigationActionPolicyCancel;
    }
    
    NSString *urlString = [[navigationAction.request.URL absoluteString] stringByReplacingPercentEscapesUsingEncoding:NSUTF8StringEncoding];
    if ([urlString containsString:@"weixin://wap/pay?"]) {
        actionPolicy =WKNavigationActionPolicyCancel;
        //解决wkwebview weixin://无法打开微信客户端的处理
        NSURL*url = [NSURL URLWithString:urlString];
        BOOL bSucc = [[UIApplication sharedApplication] openURL:url];
        if(!bSucc) {
//            [self showHint:@"未检测到微信APP，请您先安装"];
        } else {
            [[UIApplication sharedApplication] openURL:url options:@{UIApplicationOpenURLOptionUniversalLinksOnly: @NO} completionHandler:^(BOOL success) {
            }];
        }
    }
    
    decisionHandler(actionPolicy);
    
}
- (void)gotoAliPay1
{
    [self.webView goBack];
    [self performSelector:@selector(gotoAliPay2) withObject:nil afterDelay:3];
}
- (void)gotoAliPay2
{
    JCShopAliPay *c1 = [[JCShopAliPay alloc] init];
    [self.navigationController pushViewController:c1 animated:YES];
}

// 监听事件处理
- (void)observeValueForKeyPath:(NSString *)keyPath ofObject:(id)object change:(NSDictionary<NSKeyValueChangeKey,id> *)change context:(void *)context{
    if ([keyPath isEqual:@"estimatedProgress"] && object == self.webView) {
        [self.progressView setAlpha:1.0f];
        [self.progressView setProgress:self.webView.estimatedProgress animated:YES];
        if (self.webView.estimatedProgress  >= 1.0f) {
            [UIView animateWithDuration:0.3 delay:0.3 options:UIViewAnimationOptionCurveEaseOut animations:^{
                [self.progressView setAlpha:0.0f];
            } completion:^(BOOL finished) {
                [self.progressView setProgress:0.0f animated:YES];
            }];
        }
        [super observeValueForKeyPath:keyPath ofObject:object change:change context:context];
    }else{
        [super observeValueForKeyPath:keyPath ofObject:object change:change context:context];
    }
}

-(NSString*)deviceName
{
    struct utsname systemInfo;
    uname(&systemInfo);
    NSString * deviceString = [NSString stringWithCString:systemInfo.machine encoding:NSUTF8StringEncoding];
    NSLog(@"%@",deviceString);
    NSString * phoneType = [NSString stringWithCString: systemInfo.machine encoding:NSASCIIStringEncoding];
    NSLog(@"%@",phoneType);
    //iPhone
    if ([deviceString isEqualToString:@"iPhone1,1"])    return @"iPhone 1G";
    if ([deviceString isEqualToString:@"iPhone1,2"])    return @"iPhone 3G";
    if ([deviceString isEqualToString:@"iPhone2,1"])    return @"iPhone 3GS";
    if ([deviceString isEqualToString:@"iPhone3,1"])    return @"iPhone 4";
    if ([deviceString isEqualToString:@"iPhone3,2"])    return @"Verizon iPhone 4";
    if ([deviceString isEqualToString:@"iPhone4,1"])    return @"iPhone 4S";
    if ([deviceString isEqualToString:@"iPhone5,1"])    return @"iPhone 5";
    if ([deviceString isEqualToString:@"iPhone5,2"])    return @"iPhone 5";
    if ([deviceString isEqualToString:@"iPhone5,3"])    return @"iPhone 5C";
    if ([deviceString isEqualToString:@"iPhone5,4"])    return @"iPhone 5C";
    if ([deviceString isEqualToString:@"iPhone6,1"])    return @"iPhone 5S";
    if ([deviceString isEqualToString:@"iPhone6,2"])    return @"iPhone 5S";
    if ([deviceString isEqualToString:@"iPhone7,1"])    return @"iPhone 6 Plus";
    if ([deviceString isEqualToString:@"iPhone7,2"])    return @"iPhone 6";
    if ([deviceString isEqualToString:@"iPhone8,1"])    return @"iPhone 6s";
    if ([deviceString isEqualToString:@"iPhone8,2"])    return @"iPhone 6s Plus";
    if ([deviceString isEqualToString:@"iPhone8,4"])    return @"iPhone SE";
    if ([deviceString isEqualToString:@"iPhone9,1"])    return @"iPhone 7";
    if ([deviceString isEqualToString:@"iPhone9,2"])    return @"iPhone 7 Plus";
    if ([deviceString isEqualToString:@"iPhone10,1"])   return @"iPhone 8";
    if ([deviceString isEqualToString:@"iPhone10,4"])   return @"iPhone 8";
    if ([deviceString isEqualToString:@"iPhone10,2"])   return @"iPhone 8 Plus";
    if ([deviceString isEqualToString:@"iPhone10,5"])   return @"iPhone 8 Plus";
    if ([deviceString isEqualToString:@"iPhone10,3"])   return @"iPhone X";
    if ([deviceString isEqualToString:@"iPhone10,6"])   return @"iPhone X";
    if ([deviceString isEqualToString:@"iPhone11,8"])   return @"iPhone XR";
    if ([deviceString isEqualToString:@"iPhone11,2"])   return @"iPhone XS";
    if ([deviceString isEqualToString:@"iPhone11,4"])   return @"iPhone XS Max";
    if ([deviceString isEqualToString:@"iPhone11,6"])   return @"iPhone XS Max";
    if ([deviceString isEqualToString:@"iPhone12,1"])   return @"iPhone 11";
    if ([deviceString isEqualToString:@"iPhone12,3"])   return @"iPhone 11 Pro";
    if ([deviceString isEqualToString:@"iPhone12,5"])   return @"iPhone 11 Pro Max";
    if ([deviceString isEqualToString:@"iPhone12,8"])   return @"iPhone SE (2nd generation)";
    if ([deviceString isEqualToString:@"iPhone13,1"])   return @"iPhone 12 mini";
    if ([deviceString isEqualToString:@"iPhone13,2"])   return @"iPhone 12";
    if ([deviceString isEqualToString:@"iPhone13,3"])   return @"iPhone 12 Pro";
    if ([deviceString isEqualToString:@"iPhone13,4"])   return @"iPhone 12 Pro Max";
    return deviceString;
}

- (void)dealloc
{
    [self.webView removeObserver:self forKeyPath:@"estimatedProgress"];
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

/*
 #pragma mark - Navigation
 // In a storyboard-based application, you will often want to do a little preparation before navigation
 - (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
 // Get the new view controller using [segue destinationViewController].
 // Pass the selected object to the new view controller.
 }
 */

@end

