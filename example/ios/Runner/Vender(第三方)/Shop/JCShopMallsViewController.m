//
//  JCShopMallsViewController.m
//  XYFrameWork
//
//  Created by <PERSON><PERSON><PERSON> on 2019/2/22.
//  Copyright © 2019 JingchenSoft. All rights reserved.
//

#import "JCShopMallsViewController.h"
#import "QQLBXScanViewController.h"
#import <AlipaySDK/AlipaySDK.h>
#import "Global.h"
#import "StyleDIY.h"
#import "AppDelegate.h"
@interface JCShopMallsViewController ()<WKScriptMessageHandler>
@property (nonatomic,copy) NSString *lastUrl;
@property (nonatomic,assign) BOOL isToScan;
@property (nonatomic,assign) BOOL isLoginRefresh;
@end

@implementation JCShopMallsViewController

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.entrance_type_id = @"1";
//        self.shopHomeUrl = STR_IS_NIL([XYCenter sharedInstance].shopHomeUrl)?[NSString stringWithFormat:@"%@/%@",ShopURL,@"h5_project/index.html"]:[XYCenter sharedInstance].shopHomeUrl;
        [self.configuration.userContentController addScriptMessageHandler:self name:@"backHome"];
        [self.configuration.userContentController addScriptMessageHandler:self name:@"jcdy_shopScan"];
        [self.configuration.userContentController addScriptMessageHandler:self name:@"jcdy_Enter_Detail"];
        [self.configuration.userContentController addScriptMessageHandler:self name:@"jcdy_Leave_Detail"];
        [self.configuration.userContentController addScriptMessageHandler:self name:@"jcdy_Show_Tabbar"];
        [self.configuration.userContentController addScriptMessageHandler:self name:@"jcdy_Go_Home"];
        [self.configuration.userContentController addScriptMessageHandler:self name:@"jcdy_StatusBar_Color"];
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(reloadShopHomeView) name:LOGIN_CHANGED object:nil];
        NSString *url = [NSString stringWithFormat:@"%@#/?preloading=1", self.shopHomeUrl];
//        if(xy_isLogin){
//            [self loadUrl:url];
//        }
        self.lastUrl = url;
        UIStatusBarStyle statusBarStyle = UIStatusBarStyleDefault;
        if (@available(iOS 13.0, *)) {
            statusBarStyle = UIStatusBarStyleDarkContent;
        }
        self.statusBarStyle = statusBarStyle;
        self.statusBackColor = 0xFFFFFF;
        self.isNeedReload = YES;
        self.isHomePageShop = YES;
        
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
//    [UIApplication sharedApplication].statusBarStyle = self.statusBarStyle;
    [self shopDetailStateView];
    self.view.backgroundColor = COLOR_WHITE;
    [self.view bringSubviewToFront:self.progressView];
    float stateViewheight = iPhoneX?44:20;
    [self.webView setFrame:CGRectMake(0, stateViewheight, kScreenWidth, kScreenHeight - stateViewheight - (iPhoneX?34:0))];
    self.webView.scrollView.bounces = NO;
    AppDelegate *appDelegate = (AppDelegate *)[[UIApplication sharedApplication] delegate];
//    [appDelegate resetLocation];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(refreshWeb) name:@"payBackToRefrshWeb" object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(reloadWeb) name:@"jcdy_HomeWebview_Action_Refresh" object:nil];
//    if(STR_IS_NIL(userShopToken)){
//        self.isNeedReload = YES;
//    }
    if (@available(ios 11.0,*))
    {
        self.webView.scrollView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    }
}

- (void)dealloc{
    
}

-(void)reloadWeb{
//    [self.webView reload];
    //    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    self.isToScan = NO;
    [self.navigationController setNavigationBarHidden:YES animated:NO];
    if(self.isNeedReload || !self.loadSuccess || self.isLoginRefresh){
        if(STR_IS_NIL(self.lastUrl)){
            self.lastUrl = [NSString stringWithFormat:@"%@#/?", self.shopHomeUrl];
        }
        [self loadUrl:self.lastUrl];
        self.isNeedReload = NO;
        self.isLoginRefresh = NO;
    }else{
        
    }
}

- (void)viewDidAppear:(BOOL)animated{
    [UIApplication sharedApplication].statusBarStyle = self.statusBarStyle;
//    self.detailStateView.backgroundColor = HEX_RGB(self.statusBackColor);
}

- (void)viewWillDisappear:(BOOL)animated{
    [super viewWillDisappear:animated];
    if(!self.isToScan){
        [self.navigationController setNavigationBarHidden:NO animated:NO];
    }
}

//支付完成刷新webview
-(void)refreshWeb{
    UIViewController *rootVC = [UIApplication sharedApplication].keyWindow.rootViewController;
    if([rootVC isKindOfClass:[UITabBarController class]]){
        UITabBarController *root = (UITabBarController*)rootVC;
        UINavigationController *naviVC = [root.viewControllers safeObjectAtIndex:root.selectedIndex];
        if(![naviVC.visibleViewController isKindOfClass:[self class]]){
            self.isNeedReload = YES;
        }else{
            NSString *myStr = @"jcydy://com.suofang.jcbqdy/orderlist";
            NSURL *url = [NSURL URLWithString:myStr];
            NSURLRequest *req = [NSURLRequest requestWithURL:url ];
            [self.webView loadRequest:req];
        }
    }
    
}

// 商城埋点
- (void)callH5Mothord{
    NSString *jsStr = @"appUserClickEvent()";
    [self.webView evaluateJavaScript:jsStr completionHandler:^(id _Nullable result, NSError * _Nullable error) {
        NSLog(@"%@----%@",result, error);
    }];
}

//刷新商城首页
- (void)reloadShopHomeView{
//    if(!xy_isLogin){
//        [self.webView evaluateJavaScript:@"document.body.innerHTML='';" completionHandler:^(id _Nullable response, NSError * _Nullable error) {
//        }];
//    }else{
        [self refreshWebViewWith:self.shopHomeUrl];
//        self.loadSuccess = NO;
//        if(STR_IS_NIL(userShopToken)){
//            self.isNeedReload = YES;
//        }else{
//            self.isNeedReload = NO;
//            [self loadUrl:self.lastUrl];
//        }
//    }
}

//刷新根据Url路径刷新商城
- (void)refreshWebViewWith:(NSString *)myUrlStr{
    NSLog(@"开始加载URL：%@",myUrlStr);
    if([myUrlStr rangeOfString:@"#/"].location != NSNotFound){
        if([myUrlStr rangeOfString:@"?"].location != NSNotFound){
            self.lastUrl = [NSString stringWithFormat:@"%@&", myUrlStr];
        }else{
            self.lastUrl = [NSString stringWithFormat:@"%@?", myUrlStr];
        }
    }else{
        self.lastUrl = [NSString stringWithFormat:@"%@#/?", myUrlStr];
    }
    self.isNeedReload = YES;
}


//H5交互
#pragma mark - WKScriptMessageHandler
- (void)userContentController:(WKUserContentController *)userContentController didReceiveScriptMessage:(WKScriptMessage *)message
{
    self.isNeedReload = NO;
    self.progressView.alpha = 0;
    if ([message.name isEqualToString:@"backHome"]) {//点击返回
        [self.navigationController popViewControllerAnimated:YES];
//        if([m_userModel.is_FirstShop isEqualToString:@"1"]){
//            [[XYCenter sharedInstance] firstShopRequestSuccess:^(id x) {
//
//            }];
//        }
    }else if ([message.name isEqualToString:@"jcdy_shopScan"]) {//商城扫描
        [self doSaoMa];
    }else if ([message.name isEqualToString:@"jcdy_StatusBar_Color"]) {//状态栏颜色
        NSString *messageBody = message.body;
        [self statusBarBackgroundColor:messageBody];
    }else if ([message.name isEqualToString:@"jcdy_Show_Tabbar"]) {//展示底部tabbar
        if(!self.isHomePageShop){
            return;
        }
//        UIViewController *rootVC = ((AppDelegate *)[UIApplication sharedApplication].delegate).mainVC;
//        if([rootVC isKindOfClass:[UITabBarController class]]){
//            UITabBarController *root = (UITabBarController*)rootVC;
//            UINavigationController *naviVC = [root.viewControllers safeObjectAtIndex:root.selectedIndex];
//            if(![naviVC.visibleViewController isKindOfClass:[self class]]){
//                return;
//            }
//        }
        CGFloat tabbarHeight = iPhoneX?(49.0 + 34.0):49.0;
        NSNumber *num = message.body;
        if([@1 isEqualToNumber:num]){
            self.tabBarController.tabBar.hidden = NO;
            self.tabBarController.tabBar.frame = CGRectMake(0, kScreenHeight-tabbarHeight, kScreenWidth, tabbarHeight);
//            [[XYCenter sharedInstance] firstShopRequestSuccess:^(id x) {
                
//            }];
        }else{
            self.tabBarController.tabBar.hidden = YES;
            self.tabBarController.tabBar.frame = CGRectZero;
        }
        self.view.frame = UIScreen.mainScreen.bounds;
        float stateViewheight = iPhoneX?44:20;
        [self.webView setFrame:CGRectMake(0, stateViewheight, kScreenWidth, kScreenHeight - stateViewheight - (iPhoneX?34:0))];
    }else if ([message.name isEqualToString:@"jcdy_Go_Home"]) {//返回app首页
        NSArray *arr = self.tabBarController.viewControllers;
        UINavigationController *navi = [arr safeObjectAtIndex:1];
        UIViewController *vc = navi.visibleViewController;
        if([vc isKindOfClass:[self class]]){
            self.tabBarController.selectedIndex = 1;
            [self.navigationController popToRootViewControllerAnimated:NO];
        }
    }
}

//扫码事件处理
- (void)doSaoMa
{
    XYWeakSelf
    self.isToScan = YES;
    NSString *mediaType = AVMediaTypeVideo;//读取媒体类型
    AVAuthorizationStatus authStatus = [AVCaptureDevice authorizationStatusForMediaType:mediaType];//读取设备授权状态
    if(authStatus == AVAuthorizationStatusRestricted || authStatus == AVAuthorizationStatusDenied){
//        [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app00032",@"提示") message:XY_LANGUAGE_TITLE_NAMED(@"app00750",@"请前往系统应用设置中，允许访问您的相机") cancelButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00030",@"取消") sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00297",@"设置") cancelBlock:nil sureBlock:^{
//            NSURL * url = [NSURL URLWithString:UIApplicationOpenSettingsURLString];
//            if([[UIApplication sharedApplication] canOpenURL:url]) {
//                NSURL*url =[NSURL URLWithString:UIApplicationOpenSettingsURLString];
//                [[UIApplication sharedApplication] openURL:url];
//            }
//        }];
        return;
    }
    QQLBXScanViewController *vc = [QQLBXScanViewController new];
    vc.scanCreateType = 4;
    vc.libraryType = [Global sharedManager].libraryType;
    vc.scanCodeType = [Global sharedManager].scanCodeType;
    
    vc.style = [StyleDIY qqStyle];
    vc.scanBlock = ^(NSString *x){
        NSLog(@"==============获取到扫码内容==============/n%@", x);
        if(x.length == 0){
            //扫描取消
            NSString *jsStr = @"cancelScan()";
            [weakSelf.webView evaluateJavaScript:jsStr completionHandler:^(id _Nullable result, NSError * _Nullable error) {
                NSLog(@"%@----%@",result, error);
            }];
            return;
        }
        if (x.length > 0) {
            NSString *jsStr = [NSString stringWithFormat:@"returnScanResult('%@')",x];
            [weakSelf.webView evaluateJavaScript:jsStr completionHandler:^(id _Nullable result, NSError * _Nullable error) {
                NSLog(@"%@----%@",result, error);
            }];
        };
    };
    //镜头拉远拉近功能
    vc.isVideoZoom = YES;
    [self presentViewController:vc animated:YES completion:^{
        
    }];
    
}

//支付宝支付完成或取消时候回到App回调
- (void)webView:(WKWebView*)webView decidePolicyForNavigationAction:(WKNavigationAction*)navigationAction decisionHandler:(void(^)(WKNavigationActionPolicy))decisionHandler{
    __block WKNavigationActionPolicy actionPolicy = WKNavigationActionPolicyAllow;
    NSString *str = [navigationAction.request.URL absoluteString];
    NSString *wxPayRedirectUrl = [NSString stringWithFormat:@"%@://iwantbacktoapp/",SchemesWX];
    NSString *redirect_Url = [NSString stringWithFormat:@"redirect_url=%@",wxPayRedirectUrl];
    if([str hasPrefix:@"https://wx.tenpay.com/cgi-bin/mmpayweb-bin/checkmweb"] && ![str hasSuffix:redirect_Url]){
        decisionHandler(WKNavigationActionPolicyCancel);
        NSString *redirectUrl = nil;
        if([str containsString:@"redirect_url="]){
            NSRange range = [str rangeOfString:@"redirect_url="];
            redirectUrl = [[str substringToIndex:range.location] stringByAppendingString:redirect_Url];
        }else{
            redirectUrl = [str stringByAppendingString:redirect_Url];
        }
        NSMutableURLRequest *newRequest = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:redirectUrl] cachePolicy:NSURLRequestUseProtocolCachePolicy timeoutInterval:30];
        newRequest.allHTTPHeaderFields = navigationAction.request.allHTTPHeaderFields;
        [newRequest setValue:wxPayRedirectUrl forHTTPHeaderField:@"Referer"];
        newRequest.URL = [NSURL URLWithString:redirectUrl];
        [webView loadRequest:newRequest];
        return;
    }
    if ([@"jcydy://com.suofang.jcbqdy/orderlist" isEqualToString:str]) {
        actionPolicy = WKNavigationActionPolicyCancel;
        NSString *url = [NSString stringWithFormat:@"%@h5_project/index.html#/paySuccess?", JCMALLURL];
        [self loadUrl:url];
    }
    
    BOOL isIntercepted = NO;
    isIntercepted = [[AlipaySDK defaultService] payInterceptorWithUrl:[navigationAction.request.URL absoluteString] fromScheme:@"JCYDY" callback:^(NSDictionary *result) {
        // 处理支付结果
        // isProcessUrlPay 代表 支付宝已经处理该URL
        if ([result[@"isProcessUrlPay"]boolValue]) {
            actionPolicy = WKNavigationActionPolicyCancel;
            NSString *url = [NSString stringWithFormat:@"%@h5_project/#/?", JCMALLURL];
            [self loadUrl:url];
        }
    }];
    if(isIntercepted){
        actionPolicy =WKNavigationActionPolicyCancel;
    }
    
    NSString *urlString = [[navigationAction.request.URL absoluteString] stringByReplacingPercentEscapesUsingEncoding:NSUTF8StringEncoding];
    if ([urlString containsString:@"weixin://wap/pay?"]) {
        actionPolicy =WKNavigationActionPolicyCancel;
        //解决wkwebview weixin://无法打开微信客户端的处理
        NSURL*url = [NSURL URLWithString:urlString];
        BOOL bSucc = [[UIApplication sharedApplication] openURL:url];
        if(!bSucc) {
//            [self showMessage:@"未检测到微信APP，请您先安装"];
        } else {
            [[UIApplication sharedApplication] openURL:url options:@{UIApplicationOpenURLOptionUniversalLinksOnly: @NO} completionHandler:^(BOOL success) {
            }];
        }
    }
    
    decisionHandler(actionPolicy);
    
}
/*
 #pragma mark - Navigation
 
 // In a storyboard-based application, you will often want to do a little preparation before navigation
 - (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
 // Get the new view controller using [segue destinationViewController].
 // Pass the selected object to the new view controller.
 }
 */

@end
