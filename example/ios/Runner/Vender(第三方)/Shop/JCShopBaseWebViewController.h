//
//  JCShopBaseWebViewController.h
//  XYFrameWork
//
//  Created by xy on 2018/6/21.
//  Copyright © 2018年 xiaoyao. All rights reserved.
//

#import <WebKit/WebKit.h>

#define kScreenWidth        ([UIScreen mainScreen].bounds.size.width)
#define kScreenHeight       ([UIScreen mainScreen].bounds.size.height)
//#define iPhoneX \
//({BOOL isPhoneXX = NO;\
//if (@available(iOS 11.0, *)) {\
//isPhoneXX = [[UIApplication sharedApplication] delegate].window.safeAreaInsets.bottom > 0.0;\
//}\
//(isPhoneXX);})
#define HEX_RGB(V)        [UIColor fromHexValue:V]
//#define STR_IS_NIL(key) ([@"(null)" isEqualToString:(key)] || [@"" isEqualToString:(key)] || key == nil || [key isKindOfClass:[NSNull class]] || [@"null" isEqualToString:(key)] )
//#define userShopToken  m_userModel.shopToken

@interface JCShopBaseWebViewController : UIViewController
@property (nonatomic,assign) UIStatusBarStyle statusBarStyle;
@property (nonatomic,assign) unsigned long statusBackColor;
@property (nonatomic, strong)  WKWebViewConfiguration *configuration;
@property (nonatomic, strong)  WKWebView *webView;
@property (nonatomic, strong) UIView *detailStateView;
@property (nonatomic, strong) UIProgressView *progressView;
@property (nonatomic, assign) BOOL isNewCreate;
@property (nonatomic, assign) BOOL loadSuccess;
@property (nonatomic, strong) NSString *url;
@property (nonatomic, strong) NSString *entrance_type_id;
- (void)statusBarBackgroundColor:(NSString *)colorValueString;
- (void)refreshWebViewWith:(NSString *)myUrlStr;
- (void)shopDetailStateView;
- (void)loadUrl:(NSString *)url;
@end
