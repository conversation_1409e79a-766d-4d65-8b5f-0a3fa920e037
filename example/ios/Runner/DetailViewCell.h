//
//  DetailViewCell.h
//  Runner
//
//  Created by <PERSON> on 2021/1/22.
//

#import <UIKit/UIKit.h>


NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSUInteger, CellConnerStyle) {
    CellConnerStyleTop,
    CellConnerStyleMiddle,
    CellConnerStyleBottom,
};

static NSString *detailViewCellIdentifier = @"detailViewCellIdentifier";

@interface CornerStyleCell : UITableViewCell

@property(nonatomic,assign) CellConnerStyle connerStyle;

@property(nonatomic,copy) NSString *title;

@property(nonatomic,strong) UIView *cornerView;

@property(nonatomic,strong) UILabel *nameLabel;


@end

@interface DetailViewCell : CornerStyleCell

@property(nonatomic,copy) NSString *detailInfo;

@property(nonatomic,assign) BOOL interactionEnabled;

@property(nonatomic,assign) BOOL needUpdata;

@end

static NSString *CalibrationCellIdentifier = @"CalibrationCellIdentifier";

@interface CalibrationCell : CornerStyleCell

@property(nonatomic,copy) XYNormalBlock clickBlock;


@end


static NSString *AutoPowerOffCellIdentifier = @"AutoPowerOffCellIdentifier";

@interface AutoPowerOffCell : CornerStyleCell

@property(nonatomic,strong) UIImageView *selectIcon;


@end




NS_ASSUME_NONNULL_END
