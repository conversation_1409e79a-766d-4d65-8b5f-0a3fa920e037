#include "AppDelegate.h"
#include "GeneratedPluginRegistrant.h"
#import "DevicesSearchController.h"
#import "JCPrintSettingView.h"
#import "JCBluetoothManager.h"
#import "PrintData.pbobjc.h"
#import "ImageHelper.h"
#import "JCAPI.h"
#import "JCFontManager.h"

@implementation AppDelegate

- (BOOL)application:(UIApplication *)application
    didFinishLaunchingWithOptions:(NSDictionary *)launchOptions {
    FlutterViewController *controller = (FlutterViewController *)self.window.rootViewController;
    FlutterMethodChannel *channel = [FlutterMethodChannel methodChannelWithName:@"flutter://nbcanvas/pluginCodelabChannel" binaryMessenger:controller.binaryMessenger];
    [channel setMethodCallHandler:^(FlutterMethodCall * _Nonnull call, FlutterResult  _Nonnull result) {
        if ([call.method isEqualToString:@"init"]) {
            NSLog(@"init ~~~~~~~~~~~~~~");
        } else if ([call.method isEqualToString:@"blueToothConnect"]) {
            [self device];
        } else if ([call.method isEqualToString:@"print"]) {
            FlutterStandardTypedData *tData = (FlutterStandardTypedData *)call.arguments;
            NSData *imageData = tData.data;
            NSError *error;
            PrintData *printD = [PrintData parseFromData:imageData error:&error];

            NSData *data = printD.data_p;
            double width = printD.width;
            double height = printD.height;
            UIImage *img = [UIImage imageWithData:data];
            [self print:img width:width height:height];
        }
    }];
  [GeneratedPluginRegistrant registerWithRegistry:self];
  // Override point for customization after application launch.
    
    /// 配置字体
    [[JCFontManager sharedManager] configureFontMsg];
    
  return [super application:application didFinishLaunchingWithOptions:launchOptions];
}

- (UIImage*) imageFromArray:(const char*)pixelArray width:(int)width height:(int)height {

    int imageSizeInPixels = width * height;
    int bytesPerPixel = 2; // 1 byte for brightness, 1 byte for alpha
    unsigned char *pixels = (unsigned char *)malloc(imageSizeInPixels * bytesPerPixel);
    memset(pixels, 255, imageSizeInPixels * bytesPerPixel); // setting alpha values to 255
    for (int i = 0; i < imageSizeInPixels; i++) {
        pixels[i * 2] = pixelArray[i]; // writing array of bytes as image brightnesses
    }

    CGColorSpaceRef colorSpace = CGColorSpaceCreateDeviceGray();
    CGDataProviderRef provider = CGDataProviderCreateWithData(NULL, pixels, imageSizeInPixels * bytesPerPixel, NULL);
    CGImageRef cgImage = CGImageCreate(width, height, 8, 8 * bytesPerPixel, width * bytesPerPixel, colorSpace, kCGImageAlphaPremultipliedLast | kCGBitmapByteOrder32Big, provider, NULL, false, kCGRenderingIntentDefault);
    UIImage *image = [UIImage imageWithCGImage:cgImage];

    return image;
}

- (void)device {
    NSLog(@"blueToothConnect ~~~~~~~~~~~~~~");
    FlutterViewController *controller = (FlutterViewController *)self.window.rootViewController;
    DevicesSearchController *device = [[DevicesSearchController alloc]initWithEnterStyle:EnterSearchStyleNone];
    [controller presentViewController:device animated:YES completion:nil];
}

- (void)print:(UIImage *)image width:(double)width height:(double)height {
    NSLog(@"print ~~~~~~~~~~~~~~");
    FlutterViewController *controller = (FlutterViewController *)self.window.rootViewController;
    if(!JC_IS_CONNECTED_PRINTER){
        UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:@"取消" style:UIAlertActionStyleCancel  handler:^(UIAlertAction * _Nonnull action) {
            
        }];
        UIAlertAction *connectAction = [UIAlertAction actionWithTitle:@"连接" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            [self device];
        }];
        UIAlertController *alertController = [UIAlertController alertControllerWithTitle:@"提示" message:@"请先连接打印机方可打印" preferredStyle:UIAlertControllerStyleAlert];
        [alertController addAction:cancelAction];
        [alertController addAction:connectAction];
        [controller presentViewController:alertController animated:YES completion:nil];
        return;
    }
    [JCAPI getPrintingErrorInfo:^(NSString *printInfo) {
        NSLog(@"printInfo is: %@",printInfo);
    }];
    [JCAPI setPrintState:21 type:4 sucess:^(BOOL isSuccess) {
        
    }];
    [JCAPI setPrintState:23 type:3 sucess:^(BOOL isSuccess) {
        
    }];
    
    [JCAPI startDraw:width height:height orientation:0];
    
//    [JCAPI setDrawboardEdge:UIEdgeInsetsZero sucess:^(NSString *printInfo) {
//        NSLog(@"Edge info is: %@",printInfo);
//    }];
    
    [JCAPI startPage];
    [JCAPI drawImage:image
                   x:0
                   y:0
               width:width
              height:height
         orientation:0];
    [JCAPI endPage:1];
    
    [JCAPI print:^(BOOL isSuccess) {
        
    }];
//    JCPrintSettingView *settingView = [[JCPrintSettingView alloc] initWithDefaultConcentrate:DrawBoardInfo.defaultConcentrate min:DrawBoardInfo.minConcentrate max:DrawBoardInfo.maxConcentrate];
//    settingView.delegate = self;
//    [settingView show];
}

- (void)settingView:(JCPrintSettingView *)view concentrate:(NSInteger)concentrate number:(NSInteger)number {
    
}
@end
