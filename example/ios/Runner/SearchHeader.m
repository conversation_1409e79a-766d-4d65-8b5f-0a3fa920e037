//
//  SeahchHeader.m
//  Runner
//
//  Created by <PERSON> on 2020/11/24.
//

#import "SearchHeader.h"

@interface SearchHeader ()

@property (nonatomic,strong) UIButton *refreshBtn;

@property(nonatomic,strong) UIActivityIndicatorView *indicatorView;

@end

@implementation SearchHeader

-(instancetype)initWithFrame:(CGRect)frame{
    self = [super initWithFrame:frame];
    if(self){
        [self setUpUI];
    }
    return self;
}


-(void)setUpUI{
    self.backgroundColor =[UIColor whiteColor];
    self.layer.cornerRadius = 6;
  
    XYWeakSelf
    UILabel *searchLable = [[UILabel alloc] init];
    searchLable.text = XY_LANGUAGE_TITLE_NAMED(@"app00897", @"搜索设备...");
    searchLable.font = XY_S_BOLD_FONT(16);
    searchLable.textAlignment = NSTextAlignmentLeft;
    searchLable.textColor = XY_HEX_RGB(0x333333);
    [self addSubview:searchLable];
    [self addSubview:self.refreshBtn];
    
    [self addSubview:self.indicatorView];
    
    [searchLable mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(weakSelf);
        make.leading.equalTo(weakSelf).offset(16);
        
    }];
    
    [self.refreshBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.equalTo(weakSelf).offset(-16);
        make.centerY.equalTo(weakSelf);
        make.size.mas_equalTo(CGSizeMake(32, 32));
       
    }];
    
    [self.indicatorView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.equalTo(weakSelf).offset(-16);
        make.centerY.equalTo(weakSelf);
        make.size.mas_equalTo(CGSizeMake(32, 32));
       
    }];

}


-(void)changeRefreshBtn:(BOOL)refreshing{
    self.isSearching = refreshing;
    dispatch_async(dispatch_get_main_queue(), ^{
        if(refreshing){
            self.refreshBtn.hidden  =YES;
            self.indicatorView.hidden = NO;
            [self.indicatorView startAnimating];
           
        }else{
           self.refreshBtn.hidden  =NO;
           [self.indicatorView stopAnimating];
           self.indicatorView.hidden = YES;
           
        }
    });
    
}


-(void)btnClicked:(UIButton *)sender{
   
    if(self.reSerachAction){
        self.reSerachAction(self);
    }
    
}

-(UIButton *)refreshBtn{
    if(!_refreshBtn){
        _refreshBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_refreshBtn setImage:XY_IMAGE_NAMED(@"search_Icn") forState:UIControlStateNormal];
        [_refreshBtn addTarget:self action:@selector(btnClicked:) forControlEvents:UIControlEventTouchUpInside];
       
    }
    return _refreshBtn;
}


-(UIActivityIndicatorView *)indicatorView
{
    if (!_indicatorView) {
        _indicatorView = [[UIActivityIndicatorView alloc] initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleGray];
        _indicatorView.hidden = YES;
    }
    return _indicatorView;
}

@end
