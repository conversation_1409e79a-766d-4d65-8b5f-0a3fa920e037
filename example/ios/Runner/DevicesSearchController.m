//
//  DevicesSearchController.m
//  Runner
//
//  Created by <PERSON> on 2020/11/24.
//

#import "DevicesSearchController.h"
#import <Flutter/FlutterViewController.h>
#import "GeneratedPluginRegistrant.h"
#import <DZNEmptyDataSet/UIScrollView+EmptyDataSet.h>
#import "BlueToothCell.h"
#import "SearchHeader.h"
#import "JCBluetoothManager.h"
#import "DCHUDHelper.h"
#import "CommonCtrl.h"
#import "JCMessageView.h"
//#import "AppEventChannel.h"
//#import "AppMethodChannel.h"
#import "DCNavigationViewController.h"

#import "DeviceDetailController.h"

@interface DevicesSearchController ()<UITableViewDelegate,UITableViewDataSource,DZNEmptyDataSetSource,DZNEmptyDataSetDelegate>

@property(nonatomic,strong) SearchHeader *header;

@property(nonatomic,strong) UITableView *tableView;

@property(nonatomic,strong) NSMutableArray *deviceData;

@property(nonatomic,strong) DCHUDHelper *helper;

@property(nonatomic,assign) EnterSearchStyle style;


@end

@implementation DevicesSearchController

- (instancetype)initWithEnterStyle:(EnterSearchStyle)style
{
    if (self = [super init]) {
        self.style = style;
    }
    return self;
}


- (void)viewDidLoad {
    [super viewDidLoad];
    [[JCBluetoothManager sharedInstance] initBaby];
    self.view.backgroundColor = XY_HEX_RGB(0xF5F5F5);
    self.title = @"打印机";
    [self addObservers];
    [self initUI];
}

- (void)addObservers
{
   
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(openStateDidUpdate:) name:JCBlueToothOpenStateNotification object:nil];

   
}



-(void)openStateDidUpdate:(NSNotification*)info
{
    if (info.object) {
        BOOL isOn =  [info.object boolValue];
        if (isOn) {
            [JCBlUETOOTH_MANAGER startScan];
        }else{
            [self.header changeRefreshBtn:NO];
            [self.deviceData removeAllObjects];
            [self.tableView reloadData];
        }
     
    }
}




-(void)viewWillAppear:(BOOL)animated
{
    [super viewWillAppear:animated];
    [self observersBlueToothStates];
    [self.navigationController setNavigationBarHidden:NO animated:YES];
}

- (void)viewWillDisappear:(BOOL)animated
{
    [JCBlUETOOTH_MANAGER didReadBluetoothConnectStateHandler:nil];
    [super viewWillDisappear:animated];
    [self.navigationController setNavigationBarHidden:YES animated:NO];
}
- (void)initUI
{
    XYWeakSelf
    [self.view addSubview:self.header];
    [self.header mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(weakSelf.view).offset(15);
        make.top.equalTo(weakSelf.view).offset(XY_NaviBarMaxY + 15);
        make.trailing.equalTo(weakSelf.view).offset(-15);
        make.height.equalTo(@56);
    }];
    
    UILabel *nameLable = [[UILabel alloc] init];
    nameLable.text =@"附近设备";
    nameLable.font = XY_S_BOLD_FONT(16);
    nameLable.textColor = XY_HEX_RGB(0x262626);
    nameLable.textAlignment = NSTextAlignmentLeft;
    [self.view addSubview:nameLable];
    
    [nameLable mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(weakSelf.view).offset(15);
        make.top.equalTo(weakSelf.header.mas_bottom).offset(25);
        make.height.equalTo(@23);
    }];
    
    [self.view addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.equalTo(weakSelf.view);
        make.top.equalTo(nameLable.mas_bottom).offset(15);
        make.bottom.equalTo(weakSelf.view).offset(-10);
    }];
    [JCBlUETOOTH_MANAGER startScan];
    
}


- (void)observersBlueToothStates
{
    //添加已经连接的设备,因为连接之后蓝牙无法搜索到当前已经连接的设备
   JCBluetoothModel *connectedModel = [[JCBluetoothManager sharedInstance] getCurrentConnetedModel];
    if (connectedModel) {
        [self.deviceData addObject:connectedModel];
    }
    XYWeakSelf
    //发现新的设备
    [JCBlUETOOTH_MANAGER didDiscoverNewDevice:^(JCBluetoothModel * _Nonnull model){
        NSLog(@"搜索到的设备----%@",model.name);
         NSArray *peripheralArray = [weakSelf.deviceData valueForKey:@"name"];
        if (![peripheralArray containsObject:model.name]) {
            [weakSelf.deviceData addObject:model];
             [weakSelf.tableView reloadData];
        }
       
        
    }];
    //搜索中的各种状态
    [JCBlUETOOTH_MANAGER didReadBluetoothScanStateHandler:^(JCScanState state, NSString * _Nonnull desc) {
        if (state == JCScanStateSearching) {
            [weakSelf.deviceData removeAllObjects];
            [weakSelf.tableView reloadData];
            [weakSelf.header changeRefreshBtn:YES];
        }else if(state == JCScanStateBlueClose|| state ==JCScanStateAllClose ){
            [weakSelf.header changeRefreshBtn:NO];
            [weakSelf showOpenBluetoothAleart];
             
        }else{
            [weakSelf.header changeRefreshBtn:NO];
        }
    }];
  
    //设备连中的各种状态
    [JCBlUETOOTH_MANAGER didReadBluetoothConnectStateHandler:^(JCConnectState state, JCBluetoothModel * _Nullable model) {
        dispatch_async(dispatch_get_main_queue(), ^{
            switch (state) {
                case JCConnectStateInvaild:
                {
                    [weakSelf.helper onError:XY_LANGUAGE_TITLE_NAMED(@"app", @"无效连接")];
                    
                }
                    break;
             case JCConnectStateRepeat:
                {
                    NSLog(@"-----连接过于频繁-----");
                }
                    break;
          
               
                case JCConnectStateFailed:
                {
                    [weakSelf.helper onError:XY_LANGUAGE_TITLE_NAMED(@"app", @"连接失败")];
                    [JCBlUETOOTH_MANAGER startScan];
                    
                }
                    break;
              
                case JCConnectStateFailByChange:
                {
                    [weakSelf.tableView reloadData];
                }
                    break;
                case JCConnectStateSuccess:
                {
                    [weakSelf.helper onSuccess:XY_LANGUAGE_TITLE_NAMED(@"app", @"连接成功")];
                    [weakSelf.tableView reloadData];
                    if (weakSelf.style == EnterSearchStyleFirst) {
                   
                    }else if (weakSelf.style == EnterSearchStyleNone){
                        [weakSelf.navigationController popViewControllerAnimated:YES];
                    }else{
                        DeviceDetailController *detailVc = [[DeviceDetailController alloc] init];
                        [weakSelf.navigationController pushViewController:detailVc animated:YES];
                       NSMutableArray *naviStacks = [weakSelf.navigationController.viewControllers mutableCopy];
                        if (naviStacks.count >= 2) {
                            [naviStacks removeObjectAtIndex:naviStacks.count - 2];
                        }
                        weakSelf.navigationController.viewControllers = naviStacks;
                    }
                  
                }
                    break;
                    
                case JCConnectStateOuttime:
                {
                    
                    [weakSelf.helper onError:XY_LANGUAGE_TITLE_NAMED(@"app", @"连接超时")];
                    
                }
                    break;
              
                default:
               
                    break;
            }
        
        });
                 
    }];
    

}

- (void)showOpenBluetoothAleart
{
    [JCAlertView hideAllAleartOnView:XY_KEYWindow];
    BOOL unauthorized = JCBlUETOOTH_MANAGER.CBState == CBManagerStateUnauthorized;
    NSString *msg = unauthorized ?@"检测到系统蓝牙关闭，请在系统设置中重新启动蓝牙功能。":@"检测到系统蓝牙关闭，请在系统设置中重新启动蓝牙功能。";
    NSString *confirmStr = unauthorized ?@"去设置":@"知道了";
    [[JCAlertView alertWithTitle:@"提示" message:msg confirmTitle:confirmStr confirmClick:^{
        if (unauthorized) {
            dispatch_async(dispatch_get_main_queue(), ^{
                [[UIApplication sharedApplication] openURL:[NSURL URLWithString:UIApplicationOpenSettingsURLString]];
                });
        }else{
            
        }
    }] show];
   
}
    
    
#pragma mark -  UITableViewDelegate

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    return 94;
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return self.deviceData.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    BlueToothCell *cell = [tableView dequeueReusableCellWithIdentifier:BlueToothCellIdentifier forIndexPath:indexPath];
    JCBluetoothModel *model = [self.deviceData objectAtIndex:indexPath.row];
    cell.model = model;
    
    JCBluetoothModel *currentModel = [JCBlUETOOTH_MANAGER getCurrentConnetedModel];
   if (JC_IS_CONNECTED_PRINTER && [currentModel.name isEqualToString:model.name]) {
          cell.isConnect = YES;
    }else{
          cell.isConnect = NO;
    }
    cell.disconnect = ^{
        [self disconnectWithModel:model];
    };
    return cell;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    JCBluetoothModel *model = [self.deviceData objectAtIndex:indexPath.row];
    [self connectWithModel:model];
}

#pragma mark -  DZNEmptyDataSetDelegate
- (BOOL)emptyDataSetShouldDisplay:(UIScrollView *)scrollView
{
    return self.deviceData.count == 0 && !self.header.isSearching;
}



- (NSAttributedString *)titleForEmptyDataSet:(UIScrollView *)scrollView
{
    NSAttributedString *title = [[NSAttributedString alloc] initWithString:@"未搜索到设备"];
    return title;
}

- (NSAttributedString *)descriptionForEmptyDataSet:(UIScrollView *)scrollView
{
    NSString *msg = [JCBlUETOOTH_MANAGER getDeviceBluetoothState] ?@"点击按钮再次搜索设备": @"检测到系统蓝牙关闭，请在系统设置中重新启动蓝牙功能" ;
    NSAttributedString *desc = [[NSAttributedString alloc] initWithString:msg];
    return desc;
    
}


     
- (void)connectWithModel:(JCBluetoothModel*)ConnectModel
{
    ConnectModel.searchType = self.style;
    if (![ConnectModel.name isEqualToString:JC_CURRENT_CONNECTED_PRINTER]) {
             static NSTimeInterval lasttime = 0.0;
             NSTimeInterval currentTime = [NSDate date].timeIntervalSince1970;
             if (currentTime - lasttime < 2) {
                 [DCHUDHelper showInfoWithStatus:@"连接频繁，请稍后再试！"];
                 return;
             }
             lasttime = currentTime;
        self.helper= [DCHUDHelper showWithStatus:XY_LANGUAGE_TITLE_NAMED(@"app", @"正在连接...")];
       
        [JCBlUETOOTH_MANAGER connectWith:ConnectModel];
   
    }else{
        XYWeakSelf;
        [JCBlUETOOTH_MANAGER closeConnected];
        self.helper =  [DCHUDHelper showWithStatus:XY_LANGUAGE_TITLE_NAMED(@"app", @"正在断开连接...")];
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [weakSelf.helper onSuccess:XY_LANGUAGE_TITLE_NAMED(@"app", @"已断开连接")];
            [weakSelf.tableView reloadData];
        });
    }
}

- (void)disconnectWithModel:(JCBluetoothModel*)ConnectModel {
    XYWeakSelf;
    [JCBlUETOOTH_MANAGER closeConnected];
    self.helper =  [DCHUDHelper showWithStatus:XY_LANGUAGE_TITLE_NAMED(@"app", @"正在断开连接...")];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
    [weakSelf.helper onSuccess:XY_LANGUAGE_TITLE_NAMED(@"app", @"已断开连接")];
        [weakSelf.tableView reloadData];
    });
}

#pragma mark - lazy load
-(UITableView *)tableView
{
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
        _tableView.backgroundColor =XY_HEX_RGB(0xF5F5F5);
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.showsVerticalScrollIndicator = NO;
        _tableView.delegate = self;
        _tableView.dataSource = self;
        [_tableView registerClass:[BlueToothCell class] forCellReuseIdentifier:BlueToothCellIdentifier];
        _tableView.emptyDataSetSource = self;
        _tableView.emptyDataSetDelegate = self;
    }
    return _tableView;
    
}

- (SearchHeader *)header
{
    
    if (!_header) {
        _header = [[SearchHeader alloc] initWithFrame:CGRectZero];
        _header.reSerachAction = ^(id x) {
            [JCBlUETOOTH_MANAGER startScan];
        };
    }
    return _header;
}


- (NSMutableArray *)deviceData
{
    
    if (!_deviceData) {
        _deviceData = [NSMutableArray array];
    }
    return _deviceData;
    

}


-(void)dealloc
{
    NSLog(@"%@---delloc",NSStringFromClass(self.class));
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

@end
