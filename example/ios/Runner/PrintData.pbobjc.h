// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: PrintData.proto

// This CPP symbol can be defined to use imports that match up to the framework
// imports needed when using CocoaPods.
#if !defined(GPB_USE_PROTOBUF_FRAMEWORK_IMPORTS)
 #define GPB_USE_PROTOBUF_FRAMEWORK_IMPORTS 0
#endif

#if GPB_USE_PROTOBUF_FRAMEWORK_IMPORTS
 #import <Protobuf/GPBProtocolBuffers.h>
#else
 #import "GPBProtocolBuffers.h"
#endif

#if GOOGLE_PROTOBUF_OBJC_VERSION < 30004
#error This file was generated by a newer version of protoc which is incompatible with your Protocol Buffer library sources.
#endif
#if 30004 < GOOGLE_PROTOBUF_OBJC_MIN_SUPPORTED_VERSION
#error This file was generated by an older version of protoc which is incompatible with your Protocol Buffer library sources.
#endif

// @@protoc_insertion_point(imports)

#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"

CF_EXTERN_C_BEGIN

NS_ASSUME_NONNULL_BEGIN

#pragma mark - PrintDataRoot

/**
 * Exposes the extension registry for this file.
 *
 * The base class provides:
 * @code
 *   + (GPBExtensionRegistry *)extensionRegistry;
 * @endcode
 * which is a @c GPBExtensionRegistry that includes all the extensions defined by
 * this file and all files that it depends on.
 **/
GPB_FINAL @interface PrintDataRoot : GPBRootObject
@end

#pragma mark - PrintData

typedef GPB_ENUM(PrintData_FieldNumber) {
  PrintData_FieldNumber_Data_p = 1,
  PrintData_FieldNumber_Width = 2,
  PrintData_FieldNumber_Height = 3,
  PrintData_FieldNumber_Rotate = 4,
  PrintData_FieldNumber_Quantity = 5,
  PrintData_FieldNumber_LabelType = 6,
  PrintData_FieldNumber_Density = 7,
};

/**
 * protoc --proto_path=protos  --dart_out=lib/gen22 --plugin=D:\\flutter_env\\protobuf-master\\protoc_plugin\\bin\\protoc-gen-dart  jcimagesdkparam.proto
 **/
GPB_FINAL @interface PrintData : GPBMessage

@property(nonatomic, readwrite, copy, null_resettable) NSData *data_p;

@property(nonatomic, readwrite) double width;

@property(nonatomic, readwrite) double height;

@property(nonatomic, readwrite) int32_t rotate;

@property(nonatomic, readwrite) int32_t quantity;

@property(nonatomic, readwrite) int32_t labelType;

@property(nonatomic, readwrite) int32_t density;

@end

NS_ASSUME_NONNULL_END

CF_EXTERN_C_END

#pragma clang diagnostic pop

// @@protoc_insertion_point(global_scope)
