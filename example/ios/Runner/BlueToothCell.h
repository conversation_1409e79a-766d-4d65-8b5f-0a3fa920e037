//
//  BlueToothCell.h
//  Runner
//
//  Created by <PERSON> on 2020/11/24.
//

#import <UIKit/UIKit.h>
#import "JCBluetoothModel.h"

NS_ASSUME_NONNULL_BEGIN


static NSString* const BlueToothCellIdentifier = @"BlueToothCellIdentifier";

@interface BlueToothCell : UITableViewCell

@property(nonatomic,copy) JCBluetoothModel *model;

@property(nonatomic,assign) BOOL isConnect;

@property(nonatomic,copy)void(^disconnect)(void);

@end

NS_ASSUME_NONNULL_END
