//
//  CustomFucMacro.h
//  ChenYin
//
//  Created by <PERSON> on 2020/4/20.
//  Copyright © 2020 JCChenYin. All rights reserved.
//

#import <Foundation/Foundation.h>

#ifndef CustomFucMacro_h
#define CustomFucMacro_h
/*********************************************************
 *
 *  自定义宏区域
 *
 *********************************************************/

/* 消除warning : performSelector may cause a leak because its selector is unknown*/

#define sel(selName) NSSelectorFromString(selName)

#define JCPerformSelector(target,selector)                                  \
if (target && [target respondsToSelector:selector]) {           \
IMP jc_imp = [target methodForSelector:selector];           \
void (*jc_func)(id, SEL) = (void *)jc_imp;                  \
jc_func(target,selector);                                   \
}

#define JCPerformSelectorWithOneObject(target,selector,param)               \
if (target && [target respondsToSelector:selector]) {           \
IMP jc_imp = [target methodForSelector:selector];           \
void (*jc_func)(id, SEL,id) = (void *)jc_imp;               \
jc_func(target,selector,param);                             \
}



#define XY_NOT_DESIGNATED_INITIALIZER(DESIGNATED_INITIALIZER) \
@throw [NSException exceptionWithName:NSInvalidArgumentException \
reason:[NSString stringWithFormat:@"Please use the designated initializer [%p %@]", \
self, \
NSStringFromSelector(@selector(DESIGNATED_INITIALIZER))] \
userInfo:nil]
/*********************************************************
 *
 *  常用宏区域（固定）
 *
 *********************************************************/
//-------------------打印日志-------------------------
//DEBUG  模式下打印日志,当前行
#ifdef DEBUG
#   define DLog(fmt, ...) NSLog((@"%s [Line %d] " fmt), __PRETTY_FUNCTION__, __LINE__, ##__VA_ARGS__);
#else
#   define DLog(...)
#endif


//重写NSLog,Debug模式下打印日志和当前行数
#if DEBUG
#define NSLog(FORMAT, ...) fprintf(stderr,"\nfunction:%s line:%d content:%s\n", __FUNCTION__, __LINE__, [[NSString stringWithFormat:FORMAT, ##__VA_ARGS__] UTF8String])
#else
#define NSLog(FORMAT, ...) nil
#endif

//DEBUG  模式下打印日志,当前行 并弹出一个警告
#ifdef DEBUG
#   define ULog(fmt, ...)  { UIAlertView *alert = [[UIAlertView alloc] initWithTitle:[NSString stringWithFormat:@"%s\n [Line %d] ", __PRETTY_FUNCTION__, __LINE__] message:[NSString stringWithFormat:fmt, ##__VA_ARGS__]  delegate:nil cancelButtonTitle:@"Ok" otherButtonTitles:nil]; [alert show]; }
#else
#   define ULog(...)
#endif



#define UN_NIL(s) ((s==nil || [s isKindOfClass:[NSNull class]]||[@"" isEqualToString:s])?@"":s)
#define OBJ_IS_NIL(s) (s==nil || [s isKindOfClass:[NSNull class]])
#define STR_IS_NIL(key) ([@"(null)" isEqualToString:(key)] || [@"" isEqualToString:(key)] || key == nil || [key isKindOfClass:[NSNull class]] || [@"<null>" isEqualToString:(key)] )


#define DB_NAME  [XY_JC_LANGUAGE hasPrefix:@"zh-"]?[NSString stringWithFormat:@"JCPrint_%@.sqlite",@"zh-cn"]:[NSString stringWithFormat:@"JCPrint_%@.sqlite",@"en"]

/****************国际化**********************/
#define XY_CURRENT_SYSTEM_LANGUAGE [((NSArray *)[NSLocale preferredLanguages]) objectAtIndex:0]

// modify by heron
#define XY_LANGUAGE_DETAIL_DIC   @{}
//#define XY_LANGUAGE_DETAIL_DIC [[NSUserDefaults standardUserDefaults] objectForKey:@"App_Last_Used_Font_Key"]

#define XY_CURRENT_APP_LANGUAGE [[NSUserDefaults standardUserDefaults] objectForKey:@"App_Language_Switch_Key"]
//#define XY_JC_LANGUAGE_REAL [XYTool getNationalLanguageNameFromSystemLanguage:XY_CURRENT_APP_LANGUAGE]
#define XY_JC_LANGUAGE  @"zh-cn" //[XY_JC_LANGUAGE_REAL isEqualToString:@"zh-cn"]?@"zh-cn":@"en"

#define XY_ChineseConvert(valueString)  valueString

//#define XY_ChineseConvertSimple(valueString) [JCChineseConvert convertTraditionalToSimplified:valueString]

#define XY_LANGUAGE_TITLE_NAMED(name,defaultString)      ([[XY_LANGUAGE_DETAIL_DIC objectForKey:name] objectForKey:@"value"] == nil)?XY_ChineseConvert(defaultString):XY_ChineseConvert([[XY_LANGUAGE_DETAIL_DIC objectForKey:name] objectForKey:@"value"])

#define StringFromInt(x) [NSString stringWithFormat:@"%ld",x]
#define ITTDEBUG
#define ITTLOGLEVEL_INFO     10
#define ITTLOGLEVEL_WARNING  3
#define ITTLOGLEVEL_ERROR    1

#ifndef ITTMAXLOGLEVEL

#ifdef DEBUG
#define ITTMAXLOGLEVEL ITTLOGLEVEL_INFO
#else
#define ITTMAXLOGLEVEL ITTLOGLEVEL_ERROR
#endif

#endif

// The general purpose logger. This ignores logging levels.
#ifdef ITTDEBUG
#define ITTDPRINT(xx, ...)  NSLog(@"%s(%d): " xx, __PRETTY_FUNCTION__, __LINE__, ##__VA_ARGS__)
#else
#define ITTDPRINT(xx, ...)  ((void)0)
#endif

// Prints the current method's name.
#define ITTDPRINTMETHODNAME() ITTDPRINT(@"%s", __PRETTY_FUNCTION__)

// Log-level based logging macros.
#if ITTLOGLEVEL_ERROR <= ITTMAXLOGLEVEL
#define ITTDERROR(xx, ...)  ITTDPRINT(xx, ##__VA_ARGS__)
#else
#define ITTDERROR(xx, ...)  ((void)0)
#endif

#if ITTLOGLEVEL_WARNING <= ITTMAXLOGLEVEL
#define ITTDWARNING(xx, ...)  ITTDPRINT(xx, ##__VA_ARGS__)
#else
#define ITTDWARNING(xx, ...)  ((void)0)
#endif

#if ITTLOGLEVEL_INFO <= ITTMAXLOGLEVEL
#define ITTDINFO(xx, ...)  ITTDPRINT(xx, ##__VA_ARGS__)
#else
#define ITTDINFO(xx, ...)  ((void)0)
#endif

#ifdef ITTDEBUG
#define ITTDCONDITIONLOG(condition, xx, ...) { if ((condition)) { \
ITTDPRINT(xx, ##__VA_ARGS__); \
} \
} ((void)0)
#else
#define ITTDCONDITIONLOG(condition, xx, ...) ((void)0)
#endif

#define ITTAssert(condition, ...)                                       \
do {                                                                      \
if (!(condition)) {                                                     \
[[NSAssertionHandler currentHandler]                                  \
handleFailureInFunction:[NSString stringWithUTF8String:__PRETTY_FUNCTION__] \
file:[NSString stringWithUTF8String:__FILE__]  \
lineNumber:__LINE__                                  \
description:__VA_ARGS__];                             \
}                                                                       \
} while(0)

//---------------------打印日志--------------------------



//----------------------内存----------------------------

//使用ARC和不使用ARC
#if __has_feature(objc_arc)
//compiling with ARC
#else
// compiling without ARC
#endif

#pragma mark - common functions
#define RELEASE_SAFELY(__POINTER) { [__POINTER release]; __POINTER = nil; }

//释放一个对象
#define SAFE_DELETE(P) if(P) { [P release], P = nil; }

#define SAFE_RELEASE(x) [x release];x=nil



//----------------------内存----------------------------


//由角度获取弧度 有弧度获取角度
#define degreesToRadian(x) (M_PI * (x) / 180.0)
#define radianToDegrees(radian) (radian*180.0)/(M_PI)
//----------------------其他----------------------------

// 弱引用
#define XYWeakSelf __weak typeof(self) weakSelf = self;
#define XYStrongSelf __strong typeof(weakSelf) strongSelf = weakSelf;

//获取全局delegate
#define AppDelegateObj ((AppDelegate *)[[UIApplication sharedApplication] delegate])


//=========================================

#define DC_OVERLOADABLE __attribute__((overloadable))

#ifndef DC_WARN_UNUSED_RESULT
#define DC_WARN_UNUSED_RESULT __attribute__((warn_unused_result))
#endif

/**判断系统版本*/
#define iOSVersionValue ([UIDevice currentDevice].systemVersion.floatValue)
#define isiOS(version) (iOSVersionValue >= (version))

/** 发布通知 */
#define DCNCPost3p(broadcastName, oParam, infoParam) [[NSNotificationCenter defaultCenter] postNotificationName:broadcastName object:oParam userInfo:infoParam]
#define DCNCPost2p(broadcastName, object) DCNCPost3p(broadcastName, object, nil)
#define DCNCPost(broadcastName) DCNCPost3p(broadcastName, nil, nil)
/** 添加通知观察者 */
#define DCNCAddOb(ObParam, selParam, nameParam, objParam)    [[NSNotificationCenter defaultCenter] addObserver:ObParam selector:selParam name:nameParam object:objParam]
#define DCNCRemoveOb(ObParam, nameParam, objParam)   [[NSNotificationCenter defaultCenter] removeObserver:ObParam name:nameParam object:objParam]

#define DCUserDefaultsObjFor(key)  [[NSUserDefaults standardUserDefaults] objectForKey:key]

#define DCUserDefaultsSetObj(key,value)   [[NSUserDefaults standardUserDefaults] setObject:value forKey:key]

/** 系统的版本号 */
#define DC_SYSTEM_VERSION [[[UIDevice currentDevice] systemVersion] floatValue]

/** APP版本号 */
#define DC_APP_VERSION  [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleShortVersionString"]

/** APP BUILD 版本号 */
#define DC_APP_BUILD_VERSION  [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleVersion"]

/** APP名字 */
#define DC_APP_DISPLAY_NAME  [[NSBundle mainBundle] objectForInfoDictionaryKey:@"CFBundleDisplayName"]

#define DC_APP_IDENTIFIER  [[NSBundle mainBundle] objectForInfoDictionaryKey:@"CFBundleIdentifier"]

/** 当前语言 */
#define XY_LOCAL_LANGUAGE [[NSLocale currentLocale] objectForKey:NSLocaleLanguageCode]

#define JC_PageSize @30

#define JC_NonetworkCode 0401

#define LBXScan_Define_Native  //包含native库
#define LBXScan_Define_ZXing   //包含ZXing库
#define LBXScan_Define_ZBar   //包含ZBar库
#define LBXScan_Define_UI     //包含界面库


#endif /* CustomFucMacro_h */
