//
//  DomainMacro.h
//  ChenYin
//
//  Created by <PERSON> on 2020/4/22.
//  Copyright © 2020 JCChenYin. All rights reserved.
//

#ifndef DCRequestMacro_h
#define DCRequestMacro_h

#define InjectYDY_URL  [YTKNetworkConfig sharedConfig].baseUrl = JCYDYServerURL;

#define RebaseCY_URL [YTKNetworkConfig sharedConfig].baseUrl = JCCommonServerURL;


#define Environment_Online 1   //线上环境
#define Environment_Test 2     //测试环境
#define Environment_Dev 3     // 开发环境

#define Environment Environment_Test    //当前环境配置 此处设置


#if DEBUG_DEV

#define JCCommonServerURL   @"http://chenyin.jc-dev.cn/api/"
#define JCYDYServerURL   @"http://print.niimbot.com/api/"
#define JC_UploadImage_URL  @"http://uos.jc-test.cn/api/image-upload/aliyun/oss/policy"
#define SchemesWX               @"cxy.shop.jc-dev.cn"
#define JCMALLURL @"http://saas.jc-dev.cn/"
#elif PROFILE_TEST  /*=====构建机测试服打包====*/

#define JCCommonServerURL   @"http://chenyin.jc-test.cn/api/"
#define JCYDYServerURL   @"http://print.niimbot.com/api/"
#define JC_UploadImage_URL  @"http://homo.jc-saas.com/image-upload/aliyun/oss/policy"
#define SchemesWX               @"cxy.shop.jc-test.cn"
#define JCMALLURL @"https://shop.jc-test.cn"

#elif DEBUG_PRODUCT || PROFILE  /*=====构建机正式服打包====*/

#define JCCommonServerURL   @"http://cxy.niimbot.com/api/"
#define JCYDYServerURL   @"http://print.niimbot.com/api/"
#define JC_UploadImage_URL  @"http://homo.jc-saas.com/image-upload/aliyun/oss/policy"
#define SchemesWX               @"cxy.shop.jc-saas.com"
#define JCMALLURL @"https://shop.jc-saas.com/"
#elif DEBUG

#if(Environment == Environment_Online)//生产

#define JCCommonServerURL   @"http://cxy.niimbot.com/api/"
#define JCYDYServerURL   @"http://print.niimbot.com/api/"
#define JC_UploadImage_URL  @"http://homo.jc-saas.com/image-upload/aliyun/oss/policy"
#define JCMALLURL @"https://shop.jc-saas.com/"
#define SchemesWX               @"cxy.shop.jc-saas.com"

#elif(Environment == Environment_Dev)//开发环境

#define JCCommonServerURL   @"http://chenyin.jc-dev.cn/api/"
#define JCYDYServerURL   @"http://print.niimbot.com/api/"
#define JC_UploadImage_URL  @"http://homo.jc-saas.com/image-upload/aliyun/oss/policy"
#define JCMALLURL  @"http://saas.jc-dev.cn/"
#define SchemesWX               @"cxy.shop.jc-dev.cn"
#else//测试
#define JCCommonServerURL   @"http://chenyin.jc-test.cn/api/"
#define JCYDYServerURL   @"http://print.niimbot.com/api/"
#define JC_UploadImage_URL  @"http://uos.jc-test.cn/api/image-upload/aliyun/oss/policy"
//#define JCMALLURL @"https://shop.jc-test.cn"
#define JCMALLURL @"https://shop.jc-saas.com/"
#define SchemesWX          @"cxy.shop.jc-test.cn"

#endif

#else /*=====release====*/


#define JCCommonServerURL @"http://cxy.niimbot.com/api/"
#define JCYDYServerURL   @"http://print.niimbot.com/api/"
#define JC_UploadImage_URL  @"http://homo.jc-saas.com/image-upload/aliyun/oss/policy"
#define SchemesWX               @"cxy.shop.jc-saas.com"
#define JCMALLURL @"https://shop.jc-saas.com/"

#endif


/*********************************************************
 *
 *  REQUEST URL
 *
 *********************************************************/


//登录
#define JC_UserModule_LoginURL  @"user/code/login"
//获取验证码
#define JC_UserModule_VerityCodeURL  @"user/login/code"

//获取用户信息
#define JC_UserModule_UserInfoURL @"user/logged"

//三方登录
#define JC_UserModule_SocalLoginURL  @"user/social/login"

//游客登录
#define JC_UserModule_TouristLoginURL  @"user/device/login"

//绑定账号
#define JC_UserModule_BindSocialURL  @"user/social/bind"

//解除绑定
#define JC_UserModule_UnbindSocialURL  @"user/social/unbind"

//退出登录
#define JC_UserModule_LoginOutURL @"user/logout"

//注销登录
#define JC_UserModule_LoginOffURL @"user/account/logoff"

//更换/补充账户手机信息
#define JC_UserModule_Account_additon_URL @"user/account/addition"

//个人信息
#define JC_UserModule_Personal_Info_URL @"user/account/personal"


//===============设备=============================
//设备属性详情
#define JC_DeviceModule_DetailInfo_URL @"kraken/system/device/machinedetail"

//上传打印记录
#define JC_data_printRecord                   @"kraken/print/board/record/report"

//获取打印策略
#define JC_get_Rfid_PrintStrategy   @"kraken/print/board/rfid/getRfidPrintStrategy"

//获取纸张RFID信息
#define JC_get_Rfid                  @"kraken/print/board/rfid/getRfid"

//打印记录校验
#define JC_machine_checkRecordUnique            @"kraken/print/board/record/checkRecordUnique"

//===============标签=============================
//标签分类
#define JC_TagModule_Category_URL @"content/label/category"

//标签分页查询
#define JC_TagModule_Page_URL @"content/label/square"

//用户收藏列表
#define JC_TagModule_Collect_URL @"content/label"

//我的标签分页查询
#define JC_TagModule_Mine_URL @"content/label/page"

//发布生活标记
#define JC_TagModule_Publish_URL @"content/label/remark"

//点赞
#define JC_TagModule_Like_URL(labelId) [NSString stringWithFormat:@"content/label/%@/collect",labelId]

//使用计数
#define JC_TagModule_Used_URL(labelId)   [NSString stringWithFormat:@"content/label/%@/use",labelId]

//删除标签
#define JC_TagModule_Delete_URL(labelId)   [NSString stringWithFormat:@"content/label/%@",labelId]

//谁可以看
#define JC_TagModule_Limit_URL  @"content/label/tag"

//===============模板=============================
//云模板列表
#define JC_TemplateModule_CloundList_URL  @"content/label/template/page/external"

#define JC_GetFonts         @"font/lib/external"

#define JC_GetTemplateTag   @"content/label/resolve/category"

#define JC_CreateTemplate   @"content/label"

// 获取素材库行业分类列表地
#define JC_GetLogoCatergory @"content/material/external/industry/list"

// 获取素材库icon分页
#define JC_GetLogoList  @"content/material/external/page"

//保存&上传打印历史
#define JC_TemplateModule_SaveAndHistory_URL  @"content/label/resolver/save"

//静默保存(保存&上传打印历史批量)
#define JC_TemplateModule_SaveAndHistoryArray_URL  @"content/label/resolver/batchSave"

//打印历史(所有人都可上传)
#define JC_TemplateModule_History_external_URL  @"template/print/history/external"

//打印历史
#define JC_TemplateModule_History_URL  @"template/print/history"

//通过模板id查询
#define JC_TagModule_GetTemplateData_URL @"kraken/imprint/get"

#define JC_Icons_Borders_Path @"kraken/print/board/getMaterialPage"

#define JC_Icon_Category_Path @"kraken/print/board/getMaterialCategory"



#endif /* DCRequestMacro_h */
