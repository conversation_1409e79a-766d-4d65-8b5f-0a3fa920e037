// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: PrintData.proto

// This CPP symbol can be defined to use imports that match up to the framework
// imports needed when using CocoaPods.
#if !defined(GPB_USE_PROTOBUF_FRAMEWORK_IMPORTS)
 #define GPB_USE_PROTOBUF_FRAMEWORK_IMPORTS 0
#endif

#if GPB_USE_PROTOBUF_FRAMEWORK_IMPORTS
 #import <Protobuf/GPBProtocolBuffers_RuntimeSupport.h>
#else
 #import "GPBProtocolBuffers_RuntimeSupport.h"
#endif

#import "PrintData.pbobjc.h"
// @@protoc_insertion_point(imports)

#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"

#pragma mark - PrintDataRoot

@implementation PrintDataRoot

// No extensions in the file and no imports, so no need to generate
// +extensionRegistry.

@end

#pragma mark - PrintDataRoot_FileDescriptor

static GPBFileDescriptor *PrintDataRoot_FileDescriptor(void) {
  // This is called by +initialize so there is no need to worry
  // about thread safety of the singleton.
  static GPBFileDescriptor *descriptor = NULL;
  if (!descriptor) {
    GPB_DEBUG_CHECK_RUNTIME_VERSIONS();
    descriptor = [[GPBFileDescriptor alloc] initWithPackage:@""
                                                     syntax:GPBFileSyntaxProto3];
  }
  return descriptor;
}

#pragma mark - PrintData

@implementation PrintData

@dynamic data_p;
@dynamic width;
@dynamic height;
@dynamic rotate;
@dynamic quantity;
@dynamic labelType;
@dynamic density;

typedef struct PrintData__storage_ {
  uint32_t _has_storage_[1];
  int32_t rotate;
  int32_t quantity;
  int32_t labelType;
  int32_t density;
  NSData *data_p;
  double width;
  double height;
} PrintData__storage_;

// This method is threadsafe because it is initially called
// in +initialize for each subclass.
+ (GPBDescriptor *)descriptor {
  static GPBDescriptor *descriptor = nil;
  if (!descriptor) {
    static GPBMessageFieldDescription fields[] = {
      {
        .name = "data_p",
        .dataTypeSpecific.clazz = Nil,
        .number = PrintData_FieldNumber_Data_p,
        .hasIndex = 0,
        .offset = (uint32_t)offsetof(PrintData__storage_, data_p),
        .flags = (GPBFieldFlags)(GPBFieldOptional | GPBFieldClearHasIvarOnZero),
        .dataType = GPBDataTypeBytes,
      },
      {
        .name = "width",
        .dataTypeSpecific.clazz = Nil,
        .number = PrintData_FieldNumber_Width,
        .hasIndex = 1,
        .offset = (uint32_t)offsetof(PrintData__storage_, width),
        .flags = (GPBFieldFlags)(GPBFieldOptional | GPBFieldClearHasIvarOnZero),
        .dataType = GPBDataTypeDouble,
      },
      {
        .name = "height",
        .dataTypeSpecific.clazz = Nil,
        .number = PrintData_FieldNumber_Height,
        .hasIndex = 2,
        .offset = (uint32_t)offsetof(PrintData__storage_, height),
        .flags = (GPBFieldFlags)(GPBFieldOptional | GPBFieldClearHasIvarOnZero),
        .dataType = GPBDataTypeDouble,
      },
      {
        .name = "rotate",
        .dataTypeSpecific.clazz = Nil,
        .number = PrintData_FieldNumber_Rotate,
        .hasIndex = 3,
        .offset = (uint32_t)offsetof(PrintData__storage_, rotate),
        .flags = (GPBFieldFlags)(GPBFieldOptional | GPBFieldClearHasIvarOnZero),
        .dataType = GPBDataTypeInt32,
      },
      {
        .name = "quantity",
        .dataTypeSpecific.clazz = Nil,
        .number = PrintData_FieldNumber_Quantity,
        .hasIndex = 4,
        .offset = (uint32_t)offsetof(PrintData__storage_, quantity),
        .flags = (GPBFieldFlags)(GPBFieldOptional | GPBFieldClearHasIvarOnZero),
        .dataType = GPBDataTypeInt32,
      },
      {
        .name = "labelType",
        .dataTypeSpecific.clazz = Nil,
        .number = PrintData_FieldNumber_LabelType,
        .hasIndex = 5,
        .offset = (uint32_t)offsetof(PrintData__storage_, labelType),
        .flags = (GPBFieldFlags)(GPBFieldOptional | GPBFieldTextFormatNameCustom | GPBFieldClearHasIvarOnZero),
        .dataType = GPBDataTypeInt32,
      },
      {
        .name = "density",
        .dataTypeSpecific.clazz = Nil,
        .number = PrintData_FieldNumber_Density,
        .hasIndex = 6,
        .offset = (uint32_t)offsetof(PrintData__storage_, density),
        .flags = (GPBFieldFlags)(GPBFieldOptional | GPBFieldClearHasIvarOnZero),
        .dataType = GPBDataTypeInt32,
      },
    };
    GPBDescriptor *localDescriptor =
        [GPBDescriptor allocDescriptorForClass:[PrintData class]
                                     rootClass:[PrintDataRoot class]
                                          file:PrintDataRoot_FileDescriptor()
                                        fields:fields
                                    fieldCount:(uint32_t)(sizeof(fields) / sizeof(GPBMessageFieldDescription))
                                   storageSize:sizeof(PrintData__storage_)
                                         flags:(GPBDescriptorInitializationFlags)(GPBDescriptorInitializationFlag_UsesClassRefs | GPBDescriptorInitializationFlag_Proto3OptionalKnown)];
#if !GPBOBJC_SKIP_MESSAGE_TEXTFORMAT_EXTRAS
    static const char *extraTextFormatInfo =
        "\001\006\t\000";
    [localDescriptor setupExtraTextInfo:extraTextFormatInfo];
#endif  // !GPBOBJC_SKIP_MESSAGE_TEXTFORMAT_EXTRAS
    #if defined(DEBUG) && DEBUG
      NSAssert(descriptor == nil, @"Startup recursed!");
    #endif  // DEBUG
    descriptor = localDescriptor;
  }
  return descriptor;
}

@end


#pragma clang diagnostic pop

// @@protoc_insertion_point(global_scope)
