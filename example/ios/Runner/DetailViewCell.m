//
//  DetailViewCell.m
//  Runner
//
//  Created by <PERSON> on 2021/1/22.
//

#import "DetailViewCell.h"
#import "CommonCtrl.h"

@implementation  CornerStyleCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.contentView.backgroundColor = XY_HEX_RGB(0xF2F2F2);
        [self initUI];
        
    }
    return self;
}

- (void)initUI
{
    
    UIView *cornerView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, kSCREEN_WIDTH-32, 60)];
    cornerView.backgroundColor = COLOR_WHITE;
    [self.contentView addSubview:cornerView];
    self.cornerView = cornerView;
    
    UILabel *titleLabel = [[UILabel alloc] init];
    titleLabel.font = XY_S_BOLD_FONT(16);
    titleLabel.textColor = XY_HEX_RGB(0x262626);
    titleLabel.textAlignment = NSTextAlignmentLeft;
    [ self.cornerView addSubview:titleLabel];
    self.nameLabel = titleLabel;
    
   
    UIView *line = [[UIView alloc] init];
    line.backgroundColor = XY_HEX_RGB(0xEBEBEB);
    [ self.cornerView addSubview:line];
    
    XYWeakSelf;
    [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_equalTo(weakSelf.cornerView);
        make.leading.equalTo(weakSelf.cornerView).offset(24);
    }];
    
 
    [line mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.bottom.equalTo(weakSelf.cornerView);
        make.height.mas_equalTo(1);
    }];
    
   
}

- (void)setConnerStyle:(CellConnerStyle)connerStyle
{
    switch (connerStyle) {
        case CellConnerStyleTop:
            [self.cornerView jk_setRoundedCorners:UIRectCornerTopLeft|UIRectCornerTopRight radius:10];
            break;
        case CellConnerStyleMiddle:
            [self.cornerView jk_setRoundedCorners:UIRectCornerAllCorners radius:0];
            break;
        case CellConnerStyleBottom:
            [self.cornerView jk_setRoundedCorners:UIRectCornerBottomRight|UIRectCornerBottomLeft radius:10];
            
            break;
        default:
            break;
    }
}

- (void)setTitle:(NSString *)title
{
    self.nameLabel.text = title;
}


@end


@interface DetailViewCell ()

@property(nonatomic,strong) UILabel *detailInfoText;

@property(nonatomic,strong) UIImageView *arrowRight;

@property(nonatomic,strong) UIImageView *redPoint;


@end

@implementation DetailViewCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.contentView.backgroundColor = XY_HEX_RGB(0xF2F2F2);
        [self setupUI];
        
    }
    return self;
}

- (void)setupUI
{
    
   
    
    UIImageView *redPoint = [[UIImageView alloc] initWithImage:XY_IMAGE_NAMED(@"red_Point")];
    redPoint.hidden = YES;
    [self.cornerView addSubview:redPoint];
    self.redPoint = redPoint;
    
    UILabel *detailInfo = [[UILabel alloc] init];
    detailInfo.font = XY_S_FONT(16);
    detailInfo.textColor = XY_HEX_RGB(0x262626);
    detailInfo.textAlignment = NSTextAlignmentRight;
    [ self.cornerView addSubview:detailInfo];
    self.detailInfoText = detailInfo;
    
    UIImageView *arrowIcon = [[UIImageView alloc] initWithImage:XY_IMAGE_NAMED(@"Chevron")];
    arrowIcon.hidden = YES;
    [ self.cornerView addSubview:arrowIcon];
    self.arrowRight = arrowIcon;
    
    UIView *line = [[UIView alloc] init];
    line.backgroundColor = XY_HEX_RGB(0xEBEBEB);
    [ self.cornerView addSubview:line];
    
    XYWeakSelf;
 
    [detailInfo mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_equalTo(weakSelf.cornerView);
        make.trailing.equalTo(weakSelf.cornerView).offset(-42);
    }];
    
    [redPoint mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_equalTo(weakSelf.cornerView);
        make.trailing.equalTo(weakSelf.detailInfoText.mas_leading).offset(-9);
    }];
    
    [arrowIcon mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_equalTo(weakSelf.cornerView);
        make.trailing.equalTo(weakSelf.cornerView).offset(-21);
        make.size.mas_equalTo(CGSizeMake(8, 14));
    }];
    
   
   
}


- (void)setDetailInfo:(NSString *)detailInfo
{
    self.detailInfoText.text = detailInfo;
}

- (void)setInteractionEnabled:(BOOL)interactionEnabled
{
    self.arrowRight.hidden = !interactionEnabled;
}

- (void)setNeedUpdata:(BOOL)needUpdata
{
    self.redPoint.hidden = !needUpdata;
    self.interactionEnabled = YES;
}

@end

@implementation CalibrationCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.contentView.backgroundColor = XY_HEX_RGB(0xF2F2F2);
        [self setupUI];
        
    }
    return self;
}

- (void)setupUI
{
    UIButton * button = [CommonCtrl buttonWithTitlecolor:XY_HEX_RGB(0x537FB7) font:XY_S_FONT(16) target:self action:@selector(buttonDidClick:)];
    [button setTitle:@"开始校准" forState:UIControlStateNormal];
    [self.cornerView addSubview:button];
    XYWeakSelf;
    [button mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(weakSelf.cornerView);
        make.trailing.equalTo(weakSelf.cornerView).offset(-12);
        make.height.mas_equalTo(35);
    }];
}

- (void)buttonDidClick:(UIButton*)sender
{
    if (self.clickBlock) {
        self.clickBlock();
    }
}

@end



@implementation AutoPowerOffCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.contentView.backgroundColor = XY_HEX_RGB(0xF2F2F2);
        [self setupUI];
        
    }
    return self;
}

- (void)setupUI
{
    UIImageView* selectIcon = [[UIImageView alloc] initWithImage:XY_IMAGE_NAMED(@"poweroff_select")];
    selectIcon.hidden = YES;
    self.selectIcon = selectIcon;
    [self.cornerView addSubview:self.selectIcon];
    XYWeakSelf;
    [selectIcon mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(weakSelf.cornerView);
        make.trailing.equalTo(weakSelf.cornerView).offset(-12);
        make.size.mas_equalTo(CGSizeMake(19, 14));
        
        
    }];
}

@end
