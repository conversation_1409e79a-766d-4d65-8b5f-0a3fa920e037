//
//  JCDeviceUpdateAleart.h
//  Runner
//
//  Created by <PERSON> on 2021/1/22.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface JCDeviceUpdateAleart : UIView

+ (instancetype)aleartWithTitle:(NSString*)title
                       subtitle:(NSString *)subtitle
                        Message:(NSString*)message
                   ComfirmBlock:(XYNormalBlock)comfirmBlock
                    cancelBlock:(XYNormalBlock)cancelBlock;

@end

// 1 准备中  2.升级中  3.错误  4.完成
typedef NS_ENUM(NSUInteger, UpdataProessState) {
    UpdataProessStatePrepare = 1,
    UpdataProessStateUpdating,
    UpdataProessStateError,
    UpdataProessStateFinish
};

@interface JCUpdataProessAleart : UIView

@property(nonatomic,assign) CGFloat progressNum;


@property(nonatomic,copy) XYNormalBlock retryBlock;

- (void) onState:(UpdataProessState)state Title:(NSString*)title detalMsg:(NSString*)detaiMsg;


@end



NS_ASSUME_NONNULL_END
