//
//  JCDeviceUpdateAleart.m
//  Runner
//
//  Created by <PERSON> on 2021/1/22.
//

#import "JCDeviceUpdateAleart.h"
#import "CommonCtrl.h"


@interface JCDeviceUpdateAleart()

@property(nonatomic,copy) XYNormalBlock comfirmBlock ;

@property(nonatomic,copy) XYNormalBlock cancelBlock ;

@property(nonatomic,copy) NSString *title;
@property (nonatomic, strong) NSString *subtile;

@property(nonatomic,copy) NSString *message;


@end

@implementation JCDeviceUpdateAleart

+ (instancetype)aleartWithTitle:(NSString *)title
                       subtitle:(NSString *)subtitle
                        Message:(NSString*)message
                   ComfirmBlock:(XYNormalBlock)comfirmBlock
                    cancelBlock:(XYNormalBlock)cancelBlock
{
    JCDeviceUpdateAleart *aleart = [[JCDeviceUpdateAleart alloc] initWithFrame:kSCREEN_BOUNDS
                                                                         title:title
                                                                      subtitle:subtitle
                                                                       message:message];
    aleart.comfirmBlock = comfirmBlock;
    aleart.cancelBlock = cancelBlock;
    [XY_KEYWindow addSubview:aleart];
    
    return aleart;
}

-(instancetype)initWithFrame:(CGRect)frame
                       title:(NSString*)title
                    subtitle:(NSString *)subtitle
                     message:(NSString*)message
{
    if (self = [super initWithFrame:frame]) {
        self.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.35];
        self.title = title;
        self.subtile = subtitle;
        self.message = message;
        [self setupUI];
    }
    return self;
}

- (void)setupUI
{
    UIView *contentView = [[UIView alloc] init];
    contentView.backgroundColor = COLOR_WHITE;
    contentView.layer.cornerRadius = 16;
    contentView.layer.masksToBounds = YES;
    [self addSubview:contentView];
    
  
    
    UIScrollView *scrollView = [[UIScrollView alloc] init];
    scrollView.showsHorizontalScrollIndicator = NO;
    scrollView.alwaysBounceVertical = YES;
    scrollView.bounces = NO;
    [contentView addSubview:scrollView];
    
    UILabel *titleLable = [CommonCtrl labelWithNormalText:self.title textcolor:COLOR_BLACK font:XY_S_BOLD_FONT(15) textAlignment:NSTextAlignmentCenter];
    [contentView addSubview:titleLable];
    
    UILabel *subtitleLable = [CommonCtrl labelWithNormalText:self.subtile textcolor:XY_HEX_RGB(0x262626)  font:XY_S_BOLD_FONT(14) textAlignment:NSTextAlignmentLeft];
    [contentView addSubview:subtitleLable];
    
    UILabel *messageLable = [CommonCtrl labelWithNormalText:self.message textcolor:XY_HEX_RGB(0x262626) font:XY_S_BOLD_FONT(14) textAlignment:NSTextAlignmentLeft];
    messageLable.numberOfLines = 0;
    [scrollView addSubview:messageLable];
    CGFloat scrollViewW = 270 - 40;
    CGFloat height = [self.message boundingRectWithSize:CGSizeMake(scrollViewW - 5, CGFLOAT_MAX) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:XY_S_BOLD_FONT(14)}  context:nil].size.height;
    
    UILabel *adviceLable = [CommonCtrl labelWithNormalText:@"操作前建议先连通电源" textcolor:XY_HEX_RGB(0xFB4B42) font:XY_S_BOLD_FONT(14) textAlignment:NSTextAlignmentCenter];
    [contentView addSubview:adviceLable];
    
    UIButton *cancelButton = [CommonCtrl buttonWithRoundPureBackroundColor:XY_HEX_RGB(0xF5F5F5) Title:XY_LANGUAGE_TITLE_NAMED(@"app", @"以后再说") titlecolor:XY_HEX_RGB(0x595959) cornerRaidus:6 font:XY_S_BOLD_FONT(17) target:self action:@selector(cancelButtonDidClick)];
    [contentView addSubview:cancelButton];
    
    UIButton *comfirmButton = [CommonCtrl buttonWithRoundPureBackroundColor:XY_HEX_RGB(0xF8473E) Title:XY_LANGUAGE_TITLE_NAMED(@"app", @"立即更新") titlecolor:COLOR_WHITE cornerRaidus:6 font:XY_S_BOLD_FONT(17) target:self action:@selector(comfirmButtonDidClick)];
    [contentView addSubview:comfirmButton];
    

   
    XYWeakSelf
    [contentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(270);
        make.center.equalTo(weakSelf);
        make.height.mas_equalTo(280);
    }];
    
    [titleLable mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(contentView);
        make.trailing.equalTo(contentView);
        make.top.equalTo(contentView).offset(XY_AutoW(18));
    }];
    
    [subtitleLable mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(contentView).offset(20);
        make.trailing.equalTo(contentView);
        make.top.equalTo(contentView).offset(XY_AutoW(48));
    }];
    
    [scrollView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(contentView).offset(20);
        make.height.mas_equalTo(90);
        make.width.mas_equalTo(scrollViewW);
        make.top.equalTo(subtitleLable.mas_bottom).offset(10);
    }];
 
    [messageLable mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(scrollView);
        make.width.mas_equalTo(scrollViewW - 5);
        make.top.equalTo(scrollView.mas_top);
    }];
    
    [adviceLable mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(scrollView.mas_bottom).offset(16);
        make.leading.trailing.equalTo(scrollView);
        
    }];
    
    [ cancelButton mas_makeConstraints:^(MASConstraintMaker *make) {
       make.leading.equalTo(contentView).offset(20);
       make.width.mas_equalTo(107);
       make.bottom.equalTo(contentView).offset(-16);
       make.height.mas_equalTo(44);
    }];
    
    [comfirmButton mas_makeConstraints:^(MASConstraintMaker *make) {
           make.trailing.equalTo(contentView).offset(-20);
           make.bottom.equalTo(contentView).offset(-16);
           make.height.mas_equalTo(44);
           make.width.mas_equalTo(107);
    }];
    
    scrollView.contentSize = CGSizeMake(scrollViewW, height+5);
    
}

- (void)comfirmButtonDidClick
{
    [self removeFromSuperview];
    if (self.comfirmBlock) {
        self.comfirmBlock();
    }
 
    
}

- (void)cancelButtonDidClick
{
    [self removeFromSuperview];
}

@end

@interface JCUpdataProessAleart ()

@property(nonatomic,strong) UIView *updateView;

@property(nonatomic,strong) UILabel *updateTitle;

@property(nonatomic,strong) UITextView *msgText;

@property(nonatomic,strong) UIView *retryView;

@property(nonatomic,strong) UIView *finished;

@property(nonatomic,strong) UIProgressView *progress;

@end

@implementation  JCUpdataProessAleart

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.35];
        self.frame = kSCREEN_BOUNDS;
        [self setupUI];
    }
    return self;
}

- (void)setupUI
{
    UIView *contentView = [[UIView alloc] init];
    contentView.backgroundColor = COLOR_WHITE;
    contentView.layer.cornerRadius = 16;
    contentView.layer.masksToBounds = YES;
    [self addSubview:contentView];
    self.updateView = contentView;
  
    [contentView addSubview:self.updateTitle];
    [contentView addSubview:self.msgText];
    [contentView addSubview:self.progress];
    [contentView addSubview:self.retryView];
   
    
    XYWeakSelf
    [contentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(weakSelf);
        make.size.mas_equalTo(CGSizeMake(270, 200));
      
    }];
    
    [self.updateTitle mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.equalTo(contentView);
        make.top.equalTo(contentView).offset(20);
    }];
    
    [self.msgText mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(contentView).offset(30);
        make.trailing.equalTo(contentView).offset(-30);
        make.top.equalTo(weakSelf.updateTitle.mas_bottom).offset(20);
        make.bottom.equalTo(contentView).offset(-55);
        
    }];
    
    [self.progress mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(contentView).offset(40);
        make.trailing.equalTo(contentView).offset(-40);
        make.height.mas_equalTo(6);
        make.bottom.equalTo(contentView).offset(-24);
    }];
    
    [self.retryView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.equalTo(contentView);
        make.height.mas_equalTo(50);
        make.bottom.equalTo(contentView);
    }];
    
   
    
}

- (void)retryButtonDidClick:(UIButton*)sender
{
    if (sender.tag == 2020) {
        if (self.retryBlock) {
            self.retryBlock();
        }
        [self removeFromSuperview];
    }else{
        [self removeFromSuperview];
    }
    }

// 1 准备中  2.升级中  3.错误  4.完成
- (void) onState:(UpdataProessState)state Title:(NSString*)title detalMsg:(NSString*)detaiMsg
{
   
    self.progressNum = 0;
    self.progress.progress= 0;
    self.progress.hidden = !(state== 1|| state== 2);
    self.msgText.textColor = state== 1 ? XY_HEX_RGB(0xF8473E):XY_HEX_RGB(0x262626);
    self.msgText.textAlignment = state== 1 ? NSTextAlignmentCenter:NSTextAlignmentLeft;
    self.retryView.hidden = !(state == 3);
    
    self.finished.hidden = !(state==4);
    self.updateView.hidden = !self.finished.hidden;
    
    self.updateTitle.text = title;
    
    self.msgText.text = detaiMsg;
}


- (void)setProgressNum:(CGFloat)progressNum
{
    _progressNum = progressNum;
    dispatch_async(dispatch_get_main_queue(), ^{
        self.progress.progress = progressNum;
    });
   
}


- (UILabel *)updateTitle
{
    
    if (!_updateTitle) {
        _updateTitle =[CommonCtrl labelWithNormalText:@"" textcolor:COLOR_BLACK font:XY_S_BOLD_FONT(15) textAlignment:NSTextAlignmentCenter];
        
    }
    return _updateTitle;
    
}

-(UITextView *)msgText
{
    
    if (!_msgText) {
        _msgText = [[UITextView alloc] init];
        _msgText.textColor = XY_HEX_RGB(0x262626);
        _msgText.font = XY_S_FONT(14);
        _msgText.textAlignment = NSTextAlignmentLeft;
        _msgText.editable = NO;
        _msgText.scrollEnabled = YES;
    }
       
    return _msgText;
    
}

- (UIProgressView *)progress
{
    
    if (!_progress) {
        _progress = [[UIProgressView alloc] init];
        _progress.progressTintColor =XY_HEX_RGB(0x537FB7);
        _progress.trackTintColor = XY_HEX_RGB(0xEBEBEB);
    
    }
    return _progress;

}

-(UIView *)retryView
{
    
    if (!_retryView) {
        _retryView = [self sigleLineButtonWithName:@"关闭" tag:2020];
    }
    return _retryView;
    
}

- (UIView *)finished
{
    if (!_finished) {
        UIView *finishedView = [[UIView alloc] init];
        finishedView.backgroundColor = COLOR_WHITE;
        finishedView.layer.cornerRadius = 16;
        finishedView.layer.masksToBounds = YES;
        [self addSubview:finishedView];
        _finished = finishedView;
        
        UIImageView *doneImage = [[UIImageView alloc] initWithImage:XY_IMAGE_NAMED(@"print_finished")];
        [_finished addSubview:doneImage];
        
        UILabel *doneText = [CommonCtrl labelWithNormalText:@"更新完成" textcolor:XY_HEX_RGB(0x262626) font:XY_S_FONT(14) textAlignment:NSTextAlignmentCenter];
        [_finished addSubview:doneText];
        
        UIView *doneButton = [self sigleLineButtonWithName:@"完成" tag:2021];
        [_finished addSubview:doneButton];
        XYWeakSelf
        [_finished mas_makeConstraints:^(MASConstraintMaker *make) {
            make.center.equalTo(weakSelf);
            make.size.mas_equalTo(CGSizeMake(270, 170));
        }];
        
        [doneImage mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(finishedView);
            make.top.equalTo(finishedView).offset(28);
            make.size.mas_equalTo(CGSizeMake(40, 40));
        }];
        
        [doneText mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(finishedView);
            make.top.equalTo(doneImage.mas_bottom).offset(12);
            
        }];
        
        [doneButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.trailing.equalTo(finishedView);
            make.bottom.equalTo(finishedView);
            make.height.mas_equalTo(50);
        }];
        
    }
    return _finished;
}





- (UIView*)sigleLineButtonWithName:(NSString*)name tag:(int)tag
{
    UIView *retryView = [[UIView alloc] init];
  
    UIView *line = [CommonCtrl dividingLine];
    [retryView addSubview:line];
    
    UIButton *retryButton = [CommonCtrl buttonWithTitlecolor:XY_HEX_RGB(0x537FB7) font:XY_S_FONT(17) target:self action:@selector(retryButtonDidClick:)];
    [retryButton setTitle:name forState:UIControlStateNormal];
    retryButton.tag = tag;
    [retryView addSubview:retryButton];
    
    [line mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.equalTo(retryView);
        make.height.mas_equalTo(1);
        make.top.equalTo(retryView);
    }];
    
    [retryButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.equalTo(retryView);
        make.bottom.equalTo(retryView);
        make.top.equalTo(retryView).offset(1);
    }];
    
    return retryView;
}




@end



