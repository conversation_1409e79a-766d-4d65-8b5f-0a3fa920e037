//
//  JCDownLoadAleart.m
//  Runner
//
//  Created by <PERSON> on 2021/2/19.
//

#import "JCDownLoadAleart.h"
#import "CommonCtrl.h"


@interface JCDownLoadAleart()

@property(nonatomic,copy) XYNormalBlock comfirmBlock ;

@property(nonatomic,copy) XYNormalBlock cancelBlock ;

@property(nonatomic,copy) NSString *title;

@property(nonatomic,copy) NSString *message;


@end

BOOL hasFinished;
@implementation JCDownLoadAleart

+ (instancetype)aleartWithTitle:(NSString*)title Message:(NSString*)message
            cancelBlock:(XYNormalBlock)cancelBlock
{
    hasFinished = false;
    JCDownLoadAleart *aleart = [[JCDownLoadAleart alloc] initWithFrame:kSCREEN_BOUNDS title:title message:message];
    aleart.cancelBlock = cancelBlock;
    [XY_KEYWindow addSubview:aleart];
    
    return aleart;
}

+ (instancetype)aleartWithTitle:(NSString *)title Message:(NSString *)message minimumDuration:(CGFloat)minDuration cancelBlock:(XYNormalBlock)cancelBlock {
    
    return [self aleartWithTitle:title Message:message cancelBlock:cancelBlock];
}
-(instancetype)initWithFrame:(CGRect)frame title:(NSString*)title message:(NSString*)message
{
    if (self = [super initWithFrame:frame]) {
        self.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.35];
        self.title = title;
        self.message = message;
        [self setupUI];
    }
    return self;
}

- (void)setupUI
{
    UIView *contentView = [[UIView alloc] init];
    contentView.backgroundColor = COLOR_WHITE;
    contentView.layer.cornerRadius = 16;
    contentView.layer.masksToBounds = YES;
    [self addSubview:contentView];
    
    
    UILabel *titleLable = [CommonCtrl labelWithNormalText:self.title textcolor:COLOR_BLACK font:XY_S_BOLD_FONT(16) textAlignment:NSTextAlignmentCenter];
    [contentView addSubview:titleLable];
    
    UILabel *messageLable = [CommonCtrl labelWithNormalText:self.message textcolor:XY_HEX_RGB(0x262626) font:XY_S_FONT(14) textAlignment:NSTextAlignmentCenter];
     messageLable.numberOfLines = 2;
    [contentView addSubview:messageLable];
    
    UIActivityIndicatorView *indicatorView = [[UIActivityIndicatorView alloc] initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleWhiteLarge];
    indicatorView.color = [UIColor grayColor];
    [contentView addSubview:indicatorView];
    [indicatorView startAnimating];
    
    UIView *line = [CommonCtrl dividingLine];
    [contentView addSubview:line];
    
    UIButton *cancelButton = [CommonCtrl buttonWithRoundPureBackroundColor:XY_HEX_RGB(0xF5F5F5) Title:XY_LANGUAGE_TITLE_NAMED(@"app", @"取消") titlecolor:XY_HEX_RGB(0x595959) cornerRaidus:6 font:XY_S_BOLD_FONT(17) target:self action:@selector(cancelButtonDidClick)];
   [contentView addSubview:cancelButton];
    
   
    XYWeakSelf
    [contentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(250);
        make.center.equalTo(weakSelf);
        make.height.mas_equalTo(190);
    }];
    
    [titleLable mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(contentView);
        make.trailing.equalTo(contentView);
        make.top.equalTo(contentView).offset(16);
    
    }];

 
    [messageLable mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(contentView.mas_leading).offset(16);
        make.trailing.equalTo(contentView).offset(-16);
        make.top.equalTo(titleLable.mas_bottom).offset(16);
        make.height.mas_lessThanOrEqualTo(40);
    }];
    
    [indicatorView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.mas_equalTo(contentView.centerX);
        make.centerY.mas_equalTo(contentView.centerY).offset(15);
//        make.top.equalTo(messageLable.mas_bottom).offset(20);
        make.size.mas_equalTo(CGSizeMake(45, 45));
        
    }];
    
    [line mas_makeConstraints:^(MASConstraintMaker *make) {
           make.leading.trailing.equalTo(contentView);
           make.bottom.equalTo(contentView).offset(-50);
           make.height.mas_equalTo(1);
         
    }];
    
    [ cancelButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.equalTo(contentView);
       make.bottom.equalTo(contentView);
       make.height.mas_equalTo(50);
    }];
    
}

- (void)cancelButtonDidClick
{
    if (self.cancelBlock) {
        self.cancelBlock();
    }
    [self removeFromSuperview];
}

@end
