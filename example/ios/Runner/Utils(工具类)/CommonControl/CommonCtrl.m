//
//  CommonCtrl.m
//  ChenYin
//
//  Created by <PERSON> on 2020/5/7.
//  Copyright © 2020 JCChenYin. All rights reserved.
//

#import "CommonCtrl.h"

@implementation CommonCtrl

+(UIView *)dividingLine
{
    UIView *line = [[UIView alloc] init];
    line.backgroundColor = XY_HEX_RGB(0xD1D0CD);
    return line;
}

+ (UILabel *)labelWithNormalText:(NSString *)text
         textcolor:(UIColor *)color
         font:(UIFont *)font
       textAlignment:(NSTextAlignment)textAlignment
{
    UILabel *label = [[UILabel alloc] init];
    label.text = text;
    label.textColor = color;
    label.font = font;
    label.textAlignment = textAlignment;
  
    return label;
}

+ (UIButton *)buttonWithTarget:(id)target action:(SEL)action
{
     UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
     [btn addTarget:target action:action forControlEvents:UIControlEventTouchUpInside];
     return btn;
}


+ (UIButton *)buttonWithTitlecolor:(UIColor *)color font:(UIFont *)font target:(id)target
action:(SEL)action
{
    return [self buttonOnlyWithTitle:@"" titlecolor:color font:font target:target action:action];
}


+(UIButton *)buttonOnlyWithTitle:(NSString *)title titlecolor:(UIColor *)color font:(UIFont *)font target:(id)target action:(SEL)action
{
    return [self buttonWithImage:nil title:title font:font  titlecolor:color  ControlState:UIControlStateNormal StateImage:nil StateTitleColor:nil target:target action:action];
}


+ (UIButton *)buttonOnlyWithImage:(UIImage*)image ControlState:(UIControlState)state  StateImage:( NSString *)StateImage target:(id)target action:(SEL)action
{
   
    return [self buttonWithImage:image title:nil font:nil  titlecolor:nil  ControlState:UIControlStateNormal StateImage:nil StateTitleColor:nil target:target action:action];
}

+ (UIButton *)buttonWithPureBackroundColor:(UIColor *)backgroundColor  titlecolor:(UIColor *)color font:(UIFont *)font target:(id)target action:(SEL)action
{
    return [self buttonWithRoundPureBackroundColor:backgroundColor Title:@"" titlecolor:color cornerRaidus:0 font:font target:target action:action];
}

+(UIButton *)buttonWithRoundPureBackroundColor:(UIColor *)backgroundColor Title:(NSString *)title titlecolor:(UIColor *)color cornerRaidus:(CGFloat)radius font:(UIFont *)font target:(id)target action:(SEL)action
{
    UIButton *button = [CommonCtrl buttonOnlyWithTitle:title titlecolor:color font:font target:target action:action];

    button.backgroundColor = backgroundColor;
    if (radius > 0) {
        button.layer.cornerRadius = radius;
        button.layer.masksToBounds = YES;
    }
    return button;
}

+(UIButton *)buttonWithImage:(UIImage *)imgage title:(NSString *)title font:(UIFont*)font  titlecolor:(UIColor *)color ControlState:(UIControlState)state StateImage:(UIImage *)StateImage StateTitleColor:(UIColor *)stateTitleColor target:(id)target action:(SEL)action
{
     UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
    if (imgage) {
        [btn setImage:imgage forState:UIControlStateNormal];
    }
    if (title != nil && title.length>0) {
        [btn setTitle:title forState:UIControlStateNormal];
       
    }
    if (font) {
        btn.titleLabel.font = font;
           }
    if (color) {
         [btn setTitleColor:color forState:UIControlStateNormal];
    }
    if (state != UIControlStateNormal) {
        if (StateImage) {
           [btn setImage:StateImage forState:state];
        }
        if (stateTitleColor) {
            [btn setTitleColor:color forState:state];
        }
    }
    [btn addTarget:target action:action forControlEvents:UIControlEventTouchUpInside];
    return btn;
}



//创建UITextField
+ (UITextField *)commonTextFieldWithplaceholder:(NSString *)placeholder color:(UIColor *)color font:(UIFont *)font secureTextEntry:(BOOL)secureTextEntry
{
    UITextField *textField = [[UITextField alloc] init];
    if (!STR_IS_NIL(placeholder)) {
        textField.placeholder = placeholder;
    }
    textField.tintColor = XY_HEX_RGB(0x06C45C);
    textField.textColor = color;
    textField.font = font;
    textField.secureTextEntry = secureTextEntry;
    return textField;
}

//创建UITextView
+ (UITextView *)commonTextViewtext:(NSString *)text color:(UIColor *)color font:(UIFont *)font textAlignment:(NSTextAlignment)textAlignment
{
    UITextView *textView = [[UITextView alloc] init];
    textView.text = text;
    textView.textColor = color;
    textView.textAlignment = textAlignment;
    
    textView.backgroundColor = [UIColor clearColor];
    textView.editable = NO;
    textView.scrollEnabled = NO;
    textView.dataDetectorTypes = UIDataDetectorTypeLink;
    
    return textView;
}

//创建图片
+ (UIImageView*) commonImageViewWithimage:(UIImage*)image raidus:(CGFloat)raidus
{
    UIImageView *imgView = [[UIImageView alloc] initWithImage:image];
    return imgView;
}

@end
