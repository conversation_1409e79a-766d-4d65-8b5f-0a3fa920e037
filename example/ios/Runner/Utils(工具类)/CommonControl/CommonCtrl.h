//
//  CommonCtrl.h
//  ChenYin
//
//  Created by <PERSON> on 2020/5/7.
//  Copyright © 2020 JCChenYin. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN



@interface CommonCtrl : UIView

/**
 *  @brief 分割线
 */
+ (UIView*)dividingLine;
/**
 *  @brief 普通Label
 */
+ (UILabel *)labelWithNormalText:(NSString *)text
                             textcolor:(UIColor *)color
                             font:(UIFont *)font
                    textAlignment:(NSTextAlignment)textAlignment;

/**
 *  @brief 创建按钮
 */
+ (UIButton *)buttonWithTarget:(id)target action:(SEL)action;
/**
 *  @brief 创建只有文字的按钮 （文本内容国际化）
 */
+ (UIButton *)buttonWithTitlecolor:(UIColor *)color
                               font:(UIFont *)font
                               target:(id)target
                               action:(SEL)action;

/**
 *  @brief 只有文字的按钮
 */
+ (UIButton *)buttonOnlyWithTitle:(NSString *)title
                              titlecolor:(UIColor *)color
                               font:(UIFont *)font
                               target:(id)target
                               action:(SEL)action;

/**
*  @brief 纯色背景的文字按钮
*/
+ (UIButton *)buttonWithPureBackroundColor:(UIColor*)backgroundColor titlecolor:(UIColor *)color  font:(UIFont *)font target:(id)target action:(SEL)action;

/**
 *  @brief 带圆角的纯色背景的文字按钮
 */
+ (UIButton *)buttonWithRoundPureBackroundColor:(UIColor*)backgroundColor Title:(NSString *)title
    titlecolor:(UIColor *)color cornerRaidus:(CGFloat)radius font:(UIFont *)font target:(id)target action:(SEL)action;



/**
 *  @brief 只有图片的按钮
 */
+ (UIButton *)buttonOnlyWithImage:(UIImage *)imgage ControlState:(UIControlState)state  StateImage:(nullable NSString *)StateImage target:(id)target action:(SEL)action;

/**
 *  @brief 带有图片和文字的按钮
 */
+ (UIButton *)buttonWithImage:(UIImage * _Nullable)imgage title:(NSString* _Nullable)title font:(UIFont* _Nullable)font  titlecolor:(UIColor *_Nullable)color ControlState:(UIControlState)state  StateImage:(UIImage *_Nullable)StateImage StateTitleColor:(UIColor*_Nullable)stateTitleColor target:(id)target action:(SEL)action;


//创建图片
+ (UIImageView*) commonImageViewWithimage:(UIImage*)image raidus:(CGFloat)raidus;

//创建UITextField
+ (UITextField *)commonTextFieldWithplaceholder:(NSString *)placeholder
                                    color:(UIColor *)color
                                     font:(UIFont *)font
                          secureTextEntry:(BOOL)secureTextEntry;

//创建UITextView
+ (UITextView *)commonTextViewtext:(NSString *)text
                                  color:(UIColor *)color
                                   font:(UIFont *)font
                          textAlignment:(NSTextAlignment)textAlignment;




@end

NS_ASSUME_NONNULL_END
