//
//  JCLogUtil.m
//  Runner
//
//  Created by long he on 2021/6/9.
//

#import "JCLogUtil.h"

static JCLogUtil *_hclog = nil;
@implementation JCLogUtil


+(JCLogUtil*)shareInstance{
    if (_hclog == nil) {
        _hclog = [[JCLogUtil alloc]init];
    }
    return _hclog;
}

-(BOOL)write:(NSString *)format,...
{
    @synchronized(self)
    {
        va_list args;
        if (format)
        {
            va_start(args, format);
            NSString *logMsg = [[NSString alloc] initWithFormat:format arguments:args];
            NSLog(@"%@",logMsg);
            NSFileManager * fileManager = [NSFileManager defaultManager];
//            NSString *dirctory = [NSString stringWithFormat:@"%@/log",NSHomeDirectory()];
//            NSString *file = [NSString stringWithFormat:@"%@/log/%@.log",NSHomeDirectory(),self.getCurrentTime];
            
            
            NSString *file = [NSString stringWithFormat:@"%@/%@.log",NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, true).firstObject,@"chenxiaoyin"];
//            BOOL isDirctory = YES;
//            if (![fileManager fileExistsAtPath:dirctory isDirectory:&isDirctory]) { //不存在就创建
//                if(![fileManager createDirectoryAtPath:dirctory withIntermediateDirectories:YES attributes:nil error:nil])
//                {
//                    NSLog(@"%@ 文件目录创建失败",dirctory);
//                    return NO;
//                }
//            }
            BOOL isDirctory = NO;
            if (![fileManager fileExistsAtPath:file isDirectory:&isDirctory]) {
                if(![fileManager createFileAtPath:file contents:nil attributes:nil ])
                {
                    NSLog(@"%@ 文件创建失败",file);
                    return NO;
                }
            }
            
            NSString *str = [NSString stringWithContentsOfFile:file encoding:NSUTF8StringEncoding error:nil];
            
           logMsg = [[self getCurrentTime] stringByAppendingString:[NSString stringWithFormat:@": %@ \n",logMsg]];
            logMsg = [str stringByAppendingString:logMsg];
            [logMsg writeToFile:file atomically:YES encoding:NSUTF8StringEncoding error:nil];
            va_end(args);
        }
        return YES;
    }
}

-(NSString*)getCurrentTime{
    time_t t;
    time(&t);
    NSDate *date = [NSDate dateWithTimeIntervalSince1970:t];
    
    NSDateFormatter *formatter = [[NSDateFormatter alloc]init];
    [formatter setDateFormat:@"MM-dd-HH-mm-ss"];
    NSString *strDate = [formatter stringFromDate:date];
    return strDate;
}

@end
