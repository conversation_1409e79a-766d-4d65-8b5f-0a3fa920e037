//
//  SubLBXScanViewController.h
//
//  github:https://github.com/MxABC/LBXScan
//  Created by lbxia on 15/10/21.
//  Copyright © 2015年 lbxia. All rights reserved.
//

#import "LBXScanViewController.h"
#import "LBXScanPermissions.h"
#import "StyleDIY.h"
#import "Global.h"



#pragma mark -模仿qq界面
//继承LBXScanViewController,在界面上绘制想要的按钮，提示语等
@interface QQLBXScanViewController : LBXScanViewController



/**
 @brief  扫码区域上方提示文字
 */
@property (nonatomic, strong) UILabel *topTitleLabel;
@property (nonatomic, strong) UILabel *detailDresprLabel;
#pragma mark --增加拉近/远视频界面
@property (nonatomic, assign) BOOL isVideoZoom;

#pragma mark - 底部几个功能：开启闪光灯、相册、我的二维码
//底部显示的功能项
@property (nonatomic, strong) UIView *bottomItemsView;
//相册
@property (nonatomic, strong) UIButton *btnPhoto;
//闪光灯
@property (nonatomic, strong) UIButton *btnFlash;
//我的二维码
@property (nonatomic, strong) UIButton *btnMyQR;

@property (nonatomic, assign) NSInteger scanCreateType;   //0  扫码建模。1 扫码打印  2 普通扫描  3扫描搜索 4

@property (nonatomic, copy) XYBlock scanBlock;

@property(nonatomic,copy) NSString *reminder;//顶部提示文字


@end
