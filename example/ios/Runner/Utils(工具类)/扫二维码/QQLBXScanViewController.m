//
//
//
//
//  Created by lbxia on 15/10/21.
//  Copyright © 2015年 lbxia. All rights reserved.
//

#import "QQLBXScanViewController.h"
#import "LBXScanVideoZoomView.h"
#import "JCMessageView.h"
#import "UIView+EasyExtend.h"
@interface QQLBXScanViewController ()
@property (nonatomic, strong) LBXScanVideoZoomView *zoomView;
@end

@implementation QQLBXScanViewController


- (void)viewDidLoad
{
    [super viewDidLoad];
   
    
    self.isOpenInterestRect = YES;
    //设置扫码后需要扫码图像
    self.isNeedScanImage = YES;
}


- (void)viewDidAppear:(BOOL)animated
{
    [super viewDidAppear:animated];
    [self drawBottomItems];
    [self drawTitle];
    [self.view bringSubviewToFront:_topTitleLabel];
    [self.view bringSubviewToFront:_detailDresprLabel];
    
    [LBXScanPermissions requestCameraPemissionWithResult:^(BOOL granted) {
        if (!granted) {
            // 无相机权限 做一个友好的提示
           [[JCAlertView alertWithTitle:@"无法使用相机" message:@"请在iPhone的""设置-隐私-相机""中允许访问相机" leftTitle:@"取消" rightTitle:@"设置" leftClick:^{
               
           } rightClick:^{
               [[UIApplication sharedApplication] openURL:[NSURL URLWithString:UIApplicationOpenSettingsURLString]];
           }] show];
        }
    }];
    
}

//绘制扫描区域
- (void)drawTitle
{
    if (!_detailDresprLabel)
    {
        
        CGSize sizeRetangle = CGSizeMake(kSCREEN_WIDTH - 60*2, kSCREEN_WIDTH - 60*2);
        CGFloat yMinRetangle = kSCREEN_HEIGHT / 2.0 - sizeRetangle.height/2.0 - 60;
        self.detailDresprLabel = [[UILabel alloc]init];
        _detailDresprLabel.bounds = CGRectMake(0, 0, sizeRetangle.width, 100);
        _detailDresprLabel.centerX = CGRectGetWidth(self.view.frame)/2;
        _detailDresprLabel.top = yMinRetangle + sizeRetangle.height + 20;
        _detailDresprLabel.textAlignment = NSTextAlignmentCenter;
        _detailDresprLabel.numberOfLines = 0;
        
        if(self.scanCreateType == 0){
            _detailDresprLabel.text = XY_LANGUAGE_TITLE_NAMED(@"app00263",@"扫描精臣标签包装盒上一维码即可快速创建模板");
        }else if(self.scanCreateType == 1){
            _detailDresprLabel.text = XY_LANGUAGE_TITLE_NAMED(@"",@"扫描一维码即可快速获取商品信息并加入模板进行打印");
        }else if(self.scanCreateType == 3){
            _detailDresprLabel.text = XY_LANGUAGE_TITLE_NAMED(@"",@"扫描一维码即可快速获取商品信息");
        }else if(self.scanCreateType == 4){
            _detailDresprLabel.text = XY_LANGUAGE_TITLE_NAMED(@"",@"扫描精臣标签盒上一维码");
        }else{
            _detailDresprLabel.text = XY_LANGUAGE_TITLE_NAMED(@"app00118",@"将取景框对准二维码或条码即可自动扫描");
        }
        
        if (self.reminder) {
            _detailDresprLabel.text = self.reminder;
            _detailDresprLabel.textAlignment = NSTextAlignmentCenter;
        }
        _detailDresprLabel.textColor = [UIColor whiteColor];
        [self.view addSubview:_detailDresprLabel];
    }
    if (!_topTitleLabel)
    {
        
        self.topTitleLabel = [[UILabel alloc]init];
        _topTitleLabel.bounds = CGRectMake(0, 0, 200, 100);
        _topTitleLabel.center = CGPointMake(CGRectGetWidth(self.view.frame)/2, 82);
        _topTitleLabel.textAlignment = NSTextAlignmentCenter;
        _topTitleLabel.numberOfLines = 0;
        
        if(self.scanCreateType == 0){
            _topTitleLabel.text = XY_LANGUAGE_TITLE_NAMED(@"app00262",@"扫码取模");
        }else if(self.scanCreateType == 1){
            _topTitleLabel.text = XY_LANGUAGE_TITLE_NAMED(@"",@"扫描打印");
        }else if(self.scanCreateType == 3){
            _topTitleLabel.text = XY_LANGUAGE_TITLE_NAMED(@"",@"商品库搜索");
        }else if(self.scanCreateType == 4){
            _topTitleLabel.text = XY_LANGUAGE_TITLE_NAMED(@"",@"搜索商品");
        }else{
            _topTitleLabel.text = XY_LANGUAGE_TITLE_NAMED(@"app00826",@"扫描导入");
        }
        _topTitleLabel.textColor = [UIColor whiteColor];
        [self.view addSubview:_topTitleLabel];
    }    
}

- (void)cameraInitOver
{
    if (self.isVideoZoom) {
        [self zoomView];
    }
}

- (LBXScanVideoZoomView*)zoomView
{
    if (!_zoomView)
    {
      
        CGRect frame = self.view.frame;
        
        int XRetangleLeft = self.style.xScanRetangleOffset;
        
        CGSize sizeRetangle = CGSizeMake(frame.size.width - XRetangleLeft*2, frame.size.width - XRetangleLeft*2);
        
        if (self.style.whRatio != 1)
        {
            CGFloat w = sizeRetangle.width;
            CGFloat h = w / self.style.whRatio;
            
            NSInteger hInt = (NSInteger)h;
            h  = hInt;
            
            sizeRetangle = CGSizeMake(w, h);
        }
        
        CGFloat videoMaxScale = [self.scanObj getVideoMaxScale];
        
        //扫码区域Y轴最小坐标
        CGFloat YMinRetangle = frame.size.height / 2.0 - sizeRetangle.height/2.0 - self.style.centerUpOffset;
        CGFloat YMaxRetangle = YMinRetangle + sizeRetangle.height;
        
        CGFloat zoomw = sizeRetangle.width + 40;
        _zoomView = [[LBXScanVideoZoomView alloc]initWithFrame:CGRectMake((CGRectGetWidth(self.view.frame)-zoomw)/2, YMaxRetangle + 40, zoomw, 18)];
        
        [_zoomView setMaximunValue:videoMaxScale/4];
        
        
        __weak __typeof(self) weakSelf = self;
        _zoomView.block= ^(float value)
        {            
            [weakSelf.scanObj setVideoScale:value];
        };
        [self.view addSubview:_zoomView];
                
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(tap)];
        [self.view addGestureRecognizer:tap];
    }
    
    return _zoomView;
   
}

- (void)tap
{
    _zoomView.hidden = !_zoomView.hidden;
}

- (void)drawBottomItems
{
    if (_bottomItemsView) {
        
        return;
    }
    
    self.bottomItemsView = [[UIView alloc]initWithFrame:CGRectMake(0, CGRectGetMaxY(self.view.frame)-100,
                                                                      CGRectGetWidth(self.view.frame), 100)];
    _bottomItemsView.backgroundColor = [UIColor colorWithRed:0 green:0 blue:0 alpha:0.6];
    
    [self.view addSubview:_bottomItemsView];
    
    CGSize size = CGSizeMake(65, 87);
    self.btnFlash = [[UIButton alloc]init];//闪光灯->隐藏
    _btnFlash.bounds = CGRectMake(0, 0, size.width, size.height);
    _btnFlash.center = CGPointMake(CGRectGetWidth(_bottomItemsView.frame)/2, CGRectGetHeight(_bottomItemsView.frame)/2);
 
    [_btnFlash setImage:[UIImage imageNamed:@"CodeScan.bundle/qrcode_scan_btn_flash_nor"] forState:UIControlStateNormal];
    _btnFlash.hidden = YES;
    [_btnFlash addTarget:self action:@selector(openOrCloseFlash) forControlEvents:UIControlEventTouchUpInside];
    
    self.btnPhoto = [[UIButton alloc]init];//相册->闪光灯
    
    _btnPhoto.bounds = _btnFlash.bounds;
    _btnPhoto.center = CGPointMake(CGRectGetWidth(_bottomItemsView.frame)/4, CGRectGetHeight(_bottomItemsView.frame)/2);
    [_btnPhoto setImage:[UIImage imageNamed:@"扫描_开灯_normal"] forState:UIControlStateNormal];
//    [_btnPhoto setImage:[UIImage imageNamed:@"CodeScan.bundle/qrcode_scan_btn_photo_nor"] forState:UIControlStateNormal];
//    [_btnPhoto setImage:[UIImage imageNamed:@"CodeScan.bundle/qrcode_scan_btn_photo_down"] forState:UIControlStateHighlighted];
//    [_btnPhoto addTarget:self action:@selector(openPhoto) forControlEvents:UIControlEventTouchUpInside];
    [_btnPhoto addTarget:self action:@selector(openOrCloseFlash) forControlEvents:UIControlEventTouchUpInside];
    
    self.btnMyQR = [[UIButton alloc]init];//我的二维码->退出
    _btnMyQR.bounds = _btnFlash.bounds;
    _btnMyQR.center = CGPointMake(CGRectGetWidth(_bottomItemsView.frame) * 3/4, CGRectGetHeight(_bottomItemsView.frame)/2);
//    [_btnMyQR setImage:[UIImage imageNamed:@"CodeScan.bundle/qrcode_scan_btn_myqrcode_nor"] forState:UIControlStateNormal];
//    [_btnMyQR setImage:[UIImage imageNamed:@"CodeScan.bundle/qrcode_scan_btn_myqrcode_down"] forState:UIControlStateHighlighted];
    [_btnMyQR setImage:[UIImage imageNamed:@"扫描_关闭_normal"] forState:UIControlStateNormal];
    [_btnMyQR setImage:[UIImage imageNamed:@"扫描_关闭_press"] forState:UIControlStateHighlighted];
//    [_btnMyQR addTarget:self action:@selector(myQRCode) forControlEvents:UIControlEventTouchUpInside];
    [_btnMyQR addTarget:self action:@selector(clickCancel) forControlEvents:UIControlEventTouchUpInside];
    [_bottomItemsView addSubview:_btnFlash];
    [_bottomItemsView addSubview:_btnPhoto];
    [_bottomItemsView addSubview:_btnMyQR];   
    
}

- (void)showError:(NSString*)str
{
//    [LBXAlertAction showAlertWithTitle:XY_LANGUAGE_TITLE_NAMED(@"app00032",@"提示") msg:str buttonsStatement:@[@"知道了"] chooseBlock:nil];
}

- (void)scanResultWithArray:(NSArray<LBXScanResult*>*)array
{
    if (array.count < 1)
    {
        [self popAlertMsgWithScanResult:nil];
     
        return;
    }
    
    //经测试，可以同时识别2个二维码，不能同时识别二维码和条形码
    for (LBXScanResult *result in array) {
        
        NSLog(@"scanResult:%@",result.strScanned);
    }
    
    LBXScanResult *scanResult = array[0];
    
    NSString*strResult = scanResult.strScanned;
    
    self.scanImage = scanResult.imgScanned;
    
    if (!strResult) {
        
        [self popAlertMsgWithScanResult:nil];
        
        return;
    }
    
    //震动提醒
   // [LBXScanWrapper systemVibrate];
    //声音提醒
    //[LBXScanWrapper systemSound];
    
    [self showNextVCWithScanResult:scanResult];
   
}

- (void)popAlertMsgWithScanResult:(NSString*)strResult
{
    if (!strResult) {
        
        strResult = @"识别失败";
        NSLog(@"---识别失败-%@",strResult);
    }
    
    

}

- (void)showNextVCWithScanResult:(LBXScanResult*)strResult
{
    XYWeakSelf
    [self dismissViewControllerAnimated:YES completion:^{
       weakSelf.scanBlock(strResult.strScanned);
    }];
    
   
    

}


#pragma mark -底部功能项

//开关闪光灯
- (void)openOrCloseFlash
{
    [super openOrCloseFlash];
   
    if (self.isOpenFlash)
    {
        [_btnPhoto setImage:[UIImage imageNamed:@"扫描_开灯_press"] forState:UIControlStateNormal];
    }
    else
        [_btnPhoto setImage:[UIImage imageNamed:@"扫描_开灯_normal"] forState:UIControlStateNormal];
}

- (void)clickCancel{
    [self dismissViewControllerAnimated:NO completion:^{
        if(self.scanBlock){
            self.scanBlock(@"");
        }
    }];
}





@end
