//
//  DCHUDHelper.m
//  ChenYin
//
//  Created by <PERSON> on 2020/4/21.
//  Copyright © 2020 JCChenYin. All rights reserved.
//

#import "DCHUDHelper.h"
#import <objc/runtime.h>
#import <objc/message.h>

static NSTimeInterval const minimunShowTime = 0.15f;

UIKIT_STATIC_INLINE UIFont * defaultFont() {
    UIFont *font = nil;
    if ([UIFont respondsToSelector:@selector(preferredFontForTextStyle:)]) {
        font = [UIFont preferredFontForTextStyle:UIFontTextStyleHeadline];
    } else {
        font = [UIFont systemFontOfSize:17.0f];
    }
    
    return font;
}

UIKIT_STATIC_INLINE UIImage *customImage(NSString *imageName){
    NSBundle *imageBundle =  [NSBundle bundleWithPath:[[NSBundle mainBundle] pathForResource:@"MBProgressHUD" ofType:@"bundle"]];    
    UIImage *image = [UIImage imageWithContentsOfFile:[imageBundle pathForResource:imageName ofType:@"png"]];
    if ([[UIImage class] instancesRespondToSelector:@selector(imageWithRenderingMode:)]) {
        image = [image imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
    }
    return image;
}
@implementation DCHUDHelper

#pragma mark - lazy load

+ (UIImage *)infoImage {
    static UIImage *_infoImage = nil;
    if (!_infoImage) {
        _infoImage = customImage(@"info");
    }
    return _infoImage;
}

+ (UIImage *)successImage {
    static UIImage *_successImage = nil;
    if (!_successImage) {
        _successImage = customImage(@"success");
    }
    return _successImage;
}

+ (UIImage *)errorImage {
    static UIImage *_errorImage = nil;
    if (!_errorImage) {
        _errorImage = customImage(@"error");
    }
    return _errorImage;
}

+ (UIImageView *)customImageView {
    UIImageView *imageView = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, 28, 28)];
    imageView.tintColor = [UIColor whiteColor];
    return imageView;
}



+ (void)showMessage:(NSString *)msg {
    [self showMessage:msg hideAfterDelay:[DCHUDHelper displayDurationForString:msg]];
   
}

+ (void)showMessage:(NSString *)msg hideAfterDelay:(CGFloat)seconds {
    DCHUDHelper *hud = [DCHUDHelper defaultHud];
    hud.mode = MBProgressHUDModeText;
    hud.detailsLabel.text = msg;
    hud.detailsLabel.textColor = COLOR_WHITE;
    [hud showAnimated:YES];
    [hud hideAnimated:YES afterDelay:MAX(minimunShowTime, seconds)];
}


+ (instancetype)showWithRemoveFlag:(BOOL)removeFromSuperViewOnHide {
    return [self showWithStatus:nil removeFromSuperViewOnHide:removeFromSuperViewOnHide];
}

+ (instancetype)showWithStatus:(NSString *)status removeFromSuperViewOnHide:(BOOL)removeFromSuperViewOnHide {
    DCHUDHelper *hud = [DCHUDHelper defaultHud];
    hud.removeFromSuperViewOnHide = removeFromSuperViewOnHide;
    hud.detailsLabel.text = status;
    [hud showAnimated:YES];
    
    return hud;
}

+ (instancetype)defaultHud {
    DCHUDHelper *hud = [DCHUDHelper showHUDAddedTo:XY_KEYWindow animated:YES];
    hud.detailsLabel.font = defaultFont();
    hud.contentColor = COLOR_WHITE;
    hud.bezelView.style = MBProgressHUDBackgroundStyleSolidColor;
    hud.bezelView.color = XY_RGBA(0, 0, 0, 0.6);
    hud.backgroundView.style = MBProgressHUDBackgroundStyleSolidColor;
    return hud;
}

+ (instancetype)show {
    return [self showWithStatus:@""];
}

+ (instancetype)showWithStatus:(NSString *)status {
    [self hideAllForView:XY_KEYWindow animated:YES];
    return [self showWithStatus:status removeFromSuperViewOnHide:YES];
}

- (BOOL)setStatus:(NSString *)status state:(DCHUDLoadingState)state {
    BOOL execSucc = YES;
    BOOL isComplete = YES;
    UIImage *image = nil;
    if (state == DCHUDLoadingStateLoading) {
        self.mode = MBProgressHUDModeIndeterminate;
        isComplete = NO;
    } else if (state == DCHUDLoadingStateSucc) {
        image = [DCHUDHelper successImage];
    } else if (state == DCHUDLoadingStateErr) {
        image = [DCHUDHelper errorImage];
    } else if (state == DCHUDLoadingStateWarn) {
        image = [DCHUDHelper infoImage];
    }
    
    self.detailsLabel.text = status;
    
    // cancel previous hideAfterDelay
    // if`removeFromSuperViewOnHide` == NO, it will be visible on screen again
    // 注意添加测试项, 检测API的变化
    @try {
        NSTimer *minShowTimer = [self valueForKeyPath:@"minShowTimer"];
        [minShowTimer invalidate];
        [self setValue:@(NO) forKeyPath:@"finished"];
        ((void (*)(id, SEL, BOOL))objc_msgSend)(self, NSSelectorFromString(@"showUsingAnimation:"), NO);
    } @catch (NSException *exception) {
        execSucc = NO;
    }
    
    if (isComplete) {
        self.mode = MBProgressHUDModeText;
//        UIImageView *imageView = [DCHUDHelper customImageView];
//        imageView.image = image;
//        self.customView = imageView;  //暂时不显示成功与错误图标
        [self hideAnimated:YES afterDelay:[DCHUDHelper recommendDurationForString:status]];
    }
    
    return execSucc;
}

- (void)onStatusChange:(NSString *)status {
    [self setStatus:status state:DCHUDLoadingStateLoading];
}

- (BOOL)onSuccess:(NSString *)succMsg {
    return [self setStatus:succMsg state:DCHUDLoadingStateSucc];
}

- (BOOL)onError:(NSString *)errMsg {
    return [self setStatus:errMsg state:DCHUDLoadingStateErr];
}

- (BOOL)onWarning:(NSString *)warningMsg {
    return [self setStatus:warningMsg state:DCHUDLoadingStateWarn];
}

+ (void)showImage:(UIImage *)image status:(NSString *)status {
    DCHUDHelper *hud = [DCHUDHelper defaultHud];
    hud.detailsLabel.text = status;
    if (image) {
        hud.mode = MBProgressHUDModeCustomView;
        UIImageView *imageView = [self customImageView];
        imageView.image = image;
        hud.customView = imageView;
    }
    [hud showAnimated:YES];
    [hud hideAnimated:YES afterDelay:[self recommendDurationForString:status]];
}

+ (void)showInfoWithStatus:(NSString *)status {
    [self showImage:[self infoImage] status:status];
}

+ (void)showSuccessWithStatus:(NSString *)status {
    [self showImage:[self successImage] status:status];
}

+ (void)showErrorWithStatus:(NSString *)status {
    [self showImage:[self errorImage] status:status];
}

/**
 参照`SVProgressHUD`
 
 @param string 字符串
 
 @return 字符串显示的时间
 */
+ (NSTimeInterval)displayDurationForString:(NSString*)string {
    float durationTime = (float)(string.length * 0.06 + 0.5);
    if (durationTime > 5.0) {
        durationTime = 5.0;
    }
    return durationTime;
}

+ (NSTimeInterval)recommendDurationForString:(NSString *)string {
    return MAX(minimunShowTime, [self displayDurationForString:string]);
}

+ (NSUInteger)hideAllForView:(UIView *)view animated:(BOOL)animated {
    NSMutableArray *huds = [NSMutableArray array];
    NSArray *subviews = view.subviews;
    for (UIView *aView in subviews) {
        if ([aView isKindOfClass:self]) {
            [huds addObject:aView];
            
            DCHUDHelper *hud = (DCHUDHelper *)aView;
            hud.removeFromSuperViewOnHide = YES;
            if (animated && [hud respondsToSelector:@selector(hideAfterRecommendDuration)]) { // 避免找到`MBProgressHUD`
                [hud hideAfterRecommendDuration];
            } else {
                [hud hideAnimated:animated];
            }
        }
    }
    
    return [huds count];
}

- (void)hideAfterRecommendDuration {
    [self hideAnimated:YES afterDelay:[DCHUDHelper recommendDurationForString:self.detailsLabel.text]];
}

@end

//@implementation MBProgressHUD (StyleBackword)
//
//+ (void)load {
//    static dispatch_once_t onceToken;
//    dispatch_once(&onceToken, ^{
//        Method m1 = class_getInstanceMethod([MBProgressHUD class], NSSelectorFromString(@"commonInit"));
//        Method m2 = class_getInstanceMethod([MBProgressHUD class], @selector(_commonInit));
//        method_exchangeImplementations(m1, m2);
//    });
//}
//
//- (void)_commonInit {
//    [self _commonInit];
//    self.bezelView.color = [UIColor blackColor];
//    self.contentColor = [UIColor whiteColor];
//}

//@end


