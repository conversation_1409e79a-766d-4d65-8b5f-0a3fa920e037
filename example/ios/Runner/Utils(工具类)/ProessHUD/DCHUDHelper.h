//
//  DCHUDHelper.h
//  ChenYin
//
//  Created by <PERSON> on 2020/4/21.
//  Copyright © 2020 JCChenYin. All rights reserved.
//

#import "MBProgressHUD.h"

NS_ASSUME_NONNULL_BEGIN

/**
 加载状态
 
 - DCLoadingStateLoading: 加载中
 - DCLoadingStateSucc:    加载成功
 - DCLoadingStateErr:     加载失败
 - DCLoadingStateWarn:    加载出现警告
 */
typedef NS_ENUM(NSUInteger, DCHUDLoadingState) {
    DCHUDLoadingStateLoading = 0,
    DCHUDLoadingStateSucc    = 1,
    DCHUDLoadingStateErr     = 2,
    DCHUDLoadingStateWarn    = 3,
};

@interface DCHUDHelper : MBProgressHUD

/**
 只适用于以下情形(均为同步,显示在窗口上),其他情况灵活使用
 根据文字自动计算显示时间
 <pre>
 <li>操作失败\成功后不适合带图标的提示</li>
 </pre>
 
 @param msg 显示提示信息
 */
+ (void)showMessage:(NSString *)msg;
+ (void)showMessage:(NSString *)msg hideAfterDelay:(CGFloat)seconds;


/**
 同步等待返回
 
 @return 显示HUD
 */
+ (instancetype)show;

/**
 隐藏添加到视图上的所有`hudhelper`生成的实例, 会忽略`MBProgressHUD`实例
 
 @param view 要操作的视图
 @param animated 是否需要动画
 @return 计数
 */
+ (NSUInteger)hideAllForView:(UIView *)view animated:(BOOL)animated;

/**
 显示HUD, 添加到窗口上
 
 @param removeFromSuperViewOnHide 隐藏时是否从父视图移除
 @return hud实例
 */
+ (instancetype)showWithRemoveFlag:(BOOL)removeFromSuperViewOnHide;

/**
 同步等待返回
 
 @param status 提示文字, 状态改变后调用对应方法改变文字
 
 @return HUD实例
 */
+ (instancetype)showWithStatus:(NSString *)status;

/**
 状态改变后设置文字
 
 @param status 提示文字
 */
- (void)onStatusChange:(NSString *)status;

/**
 成功完成加载时提示的信息
 
 @param succMsg 提示信息
 */
- (BOOL)onSuccess:(NSString *)succMsg;

/**
 加载失败后的提示信息
 
 @param errMsg 提示信息
 */
- (BOOL)onError:(NSString *)errMsg;

/**
 加载失败后的警告信息, 调用后`status`在一段时间后会终止
 
 @param warningMsg 警告信息
 */
- (BOOL)onWarning:(NSString *)warningMsg;

/**
 @note 不要与`showWithStatus`或`showInfoWithStatus`连用
 
 @param status 提示文字
 */
+ (void)showInfoWithStatus:(NSString*)status;

/**
 @note 不要与`showWithStatus`或`showInfoWithStatus`连用
 
 @param status 提示文字
 */
+ (void)showSuccessWithStatus:(NSString*)status;

/**
 @note 不要与`showWithStatus`或`showInfoWithStatus`连用
 
 @param status 提示文字
 */
+ (void)showErrorWithStatus:(NSString*)status;

/**
 推荐的显示时间(最短不低于0.15s)
 
 @param string 要显示的文字
 @return 推荐的显示时间
 */
+ (NSTimeInterval)recommendDurationForString:(NSString *)string;

/**
 动画隐藏, 时间由内部计算
 */
- (void)hideAfterRecommendDuration;

@end

@interface MBProgressHUD (StyleBackword)

@end

NS_ASSUME_NONNULL_END
