//
//  DCNormalHTTPRequst.h
//  ChenYin
//
//  Created by <PERSON> on 2020/4/21.
//  Copyright © 2020 JCChenYin. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "JCErrorInfo.h"
#import "DCBaseRequst.h"
#import "YTKNetworkConfig.h"

NS_ASSUME_NONNULL_BEGIN
#define DCHTTPRequest   [DCNormalHTTPRequst shareInstance]

#define DC_ACCESS_TOKENkEY @"DC_ACCESS_TOKENkEY"
#define DC_ACCESS_MALL_KENkEY @"DC_ACCESS_MALL_KENkEY"

@interface DCNormalHTTPRequst : NSObject

@property(nonatomic,assign,readonly) BOOL noNetWork;

@property(nonatomic,copy,readonly) NSString *userAgent;


/**单例*/
+(instancetype)shareInstance;

/**初始化网络设置*/
- (void)commonInit;


- (DCBaseRequst*)getWithParams:(nullable id)params
                           url:(NSString *)url
                       Success:(XYRequestCompletionBlock)success
                       failure:(XYRequestFailureBlock)failure;


- (DCBaseRequst*)getWithParams:(nullable id)params
                     ModelType:(_Nullable Class)modelType
                           url:(NSString *)path
                       Success:(XYRequestCompletionBlock)success
                       failure:(XYRequestFailureBlock)failure;


- (DCBaseRequst*)getParams:(nullable id)params
                 ModelType:(_Nullable Class)modelType
                       url:(NSString *)url
              WithoutToken:(BOOL)withOutToken
                   Success:(XYRequestCompletionBlock)success
                   failure:(XYRequestFailureBlock)failure;

                                   


- (DCBaseRequst*)postWithParams:(nullable id)params
                           Path:(NSString *)path
                           Json:(BOOL)isJson
                        Success:(XYRequestCompletionBlock)success
                        failure:(XYRequestFailureBlock)failure;


- (DCBaseRequst*)postWithParams:(nullable id)params
                      ModelType:(_Nullable Class)modelType
                           Path:(NSString *)path
                           Json:(BOOL)isJson
                        Success:(XYRequestCompletionBlock)success
                        failure:(XYRequestFailureBlock)failure;


- (DCBaseRequst*)postWithParams:(nullable id)params
                      ModelType:(_Nullable Class)modelType
                           Path:(NSString *)path
                           Json:(BOOL)isJson
                   WithoutToken:(BOOL)withOutToken
                        Success:(XYRequestCompletionBlock)success
                        failure:(XYRequestFailureBlock)failure;




- (DCBaseRequst*)putWithParams:(_Nullable id)params
                          Path:(NSString *)path
                       Success:(XYRequestCompletionBlock)success
                       failure:(XYRequestFailureBlock)failure;

-(DCBaseRequst *)downLoadFileWithParams:(id)params
                              UrlString:(NSString *)downLoadPath
                               savePath:(NSString *)savePath
                              tagString:(NSString *)tagString
                                Success:(XYRequestCompletionBlock)success
                                failure:(XYRequestFailureBlock)failure
                          downloadBlock:(XYRequestDownloadBlock)downloadBlock;


-(DCBaseRequst *)downLoadFileWithParams:(nullable id)params
                              UrlString:(NSString *_Nullable)downLoadPath
                               savePath:(NSString *_Nullable)savePath
                            ResopseHTTP:(BOOL)resopseHTTP
                                Success:(XYRequestCompletionBlock _Nullable )success
                                failure:(XYRequestFailureBlock _Nullable )failure
                          downloadBlock:(XYRequestDownloadBlock)downloadBlock;



- (DCBaseRequst*)uploadWithValues:(_Nullable id)values
                             file:(id)file
                             Path:(NSString *)path
                          FormKey:(NSString *_Nullable)formKey
                          Success:(XYRequestCompletionBlock)success
                          failure:(XYRequestFailureBlock)failure;



- (DCBaseRequst*)RequestWithMethod:(YTKRequestMethod)method
                            Params:(nullable id)params
                               url:(NSString *)url
                           Success:(XYRequestCompletionBlock)success
                          failure:(XYRequestFailureBlock)failure;



- (DCBaseRequst*)RequestWithMethod:(YTKRequestMethod)method
                            Params:(nullable id)params
                               url:(NSString *)url
                              Json:(BOOL)isJson
                           Success:(XYRequestCompletionBlock)success
                           failure:(XYRequestFailureBlock)failure;

@end

NS_ASSUME_NONNULL_END
