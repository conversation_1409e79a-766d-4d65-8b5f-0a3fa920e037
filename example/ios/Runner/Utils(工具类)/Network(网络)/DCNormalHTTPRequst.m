//
//  DCNormalHTTPRequst.m
//  ChenYin
//
//  Created by <PERSON> on 2020/4/21.
//  Copyright © 2020 JCChenYin. All rights reserved.
//

#import "DCNormalHTTPRequst.h"
#import "YTKNetworkConfig.h"
#import "AFNetworkReachabilityManager.h"
#import "NSString+JC_Device.h"
#import "JCKeychainTool.h"
#import "JCApplicationManager.h"

@interface DCNormalHTTPRequst ()




@end

@implementation DCNormalHTTPRequst

+ (void)load {
    [DCNormalHTTPRequst monitorNetworking];
}

static DCNormalHTTPRequst *_instance = nil;
+(instancetype)shareInstance
{
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        _instance = [[self alloc] init];
    });
    return _instance;
    
}


/**初始化网络设置*/
- (void)commonInit
{
    
    YTKNetworkConfig *config = [YTKNetworkConfig sharedConfig];
    config.baseUrl = JCCommonServerURL ;
    NSLog(@"++++++++++++++");
    AFNetworkReachabilityManager * manager = [AFNetworkReachabilityManager sharedManager];
    [manager startMonitoring];
//    [[RACObserve(manager, networkReachabilityStatus) takeUntil:self.rac_willDeallocSignal] subscribeNext:^(NSNumber *status) {
//         AFNetworkReachabilityStatus networkStatus = [status intValue];
//         switch (networkStatus) {
//                     case AFNetworkReachabilityStatusUnknown:
//                         break;
//                     case AFNetworkReachabilityStatusNotReachable:
//                 [DCHUDHelper showMessage:XY_LANGUAGE_TITLE_NAMED(@"app", @"没有网络")];
//                         break;
//                     case AFNetworkReachabilityStatusReachableViaWWAN:
//
//
//                         break;
//                     case AFNetworkReachabilityStatusReachableViaWiFi:
//
//                         break;
//         }
//
//    }];
    
    
    
}

- (DCBaseRequst *)getWithParams:(id)params url:(NSString *)url Success:(XYRequestCompletionBlock)success failure:(XYRequestFailureBlock)failure
{
  return  [self getWithParams:params ModelType:Nil url:url  Success:success failure:failure];
}

-(DCBaseRequst *)getWithParams:(id)params ModelType:(Class)modelType url:(NSString *)path Success:(XYRequestCompletionBlock)success failure:(XYRequestFailureBlock)failure
{
   
    return [self getParams:params ModelType:modelType url:path WithoutToken:NO Success:success failure:failure];
}

-(DCBaseRequst *)getParams:(id)params ModelType:(Class)modelType url:(NSString *)url WithoutToken:(BOOL)withOutToken Success:(XYRequestCompletionBlock)success failure:(XYRequestFailureBlock)failure
{
     
        DCBaseRequst *baseRequest = [[DCBaseRequst alloc] init];
        baseRequest.parameters = params;
    if (modelType) {
          baseRequest.modelType = modelType;
        }
        baseRequest.path = url;
        baseRequest.addRequestHeader = [self addRequestHeaderWithoutToken:NO];
    
       [self request:baseRequest type:YTKRequestMethodGET Success:success failure:failure];
         return baseRequest;
    
}



-(DCBaseRequst *)postWithParams:(id)params Path:(NSString *)path Json:(BOOL)isJson Success:(XYRequestCompletionBlock)success failure:(XYRequestFailureBlock)failure
{
    return [self postWithParams:params ModelType:Nil Path:path Json:isJson Success:success failure:failure];
}

- (DCBaseRequst *)postWithParams:(id)params ModelType:(Class)modelType Path:(NSString *)path Json:(BOOL)isJson Success:(XYRequestCompletionBlock)success failure:(XYRequestFailureBlock)failure
{
    return [self postWithParams:params ModelType:modelType Path:path Json:isJson WithoutToken:NO Success:success failure:failure];
}

- (DCBaseRequst *)postWithParams:(id)params ModelType:(Class)modelType Path:(NSString *)path Json:(BOOL)isJson WithoutToken:(BOOL)withOutToken Success:(XYRequestCompletionBlock)success failure:(XYRequestFailureBlock)failure
{
   
    
    DCBaseRequst *baseRequest = [[DCBaseRequst alloc] init];
    baseRequest.parameters = params;
    if (modelType) {
         baseRequest.modelType = modelType;
     }
   
    baseRequest.path = path;
    baseRequest.isJson = isJson;
    baseRequest.addRequestHeader = [self addRequestHeaderWithoutToken:withOutToken];
    
    [self request:baseRequest type:YTKRequestMethodPOST Success:success failure:failure];
    return baseRequest;
}


- (DCBaseRequst *)putWithParams:(id)params Path:(NSString *)path Success:(XYRequestCompletionBlock)success failure:(XYRequestFailureBlock)failure
{
//      DCBaseRequstModel *model = [[DCBaseRequstModel alloc] init];
//       model.paramters = params;
//       model.path = path;
//    DCBaseRequst *baseRequest = [[DCBaseRequst alloc] initWithRequsetModel:model];
    
    DCBaseRequst *baseRequest = [[DCBaseRequst alloc] init];
      baseRequest.parameters = params;
      baseRequest.path = path;
      baseRequest.addRequestHeader = [self addRequestHeaderWithoutToken:NO];
    
    [self request:baseRequest type:YTKRequestMethodPUT Success:success failure:failure];
       return baseRequest;
    
}


-(DCBaseRequst *)downLoadFileWithParams:(id)params UrlString:(NSString *)downLoadPath savePath:(NSString *)savePath tagString:(NSString *)tagString Success:(XYRequestCompletionBlock)success failure:(XYRequestFailureBlock)failure downloadBlock:(XYRequestDownloadBlock)downloadBlock
{

    downLoadPath = [downLoadPath stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLFragmentAllowedCharacterSet]];
    DCBaseRequst *baseRequest = [[DCBaseRequst alloc] init];
    baseRequest.parameters = params;
    baseRequest.path = downLoadPath;
    baseRequest.addRequestHeader = [self addRequestHeaderWithoutToken:NO];
    baseRequest.resumableDownloadProgressBlock = ^(NSProgress * _Nonnull progess) {
        float percent = progess.fractionCompleted;
        if (downloadBlock) {
            downloadBlock(percent);
        }
        NSArray *pressInfoArr = @[tagString,[NSString stringWithFormat:@"%f",percent],[NSString stringWithFormat:@"%d",1]];
        [[NSNotificationCenter defaultCenter] postNotificationName:JCNOTICATION_DOWNLOADPRESS_ChenYin object:pressInfoArr];
    };
    baseRequest.resumableDownloadPath = savePath;
    [self request:baseRequest type:YTKRequestMethodGET Success:success failure:failure];
    return baseRequest;
}

-(DCBaseRequst *)downLoadFileWithParams:(id)params UrlString:(NSString *)downLoadPath savePath:(NSString *)savePath ResopseHTTP:(BOOL)resopseHTTP Success:(XYRequestCompletionBlock)success failure:(XYRequestFailureBlock)failure downloadBlock:(XYRequestDownloadBlock)downloadBlock
{

    
      DCBaseRequst *baseRequest = [[DCBaseRequst alloc] init];
         baseRequest.parameters = params;
         baseRequest.path = downLoadPath;
         baseRequest.responseHTTP = resopseHTTP;
         baseRequest.addRequestHeader = [self addRequestHeaderWithoutToken:NO];
    
    baseRequest.resumableDownloadProgressBlock = ^(NSProgress * _Nonnull progess) {

    };
     baseRequest.resumableDownloadPath = savePath;
     [self request:baseRequest type:YTKRequestMethodGET Success:success failure:failure];
   
   
     return baseRequest;
}

-(DCBaseRequst *)uploadWithValues:(id)values file:(id)file Path:(NSString *)path FormKey:(NSString *)formKey Success:(XYRequestCompletionBlock)success failure:(XYRequestFailureBlock)failure
{

     DCBaseRequst *baseRequest = [[DCBaseRequst alloc] init];
        baseRequest.parameters = values;
        baseRequest.file= file;
        baseRequest.path = path;
        baseRequest.formKey = formKey;
        baseRequest.addRequestHeader = [self addRequestHeaderWithoutToken:NO];
    [self request:baseRequest type:YTKRequestMethodPOST Success:success failure:failure];
     return baseRequest;
}


- (DCBaseRequst*)RequestWithMethod:(YTKRequestMethod)method Params:(nullable id)params
    url:(NSString *)url
Success:(XYRequestCompletionBlock)success
failure:(XYRequestFailureBlock)failure
{

    
      DCBaseRequst *baseRequest = [[DCBaseRequst alloc] init];
         baseRequest.parameters = params;
         baseRequest.path = url;
         baseRequest.isJson = YES;
         baseRequest.addRequestHeader = [self addRequestHeaderWithoutToken:NO];
    
      [self request:baseRequest type:method Success:success failure:failure];
        return baseRequest;
    
}

- (DCBaseRequst*)RequestWithMethod:(YTKRequestMethod)method Params:(nullable id)params
     url:(NSString *)url  Json:(BOOL)isJson
Success:(XYRequestCompletionBlock)success
failure:(XYRequestFailureBlock)failure
{

    
      DCBaseRequst *baseRequest = [[DCBaseRequst alloc] init];
         baseRequest.parameters = params;
         baseRequest.path = url;
         baseRequest.isJson = isJson;
         baseRequest.addRequestHeader = [self addRequestHeaderWithoutToken:NO];
    
      [self request:baseRequest type:method Success:success failure:failure];
        return baseRequest;
    
}


- (void)request:(DCBaseRequst*)baseRequest  type:(YTKRequestMethod)type  Success:(XYRequestCompletionBlock)success
    failure:(XYRequestFailureBlock)failure

{
   
    if (self.noNetWork && failure) {
      NSError *interError = [NSError errorWithCode:JC_NonetworkCode localizedMsg:XY_LANGUAGE_TITLE_NAMED(@"app", @"没有网络连接")];
        failure(baseRequest,interError);
        
        return;
    }
   
    [baseRequest requestWithtype:type CompletionBlockWithSuccess:success failure:failure];

}

-(NSDictionary*)addRequestHeaderWithoutToken:(BOOL)withoutToken
{
  
    NSMutableDictionary *header = [NSMutableDictionary dictionary];
    header[@"languageCode"] = @"zh-cn";
    header [@"niimbot-user-agent"] = self.userAgent;
    if (!STR_IS_NIL(kJC_Token)) {
        header [@"Authorization"] =[NSString stringWithFormat:@"Bearer %@",kJC_Token];
    }
    
    return [header copy];
}

#pragma mark -  setter & getter
- (BOOL)noNetWork
{
     AFNetworkReachabilityManager * manager = [AFNetworkReachabilityManager sharedManager];
    return manager.networkReachabilityStatus == AFNetworkReachabilityStatusNotReachable;
}


-(NSString *)userAgent
{
    return [NSString stringWithFormat:@"AppId/%@ OS/iOS AppVersionName/%@ Model/%@ SystemVersion/%@ DeviceId/%@ referer/CP002Mobile",DC_APP_IDENTIFIER,DC_APP_VERSION,[NSString deviceName],[UIDevice currentDevice].systemVersion,[JCKeychainTool getDeviceIDInKeychain]];;
}


-(void)setAccess_token:(NSString *)access_token
{
    if (STR_IS_NIL(access_token)) {
        [[NSUserDefaults standardUserDefaults] removeObjectForKey:DC_ACCESS_TOKENkEY];
    }else{
        [[NSUserDefaults standardUserDefaults] setObject:access_token forKey:DC_ACCESS_TOKENkEY];
    }

}

- (NSString *)access_token
{
     NSString *access_token = [[NSUserDefaults standardUserDefaults] objectForKey:DC_ACCESS_TOKENkEY];
    return access_token;
}

#pragma mark - ------------- 监测网络状态 -------------
+ (void)monitorNetworking
{
    [[AFNetworkReachabilityManager sharedManager] startMonitoring];
    [[AFNetworkReachabilityManager sharedManager] setReachabilityStatusChangeBlock:^(AFNetworkReachabilityStatus status) {
        [[NSNotificationCenter defaultCenter] postNotificationName:@"monitorNetworking" object:@(status) userInfo:nil];
    }];
}


@end


