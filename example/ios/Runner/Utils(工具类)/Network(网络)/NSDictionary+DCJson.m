//
//  NSDictionary+DCJson.m
//  ChenYin
//
//  Created by <PERSON> on 2020/4/21.
//  Copyright © 2020 JCChenYin. All rights reserved.
//

#import "NSDictionary+DCJson.h"

@implementation NSDictionary (DCJson)

- (id)jsonObjectForKey:(id<NSCopying>)key
{
    id value = [self objectForKey:key];
    if ([value isKindOfClass:[NSNull class]]) {
        return nil;
    }
    return value;
}
- (NSMutableDictionary *)dc_dictionaryForKey:(id<NSCopying>)key
{
    NSDictionary *dic = (NSDictionary *)[self jsonObjectForKey:key];
    return [NSMutableDictionary dictionaryWithDictionary:dic];
}

- (NSString *)dc_stringForKey:(id<NSCopying>)key
{
    return [self jsonObjectForKey:key];
}

- (NSNumber *)numberForKey:(id<NSCopying>)key
{
    NSNumber *num = [self jsonObjectForKey:key];
    return num;
}

- (NSInteger)dc_integerForKey:(id<NSCopying>)key
{
    return [self numberForKey:key].integerValue;
}

- (BOOL)dc_boolForKey:(id<NSCopying>)key
{
    id va = [self jsonObjectForKey:key];
    if (va)
    {
        if ([va isKindOfClass:[NSString class]])
        {
            NSString *value = (NSString *)va;
            return [[value lowercaseString] isEqualToString:@"y"];
            
        }
        else
        {
            return [self numberForKey:key].boolValue;
        }
    }
    
    return NO;
    
    
}

- (CGFloat)dc_floatForKey:(id<NSCopying>)key
{
    return [self numberForKey:key].floatValue;
}

- (double)dc_doubleForKey:(id<NSCopying>)key
{
    return [self numberForKey:key].doubleValue;
}

- (NSMutableArray *)dc_arrayForKey:(id<NSCopying>)key
{
    NSArray *array = [self jsonObjectForKey:key];
    
    NSMutableArray *mutablearray = [NSMutableArray array];
    
    for (NSDictionary *dic in array) {
        NSMutableDictionary *mdic = [NSMutableDictionary dictionaryWithDictionary:dic];
        [mutablearray addObject:mdic];
    }
    
    return mutablearray;
}

-(NSString *)dc_toJSONString
{
    if ([NSJSONSerialization isValidJSONObject:self])
    {
        NSError *error = nil;
        NSData *jsonData = [NSJSONSerialization dataWithJSONObject:self options:NSJSONWritingPrettyPrinted error: &error];
        NSString *string = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
        return string;
    }
    return nil;
}

@end

@implementation NSMutableDictionary (Json)

- (void)dc_addString:(NSString *)aValue forKey:(id <NSCopying>)aKey
{
    if (aValue == nil)
    {
        [self setObject:@"" forKey:aKey];
    }
    else
    {
        [self setObject:aValue forKey:aKey];
    }
}

- (void)dc_addInteger:(NSInteger)aValue forKey:(id <NSCopying>)aKey
{
    [self dc_addNumber:[NSNumber numberWithInteger:aValue] forKey:aKey];
}


- (void)dc_addCGFloat:(CGFloat)aValue forKey:(id <NSCopying>)aKey
{
    [self dc_addNumber:[NSNumber numberWithDouble:aValue] forKey:aKey];
}


- (void)dc_addBOOL:(BOOL)aValue forKey:(id <NSCopying>)aKey
{
    //    [self addString:aValue ? @"Y" : @"N" forKey:aKey];
    [self dc_addNumber:[NSNumber numberWithBool:aValue] forKey:aKey];
}

- (void)dc_addBOOLStr:(BOOL)aValue forKey:(id <NSCopying>)aKey
{
    [self dc_addString:aValue ? @"Y" : @"N" forKey:aKey];
}


- (void)dc_addNumber:(NSNumber *)aValue forKey:(id <NSCopying>)aKey
{
    [self setObject:aValue forKey:aKey];
}

- (void)dc_addArray:(NSArray *)aValue forKey:(id <NSCopying>)aKey
{
    if (aValue == nil) {
        [self setObject:[NSNull null] forKey:aKey];
    }
    else
    {
        [self setObject:aValue forKey:aKey];
    }
}

@end
