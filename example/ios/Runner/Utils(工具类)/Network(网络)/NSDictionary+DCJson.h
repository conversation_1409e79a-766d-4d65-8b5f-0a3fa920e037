//
//  NSDictionary+DCJson.h
//  ChenYin
//
//  Created by <PERSON> on 2020/4/21.
//  Copyright © 2020 JCChenYin. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface NSDictionary (DCJson)

- (NSMutableDictionary *)dc_dictionaryForKey:(id<NSCopying>)key;

- (NSString *)dc_stringForKey:(id<NSCopying>)key;

- (NSInteger)dc_integerForKey:(id<NSCopying>)key;

- (BOOL)dc_boolForKey:(id<NSCopying>)key;

- (CGFloat)dc_floatForKey:(id<NSCopying>)key;

- (double)dc_doubleForKey:(id<NSCopying>)key;

- (NSMutableArray *)dc_arrayForKey:(id<NSCopying>)key;

- (nullable NSString *)dc_toJSONString;

@end

@interface NSMutableDictionary (Json)

- (void)dc_addString:(NSString *)aValue forKey:(id<NSCopying>)aKey;

- (void)dc_addInteger:(NSInteger)aValue forKey:(id<NSCopying>)aKey;

- (void)dc_addCGFloat:(CGFloat)aValue forKey:(id<NSCopying>)aKey;

- (void)dc_addBOOL:(BOOL)aValue forKey:(id<NSCopying>)aKey;

- (void)dc_addBOOLStr:(BOOL)aValue forKey:(id<NSCopying>)aKey;

- (void)dc_addNumber:(NSNumber *)aValue forKey:(id<NSCopying>)aKey;

- (void)dc_addArray:(NSArray *)aValue forKey:(id<NSCopying>)aKey;

@end

NS_ASSUME_NONNULL_END
