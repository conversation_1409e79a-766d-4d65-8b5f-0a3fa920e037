//
//  JCOSSRequest.m
//  ChenYin
//
//  Created by <PERSON> on 2020/5/29.
//  Copyright © 2020 JCChenYin. All rights reserved.
//

#import "JCOSSRequest.h"

@interface JCOSSRequest ()

@property(nonatomic,copy) NSString *urlPathStr;

@property(nonatomic,assign)  YTKRequestMethod httpMethod;

@property(nonatomic,copy) NSDictionary *paramtersDic;

@end

@implementation JCOSSRequest

- (instancetype)initWithUrl:(NSString *)url Method:(YTKRequestMethod)method Paramters:(NSDictionary *)paramters
{
    if (self = [super init]) {
        self.urlPathStr = url;
        self.httpMethod = method;
        self.paramtersDic =paramters;
    }
    return self;
}

-(YTKRequestMethod)requestMethod
{
    return self.httpMethod;
    
}

-(NSString *)requestUrl
{
    return self.urlPathStr;
}

-(id)requestArgument
{
    return self.paramtersDic;
}

- (YTKResponseSerializerType)responseSerializerType
{
    return YTKResponseSerializerTypeJSON;
}

- (NSDictionary *)requestHeaderFieldValueDictionary {
    return @{@"apikey": @"82b3nohbVk5NLcGKUnWcRGMleCAGMvDsF37N"};
}


@end
