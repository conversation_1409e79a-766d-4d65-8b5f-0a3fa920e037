//
//  DCBaseRequst.m
//  ChenYin
//
//  Created by <PERSON> on 2020/4/21.
//  Copyright © 2020 JCChenYin. All rights reserved.
//

#import "DCBaseRequst.h"
#import "DCBaseModel.h"
#import "YTKNetworkConfig.h"
#import <AFNetworking/AFHTTPSessionManager.h>
#import "JCMessageView.h"
#import "NSDictionary+DCJson.h"
#import "JCErrorInfo.h"
@interface DCBaseRequst ()

/**上传图片*/
@property (nonatomic, copy) UIImage *image;

/**图片数组*/
@property (nonatomic, copy) NSArray *images;

@end

@implementation DCBaseRequst

- (instancetype)init
{
   if (self = [super init]) {
        _parameters = @{};
        _path = @"";
        _isJson = YES;
        _responseHTTP = NO;
    
    }
    
    return self;
}




#pragma mark - override
- (NSString *)requestUrl {

    return [NSString stringWithFormat:@"%@", _path];
    
}

- (YTKRequestMethod)requestMethod {
    if (_image || _filePath) {
        return YTKRequestMethodPOST;
    }
    return _requestMethod;
}

- (NSTimeInterval)requestTimeoutInterval
{
    return 10.0;
}

- (NSDictionary *)requestHeaderFieldValueDictionary {
    
    return self.addRequestHeader;
    
}

- (id)requestArgument {
    return _parameters;
}

- (YTKRequestSerializerType)requestSerializerType {
    if (_isJson) {
        return YTKRequestSerializerTypeJSON;
    }
    
    return YTKRequestSerializerTypeHTTP;
}

- (YTKResponseSerializerType)responseSerializerType
{
    if (_responseHTTP) {
        return YTKResponseSerializerTypeHTTP;
    }
    return YTKResponseSerializerTypeJSON;
}


#pragma mark - Public Functions
-(void)requestWithtype:(YTKRequestMethod)type CompletionBlockWithSuccess:(XYRequestCompletionBlock)success failure:(XYRequestFailureBlock)failure {
    
    //打印出请求log

    NSMutableString *logstr = [NSMutableString string];
    if ([_parameters isKindOfClass:[NSDictionary class]]) {
        NSDictionary *params = (NSDictionary*)_parameters;
        NSArray *keys = params.allKeys;
        for (NSString *key in keys) {
            [logstr appendString:key];
            [logstr appendString:@"="];
            id obj = [params objectForKey:key];
             if ([obj isKindOfClass:[NSArray class]]){
                NSString *string = [((NSArray *)obj) componentsJoinedByString:@","];
                [logstr appendString:string];
            }else{
                [logstr appendString:[NSString stringWithFormat:@"%@",obj]];
            }
                
            
            if (![key isEqualToString:keys.lastObject]) {
                [logstr appendString:@"&"];
            }
        }
     
    }else if ([_parameters isKindOfClass:[NSArray class]]){
        NSArray *parmas = (NSArray*)_parameters;
        logstr = [[parmas componentsJoinedByString:@","] mutableCopy];
    }else{
        logstr = [[NSString stringWithFormat:@"%@",_parameters] mutableCopy];
    }
    YTKNetworkConfig *config = [YTKNetworkConfig sharedConfig];
//    if (config.baseUrl.length == 0) {
//            config.baseUrl = JCCommonServerURL;
//    }
         if (type ==YTKRequestMethodGET) {
             DLog(@"\n\nGET请求URL:\n%@/%@?%@\n",config.baseUrl, _path,logstr);
         } else if(type ==YTKRequestMethodPOST) {

             DLog(@"\n\nPOST请求URL:\n%@/%@?%@\n",config.baseUrl, _path,logstr);
             
         } else if(type ==YTKRequestMethodPUT) {
            
             DLog(@"\n\nPUT请求URL:\n%@/%@?%@\n",config.baseUrl, _path,logstr);
         }else if(type ==YTKRequestMethodPATCH) {
            
             DLog(@"\n\nPATCH请求URL:\n%@/%@?%@\n",config.baseUrl, _path,logstr);
         }

     _requestMethod = type;
    
    [self dc_startWithCompletionBlockWithSuccess:success failure:failure];
    
}


- (void)dc_startWithCompletionBlockWithSuccess:(XYRequestCompletionBlock)success failure:(XYRequestFailureBlock)failure
{
    XYWeakSelf
    [self startWithCompletionBlockWithSuccess:^(__kindof YTKBaseRequest * _Nonnull request) {
            if(request.resumableDownloadPath != nil){
                success(request, request.responseString);
                return;
            }
           
            NSString *desc = request.responseString;
            NSString *desc1 = nil;
        //  NSString *desc1 = [NSString stringWithCString:[desc cStringUsingEncoding:NSUTF8StringEncoding] encoding:NSNonLossyASCIIStringEncoding];
        if (weakSelf.requestMethod ==YTKRequestMethodGET) {
                DLog(@"\nGET(%@)请求结果:\n%@", weakSelf.path, desc1==nil?desc:desc1);
            } else if (weakSelf.requestMethod ==YTKRequestMethodPOST) {
                DLog(@"\nPOST(%@)请求结果:\n%@", weakSelf.path, desc1==nil?desc:desc1);
            } else if (weakSelf.requestMethod ==YTKRequestMethodPUT) {
                DLog(@"\nPUT(%@)请求结果:\n%@", weakSelf.path, desc1==nil?desc:desc1);
            }else if (weakSelf.requestMethod ==YTKRequestMethodPATCH) {
                DLog(@"\nPATCH(%@)请求结果:\n%@", weakSelf.path, desc1==nil?desc:desc1);
            }
        
        
        JSONModelError* err = nil;
        DCBaseModel *obj = [[DCBaseModel alloc] initWithString:request.responseString error:&err];
        
        if (obj && !err) {
           id model = obj.data;
           
            if ([model isKindOfClass:[NSDictionary class]]) {
                if(weakSelf.modelType){
                    NSError *jsonModelError;
                   model = [[weakSelf.modelType alloc] initWithDictionary:(NSDictionary*)model error:&jsonModelError];
                    if (jsonModelError) {
                         NSLog(@"jsonModelError---%@",jsonModelError);
                    }
                   
                   
                }
            } else if ([model isKindOfClass:[NSArray class]]) {
                NSMutableArray *arr = [NSMutableArray arrayWithCapacity:((NSArray *)model).count];
                for (id itemModel in model) {
                    if([itemModel isKindOfClass:[NSDictionary class]] && weakSelf.modelType){
                        NSDictionary *dic = itemModel;
                        JSONModelError *arrError = nil;
                        DCBaseModel *m = [[weakSelf.modelType alloc] initWithString:[dic dc_toJSONString] error:&arrError];
                        if(m && !arrError){
                            [arr addObject:m];
                        }
                    }else{
                        [arr addObject:itemModel];
                    }
                }
                model = arr;
            }
            if ([@"200" isEqualToString:obj.status_code] || [@"100000" isEqualToString:obj.code] ||[@"200" isEqualToString:obj.code]|| [obj.code isEqualToString:@"1"]) {  //成功
               if (  model == nil || [model isKindOfClass:[NSString class]]) {
                   DLog(@"数据未转换成模型返回----%@",weakSelf.modelType);
                   model = obj;
                }
                 success(request, model);
                       
            }else{
                NSError *requestError = [NSError errorWithCode:[obj.status_code integerValue] localizedMsg:obj.message];
                failure(request, requestError);
            }
           
        }
 
        } failure:^(__kindof YTKBaseRequest *request) {
            NSLog(@"！！！⚠️——fialure---code=%@-----%ld----response-%@",request,(long)request.responseStatusCode,request.responseString);
            NSInteger statusCode = request.responseStatusCode;
            NSError *error ;
            if ([request.responseObject isKindOfClass:[NSDictionary class]]) {
                NSDictionary *requestDic = (NSDictionary*)request.responseObject;
                if ([requestDic.allKeys containsObject:@"message"]) {
                    if (statusCode == 401){
                        DCNCPost2p(JCLoginOutNotification, @"您的登录已过期，需要重新登录");
                       
                    }else if (statusCode == 403){
                        DCNCPost2p(JCLoginOutNotification, request.responseObject[@"message"]);
                    }
                    error = [NSError errorWithCode:statusCode localizedMsg:request.responseObject[@"message"]];
                }else{
                    error = request.error;
                }
            }else{
                error = request.error;
            }
         
              failure(request,error);
        }];
    
}




- (AFConstructingBlock)constructingBodyBlock {
    XYWeakSelf;
    if (_image) {
        return ^(id<AFMultipartFormData> formData) {
            NSData *data = UIImagePNGRepresentation(weakSelf.image);
            NSString *name = @"image.png";
            NSString *type = @"jpeg/png";
            [formData appendPartWithFileData:data name:weakSelf.formKey fileName:name mimeType:type];
        };
    }
    if (_filePath) {
        return ^(id<AFMultipartFormData> formData) {
            NSData *data = [NSData dataWithContentsOfFile:weakSelf.filePath];
            NSString *name = [weakSelf.filePath lastPathComponent];
            NSString *type = [weakSelf.filePath pathExtension];
            [formData appendPartWithFileData:data name:weakSelf.formKey fileName:name mimeType:type];
        };
    }
    if (_images.count) {
        return ^(id<AFMultipartFormData> formData) {
            for (NSInteger i = 0; i < weakSelf.images.count; i ++) {
                UIImage * upImage = [weakSelf.images objectAtIndex:i];
                NSData *data = UIImagePNGRepresentation(upImage);
                NSString *name = @"images.png";
                NSString *type = @"jpeg/png";
                [formData appendPartWithFileData:data name:weakSelf.formKey fileName:name mimeType:type];
            }
            
        };
    }
    return [super constructingBodyBlock];
}



- (void)setFile:(id)file
{
    _file = file;
    
    if ([_file isKindOfClass:[NSArray class]]) {
               _images = _file;
        }
    if([_file isKindOfClass:[UIImage class]]){
               _image = _file;
      }
     if ([_file isKindOfClass:[NSString class]]) {
               _filePath = _file;
       }
}

@end



