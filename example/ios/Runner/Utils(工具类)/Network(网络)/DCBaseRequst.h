//
//  DCBaseRequst.h
//  ChenYin
//
//  Created by <PERSON> on 2020/4/21.
//  Copyright © 2020 JCChenYin. All rights reserved.
//

#import "YTKRequest.h"

NS_ASSUME_NONNULL_BEGIN



typedef void(^XYRequestCompletionBlock)(__kindof YTKBaseRequest *request, id responseObject);
typedef void (^XYRequestFailureBlock)( YTKBaseRequest *_Nullable request, NSError * _Nullable error);
typedef void(^XYRequestDownloadBlock)(double percent);


@interface DCBaseRequst : YTKRequest

/**请求参数*/
@property (nonatomic, copy) id parameters;

/**url路径*/
@property (nonatomic, copy) NSString *path;

/**上传对应key*/
@property (nonatomic, copy) NSString *formKey;

/**数据对应的模型*/
@property (nonatomic, assign) Class modelType;

/**上传文件*/
@property(nonatomic,copy) id file;

/**HTTP请求方式*/
@property (nonatomic, assign) YTKRequestMethod requestMethod;

/**下载对应的本地路径*/
@property (nonatomic, copy) NSString *filePath;

/**请求参数格式 YES json NO 表单*/
@property(nonatomic,assign) BOOL isJson;

/**数据方式格式 YES 超文本文本或其他方式 NO json*/
@property(nonatomic,assign) BOOL responseHTTP;

/**请求头部添加的参数*/
@property(nonatomic,copy) NSDictionary *addRequestHeader;




-(void)requestWithtype:(YTKRequestMethod)type  CompletionBlockWithSuccess:(XYRequestCompletionBlock)success failure:(XYRequestFailureBlock)failure;



@end

NS_ASSUME_NONNULL_END
