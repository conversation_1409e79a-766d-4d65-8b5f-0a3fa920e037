//
//  DeviceUpdateUtil.h
//  Runner
//
//  Created by <PERSON> on 2021/1/22.
//

#import <Foundation/Foundation.h>
#import "JCDeviceFirmwareRemoteModel.h"

NS_ASSUME_NONNULL_BEGIN



@interface DeviceUpdateUtil : NSObject

@property(nonatomic,copy) NSString *hardware;

@property(nonatomic,copy) NSString *firmware;

@property(nonatomic,copy) NSString *gears;

@property(nonatomic,copy,nullable) JCDeviceFirmwareRemoteModel *updateModel;

+(instancetype)sharedInstance;

- (void)requestUpdateMsgDeviceReconnect:(BOOL)reconnect Handle:(XYBlock)deviceBlock Success:(XYRequestCompletionBlock)success failure:(XYRequestFailureBlock)failure;

- (void)beginUpdateFinishHandle:(XYNormalBlock)block;

- (void)updateFinishedWithDevice:(NSString*)device;


/**损坏固件更新*/
- (void)defectivePrinterUpdate;


@end

NS_ASSUME_NONNULL_END
