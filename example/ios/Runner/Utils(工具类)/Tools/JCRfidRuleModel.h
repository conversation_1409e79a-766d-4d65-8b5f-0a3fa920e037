//
//  JCRfidRuleModel.h
//  XYFrameWork
//
//  Created by j c on 2019/9/6.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

@interface JCPrintStatusModel : JSONModel <NSCoding>
@property (nonatomic, strong) NSString <Optional> *status;//RFID功能状态:0-启用
@property (nonatomic, strong) NSString <Optional> *start_time;//禁用开始时间
@property (nonatomic, strong) NSString <Optional> *end_time;//禁用结束时间
@end

@protocol JCRfidRuleModel <NSObject>


@end
@interface JCRfidRuleModel : JSONModel <NSCoding>
@property (nonatomic, strong) NSString <Optional> *bluetooth_number;//硬件蓝牙编号
@property (nonatomic, strong) NSArray <JCRfidRuleModel,Optional> *print_strategy;
@property (nonatomic, strong) NSArray <JCRfidRuleModel,Optional> *print_strategy_ribbon;
@property (nonatomic, strong) NSString <Optional> *xyid;//打印策略id  1.最低浓度打印  2.禁止批量打印 3.禁止打印  4.不做限制
@property (nonatomic, strong) NSString <Optional> *typeName;//策略名称
@property (nonatomic, strong) NSString <Optional> *name;//策略名称
@property (nonatomic, strong) NSString <Optional> *is_open;//是否开启:0-关闭,1
@property (nonatomic, strong) NSString <Optional> *option;//最低浓度打印时的浓度值
@property (nonatomic, strong) JCPrintStatusModel <Optional> *rfid_status;
@property (nonatomic, strong) NSString <Optional> *status;//RFID功能状态:0-启用
@end



@interface JCRuleModel : JSONModel <NSCoding>

@property (nonatomic, strong) NSString <Optional> *xyid;
@property (nonatomic, strong) NSString <Optional> *bluetooth_number;
@property (nonatomic, strong) NSString <Optional> *start_time;
@property (nonatomic, strong) NSString <Optional> *end_time;
@property (nonatomic, strong) NSString <Optional> *status;
@property (nonatomic, strong) NSString <Optional> *rullJosnString;

-(void)toJCRuleModelWith:(JCRfidRuleModel *)model;
-(JCRfidRuleModel *)reversalSelfToJCRfidRuleModel;

@end
