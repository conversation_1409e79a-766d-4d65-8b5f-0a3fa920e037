//
//  JCAntiLostUtil.m
//  Runner
//
//  Created by <PERSON> on 2021/3/11.
//

#import "JCAntiLostUtil.h"
#import "JCAPI_Antilost.h"
#import "JCApplicationManager.h"
//#import "AppEventChannel.h"
#import "JCBluetoothManager.h"
#import "JCMessageView.h"
#import "LocalNotificationUtil.h"
//#import "AppMethodChannel.h"
#import <MediaPlayer/MediaPlayer.h>
#import "JCMessageView.h"
#import "JCDeviceFirmwareRemoteModel.h"
#import "DeviceUpdateUtil4AntiLost.h"
#import "JCDeviceUpdateAleart.h"
#import "JCLogUtil.h"

@interface JCAntiLostUtil() <AVAudioPlayerDelegate>
@property (nonatomic, assign) float originVolume;
@property (nonatomic, strong) AVAudioPlayer *player;
@property (nonatomic, strong) JCAlertView *alertView;
@property (nonatomic, strong) NSString *latestDisconnectSerialNo; /// 保存最新断开设备得序列号
@property (nonatomic, assign) UIBackgroundTaskIdentifier backgroundTaskIdentifier;
@property (nonatomic, strong) DeviceUpdateUtil4AntiLost *updateUtil;
@property(nonatomic,strong)AVAudioSession *audioSession;
@end

@implementation JCAntiLostUtil

static JCAntiLostUtil *antiLostUtil = nil;


+ (JCAntiLostUtil *)shareInstance {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        antiLostUtil = [[JCAntiLostUtil alloc] init];
    });
    return antiLostUtil;
}

- (id)init {
    self = [super init];
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(volumeChangeNotification:)
                                                 name:@"AVSystemController_SystemVolumeDidChangeNotification" object:nil];
    self.backgroundTaskIdentifier = UIBackgroundTaskInvalid;
    self.originVolume = -1;
    [self audioSession];
    return self;
}

//系统音量回调
- (void)volumeChangeNotification:(NSNotification *)noti {
    float volume = [[[noti userInfo]
                     objectForKey:@"AVSystemController_AudioVolumeNotificationParameter"] floatValue];
    NSLog(@"系统音量:%f", volume);
//    if (volume < 0.3 && _player != nil) {
//        [_player stop];
//        _player = nil;
//    }
}

static inline void connectEventData(NSString *serialNo, NSString *identifier, BOOL connected){
    NSDictionary *data = @{
        @"serialNo":serialNo,
        @"anti_Connected":[NSNumber numberWithBool:connected],
        @"identifier":identifier?:@"",
    };
//    [[AppEventChannel shareInstance] eventData:data];

    [[NSNotificationCenter defaultCenter] postNotificationName:JCAntilostStateNotification object:@{@"success":[NSNumber numberWithBool:connected],@"serialNo":serialNo}];
    
};

- (void)handleWithReuslt:(FlutterResult)result
               arguments:(NSDictionary *)arguments {
    NSString *fucName = arguments[@"fucName"];
    NSLog(@"flutter antilost 调用原生:%@",fucName);
    XYWeakSelf;
    if ([fucName isEqualToString:@"connect"]) {
        NSString *serialNo = arguments[@"serialNo"];
        __block NSString *identifier = nil;
        NSLog(@"开始连接：%@", serialNo);
        [JCAPI_Antilost initWith:serialNo withSuccessBlock:^(NSString * _Nonnull string) {
            NSLog(@"连接成功：%@ %@", serialNo, string);
            identifier = string;
            if ([string length] > 0) {
                [weakSelf observerAntiLostStateWithName:serialNo identifier:string];
                connectEventData(serialNo, string, YES);
            }
        } withErrorBlock:^(int errorCode) {
            NSLog(@"连接失败：%@", @(errorCode));
            if (identifier != nil) {
                [weakSelf handleDeviceDisconnect:serialNo identifier:identifier];
                identifier = nil;
            }
        }];
      
    } else if ([fucName isEqualToString:@"disconnect"]) {
        NSString *serialNo = arguments[@"serialNo"];
        NSString *identifier = arguments[@"identifier"];
        [JCAPI_Antilost closeWithIdentifier:identifier withComplete:^{
            NSLog(@"主动断开连接：%@ %@", serialNo, identifier);
        }];
        
    } else if ([fucName isEqualToString:@"bindCancelled"]) {
        NSLog(@"绑定手动取消");
        [JCAPI_Antilost cancelDiscover];
    
    } else if ([fucName isEqualToString:@"bind"]) {
        NSArray *serialNos = arguments[@"serialNos"] ?: @[];
      __block NSTimer *timer = [NSTimer scheduledTimerWithTimeInterval:45 repeats:false block:^(NSTimer * _Nonnull timer) {
            NSDictionary *data = @{@"bindStatus":@NO,@"errorMsg":@"未发现未绑定的防丢器，请重试"};
//            [[AppEventChannel shareInstance] eventData:data];
        }];
        [JCAPI_Antilost startDiscoverWithFilters:@[@"Q2",@"JcTag"]
                                     withTimeOut:24.0f//超时时间不生效,超时后没有回调,需要自己写定时器
                                    withComplete:^(NSString * _Nonnull name) {
            NSLog(@"绑定时搜索到的设备：%@", name);
            // 扫描到已绑定的序列号，则不再进行冗余的绑定动作
            if (([name hasPrefix:@"Q2-"] || [name hasPrefix:@"JcTag-"])&& ![serialNos containsObject:name]) {
                [JCAPI_Antilost initWith:name withSuccessBlock:^(NSString * _Nonnull string) {
                    NSLog(@"连接到设备，准备绑定：%@", name);
                    [JCAPI_Antilost bind:[JCApplicationManager shareInstance].user.userId
                           withDevieceId:string
                            withComplete:^(NSDictionary * _Nonnull dictionary) {
                        int statusCode = [dictionary[@"statusCode"] intValue];
                        NSLog(@"绑定结果 serialNo:%@ uid:%@ result:%@",
                              name,
                              [JCApplicationManager shareInstance].user.userId,
                              dictionary);
                        
                        if (statusCode == 0) {
                           
                            //成功之后去掉
                            [timer invalidate];
                            timer = nil;
                            [JCAPI_Antilost cancelDiscover];
                            
//                            [DCHUDHelper showMessage:@"绑定成功"];
                            [weakSelf observerAntiLostStateWithName:name identifier:string];
                            
                            NSDictionary *data = @{@"bindStatus":@YES,@"serialNo":name,@"identifier":string};
//                            [[AppEventChannel shareInstance] eventData:data];
                        } else {
                            NSString *msg = @"绑定失败";
                            if (statusCode == 401 ||
                                statusCode == 404){
//                                msg = @"已绑定其他用户";
                            } else if (statusCode == 302){
//                                msg = @"连接断开";
                            } else if (statusCode == 303){
                                msg = @"未发现未绑定的防丢器，请重试";
                            } else if (statusCode == 307){
//                                msg = @"参数错误";
                            }
//                            [DCHUDHelper showMessage:[NSString stringWithFormat:@"%@ %@", name, msg]
//                                      hideAfterDelay:3];
                          
                            
                            [JCAPI_Antilost closeWithIdentifier:string withComplete:^{
                                NSLog(@"绑定失败后，断开连接 %@ %@", name, msg);
                            }];
                        }
                    }];
                } withErrorBlock:^(int errorCode) {
                    NSLog(@"连接到设备失败 serialNo:%@ errorCode:%@", name, @(errorCode));
//                    [DCHUDHelper showMessage:[NSString stringWithFormat:@"防丢器“%@”绑定失败", name]
//                              hideAfterDelay:3];
                }];
               
            }
        }];
      
    } else if([fucName isEqualToString:@"setAntiLost"]) {
        int type = [arguments[@"type"] intValue];
        NSString *identifier = arguments[@"identifier"];
        [JCAPI_Antilost getDeviceInfo:type withIdentifier:identifier withComplete:^(NSDictionary * _Nonnull dictionary) {
            NSLog(@"发送防丢器指令:%@ 结果:%@", @(type), dictionary);
            //@"0.成功 -1.失败 -2.忙碌
            int statusCode = [dictionary[@"statusCode"] intValue];
            
            if (result) {
                result(@{@"statusCode":@(statusCode)});
            }
        }];
        
    } else if ([fucName isEqualToString:@"unbind"]) {
        NSString *identifier = arguments[@"identifier"];
        [JCAPI_Antilost getDeviceInfo:7
                       withIdentifier:identifier
                         withComplete:^(NSDictionary * _Nonnull dictionary) {
            NSLog(@"iOS 解绑结果：%@，%@", identifier, dictionary);
            
            /// 解绑后断开连接
            [JCAPI_Antilost closeWithIdentifier:identifier
                                   withComplete:^{
                NSLog(@"iOS 解绑后连接已断开 %@", identifier);
            }];
            
            if (result) {
                if (dictionary) {
                    NSDictionary *data = @{
                        @"success":[NSNumber numberWithBool:
                                    [dictionary[@"statusCode"] integerValue] == 0]
                    };
                    result(data);
                } else {
                    result(@{@"success":[NSNumber numberWithBool:NO]});
                }
            }
        }];
        
    } else if ([fucName isEqualToString:@"queryDeviceInfo"]) {
        NSString *identifier = arguments[@"identifier"];
        [JCAPI_Antilost getDeviceInfo:17
                       withIdentifier:identifier
                         withComplete:^(NSDictionary * _Nonnull dictionary) {
            NSLog(@"iOS 获取设备详情 %@ %@", identifier, dictionary);
            if (result) {
                if ([dictionary isKindOfClass:[NSDictionary class]]
                    && [dictionary[@"statusCode"] integerValue] == 0
                    && [dictionary[@"result"] isKindOfClass:[NSDictionary class]]) {
                    NSDictionary *data = @{
                        @"serialNumber": dictionary[@"result"][@"4"],
                        @"macAddress": dictionary[@"result"][@"3"],
                        @"hardwareVersion": dictionary[@"result"][@"1"],
                        @"softwareVersion": dictionary[@"result"][@"2"],
                    };
                    result(data);
                } else {
                    result(@{});
                }
            }
        }];
    } else if ([fucName isEqualToString:@"setDeviceBellStatus"]) {
        NSString *identifier = arguments[@"identifier"];
        NSNumber *status = arguments[@"status"];
        [JCAPI_Antilost setAlarmMode:[status boolValue] ? 1/*1-超出距离设备报警*/ : 2/*-超出距离设备不报警*/
                       withDevieceId:identifier
                        withComplete:^(NSDictionary * _Nonnull dictionary) {
            NSLog(@"iOS 设置设备响铃%@ %@ %@", identifier, status, dictionary);
            if (result) {
                if ([dictionary isKindOfClass:[NSDictionary class]]
                    && [dictionary[@"statusCode"] integerValue] == 0) {
                    NSDictionary *data = @{
                        @"success":[NSNumber numberWithBool:
                                    [dictionary[@"statusCode"] integerValue] == 0]
                    };
                    result(data);
                } else {
                    result(@{@"success":[NSNumber numberWithBool:NO]});
                }
            }
        }];
    } else if ([fucName isEqualToString:@"firmwareUpgrade"]) {
        NSString *identifier = arguments[@"identifier"];
        NSDictionary *deviceInfo = arguments[@"deviceInfo"];
        NSDictionary *info = arguments[@"info"];
        
        // TODO: 待原生对接
        NSLog(@"iOS 防丢器更新信息 %@ %@", identifier, info);
        if (result) {
            JCDeviceFirmwareRemoteModel *model = [[JCDeviceFirmwareRemoteModel alloc] initWithDictionary:info error:nil];
            self.updateUtil.updateModel = model;
            self.updateUtil.identifier = identifier;
            self.updateUtil.hardware = deviceInfo[@"hardwareVersion"];
            self.updateUtil.firmware = deviceInfo[@"softwareVersion"];
            
            [JCDeviceUpdateAleart aleartWithTitle:@"固件可更新"
                                         subtitle:[NSString stringWithFormat:@"最新固件版本V%@更新内容:",self.updateUtil.updateModel.version]
                                          Message:UN_NIL(self.updateUtil.updateModel.updateInfo)
                                     ComfirmBlock:^{
                [weakSelf.updateUtil beginUpdateFinishHandle:^{
                    result(@{@"success":[NSNumber numberWithBool:YES]});
                }];
            } cancelBlock:^{
                result(@{@"success":[NSNumber numberWithBool:NO]});
            }];
        }
    } else if ([fucName isEqualToString:@"checkBluetooth"]) {
        // 获取蓝牙开关状态
        BOOL isOpen = [[JCBluetoothManager sharedInstance] getDeviceBluetoothState];
        // NSLog(@"iOS 蓝牙开关状态 %@", @(isOpen));
        BOOL ignoreToast = [arguments[@"ignoreToast"] boolValue];
        if (!isOpen && !ignoreToast) {
            [DCHUDHelper showMessage:@"请打开系统蓝牙后进行操作"
                      hideAfterDelay:3];
        }
        if (result) {
            result(isOpen ? @2 : @1);
        }
    } else if ([fucName isEqualToString:@"ringTheBell"]) {
        NSInteger bellIndex = [arguments[@"index"] integerValue];
        [self ringTheBell:bellIndex
                     once:YES
                volumeMax:NO];
    } else if ([fucName isEqualToString:@"registerAPN"]){
        [LocalNotificationUtil registerAPNWithCompletion:^(BOOL success) {
            result(success ? @1:@0);
        }];
    } else if ([fucName isEqualToString:@"goToSettingAPNS"]){
        NSURL * url = [NSURL URLWithString:UIApplicationOpenSettingsURLString];
        dispatch_async(dispatch_get_main_queue(), ^{
            if ([[UIApplication sharedApplication] canOpenURL:url]) {
                [[UIApplication sharedApplication] openURL:url];
            }
        });
        
    }
}

- (DeviceUpdateUtil4AntiLost *)updateUtil {
    if (!_updateUtil) {
        _updateUtil = [[DeviceUpdateUtil4AntiLost alloc] init];
    }
    return _updateUtil;
}

- (void)audioPlayerDidFinishPlaying:(AVAudioPlayer *)player successfully:(BOOL)flag {
    [self restoreSystemVolume];
}

- (void)stopPlayer {
    if (_player != nil) {
        [_player stop];
        _player = nil;
        
        [self restoreSystemVolume];
    }
}

- (void)restoreSystemVolume {
    __weak typeof(self) weakSelf = self;
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        if (weakSelf.originVolume > -1) {
            MPMusicPlayerController *mpVC = [MPMusicPlayerController applicationMusicPlayer];
            mpVC.volume = weakSelf.originVolume;
            weakSelf.originVolume = -1;
        }
    });
}

- (void)observerAntiLostStateWithName:(NSString *)serialNo
                           identifier:(NSString *)identifier {
    NSLog(@"开始监听状态：%@ %@", serialNo, identifier);
    XYWeakSelf;
    
    /// 重连成功，关闭响铃
    if ([_latestDisconnectSerialNo isEqualToString:serialNo]) {
        [self stopPlayer];
    }
    
    /// 连接成功，同步警报开关
//    [[AppMethodChannel shareInstance].methodChannel invokeMethod:@"queryAntiLoseDeviceSettings"
//                                                       arguments:@{ @"uniqueNo":serialNo }
//                                                          result:^(NSDictionary * _Nullable result) {
//        [JCAPI_Antilost setAlarmMode:[result[@"deviceBellStatus"] boolValue] ? 1/*1-超出距离设备报警*/ : 2/*-超出距离设备不报警*/
//                       withDevieceId:identifier
//                        withComplete:^(NSDictionary * _Nonnull dictionary) {
//            NSLog(@"iOS 连上自动设置设备响铃 - %@ %@ %@", identifier, result[@"deviceBellStatus"], dictionary);
//        }];
//    }];
    
    [JCAPI_Antilost onCalled:identifier withComplete:^{
        NSLog(@"收到设备呼叫");
        [weakSelf queryAntiLostDisplayName:serialNo
                                  callback:^(NSString *displayName) {
            NSString *msg = [NSString stringWithFormat:@"防丢器“%@”正在呼叫你", displayName];
            [weakSelf alertCallMsg:msg
                          serialNo:serialNo];
        }];
    }];
    
    [JCAPI_Antilost readSSIWithIdentifier:identifier withComplete:^(int errorCode) {
        NSDictionary *data = @{@"observerAnti":serialNo,@"identifier":identifier,@"signalCode":@(errorCode),};
//        [[AppEventChannel shareInstance] eventData:data];
    }];
    
    [JCAPI_Antilost onPassiveDisconnect:identifier withComplete:^{
        NSLog(@"连接断开：%@",identifier);
        [weakSelf handleDeviceDisconnect:serialNo identifier:identifier];
    }];
    
    [JCAPI_Antilost readSingleDevicePowerWithIdentifier:identifier withComplete:^(NSDictionary * _Nonnull dictionary) {
        NSLog(@"电池电量：%@",dictionary);
        // @{@"status":@"电池充电状态-1.充电中  2.充满  3.未充电"，@“power”:@"电量-1~4 电量等级"}
        if (dictionary && [dictionary.allKeys containsObject:@"status"]) {
            NSInteger status = [dictionary[@"status"] integerValue];
            NSInteger power = [dictionary[@"power"] integerValue];
            
            NSDictionary *data = @{
                @"observerAnti": serialNo,
                @"identifier": identifier,
                @"devicePower": @(power),
                @"status": @(status)
            };
//            [[AppEventChannel shareInstance] eventData:data];
            
            [weakSelf queryAntiLostDisplayName:serialNo
                                      callback:^(NSString *displayName) {
                if (power <= 1) {
                    [[JCAlertView alertWithTitle:@"提示"
                                         message:[NSString stringWithFormat:@"防丢器“%@”电量低，请充电", displayName ?: @""]
                                    confirmTitle:@"好的"
                                    confirmClick:^{
                    }] show];
                }
                
                if (status == 2) {
                    [[JCAlertView alertWithTitle:@"提示"
                                         message:[NSString stringWithFormat:@"防丢器“%@”已充满电", displayName ?: @""]
                                    confirmTitle:@"好的"
                                    confirmClick:^{
                    }] show];
                }
            }];
        }
      
    }];
}

- (void)handleDeviceDisconnect:(NSString *)serialNo
                    identifier:(NSString *)identifier {
    self.latestDisconnectSerialNo = serialNo;
    connectEventData(serialNo, identifier, NO);
    
    [self queryAntiLostDisplayName:serialNo
                          callback:^(NSString *displayName) {
        NSString *msg = [NSString stringWithFormat:@"防丢器“%@”已断开", displayName ?: @""];
        [self alertDisconnectMsg:msg
                        serialNo:serialNo];
    }];
    
    /// 申请一段后台运行时间用于尝试自动重连
    if ([UIApplication sharedApplication].applicationState == UIApplicationStateBackground) {
        [self beginBackgroundTask];
    }
}

- (void)queryAntiLostDisplayName:(NSString *)serialNo
                        callback:(void (^)(NSString *))callback {
//    [[AppMethodChannel shareInstance].methodChannel invokeMethod:@"queryAntiLostDisplayName"
//                                                       arguments:@{ @"uniqueNo":serialNo }
//                                                          result:^(NSDictionary * _Nullable result) {
//        if ([result[@"displayName"] isKindOfClass:[NSString class]]) {
//            callback(result[@"displayName"]);
//        } else {
//            callback(serialNo);
//        }
//    }];
}

/// 设备主动呼叫，忽略勿扰模式
- (void)alertCallMsg:(NSString *)msg
            serialNo:(NSString *)serialNo {
    __weak typeof(self) weakSelf = self;
    /// 获取 flutter 设置
//    [[AppMethodChannel shareInstance].methodChannel invokeMethod:@"queryAntiLoseDeviceSettings"
//                                                       arguments:@{ @"uniqueNo":serialNo }
//                                                          result:^(NSDictionary * _Nullable result) {
//        UIApplicationState state = [UIApplication sharedApplication].applicationState;
//        if (state == UIApplicationStateBackground) {
//            [LocalNotificationUtil addLocalNotice:msg
//                                       identifier:serialNo repeat:YES];
//        } else {
//            if (weakSelf.alertView != nil) {
//                [weakSelf.alertView removeFromSuperview];
//            }
//            weakSelf.alertView = [JCAlertView alertWithTitle:@"提示"
//                                                     message:msg
//                                                confirmTitle:@"好的"
//                                                confirmClick:^{
//                [weakSelf stopPlayer];
//                weakSelf.alertView = nil;
//            }];
//            [weakSelf.alertView show];
//        }
//
//        NSInteger bellIndex = [result[@"mobileBellIndex"] integerValue];
//        [weakSelf ringTheBell:bellIndex
//                         once:NO
//                    volumeMax:YES];
//    }];
}

/// 判断勿扰模式然后响铃
- (void)alertDisconnectMsg:(NSString *)msg
                  serialNo:(NSString *)serialNo {
    __weak typeof(self) weakSelf = self;
    /// 获取 flutter 设置
//    [[AppMethodChannel shareInstance].methodChannel invokeMethod:@"queryAntiLoseDeviceSettings"
//                                                       arguments:@{ @"uniqueNo":serialNo }
//                                                          result:^(NSDictionary * _Nullable result) {
//        /// 1.勿扰模式是否打开
//        if ([result[@"dndStatus"] boolValue]) {
//            /// 2.勿扰时间段是否开启
//            if (![result[@"dndDurationStatus"] boolValue]) {
//                /// 未自定义时间段，则全天勿扰
//                return;
//            }
//            
//            NSInteger dndDurationStart = [result[@"dndDurationStart"] integerValue];
//            NSInteger dndDurationEnd = [result[@"dndDurationEnd"] integerValue];
//            
//            NSDate *now = [[NSDate alloc] init];
//            NSInteger seconds = [now hour] * 3600 + [now minute] * 60 + [now seconds];
//            
//            /// 不跨天时间段
//            if (dndDurationStart < dndDurationEnd &&
//                seconds >= dndDurationStart &&
//                seconds <= dndDurationEnd) {
//                return;
//            }
//            
//            /// 跨天时间段
//            if (dndDurationStart > dndDurationEnd &&
//                (seconds >= dndDurationStart ||
//                 seconds <= dndDurationEnd)) {
//                return;
//            }
//        }
//        
//        UIApplicationState state = [UIApplication sharedApplication].applicationState;
//        if (state == UIApplicationStateBackground) {
//            [LocalNotificationUtil addLocalNotice:msg
//                                       identifier:serialNo repeat:NO];
//        } else {
//            if (weakSelf.alertView != nil) {
//                [weakSelf.alertView removeFromSuperview];
//            }
//            weakSelf.alertView = [JCAlertView alertWithTitle:@"提示"
//                                                     message:msg
//                                                confirmTitle:@"好的"
//                                                confirmClick:^{
//                [weakSelf stopPlayer];
//                weakSelf.alertView = nil;
//            }];
//            [weakSelf.alertView show];
//        }
//        /// 3.手机响铃是否开启
//        if ([result[@"mobileBellStatus"] boolValue]) {
//            /// 正常响铃
//            NSInteger bellIndex = [result[@"mobileBellIndex"] integerValue];
//            [weakSelf ringTheBell:bellIndex
//                             once:YES
//                        volumeMax:NO];
//        }
//    }];
}

- (void)ringTheBell:(NSInteger)bellIndex
               once:(BOOL)once
          volumeMax:(BOOL)volumeMax {
    
    JCLog(@"收到响铃通知");
    
    /// 申请一段后台运行时间用于响铃激活
     if ([UIApplication sharedApplication].applicationState == UIApplicationStateBackground) {
         [self beginBackgroundTask];
     }
     
    
    if (volumeMax) {
        MPMusicPlayerController *mpVC = [MPMusicPlayerController applicationMusicPlayer];
        // 正在播放，则不用重新保存系统音量值
        if (_player == nil) {
            self.originVolume = mpVC.volume;
        } else {
            [_player stop];
            _player = nil;
        }
#if DEBUG
        mpVC.volume = 0.3f;
#else
        mpVC.volume = 1.0f;
#endif
    } else {
        self.originVolume = -1;
    }
    
    NSError *err;
    NSURL *url = [[NSBundle mainBundle] URLForResource:[@(bellIndex) description]
                                         withExtension:@"caf"];
    
    // 初始化播放器
    _player = [[AVAudioPlayer alloc] initWithContentsOfURL:url
                                                     error:&err];
    JCLog(@"初始化播放器:%@",err);
    // 设置播放器声音
    _player.volume = 1.0f;
    // 设置代理
    _player.delegate = self;
    // 设置播放速率
    _player.rate = 1.0;
    // 设置播放次数 负数代表无限循环
    if (once) {
        _player.numberOfLoops = 1;
    } else {
        // 根据铃声时长定义，循环播放约15s
        NSArray *loops = @[@15, @5, @8, @45, @8, @8, @7, @15, @8, @15];
        if (bellIndex < loops.count) {
            _player.numberOfLoops = [[loops objectAtIndex:bellIndex] integerValue];
        } else {
            _player.numberOfLoops = 1;
        }
    }

    __weak typeof(self) weakSelf = self;
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)((once ? 0.1 : 0.6) * NSEC_PER_SEC)),
                   dispatch_get_main_queue(), ^{
        // 准备播放
        if (![weakSelf.player prepareToPlay]) {
            NSLog(@"警报预播放失败");
            JCLog(@"警报预播放失败");
        }
        if (![weakSelf.player play]) {
            NSLog(@"警报播放失败");
            JCLog(@"警报播放失败");
        }
        [[MPNowPlayingInfoCenter defaultCenter] setNowPlayingInfo:@{}];
    });
}

// 申请后台
// https://www.jianshu.com/p/1f2572c08816
- (void)beginBackgroundTask {
    if (self.backgroundTaskIdentifier != UIBackgroundTaskInvalid) {
        [self endBackgroundTask];
    }
    NSLog(@"== beginBackgroundTask ==");
    __weak typeof(self) weakSelf = self;
    self.backgroundTaskIdentifier = [[UIApplication sharedApplication] beginBackgroundTaskWithExpirationHandler:^{
        // 在时间到之前会进入这个block，一般是iOS7及以上是3分钟。按照规范，在这里要手动结束后台，你不写也是会结束的（据说会crash）
        NSLog(@"== backgroundTask expiration handler called ==");
        [weakSelf endBackgroundTask];
    }];
}

// 注销后台
- (void)endBackgroundTask {
    NSLog(@"== endBackgroundTask ==");
    if (self.backgroundTaskIdentifier != UIBackgroundTaskInvalid) {
        [[UIApplication sharedApplication] endBackgroundTask:self.backgroundTaskIdentifier];
        self.backgroundTaskIdentifier = UIBackgroundTaskInvalid;
    } else {
        NSLog(@"== backgroundTaskIdentifier UIBackgroundTaskInvalid ==");
    }
}

- (void)audioPlayerDecodeErrorDidOccur:(AVAudioPlayer *)player error:(NSError *)error {
    NSLog(@"%@==%@",player,error);
}
- (AVAudioSession *)audioSession {
    if (!_audioSession) {
        _audioSession = [AVAudioSession sharedInstance];
        NSError *error;
        [_audioSession setCategory:AVAudioSessionCategoryPlayback error:&error];
        [_audioSession setActive:YES error:&error];
        if (error) {
            NSLog(@"音频初始化失败:%@",error);
        }
    }
    return _audioSession;
}



@end
