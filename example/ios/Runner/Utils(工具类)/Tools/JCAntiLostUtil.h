//
//  JCAntiLostUtil.h
//  Runner
//
//  Created by <PERSON> on 2021/3/11.
//

#import <Foundation/Foundation.h>
#import <Flutter/Flutter.h>
NS_ASSUME_NONNULL_BEGIN

@interface JCAntiLostUtil : NSObject

+ (JCAntiLostUtil *)shareInstance;

- (void)handleWithReuslt:(FlutterResult)result
               arguments:(NSDictionary *)arguments;

- (void)stopPlayer;

// 申请后台
- (void)beginBackgroundTask;
// 注销后台
- (void)endBackgroundTask;

- (void)ringTheBell:(NSInteger)bellIndex
               once:(BOOL)once
          volumeMax:(BOOL)volumeMax;

@end

NS_ASSUME_NONNULL_END
