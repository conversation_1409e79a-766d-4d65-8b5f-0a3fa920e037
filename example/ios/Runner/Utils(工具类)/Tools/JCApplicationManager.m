//
//  JCApplicationManager.m
//  Runner
//
//  Created by <PERSON> on 2020/12/18.
//

#import "JCApplicationManager.h"
#import "JCPrintCenter.h"
#import "JCBluetoothManager.h"


@implementation JCApplicationManager
{
    NSString *_token;
    NSString *_mallToken;
    
}

@dynamic token;

+ (instancetype)shareInstance
{
    static JCApplicationManager *_manager = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        _manager = [[JCApplicationManager alloc] init];
    });
    return _manager;
}

- (void)setUser:(JCUserModel *)user {
    _user = user;
    m_userModel = user;
}

-(void)setToken:(NSString *)token
{
    _token = token;
    [[NSUserDefaults standardUserDefaults] setObject:token forKey:DC_ACCESS_TOKENkEY];
}

- (NSString *)token
{
    if (STR_IS_NIL(_token)) {
        _token = [[NSUserDefaults standardUserDefaults] objectForKey:DC_ACCESS_TOKENkEY];
    }
    return UN_NIL(_token);
}

- (void)setMallToken:(NSString *)token {
    _mallToken = token;
    [[NSUserDefaults standardUserDefaults] setObject:_mallToken forKey:DC_ACCESS_MALL_KENkEY];
}

- (NSString *)mallToken {
    if (STR_IS_NIL(_mallToken)) {
        _mallToken = [[NSUserDefaults standardUserDefaults] objectForKey:DC_ACCESS_MALL_KENkEY];
    }
    return UN_NIL(_mallToken);
}

- (void)setDeviceList:(NSArray<JCDevicesSeriesModel *> *)deviceList
{
    if (deviceList) {
        NSMutableArray *mutableArray = [NSMutableArray array];
        for (JCDevicesSeriesModel *model in deviceList) {
            if ([model.code isEqualToString:@"Q2"]) {
                continue;;
            }
            for (AdaptMachinesModel *adapt in model.adaptMachines) {
                [mutableArray addObject:adapt.machineName];
            }
        }
        JCBlUETOOTH_MANAGER.filterArray = [mutableArray copy];
        _deviceList = deviceList;
    }
}


-(JCDevicesSeriesModel *)mapPrinterName:(NSString*)name
{
    NSParameterAssert(name != nil&& ![name isEqualToString:@""]);
    __block JCDevicesSeriesModel *model;
    [self.deviceList enumerateObjectsUsingBlock:^(JCDevicesSeriesModel * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        for (AdaptMachinesModel *item in obj.adaptMachines) {
            if ([name hasPrefix:@"D110"]) {//d110和D11系列匹配特殊处理
                if ([item.machineName isEqualToString:@"D110"]) {
                    model = obj;
                    *stop = YES;
                }
            }else{
            if ([name hasPrefix:item.machineName]) {
                model = obj;
                *stop = YES;
            }
        }
        }
    }];
    return model;
   
    
}

@end
