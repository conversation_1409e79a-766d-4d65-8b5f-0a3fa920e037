//
//  LocalNotificationUtil.m
//  Runner
//
//  Created by <PERSON> on 2021/3/24.
//

#import "LocalNotificationUtil.h"
#import <UserNotifications/UserNotifications.h>

@implementation LocalNotificationUtil

// 注册通知
+ (void)registerAPNWithCompletion:(void(^)(BOOL success))completion {
    if (@available(iOS 10.0, *)) { // iOS10 以上
       
        UNUserNotificationCenter *center = [UNUserNotificationCenter currentNotificationCenter];
        [center requestAuthorizationWithOptions:(UNAuthorizationOptionAlert | UNAuthorizationOptionSound | UNAuthorizationOptionBadge)
                              completionHandler:^(BOOL granted, NSError * _Nullable error) {
            NSLog(@"%d====%@",granted,error);
            completion(granted);
            if (!granted) {
//                dispatch_async(dispatch_get_main_queue(), ^{
//                    [DCHUDHelper showMessage:@"请打开系统蓝牙后进行操作"
//                              hideAfterDelay:3];
//                });
            }
        }];
    } else {// iOS8.0 以上
        [self checkCurrentNotificationStatusWithCompletion:^(BOOL success) {
            
        }];
        UIUserNotificationSettings *setting = [UIUserNotificationSettings settingsForTypes:UIUserNotificationTypeBadge | UIUserNotificationTypeSound | UIUserNotificationTypeAlert categories:nil];
        [[UIApplication sharedApplication] registerUserNotificationSettings:setting];
    }
}

+ (void) checkCurrentNotificationStatusWithCompletion:(void(^)(BOOL success))completion{
    if (@available(iOS 10 , *))
    {
         [[UNUserNotificationCenter currentNotificationCenter] getNotificationSettingsWithCompletionHandler:^(UNNotificationSettings * _Nonnull settings) {
                // 没权限
                completion(settings.authorizationStatus != UNAuthorizationStatusDenied);
        }];
    }
    else if (@available(iOS 8 , *))
    {
        UIUserNotificationSettings * setting = [[UIApplication sharedApplication] currentUserNotificationSettings];
        completion(setting.types != UIUserNotificationTypeNone);
    }
    else
    {
        UIRemoteNotificationType type = [[UIApplication sharedApplication] enabledRemoteNotificationTypes];
        completion(type != UIUserNotificationTypeNone);
    }
}
/**
 {"aps"=>{
         "content-available" => 1,
         "alert" => {
             "title"=>"这是标题",
             "subtitle"=>"这是小标题",
             "body"=>"这是内容"
         },
         "badge" => 1
         "sound" => "one.wav"
     }
 }
 ————————————————
 版权声明：本文为CSDN博主「zyww」的原创文章，遵循CC 4.0 BY-SA版权协议，转载请附上原文出处链接及本声明。
 原文链接：https://blog.csdn.net/sinat_27741463/article/details/102758611
 */
+ (void)addLocalNotice:(NSString *)notice
            identifier:(NSString *)identifier repeat:(BOOL)repeat bellIndex:(NSInteger)bellIndex{
    if (@available(iOS 10.0, *)) {
        UNUserNotificationCenter *center = [UNUserNotificationCenter currentNotificationCenter];
        UNMutableNotificationContent *content = [[UNMutableNotificationContent alloc] init];
        
        // 标题
//        content.title = @"测试标题";
//        content.subtitle = @"测试通知副标题";
        // 内容
        content.body = notice;
        // 声音
//        content.sound = [UNNotificationSound soundNamed:[NSString stringWithFormat:@"%ld.caf",bellIndex]];
        content.sound = nil;
        // 角标
        content.badge = @1;
        
        // 多少秒后发送,可以将固定的日期转化为时间
        NSTimeInterval time = [[NSDate dateWithTimeIntervalSinceNow:0.2] timeIntervalSinceNow];
        
        // repeats，是否重复，如果重复的话时间必须大于60s，要不会报错
        UNTimeIntervalNotificationTrigger *trigger = [UNTimeIntervalNotificationTrigger triggerWithTimeInterval:time repeats:NO];
        
        UNNotificationRequest *request = [UNNotificationRequest requestWithIdentifier:identifier // 添加通知的标识符，可以用于移除，更新等操作
                                                                              content:content
                                                                              trigger:trigger];
        
        [center addNotificationRequest:request withCompletionHandler:^(NSError *_Nullable error) {
            NSLog(@"成功添加推送");
        }];
    } else {
        UILocalNotification *notif = [[UILocalNotification alloc] init];
        // 发出推送的日期
        notif.fireDate = [NSDate dateWithTimeIntervalSinceNow:0.2];
        // 推送的内容
        notif.alertBody = notice;
        // 可以添加特定信息
//        notif.userInfo = @{@"noticeId":@"00001"};
        // 角标
        notif.applicationIconBadgeNumber = 1;
        // 提示音
//        notif.soundName = [NSString stringWithFormat:@"%ld.caf",bellIndex];
        notif.soundName = @"";
        // 每周循环提醒
//        notif.repeatInterval = NSCalendarUnitWeekOfYear;
        
        [[UIApplication sharedApplication] scheduleLocalNotification:notif];
    }
}

+ (void)addLocalNotice:(NSString *)notice
            identifier:(NSString *)identifier repeat:(BOOL)repeat {
    [self addLocalNotice:notice identifier:identifier repeat:repeat bellIndex:-1];
}
@end
