//
//  DeviceUpdateUtil.m
//  Runner
//
//  Created by <PERSON> on 2021/1/22.
//

#import "DeviceUpdateUtil.h"
#import "JCMessageImageView.h"
#import "NSData+YDCRC.h"
#import "JCBluetoothManager.h"
#import "JCPrintDevice.h"
#import "JCAlert.h"
#import "JCDeviceUpdateAleart.h"
#import "DCWeakTimer.h"

@interface DeviceUpdateUtil ()

@property(nonatomic,strong) JCUpdataProessAleart *progressAleart;

@property(nonatomic,strong) DCWeakTimer *timer;

@property(nonatomic,copy) NSString *updatedDevice;

@property(nonatomic,copy) XYNormalBlock finishBlock;

@property(nonatomic,strong) DCHUDHelper *hud;

@end

@implementation DeviceUpdateUtil

+ (instancetype)sharedInstance
{
    static DeviceUpdateUtil *manager = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        manager = [[DeviceUpdateUtil alloc] init];
    });
    return manager;
}


- (void)defectivePrinterUpdate
{
    self.hud = [DCHUDHelper show];
    XYWeakSelf;
    [self requestUpdateMsgDeviceReconnect:NO Handle:^(id x) {
        } Success:^(__kindof YTKBaseRequest * _Nonnull request, id  _Nonnull responseObject) {
            [weakSelf.hud hideAnimated:YES];
            [JCDeviceUpdateAleart aleartWithTitle:@"固件已损坏"
                                         subtitle:@""
                                          Message:UN_NIL(self.updateModel.updateInfo)
                                     ComfirmBlock:^{
                [weakSelf beginUpdateFinishHandle:^{
                    
                }];
                } cancelBlock:^{
                            
            }];
         
        } failure:^(YTKBaseRequest * _Nullable request, NSError * _Nullable error) {
            [weakSelf.hud onError:error.localizedDescription];
        }];
    
}



- (void)beginUpdateFinishHandle:(XYNormalBlock)block
{
    self.finishBlock = block;
   [XY_KEYWindow addSubview:self.progressAleart];
  [self.progressAleart onState:UpdataProessStatePrepare Title:@"固件升级准备中..." detalMsg:@"请勿锁屏或切换程序\n老版本设备连接电源"];
   
    XYWeakSelf;
    NSDictionary *callbackInfoDic = @{@"8":XY_LANGUAGE_TITLE_NAMED(@"app00701",@"更新失败"),@"9":XY_LANGUAGE_TITLE_NAMED(@"app00703", @"正在更新中"),@"1":XY_LANGUAGE_TITLE_NAMED(@"app00743",@"打印机连接已断开"),@"3":XY_LANGUAGE_TITLE_NAMED(@"app00574",@"电量不足"),@"7":XY_LANGUAGE_TITLE_NAMED(@"app00705",@"手动取消固件升级"),@"5":XY_LANGUAGE_TITLE_NAMED(@"app00706",@"打印机拒绝固件升级请求"),@"2":XY_LANGUAGE_TITLE_NAMED(@"app00706",@"打印机拒绝固件升级请求"),@"6":XY_LANGUAGE_TITLE_NAMED(@"app00714",@"升级异常，请连接电源重启打印机后再升级"),@"4":XY_LANGUAGE_TITLE_NAMED(@"app00823",@"文件校验失败"),@"10":XY_LANGUAGE_TITLE_NAMED(@"",@"参数错误"),@"11":XY_LANGUAGE_TITLE_NAMED(@"",@"更新成功")};
    [self downLoadFirmtag:21 DownloadSuccess:^{
        
    } DownloadError:^(id x) {
        [weakSelf.progressAleart onState:UpdataProessStateError Title:@"固件升级出错" detalMsg:x];
     }  DownloadpProgress:^(double percent) {
         dispatch_async(dispatch_get_main_queue(), ^{
             weakSelf.progressAleart.progressNum = percent *0.2;
         });
                    
                }
                 TransferResult:^(NSString *Info) {
                    NSLog(@"----------%@",Info);
                if ([Info isEqualToString:@"9"]) {
                   

                }else {
                     
                    if([Info hasPrefix:@"9_["] && [Info hasSuffix:@"]"]){
                        NSRange startRange = [Info rangeOfString:@"["];
                        NSRange endRange = [Info rangeOfString:@"]"];
                        NSRange range = NSMakeRange(startRange.location + startRange.length, endRange.location - startRange.location - startRange.length);
                        NSString *result = [Info substringWithRange:range];
                        
                        if ([result intValue] == 1) {
                            weakSelf.updatedDevice = JC_CURRENT_CONNECTED_PRINTER;
                            [weakSelf.progressAleart onState:UpdataProessStateUpdating Title:@"固件开始升级" detalMsg:@"1.硬件设备会断开\n2.如果该设备没有自动重启，需要您手动重启"];
                            weakSelf.timer  =[DCWeakTimer timerWithTimeInterval:0.6 target:weakSelf selector:@selector(countDown) userInfo:nil repeats:YES runLoop:[NSRunLoop mainRunLoop] mode:NSDefaultRunLoopMode];
                            

                        }else{
                            weakSelf.progressAleart.progressNum =0.2 + [result floatValue]*0.8;
                        }
                            
                        }else if([Info isEqualToString:@"11"]){
                          
                        }else{
                            [weakSelf.progressAleart onState:UpdataProessStateError Title:@"固件升级出错" detalMsg:[callbackInfoDic objectForKey:Info]];
                         }
                }
                    
                }];
}


- (void)countDown
{
    NSLog(@"----进度%f", self.progressAleart.progressNum);
    if (self.progressAleart.progressNum >= 1) {
        [self.timer invalidate];
         self.timer = nil;
        [self.progressAleart removeFromSuperview];
        self.progressAleart.progressNum= 0;
        if (self.finishBlock) {
            self.finishBlock();
        }
     
        return;
    }
 
    self.progressAleart.progressNum = self.progressAleart.progressNum + 0.01;
}

- (void)updateFinishedWithDevice:(NSString*)device
{
     [self.progressAleart removeFromSuperview];
    if ([device isEqualToString:self.updatedDevice]) {
        [self.timer invalidate];
        self.timer = nil;
        [XY_KEYWindow addSubview:self.progressAleart];
        [self.progressAleart onState:UpdataProessStateFinish Title:@"" detalMsg:@""];
        self.updatedDevice = @"";
        self.finishBlock = nil;
    }
  
      
   
  
   
}


- (void)requestUpdateMsgDeviceReconnect:(BOOL)reconnect Handle:(XYBlock)deviceBlock Success:(XYRequestCompletionBlock)success failure:(XYRequestFailureBlock)failure
{
    XYWeakSelf;
    [self requestDeviceMsgSuccess:^(id x) {
        if (deviceBlock) {
            deviceBlock(x);
        }
        NSString *device = JC_CURRENT_CONNECTED_PRINTER;
        NSString *mac = [x valueForKeyPath:PRINT_DEVICE_MAC_ADDRESS];
        NSString *printType = [[JCBluetoothManager sharedInstance] printerTypeFromPrinterName:device isNeedShowChildType:YES];
        NSDictionary *params = @{
        @"machineType" : UN_NIL(device),
        @"hardVersion" : UN_NIL( weakSelf.hardware),
        @"firmVersion" : UN_NIL(weakSelf.firmware),
        @"machineSno":UN_NIL(mac),
        @"machineName" : UN_NIL(printType),
        };
        [DCHTTPRequest postWithParams:params ModelType:[JCDeviceFirmwareRemoteModel class] Path:JC_DeviceModule_DetailInfo_URL  Json:YES
                              Success:^(__kindof YTKBaseRequest * _Nonnull request, JCDeviceFirmwareRemoteModel *model) {
           if (model) {
               weakSelf.updateModel = model;
               
               if (success) {
                   success(request,model);
               }
               if (!model.needUpgrade && reconnect) {
                   [weakSelf updateFinishedWithDevice:JC_CURRENT_CONNECTED_PRINTER];
               }
             
           }else{
               weakSelf.updateModel= nil;
               if (failure) {
                   failure(request,[NSError errorWithCode:1009 localizedMsg:@"未获取到升级信息"]);
               }
  
           }

       } failure:^(YTKBaseRequest * _Nullable request, NSError * _Nullable error) {
           weakSelf.updateModel= nil;
           if (failure) {
               failure(request,error);
           }
       }];
     
            
        } error:^(id x) {
            if (failure) {
                failure(nil, [NSError errorWithCode:1009 localizedMsg:x]);
            }
        }];
}

- (void)requestDeviceMsgSuccess:(XYBlock)sucess error:(XYBlock)error {
    XYWeakSelf;
    if (!JC_IS_CONNECTED_PRINTER) {
        if (error) {
            weakSelf.hardware = @"";
            weakSelf.firmware = @"";
            error(@"未连接设备");
        }
    }
    [[JCPrintDevice shareDevice] getDeviceInfoDict:^(NSDictionary *x) {
        if (x !=nil && x.allKeys.count > 0) {
            if ([x.allKeys containsObject:PRINT_DEVICE_HARDWARE_TYPE] && [x.allKeys containsObject:PRINT_DEVICE_FIRMWARE_TYPE]) {
                
                NSString *deviceHardware =  [x valueForKeyPath:PRINT_DEVICE_HARDWARE_TYPE];
                NSString *deviceFirmware =  [x valueForKeyPath:PRINT_DEVICE_FIRMWARE_TYPE];
                NSString *gears =  [x valueForKeyPath:PRINT_DEVICE_AUTO_SHUTDOWN_DURATION];

                if([deviceHardware isEqualToString:@"UNRESOPN_ERROR"] || STR_IS_NIL(deviceHardware)){
                    if (error) {
                        weakSelf.hardware = @"";
                        error(@"获取硬件信息失败");
                    }
                    return;
                }
                if([deviceFirmware isEqualToString:@"UNRESOPN_ERROR"]|| STR_IS_NIL(deviceFirmware)){
                    if (error) {
                        weakSelf.firmware = @"";
                        error(@"获取固件信息失败");
                    }
                    return;
                }

                weakSelf.hardware = deviceHardware;
                weakSelf.firmware = deviceFirmware;
                weakSelf.gears = UN_NIL(gears);
               
                if (sucess) {
                    sucess(x);
                }
                 
            }else{
                if (error) {
                    weakSelf.hardware = @"";
                    weakSelf.firmware = @"";
                    error(@"获取设备信息为空");
                }
            }
        
        }else{
            if (error) {
                weakSelf.hardware = @"";
                weakSelf.firmware = @"";
                error(@"获取设备信息失败");
            }
        }
      
    }];

}



/*
 0-更新失败
 1-正在更新中
 2-固件版本格式解析异常
 3-文件解析异常
 4-更新成功（表示固件传输给打印机成功）
 5-手动取消固件升级
 6-打印机拒绝固件升级请求
 7-通讯异常（长时间未接收到打印机请求数据指令）
 */
- (void)downLoadFirmtag:(NSInteger)tag  DownloadSuccess:(XYNormalBlock)success   DownloadError:(XYBlock)downError DownloadpProgress:(XYRequestDownloadBlock)DownloadpProgress TransferResult:(XYBlock)result{
    XYWeakSelf
   
    if(![[NSFileManager defaultManager] fileExistsAtPath:RESOURCE_FIRM_PATH]){
        [[NSFileManager defaultManager] createDirectoryAtPath:RESOURCE_FIRM_PATH withIntermediateDirectories:YES attributes:nil error:nil];
    }
    NSString *saveUrlString = [NSString stringWithFormat:@"%@/%@.%@",RESOURCE_FIRM_PATH,[[  self.updateModel.url lastPathComponent] stringByDeletingPathExtension],[  self.updateModel.url pathExtension]];
    
    YTKNetworkConfig *config = [YTKNetworkConfig sharedConfig];
    config.baseUrl = @"";
    NSLog(@"----------------->>>");
    [DCHTTPRequest downLoadFileWithParams:@{} UrlString:self.updateModel.url savePath:saveUrlString tagString:StringFromInt(tag) Success:^(__kindof YTKBaseRequest * _Nonnull request, id  _Nonnull responseObject) {
       
        if (JC_IS_CONNECTED_PRINTER) {
            if (success) {
                success();
            }
        }else{
            if (downError) {
                downError(@"打印机已断开连接");
            }
        }

        NSLog(@"文件是否存在:%@",[[NSFileManager defaultManager] fileExistsAtPath:saveUrlString]?@"是":@"否");
        [[JCPrintDevice shareDevice] updateFirmwareWithResource:saveUrlString crcString:weakSelf.updateModel.md5 firmVersion:weakSelf.firmware hardVersion:UN_NIL(weakSelf.hardware) result:^(NSString * _Nonnull Info) {
            NSLog(@"更新状态：%@",Info);
            if (result) {
                result(Info);
            }
        }];
    } failure:^(YTKBaseRequest * _Nullable request, NSError * _Nullable error) {
        if (downError) {
            downError(error.localizedDescription);
        }
    } downloadBlock:DownloadpProgress];
    config.baseUrl = JCCommonServerURL;
    NSLog(@"++++++++++++++");
}


- (JCUpdataProessAleart *)progressAleart
{
    if (!_progressAleart) {
        _progressAleart = [[JCUpdataProessAleart alloc] init];
    }
    return _progressAleart;
}

- (void)dealloc
{
    NSLog(@"---%@ delloc",[self class]);
    [self.timer invalidate];
     self.timer = nil;
}


@end
