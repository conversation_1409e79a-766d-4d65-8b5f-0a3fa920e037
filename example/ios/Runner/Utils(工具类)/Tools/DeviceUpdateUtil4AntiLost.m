//
//  DeviceUpdateUtil4AntiLost.m
//  Runner
//
//  Created by <PERSON> on 2021/1/22.
//

#import "DeviceUpdateUtil4AntiLost.h"
#import "JCMessageImageView.h"
#import "NSData+YDCRC.h"
#import "JCBluetoothManager.h"
#import "JCPrintDevice.h"
#import "JCAlert.h"
#import "JCDeviceUpdateAleart.h"
#import "DCWeakTimer.h"
#import "JCAPI_Antilost.h"

@interface DeviceUpdateUtil4AntiLost ()

@property(nonatomic,strong) JCUpdataProessAleart *progressAleart;

@property(nonatomic,strong) DCWeakTimer *timer;

@property(nonatomic,copy) NSString *updatedDevice;

@property(nonatomic,copy) XYNormalBlock finishBlock;

@property(nonatomic,strong) DCHUDHelper *hud;

@end

@implementation DeviceUpdateUtil4AntiLost

+ (instancetype)sharedInstance
{
    static DeviceUpdateUtil4AntiLost *manager = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        manager = [[DeviceUpdateUtil4AntiLost alloc] init];
    });
    return manager;
}


- (void)beginUpdateFinishHandle:(XYNormalBlock)block {
    self.finishBlock = block;
    [XY_KEYWindow addSubview:self.progressAleart];
    [self.progressAleart onState:UpdataProessStatePrepare Title:@"固件升级准备中..." detalMsg:@"请勿锁屏或切换程序\n老版本设备连接电源"];
   
    XYWeakSelf;
    [self downLoadFirmtag:21 DownloadSuccess:^{
        
    } DownloadError:^(id x) {
        [weakSelf.progressAleart onState:UpdataProessStateError
                                   Title:@"固件升级出错"
                                detalMsg:x];
        
    } DownloadpProgress:^(double percent) {
        dispatch_async(dispatch_get_main_queue(), ^{
            weakSelf.progressAleart.progressNum = percent * 0.15;
        });
        
    } progress:^(float progress) {
        weakSelf.progressAleart.progressNum = 0.15 + progress * 0.85;
    } complete:^(int errorCode) {
        if (errorCode == 0) {
            weakSelf.updatedDevice = weakSelf.identifier;
            [weakSelf.progressAleart onState:UpdataProessStateUpdating
                                       Title:@"固件开始升级"
                                    detalMsg:@"1.硬件设备会断开\n2.如果该设备没有自动重启，需要您手动重启"];
            weakSelf.timer = [DCWeakTimer timerWithTimeInterval:0.6
                                                         target:weakSelf
                                                       selector:@selector(countDown)
                                                       userInfo:nil
                                                        repeats:YES
                                                        runLoop:[NSRunLoop mainRunLoop]
                                                           mode:NSDefaultRunLoopMode];
        } else {
            NSDictionary *errorMessageMap = @{
                @"200": @"取消成功",
                @"551": @"固件升级请求被拒",
                @"302": @"设备连接断开",
                @"303": @"超时错误",
                @"304": @"设备忙碌",
                @"305": @"设备不支持该功能",
                @"501": @"文件校验不通过",
                @"502": @"设备电量低",
                @"551": @"固件升级请求被拒",
                @"526": @"取消失败，设备已开始升级无法取消",
                @"527": @"取消失败，当前未进行固件升级",
                @"308": @"设备未绑定",
                @"309": @"crc校验失败",
                @"601": @"包号校验失败",
            };
            
            NSString *errorCodeString = [@(errorCode) description];
            NSString *errorMessage = [NSString stringWithFormat:@"错误码：%@", errorCodeString];
            if ([errorMessageMap.allKeys containsObject:errorCodeString]) {
                errorMessage = errorMessageMap[errorCodeString];
            }
            
            [weakSelf.progressAleart onState:UpdataProessStateError
                                       Title:@"固件升级出错"
                                    detalMsg:errorMessage];
        }
    }];
}


- (void)countDown
{
    NSLog(@"----进度%f", self.progressAleart.progressNum);
    if (self.progressAleart.progressNum >= 1) {
        [self.timer invalidate];
         self.timer = nil;
        [self.progressAleart removeFromSuperview];
        self.progressAleart.progressNum= 0;
        if (self.finishBlock) {
            self.finishBlock();
        }
     
        return;
    }
 
    self.progressAleart.progressNum = self.progressAleart.progressNum + 0.03;
}

- (void)updateFinishedWithDevice:(NSString*)device
{
     [self.progressAleart removeFromSuperview];
    if ([device isEqualToString:self.updatedDevice]) {
        [self.timer invalidate];
        self.timer = nil;
        [XY_KEYWindow addSubview:self.progressAleart];
        [self.progressAleart onState:UpdataProessStateFinish Title:@"" detalMsg:@""];
        self.updatedDevice = @"";
        self.finishBlock = nil;
    }
}

- (void)downLoadFirmtag:(NSInteger)tag
        DownloadSuccess:(XYNormalBlock)success
          DownloadError:(XYBlock)downError
      DownloadpProgress:(XYRequestDownloadBlock)DownloadpProgress
               progress:(JCSDKBlockFloat)progress
               complete:(JCSDKBlockInt)complete {
    XYWeakSelf
   
    if(![[NSFileManager defaultManager] fileExistsAtPath:RESOURCE_FIRM_PATH]){
        [[NSFileManager defaultManager] createDirectoryAtPath:RESOURCE_FIRM_PATH withIntermediateDirectories:YES attributes:nil error:nil];
    }
    NSString *saveUrlString = [NSString stringWithFormat:@"%@/%@.%@",RESOURCE_FIRM_PATH,[[  self.updateModel.url lastPathComponent] stringByDeletingPathExtension],[  self.updateModel.url pathExtension]];
    
    YTKNetworkConfig *config = [YTKNetworkConfig sharedConfig];
    config.baseUrl = @"";
    NSLog(@"----------------->>>");
    [DCHTTPRequest downLoadFileWithParams:@{} UrlString:self.updateModel.url savePath:saveUrlString tagString:StringFromInt(tag) Success:^(__kindof YTKBaseRequest * _Nonnull request, id  _Nonnull responseObject) {
        if (success) {
            success();
        }

        NSLog(@"文件是否存在:%@",[[NSFileManager defaultManager] fileExistsAtPath:saveUrlString]?@"是":@"否");
        [JCAPI_Antilost updatePrinterWithIndentifiers:weakSelf.identifier
                                             sVersion:weakSelf.updateModel.version
                                             crcValue:weakSelf.updateModel.md5
                                                 path:saveUrlString
                                             hVersion:weakSelf.hardware
                                         withComplete:^(int errorCode) {
            NSLog(@"防丢器固件更新完成状态：%@", @(errorCode));
            if (complete) {
                complete(errorCode);
            }
        }
                                       withOnProgress:^(float progressValue) {
            NSLog(@"防丢器固件更新进度：%@", @(progressValue));
            if (progress) {
                progress(progressValue);
            }
        }];
        
    } failure:^(YTKBaseRequest * _Nullable request, NSError * _Nullable error) {
        if (downError) {
            downError(error.localizedDescription);
        }
    } downloadBlock:DownloadpProgress];
    NSLog(@"++++++++++++++");
    config.baseUrl = JCCommonServerURL;
}


- (JCUpdataProessAleart *)progressAleart
{
    if (!_progressAleart) {
        _progressAleart = [[JCUpdataProessAleart alloc] init];
    }
    return _progressAleart;
}

- (void)dealloc
{
    NSLog(@"---%@ delloc",[self class]);
    [self.timer invalidate];
     self.timer = nil;
}


@end
