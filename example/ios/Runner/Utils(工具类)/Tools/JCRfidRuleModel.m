//
//  JCRfidRuleModel.m
//  XYFrameWork
//
//  Created by j c on 2019/9/6.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCRfidRuleModel.h"
#import "NSDictionary+DCJson.h"

@implementation JCRfidRuleModel

-(instancetype)init{
    self = [super init];
    if(self){
        _print_strategy = @[];
        _rfid_status = [[JCPrintStatusModel alloc] init];
    }
    return self;
}

+(JSONKeyMapper *)keyMapper
{
    return [[JSONKeyMapper alloc] initWithModelToJSONDictionary:@{@"xyid":@"id",@"bluetooth_number":@"bluetoothNumber",@"is_open":@"enabled",@"option":@"value",@"print_strategy":@"printConfig",@"rfid_status":@"rfidStatus"}];
}

@end


@implementation JCPrintStatusModel

-(instancetype)init{
    self = [super init];
    if(self){
        _status = @"0";
    }
    return self;
}

+(J<PERSON><PERSON>KeyMapper *)keyMapper
{
    return [[JSONKeyMapper alloc] initWithModelToJSONDictionary:@{@"start_time":@"enableStartTime",@"end_time":@"enableDeadlineTime"}];
}
@end

@implementation JCRuleModel

+(JSONKeyMapper *)keyMapper
{
    return [[JSONKeyMapper alloc] initWithModelToJSONDictionary:@{@"start_time":@"enableStartTime",@"end_time":@"enableDeadlineTime"}];
}

-(instancetype)init{
    self = [super init];
    if(self){
        
    }
    return self;
}
//@property (nonatomic, strong) NSString <Optional> *xyid;
//@property (nonatomic, strong) NSString <Optional> *bluetooth_number;
//@property (nonatomic, strong) NSString <Optional> *start_time;
//@property (nonatomic, strong) NSString <Optional> *end_time;
//@property (nonatomic, strong) NSString <Optional> *status;
//@property (nonatomic, strong) NSString <Optional> *rullJosnString;
-(void)toJCRuleModelWith:(JCRfidRuleModel *)model{
    self.xyid = model.xyid;
    self.bluetooth_number = model.bluetooth_number;
    self.status = model.rfid_status.status;
    self.start_time = model.rfid_status.start_time;
    self.end_time = model.rfid_status.end_time;
    NSMutableString *str = [[NSMutableString alloc] init];
    for (NSInteger i = 0; i< model.print_strategy.count; i++){
        JCRfidRuleModel *m = [model.print_strategy objectAtIndex:i];
        NSDictionary *dic = @{
                              @"xyid":UN_NIL(m.xyid),
                              @"name":UN_NIL(m.name),
                              @"is_open":UN_NIL(m.is_open),
                              @"option":UN_NIL(m.option)
                              };
        [str appendString:dic.dc_toJSONString];
        if(i != model.print_strategy.count - 1){
            [str appendString:@"-|-"];
        }
    }
    if(str.length != 0){
        self.rullJosnString = str;
    }
}

-(JCRfidRuleModel *)reversalSelfToJCRfidRuleModel{
    JCRfidRuleModel *m = [[JCRfidRuleModel alloc] init];
    m.xyid = self.xyid;
    m.bluetooth_number = self.bluetooth_number;
    m.rfid_status = [[JCPrintStatusModel alloc] init];
    m.rfid_status.status = self.status;
    m.rfid_status.start_time = self.start_time;
    m.rfid_status.end_time = self.end_time;
    NSMutableArray *arr = [NSMutableArray array];
    NSArray *arrJson = [self.rullJosnString componentsSeparatedByString:@"-|-"];
    for (NSString *str in arrJson){
        NSData *jsonData = [str dataUsingEncoding:NSUTF8StringEncoding];
        NSDictionary *dic = [NSJSONSerialization JSONObjectWithData:jsonData options:kNilOptions error:nil];
        JCRfidRuleModel *m1 = [[JCRfidRuleModel alloc] init];
        m1.is_open = [dic valueForKey:@"is_open"];
        m1.xyid = [dic valueForKey:@"xyid"];
        m1.name = [dic valueForKey:@"name"];
        m1.option = [dic valueForKey:@"option"];
        [arr addObject:m1];
    }
    m.print_strategy = arr;
    return m;
}

@end
