//
//  JCDevicesSeriesModel.h
//  Runner
//
//  Created by <PERSON> on 2021/1/20.
//

#import <JSONModel/JSONModel.h>

NS_ASSUME_NONNULL_BEGIN

@interface AdaptMachinesModel : JSONModel

@property(nonatomic,assign) NSString *machineId;

@property(nonatomic,assign) NSString *machineName;

@end

@protocol AdaptMachinesModel;

@interface JCDevicesSeriesModel : JSONModel

@property(nonatomic,assign) NSString *xyid;

@property(nonatomic,copy) NSString *name;

@property(nonatomic,copy) NSString *prototypeUrl;

@property(nonatomic,copy) NSString *frameUrl;

@property(nonatomic,copy)NSString *code;

@property(nonatomic,copy) NSArray <AdaptMachinesModel> *adaptMachines;




@end

NS_ASSUME_NONNULL_END
