//
//  LocalNotificationUtil.h
//  Runner
//
//  Created by <PERSON> on 2021/3/24.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface LocalNotificationUtil : NSObject

// 注册通知
+ (void)registerAPNWithCompletion:(void(^)(BOOL success))completion;
+ (void)addLocalNotice:(NSString *)notice
            identifier:(NSString *)identifier repeat:(BOOL)repeat;

+ (void)addLocalNotice:(NSString *)notice
            identifier:(NSString *)identifier repeat:(BOOL)repeat bellIndex:(NSInteger)bellIndex;

@end

NS_ASSUME_NONNULL_END
