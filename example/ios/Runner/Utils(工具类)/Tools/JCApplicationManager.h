//
//  JCApplicationManager.h
//  Runner
//
//  Created by <PERSON> on 2020/12/18.
//

#import <Foundation/Foundation.h>
#import "JCUserModel.h"
#import "JCDevicesSeriesModel.h"

NS_ASSUME_NONNULL_BEGIN

#define JCAPP_Manager  [JCApplicationManager shareInstance]

#define kJCPrinter_Type  JCAPP_Manager.cuttentDevice.name

#define kJCPrinter_Id  JCAPP_Manager.cuttentDevice.xyid

#define kJC_Device  JCAPP_Manager.cuttentDevice

#define kJC_DeviceList  JCAPP_Manager.deviceList

#define kJC_Token   JCAPP_Manager.token

#define kJC_Mall_Token   JCAPP_Manager.mallToken

@interface JCApplicationManager : NSObject
@property(nonatomic,copy) NSString *token;

@property(nonatomic,copy) NSString *mallToken;

@property(nonatomic,strong) JCUserModel *user;

@property(nonatomic,strong) JCDevicesSeriesModel *cuttentDevice;

@property(nonatomic,strong)NSString *machineID;

@property(nonatomic,copy) NSArray <JCDevicesSeriesModel *> *deviceList;





+(instancetype)shareInstance;

-(JCDevicesSeriesModel *)mapPrinterName:(NSString*)name;


@end

NS_ASSUME_NONNULL_END
