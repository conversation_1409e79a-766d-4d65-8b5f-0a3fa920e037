//
//  JCDevicesSeriesModel.m
//  Runner
//
//  Created by <PERSON> on 2021/1/20.
//

#import "JCDevicesSeriesModel.h"


@implementation AdaptMachinesModel

+ (BOOL)propertyIsOptional:(NSString *)propertyName
{
    return  YES;
}



@end

@implementation JCDevicesSeriesModel

+(JSON<PERSON>eyMapper *)keyMapper
{
    return [[JSONKeyMapper alloc] initWithModelToJSONDictionary:@{@"xyid":@"id"}];
}

+ (BOOL)propertyIsOptional:(NSString *)propertyName
{
    return  YES;
}



@end
