////
////  JCVenderConfig.m
////  ChenYin
////
////  Created by <PERSON> on 2020/4/29.
////  Copyright © 2020 JCChenYin. All rights reserved.
////
//
//#import "JCVenderTool.h"
//////百度地图///
////#import <BMKLocationkit/BMKLocationComponent.h>
////#import <BMKLocationkit/BMKLocationManager.h>
//#import <UMShare/UMShare.h>
//#import <UMCommon/UMCommon.h>
//
//static NSString *const jUMengKey = @"5ea7f3f0167edd77a9000148";
//static NSString *const jWeiXinKey = @"wx3e8fa98f8dbda7ec";
//static NSString *const jWeiXinSecret = @"d937c6f8558ab6cad1b8e34e1b7fe460";
//static NSString *const jQQKey = @"101891179";
//static NSString *const jQQSecret = @"80a33ba698a7602ade14cf815fb2209f";
//static NSString *const jSinaKey = @"3131827243";
//static NSString *const jSinaSecret = @"bc63dcf6056a2d074d0eebdc85d8da90";
//static NSString *const jBuglyKey = @"1db72849c5";
//static NSString *const jBuglySecret = @"d7d7b9f7-bf78-44d4-a044-3500f17ead8d";
//static NSString *const jBaiduKey = @"d7d7b9f7-bf78-44d4-a044-3500f17ead8d";
//
//@interface JCVenderTool()
////<BMKLocationManagerDelegate, BMKLocationAuthDelegate>
////{
////    BMKLocationManager *_locationManager;
////}
//
//
//@end
//
//@implementation JCVenderTool
//
// static JCVenderTool *_instance =nil;
//+ (instancetype)shareInstance
//{
//   
//    static dispatch_once_t onceToken;
//    dispatch_once(&onceToken, ^{
//        _instance = [[self alloc] init];
//    });
//    return _instance ;
//
//  
//}
//
//- (void)config
//{
//    [self configBaiDuMap]; //配置百度地图
//    [self configUSharePlatforms]; //配置友盟
//}
//
////百度定位配置
//- (void)configBaiDuMap
//{
////    [[BMKLocationAuth sharedInstance] checkPermisionWithKey:jBaiduKey authDelegate:self];
////    //初始化实例
////    _locationManager = [[BMKLocationManager alloc] init];
////    //设置delegate
////    _locationManager.delegate = self;
////    //设置返回位置的坐标系类型
////    _locationManager.coordinateType = BMKLocationCoordinateTypeBMK09LL;
////    //设置距离过滤参数
////    _locationManager.distanceFilter = kCLDistanceFilterNone;
////    //设置预期精度参数
////    _locationManager.desiredAccuracy = kCLLocationAccuracyBest;
////    //设置应用位置类型
////    _locationManager.activityType = CLActivityTypeAutomotiveNavigation;
////    //设置是否自动停止位置更新
////    _locationManager.pausesLocationUpdatesAutomatically = NO;
////    //设置是否允许后台定位
////    //_locationManager.allowsBackgroundLocationUpdates = YES;
////    //设置位置获取超时时间
////    _locationManager.locationTimeout = 10;
////    //设置获取地址信息超时时间
////    _locationManager.reGeocodeTimeout = 10;
////    
////    [_locationManager requestLocationWithReGeocode:YES withNetworkState:YES completionBlock:^(BMKLocation * _Nullable location, BMKLocationNetworkState state, NSError * _Nullable error) {
////        
////    }];
//}
//
//- (void)configUSharePlatforms
//{
//    [UMConfigure initWithAppkey:jUMengKey channel:@"App Store"];
//
//    /* 设置微信的appKey和appSecret */
//    [[UMSocialManager defaultManager] setPlaform:UMSocialPlatformType_WechatSession appKey:jWeiXinKey appSecret:jWeiXinSecret redirectURL:@"http://mobile.umeng.com/social"];
//    /*设置QQ平台的appID*/
//    [[UMSocialManager defaultManager] setPlaform:UMSocialPlatformType_QQ appKey:jQQKey appSecret:jQQSecret redirectURL:@"http://mobile.umeng.com/social"];
//    /* 设置新浪的appKey和appSecret */
//    [[UMSocialManager defaultManager] setPlaform:UMSocialPlatformType_Sina appKey:jSinaKey  appSecret:jSinaSecret redirectURL:@"http://www.niimbot.com"];
//
//}
//
//
//-(BOOL)application:(UIApplication *)app openURL:(NSURL *)url options:(NSDictionary<UIApplicationOpenURLOptionsKey,id> *)options{
//      
// if([url.absoluteString containsString:jWeiXinKey] || [url.absoluteString containsString:jQQKey] || [url.absoluteString containsString:jSinaKey]){
//        //qq 微信登录回调
//        BOOL result = [[UMSocialManager defaultManager] handleOpenURL:url];
//        return result;
//    }
//  
//
//    return YES;
//}
//
//- (BOOL)installPlatform:(JPlatformType)type
//{
//    UMSocialPlatformType umType = (int)type;
//    return [[UMSocialManager defaultManager] isInstall:umType];
//}
//
//
//            
//- (BOOL)getUserInWithViewController:(id)viewController Platform:(JPlatformType)platformType
//                             result:(JCResultBlock)resultBlock
//{
//    UMSocialPlatformType umType = (int)platformType;
//    if([[UMSocialManager defaultManager] isInstall:umType]){
//        [[UMSocialManager defaultManager] getUserInfoWithPlatform:umType currentViewController:viewController completion:^(id result, NSError *error) {
//            
//            if(error){
//                if (resultBlock) {
//                    resultBlock(nil,error);
//                }
//                return;
//             }
//    
//            UMSocialUserInfoResponse *resp = result;
//               NSMutableDictionary *userInfo = [NSMutableDictionary dictionary];
//               userInfo[@"openId"] = resp.openid ? resp.openid : UN_NIL(resp.usid) ;
//               userInfo[@"profileUrl"] = UN_NIL(resp.iconurl);
//               userInfo[@"unionId"] = UN_NIL(resp.unionId);
//               userInfo[@"displayName"]=  UN_NIL(resp.name);
//            NSLog(@"---------三方平台信息--%@",userInfo);
//            if (resultBlock) {
//                resultBlock(userInfo ,nil);
//            }
//            
//        }];
//        return YES;
//    }
//    return NO;
//    
//    
//
//
//  
//    
//}
//
//
//@end
