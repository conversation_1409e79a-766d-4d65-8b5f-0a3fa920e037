//
//  JCPrinterSameTool.m
//  Runner
//
//  Created by <PERSON> on 2020/12/23.
//

#import "JCPrinterSameTool.h"
#import "DCNormalHTTPRequst.h"
#import "JCTemplateData.h"
#import "JCTemplateEditController.h"
#import "JCTemplateData+External.h"
#import "JCTemplateImageManager.h"
#import "JCBoardTemplateManager.h"
#import "JCDownLoadAleart.h"
#import "JCFontDownLoadManager.h"

@interface JCPrinterSameTool ()

@property(nonatomic,assign) BOOL cancel;
@property (nonatomic, strong) JCFontDownLoadManager *manager;

@end

@implementation JCPrinterSameTool

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.cancel = NO;
    }
    return self;
}


- (void)printSameWithTemplateId:(NSNumber*)templateId param:(NSDictionary *)params{
    NSString *nickName = params[@"nickName"];
    NSString *profileURL = params[@"profileURL"];
    NSString *source = params[@"type"];
    NetWorkErrorReturn
    __weak DCHUDHelper *hud =  [DCHUDHelper showWithStatus:@"加载中..."];
    XYWeakSelf
    [DCHTTPRequest getWithParams:@{@"imprintId":templateId} ModelType:[JCTemplateData class] url:JC_TagModule_GetTemplateData_URL Success:^(__kindof YTKBaseRequest * _Nonnull request, id  _Nonnull responseObject) {
         [hud hideAnimated:YES];
        if ([responseObject isKindOfClass:[JCTemplateData class]]) {
            JCTemplateData *data = (JCTemplateData*)responseObject;
            data.nickName = nickName;
            data.profileURL = profileURL;
            data.printSameSource = source;
            if ([data fontHasNotDownload] || ![data hasDownLoadAllImages] ) {
               __block BOOL hasFinshedTask = false;
                __block BOOL hasFinshedCount = false;
                JCDownLoadAleart *aleart = [JCDownLoadAleart aleartWithTitle:@"正在下载..." Message:@"模板数据（包含字体、图标、边框）下载中..."
                                      cancelBlock:^{
                   weakSelf.cancel = YES;
                  }];
                // 弹窗+计时器(最短2秒)
                [NSTimer jk_scheduledTimerWithTimeInterval:2 block:^{
                    hasFinshedCount = true;
                    if (hasFinshedTask) {
                        [aleart removeFromSuperview];
                        [weakSelf enterTemplateWithData:data];
                    }
                } repeats:false];
                // 下载图像
                [JCTemplateImageManager downLoadImagesForData:data options:DownAll complete:^{
                    if ([data fontHasNotDownload]) {
                        // 下载字体
                        self.manager = [[JCFontDownLoadManager alloc] initWithTemplateData:data];
                        [self.manager downLoadFonts:^(NSString * _Nonnull fontName, NSString * _Nonnull fontCode, double percent) {
                            
                        } completion:^{
                            hasFinshedTask = true;
                            if (aleart && hasFinshedCount) {
                                [aleart removeFromSuperview];
                                [weakSelf enterTemplateWithData:data];
                            }
                        }];
                    } else {
                        hasFinshedTask = true;
                        if (aleart && hasFinshedCount) {
                            [aleart removeFromSuperview];
                            [weakSelf enterTemplateWithData:data];
                        }
                    }
                }];
            }else{
                [weakSelf enterTemplateWithData:data];
            }
          
        }else{
        
            [DCHUDHelper showMessage:@"标签内容为空"];
        }
            
        } failure:^(YTKBaseRequest * _Nullable request, NSError * _Nullable error) {
            [hud onError:error.localizedDescription];
            
        }];
    
}

- (void)enterTemplateWithData:(JCTemplateData*)data
{
    XYWeakSelf
    [[JCBoardTemplateManager sharedManager] getDrawBoardTemplateWithAnotherModel:data completion:^(BOOL update, JCTemplateData * _Nonnull tmData) {
        if (!weakSelf.cancel) {
            UINavigationController *navi = (UINavigationController*)XY_KEYWindow.rootViewController;
            CGFloat scale = 1;
            if (update) {
                if (data.width > 0 && data.width > tmData.width) {
                    scale = tmData.width/data.width;
                }
            }
           
//            scale = 1;
            JCTemplateEditController *vc = [[JCTemplateEditController alloc] initWithModel:tmData printSameModel:data addElement:data.elements sourceId:data.sourceImprintId defaultScale:scale];
            if (update) {
                vc.deviceTemplate = tmData;
            }
            UINavigationController *navigationController = [[UINavigationController alloc] initWithRootViewController:vc];
            navigationController.modalPresentationStyle = UIModalPresentationFullScreen;
            navigationController.navigationBarHidden = YES;
            [navi presentViewController:navigationController animated:YES completion:nil];
        }
     
    }];
}

- (void)dealloc
{
    NSLog(@"-------delloc");
}

@end
