//
//  JCRFIDModel.m
//  XYFrameWork
//
//  Created by j c on 2019/8/27.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCRFIDModel.h"

@implementation JCRFIDModel

- (instancetype)init{
    self = [super init];
    if(self){
        self.allow_number = @"";
        self.allow_number_edit = @"";
        self.print_number = @"";
        self.serial_number = @"";
        self.use_status = @"";
        self.xyid = @"";
    }
    return self;
}

+(JSONKeyMapper *)keyMapper
{
    return [[JSONKeyMapper alloc] initWithModelToJSONDictionary:@{@"xyid":@"id",@"allow_number":@"allowNum",@"allow_number_edit":@"fixedAllowNum",@"serial_number":@"serialNumber",@"use_status":@"rfidStatus",@"print_number":@"materialUsed"}];
}
@end
