//
//  JCEventTrackingManager.h
//  ChenYin
//
//  Created by <PERSON> on 2020/7/3.
//  Copyright © 2020 JCChenYin. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface JCEventTrackingTool : NSObject

+ (instancetype)sharedInstance;

///**登录用户缓存的打印数据上传*/
//- (void)uploadSaveAndHistoryCacheData;


///**
// 静默上传
// 
// @param data 模型数据
// @param printNum 打印张数
// @param isHistoryPrint 是否是打印历史列表
// */
//- (void)uploadPrintTemplateData:(JCTemplateData*)data printNum:(NSString*)printNum isHistoryPrint:(BOOL)isHistoryPrint;




@end

NS_ASSUME_NONNULL_END
