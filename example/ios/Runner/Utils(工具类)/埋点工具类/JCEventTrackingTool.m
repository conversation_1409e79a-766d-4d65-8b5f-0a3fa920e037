//
//  JCEventTrackingManager.m
//  ChenYin
//
//  Created by <PERSON> on 2020/7/3.
//  Copyright © 2020 JCChenYin. All rights reserved.
//

#import "JCEventTrackingTool.h"
//#import "JCTemplateData+Transfer.h"
//#import "JCPrintSaveModel.h"
//#import <YYCache/YYCache.h>
//#import "JCTemplateImageManager.h"
//#import "JCKeychainTool.h"
//#import "NSDate+Custom.h"

static NSString *const jcPrintsaveModelCacheKey = @"jcPrintsaveModelCacheKey";

static NSString *const jcVisitorHistoryCacheKey = @"jcVisitorHistoryCacheKey";

static NSString *const jcLastTemplateId = @"jcLastTemplateId";

FOUNDATION_STATIC_INLINE NSString * JCGetLastTemplateId(){
    NSString *templateId = [[NSUserDefaults standardUserDefaults] objectForKey:jcLastTemplateId];
    return templateId;
}

FOUNDATION_STATIC_INLINE void JCSetLastTemplateId(NSString *templateId ){
    [[NSUserDefaults standardUserDefaults] setObject:templateId forKey:jcLastTemplateId];
}

@interface JCEventTrackingTool ()

//@property(nonatomic,strong)  YYDiskCache* printCache;
//
//@property(nonatomic,strong) YYDiskCache *historyCache;
//
///**游客已存在的打印历史模型*/
////@property(nonatomic,copy) JCPrintMetaData *metaData;
//
//@property(nonatomic,copy) NSString *printNum;


@end

@implementation JCEventTrackingTool

+ (instancetype)sharedInstance
{
    static JCEventTrackingTool *_shareInstance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        _shareInstance = [[JCEventTrackingTool alloc] init];
    });
    return _shareInstance;
}


//- (void)uploadPrintTemplateData:(JCTemplateData*)data printNum:(NSString*)printNum isHistoryPrint:(BOOL)isHistoryPrint
//{
//    self.printNum = printNum;
//    JCTemplateData *templateData = [data copy];
//    templateData.thumbnail = @"";
//    templateData.profile.extrain.createTime = [[NSDate date] getCurrentTimes];
//
//    BOOL isSame = [JC_CurrentTempteData isTheSameDataForPrint:data];
//    if (JC_USERVIEWMODEL.isLogined) {
//        if (!isHistoryPrint) {
//             [self saveAndUploadHistoryTemplateDatas:templateData isSame:isSame];
//        }else{
//             [self uploadUserHistoryData:data isHistoryPrint:isHistoryPrint];
//        }
//    }else{
//        [self uploadVistorHistoryTemplateDatas:templateData isSame:isSame];
//
//    }
//    //历史打印不保存模板
//    if (!isHistoryPrint) {
//       JC_CurrentTempteData = [templateData copy];
//    }
//
//}
//
////保存&上传打印历史
//- (void)saveAndUploadHistoryTemplateDatas:(JCTemplateData*)data isSame:(BOOL)isSame
//{
//     XYWeakSelf
//
//    if (isSame) {
//        NSLog(@"----更新相同模板上传打印历史-------");
//        [self uploadUserHistoryData:data isHistoryPrint:NO];
//    }else{
//         NSLog(@"----创建模板保存&上传打印历史------");
//        [[NSUserDefaults standardUserDefaults] removeObjectForKey:jcLastTemplateId];
//        [JCTemplateImageManager upLoadImageWithData:data complete:^{
//                  data.idStr = @""; //更新id置空
//                  data.profile.extrain.userId =JC_USERInfoModel.userId;
//                  JCPrintSaveModel *saveModel = [JCPrintSaveModel new];
//                  NSArray *categoryText = [data getAllTextContent];
//                  saveModel.categoryText = categoryText;
//                  saveModel.jsonTemplate = data;
//                 JCPrintMetaData *metaData = [JCPrintMetaData new];
//                  metaData.deviceId = kJCPrinter_Type;
//            metaData.printedInfo = [@{@"printNum":self.printNum} dc_toJSONString];
//            metaData.printedTime = [[NSDate date] getCurrentTimes];
//            metaData.userId =JC_USERInfoModel.userId;
//            saveModel.printMetadata = metaData;
//            saveModel.privated = !JC_USERVIEWMODEL.isPublic;
//                 if (data.thumbnail.length > 0) {
//                     NSDictionary *dic =  [saveModel jc_toDictory];
//                     NSLog(@"----打印静默保存requestJson---=%@",[dic dc_toJSONString]);
//                     [DCHTTPRequest postWithParams:dic  Path:JC_TemplateModule_SaveAndHistory_URL Json:YES Success:^(__kindof YTKBaseRequest * _Nonnull request, NSDictionary *responseObject) {
//                              NSLog(@"--😁😁😁😁--打印静默保存成功-----");
//                         [[NSNotificationCenter defaultCenter] postNotificationName:JCTemplateSvaeNotification object:nil];
//                         if (responseObject && responseObject.allKeys.count > 0) {
//                             NSString *templateId =[NSString stringWithFormat:@"%@",[responseObject objectForKey:@"templateId"]];
//                             JCSetLastTemplateId(templateId);
//                         }
//
//
//                        } failure:^(YTKBaseRequest * _Nullable request, NSError * _Nullable error) {
//                            #pragma mark - TODO 修改本地存储整个保存模型
//                            [weakSelf TemplateDataCacheAddObject:saveModel];
//                     }];
//
//                 }else{
//                      [weakSelf TemplateDataCacheAddObject:saveModel];
//                 }
//             }];
//    }
//
//
//}
//
//- (void)uploadUserHistoryData:(JCTemplateData*)data isHistoryPrint:(BOOL)isHistoryPrint
//{
//    XYWeakSelf
//
//    void (^uploadHistory)(JCTemplateData *tempData)=^(JCTemplateData* tempData){
//        JCPrintMetaData *metaModel = [JCPrintMetaData new];
//               metaModel.deviceId = kJCPrinter_Type;
//               metaModel.printedInfo = [@{@"printNum":weakSelf.printNum} dc_toJSONString];
//               metaModel.printedTime = [[NSDate date] getCurrentTimes];
//               metaModel.templateId = tempData.idStr;
//               metaModel.templateJson= tempData;
//               metaModel.userId =JC_USERInfoModel.userId;
//              if (data.thumbnail.length > 0) {
//                  NSDictionary *dic =  [metaModel  jc_toDictory];
//                  NSLog(@"上传历史数据-----%@",dic);
//                  [DCHTTPRequest postWithParams:@[dic] ModelType:[JCPrintSaveModel class] Path:JC_TemplateModule_History_external_URL Json:YES Success:^(__kindof YTKBaseRequest * _Nonnull request, id  _Nonnull responseObject) {
//                       NSLog(@"----登录用户上传历史-----%@",responseObject);
//                     } failure:^(YTKBaseRequest * _Nullable request, NSError * _Nullable error) {
//                          [weakSelf userHistoryCacheAddObject:metaModel];
//                  }];
//              }else{
//                  [weakSelf userHistoryCacheAddObject:metaModel];
//              }
//    };
//    if (isHistoryPrint) {
//        if (data.idStr.length > 0 && data.thumbnail.length > 0) {
//            uploadHistory(data);
//        }else{
//            NSLog(@"----%@---上传历史失败",data);
//        }
//    }else{
//        [JCTemplateImageManager upLoadImageWithData:data complete:^{
//            NSString *lastTemplateId = JCGetLastTemplateId();
//            if (lastTemplateId.length > 1) {
//              data.idStr = lastTemplateId;
//              uploadHistory(data);
//            }else{
//               return;
//            }
//        }];
//        }
//
//
//
//
//
//}
//
//- (void)TemplateDataCacheAddObject:(JCPrintSaveModel *)object
//{
//    id result = [self.printCache objectForKey:jcPrintsaveModelCacheKey];
//       NSMutableArray *resultArray = [NSMutableArray array];
//      if (result && [result isKindOfClass:[NSArray class]]) {
//          NSArray *array = (NSArray*)result;
//          resultArray = [array mutableArray];
//      }
//
//    [resultArray addObject:object];
//    [self.printCache setObject:[resultArray copy] forKey:jcPrintsaveModelCacheKey];
//
//}
//
//
//
////游客保存打印
//- (void)uploadVistorHistoryTemplateDatas:(JCTemplateData*)data isSame:(BOOL)isSame
//{
//    XYWeakSelf
//    dispatch_async(dispatch_get_global_queue(0, 0), ^{
//          [JCTemplateImageManager upLoadImageWithData:data complete:^{
//                 JCPrintMetaData *metaData = [JCPrintMetaData new];
//                 metaData.deviceId = kJCPrinter_Type;
//                 metaData.printedInfo = [@{@"printNum":self.printNum} dc_toJSONString];
//                 metaData.printedTime = [[NSDate date] getCurrentTimes];
//                 data.idStr = @"";
//                 metaData.templateId = data.idStr;
//                 metaData.templateJson= data;
//
//              if (isSame) {
//                  NSInteger templateSerial  = [[[NSDate date] getTimeStamp] integerValue] *(arc4random()%5 +1);
//                 metaData.templateSerial = [NSString stringWithFormat:@"%ld",templateSerial];
//              }else{
//                  JCPrintSaveModel *saveModel = [[JCPrintSaveModel alloc]init];
//                     saveModel.categoryText = [data getAllTextContent];
//                     saveModel.printMetadata = [metaData copy];
//                     saveModel.jsonTemplate = data;
//                     [weakSelf TemplateDataCacheAddObject:saveModel]; //保存模型
//              }
//
//
//
//
////              if (data.thumbnail.length > 0) {
////                     NSDictionary *dic = [metaData  jc_toDictory];
////                     [DCHTTPRequest postWithParams:@[dic] Path:JC_TemplateModule_History_external_URL Json:YES Success:^(__kindof YTKBaseRequest * _Nonnull request, id  _Nonnull responseObject) {
////                            NSLog(@"-- 游客-模型-上传历史-----%@",responseObject);
////                     } failure:^(YTKBaseRequest * _Nullable request, NSError * _Nullable error) {
////
////                   }];
////                }
//
//             }];
//
//
//      });
//
//
//}
//
//- (void)userHistoryCacheAddObject:(JCPrintMetaData *)object
//{
//    dispatch_async(dispatch_get_global_queue(0, 0), ^{
//       id result = [self.historyCache objectForKey:jcVisitorHistoryCacheKey];
//         if (result && [result isKindOfClass:[NSArray class]]) {
//             NSMutableArray *resultArray = [(NSArray*)result mutableCopy];
//             [resultArray addObject:object];
//             [self.historyCache setObject:[resultArray copy] forKey:jcVisitorHistoryCacheKey];
//         }else{
//            [self.historyCache setObject:@[object] forKey:jcVisitorHistoryCacheKey];
//         }
//
//    });
//
//}
//
//
//
//- (void)uploadUserSaveTemplateDatasCache
//{
//
//    static BOOL finished = YES;
//    if (!finished || !JC_USERVIEWMODEL.isLogined) {
//            return;
//       }
//       finished = NO;
//    dispatch_async(dispatch_get_global_queue(0, 0), ^{  //静默批量上传
//        id result = [self.printCache objectForKey:jcPrintsaveModelCacheKey];
//           if (result && [result isKindOfClass:[NSArray class]]) {
//                NSArray *resultArray = (NSArray*)result;
//               if (resultArray.count > 0) {
//                  dispatch_group_t group = dispatch_group_create();
//                    dispatch_group_enter(group);
//                   for (JCPrintSaveModel *saveModel in resultArray) {
//                       saveModel.privated = !JC_USERVIEWMODEL.isPublic;
//                       JCTemplateData *data = saveModel.jsonTemplate;
//                       data.idStr = @"";
//                       if (STR_IS_NIL(data.profile.extrain.userId )) {
//                          data.profile.extrain.userId =JC_USERInfoModel.userId;
//                       }
//                       if (STR_IS_NIL(saveModel.printMetadata.userId)) {
//                          saveModel.printMetadata.userId =JC_USERInfoModel.userId;
//                       }
//
//                       if (STR_IS_NIL(data.thumbnail)) {
//                           dispatch_group_enter(group);
//                           [JCTemplateImageManager upLoadImageWithData:data complete:^{
//                               dispatch_group_leave(group);
//                           }];
//                        }
//                     }
//                    dispatch_group_leave(group);
//                   dispatch_group_notify(group, dispatch_get_global_queue(0, 0), ^{
//                       NSMutableArray *thumbnailArray = [NSMutableArray array];
//                       NSMutableArray *mutableArray =  [resultArray mutableCopy];
//                       for (JCPrintSaveModel *model in resultArray) {
//                           if (model.jsonTemplate.thumbnail.length > 0) {
//                               [thumbnailArray addObject:[model jc_toDictory]];
//                               [mutableArray removeObject:model];
//                           }
//                       }
//                       @weakify(self);
//
//                       [DCHTTPRequest postWithParams:[thumbnailArray copy] Path:JC_TemplateModule_SaveAndHistoryArray_URL Json:YES Success:^(__kindof YTKBaseRequest * _Nonnull request, id  _Nonnull responseObject) {
//                           @strongify(self);
//                           finished = YES;
//                           if (mutableArray.count == 0) {
//                               [self.printCache removeAllObjects];
//                           }else{
//                               [self.printCache setObject:[mutableArray copy] forKey:jcPrintsaveModelCacheKey];
//                           }
//                                 NSLog(@"-- 登录用户- 批量-上传-----%@",responseObject);
//                        } failure:^(YTKBaseRequest * _Nullable request, NSError * _Nullable error) {
//                            finished = YES;
//                             NSLog(@"-- 登录用户- 批量-上传----失败");
//                        }];
//
//                   });
//               }else{
//                   finished = YES;
//               }
//
//           }else{
//               finished = YES;
//           }
//
//    });
//
//
//}
//
//
//- (void)uploadLocalHistoyCache
//{
//    static BOOL finished = YES;
//    if (!finished) {
//         return;
//    }
//    finished = NO;
//    dispatch_async(dispatch_get_global_queue(0, 0), ^{  //静默批量上传
//           id result = [self.historyCache objectForKey:jcVisitorHistoryCacheKey];
//              if (result && [result isKindOfClass:[NSArray class]]) {
//                   NSArray *resultArray = (NSArray*)result;
//                  if (resultArray.count > 0) {
//                     dispatch_group_t group = dispatch_group_create();
//                       dispatch_group_enter(group);
//                      for (JCPrintMetaData *meta in resultArray) {
//                          JCTemplateData *data = meta.templateJson;
//                          if (JC_USERInfoModel.userId.length > 0) {
//                              meta.userId = JC_USERInfoModel.userId;
//                              data.profile.extrain.userId = JC_USERInfoModel.userId;
//                          }
//                          if (STR_IS_NIL(data.thumbnail)) {
//                              dispatch_group_enter(group);
//                              [JCTemplateImageManager upLoadImageWithData:data complete:^{
//                                  dispatch_group_leave(group);
//                              }];
//                           }
//                        }
//                       dispatch_group_leave(group);
//                      dispatch_group_notify(group, dispatch_get_global_queue(0, 0), ^{
//                          NSMutableArray *thumbnailArray = [NSMutableArray array];
//                          NSMutableArray *mutableArray =  [resultArray mutableCopy];
//                          for (JCPrintMetaData *model in resultArray) {
//                              if (model.templateJson.thumbnail.length > 0) {
//                                  [thumbnailArray addObject:[model jc_toDictory]];
//                                  [mutableArray removeObject:model];
//                              }
//                          }
//                          @weakify(self);
//                          [DCHTTPRequest postWithParams:[thumbnailArray copy] Path:JC_TemplateModule_History_external_URL Json:YES Success:^(__kindof YTKBaseRequest * _Nonnull request, id  _Nonnull responseObject) {
//                              @strongify(self);
//                              finished = YES;
//                              if (mutableArray.count == 0) {
//                                  [self.historyCache removeAllObjects];
//                              }else{
//                                  [self.historyCache setObject:[mutableArray copy] forKey:jcPrintsaveModelCacheKey];
//                              }
//                                  NSLog(@"-- - 批量 -- 上传的失败的历史---%@",responseObject);
//                           } failure:^(YTKBaseRequest * _Nullable request, NSError * _Nullable error) {
//                             finished = YES;
//                           }];
//
//                      });
//                  }else{
//                      finished = YES;
//                  }
//
//              }else{
//                  finished = YES;
//              }
//
//       });
//
//}
//
//- (void)uploadSaveAndHistoryCacheData
//{
//    [self uploadUserSaveTemplateDatasCache];
//    [self uploadLocalHistoyCache];
//}
//
//
//
//-(YYDiskCache *)printCache
//{
//
//    if (!_printCache) {
//        _printCache = [[YYDiskCache alloc] initWithPath:JCPrintSaveTemplateFilePath];
//       _printCache.countLimit = 100;
//    }
//    return _printCache;
//
//}
//
//-(YYDiskCache *)historyCache
//{
//
//    if (!_historyCache) {
//        _historyCache = [[YYDiskCache alloc] initWithPath:JCHistoryTemplateFilePath];
//        _historyCache.countLimit = 100;
//
//    }
//    return _historyCache;
//
//
//}
//
//- (JCPrintMetaData *)metaData
//{
//
//    if (!_metaData) {
//        _metaData = [[JCPrintMetaData alloc] init];
//    }
//    return _metaData;
//
//
//}

@end
