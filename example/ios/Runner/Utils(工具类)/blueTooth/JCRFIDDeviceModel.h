//
//  JCRFIDDeviceModel.h
//  XYFrameWork
//
//  Created by j c on 2019/8/23.
//  Copyright © 2019 yumingyi. All rights reserved.
//

#import <Foundation/Foundation.h>

@interface JCRFIDDeviceModel : NSObject
/*
 0: rfid 的ID
 1:条码内容
2:批次号内容
3:限制张/米数
4:已用张/米数
5:耗材类型(1—间隙纸,2—黑标纸,3—连续纸,4-定孔纸，5-透明纸（4，目前还没有）6,碳带)返回对应的信息。
@"UNRESOPN_ERROR"表示获取信息超时; 空表示获取失败
 */
@property (nonatomic,copy) NSString *rfid; //条码内容
@property (nonatomic,copy) NSString *rfidCode; //条码内容
@property (nonatomic,copy) NSString *piciCode; //批次号内容
@property (nonatomic,copy) NSString *maxNumber; //限制张/米数
@property (nonatomic,copy) NSString *usedNumber; //已用张/米数
@property (nonatomic,copy) NSString *paperType; //耗材类型
@property (nonatomic,copy) NSString *error; //错误信息

-(instancetype)initWithDictionary:(NSDictionary *)dic;

@end

