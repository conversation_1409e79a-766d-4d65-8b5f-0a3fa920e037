//
//  JCRFIDDeviceModel.m
//  XYFrameWork
//
//  Created by j c on 2019/8/23.
//  Copyright © 2019 yumingyi. All rights reserved.
//

#import "JCRFIDDeviceModel.h"

@implementation JCRFIDDeviceModel

-(instancetype)initWithDictionary:(NSDictionary *)dic{
    self = [super init];
    if(self){
        self.error = [dic valueForKey:@"UNRESOPN_ERROR"];
        self.rfidCode = [dic valueForKey:@"1"];
        self.piciCode = [dic valueForKey:@"2"];
        self.maxNumber = [dic valueForKey:@"3"];
        self.usedNumber = [dic valueForKey:@"4"];
        self.paperType = [dic valueForKey:@"5"];
        self.rfid = [dic valueForKey:@"0"];
    }
    return self;
}

- (void)setUsedNumber:(NSString *)usedNumber{
    _usedNumber = usedNumber;
}
@end
