//
//  JCAPI.h
//  antilost
//
//  Created by j c on 2021/1/12.
//https://whjc.yuque.com/as00m5/manual/txug3s
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

typedef void (^JCSDKBlock) (void) ;
typedef void (^JCSDKBlockInt) (int errorCode) ;
typedef void (^JCSDKBlockArray) (NSArray *datasArray) ;
typedef void (^JCSDKBlockDictionary) (NSDictionary *dictionary) ;
typedef void (^JCSDKBlockBool) (BOOL isSuccess) ;
typedef void (^JCSDKBlockFloat) (float progress) ;
typedef void (^JCSDKBlockString) (NSString *string) ;


@interface JCAPI_Antilost : NSObject


/// 搜索蓝牙列表
/// @param filters 过滤字段
/// @param time 搜索超时时间
/// @param complete 回调
+ (void)startDiscoverWithFilters:(nullable NSArray <NSString *>*)filters
                     withTimeOut:(float)time
                    withComplete:(JCSDKBlockString)complete;



/// 用于连接后的设备，主动寻找手机
/// @param identifier 连接设备的ID
/// @param complete 回调
+ (void)onCalled:(NSString *)identifier withComplete:(JCSDKBlock)complete;

/// 设置超出范围报警模式
/// @param type 1-防丢器端报警 2-防丢器端不报警
/// @param identifier 连接设备ID
/// @param complete 回调
+ (void)setAlarmMode:(int)type withDevieceId:(NSString *)identifier withComplete:(JCSDKBlockDictionary)complete;

/// 绑定用户
/// @param userid 用户ID
/// @param identifier 连接设备ID
/// @param complete 回调
+ (void)bind:(NSString *)userid withDevieceId:(NSString *)identifier withComplete:(JCSDKBlockDictionary)complete;

/// 连接设备
/// @param connectName 设备名称
/// @param onInitSuccess 连接回调，返回连接设备的ID
/// @param onInitFail 连接失败 101-未授权，iOS无效。 301-未开启蓝牙
+  (void)initWith:(NSString *)connectName
 withSuccessBlock:(JCSDKBlockString)onInitSuccess
   withErrorBlock:(JCSDKBlockInt)onInitFail;


/// 外部断开的回调，连接成功回调里面进行监听
/// @param identifier 连接回调，返回连接设备的ID
/// @param complete 回调
+ (void)onPassiveDisconnect:(NSString *)identifier withComplete:(JCSDKBlock)complete;

/// 获取信号强度
/// @param identifier 蓝牙连接成功的ID
/// @param complete 回调
+ (void)readSSIWithIdentifier:(NSString *)identifier withComplete:(JCSDKBlockInt)complete;


/// 获取电池电量状态
/// @param identifier 蓝牙连接成功的ID
/// @param complete 回调 @{@"status":@"电池充电状态-1.充电中  2.充满  3.未充电"，@“power”:@"电量-1~4 电量等级,0表示电量很少防丢器关机了"}
+ (void)readSingleDevicePowerWithIdentifier:(NSString *)identifier withComplete:(JCSDKBlockDictionary)complete;


/// 取消搜索
+ (void)cancelDiscover;

/// 断开单个连接
/// @param identifier 连接设备的ID
/// @param complete 回调
+ (void)closeWithIdentifier:(NSString *)identifier withComplete:(JCSDKBlock)complete;

/// 获取打印机信息接口
/// @param type 1-超出距离设备报警
///             2-超出距离设备不报警
///             3-app发指令，防丢器报警
///             5-app发暂停报警
///             7-解绑设备
///             8-设备类型查询
///             9-设备序列号查询
///             10-设备固件版本号
///             11-设备硬件版本号
///             17-设备信息查询
/// @param identifier 蓝牙连接成功的ID
/// @param complete @{@"statusCode":@"0.成功 -1.失败 -2.忙碌 -3.不支持",@"result":字符串/字典}
+ (void)getDeviceInfo:(unsigned int)type withIdentifier:(NSString *)identifier withComplete:(JCSDKBlockDictionary)complete;


/// 获取当前连接的设备ID
/// @param complete 回调 @{@"name":key,@"id":connectUUIDString}
+ (void)getConnectDevicesIdentifiers:(JCSDKBlockDictionary)complete;


/// 发送图片到防丢器屏幕显示
/// @param picture 图像对象
/// @param identifier 已连接设备的ID
/// @param complete 发送完成/报错的回调
/// 0-发送成功，防丢器刷屏成功
/// 308-设备未绑定
/// 309-crc校验失败
/// 601-包号校验失败
/// @param progresss 进度条
+ (void)sendPictureToDevice:(UIImage *)picture withIndentifiers:(NSString *)identifier withComplete:(JCSDKBlockInt)complete withOnProgress:(JCSDKBlockFloat)progresss;
/// @param threshold 图像二值域值
+ (void)sendPictureToDevice:(UIImage *)picture withThreshold:(int)threshold withIndentifiers:(NSString *)identifier withComplete:(JCSDKBlockInt)complete withOnProgress:(JCSDKBlockFloat)progresss;


/// 固件升级
/// @param identifier 连接的设备id
/// @param sVersion 升级固件版本号
/// @param crcValue crc
/// @param path 固件文件路径
/// @param hVersion 硬件版本
/// @param complete 回调
/// 0:升级成功
/// 200:取消成功
/// 错误码：551-固件升级请求被拒
/// 错误码：302-设备连接断开
/// 错误码：303-超时错误
/// 错误码：304-设备忙碌（正在设置显示图像时）
/// 错误码：305-不支持该功能（设备无该功能）
/// 错误码：305-不支持该功能（设备无该功能）
/// 错误码：305-不支持该功能（设备无该功能）
/// 错误码：501-文件校验不通过（CRC值校验不通过 ）
/// 错误码：502-设备电量低
/// 错误码：551-固件升级请求被拒
/// 526-取消失败，设备已开始升级无法取消
/// 527-取消失败，当前未进行固件升级
/// 308-设备未绑定
/// 309-crc校验失败
/// 601-包号校验失败
/// @param progresss 进度
+ (void)updatePrinterWithIndentifiers:(NSString *)identifier sVersion:(NSString*)sVersion crcValue:(NSString *)crcValue path:(NSString *)path hVersion:(NSString *)hVersion withComplete:(JCSDKBlockInt)complete withOnProgress:(JCSDKBlockFloat)progresss;


/// 取消固件升级
/// @param identifier 连接设备的ID
+ (void)cancelUpdateWithIndentifiers:(NSString *)identifier;

@end

NS_ASSUME_NONNULL_END
