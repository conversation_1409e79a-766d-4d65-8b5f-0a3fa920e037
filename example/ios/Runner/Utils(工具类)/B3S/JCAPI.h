//
//  JCAPI.h
//  JCPrinterSDK
//
//  Created by  ydong on 2019/1/29.
//  Copyright © 2019  ydong. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

/**
 建议在iOS9以上系统使用
 */

/*
  v2.01版.     2020/04/10
  v2.11版.     2020/04/30
  v2.12版。    2020/05/06
  v2.12 B21分家版。    2020/06/05
  v2.12 适配新d11    2020/09/17
 */

typedef NS_ENUM(NSUInteger, JCBarcodeMode){
    //CODEBAR 1D format.
    JCBarcodeFormatCodebar      ,
    
    //Code 39 1D format.
    JCBarcodeFormatCode39       ,
    
    //Code 93 1D format.
    JCBarcodeFormatCode93       ,
    
    //Code 128 1D format.
    JCBarcodeFormatCode128      ,
    
    //EAN-8 1D format.
    JCBarcodeFormatEan8         ,
    
    //EAN-13 1D format.
    JCBarcodeFormatEan13        ,
    
    //ITF (Interleaved Two of Five) 1D format.
    JCBarcodeFormatITF          ,
    
    //UPC-A 1D format.
    JCBarcodeFormatUPCA         ,
    
    //UPC-E 1D format.
    JCBarcodeFormatUPCE
};

typedef void (^DidOpened_Printer_Block) (BOOL isSuccess)                ;
typedef void (^DidPrinted_Block)        (BOOL isSuccess)                ;
typedef void (^PRINT_INFO)              (NSString * printInfo)          ;
typedef void (^PRINT_STATE)             (BOOL isSuccess)                ;
typedef void (^PRINT_DIC_INFO)          (NSDictionary * printDicInfo)   ;

@interface JCAPI : NSObject

/**
 Wi-Fi获取手机当前连接wifi名。
 */
+ (NSString *)connectingWifiName;

/**
 Wi-Fi配置打印机连接手机当前连接wifi。
 
 @param   wifiName        wifi账号（非必须）
 @param   password        wifi密码。
 @param  timeout            设置超时(最小15)
 @param  searchTimes     搜索次数（最小3）
 @param   completion      配置打印机连接Wi-Fi是否成功。
 */
+ (void)connectWifiName:(NSString *)wifiName
               password:(NSString *)password
                timeout:(float)timeout
            searchTimes:(int)searchTimes
             completion:(PRINT_STATE)completion;


/**
  Wi-Fi取消配网
 */
+(void)cancleConnectWifi;

/**
 蓝牙/Wi-Fi获取搜索到的打印机列表。
 wifi回调：  @[@{@"ipAdd":@"ip地址", @"bleName":@"蓝牙名字"}]

 @param   isWifi          YES:为搜索Wi-Fi，NO为搜索蓝牙
 @param   completion      打印机名字数组block(该名字为连接打印机时的printerName参数)
 */
+ (void)scanPrinterNames:(BOOL)isWifi completion:(void(^)(NSArray *scanedPrinterNames))completion;

/// 适配d11连接时需要升级的适配指令,在openPrinter之前使用
/// @param block 回调
+ (void)readD11UpdateBlock:(PRINT_STATE)block;

/**
 蓝牙连接指定名称的打印机。
 
 @param   printerName     打印机名称。
 @param   completion      连接打印机是否成功。（连接状态改变通过该回调返回）
 */
+ (void)openPrinter:(NSString *)printerName
         completion:(DidOpened_Printer_Block)completion;

/**
 Wi-Fi连接指定名称的打印机。
 
 @param   host              打印机名称。
 @param   completion      连接打印机是否成功。（连接状态改变通过该回调返回）
 */
+(void)openPrinterHost:(NSString *)host
            completion:(DidOpened_Printer_Block)completion;



/**
 打印机类型,连接成功后才能使用,其它厂家打印机返回@“”/@“0”,精臣系打印机返回
 hBit = value/256
 lBit = value%256
 B3S: hBit/lBit = 1
 D11: hBit/lBit = 2
 B21: hBit = 3
 {
  L2B: lBit = 1
  L2W: lBit = 2
  C2B: lBit = 3
  C2W: lBit = 4
  C3B: lBit = 5
  C3W: lBit = 6
 }
 P1: hBit/lBit = 4
 B11: hBit/lBit = 7
 @return 返回打印机类型
 */
+(NSInteger )printerTypeInfo;


/**
 蓝牙关闭打开的打印机连接。
 */
+ (void)closePrinter;

/**
 Wi-Fi关闭打开的打印机连接。
 */
+ (void)closeWifiPrinter;

/**
 蓝牙/Wi-Fi获取当前连接的打印机名称（Wi-Fi为ip地址）。
 
 @return  当前连接的打印机名称。
 */
+ (NSString *)connectingPrinterName;

/**
 蓝牙/Wi-Fi连接状态
 
 @return 2表示连接Wi-Fi，1表示连接蓝牙，0表示无连接
 */
+ (int)isConnectingState;

/**
 蓝牙/Wi-Fi设置打印机。
 
 @param   state           参数为21-29      @param type   对应参数如下
                          21-设置打印浓度                 1—淡，2—正常, 3—浓, 4-较浓 5-最浓；（打价器仍只有三挡:1,2,3）
                          22-设置打印速度                 1—慢, 2—稍慢, 3—正常, 4—稍快, 5—快；（打价器速度三挡）
                          23-设置纸张类型                 1—间隙纸, 2—黑标纸, 3—连续纸，4-定孔纸，5-透明纸（4，目前还没有）
                          24-设置马达驱动(预留)            1
                          25-设置自动出纸(预留)            1、2、3
                          26-设置打印机语言；              1-中文，2-英文
                          27-设置自动关机时间              N分钟 1-4(
                                                        1:15min;
                                                        2:30min;
                                                        3:60min(D11为45分钟);
                                                        4:从不(D11为60分钟)
                                                        )
                          28-恢复出厂设置                 1
                          29-纸张类型标定                 1
 @param   completion      是否设置成功。
 */
+ (void)setPrintState:(NSInteger)state type:(NSInteger)type sucess:(PRINT_STATE)completion;

/**
 蓝牙/Wi-Fi获取打印机信息。b11系列只有9&12
 
 @param   type            1-打印浓度
                          2-打印速度
                          3-纸张类型
                          4-马达驱动（预留）
                          5-自动出纸（预留）
                          6-打印机语言
                          7-自动关机时间
                          8-机器型号
                          9-软件版本（固件版本）
                          10-当前电量
                          11-机器序列号
                          12-硬件版本
                          13-硬件参数信息（如:电池电压等）----(后面去掉该类型支持)
                          14-历史打印信息。
                          15-获取打印机读取成功次数信息(仅支持b21打印机,V2.11支持d11系列3.28以上版本)
                          16-查询打印机配网状态(仅支持b21打印机)
                          17-获取Mac地址
                          18-打印模式(2、热感应 1、热敏)
 @param   completion      返回对应的信息。@"UNRESOPN_ERROR"表示获取信息超时
 */
+ (void)getPrintInfo:(NSInteger)type sucess:(PRINT_INFO)completion;

/**
 蓝牙/Wi-Fi合并返回固件版本和硬件版本，英文,分隔（固件版本,硬件版本）

 @param   completion      @"UNRESOPN_ERROR"表示获取信息超时
 */
+ (void)getPrintInfosSucess:(PRINT_INFO)completion;


/**
 是否支持获取碳带长度

 @return YES:支持  NO：不支持
 */
+ (BOOL)isSupportGetCarbonUsedLength;


/**
 打印完单张返回碳带长度
 ***仅p1支持
 @param completion 返回打印单张使用的碳带长度
 @return YES:支持  NO：不支持
 */
+ (BOOL)getCarbonUsedLength:(PRINT_INFO)completion;

/**
 蓝牙/Wi-Fi获取打印机信息。

 @param   completion      为空表示获取信息超时
                          0-打印机名称（wifi时需先调用搜索接口，获取对应的蓝牙名字）
                          1-打印浓度;
                          2-打印速度
                          3-纸张类型
                          7-自动关机时间
                          9-软件版本（固件版本）
                          12-硬件版本
                          15-获取打印机读取成功次数信息(仅支持b21打印机)
 */
+ (void)getPrintAllInfosSucess:(PRINT_DIC_INFO)completion;

/**
 蓝牙/Wi-Fi是否支持盒盖状态检测
 
 @return  YES:支持、NO:不支持
 */
+ (BOOL)isSupportPrintCoverStatus;

/**
 蓝牙/Wi-Fi盒盖状态改变返回(在连接成功后调用)
 
 @param   completion      盒盖状态：0打开、1关闭
 @param   checkPaperBlock 当前打印机是否有耗材：0没有、1有
 @return  是否支持盒盖状态检测：YES:支持、NO:不支持
 */
+ (BOOL)getPrintCoverStatusChange:(PRINT_INFO)completion withCheckPrinterHavePaperBlock:(PRINT_INFO)checkPaperBlock;

/**
  蓝牙/Wi-Fi是否支持缺纸检测等功能（目前app没有用到具体功能接口，替换为该接口）

 @return  是否支持：YES:支持、NO:不支持
 */
+ (BOOL)isSupportNOPaper;

/**
 蓝牙/Wi-Fi是否支持RFID

 @return  YES:支持、NO:不支持
 */
+ (BOOL)isSupportRFID;

/**
 蓝牙/Wi-Fi获取RFID信息

 @param   completion      0:uuid
                         1:条码内容
                          2:批次号内容
                          3:限制张/厘米数
                          4:已用张/厘米数
                          5:耗材类型(1—间隙纸,2—黑标纸,3—连续纸,4-定孔纸，5-透明纸（4，目前还没有）6,碳带)返回对应的信息。
                          @"UNRESOPN_ERROR"表示获取信息超时; 空表示获取失败
 @return  是否支持RFID：YES:支持、NO:不支持
 */
+(BOOL)getRFIDDicInfoSucess:(PRINT_DIC_INFO)completion;
+ (BOOL)isSupportTag;
+(BOOL)getTagArrInfo:(BOOL)isWifi completion:(PRINT_DIC_INFO)completion;

/**
 蓝牙/Wi-Fi开始打印任务。
 
 @return  是否出现异常:YES为正常。
 */
+ (BOOL)startPage;

/**
 蓝牙/Wi-Fi结束单页打印.
 
 @param   count           打印份数。(只对smt有效)
 @return  是否出现异常:YES为正常。
 */
+ (BOOL)endPage:(CGFloat)count;


/**
 p1打印机打印前传入总打印份数,startDraw:height:orientation:之前调用

 @param totalQuantityOfPrints 总份数
 */
+ (void)setTotalQuantityOfPrints:(NSInteger)totalQuantityOfPrints;
/**
 蓝牙/Wi-Fi设置单页打印。
 
 @param   width           标签宽度，单位：毫米。
 @param   height          标签高度，单位：毫米。
 @param   orientation     标签打印方向: 0：不旋转；90：顺时针旋转90度；180：旋转180度；270：逆时针旋转90度。
 @return  是否出现异常:YES为正常。
 */
+ (BOOL)startDraw:(CGFloat)width
           height:(CGFloat)height
      orientation:(NSInteger)orientation;


/**
 蓝牙/Wi-Fi设置单页打印边距，这部分内的内容不打印出来

 @param insets CGFloat top, left, bottom, right 单位：毫米
 @param completion @"0":正常。
                   @"1":上边距<0异常
                   @"2":左边距<0异常
                   @"3":下边距<0异常
                   @"4":右边距<0异常
                   @"5":调用时机不对，应该在 startDraw: height: origation:之后调用
                   @"6":上边距+下边距 < draw height
                   @"7":左边距+右边距 < draw width
 @return 是否出现异常:YES为正常
 */
+(BOOL)setDrawboardEdge:(UIEdgeInsets)insets sucess:(PRINT_INFO)completion;

/**
 蓝牙/Wi-Fi打印文本。
 
 @param   text            文本内容。
 @param   x               打印对象的位置，单位毫米。
 @param   y               打印对象的位置，单位毫米。
 @param   width           打印对象的尺寸，单位毫米。
 @param   height          打印对象的尺寸，单位毫米。(可以用获取字体高度的选项获取)
 @param   canResetHeigth  是否重置允许改变高度:YES为可以。
 @param   fontHeight      文本的字体高度，单位毫米。
 @param   fontStyle       字体风格:0,正常; 1,粗体; 2,斜体; 3,斜体粗体; 4,下划线; 5,下划线粗体; 6,下划线斜体; 7,下划线斜体粗体。
 @param   alignment       对齐方式:0,左对齐; 1,居中对齐; 2,右对齐。
 @param   fontSpacing     字间距，单位毫米。
 @param   lineSpace       行间距，单位毫米。
 @param   orientation     旋转角度:0,不旋转; 90,顺时针旋转90度; 180,旋转180度; 270,逆时针旋转90度。
 @param   fontName        字体名字。
 @param   canBreak        是否换行，YES:是，NO:否
 
 @return  是否出现异常:YES为正常。
 */
+ (BOOL)drawText:(NSString *)text
               x:(CGFloat)x
               y:(CGFloat)y
           width:(CGFloat)width
          height:(CGFloat)height
  canResetHeight:(BOOL)canResetHeigth
      fontHeigth:(CGFloat)fontHeight
       fontStyle:(NSInteger)fontStyle
       alignment:(NSInteger)alignment
     fontSpacing:(CGFloat)fontSpacing
       lineSpace:(CGFloat)lineSpace
     orientation:(NSInteger)orientation
        fontName:(NSString *)fontName
        canBreak:(BOOL)canBreak;


/**
 蓝牙/Wi-Fi字体绘制高度（用于适配drawText: x: y:  width: height: fontHeigth: fontStyle: alignment: fontSpacing: lineSpace: orientation: fontName: canBreak:）
 
 @param   text            文本内容
 @param   width           打印对象的尺寸，单位毫米。
 @param   height          打印对象的尺寸，单位毫米。
 @param   fontHeight      字体高度：像素。
 @param   fontStyle       字体风格:0,正常; 1,粗体; 2,斜体; 3,斜体粗体; 4,下划线; 5,下划线粗体; 6,下划线斜体; 7,下划线斜体粗体; 8,删除线。
 @param   alignment       对其方式:0,left; 1,center; 2,right。
 @param   fontName        字体名。
 @param   fontSpacing     字间距。
 @param   lineSpace       行间距。
 @param   canBreak        是否换行，YES:是，NO:否
 @return  字体绘制高度。
 */
+ (CGFloat)textFrameHeightWidthText:(NSString*)text
                              width:(CGFloat)width
                             height:(CGFloat)height
                         fontHeigth:(CGFloat)fontHeight
                          fontStyle:(NSInteger)fontStyle
                          alignment:(NSInteger)alignment
                           fontName:(NSString *)fontName
                        fontSpacing:(CGFloat)fontSpacing
                          lineSpace:(CGFloat)lineSpace
                           canBreak:(BOOL)canBreak;

/**
 蓝牙/Wi-Fi打印文本。（二次开发接口，会根据width/height缩小文字）。
 
 @param   text            文本内容。
 @param   x               打印对象的位置，单位毫米。
 @param   y               打印对象的位置，单位毫米。
 @param   width           打印对象的尺寸，单位毫米。
 @param   height          打印对象的尺寸，单位毫米。
 @param   fontHeight      文本的字体高度，单位毫米。
 @param   alignment       对齐方式：0,左对齐; 1,居中对齐; 2,右对齐。
 @return  是否出现异常：YES为正常。
 */
+ (BOOL)drawText:(NSString *)text
               x:(CGFloat)x
               y:(CGFloat)y
           width:(CGFloat)width
          height:(CGFloat)height
      fontHeigth:(CGFloat)fontHeight
       alignment:(NSInteger)alignment
     orientation:(NSInteger)orientation;

/**
 蓝牙/Wi-Fi打印 Barcode 一维码。
 
 @param   text            一维码的内容。
 @param   x               打印对象的位置，单位毫米。
 @param   y               打印对象的位置，单位毫米。
 @param   width           打印对象的尺寸，单位毫米。
 @param   height          打印对象的尺寸，单位毫米。
 @param   fontHeight      打印字体的尺寸，单位毫米。小于等于0不显示字体。
 @param   textStyle       文字位置：0：条码下，1:条码上，2:不需要文字。
 @param   barcodeMode     条码类型。
 @param   isWhiteBack     是否带白色背景:YES为带，NO未不带。
 @param   orientation     旋转角度：0：不旋转；90：顺时针旋转90度；180：旋转180度；270：逆时针旋转90度。
 @return  是否出现异常：YES为正常
 */
+ (BOOL)drawBarcode:(NSString *)text
                  x:(CGFloat)x
                  y:(CGFloat)y
              width:(CGFloat)width
             height:(CGFloat)height
         fontHeight:(CGFloat)fontHeight
          textStyle:(NSInteger)textStyle
        barcodeMode:(JCBarcodeMode)barcodeMode
        isWhiteBack:(BOOL)isWhiteBack
        orientation:(NSInteger)orientation;

/**
 蓝牙/Wi-Fi生成条码最小宽度,单位像素。
 
 @param   text            条码内容。
 @param   barcodeMode     条码类型。
 @return  条码最小宽度。
 */
+ (NSInteger)getBarCodeWidth:(NSString *)text codeFormat:(JCBarcodeMode)barcodeMode;

/**
 蓝牙/Wi-Fi打印 Barcode 一维码带延长线。
 
 @param   text            一维码的内容。
 @param   x               打印对象的位置，单位毫米。
 @param   y               打印对象的位置，单位毫米。
 @param   width           打印对象的尺寸，单位毫米。
 @param   height          打印对象的尺寸，单位毫米。
 @param   fontHeight      打印字体的尺寸，单位毫米。小于等于0不显示字体（条码类型支持延长线时会忽略该值）。
 @param   textStyle       文字位置：0：条码下，1:条码上，2:不需要文字（条码类型支持延长线时会忽略该值）。
 @param   barcodeMode     条码类型。
 @param   isWhiteBack     是否带白色背景:YES为带，NO未不带。
 @param   isExtension     是否带延长线:YES为带，NO未不带。
 @param   orientation     旋转角度：0：不旋转；90：顺时针旋转90度；180：旋转180度；270：逆时针旋转90度。
 @return  是否出现异常：YES为正常
 */
+ (BOOL)drawBarcode:(NSString *)text
                  x:(CGFloat)x
                  y:(CGFloat)y
              width:(CGFloat)width
             height:(CGFloat)height
         fontHeight:(CGFloat)fontHeight
          textStyle:(NSInteger)textStyle
        barcodeMode:(JCBarcodeMode)barcodeMode
        isWhiteBack:(BOOL)isWhiteBack
        isExtension:(BOOL)isExtension
        orientation:(NSInteger)orientation;

/**
 蓝牙/Wi-Fi生成带延长线条码最小宽度,单位像素。
 
 @param   text            条码内容。
 @param   barcodeMode     条码类型。
 @param   isExtension     是否带延长线:YES为带，NO未不带。
 @return  条码最小宽度。
 */
+ (NSInteger)getBarCodeWidth:(NSString *)text codeFormat:(JCBarcodeMode)barcodeMode  isExtension:(BOOL)isExtension;

/**
 蓝牙/Wi-Fi打印 QRCode 二维码。
 
 @param   text            二维码的内容。
 @param   x               打印对象的位置，单位毫米。
 @param   y               打印对象的位置，单位毫米。
 @param   width           打印对象的尺寸，单位毫米。
 @param   isWhiteBack     是否带白色背景:YES为带，NO未不带。
 @param   orientation     旋转角度：0：不旋转；90：顺时针旋转90度；180：旋转180度；270：逆时针旋转90度。
 @return  是否出现异常：YES为正常。
 */
+ (BOOL)drawQRCode:(NSString *)text
                 x:(CGFloat)x
                 y:(CGFloat)y
             width:(CGFloat)width
       isWhiteBack:(BOOL)isWhiteBack
       orientation:(NSInteger)orientation;

/**
 蓝牙/Wi-Fi生成二维码最小宽度,单位像素。
 
 @param   text            二维码内容。
 @return  二维码最小宽/高。
 */
+ (NSInteger)getQRCodeWidth:(NSString *)text;

/**
 检验条码内容是否规范。
 
 @param   text            条码内容。
 @param   barcodeMode     条码类型。
 @return  -1.不支持的一维码类型
          0:正常
          1:长度不符合要求
          2:存在非支持字符
          3.存在非支持字符且长度不对
          4.字符串为null
 */
+ (NSString *)checkBarCode:(NSString *)text withBarcodeMode:(JCBarcodeMode)barcodeMode;

/**
 蓝牙/Wi-Fi返回处理后的条码内容。
 
 @param   text            条码内容。
 @param   barcodeMode     条码类型。
 @return  处理后的条码内容（也可用于检查条码内容是否规范，@"":表示条码内容不规范）
 */
+ (NSString *)dealBarCodeText:(NSString *)text withBarcodeMode:(JCBarcodeMode)barcodeMode;

/**
 蓝牙/Wi-Fi打印图片。
 
 @param   image           图像对象。
 @param   x               打印对象的位置，单位毫米。
 @param   y               打印对象的位置，单位毫米。
 @param   width           打印对象的尺寸，单位毫米。
 @param   height          打印对象的尺寸，单位毫米。
 @param   orientation     旋转角度：0：不旋转；90：顺时针旋转90度；180：旋转180度；270：逆时针旋转90度。
 @return  成功与否，成功为YES。
 */
+ (BOOL)drawImage:(UIImage *)image
                x:(CGFloat)x
                y:(CGFloat)y
            width:(CGFloat)width
           height:(CGFloat)height
      orientation:(NSInteger)orientation;

/**
 蓝牙/Wi-Fi打印虚线。
 
 @param   x               打印对象的位置，单位毫米。
 @param   y               打印对象的位置，单位毫米。
 @param   width           打印对象的尺寸，单位毫米。
 @param   height          打印对象的尺寸，单位毫米。
 @param   fullLineWidth   打印对象为虚线时的实线部分尺寸，单位毫米。
 @param   dashLineWidth   打印对象为虚线时的虚线部分尺寸，单位毫米。
 @param   orientation     旋转角度：0：不旋转；90：顺时针旋转90度；180：旋转180度；270：逆时针旋转90度;
 @return  是否出现异常：YES为正常。
 */

+ (BOOL)drawLineDashWithX:(CGFloat)x
                        y:(CGFloat)y
                    width:(CGFloat)width
                   height:(CGFloat)height
            fullLineWidth:(CGFloat)fullLineWidth
            dashLineWidth:(CGFloat)dashLineWidth
              orientation:(NSInteger)orientation;

/**
 蓝牙/Wi-Fi打印直线。
 
 @param   x               打印对象的位置，单位毫米。
 @param   y               打印对象的位置，单位毫米。
 @param   width           打印对象的尺寸，单位毫米。
 @param   height          打印对象的尺寸，单位毫米。
 @param   orientation     旋转角度：0：不旋转；90：顺时针旋转90度；180：旋转180度；270：逆时针旋转90度;
 @return  是否出现异常：YES为正常。
 */
+ (BOOL)drawLineWithX:(CGFloat)x
                    y:(CGFloat)y
                width:(CGFloat)width
               height:(CGFloat)height
          orientation:(NSInteger)orientation;

/**
 蓝牙/Wi-Fi打印矩形。
 
 @param   x               打印对象的位置，单位毫米。
 @param   y               打印对象的位置，单位毫米。
 @param   width           打印对象的尺寸，单位毫米。
 @param   height          打印对象的尺寸，单位毫米。
 @param   lineWidth       线条宽度，单位毫米。
 @param   fullLineWidth   打印对象为虚线时的实线部分尺寸，单位毫米。
 @param   dashLineWidth   打印对象为虚线时的虚线部分尺寸，单位毫米。
 @param   isDash          是否为虚线，NO:为x实线(会忽略fullLineWidth和dashLineWidth的值)，YES:为虚线。
 @param   isFilled        是否填充，NO:为虚线，YES:为填充(会忽略fullLineWidth和dashLineWidth的值)。
 @param   orientation     旋转角度：0：不旋转；90：顺时针旋转90度；180：旋转180度；270：逆时针旋转90度。
 @return  是否出现异常：YES为正常。
 */
+ (BOOL)drawRectangleWithX:(CGFloat)x
                         y:(CGFloat)y
                     width:(CGFloat)width
                    height:(CGFloat)height
                 lineWidth:(CGFloat)lineWidth
             fullLineWidth:(CGFloat)fullLineWidth
             dashLineWidth:(CGFloat)dashLineWidth
                    isDash:(BOOL)isDash
                  isFilled:(BOOL)isFilled
               orientation:(NSInteger)orientation;

/**
 蓝牙/Wi-Fi打印圆角矩形。
 
 @param   x               打印对象的位置，单位毫米。
 @param   y               打印对象的位置，单位毫米。
 @param   width           打印对象的尺寸，单位毫米。
 @param   height          打印对象的尺寸，单位毫米。
 @param   lineWidth       线条宽度，单位毫米。
 @param   radius          圆角的半径，单位毫米。
 @param   fullLineWidth   打印对象为虚线时的实线部分尺寸，单位毫米。
 @param   dashLineWidth   打印对象为虚线时的虚线部分尺寸，单位毫米。
 @param   isDash          是否为虚线NO:为x实线(会忽略fullLineWidth和dashLineWidth的值)，YES:为虚线。
 @param   isFilled        是否填充，NO:为虚线，YES:为填充(会忽略fullLineWidth和dashLineWidth的值)。
 @param   orientation     旋转角度：0：不旋转；90：顺时针旋转90度；180：旋转180度；270：逆时针旋转90度。
 @return  是否出现异常：YES为正常。
 */
+ (BOOL)drawRoundRectangleWithX:(CGFloat)x
                              y:(CGFloat)y
                          width:(CGFloat)width
                         height:(CGFloat)height
                      lineWidth:(CGFloat)lineWidth
                         radius:(CGFloat)radius
                  fullLineWidth:(CGFloat)fullLineWidth
                  dashLineWidth:(CGFloat)dashLineWidth
                         isDash:(BOOL)isDash
                       isFilled:(BOOL)isFilled
                    orientation:(NSInteger)orientation;

/**
 蓝牙/Wi-Fi打印椭圆。
 
 @param   x               打印对象的位置，单位毫米。
 @param   y               打印对象的位置，单位毫米。
 @param   width           打印对象的尺寸，单位毫米。
 @param   height          打印对象的尺寸，单位毫米。
 @param   lineWidth       线条宽度，单位毫米。
 @param   fullLineWidth   打印对象为虚线时的实线部分尺寸，单位毫米。
 @param   dashLineWidth   打印对象为虚线时的虚线部分尺寸，单位毫米。
 @param   isDash          是否为虚线NO:为x实线(会忽略fullLineWidth和dashLineWidth的值)，YES:为虚线。
 @param   isFilled        是否填充，NO:为虚线，YES:为填充(会忽略fullLineWidth和dashLineWidth的值)。
 @param   orientation     旋转角度：0：不旋转；90：顺时针旋转90度；180：旋转180度；270：逆时针旋转90度。
 @return  是否出现异常：YES为正常。
 */
+ (BOOL)drawEllipseWithX:(CGFloat)x
                       y:(CGFloat)y
                   width:(CGFloat)width
                  height:(CGFloat)height
               lineWidth:(CGFloat)lineWidth
           fullLineWidth:(CGFloat)fullLineWidth
           dashLineWidth:(CGFloat)dashLineWidth
                  isDash:(BOOL)isDash
                isFilled:(BOOL)isFilled
             orientation:(NSInteger)orientation;

/**
 蓝牙/Wi-Fi打印圆。
 
 @param   x               打印对象的位置，单位毫米。
 @param   y               打印对象的位置，单位毫米。
 @param   width           打印对象的尺寸，单位毫米。
 @param   lineWidth       线条宽度，单位毫米。
 @param   fullLineWidth   打印对象为虚线时的实线部分尺寸，单位毫米。
 @param   dashLineWidth   打印对象为虚线时的虚线部分尺寸，单位毫米。
 @param   isDash          是否为虚线NO:为x实线(会忽略fullLineWidth和dashLineWidth的值)，YES:为虚线。
 @param   isFilled        是否填充，NO:为虚线，YES:为填充(会忽略fullLineWidth和dashLineWidth的值)。
 @return  是否出现异常：YES为正常。
 */
+ (BOOL)drawCircleWithX:(CGFloat)x
                      y:(CGFloat)y
                  width:(CGFloat)width
              lineWidth:(CGFloat)lineWidth
          fullLineWidth:(CGFloat)fullLineWidth
          dashLineWidth:(CGFloat)dashLineWidth
                 isDash:(BOOL)isDash
               isFilled:(BOOL)isFilled;

/**
 蓝牙/Wi-Fi获取第几页预览图(在print:之前获取)。
 
 @param   index           第几页（从0开始）
 @return                  预览图
 */
+ (UIImage *)previewImage:(NSInteger)index;

/**
 蓝牙/Wi-Fi图片直接生成预览图图片。
 
 @param   image           图像对象。
 @param   width           打印对象的尺寸，单位毫米。
 @param   height          打印对象的尺寸，单位毫米。
 @param   orientation     旋转角度：0：不旋转；90：顺时针旋转90度；180：旋转180度；270：逆时针旋转90度。
 @return  预览图
 */
+ (UIImage *)drawpreviewImagFromImage:(UIImage *)image
      width:(CGFloat)width
     height:(CGFloat)height
orientation:(NSInteger)orientation;

/**
 蓝牙/Wi-Fi提交打印数据，进行打印，需先调用 endPage: 方法。
 
 @param   completion      打印结束回调（在发生异常后不会返回）
 */
+ (void)print:(DidPrinted_Block)completion;

/**
 蓝牙/Wi-Fi取消打印(打印未完成调用)。
 
 @param   completion      打印结束回调（在发生异常后不会返回）
 */
+ (void)canclePrint:(DidPrinted_Block)completion;

/**
 蓝牙/Wi-Fi打印完成(打印完成后调用)。
 
 @param   completion      打印结束回调（在发生异常后不会返回）
 */
+ (void)endPrint:(DidPrinted_Block)completion;

/**
 蓝牙/Wi-Fi打价器打印完成的份数(只对打价器有效，可能部分丢失，app做超时重置状态)。
 
 @param   count           打印完成的份数（在发生异常后不会返回）
 */
+ (void)getPrintingCountInfo:(PRINT_INFO)count;

/**
 蓝牙/Wi-Fi异常接收(连接成功后调用)。
 
 @param   error           打印异常：1:盒盖打开,
                                  2:缺纸,
                                  3:电量不足,
                                  4:电池异常,
                                  5:手动停止,
                                  6:数据错误,
                                  7:温度过高,
                                  8:出纸异常,
                                  9:纸张错误,
                                  10:SDK接收流程错误,
                                  11:请求打印失败(打印机正忙)
                                  A:rfid写入失败
                                  -1:拒绝/超时,做打印过程中复位用
                                  -2:表示蓝牙连接可能出问题了,app做对应处理(重新连接/重启蓝牙)
 */
+ (void)getPrintingErrorInfo:(PRINT_INFO)error;

/**
 蓝牙/Wi-Fi像素转毫米(会对像素进行处理)。
 
 @param   pixel           像素
 @return  绘制参数
 */
+ (CGFloat)pixelToMm:(CGFloat)pixel;

/**
 蓝牙/Wi-Fi毫米转像素(会对毫米进行处理)。

 @param  mm       毫米
 @return  绘制参数
 */
+ (CGFloat)mmToPixel:(CGFloat)mm;

/**
 蓝牙/Wi-Fi打印机分辨率

 @return  返回对应的打印机分辨率(连接打印机后有效)
 */
+ (NSInteger)printerResolution;

/**
 蓝牙更新打印机固件
 
 @param   type            升级的固件版本，数字型的字符串如@"101"。(APP暂定固定的@"100")
 @param   path            固件文件路径。
 @param   crcValue        CRC值，如@"0xab6c0d01"。
 @param   hVersion        固件版本号
 @param   completion      返回信息。
 异常码:
 1-打印机连接断开
 2-不支持固件升级
 3-打印机电量低
 4-固件文件校验失败
 5-拒绝固件升级请求
 6-通讯异常（固件升级过程中数据收发出现异常）
 7-手动取消升级
 8-升级失败
 9-开始升级 9_[0.06]-正在更新中([]里面表示进度
 11-升级成功
 12-固件版本号比对失败，比如 1.2和2.2大版本不同返回失败
 */
+ (void)updatePrinter:(NSString*)type crcValue:(NSString *)crcValue path:(NSString *)path hversion:(NSString *)hVersion sucess:(PRINT_INFO)completion;

/**
 蓝牙取消固件升级（在4-更新成功之前调用有效）

 @return  是否出现异常：YES为可以正常取消，
                     NO表示取消失败。最终取消状态在{更新打印机固件}接口返回
 */
+ (BOOL)stopUpdatePrinter;

/**
 蓝牙/Wi-Fi异常时获取打印相关信息（只支持b3s和打价宝系列打印机,连接成功后调用）
 
 @return  打印相关信息，(每次异常只能获取到一次数据)
 */
+(NSString *)getPrintingInfoWhenError;

@end

