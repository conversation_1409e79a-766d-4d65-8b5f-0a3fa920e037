<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/AsyncSocket.h</key>
		<data>
		GyStCmhuxAWLghA+fHLwPiSbVNI=
		</data>
		<key>Headers/AsyncUdpSocket.h</key>
		<data>
		oqLPl3ROAyiOqTaqhuupNjuxVU0=
		</data>
		<key>Headers/CocoaAsyncSocket.h</key>
		<data>
		fwx/DHzHzUQhtlfC9ffKAK+SFx0=
		</data>
		<key>Headers/GCDAsyncSocket.h</key>
		<data>
		/7aHrd4SpiKhqk1H7YRnTBSmJow=
		</data>
		<key>Headers/GCDAsyncUdpSocket.h</key>
		<data>
		LH5V3wgNYRH1dy/fNu8r4lu8puI=
		</data>
		<key>Info.plist</key>
		<data>
		hIIk3iGpMXJkFPisCMWGNsBtuQI=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		+n94rYTWDjekX3imyh+PSyA9vgA=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/AsyncSocket.h</key>
		<dict>
			<key>hash</key>
			<data>
			GyStCmhuxAWLghA+fHLwPiSbVNI=
			</data>
			<key>hash2</key>
			<data>
			FK+dN4jyAlPVkpV5Ai//YboXm9aUxPv3x3Q3viDjqTU=
			</data>
		</dict>
		<key>Headers/AsyncUdpSocket.h</key>
		<dict>
			<key>hash</key>
			<data>
			oqLPl3ROAyiOqTaqhuupNjuxVU0=
			</data>
			<key>hash2</key>
			<data>
			aTSbXrHSbcwPrz3/NoP6Q+jWZEwou6OSHQh/8WRkf/0=
			</data>
		</dict>
		<key>Headers/CocoaAsyncSocket.h</key>
		<dict>
			<key>hash</key>
			<data>
			fwx/DHzHzUQhtlfC9ffKAK+SFx0=
			</data>
			<key>hash2</key>
			<data>
			OeCNZd4h1R70nH11jcrFn8m1hnTuvTAHjDhMuhI/3Ao=
			</data>
		</dict>
		<key>Headers/GCDAsyncSocket.h</key>
		<dict>
			<key>hash</key>
			<data>
			/7aHrd4SpiKhqk1H7YRnTBSmJow=
			</data>
			<key>hash2</key>
			<data>
			bOafNUOs6luKovAV0sGzRLmHYf/vKZPF/Wg0IJD1zwQ=
			</data>
		</dict>
		<key>Headers/GCDAsyncUdpSocket.h</key>
		<dict>
			<key>hash</key>
			<data>
			LH5V3wgNYRH1dy/fNu8r4lu8puI=
			</data>
			<key>hash2</key>
			<data>
			CAMdz3VphGMLZyKBHhxLfGK4d70mHUiuHsG45jRPi+0=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			+n94rYTWDjekX3imyh+PSyA9vgA=
			</data>
			<key>hash2</key>
			<data>
			RoVn8xMeEnU3Izg0DtYjYL/krI8V7qw0sa7Ggf+08Rs=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
