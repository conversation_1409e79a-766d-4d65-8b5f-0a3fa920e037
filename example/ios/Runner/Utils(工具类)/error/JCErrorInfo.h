//
//  JCErrorInfo.h
//  ChenYin
//
//  Created by <PERSON> on 2020/5/4.
//  Copyright © 2020 JCChenYin. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface JCErrorInfo : NSObject

/** 用于弹窗的标题*/
@property(nonatomic, copy) NSString *errTitle;


@property(nonatomic, copy) NSString *errMsg;

/** 错误码 */
@property (nonatomic, assign) NSInteger code;

/** Domain*/
@property(nonatomic, copy) NSString *domain;

+(instancetype)errorInfoWithCode:(NSInteger)code msg:(NSString *)msg;

+ (instancetype)errorInfoWith:(NSInteger)code title:(NSString * _Nullable)title msg:(NSString *)msg;

+ (instancetype)errorInfoWithError:(NSError *)error;

@end

@interface NSError (Extension)

+ (instancetype)errorWithCode:(NSInteger)code localizedMsg:(NSString * _Nullable)msg;

@end

NS_ASSUME_NONNULL_END
