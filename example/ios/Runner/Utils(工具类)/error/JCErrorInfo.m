//
//  JCErrorInfo.m
//  ChenYin
//
//  Created by <PERSON> on 2020/5/4.
//  Copyright © 2020 JCChenYin. All rights reserved.
//

#import "JCErrorInfo.h"

static NSString * const ERROR_DOMAIN = @"com@";

@implementation JCErrorInfo

+(instancetype)errorInfoWithCode:(NSInteger)code msg:(NSString *)msg
{
    return [self errorInfoWith:code title:@"" msg:msg];
}

+ (instancetype)errorInfoWith:(NSInteger)code title:(NSString *)title msg:(NSString *)msg {
    return [[self alloc] initWith:code title:title msg:msg];
}

+ (instancetype)errorInfoWithError:(NSError *)error {
    return [[self alloc] initWithError:error];
}

- (instancetype)initWithError:(NSError *)error {
    self = [super init];
    if (self) {
        self.code = error.code;
        self.domain = error.domain;
        self.errMsg = error.localizedDescription;
    }
    return self;
}

- (instancetype)initWith:(NSInteger)code title:(NSString *)title msg:(NSString *)msg {
    self = [super init];
    if (self) {
        self.code = code;
        self.domain = ERROR_DOMAIN;
        self.errTitle = title;
        self.errMsg = msg;
    }
    return self;
}

@end

@implementation NSError (Extension)

+ (instancetype)errorWithCode:(NSInteger)code localizedMsg:(NSString * _Nullable)msg {
    NSDictionary *userInfo = nil;
    if (msg.length > 0) {
        userInfo = @{NSLocalizedDescriptionKey : msg};
    }
    NSError *error = [[self alloc] initWithDomain:ERROR_DOMAIN code:code userInfo:userInfo];
    return error;
}
@end
