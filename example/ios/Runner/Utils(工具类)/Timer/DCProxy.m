//
//  DCProxy.m
//  ChenYin
//
//  Created by <PERSON> on 2020/4/30.
//  Copyright © 2020 JCChenYin. All rights reserved.
//

#import "DCProxy.h"

@interface DCProxy ()

@property(nonatomic,weak,readwrite) id realObject;


@end

@implementation DCProxy

+ (instancetype)proxyWithTarget:(id)target {
    return [[self alloc] initWithTarget:target];
}

- (instancetype)initWithTarget:(id)target {
    self.realObject = target;
    return self;
}


-(BOOL)respondsToSelector:(SEL)aSelector
{
    return [_realObject respondsToSelector:aSelector];
}

#pragma mark - 消息转发

-(void)forwardInvocation:(NSInvocation *)invocation
{
    [invocation setTarget:_realObject];
    [invocation invoke];
}

-(NSMethodSignature *)methodSignatureForSelector:(SEL)sel
{
    return [_realObject methodSignatureForSelector:sel];
}

#pragma mark - NSObject Protocol

- (BOOL)isEqual:(id)object {
    return [_realObject isEqual:object];
}

- (NSUInteger)hash {
    return [_realObject hash];
}

- (Class)superclass {
    return [_realObject superclass];
}

- (Class)class {
    return [_realObject class];
}

- (BOOL)isProxy {
    return YES;
}

- (BOOL)isKindOfClass:(Class)aClass {
    return [_realObject isKindOfClass:aClass];
}

- (BOOL)isMemberOfClass:(Class)aClass {
    return [_realObject isMemberOfClass:aClass];
}

- (BOOL)conformsToProtocol:(Protocol *)aProtocol {
    return [_realObject conformsToProtocol:aProtocol];
}

- (NSString *)description {
    return [_realObject description];
}

- (NSString *)debugDescription {
    return [_realObject debugDescription];
}

@end
