//
//  DCGCDTimer.m
//  ChenYin
//
//  Created by <PERSON> on 2020/4/30.
//  Copyright © 2020 JCChenYin. All rights reserved.
//

#import "DCGCDTimer.h"

@interface DCGCDTimer ()

@property(nonatomic, strong) dispatch_source_t timer;

@property(nonatomic,assign) BOOL repeats;

@property(nonatomic,assign) BOOL onMainThread;


@end

@implementation DCGCDTimer

+ (instancetype)timerWithTimeAfterInterval:(NSTimeInterval)ti repeats:(BOOL)yesOrNo onMainThread:(BOOL)onMainThread block:(nonnull void (^)(void))block {
    return [[self alloc] initWithTimeAfterInterval:ti repeats:yesOrNo onMainThread:onMainThread block:block];
}

- (instancetype)initWithTimeAfterInterval:(NSTimeInterval)ti repeats:(BOOL)repeats onMainThread:(BOOL)onMainThread block:(nonnull void (^)(void))block  {
    
    if (self = [super init]) {
        dispatch_queue_t queue;
        if (onMainThread) {
            queue = dispatch_get_main_queue();
        }else{
            queue = dispatch_queue_create("com.niimbot.cprinter.gcdTimerQueue", DISPATCH_QUEUE_SERIAL);
            if (queue == nil) {
                return nil;
            }
            dispatch_set_target_queue(queue, dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0));
        }
        self.repeats = repeats;
        self.onMainThread = onMainThread;
        self.timer = dispatch_source_create(DISPATCH_SOURCE_TYPE_TIMER, 0, 0, queue);
        dispatch_time_t startTime = dispatch_time(DISPATCH_TIME_NOW, ti *NSEC_PER_SEC);
        dispatch_source_set_timer(self.timer, startTime, ti, 0);
        XYWeakSelf;
        void (^eventHandle)(void) = ^{
            if (weakSelf && weakSelf.isValid) {
                if (weakSelf.onMainThread  && ![DCGCDTimer isOnMainThread]) {
                    dispatch_async(dispatch_get_main_queue(), ^{
                        if (block) {
                           block();
                        }
                    });
                }else{
                    if (block) {
                        block();
                    }
                }
            }
            if (!weakSelf.repeats) {
                [weakSelf invalidate];
            }
        };
        
        dispatch_source_set_event_handler(self.timer, eventHandle);
        _isValid = YES;
        
        dispatch_resume(self.timer);
        
        
    }
    return self;
}


- (void)invalidate {
    if (_isValid) {
        _isValid = NO;
        dispatch_source_cancel(_timer);
    }
}

+ (BOOL)isOnMainThread {
    return [NSOperationQueue.currentQueue isEqual:NSOperationQueue.mainQueue] || [NSThread isMainThread];
}

@end
