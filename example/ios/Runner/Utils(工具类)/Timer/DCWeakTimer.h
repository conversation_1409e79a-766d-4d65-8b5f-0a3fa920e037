//
//  DCWeakTimer.h
//  ChenYin
//
//  Created by <PERSON> on 2020/4/30.
//  Copyright © 2020 JCChenYin. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN
/**
目前并不是线程安全的, 不要在子线程中`fire`
自身需要被持有, 对`target`没有强引用
*/
@interface DCWeakTimer : NSObject

/**
 启动一个定时器

 @param ti 定时器的时间间隔
 @param aTarget 执行任务的对象
 @param aSelector 执行的操作
 @param userInfo 额外信息
 @param yesOrNo 是否需要重复
 @param runLoop 添加到哪个`runLoop`
 @param mode runLoop mode
 @return 定时器
 */
+ (instancetype)timerWithTimeInterval:(NSTimeInterval)ti target:(id)aTarget selector:(SEL)aSelector userInfo:(nullable id)userInfo repeats:(BOOL)yesOrNo runLoop:(NSRunLoop *)runLoop mode:(NSString *)mode;

/**
 启动一个定时器
 
 默认添加到当前运行循环, 因此在子线程调用时会在子线程回调

 @param ti 时间间隔
 @param aTarget 执行任务的对象
 @param aSelector 执行的操作
 @param userInfo 额外信息
 @param yesOrNo 是否需要重复
 @return 定时器
 */
+ (instancetype)scheduledTimerWithTimeInterval:(NSTimeInterval)ti target:(id)aTarget selector:(SEL)aSelector userInfo:(nullable id)userInfo repeats:(BOOL)yesOrNo;

/**
 销毁定时器
 */
- (void)invalidate;

/**
 *  启动/关闭定时器
 */
- (void)setFireDate:(NSDate*)date;

/** 额外信息 */
@property (nullable, readonly, strong) id userInfo;

@end

NS_ASSUME_NONNULL_END
