//
//  DCGCDTimer.h
//  ChenYin
//
//  Created by <PERSON> on 2020/4/30.
//  Copyright © 2020 JCChenYin. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface DCGCDTimer : NSObject

/**
 是否还在正常计时
 @note 可能会在子线程中更改
 */
@property (nonatomic, readonly) BOOL isValid;

/**
 销毁定时器
 */
- (void)invalidate;

/**
 启动一个定时器

 @param ti 定时器间隔
 @param yesOrNo 是否需要重复
 @param onMainThread 是否需要在主线程回调
 @param block 定时器更新间隔到达时执行的任务
 @return 定时器
 */
+ (instancetype)timerWithTimeAfterInterval:(NSTimeInterval)ti
                                   repeats:(BOOL)yesOrNo
                              onMainThread:(BOOL)onMainThread
                                     block:(void (^)(void))block;

@end

NS_ASSUME_NONNULL_END
