//
//  DCProxy.h
//  ChenYin
//
//  Created by <PERSON> on 2020/4/30.
//  Copyright © 2020 JCChenYin. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface DCProxy : NSProxy

@property(nonatomic,weak,readonly) id realObject;

/**
创建一个代理对象

@param target 被代理的对象
@return 代理对象
*/
+ (instancetype)proxyWithTarget:(id)target;


/**
创建一个代理对象

@param target 被代理的对象
@return 代理对象
*/
- (instancetype)initWithTarget:(id)target;

@end

NS_ASSUME_NONNULL_END
