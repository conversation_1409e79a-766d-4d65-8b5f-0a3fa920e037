//
//  DCWeakTimer.m
//  ChenYin
//
//  Created by <PERSON> on 2020/4/30.
//  Copyright © 2020 JCChenYin. All rights reserved.
//

#import "DCWeakTimer.h"
#import <objc/runtime.h>
#import "DCProxy.h"

@interface DCWeakTimer ()

@property (nonatomic, weak) NSTimer *timer;
@property (nullable, readwrite, strong) id userInfo;

@end

@implementation DCWeakTimer

+ (instancetype)timerWithTimeInterval:(NSTimeInterval)ti
                               target:(id)aTarget
                             selector:(SEL)aSelector
                             userInfo:(id)userInfo
                              repeats:(BOOL)yesOrNo
                              runLoop:(nonnull NSRunLoop *)runLoop
                                 mode:(nonnull NSString *)mode
{
    
    return [[self alloc] initWithTimeInterval:ti target:aTarget selector:aSelector userInfo:userInfo repeats:yesOrNo runLoop:runLoop mode:mode];
    
}

+ (instancetype)scheduledTimerWithTimeInterval:(NSTimeInterval)ti target:(id)aTarget selector:(SEL)aSelector userInfo:(id)userInfo repeats:(BOOL)yesOrNo
{
    
    return [self timerWithTimeInterval:ti target:aTarget selector:aSelector userInfo:userInfo repeats:yesOrNo runLoop:[NSRunLoop currentRunLoop] mode:NSDefaultRunLoopMode];
}

- (instancetype)initWithTimeInterval:(NSTimeInterval)ti
                              target:(id)aTarget
                            selector:(SEL)aSelector
                            userInfo:(id)userInfo
                             repeats:(BOOL)yesOrNo
                             runLoop:(nonnull NSRunLoop *)runLoop
                                mode:(nonnull NSString *)mode
{
    if (self = [super init]) {
        DCProxy *wTarget = [DCProxy proxyWithTarget:aTarget];
        NSTimer *timer = [NSTimer timerWithTimeInterval:ti target:wTarget selector:aSelector userInfo:userInfo repeats:yesOrNo];
        [runLoop addTimer:timer forMode:mode];
        self.timer = timer;
        self.userInfo = [userInfo copy];
    }
    return self;
}


- (void)invalidate
{
    if (self.timer) {
         [self.timer invalidate];
    }
   
}

- (void)setFireDate:(NSDate*)date
{
    [self.timer setFireDate:date];
}

@end
