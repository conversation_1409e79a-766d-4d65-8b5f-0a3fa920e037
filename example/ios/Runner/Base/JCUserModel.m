//
//  JCUserModel.m
//  ChenYin
//
//  Created by <PERSON> on 2020/4/29.
//  Copyright © 2020 JCChenYin. All rights reserved.
//

#import "JCUserModel.h"
#import "NSObject+JKAutoCoding.h"

@implementation JCAddressModel

@end


@implementation JCSocialModel

@end


@implementation JCUserModel


+(JSONKeyMapper*)keyMapper
{

    NSMutableDictionary *mutableDic = [@{@"userId":@"id"} mutableCopy];

    return [[JSONKeyMapper alloc] initWithModelToJSONDictionary:[mutableDic copy]];
}


@end
