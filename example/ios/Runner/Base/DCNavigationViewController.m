//
//  DCNavigationViewController.m
//  ChenYin
//
//  Created by <PERSON> on 2020/4/22.
//  Copyright © 2020 JCChenYin. All rights reserved.
//

#import "DCNavigationViewController.h"
#import <objc/runtime.h>

@interface DCNavigationViewController ()<UIGestureRecognizerDelegate,UINavigationControllerDelegate>

@property(nonatomic,strong) id <UINavigationControllerDelegate> delegateProxy;


@end

@implementation DCNavigationViewController



- (void)viewDidLoad {
    [super viewDidLoad];
    //恢复边缘滑动手势
    if ([self respondsToSelector:@selector(interactivePopGestureRecognizer)]) {
        self.interactivePopGestureRecognizer.delegate = self;
    }
    
    self.delegate = self;
   
}

#pragma mark - 返回上一级
- (void)backBtnClicked {
    [self popViewControllerAnimated:YES];
}

- (UIButton *)getBackBtnItem
{
    UIButton *backBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    backBtn.frame = CGRectMake(0, 0, 24, 24);
    backBtn.imageEdgeInsets = UIEdgeInsetsMake(0, -12, 0, 0);
    [backBtn setImage:XY_IMAGE_NAMED(@"icn_back") forState:UIControlStateNormal];
    [backBtn addTarget:self action:@selector(backBtnClicked) forControlEvents:UIControlEventTouchUpInside];
    return backBtn;
}

#pragma mark - proxy
- (BOOL)respondsToSelector:(SEL)aSelector
{
    return [super respondsToSelector:aSelector] || ([self shouldRespondDelegateProxyWithSelector:aSelector] && [self.delegateProxy respondsToSelector:aSelector]);
}

- (void)forwardInvocation:(NSInvocation *)anInvocation
{
    if ([self.delegateProxy respondsToSelector:anInvocation.selector]) {
        [anInvocation invokeWithTarget:self.delegateProxy];
    }
}

- (NSMethodSignature *)methodSignatureForSelector:(SEL)aSelector
{
     return [super methodSignatureForSelector:aSelector] ?: [(id)self.delegateProxy methodSignatureForSelector:aSelector];
    
}
- (BOOL)shouldRespondDelegateProxyWithSelector:(SEL)aSelector
{
    // 只重写了一个方法
    return [NSStringFromSelector(aSelector) isEqualToString:@"navigationController:willShowViewController:animated:"];
}

#pragma mark - gesture delegate
- (BOOL)gestureRecognizerShouldBegin:(UIGestureRecognizer *)gestureRecognizer {
   
    BOOL viewTransitionInProgress = [objc_getAssociatedObject(self, NSSelectorFromString(@"viewTransitionInProgress")) boolValue];
    if (viewTransitionInProgress) {
        return NO;
    }
    if (self.childViewControllers.count == 1) { // 根控制器
        return NO;
    }
    return YES;
}

#pragma mark - navigationController delegate

- (void)pushViewController:(UIViewController *)viewController animated:(BOOL)animated {
    if (self.childViewControllers.count == 1) { // 只有rootVc
        viewController.hidesBottomBarWhenPushed = YES; // 隐藏二级界面底部的`tabbar`
    }
    [super pushViewController:viewController animated:animated];
}

// 调用时机: 在 viewWillAppear: 之后
- (void)navigationController:(UINavigationController *)navigationController willShowViewController:(UIViewController *)viewController animated:(BOOL)animated {
    // 如果不是rootVc,更改左侧返回按钮
    if (self.childViewControllers.count > 1) {
        UIBarButtonItem *leftItem = [[UIBarButtonItem alloc] initWithCustomView:[self getBackBtnItem]];
        viewController.navigationItem.leftBarButtonItem = leftItem;
    }
}


@end
