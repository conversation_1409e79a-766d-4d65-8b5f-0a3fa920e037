//
//  JCUserModel.h
//  ChenYin
//
//  Created by <PERSON> on 2020/4/29.
//  Copyright © 2020 JCChenYin. All rights reserved.
//

#import "JSONModel.h"

NS_ASSUME_NONNULL_BEGIN

/**机器系列*/
static NSString  *JC_B16 = @"B16";
static NSString  *JC_D11 = @"D11";
static NSString  *JC_B21 = @"B21";

@interface JCAddressModel : JSONModel

@property(nonatomic,copy)NSString <Optional> *city;

@property(nonatomic,copy)NSString <Optional> *cityCode;

@property(nonatomic,copy)NSString <Optional> *country;

@property(nonatomic,copy)NSString <Optional> *countryCode;

@property(nonatomic,copy)NSString <Optional> *detail;

@property(nonatomic,copy)NSString <Optional> *district;

@property(nonatomic,copy)NSString <Optional> *districtCode;

@property(nonatomic,copy)NSString <Optional> *province;

@property(nonatomic,copy)NSString <Optional> *provinceCode;

@end

@interface JCSocialModel : JSONModel

/**用户昵称*/
@property(nonatomic,copy)NSString <Optional> *displayName;

/**用户图像*/
@property(nonatomic,copy)NSString <Optional> *profileUrl;

/**方式*/
@property(nonatomic,copy)NSString <Optional> *providerId;

/**对应openId*/
@property(nonatomic,copy)NSString <Optional> *providerUserId;

/**用户id*/
@property(nonatomic,copy)NSString <Optional> *userId;

@end


@protocol JCSocialModel;



@interface JCUserModel : JSONModel

/**用户id*/
@property(nonatomic,copy)NSString <Optional> *userId;


@property(nonatomic,copy)NSString <Optional> *uid;

/**账户*/
@property(nonatomic,copy)NSString <Optional> *unionId;
/**账户*/
@property(nonatomic,copy)NSString <Optional> *phone;

/**用户图像*/
@property(nonatomic,copy)NSString <Optional> *profileUrl;

/**用户昵称*/
@property(nonatomic,copy)NSString <Optional> *nickName;
/**国际码*/
@property(nonatomic,copy) NSString <Optional> *areaCode;

/**用户照片*/
@property(nonatomic,copy)NSString <Optional> *imageUrl;
/**设备唯一编码*/
@property(nonatomic,copy)NSString <Optional> *deviceSerial;
/**是否完善用户信息*/
@property(nonatomic,copy)NSNumber <Optional> *improved;

/**第三方登录用户唯一标识*/
@property(nonatomic,copy)NSString <Optional> *providerUserId;

@property(nonatomic,copy)JCAddressModel <Optional> *address;

@property(nonatomic,copy)NSArray <Optional,JCSocialModel> *userConnectDtos;

/**登录方式(密码/验证码),可用值:PASSWORD,CODE,WECHAT,QQ,WEIBO,DD*/
@property(nonatomic,copy)NSString <Optional> *loginType;

//@property(nonatomic,assign) BOOL enabled;



@end


NS_ASSUME_NONNULL_END
