//
//  SettingPowerOffController.m
//  Runner
//
//  Created by <PERSON> on 2021/3/9.
//

#import "SettingPowerOffController.h"
#import "DetailViewCell.h"
#import "JCDeviceFirmwareRemoteModel.h"

@interface SettingPowerOffController ()<UITableViewDelegate,UITableViewDataSource>

@property(nonatomic,strong) UITableView *tableView;

@property(nonatomic,strong) DCHUDHelper *hud;

@property(nonatomic,copy) NSArray *gearsArray;

@property(nonatomic,assign) int gears;

@property(nonatomic,copy) XYBlock selectBlock;


@end

@implementation SettingPowerOffController

- (instancetype)initWithGearsArray:(NSArray *)gearsArray cuttentGears:(int)gears selectBlock:(XYBlock)selectBlock
{
    if (self = [super init]) {
        self.gearsArray = gearsArray;
        self.gears = gears;
        self.selectBlock = selectBlock;
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = XY_HEX_RGB(0xF2F2F2);
    self.title = @"设置自动关机";
    [self.view addSubview:self.tableView];
}
-(void)viewWillAppear:(BOOL)animated
{
    [super viewWillAppear:animated];
    [self.navigationController setNavigationBarHidden:NO animated:YES];
}


- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    return 60;
}

- (NSInteger)tableView:(nonnull UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return 4;
}

- ( UITableViewCell *)tableView:(nonnull UITableView *)tableView cellForRowAtIndexPath:(nonnull NSIndexPath *)indexPath {
    
    AutoPowerOffCell *cell = [tableView dequeueReusableCellWithIdentifier:AutoPowerOffCellIdentifier forIndexPath:indexPath];
    JCShutdownModel *model = self.gearsArray[indexPath.row];
    if (indexPath.row == 0) {
        cell.connerStyle = CellConnerStyleTop;
    }
    if (indexPath.row == 3) {
        cell.connerStyle = CellConnerStyleBottom;
    }
    cell.selectIcon.hidden = self.gears != [model.gear intValue];
    cell.title = [NSString stringWithFormat:@"%@",model.minute];
    return cell;

}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    [tableView deselectRowAtIndexPath:indexPath animated:NO];
    JCShutdownModel *model = self.gearsArray [indexPath.row];
    //1-4(1:15min;2:30min3:45min4:60min)
    NSInteger type = [model.gear integerValue];
    XYWeakSelf;
//    [JCAPI setPrintState:27 type:type sucess:^(NSDictionary *printDicInfo) {
//        if([@"0" isEqualToString:printDicInfo[@"statusCode"]]){
//             weakSelf.gears = [model.gear intValue];
//                [weakSelf.tableView reloadData];
//                if (weakSelf.selectBlock) {
//                    weakSelf.selectBlock(model.gear);
//                }
//         }else{
//             [DCHUDHelper showMessage:@"设置失败"];
//         }
//    }];
}


- (UITableView *)tableView
{
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectMake(16,XY_NaviBarMaxY+ 20, kSCREEN_WIDTH - 32, kSCREEN_HEIGHT-XY_NaviBarMaxY-20) style:UITableViewStylePlain];
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.showsHorizontalScrollIndicator = NO;
        _tableView.backgroundColor =XY_HEX_RGB(0xF2F2F2);
        _tableView.delegate = self;
        _tableView.dataSource = self;
        [_tableView registerClass:[AutoPowerOffCell class] forCellReuseIdentifier:AutoPowerOffCellIdentifier];
       
    }
    return _tableView;

}

@end
