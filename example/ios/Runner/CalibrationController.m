//
//  CalibrationController.m
//  Runner
//
//  Created by <PERSON> on 2021/3/9.
//

#import "CalibrationController.h"
#import "DetailViewCell.h"
#import "JCAPI.h"


@interface CalibrationController ()<UITableViewDelegate,UITableViewDataSource>

@property(nonatomic,strong) UITableView *tableView;

@property(nonatomic,strong) DCHUDHelper *hud;

@property(nonatomic,copy) NSArray *paperNameArray;

@property(nonatomic,copy) NSArray *paperTypeArray;



@end

@implementation CalibrationController

- (instancetype)initWithPaperArray:(NSArray *)nameArray typeArray:(NSArray *)typeArray
{
    if (self = [super init]) {
        self.paperNameArray = nameArray;
        self.paperTypeArray = typeArray;
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = XY_HEX_RGB(0xF2F2F2);
    self.title = @"设备校准";
    [self.view addSubview:self.tableView];
}

-(void)viewWillAppear:(BOOL)animated
{
    [super viewWillAppear:animated];
    [self.navigationController setNavigationBarHidden:NO animated:YES];
}



- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    return 60;
}

- (NSInteger)tableView:(nonnull UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.paperNameArray.count;
}

- ( UITableViewCell *)tableView:(nonnull UITableView *)tableView cellForRowAtIndexPath:(nonnull NSIndexPath *)indexPath {
    
    CalibrationCell *cell = [tableView dequeueReusableCellWithIdentifier:CalibrationCellIdentifier forIndexPath:indexPath];
    if (indexPath.row == 0) {
        cell.connerStyle = CellConnerStyleTop;
    } else if (indexPath.row == self.paperNameArray.count - 1) {
        cell.connerStyle = CellConnerStyleBottom;
    } else {
        cell.connerStyle = CellConnerStyleMiddle;
    }
    cell.title = self.paperNameArray[indexPath.row];
    int type = [self.paperTypeArray[indexPath.row] intValue];
    XYWeakSelf;
    cell.clickBlock = ^{
        weakSelf.hud = [DCHUDHelper show];
//        [JCAPI setPrintState:29 type:type sucess:^(NSDictionary *printDicInfo) {
//               NSString *message = @"";
//               if([@"0" isEqualToString:printDicInfo[@"statusCode"]]){
//                   message = XY_LANGUAGE_TITLE_NAMED(@"app00929", @"纸张校准成功");
//               }else if([@"-1" isEqualToString:printDicInfo[@"statusCode"]]){
//                   message = XY_LANGUAGE_TITLE_NAMED(@"app00901", @"纸张校准失败");
//               }else if([@"-2" isEqualToString:printDicInfo[@"statusCode"]]){
//                   
//               }else if([@"-3" isEqualToString:printDicInfo[@"statusCode"]]){
//                   
//               }
//               [weakSelf.hud hideAnimated:YES];
//            [DCHUDHelper showMessage:message];
//           }];
    };
    return cell;

}


- (UITableView *)tableView
{
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectMake(16,XY_NaviBarMaxY+ 20, kSCREEN_WIDTH - 32, kSCREEN_HEIGHT-XY_NaviBarMaxY-20) style:UITableViewStylePlain];
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.showsHorizontalScrollIndicator = NO;
        _tableView.backgroundColor =XY_HEX_RGB(0xF2F2F2);
        _tableView.delegate = self;
        _tableView.dataSource = self;
        [_tableView registerClass:[CalibrationCell class] forCellReuseIdentifier:CalibrationCellIdentifier];
       
    }
    return _tableView;

}

@end
