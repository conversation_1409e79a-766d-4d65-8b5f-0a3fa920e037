//
//  detailHeaderCell.m
//  Runner
//
//  Created by <PERSON> on 2021/1/22.
//

#import "detailHeaderCell.h"

@interface detailHeaderCell ()

@property(nonatomic,strong) UIImageView* deviceImage;

@property(nonatomic,strong) UIImageView *connectIcon;

@property(nonatomic,strong) UILabel *connectLabel;

@property(nonatomic,strong) UILabel *nameLabel;

@end

@implementation detailHeaderCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.contentView.backgroundColor = XY_HEX_RGB(0xF2F2F2);
        [self setupUI];
    }
    return self;
}

- (void)setupUI
{
    UIView *cornerView = [[UIView alloc] init];
    cornerView.backgroundColor = COLOR_WHITE;
    cornerView.layer.cornerRadius = 10;
    cornerView.layer.masksToBounds = YES;
    [self.contentView addSubview:cornerView];
    
    UIImageView *image = [[UIImageView alloc] init];
    [cornerView addSubview:image];
    self.deviceImage = image;
    
    UILabel *nameLabel = [[UILabel alloc] init];
    nameLabel.font = XY_S_BOLD_FONT(16);
    nameLabel.textColor = XY_HEX_RGB(0x262626);
    nameLabel.textAlignment = NSTextAlignmentLeft;
    [cornerView addSubview:nameLabel];
    self.nameLabel = nameLabel;
    
    UIImageView *connectIcon = [[UIImageView alloc] init];
    [cornerView addSubview:connectIcon];
    self.connectIcon = connectIcon;
    
    UILabel *connectLabel = [[UILabel alloc] init];
    connectLabel.font = XY_S_FONT(14);
    connectLabel.textColor = XY_HEX_RGB(0x999999);
    connectLabel.textAlignment = NSTextAlignmentLeft;
    [cornerView addSubview:connectLabel];
    self.connectLabel = connectLabel;
    
    UIButton *disconnectBtn = [[UIButton alloc] initWithFrame:CGRectMake(0, 0, 60, 30)];
    disconnectBtn.layer.cornerRadius = 15;
    disconnectBtn.backgroundColor = [UIColor jk_colorWithHex:0xF5F5F5];
    NSAttributedString *attStr = [[NSAttributedString alloc] initWithString:@"断开" attributes:@{
        NSFontAttributeName:MY_FONT_Regular(14),
        NSForegroundColorAttributeName:[UIColor jk_colorWithHex:0x262626]
    }];
    [disconnectBtn setAttributedTitle:attStr forState:UIControlStateNormal];
    [disconnectBtn addTarget:self action:@selector(disconnectAction) forControlEvents:UIControlEventTouchUpInside];
    [cornerView addSubview:disconnectBtn];

    XYWeakSelf;
    [cornerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(weakSelf);
        
    }];
    
    [image mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(cornerView).offset(10);
        make.centerY.equalTo(cornerView);
        make.size.mas_equalTo(CGSizeMake(52, 52));
    }];
    
    [nameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(image.mas_trailing).offset(16);
        make.top.equalTo(image);
    }];
    
    [connectIcon mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(nameLabel);
        make.bottom.equalTo(cornerView).offset(-16);
        make.size.mas_equalTo(CGSizeMake(15, 15));
        
    }];
    
    [connectLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(connectIcon.mas_trailing).offset(4);
        make.centerY.equalTo(connectIcon);
    }];
    
    [disconnectBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_equalTo(cornerView);
        make.trailing.mas_equalTo(cornerView.mas_trailing).offset(-13);
        make.height.mas_equalTo(30);
        make.width.mas_equalTo(60);
    }];

}

- (void)disconnectAction{
    //断开连接
    if (self.disconnect) {
        self.disconnect();
    }
}
- (void)configWithName:(NSString *)name IsConnect:(BOOL)connect
{
    if ([name hasPrefix:@"B21"]) {
        self.deviceImage.image  = XY_IMAGE_NAMED(@"B21_connect_icn");
    } else if ([name hasPrefix:@"D110"]){
        self.deviceImage.image  = XY_IMAGE_NAMED(@"d110_connect_icon");
    } else {
        self.deviceImage.image = XY_IMAGE_NAMED(@"D11_connect_icn");
    }
    self.nameLabel.text = name;
    self.connectIcon.image = connect ?XY_IMAGE_NAMED(@"icn_connected"):XY_IMAGE_NAMED(@"icn_disconnected");
    self.connectLabel.text = connect ? @"已连接":@"未连接";
}

@end
