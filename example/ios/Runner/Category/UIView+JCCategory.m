//
//  UIView+JCCategory.m
//  MallPackage
//
//  Created by xiaoyao on 2020/6/26.
//  Copyright © 2020 luxiguang. All rights reserved.
//

#import "UIView+JCCategory.h"


@implementation UIView (JCCategory)

- (CGFloat)jc_top
{
    return self.frame.origin.y;
}

- (void)setJc_top:(CGFloat)jc_top
{
    CGRect newframe = self.frame;
    newframe.origin.y = jc_top;
    self.frame = newframe;
}

- (CGFloat)jc_centerX{
    return self.center.x;
}

- (void)setJc_centerX:(CGFloat)jc_centerX{
    CGPoint newCenter = self.center;
    newCenter.x = jc_centerX;
    self.center = newCenter;
}

- (CGFloat)jc_height
{
    return self.frame.size.height;
}

- (void)setJc_height:(CGFloat)jc_height
{
    CGRect newframe = self.frame;
    newframe.size.height = jc_height;
    self.frame = newframe;
}
@end
