//
//  NSTimer+addition.h
//  MallPackage
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/6/22.
//  Copyright © 2020 luxiguang. All rights reserved.
//
#import <Foundation/Foundation.h>

@interface NSTimer (addition)

- (void)pause;
- (void)resume;
- (void)resumeWithTimeInterval:(NSTimeInterval)time;

+ (NSTimer *)wy_scheduledTimerWithTimeInterval:(NSTimeInterval)ti repeats:(BOOL)yesOrNo block:(void(^)(NSTimer *timer))block;

@end
