//
//  BlueToothCell.m
//  Runner
//
//  Created by <PERSON> on 2020/11/24.
//

#import "BlueToothCell.h"
#import "UIView+EasyExtend.h"
#import "JCApplicationManager.h"
@interface BlueToothCell ()

@property(nonatomic,strong) UIImageView *icon;

@property(nonatomic,strong) UILabel *nameLabel;

@property(nonatomic,strong) UILabel *connectLable;

@property(nonatomic,strong) UIImageView *connectIcon;
@property(nonatomic,weak)UIButton *disconnectButton;

@end

@implementation BlueToothCell

-(instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        self.backgroundColor = XY_HEX_RGB(0xF5F5F5);
        [self setupUI];
    }
    return self;
}

- (void)setupUI
{
    UIView *backView = [[UIView alloc] initWithFrame:CGRectMake(16, 0, kSCREEN_WIDTH - 32, 86)];
    backView.backgroundColor = [UIColor whiteColor];
    backView.layer.cornerRadius = 10;
    backView.layer.masksToBounds =YES;
    [self.contentView addSubview:backView];
    
    UIImageView *icon = [[UIImageView alloc] initWithFrame:CGRectMake(10, 12, 52, 52)];
    icon.contentMode = UIViewContentModeScaleAspectFit;
    self.icon = icon;
    [backView addSubview:icon];
   
    
    UILabel *nameLable = [UILabel new];
    nameLable.font = XY_S_FONT(16);
    nameLable.textColor = XY_HEX_RGB(0x262626);
    nameLable.textAlignment = NSTextAlignmentLeft;
    nameLable.left = 80;
    nameLable.top = 14;
    nameLable.height = 21;
    self.nameLabel = nameLable;
    [backView addSubview:nameLable];
    
    UILabel *connectLabel = [UILabel new];
    connectLabel.font = XY_S_FONT(14);
    connectLabel.textColor = XY_HEX_RGB(0x999999);
    connectLabel.textAlignment = NSTextAlignmentLeft;
    connectLabel.left = 98;
    connectLabel.top = 42;
    connectLabel.height = 20;
    self.connectLable = connectLabel;
    [backView addSubview:connectLabel];
    
    UIImageView *connectIcon = [[UIImageView alloc] initWithImage:XY_IMAGE_NAMED(@"icn_connected")];
    connectIcon.hidden = YES;
    connectIcon.frame = CGRectMake(81, 42, 15, 15);
    self.connectIcon = connectIcon;
    [backView addSubview:connectIcon];
    
    UIButton *disconnectBtn = [[UIButton alloc] initWithFrame:CGRectMake(0, 0, 60, 30)];
    disconnectBtn.layer.cornerRadius = 15;
    disconnectBtn.backgroundColor = [UIColor jk_colorWithHex:0xF5F5F5];
    NSAttributedString *attStr = [[NSAttributedString alloc] initWithString:@"断开" attributes:@{
        NSFontAttributeName:MY_FONT_Regular(14),
        NSForegroundColorAttributeName:[UIColor jk_colorWithHex:0x262626]
    }];
    [disconnectBtn setAttributedTitle:attStr forState:UIControlStateNormal];
    [disconnectBtn addTarget:self action:@selector(disconnectAction) forControlEvents:UIControlEventTouchUpInside];
    [backView addSubview:disconnectBtn];
    self.disconnectButton = disconnectBtn;
    disconnectBtn.centerY = backView.centerY;
    disconnectBtn.right = backView.width - 13;
}

- (void)disconnectAction{
    //断开连接
    if (self.disconnect) {
        self.disconnect();
    }
}
- (void)setModel:(JCBluetoothModel *)model
{
    _model = model;
    self.nameLabel.text = model.name;
    [self.nameLabel sizeToFit];
    //D110和D11通过前缀判断要有先后顺序
    if ([model.name hasPrefix:@"D110"]){
       JCDevicesSeriesModel *d110 = [[JCApplicationManager shareInstance] mapPrinterName:model.name];
       [self.icon sd_setImageWithURL:[NSURL URLWithString:[d110.prototypeUrl stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]]] placeholderImage: XY_IMAGE_NAMED(@"d110_connect_icon")];
   }else if ([model.name hasPrefix:@"D11"]) {
        JCDevicesSeriesModel *d11= [[JCApplicationManager shareInstance] mapPrinterName:model.name];
        [self.icon sd_setImageWithURL:[NSURL URLWithString:[d11.prototypeUrl stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]]] placeholderImage: XY_IMAGE_NAMED(@"D11_connect_icn")];
    }else if ([model.name hasPrefix:@"B21"]){
        JCDevicesSeriesModel *b21= [[JCApplicationManager shareInstance] mapPrinterName:model.name];
        [self.icon sd_setImageWithURL:[NSURL URLWithString:[b21.prototypeUrl stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]]] placeholderImage: XY_IMAGE_NAMED(@"B21_connect_icn")];
    }else{
        JCDevicesSeriesModel *device= [[JCApplicationManager shareInstance] mapPrinterName:model.name];
        [self.icon sd_setImageWithURL:[NSURL URLWithString:[device.prototypeUrl stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]]] completed:^(UIImage * _Nullable image, NSError * _Nullable error, SDImageCacheType cacheType, NSURL * _Nullable imageURL) {
            
        }];
    }
  
}

- (void)setIsConnect:(BOOL)isConnect
{
    _isConnect = isConnect;
    self.connectIcon.hidden = ! isConnect;
    self.connectLable.text = isConnect ? @"已连接":@"未连接";
    self.connectLable.left = isConnect ?102 :80;
    [self.connectLable sizeToFit];
    
    self.disconnectButton.hidden = !_isConnect;
}


@end
