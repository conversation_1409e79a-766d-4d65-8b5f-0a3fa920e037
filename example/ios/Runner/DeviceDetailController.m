//
//  DeviceDetiailController.m
//  Runner
//
//  Created by <PERSON> on 2021/1/22.
//

#import "DeviceDetailController.h"
#import "JCApplicationManager.h"
#import "JCBluetoothManager.h"
#import "DetailViewCell.h"
#import "detailHeaderCell.h"
#import "DeviceUpdateUtil.h"
#import "JCDeviceUpdateAleart.h"
#import "CommonCtrl.h"
#import "CalibrationController.h"
#import "SettingPowerOffController.h"
#import "JCBluetoothManager.h"
#import "DCHUDHelper.h"
#import "DevicesSearchController.h"

@interface DeviceDetailController ()<UITableViewDelegate,UITableViewDataSource>

@property(nonatomic,strong) UITableView *tableView;

@property(nonatomic,strong) DeviceUpdateUtil *util;

@property(nonatomic,strong) NSMutableArray *contentArray;

@property(nonatomic,strong) DCHUDHelper *hud;

@property(nonatomic,strong) JCUpdataProessAleart *progressAleart;

@property(nonatomic,copy) NSString *gears;

@property(nonatomic,strong) DCHUDHelper *helper;

@end

@implementation DeviceDetailController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = XY_HEX_RGB(0xF2F2F2);
    self.title = @"设备详情";
    UIButton *rightbutton =  [CommonCtrl buttonOnlyWithImage:XY_IMAGE_NAMED(@"detai_refresh") ControlState:UIControlStateNormal StateImage:@"detai_refresh" target:self action:@selector(refreshButtonDidClick)];
          [rightbutton sizeToFit];
    UIBarButtonItem *rightItem =  [[UIBarButtonItem alloc] initWithCustomView:rightbutton];
    self.navigationItem.rightBarButtonItem = rightItem;
      
    
    [self.view addSubview:self.tableView];
    [self loadUpdateInfoAutoconnect:NO];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(openStateDidUpdate:) name:PrinterStatusNotification object:nil];
   
}

-(void)viewWillAppear:(BOOL)animated
{
    [super viewWillAppear:animated];
    [self.navigationController setNavigationBarHidden:NO animated:YES];
}

- (void)viewWillDisappear:(BOOL)animated
{
    [super viewWillDisappear:animated];
    [self.navigationController setNavigationBarHidden:YES animated:NO];
}

- (void)refreshButtonDidClick
{
    if (!JC_IS_CONNECTED_PRINTER) {
        [DCHUDHelper showMessage:@"请连接设备"];
        return;
    }
    [self loadUpdateInfoAutoconnect:NO];
}

- (void)loadUpdateInfoAutoconnect:(BOOL)autoconnect
{
    if (!JC_IS_CONNECTED_PRINTER) {
        return;
    }
    XYWeakSelf;
    self.hud = [DCHUDHelper show];
    [self.util requestUpdateMsgDeviceReconnect:autoconnect Handle:^(id x) {
        [weakSelf.tableView reloadData];
        } Success:^(__kindof YTKBaseRequest * _Nonnull request, JCDeviceFirmwareRemoteModel  *updateModel) {
            weakSelf.contentArray = nil;
            if ([updateModel.isCalibration boolValue]
                && updateModel.paperType!= nil
                && updateModel.paperType.count > 0) {
                [weakSelf.contentArray addObject:@"走纸校准"];
            }
            /*
             * 屏蔽自动关机设置功能
             *
            if (updateModel.isAutoShutdown &&updateModel.shutdownTimes!= nil && !STR_IS_NIL(weakSelf.util.gears)) {
                weakSelf.gears =weakSelf.util.gears;
                [weakSelf.contentArray addObject:@"自动关机"];
            }
             */
            [weakSelf.hud hideAnimated:YES];
            [weakSelf.tableView reloadData];
        } failure:^(YTKBaseRequest * _Nullable request, NSError * _Nullable error) {
            [weakSelf.hud hideAnimated:YES];
            [weakSelf.tableView reloadData];
        }];
    
}

-(void)openStateDidUpdate:(NSNotification*)info
{
  
    if (JC_IS_CONNECTED_PRINTER) {
        [self loadUpdateInfoAutoconnect:YES];
        [self.tableView reloadData];
     
    }else{
        [self.hud hideAnimated:YES];
        [self.tableView reloadData];
    }
}
- (void)disconnect{
XYWeakSelf;
[JCBlUETOOTH_MANAGER closeConnected];
self.helper =  [DCHUDHelper showWithStatus:XY_LANGUAGE_TITLE_NAMED(@"app", @"正在断开连接...")];
dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
[weakSelf.helper onSuccess:XY_LANGUAGE_TITLE_NAMED(@"app", @"已断开连接")];
    [self.navigationController popViewControllerAnimated:true];
});
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return 2;
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section
{
    return 20;
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section
{
    return [UIView new];
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section
{
    return 0.01;
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section
{
    return [UIView new];
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    if (indexPath.section == 0) {
        return 76;
    }
    return 60;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    if (section == 0) {
        return 1;
    }
    return self.contentArray.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    NSString *printName = JC_CURRENT_CONNECTED_PRINTER;
    BOOL connect = JC_IS_CONNECTED_PRINTER;
    if (indexPath.section == 0) {
        detailHeaderCell *cell = [tableView dequeueReusableCellWithIdentifier:detailHeaderCellIndentifer forIndexPath:indexPath];
        [cell configWithName:printName IsConnect:connect];
        cell.disconnect = ^{
            [self disconnect];
        };
        return cell;
    }else{
        DetailViewCell *cell = [tableView dequeueReusableCellWithIdentifier:detailViewCellIdentifier forIndexPath:indexPath];
        NSString *name = self.contentArray[indexPath.row];
        cell.title = name;
        if (indexPath.row == 0) {
            cell.detailInfo = connect ? self.util.hardware :@"";
        }else if(indexPath.row == 1){
            cell.detailInfo = connect ? self.util.firmware :@""; ;
            cell.interactionEnabled = YES;
            cell.needUpdata =connect ? self.util.updateModel.needUpgrade :NO;
        }else if([name isEqualToString:@"走纸校准"]){
            cell.interactionEnabled = YES;
            cell.detailInfo = @"";
        }else{
            if (connect) {
                for (JCShutdownModel *shutdownModel in self.util.updateModel.shutdownTimes) {
                    if ([shutdownModel.gear isEqualToString:self.gears]) {
                       cell.detailInfo  = shutdownModel.minute;
                        break;
                    }
                }
            }else{
                cell.detailInfo = @"";
            }
            cell.interactionEnabled = YES;
        }
        
        if (indexPath.row == self.contentArray.count - 1) {
            cell.connerStyle = CellConnerStyleBottom;
        } else if (indexPath.row == 0) {
            cell.connerStyle = CellConnerStyleTop;
        } else {
            cell.connerStyle = CellConnerStyleMiddle;
        }
        
        cell.selectionStyle = UITableViewCellSelectionStyleNone;
        return cell;
    }
   
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    
    if (indexPath.section != 1) return;

    if (indexPath.row == 1 && JC_IS_CONNECTED_PRINTER ) {
        if (!self.util.updateModel) {
            [self loadUpdateInfoAutoconnect:NO];
        }else{
            if (self.util.updateModel.needUpgrade) {
                XYWeakSelf;
                [JCDeviceUpdateAleart aleartWithTitle:@"固件可更新"
                                             subtitle:[NSString stringWithFormat:@"最新固件版本V%@更新内容:",self.util.updateModel.version]
                                              Message:UN_NIL(self.util.updateModel.updateInfo)
                                         ComfirmBlock:^{
                    [weakSelf.util beginUpdateFinishHandle:^{

                        [weakSelf loadUpdateInfoAutoconnect:YES];
                    }];
                    } cancelBlock:^{
                                
                }];
            }else{
                [DCHUDHelper showMessage:@"当前已是最新版本"];
            }
        }
    }
    NSString *name = self.contentArray[indexPath.row];
    if ([name isEqualToString:@"走纸校准"]) {
        // 排重
        NSMutableArray *paperTypeNames = [[NSMutableArray alloc] init];
        [self.util.updateModel.paperTypeNames enumerateObjectsUsingBlock:^(id  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            if (![paperTypeNames containsObject:obj]) {
                [paperTypeNames addObject:obj];
            }
        }];
        
        NSMutableArray *paperTypes = [[NSMutableArray alloc] init];
        [self.util.updateModel.paperType enumerateObjectsUsingBlock:^(id  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            if (![paperTypes containsObject:obj]) {
                [paperTypes addObject:obj];
            }
        }];
        
        CalibrationController * calibrationVC = [[CalibrationController alloc] initWithPaperArray:paperTypeNames
                                                                                        typeArray:paperTypes];
        [self.navigationController pushViewController:calibrationVC animated:YES];
    }
    
    if ([name isEqualToString:@"自动关机"]) {
        XYWeakSelf;
        SettingPowerOffController *powerOffVC = [[SettingPowerOffController alloc] initWithGearsArray:self.util.updateModel.shutdownTimes cuttentGears:[self.gears intValue] selectBlock:^(id x) {
            if (![weakSelf.gears isEqualToString:x]) {
                weakSelf.gears = x;
                [weakSelf.tableView reloadData];
            }
        }];
        [self.navigationController pushViewController:powerOffVC animated:YES];
    }
}


- (UITableView *)tableView
{
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectMake(16, 0, kSCREEN_WIDTH - 32, kSCREEN_HEIGHT) style:UITableViewStyleGrouped];
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.showsHorizontalScrollIndicator = NO;
        _tableView.backgroundColor =XY_HEX_RGB(0xF2F2F2);
        _tableView.delegate = self;
        _tableView.dataSource = self;
        [_tableView registerClass:[detailHeaderCell class] forCellReuseIdentifier:detailHeaderCellIndentifer];
        [_tableView registerClass:[DetailViewCell class] forCellReuseIdentifier:detailViewCellIdentifier];
    }
    return _tableView;

}

-(DeviceUpdateUtil *)util
{
    
    if (!_util) {
        _util = [[DeviceUpdateUtil alloc] init];
    }
    return _util;
    

}

- (NSMutableArray *)contentArray
{
    if (!_contentArray) {
        _contentArray = [@[@"硬件版本",@"固件版本"] mutableCopy];
    }
    return _contentArray;
}

@end
