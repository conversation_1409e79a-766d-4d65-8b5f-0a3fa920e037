//
//  JCElementModel+Transfer.m
//  XYFrameWork
//
//  Created by xingling xu on 2020/3/25.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import "JCElementModel+Transfer.h"
#import "JCExcelForElement.h"
//#import "JCTMDataBindGoodsInfoManager.h"

#define SameJCPorpertyNumber(property)   [[NSString stringWithFormat:@"%.1f",self.property] isEqualToString:[NSString stringWithFormat:@"%.1f",anotherModel.property]]
#define SameFloat(property)   fabsf(self.property - anotherModel.property) <= 2
//([[NSString stringWithFormat:@"%.0f",self.property] isEqualToString:[NSString stringWithFormat:@"%.0f",anotherModel.property]])
#define SameInteger(property)   (self.property == anotherModel.property)
#define SameString(property)    ([self.property isEqualToString:anotherModel.property])
#define SameArray(property)     ([self.property isEqualToArray:anotherModel.property])

// 将本地的 0⊙1 包装成 ${0⊙1}
#define ServerTextValue(value)  IsExcelString(value)?[NSString stringWithFormat:@"${%@}",value]:value

/** 字符串属性 */
#define dict_set_key_value_string(key,string)   if (string){[dict setObject:string forKey:key];}
/** 字符串属性 */
#define dict_set_key_value_object(key,object)   if (object){[dict setObject:object forKey:key];}
/** 数字类型属性 */
#define dict_set_key_value_number(key,number)   [dict setObject:@(number) forKey:key]
/** bool属性转换 */
#define dict_set_key_value_bool(key,boolValue)  [dict setObject:boolValue?@(1):@(0) forKey:key]

NSString *const JCElementModel2SdkPropertyKey       = @"JCElementModel2SdkPropertyKey";
NSString *const JCElementModelShowPlaceHolder       = @"JCElementModelShowPlaceHolder";
NSString *const KJCElementModelCurrentPageIndex     = @"KJCElementModelCurrentPageIndex";
NSString *const KJCElementModelExternalData         = @"KJCElementModelExternalData";
NSString *const KJCElementModelTask                 = @"KJCElementModelTask";
NSString *const KJCElementModelCurrentCopyNumber    = @"KJCElementModelCurrentCopyNumber";
NSString *const KJCElementTotalCopyNumber           = @"KJCElementTotalCopyNumber";

@implementation JCElementModel (Transfer)

- (NSDictionary *)elementDict {
    NSMutableDictionary *dict = [NSMutableDictionary dictionary];
    [dict addEntriesFromDictionary:[self commonProperty]];
    NSString *type = self.type;
    NSAssert(type.length > 0, @"type must not nil");
    if ([type isEqualToString:@"text"]) {
        [dict addEntriesFromDictionary:[self textSeparateProperty]];
    } else if ([type isEqualToString:@"date"]) {
        [dict addEntriesFromDictionary:[self dateSeparateProperty]];
    } else if ([type isEqualToString:@"serial"]) {
        [dict addEntriesFromDictionary:[self serialSeparateProperty]];
    } else if ([type isEqualToString:@"barcode"]) {
        [dict addEntriesFromDictionary:[self barcodeSeparateProperty]];
    } else if ([type isEqualToString:@"qrcode"]) {
        [dict addEntriesFromDictionary:[self qrcodeSeparateProperty]];
    } else if ([type isEqualToString:@"line"]) {
        [dict addEntriesFromDictionary:[self lineSeparateProperty]];
    } else if ([type isEqualToString:@"graph"]) {
        [dict addEntriesFromDictionary:[self graphSeparateProperty]];
    } else if ([type isEqualToString:@"table"]) {
        [dict addEntriesFromDictionary:[self tableSeparateProperty]];
    } else if ([type isEqualToString:@"image"]) {
        [dict addEntriesFromDictionary:[self imageSeparateProperty]];
    }
    return dict;
}

/** 元素公共属性 */
- (NSDictionary *)commonProperty {
    NSMutableDictionary *dict = [NSMutableDictionary dictionaryWithCapacity:9];
    dict_set_key_value_string(@"id",self.elementId);
    dict_set_key_value_string(@"type",self.type);
    dict_set_key_value_number(@"x",self.x);
    dict_set_key_value_number(@"y",self.y);
    dict_set_key_value_number(@"width",self.width);
    dict_set_key_value_number(@"height",self.height);
    dict_set_key_value_number(@"rotate",self.rotate);
    dict_set_key_value_number(@"zIndex",self.zIndex);
    dict_set_key_value_bool(@"isTitle", self.isTitle);
    if (![self forSdk]) {
        dict_set_key_value_bool(@"isLock",self.isLock);
        dict_set_key_value_string(@"fieldName", self.fieldName);
    }
    return dict;
}

/** 文本单独属性 */
- (NSDictionary *)textSeparateProperty {
    NSMutableDictionary *dict = [NSMutableDictionary dictionaryWithCapacity:10];
    dict_set_key_value_string(@"value",ServerTextValue(self.value));
    if ([self forSdk]) {
        dict_set_key_value_string(@"fontFamily",self.fontCode);
    } else {
        dict_set_key_value_string(@"fontFamily",self.fontFamily);
    }
    dict_set_key_value_string(@"fontCode",self.fontCode);
    dict_set_key_value_number(@"lineMode",self.lineMode);
    dict_set_key_value_number(@"wordSpacing",self.wordSpacing);
    
   
    if ([self forSdk]) {
//        if (!STR_IS_NIL(self.fieldName) && STR_IS_NIL(self.value) && [self getShowPlaceHolder]) {
//            dict_set_key_value_string(@"value",[JCTMDataBindGoodsInfoManager getPlaceHolderValueWithFieldName:self.fieldName]);
//        } else {
            NSString *content = [self elementValue:self.value withExcelIndex:[self getCurrentPageIndex] elementId:self.elementId];
            dict_set_key_value_string(@"value",UN_NIL(content));
//        }
        dict_set_key_value_number(@"letterSpacing",self.letterSpacing);
        dict_set_key_value_number(@"lineSpacing",self.lineSpacing);
        dict_set_key_value_number(@"fontSize",self.fontSize);
    } else {
        dict_set_key_value_number(@"fontSize",[([NSString stringWithFormat:@"%.1f",self.fontSize]) floatValue]);
        dict_set_key_value_number(@"letterSpacing",[([NSString stringWithFormat:@"%.1f",self.letterSpacing]) floatValue]);
        dict_set_key_value_number(@"lineSpacing",[([NSString stringWithFormat:@"%.1f",self.lineSpacing]) floatValue]);
    }
    dict_set_key_value_number(@"textAlignHorizonral",self.textAlignHorizonral);
    dict_set_key_value_number(@"textAlignVertical",self.textAlignVertical);
    dict_set_key_value_object(@"fontStyle",self.fontStyle);
    return dict;
}

/** 流水号单独属性 */
- (NSDictionary *)serialSeparateProperty {
    NSMutableDictionary *dict = [NSMutableDictionary dictionaryWithCapacity:16];
    [dict addEntriesFromDictionary:[self textSeparateProperty]];
    if (![self forSdk]) {
        dict_set_key_value_string(@"prefix",self.prefix);
        dict_set_key_value_string(@"suffix",self.suffix);
        dict_set_key_value_string(@"startNumber",self.startNumber);
        dict_set_key_value_number(@"fixLength",self.fixLength);
        dict_set_key_value_string(@"fixValue",self.fixValue);
        dict_set_key_value_number(@"incrementValue",self.incrementValue);
    } else {
        NSInteger totalCopyNumber = [self getTotalCopyNumber];
        NSInteger currentCopyNumber = [self getCurrentCopyNumber];
        NSInteger currentPageIndex = [self getCurrentPageIndex];
        NSInteger incrementValue = self.incrementValue;
        NSInteger beginNumber;
        if (totalCopyNumber <= 1) {
            beginNumber = self.startNumber.integerValue + currentPageIndex*incrementValue;
        } else {
            beginNumber = self.startNumber.integerValue + currentCopyNumber*incrementValue;
        }
        NSString *text = StringFromInt(beginNumber);
        NSMutableString *string = [NSMutableString string];
        if (text.length < self.startNumber.length) {
            NSInteger number = self.startNumber.length - text.length;
            for (NSInteger i = 0; i < number; i ++) {
                [string appendString:@"0"];
            }
        }
        if (!STR_IS_NIL(string)) {// 补0操作
            text = [NSString stringWithFormat:@"%@%@",string,text];
        }
        NSString *prefix = self.prefix;
        if (prefix && prefix.length > 0) {
            text = [NSString stringWithFormat:@"%@%@",prefix,text];
        }
        NSString *suffix = self.suffix;
        if (suffix && suffix.length > 0) {
            text = [NSString stringWithFormat:@"%@%@",text,suffix];
        }
        dict_set_key_value_string(@"value",UN_NIL(text));
    }
    return dict;
}

/** 日期单独属性 */
- (NSDictionary *)dateSeparateProperty {
    NSMutableDictionary *dict = [NSMutableDictionary dictionaryWithCapacity:13];
    [dict addEntriesFromDictionary:[self textSeparateProperty]];
    dict_set_key_value_string(@"dateFormat",self.dateFormat);
    dict_set_key_value_string(@"timeFormat",self.timeFormat);
    dict_set_key_value_bool(@"dateIsRefresh", self.dateIsRefresh);
    // 将时间戳改为格式字符串
    NSString *value = [self getDateString];
    // sdk value传格式后的字符串  服务端传时间戳
    NSString *result = [self forSdk]?value:UN_NIL(self.value);
    dict_set_key_value_string(@"value", result);
    return dict;
}

/** 条码单独属性 */
- (NSDictionary *)barcodeSeparateProperty {
    NSMutableDictionary *dict = [NSMutableDictionary dictionaryWithCapacity:5];
    dict_set_key_value_string(@"value",ServerTextValue(self.value));
    if ([self forSdk]) {
        NSString *content = [self elementValue:self.value withExcelIndex:[self getCurrentPageIndex] elementId:self.elementId];
        dict_set_key_value_string(@"value", UN_NIL(content));
    }
    dict_set_key_value_number(@"textPosition",self.textPosition);
    dict_set_key_value_number(@"codeType",self.codeType);
    dict_set_key_value_number(@"fontSize",self.fontSize);
    dict_set_key_value_number(@"textHeight",self.textHeight);
    return dict;
}

/** 二维码单独属性 */
- (NSDictionary *)qrcodeSeparateProperty {
    NSMutableDictionary *dict = [NSMutableDictionary dictionaryWithCapacity:3];
    if ([self forSdk]) {
        NSString *value = [self elementValue:self.value withExcelIndex:[self getCurrentPageIndex] elementId:self.elementId];
        dict_set_key_value_string(@"value",STR_IS_NIL(value)?@"0":UN_NIL(value));
    } else {
        dict_set_key_value_string(@"value",ServerTextValue(self.value));
    }
    dict_set_key_value_number(@"correctLevel",self.correctLevel);
    dict_set_key_value_number(@"codeType",self.codeType);
    if (self.codeType != JCCodeType_PDF417 && self.height != self.width) {
        dict_set_key_value_number(@"height",MIN(self.width, self.height));
    }
    return dict;
}

/** 线条单独属性 */
- (NSDictionary *)lineSeparateProperty {
    NSMutableDictionary *dict = [NSMutableDictionary dictionaryWithCapacity:2];
    if ([self forSdk]) {
        dict_set_key_value_number(@"lineWidth",self.lineWidth);
        dict_set_key_value_number(@"height",self.lineWidth);
    } else {
        dict_set_key_value_number(@"lineWidth",[([NSString stringWithFormat:@"%.1f",self.lineWidth]) floatValue]);
    }
    dict_set_key_value_object(@"dashwidth", self.dashwidth);
    dict_set_key_value_number(@"lineType",self.lineType);
    return dict;
}

/** 几何图形单独属性 */
- (NSDictionary *)graphSeparateProperty {
    NSMutableDictionary *dict = [NSMutableDictionary dictionaryWithCapacity:3];
    dict_set_key_value_number(@"graphType",self.graphType);
    dict_set_key_value_number(@"lineType",self.lineType);
    if ([self forSdk]) {
        dict_set_key_value_number(@"lineWidth",self.lineWidth);
    } else {
        dict_set_key_value_number(@"lineWidth",[([NSString stringWithFormat:@"%.1f",self.lineWidth]) floatValue]);
    }
    if (self.dashwidth.count == 0) {
        CGFloat dsw = dash_line_default_width;
        dict_set_key_value_object(@"dashwidth",(@[@(dsw),@(dsw)]));
    } else {
        dict_set_key_value_object(@"dashwidth",self.dashwidth);
    }
    CGFloat cornerRadius = self.cornerRadius;
    if (self.graphType ==  JCGraphTypeRectRound && self.cornerRadius*2 > self.width || self.cornerRadius * 2 > self.height) {
        cornerRadius = MIN(self.width/2, self.height/2)-0.1;
    }
    dict_set_key_value_number(@"cornerRadius",cornerRadius);
    return dict;
}

/** 图片单独属性 */
- (NSDictionary *)imageSeparateProperty {
    NSMutableDictionary *dict = [NSMutableDictionary dictionaryWithCapacity:4];
    if ([self forSdk]) {// 仅针对sdk提供 imageData 参数  服务端只保存url
        NSString *localPath = ImageLocalPath(self.imageUrl);
        NSData *data = [NSData dataWithContentsOfFile:localPath];
        NSString *encodedImageStr;
        if (STR_IS_NIL(self.imageData)) {
            encodedImageStr =[data base64EncodedStringWithOptions:NSDataBase64Encoding64CharacterLineLength];
        } else {
            encodedImageStr = self.imageData;
        }
        dict_set_key_value_string(@"imageData",encodedImageStr);
    }
    dict_set_key_value_string(@"imageUrl",self.imageUrl);
    dict_set_key_value_number(@"imageProcessingType",self.imageProcessingType);
    dict_set_key_value_object(@"imageProcessingValue", self.imageProcessingValue);
    dict_set_key_value_string(@"materialId",self.materialId);
    dict_set_key_value_string(@"materialType",self.materialType);
    return dict;
}

/** 表格单独属性 */
- (NSDictionary *)tableSeparateProperty {
    NSMutableDictionary *dict = [NSMutableDictionary dictionaryWithCapacity:6];
    dict_set_key_value_number(@"imageProcessingType",self.imageProcessingType);
    dict_set_key_value_number(@"row",self.row);
    dict_set_key_value_number(@"column",self.column);
    dict_set_key_value_object(@"rowHeight", self.rowHeight);
    dict_set_key_value_object(@"columnWidth", self.columnWidth);
    dict_set_key_value_string(@"combineId",self.combineId);
    if ([self forSdk]) {
        dict_set_key_value_number(@"lineWidth",self.lineWidth);
    } else {
        dict_set_key_value_number(@"lineWidth",[([NSString stringWithFormat:@"%.1f",self.lineWidth]) floatValue]);
    }
    dict_set_key_value_number(@"lineType",self.lineType);
    NSMutableArray *cells = [NSMutableArray arrayWithCapacity:self.cells.count];
    [self.cells enumerateObjectsUsingBlock:^(JCElementModel * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        // 如果表格是往sdk传，那么内部的文本也往sdk转
        [obj injectExternalData:[self getExternalData] task:[self getTask]];
        // 配置sdk与否
        obj = obj.toSdk([self forSdk]);
        // 配置当前excel分页
        obj = obj.setCurrentPageIndex([self getCurrentPageIndex]);
        
        NSDictionary *temp = [self sigleGridProperty:obj];
        [cells addObject:temp];
    }];
    dict_set_key_value_object(@"cells", cells);
    NSMutableArray *combineCells = [NSMutableArray arrayWithCapacity:self.combineCells.count];
    [self.combineCells enumerateObjectsUsingBlock:^(JCElementModel * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        // 如果表格是往sdk传，那么内部的文本也往sdk转
        [obj injectExternalData:[self getExternalData] task:[self getTask]];
        // 配置sdk与否
        obj = obj.toSdk([self forSdk]);
        // 配置当前excel分页
        obj = obj.setCurrentPageIndex([self getCurrentPageIndex]);
        NSDictionary *temp = [self combineGridProperty:obj];
        [combineCells addObject:temp];
    }];
    dict_set_key_value_object(@"combineCells", combineCells);
    
    return dict;
}

/** 单独的单元格文本信息 */
- (NSDictionary *)sigleGridProperty:(JCElementModel *)cellModel {
    NSMutableDictionary *dict = [NSMutableDictionary dictionaryWithCapacity:14];
    dict_set_key_value_string(@"value",ServerTextValue(cellModel.value));
    dict_set_key_value_string(@"type",@"text");
    if ([cellModel forSdk]) {
        dict_set_key_value_string(@"fontFamily",cellModel.fontCode);
    } else {
        dict_set_key_value_string(@"fontFamily",cellModel.fontFamily);
    }
    dict_set_key_value_string(@"fontCode",cellModel.fontCode);
    dict_set_key_value_number(@"lineMode",cellModel.lineMode);
    dict_set_key_value_number(@"wordSpacing",cellModel.wordSpacing);
    dict_set_key_value_number(@"letterSpacing",cellModel.letterSpacing);
    dict_set_key_value_number(@"lineSpacing",cellModel.lineSpacing);
    if ([cellModel forSdk]) {
        NSString *content = [self elementValue:cellModel.value withExcelIndex:[cellModel getCurrentPageIndex] elementId:cellModel.elementId];
        dict_set_key_value_string(@"value",UN_NIL(content));
        dict_set_key_value_number(@"fontSize",cellModel.fontSize);
    } else {
        dict_set_key_value_number(@"fontSize",cellModel.fontSize);
    }
    dict_set_key_value_number(@"textAlignHorizonral",cellModel.textAlignHorizonral);
    dict_set_key_value_number(@"textAlignVertical",cellModel.textAlignVertical);
    dict_set_key_value_object(@"fontStyle",cellModel.fontStyle);
    dict_set_key_value_string(@"id", cellModel.elementId);
    dict_set_key_value_string(@"combineId",cellModel.combineId);
    dict_set_key_value_number(@"rowIndex",cellModel.rowIndex);
    dict_set_key_value_number(@"columnIndex",cellModel.columnIndex);
    return dict;
}

/** 合并单元格信息 */
- (NSDictionary *)combineGridProperty:(JCElementModel *)cellModel {
    return [self sigleGridProperty:cellModel];
}

#pragma mark - sdk数据同服务端数据区分
- (JCElementModel *(^)(BOOL transfer2sdk))toSdk {
    return ^(BOOL transfer2sdk){
        objc_setAssociatedObject(self, &JCElementModel2SdkPropertyKey, @(transfer2sdk), OBJC_ASSOCIATION_ASSIGN);
        return self;
    };
}

- (BOOL)forSdk {
    id object = objc_getAssociatedObject(self, &JCElementModel2SdkPropertyKey);
    return [object integerValue] > 0 ? YES:NO;
}

- (JCElementModel *(^)(BOOL showHolder))showPlaceHolder {
    return ^(BOOL showHolder){
        objc_setAssociatedObject(self, &JCElementModelShowPlaceHolder, @(showHolder), OBJC_ASSOCIATION_ASSIGN);
        return self;
    };
}

- (BOOL)getShowPlaceHolder {
    id object = objc_getAssociatedObject(self, &JCElementModelShowPlaceHolder);
    return [object integerValue] > 0 ? YES:NO;
}

#pragma mark - Excel数据相关
- (void)injectExternalData:(NSDictionary *)externalData task:(NSDictionary *)task {
    if (externalData) {
        objc_setAssociatedObject(self, &KJCElementModelExternalData, externalData, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    }
    if (task) {
        objc_setAssociatedObject(self, &KJCElementModelTask, task, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    }
}

- (NSDictionary *)getExternalData {
    NSDictionary *object = objc_getAssociatedObject(self, &KJCElementModelExternalData);
    if (object && [object isKindOfClass:[NSDictionary class]]) {
        return object;
    }
    return nil;
}

- (NSDictionary *)getTask {
    NSDictionary *object = objc_getAssociatedObject(self, &KJCElementModelTask);
    if (object && [object isKindOfClass:[NSDictionary class]]) {
        return object;
    }
    return nil;
}

- (JCElementModel *(^)(NSInteger currentIndex))setCurrentPageIndex {
    return ^(NSInteger currentIndex){
        objc_setAssociatedObject(self, &KJCElementModelCurrentPageIndex, @(currentIndex), OBJC_ASSOCIATION_ASSIGN);
        return self;
    };
}

- (NSInteger)getCurrentPageIndex {
    id object = objc_getAssociatedObject(self, &KJCElementModelCurrentPageIndex);
    return [object integerValue];
}

- (JCElementModel *(^)(NSInteger copyNumber))setCurrentCopyNumber {
    return ^(NSInteger copyNumber){
        objc_setAssociatedObject(self, &KJCElementModelCurrentCopyNumber, @(copyNumber), OBJC_ASSOCIATION_ASSIGN);
        return self;
    };
}

- (NSInteger)getCurrentCopyNumber {
    id object = objc_getAssociatedObject(self, &KJCElementModelCurrentCopyNumber);
    return [object integerValue];
}

- (JCElementModel *(^)(NSInteger copyNumber))setTotalCopyNumber {
    return ^(NSInteger copyNumber){
        objc_setAssociatedObject(self, &KJCElementTotalCopyNumber, @(copyNumber), OBJC_ASSOCIATION_ASSIGN);
        return self;
    };
}

- (NSInteger)getTotalCopyNumber {
    id object = objc_getAssociatedObject(self, &KJCElementTotalCopyNumber);
    return [object integerValue];
}

- (BOOL)hasExcelImport {
    __block BOOL value = NO;
    if (self.templateType == JCTemplateType_Table) {
        [self.cells enumerateObjectsUsingBlock:^(JCElementModel *obj, NSUInteger idx, BOOL * _Nonnull stop) {
            if (IsExcelString(obj.value)) {
                value = YES;
                *stop = YES;
            }
        }];
    } else {
        value = IsExcelString(self.value);
    }
    return value;
}

- (NSInteger)currentExcelNumber {
    NSInteger excelNumber = 1;
    __block NSString *excelValue = @"";
    if (self.templateType == JCTemplateType_Table) {
        [self.cells enumerateObjectsUsingBlock:^(JCElementModel *obj, NSUInteger idx, BOOL * _Nonnull stop) {
            if (IsExcelString(obj.value)) {
                excelValue = obj.value;
                *stop = YES;
            }
        }];
    } else {
        excelValue = self.value;
    }
    NSString *simpleValue = [excelValue getExcelValue];
    NSArray *array = [simpleValue componentsSeparatedByString:excel_component];
    if (array.count == 2) {
        NSInteger columnIndex = [array.lastObject integerValue];
        excelNumber = [DrawBoardInfo getExcelNumbersWithExcelColumnInex:columnIndex];
    }
    return excelNumber;
}

- (BOOL)isSameWith:(JCElementModel *)anotherModel {
    if (!SameString(type)) return NO;
    BOOL la = SameFloat(x);
    NSString *y1 = [NSString stringWithFormat:@"%.1f",self.height];
    NSString *y2 = [NSString stringWithFormat:@"%.1f",anotherModel.height];
    la = SameFloat(y);
    !la? NSLog(@"y is not same"):nil;
    la = SameFloat(width);
    !la? NSLog(@"width is not same"):nil;
    la = SameFloat(height);
    !la? NSLog(@"height is not same"):nil;
    la = SameInteger(rotate);
    !la? NSLog(@"rotate is not same"):nil;
    la = SameString(fieldName);
    !la? NSLog(@"fieldName is not same"):nil;
    NSString *v1 = [self.value getExcelValue];
    NSString *v2 = [anotherModel.value getExcelValue];
    if (self.templateType != JCTemplateType_SerialNumber) {
        BOOL valueIsSame = [[self.value getExcelValue] isEqualToString:[anotherModel.value getExcelValue]];
        la = valueIsSame & la;
        !la? NSLog(@"value is not same"):nil;
    }
    BOOL value = SameFloat(x) && SameFloat(y) && (SameFloat(width) || SameFloat(height)) && SameInteger(rotate) && SameString(fieldName);
    if (![self.type isEqualToString:@"date"]) {
        value = value & la;
    }
    if (!value) return NO;
    if (self.templateType == JCTemplateType_Text || self.templateType == JCTemplateType_Date || self.templateType == JCTemplateType_SerialNumber) {
        value = SameInteger(textAlignHorizonral) && SameInteger(textAlignVertical) && SameInteger(lineMode) && SameJCPorpertyNumber(wordSpacing) && SameJCPorpertyNumber(letterSpacing) && SameJCPorpertyNumber(lineSpacing) && SameString(fontCode) && SameArray(fontStyle) && SameJCPorpertyNumber(fontSize);
        NSString *date1 = [self.dateFormat stringByTrimmingCharactersInSet:[NSCharacterSet characterSetWithCharactersInString:@"无"]];
        NSString *date2 = [anotherModel.dateFormat stringByTrimmingCharactersInSet:[NSCharacterSet characterSetWithCharactersInString:@"无"]];
        NSString *time1 = [self.timeFormat stringByTrimmingCharactersInSet:[NSCharacterSet characterSetWithCharactersInString:@"无"]];
        NSString *time2 = [anotherModel.timeFormat stringByTrimmingCharactersInSet:[NSCharacterSet characterSetWithCharactersInString:@"无"]];
        value = value && [date1 isEqualToString:date2] && [time1 isEqualToString:time2];
        value = value && SameString(prefix) && SameString(startNumber) && SameString(suffix) && SameInteger(incrementValue);
    } else if (self.templateType == JCTemplateType_BarCode) {
        value = SameInteger(textPosition) && SameFloat(textHeight) && SameInteger(codeType);
    } else if (self.templateType == JCTemplateType_QRCode) {
        value = SameInteger(correctLevel);
    } else if (self.templateType == JCTemplateType_Graph || self.templateType == JCTemplateType_Line) {
        value = SameJCPorpertyNumber(lineWidth) && SameInteger(lineType) && SameInteger(graphType);
    } else if (self.templateType == JCTemplateType_Image) {
        value = SameArray(imageProcessingValue);
    } else if (self.templateType == JCTemplateType_Table) {
        value = SameInteger(row) && SameInteger(column);
        if (!value) return NO;
        // 比较行高
        for (NSInteger i = 0; i < self.rowHeight.count; i ++) {
            NSString *v1 = [NSString stringWithFormat:@"%.0f",[self.rowHeight[i] floatValue]];
            NSString *v2 = [NSString stringWithFormat:@"%.0f",[anotherModel.rowHeight[i] floatValue]];
            if (![v1 isEqualToString:v2]) {
                return NO;
            }
        }
        // 比较列宽
        for (NSInteger i = 0; i < self.columnWidth.count; i ++) {
            NSString *v1 = [NSString stringWithFormat:@"%.0f",[self.columnWidth[i] floatValue]];
            NSString *v2 = [NSString stringWithFormat:@"%.0f",[anotherModel.columnWidth[i] floatValue]];
            if (![v1 isEqualToString:v2]) {
                return NO;
            }
        }
        // 比较cell
        NSArray *cell1 = self.cells;
        NSArray *cell2 = anotherModel.cells;
        __block BOOL __cellIsSame = YES;
        [cell1 enumerateObjectsUsingBlock:^(JCElementModel *cellModel1, NSUInteger idx, BOOL * _Nonnull stop) {
            JCElementModel *matchModel = [cell2 find:^BOOL(JCElementModel *obj) {
                return obj.rowIndex == cellModel1.rowIndex && obj.columnIndex == cellModel1.columnIndex;
            }];
            if (matchModel) {
                BOOL value = [cellModel1 tableCellIsSameWith:matchModel];
                if (!value) {
                    __cellIsSame = NO;
                    *stop = YES;
                }
            }
        }];
        if (!__cellIsSame) return NO;
        
        // 比较合并后的单元格
        NSArray *combineCell1 = self.combineCells;
        NSArray *combineCell2 = anotherModel.combineCells;
        __block BOOL __combineCellIsSame = YES;
        [combineCell1 enumerateObjectsUsingBlock:^(JCElementModel *cellModel1, NSUInteger idx, BOOL * _Nonnull stop) {
            JCElementModel *matchModel = [combineCell2 find:^BOOL(JCElementModel *obj) {
                return [obj.elementId isEqualToString:cellModel1.elementId];
            }];
            if (matchModel) {
                BOOL value = [cellModel1 tableCellIsSameWith:matchModel];
                if (!value) {
                    __combineCellIsSame = NO;
                    *stop = YES;
                }
            }
        }];
        if (!__combineCellIsSame) return NO;
    }
    return value;
}

// 单独比较表格的某个属性
- (BOOL)tableCellIsSameWith:(JCElementModel *)anotherModel {
    NSString *v1 = [self.value getExcelValue];
    NSString *v2 = [anotherModel.value getExcelValue];
    BOOL value = [[self.value getExcelValue] isEqualToString:[anotherModel.value getExcelValue]];
    if (!value) return NO;
    value = SameInteger(textAlignHorizonral) && SameInteger(textAlignVertical)/* && SameInteger(lineMode) */&& SameJCPorpertyNumber(wordSpacing) && SameJCPorpertyNumber(letterSpacing) && SameJCPorpertyNumber(lineSpacing) && SameString(fontCode) && SameArray(fontStyle) && SameJCPorpertyNumber(fontSize);
    return value;
}

#pragma mark - custom method
- (NSString *)elementValue:(NSString *)value withExcelIndex:(NSInteger)pageIndex elementId:(NSString *)elementId {
    NSArray *array = [value componentsSeparatedByString:excel_component];
    NSString *result = value;
    if (array.count == 2) {
        // excel原始数据
        JCExcelForElement *excelInfo = [JCExcelForElement excelInfoWith:[self getExternalData]];
        NSInteger columnNumber = [array.lastObject integerValue];
        NSArray *columnInfo = [excelInfo.columnArr safeObjectAtIndex:columnNumber];
        result = [columnInfo safeObjectAtIndex:pageIndex];
        // 修改过的内容
        NSDictionary *task = [self getTask];
        NSDictionary *modifyData = [task objectForKey:@"modifyData"];
        if (modifyData && [modifyData isKindOfClass:[NSDictionary class]]) {
            NSDictionary *temp = [modifyData objectForKey:elementId];
            if ([temp count] > 0 && [temp.allKeys containsObject:StringFromInt(pageIndex)]) {
                NSString *changeValue = [temp objectForKey:StringFromInt(pageIndex)];
                result = changeValue;
            }
        }
    }
    return result;
}

- (NSArray *)getAllFontCodes {
    NSMutableArray *fontCodes = [NSMutableArray array];
    if (self.templateType == JCTemplateType_Table) {
        [self.cells enumerateObjectsUsingBlock:^(JCElementModel *cell, NSUInteger idx, BOOL * _Nonnull stop) {
            if (![fontCodes containsObject:cell.fontCode]) {
                [fontCodes addObject:cell.fontCode];
            }
        }];
        
        [self.combineCells enumerateObjectsUsingBlock:^(JCElementModel *combineCell, NSUInteger idx, BOOL * _Nonnull stop) {
            if (![fontCodes containsObject:combineCell.fontCode]) {
                [fontCodes addObject:combineCell.fontCode];
            }
        }];
    } else {
        [fontCodes addObject:self.fontCode];
    }
    return fontCodes;
}

@end
