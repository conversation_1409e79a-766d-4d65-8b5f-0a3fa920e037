//
//  JCTemplateData.m
//  XYFrameWork
//
//  Created by xingling xu on 2020/3/11.
//  Copyright © 2020 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCTemplateData.h"

@implementation JCTemplateData

- (instancetype)init {
    self = [super init];
    if (self) {
        self.idStr = @"";
        self.name = @"";
        self.thumbnail = @"";
        self.contentThumbnail = @"";
        self.backgroundImage = @"";
        self.previewImage = @"";
        self.des = @"";
        self.rotate = 0;
        self.cableLength = 0;
        self.cableDirection = 0;
        self.paperType = 1;
        self.consumableType = 1;
        self.isCable = NO;
        self.width = 0;
        self.height = 0;
        self.margin = [NSArray array];
        self.usedFonts = [self getCurrentFonts];
        self.unit = @"";
        self.externalData = [NSDictionary dictionary];
        self.task = [NSDictionary dictionary];
        self.currentPage = 1;
        self.totalPage = 1;
        self.elements = [NSArray array].copy;
        self.profile = [JCProfile new];
        self.isMutableBackground = NO;
        self.mutableBackCurrentIndex = 0;
        self.margin = [NSArray array];
        self.localType = JCLocalType_Default;
        self.fromOldVersion = 2;
        self.labelId = @"0";
        self.sizeId = @"0";
        self.sourceImprintId = @"";
        self.isUpperShelf = false;
    }
    return self;
}

- (void)setPaperType:(NSInteger)paperType{
    if(paperType == 2){
        NSLog(@"");
    }
    _paperType = paperType;
}
- (NSDictionary *)getCurrentFonts {
    NSString *fontPath = [NSString stringWithFormat:@"%@/font",DocumentsFontPath];
    NSArray *fontNameArr = [[NSFileManager defaultManager] contentsOfDirectoryAtPath:fontPath error:nil];
    NSMutableDictionary *temp = [NSMutableDictionary dictionaryWithCapacity:fontNameArr.count];
    [fontNameArr enumerateObjectsUsingBlock:^(NSString *obj, NSUInteger idx, BOOL * _Nonnull stop) {
        // 把 name.ttf 拆分出
        NSArray *componets = [obj componentsSeparatedByString:@"."];
        NSString *fontName = componets.firstObject;
        if (obj && fontName && fontName.length > 0) {
            [temp setObject:obj forKey:fontName];
        }
    }];
    [temp setObject:[NSString stringWithFormat:@"%@.ttf",text_default_font_code] forKey:@"fontDefault"];
    return temp;
}

- (NSDictionary *)usedFonts {
    return [self getCurrentFonts];
}

+ (BOOL)propertyIsOptional:(NSString*)propertyName
{
  return YES;
}

/** 更新本地化的字段 */
- (void)updateLocalProperty {
    for (JCElementModel *element in self.elements) {
        [element updateLocalProperty];
    }
}

#pragma mark - yymodel protocol
+(JSONKeyMapper *)keyMapper
{
    return [[JSONKeyMapper alloc] initWithModelToJSONDictionary:@{@"idStr":@"id",@"des":@"description"}];
}

YYModel_Encode_And_Copy_Method_Implementation

- (id)copyWithZone:(NSZone *)zone {
    typeof(self) newOne = [self yy_modelCopy]; // 浅拷贝
    newOne.profile = self.profile.copy;
    return newOne;
    
}

@end

@implementation JCProfile
- (instancetype)init {
    self = [super init];
    if (self) {
        self.barcode = @"";
        self.keyword = @"";
        self.hardwareSeriesId = @"";
        self.hardwareSeriesName = @"";
        self.machineName = @"";
        self.machineId = @"";
        self.extrain = [JCProfileExtrain new];
    }
    return self;
}

+(BOOL)propertyIsOptional:(NSString*)propertyName
{
  return YES;
}

YYModel_Encode_And_Copy_Method_Implementation
- (id)copyWithZone:(NSZone *)zone {
    typeof(self) newOne = [self yy_modelCopy]; // 浅拷贝
    newOne.extrain = self.extrain.copy;
    return newOne;
    
}
@end

@implementation JCProfileExtrain
+(BOOL)propertyIsOptional:(NSString*)propertyName
{
  return YES;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        self.isCustom = NO;
        self.folderId = @"0";
        self.adaptPlatformCode = @"";
        self.adaptPlatformName = @"";
        self.userId = @"";
        self.industryId = @"";
        self.commodityCategoryId = @"";
        self.downloadCount = @"";
        self.isDelete = NO;
        self.createTime = @"";
        self.updateTime = @"";
        self.isHot = NO;
        self.sortDependency = @"";
        self.clickNum = @"";
        self.templateType = @"0";
        self.isPrivate = NO;
        self.amazonCodeBeijing = @"";
        self.amazonCodeWuhan = @"";
        self.sparedCode = @"";
        self.virtualBarCode = @"";
        self.isNewPath = NO;
        self.isMobileTemplete = NO;
        self.phone = @"";
    }
    return self;
}

- (void)setTemplateType:(NSString *)templateType{
    _templateType = templateType;
}

YYModel_Encode_And_Copy_Method_Implementation
- (id)copyWithZone:(NSZone *)zone {
    typeof(self) newOne = [self yy_modelCopy]; // 浅拷贝
    return newOne;
    
}

@end

