//
//  JCTemplateData+Transfer.h
//  XYFrameWork
//
//  Created by xingling xu on 2020/3/23.
//  Copyright © 2020 Jingchen Technology Co.Ltd . All rights reserved.
//

@interface JCTemplateData (Transfer)
/** convert data 2  dict*/
- (NSDictionary *)dataDict;

/** convert data which is matching sdk */
- (JCTemplateData *(^)(BOOL transfer2sdk))toSdk;

- (JCTemplateData *(^)(BOOL showHolder))showPlaceHolder;

/** set the current excel index for data if you want to get the specify page of the data */
- (JCTemplateData *(^)(NSInteger currentIndex))setCurrentPageIndex;

/** template data which has configure the background image ,default is 'NO' for the 3rd-party of the printer sdk ,
    if set 'YES' to get the image for preview.
 */
- (JCTemplateData *(^)(BOOL hasBackground))configureBase64Background;

/** current print copy number,which only affect the print of serial element */
- (JCTemplateData *(^)(NSInteger copyNumber))setCurrentCopyNumber;

/** print total copy number */
- (JCTemplateData *(^)(NSInteger copyNumber))setTotalCopyNumber;

- (BOOL)containSerialNumber;

/** 画板中是否包含导入excel的元素 */
- (BOOL)hasExcelElement;

/** 获取当前模板的excel导入的最大页码 */
- (NSInteger)currentMaxExcelNumber;



// 是否编辑过
- (BOOL)isSameWith:(JCTemplateData *)anotherData;
@end

@interface JCProfile (Transfer)
- (NSDictionary *)profileDict;
@end

@interface JCProfileExtrain (Transfer)
- (NSDictionary *)extrainDict;
@end


