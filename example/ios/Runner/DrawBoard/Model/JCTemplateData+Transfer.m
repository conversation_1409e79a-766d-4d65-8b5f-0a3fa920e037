//
//  JCTemplateData+Transfer.m
//  XYFrameWork
//
//  Created by xingling xu on 2020/3/23.
//  Copyright © 2020 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCTemplateData+Transfer.h"
#import "JCElementModel+Transfer.h"
#import "JCApplicationManager.h"

#define dict_set_key_value_string(key,string)   if (string && string.length > 0){[dict setObject:string forKey:key];}
/** 字符串属性 */
/** 字符串属性 */
#define dict_set_key_value_object(key,object)   if (object){[dict setObject:object forKey:key];}
/** 数字类型属性 */
#define dict_set_key_value_number(key,number)   [dict setObject:@(number) forKey:key]
/** bool属性转换 */
#define dict_set_key_value_bool(key,boolValue)  [dict setObject:boolValue?@(1):@(0) forKey:key]

NSString *const JCTemplateData2SdkPropertyKey       = @"JCTemplateData2SdkPropertyKey";
NSString *const JCTemplateDataShowPlaceHolder       = @"JCTemplateDataShowPlaceHolder";
NSString *const KJCTemplateDataCurrentIndex         = @"KJCTemplateDataCurrentIndex";
NSString *const KJCTemplateDataHasBackground        = @"KJCTemplateDataHasBackground";
NSString *const KJCTemplateDataCurrentCopyNumber    = @"KJCTemplateDataCurrentCopyNumber";
NSString *const KJCTemplateDataTotalCopyNumber      = @"KJCTemplateDataTotalCopyNumber";

@implementation JCTemplateData (Transfer)

- (NSDictionary *)dataDict {
    if (DrawBoardInfo.mm2pxScale == 0) DrawBoardInfo.mm2pxScale = (SCREEN_WIDTH-45)/self.width;
    NSMutableDictionary *dict = [NSMutableDictionary dictionaryWithCapacity:22];
    dict_set_key_value_string(@"id", self.idStr);
    dict_set_key_value_string(@"name", self.name);
    dict_set_key_value_string(@"thumbnail", self.thumbnail);
    dict_set_key_value_string(@"contentThumbnail", self.contentThumbnail);
    dict_set_key_value_number(@"fromOldVersion", self.fromOldVersion);
    if ([self forSdk]) {
        // sdk返回的image要显示背景图
        if ([self getConfigureBackground]) {
            // 多背景图
            NSString *currentBackImgPath;
            NSArray *imageArr = [self.backgroundImage componentsSeparatedByString:background_image_component];
            
            if (imageArr.count > 1) {
                // set base64 string
                NSString *imageUrl = [imageArr safeObjectAtIndex:self.mutableBackCurrentIndex];
                currentBackImgPath = ImageLocalPath(imageUrl);
            } else {
                currentBackImgPath = ImageLocalPath(self.backgroundImage);
            }
            UIImage *image = [[UIImage alloc] initWithContentsOfFile:currentBackImgPath];
            NSData *imageData = UIImagePNGRepresentation(image);
            NSString *encodedImageStr = (image == nil) ? @"":[imageData base64EncodedStringWithOptions:NSDataBase64Encoding64CharacterLineLength];
            dict_set_key_value_string(@"backgroundImage", encodedImageStr);
        }
    } else {
        // 上传服务端统一url
        dict_set_key_value_string(@"backgroundImage", self.backgroundImage);
    }
    dict_set_key_value_string(@"previewImage", self.previewImage);
    dict_set_key_value_number(@"rotate", self.rotate);
    dict_set_key_value_number(@"cableLength", self.cableLength);
    dict_set_key_value_number(@"cableDirection", self.cableDirection);
    dict_set_key_value_number(@"paperType", self.paperType);
    dict_set_key_value_number(@"consumableType", self.consumableType);
    dict_set_key_value_bool(@"isCable", self.isCable);
    dict_set_key_value_number(@"width", self.width);
    dict_set_key_value_number(@"height", self.height);
    dict_set_key_value_object(@"margin", self.margin);
    dict_set_key_value_object(@"usedFonts", self.usedFonts);
    dict_set_key_value_string(@"unit", self.unit);
    dict_set_key_value_number(@"currentPage",self.currentPage);
    dict_set_key_value_number(@"totalPage",self.totalPage);
    dict_set_key_value_string(@"labelId", self.labelId);
    dict_set_key_value_string(@"sizeId", self.sizeId);
    dict_set_key_value_string(@"sourceImprintId", self.sourceImprintId);
    dict_set_key_value_string(@"sourceId", self.sourceImprintId);
    dict_set_key_value_string(@"deviceId", self.deviceId);
    dict_set_key_value_string(@"syncedImage", self.syncedImage);
    NSMutableArray *elements = [NSMutableArray arrayWithCapacity:self.elements.count];
    [self.elements enumerateObjectsUsingBlock:^(JCElementModel * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        // 注入Excel数据
        [obj injectExternalData:self.externalData task:self.task];
        // 配置sdk与否
        obj = obj.toSdk([self forSdk]);
        // 配置份数 针对sdk打印流水号
        obj = obj.setCurrentCopyNumber([self getCurrentCopyNumber]);
        // 配置当前excel分页
        obj = obj.setCurrentPageIndex([self getCurrentPageIndex]);
        // 配置当前打印的总页数，适配流水号递增
        obj = obj.setTotalCopyNumber([self getTotalCopyNumber]);
        // 是否显示placeholder
        obj = obj.showPlaceHolder([self getShowPlaceHolder]);
        // 生成json
        [elements addObject:[obj elementDict]];
    }];
    dict_set_key_value_object(@"elements", elements);
    if (![self forSdk]) {
        dict_set_key_value_string(@"des", self.des);
        dict_set_key_value_object(@"profile", [self.profile profileDict]);
        dict_set_key_value_object(@"externalData", self.externalData);
        dict_set_key_value_object(@"task", self.task);
    }
    return dict;
}

#pragma mark - some configure
- (JCTemplateData *(^)(BOOL transfer2sdk))toSdk {
    return ^(BOOL transfer2sdk){
        objc_setAssociatedObject(self, &JCTemplateData2SdkPropertyKey, @(transfer2sdk), OBJC_ASSOCIATION_ASSIGN);
        return self;
    };
}

- (BOOL)forSdk {
    id object = objc_getAssociatedObject(self, &JCTemplateData2SdkPropertyKey);
    return [object integerValue] > 0 ? YES:NO;
}

- (JCTemplateData *(^)(BOOL showHolder))showPlaceHolder {
    return ^(BOOL showHolder){
        objc_setAssociatedObject(self, &JCTemplateDataShowPlaceHolder, @(showHolder), OBJC_ASSOCIATION_ASSIGN);
        return self;
    };
}

- (BOOL)getShowPlaceHolder {
    id object = objc_getAssociatedObject(self, &JCTemplateDataShowPlaceHolder);
    return [object integerValue] > 0 ? YES:NO;
}


- (JCTemplateData *(^)(NSInteger currentIndex))setCurrentPageIndex {
    return ^(NSInteger currentIndex){
        objc_setAssociatedObject(self, &KJCTemplateDataCurrentIndex, @(currentIndex), OBJC_ASSOCIATION_ASSIGN);
        return self;
    };
}

- (NSInteger)getCurrentPageIndex {
    id object = objc_getAssociatedObject(self, &KJCTemplateDataCurrentIndex);
    return [object integerValue];
}

- (JCTemplateData *(^)(BOOL hasBackground))configureBase64Background {
    return ^(BOOL hasBackground){
        objc_setAssociatedObject(self, &KJCTemplateDataHasBackground, @(hasBackground), OBJC_ASSOCIATION_ASSIGN);
        return self;
    };
}

- (BOOL)getConfigureBackground {
    id object = objc_getAssociatedObject(self, &KJCTemplateDataHasBackground);
    return [object integerValue] > 0 ? YES:NO;
}

- (JCTemplateData *(^)(NSInteger copyNumber))setCurrentCopyNumber {
    return ^(NSInteger copyNumber){
        objc_setAssociatedObject(self, &KJCTemplateDataCurrentCopyNumber, @(copyNumber), OBJC_ASSOCIATION_ASSIGN);
        return self;
    };
}

- (NSInteger)getCurrentCopyNumber {
    id object = objc_getAssociatedObject(self, &KJCTemplateDataCurrentCopyNumber);
    return [object integerValue];
}

- (JCTemplateData *(^)(NSInteger copyNumber))setTotalCopyNumber {
    return ^(NSInteger copyNumber){
        objc_setAssociatedObject(self, &KJCTemplateDataTotalCopyNumber, @(copyNumber), OBJC_ASSOCIATION_ASSIGN);
        return self;
    };
}

- (NSInteger)getTotalCopyNumber {
    id object = objc_getAssociatedObject(self, &KJCTemplateDataTotalCopyNumber);
    return [object integerValue];
}

- (BOOL)containSerialNumber {
    __block BOOL value = NO;
    [self.elements enumerateObjectsUsingBlock:^(JCElementModel *model, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([model.type isEqualToString:@"serial"]) {
            value = YES;*stop = YES;
        }
    }];
    return value;
}

- (BOOL)hasExcelElement {
    __block BOOL value = NO;
    [self.elements enumerateObjectsUsingBlock:^(JCElementModel *obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([obj hasExcelImport]) {
            value = YES;
            *stop = YES;
        }
    }];
    return value;
}

- (NSInteger)currentMaxExcelNumber {
    __block NSInteger excelNumber = 1;
    [self.elements enumerateObjectsUsingBlock:^(JCElementModel *obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([obj hasExcelImport]) {
            excelNumber = MAX(excelNumber, [obj currentExcelNumber]);
        }
    }];
    return excelNumber;
}

// 比较基础信息是否被修改过
- (BOOL)baseInfoIsSameWith:(JCTemplateData *)anotherData {
    BOOL same = [self.name isEqualToString:anotherData.name] && (self.cableLength == anotherData.cableLength) && (self.cableDirection == anotherData.cableDirection) /*&& (self.paperType == anotherData.paperType)*/ && (self.consumableType == anotherData.consumableType) && (self.width == anotherData.width) && (self.height == anotherData.height) && (self.currentPage == anotherData.currentPage) && (self.totalPage == anotherData.totalPage || (self.totalPage - anotherData.totalPage <= 1))/* && [self.profile isEqual:anotherData.profile]*/;
    return same;
}

// 比较模板数据
- (BOOL)isSameWith:(JCTemplateData *)anotherData {
    NSArray *orignalElements = self.elements;
    NSArray *anotherElements = anotherData.elements;
    if (orignalElements.count != anotherElements.count) return NO;
    __block BOOL same = [self baseInfoIsSameWith:anotherData];
    if (!same) return NO;
    [orignalElements enumerateObjectsUsingBlock:^(JCElementModel *orignalModel, NSUInteger idx, BOOL * _Nonnull stop) {
        NSArray *filterArr = [anotherElements filter:^BOOL(JCElementModel *anotherModel) {
            return [orignalModel.elementId isEqualToString:anotherModel.elementId];
        }];
        if (filterArr.count == 0) {
            same = NO;
            *stop = YES;
        } else if (filterArr.count == 1) {
            same = [orignalModel isSameWith:filterArr.firstObject];
             if (!same) *stop = YES;
        } else {
            __block BOOL temple = NO;
            [filterArr enumerateObjectsUsingBlock:^(JCElementModel *obj, NSUInteger idx, BOOL * _Nonnull stop) {
                if ([orignalModel isSameWith:obj]) {
                    temple = YES;
                    *stop = YES;
                }
            }];
            same = temple;
            if (!same) *stop = YES;
        }
//        JCElementModel *model = [anotherElements find:^BOOL(JCElementModel *anotherModel) {
//            return [orignalModel.elementId isEqualToString:anotherModel.elementId];
//        }];
//        if (model) {
//            same = [orignalModel isSameWith:model];
//            if (!same) *stop = YES;
//        } else {
//            same = NO;
//            *stop = YES;
//        }
    }];
    return same;
}

@end

@implementation JCProfile (Transfer)

- (NSDictionary *)profileDict {
    NSMutableDictionary *dict = [NSMutableDictionary dictionaryWithCapacity:10];
    dict_set_key_value_string(@"barcode", self.barcode);
    dict_set_key_value_string(@"keyword", self.keyword);
    dict_set_key_value_string(@"hardwareSeriesId", self.hardwareSeriesId);
    dict_set_key_value_string(@"hardwareSeriesName", self.hardwareSeriesName);
    dict_set_key_value_string(@"machineName", self.machineName);
    dict_set_key_value_object(@"extrain", [self.extrain extrainDict]);
    return dict;
}

@end

@implementation JCProfileExtrain (Transfer)
- (NSDictionary *)extrainDict {
    NSMutableDictionary *dict = [NSMutableDictionary dictionaryWithCapacity:10];
    dict_set_key_value_bool(@"isCustom", self.isCustom);
    dict_set_key_value_string(@"folderId", self.folderId);
    dict_set_key_value_string(@"adaptPlatformCode", self.adaptPlatformCode);
    dict_set_key_value_string(@"adaptPlatformName", self.adaptPlatformName);
    if (STR_IS_NIL(self.userId)) {
        [dict setObject:UN_NIL(JCAPP_Manager.user.userId) forKey:@"userId"];
    } else {
        dict_set_key_value_string(@"userId", self.userId);
    }
    dict_set_key_value_string(@"industryId", self.industryId);
    dict_set_key_value_string(@"commodityCategoryId", self.commodityCategoryId);
    dict_set_key_value_string(@"downloadCount", self.downloadCount);
    dict_set_key_value_bool(@"isDelete", self.isDelete);
    dict_set_key_value_string(@"createTime", self.createTime);
    dict_set_key_value_string(@"updateTime", self.updateTime);
    dict_set_key_value_bool(@"isHot", self.isHot);
    dict_set_key_value_string(@"sortDependency", self.sortDependency);
    dict_set_key_value_string(@"clickNum", self.clickNum);
    dict_set_key_value_string(@"templateType", self.templateType);
    dict_set_key_value_bool(@"isPrivate", self.isPrivate);
    dict_set_key_value_string(@"amazonCodeBeijing", self.amazonCodeBeijing);
    dict_set_key_value_string(@"amazonCodeWuhan", self.amazonCodeWuhan);
    dict_set_key_value_string(@"sparedCode", self.sparedCode);
    dict_set_key_value_string(@"virtualBarCode", self.virtualBarCode);
    dict_set_key_value_bool(@"isNewPath", self.isNewPath);
    dict_set_key_value_bool(@"isMobileTemplete", self.isMobileTemplete);
    dict_set_key_value_object(@"phone", self.phone);
    dict_set_key_value_string(@"sourceId", self.sourceId);
    dict_set_key_value_string(@"goodsCode", self.goodsCode);
    return dict;
}
@end
