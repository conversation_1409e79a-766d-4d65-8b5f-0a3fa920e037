//
//  JCTemplateData+Images.h
//  XYFrameWork
//
//  Created by xingling xu on 2020/6/3.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//
#import "JCTemplateData.h"

@interface JCTemplateData (External)

- (void)downLoadAllImages:(void(^)(void))block;

- (void)downLoadAllFonts:(void(^)(void))block;

// 模板中没有下载过的字体 返回数组为空则代表所有字体均已下载
- (NSArray<JCFontModel *> *)fontModelsNotDownload;

// 模板中是否有尚未下载的字体
- (BOOL)fontHasNotDownload;

/** 是否downLoad所有的图片信息:缩略图，背景图，元素图片 */
- (BOOL)hasDownLoadAllImages;

@end

