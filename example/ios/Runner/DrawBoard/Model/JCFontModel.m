//
//  JCFontModel.m
//  XYFrameWork
//
//  Created by <PERSON><PERSON><PERSON> on 2018/6/16.
//  Copyright © 2018年 xiaoyao. All rights reserved.
//

#import "JCFontModel.h"

static NSString * const font_down_load_time_info = @"font_down_load_time_info";
static NSString * const font_down_load_progress_info = @"font_down_load_progress_info";

@implementation JCFontModel

- (id)init{
    self = [super init];
    if(self){
        self.percentForDownLoad = @"0";
        self.isDownLoading = NO;
    }
    return self;
}

+ (JSONKeyMapper *)keyMapper
{
    return [[JSONKeyMapper alloc] initWithModelToJSONDictionary:@{@"xyid":@"id",@"fontCode":@"code",@"url":@"path",@"release_time":@"publishTime"}];
}

+(BOOL)propertyIsOptional:(NSString*)propertyName
{
  return YES;
}


+ (JCFontModel *)defaultCnFont {
    JCFontModel *model = [[JCFontModel alloc] init];
    model.name = XY_LANGUAGE_TITLE_NAMED(text_default_font_name, @"思源黑体");
    model.font_type = @"1000";
    model.langType = @"zh";
    model.xyid = @"10000001";
    model.fontCode = text_default_font_code;
    model.hasDownLoad = YES;
    model.lastUseTimeStamp = [XYTool getNowTimeTimestamp];
    [JCFontModel saveTime:StringFromInt(NSIntegerMax) fontCode:text_default_font_code];
    return model;
}

+ (JCFontModel *)defaultEnFont {
    JCFontModel *model = [[JCFontModel alloc] init];
    model.name = @"Arail";
    model.font_type = @"1000";
    model.xyid = @"10000002";
    model.langType = @"en";
    [JCFontModel saveTime:@"1" fontCode:@"Arail"];
    return model;
}

- (BOOL)fontHasDownload {
    NSArray *downLoadCodes = [JCFontModel getDownLoadFontCodeArr];
    BOOL value = [downLoadCodes containsObject:self.fontCode]?YES:NO;
    return value;
}

+ (BOOL)fontHasDownLoad:(NSString *)fontCode {
    NSArray *downLoadCodes = [JCFontModel getDownLoadFontCodeArr];
    BOOL value = [downLoadCodes containsObject:fontCode]?YES:NO;
    return value;
}

+ (NSMutableArray *)getDownLoadFontCodeArr {
    NSMutableArray *arr = @[].mutableCopy;
    NSFileManager* fm=[NSFileManager defaultManager];
    NSString *fontPath = [NSString stringWithFormat:@"%@/font",DocumentsFontPath];
    if(![fm fileExistsAtPath:fontPath]){
        [fm createDirectoryAtPath:fontPath withIntermediateDirectories:YES attributes:nil error:nil];
    }else{
        NSArray *files = [fm contentsOfDirectoryAtPath:fontPath error:nil];
        for (NSString *fileName in files) {
            NSArray *tempArr = [fileName componentsSeparatedByString:@"."];
            NSString *codeName = [tempArr safeObjectAtIndex:0];
            if(!STR_IS_NIL(codeName)){
                [arr addObject:codeName];
            }
        }
    }
    [arr addObject:text_default_font_code];
    return arr;
}


- (BOOL)isEqualTo:(JCFontModel *)otherModel {
    return [self.name isEqualToString:otherModel.name];
}

+ (NSString *)dowloadTimeForCode:(NSString *)fontCode {
    NSString *value = @"";
    NSDictionary *timeInfo = [[NSUserDefaults standardUserDefaults] objectForKey:font_down_load_time_info];
    if ([timeInfo count] > 0) {
        NSString *object = [timeInfo objectForKey:fontCode];
        if (object && object.length > 0) {
            value = object;
        }
    }
    return value;
}

+ (void)saveTime:(NSString *)time fontCode:(NSString *)fontCode {
    if (!time || time.length <= 0) return;
    if (!fontCode || fontCode.length <= 0) return;
    NSMutableDictionary *timeInfo = [[NSUserDefaults standardUserDefaults] dictionaryForKey:font_down_load_time_info].mutableCopy;
    if (!timeInfo) {
        timeInfo = [NSMutableDictionary dictionaryWithCapacity:2];
    }
    [timeInfo setObject:time forKey:fontCode];
    [[NSUserDefaults standardUserDefaults] setObject:timeInfo forKey:font_down_load_time_info];
    [[NSUserDefaults standardUserDefaults] synchronize];
}

+ (void)saveProgress:(CGFloat)progress fontCode:(NSString *)fontCode {
    if (!fontCode || fontCode.length <= 0) return;
    NSMutableDictionary *timeInfo = [[NSUserDefaults standardUserDefaults] dictionaryForKey:font_down_load_progress_info].mutableCopy;
    if (!timeInfo) {
        timeInfo = [NSMutableDictionary dictionaryWithCapacity:2];
    }
    [timeInfo setObject:@(progress) forKey:fontCode];
    [[NSUserDefaults standardUserDefaults] setObject:timeInfo forKey:font_down_load_progress_info];
    [[NSUserDefaults standardUserDefaults] synchronize];
}

+ (CGFloat)progressForCode:(NSString *)fontCode {
    CGFloat progress = 0;
    NSDictionary *timeInfo = [[NSUserDefaults standardUserDefaults] objectForKey:font_down_load_progress_info];
    if ([timeInfo count] > 0) {
        NSNumber *object = [timeInfo objectForKey:fontCode];
        if (object) {
            progress = [object floatValue];
        }
    }
    return progress;
}

@end
