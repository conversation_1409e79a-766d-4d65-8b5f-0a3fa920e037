//
//  JCTemplateData+Images.m
//  XYFrameWork
//
//  Created by xingling xu on 2020/6/3.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import "JCTemplateData+External.h"
#import "JCElementModel+Transfer.h"
#import "JCFontManager.h"
#import "JCTemplateImageManager.h"

@implementation JCTemplateData (External)

- (void)downLoadAllImages:(void(^)(void))block {
    [JCTemplateImageManager downLoadImagesForData:self options:DownAll complete:^{
        if (block) {
            block();
        }
    }];
}

- (void)downLoadAllFonts:(void(^)(void))block {
    NSArray *notExistFontModels = [self fontModelsNotDownload];
    // download
    if (notExistFontModels.count == 0 ) {
        if (block) block();
        return;
    }
    dispatch_group_t group = dispatch_group_create();
    for (NSInteger i = 0; i < notExistFontModels.count; i ++) {
        JCFontModel *model = [notExistFontModels safeObjectAtIndex:i];
        if (model) {
            dispatch_group_enter(group);
            [[JCFontManager sharedManager] downLoadFontRequestWithModel:model finishBlock:^(BOOL isscuess) {
                [JCFontModel saveTime:[XYTool getNowTimeTimestamp] fontCode:model.fontCode];
                dispatch_group_leave(group);
            }];
        }
    }
    dispatch_group_notify(group, dispatch_get_main_queue(), ^{
        NSLog(@"请求完成");
        if (block) block();
    });
}

- (NSArray<JCFontModel *> *)fontModelsNotDownload {
    NSArray *fonts = [[JCFMDB shareDatabase:DB_NAME]  jc_lookupTable:TABLE_FONTINFO dicOrModel:[JCFontModel class] whereFormat:nil];
    NSArray *existFontCodes = [JCFontModel getDownLoadFontCodeArr];
    NSMutableArray *notExistFontCodes = [NSMutableArray array];
    [self.elements enumerateObjectsUsingBlock:^(JCElementModel *obj, NSUInteger idx, BOOL * _Nonnull stop) {
        NSArray *modelFonts = [obj getAllFontCodes];
        [modelFonts enumerateObjectsUsingBlock:^(NSString *fontCode, NSUInteger idx, BOOL * _Nonnull stop) {
            if (!STR_IS_NIL(fontCode) && ![existFontCodes containsObject:fontCode]) {
                [notExistFontCodes addObject:fontCode];
            }
        }];
    }];
    NSMutableArray *notExistFontModels = [NSMutableArray arrayWithCapacity:notExistFontCodes.count];
    [fonts enumerateObjectsUsingBlock:^(JCFontModel *fontModel, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([notExistFontCodes containsObject:fontModel.fontCode]) {
            [notExistFontModels addObject:fontModel];
        }
    }];
    return notExistFontModels;
}

- (BOOL)fontHasNotDownload {
    NSArray *notExistFontModels = [self fontModelsNotDownload];
    return notExistFontModels.count > 0;
}


- (BOOL)hasDownLoadAllImages {
    // 判断缩略图是否下载到本地
    BOOL thumbImageHasDownLoad = YES;
    NSString *thumbUlr = self.thumbnail;
    if (!STR_IS_NIL(thumbUlr)) {
        NSString *thumbLocalPath = ImageLocalPath(thumbUlr);
        if (!JCLocalFileExist(thumbLocalPath)) {
            thumbImageHasDownLoad = NO;
        }
    }
    
    // 判断背景图是否下载到本地
    BOOL backGroundHasDownLoad = YES;
    NSString *backgroundUrl = self.backgroundImage;
    NSArray *backgroundUrlArr = [backgroundUrl componentsSeparatedByString:background_image_component];
    if (backgroundUrlArr.count > 1) {// 多图
        for (NSInteger i = 0; i < backgroundUrlArr.count; i ++) {
            NSString *url = [backgroundUrlArr safeObjectAtIndex:i];
            if (!STR_IS_NIL(url)) {
                NSString *currentBackPath =ImageLocalPath(url);
                if (!JCLocalFileExist(currentBackPath)) {
                    backGroundHasDownLoad = NO;
                    break;
                }
            }
        }
    } else {// 单图
        NSString *url = backgroundUrlArr.firstObject;
        if (!STR_IS_NIL(url)) {
            NSString *localPath = ImageLocalPath(url);
            if (!JCLocalFileExist(localPath)) {
                backGroundHasDownLoad = NO;
            }
        }
    }
    
    // 判断内部图片元素的图片数据是否已经下载到本地
    __block BOOL elementImageHasDown = YES;
    [self.elements enumerateObjectsUsingBlock:^(JCElementModel *obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if (obj.templateType == JCTemplateType_Image) {
            NSString *imageUrl = obj.imageUrl;
            NSString *elementId = obj.elementId;
            if (!STR_IS_NIL(imageUrl)) {
                NSString *elementLocalPath = ImageLocalPath(imageUrl);
                if (!JCLocalFileExist(elementLocalPath)) {
                    elementImageHasDown = NO;
                    *stop = YES;
                }
            }
        }
    }];
    
    return thumbImageHasDownLoad && backGroundHasDownLoad && elementImageHasDown;
}
@end
