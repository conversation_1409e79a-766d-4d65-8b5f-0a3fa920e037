//
//  JCFontSize.m
//  XYFrameWork
//
//  Created by xingling xu on 2020/5/26.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import "JCFontSize.h"

#define JCFont(cnV,ptV,mmV,inV) [JCFontSize sizeWithCN:cnV ptValue:ptV mmValue:mmV inValue:inV]

@implementation JCFontSize

+ (NSArray *)jcFontSizes {
    NSMutableArray *fontArray = [NSMutableArray arrayWithCapacity:24];
    [fontArray addObject:J<PERSON>ont(@"八号", @"5", @"1.8", @"0.07")];
    [fontArray addObject:J<PERSON><PERSON>(@"七号", @"5.5", @"1.9", @"0.08")];
    [fontArray addObject:J<PERSON><PERSON>(@"小六", @"6.5", @"2.3", @"0.09")];
    [fontArray addObject:<PERSON><PERSON><PERSON>(@"六号", @"7.5", @"2.6", @"0.10")];
    [fontArray addObject:J<PERSON><PERSON>(@"小五", @"9", @"3.2", @"0.13")];
    [fontArray addObject:J<PERSON>ont(@"五号", @"10.5", @"3.7", @"0.15")];
    [fontArray addObject:JCFont(@"小四", @"12", @"4.2", @"0.17")];
    [fontArray addObject:JCFont(@"四号", @"14", @"4.9", @"0.19")];
    [fontArray addObject:JCFont(@"小三", @"15", @"5.3", @"0.21")];
    [fontArray addObject:JCFont(@"三号", @"16", @"5.6", @"0.22")];
    [fontArray addObject:JCFont(@"小二", @"18", @"6.3", @"0.25")];
    [fontArray addObject:JCFont(@"二号", @"22", @"7.8", @"0.31")];
    [fontArray addObject:JCFont(@"小一", @"24", @"8.5", @"0.33")];
    [fontArray addObject:JCFont(@"一号", @"26", @"9.2", @"0.36")];
    [fontArray addObject:JCFont(@"小初", @"36", @"12.7", @"0.50")];
    [fontArray addObject:JCFont(@"初号", @"42", @"14.8", @"0.58")];
    [fontArray addObject:JCFont(@"特号", @"54", @"19.0", @"0.75")];
    [fontArray addObject:JCFont(@"大特号", @"63", @"22.2", @"0.88")];
    [fontArray addObject:JCFont(@"72", @"72", @"25.4", @"1.00")];
//    [fontArray addObject:JCFont(@"80", @"80", @"28.2", @"1.11")];
//    [fontArray addObject:JCFont(@"90", @"90", @"31.7", @"1.25")];
//    [fontArray addObject:JCFont(@"100", @"100", @"35.3", @"1.39")];
//    [fontArray addObject:JCFont(@"110", @"110", @"38.8", @"1.53")];
//    [fontArray addObject:JCFont(@"120", @"120", @"42.3", @"1.67")];
    return fontArray;
}

+ (JCFontSize *)sizeWithCN:(NSString *)cnValue ptValue:(NSString *)ptValue mmValue:(NSString *)mmValue inValue:(NSString *)inValue {
    JCFontSize *fontSize = [JCFontSize new];
    fontSize.size_cn = cnValue;
    fontSize.size_pt = ptValue;
    fontSize.size_mm = mmValue;
    fontSize.size_in = inValue;
    return fontSize;
}

+ (NSArray *)cnFontSizes {
    NSArray *fontSize = [JCFontSize jcFontSizes];
    NSMutableArray *tmp = [NSMutableArray arrayWithCapacity:fontSize.count];
    [fontSize enumerateObjectsUsingBlock:^(JCFontSize  *obj, NSUInteger idx, BOOL * _Nonnull stop) {
        [tmp addObject:obj.size_cn];
    }];
    return tmp;
}

+ (NSArray *)ptFontSizes {
    NSArray *fontSize = [JCFontSize jcFontSizes];
    NSMutableArray *tmp = [NSMutableArray arrayWithCapacity:fontSize.count];
    [fontSize enumerateObjectsUsingBlock:^(JCFontSize  *obj, NSUInteger idx, BOOL * _Nonnull stop) {
        [tmp addObject:obj.size_pt];
    }];
    return tmp;
}

+ (NSArray *)mmFontSizes {
    NSArray *fontSize = [JCFontSize jcFontSizes];
    NSMutableArray *tmp = [NSMutableArray arrayWithCapacity:fontSize.count];
    [fontSize enumerateObjectsUsingBlock:^(JCFontSize  *obj, NSUInteger idx, BOOL * _Nonnull stop) {
        [tmp addObject:obj.size_mm];
    }];
    return tmp;
}
@end
