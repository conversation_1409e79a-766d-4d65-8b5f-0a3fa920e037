//
//  JCElementEditManager.h
//  XYFrameWork
//
//  Created by xingling xu on 2019/12/27.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import <Foundation/Foundation.h>


NS_ASSUME_NONNULL_BEGIN
///
/// 所有针对使用 JCElementBaseView 的参数必须使用名字为 aElement
///
@interface JCElementEditManager : NSObject
/** 元素是否支持键盘弹起 */
+ (BOOL)canShowKeyBoard:(JCElementBaseView *)aElement;
/** 键盘获取元素的文本内容 */
+ (NSString *)getElementContent:(JCElementBaseView *)aElement;
/** 编辑文本内容 */
+ (void)editElement:(JCElementBaseView *)aElement content:(NSString *)content;
/** 修改元素的大小 */
+ (void)editElement:(JCElementBaseView *)aElement frame:(CGRect)frame;
/** 获取元素字体名 */
+ (NSString *)getFontName:(JCElementBaseView *)aElement;
/** 设置元素字体名 */
+ (void)editElement:(JCElementBaseView *)aElement fontName:(NSString *)fontName;
/** 获取元素字体code */
+ (NSString *)getFontCode:(JCElementBaseView *)aElement;
/** 设置元素字体code */
+ (void)editElement:(JCElementBaseView *)aElement fontCode:(NSString *)fontCode;
/** 获取条码当前支持的最大字号 */
+ (CGFloat)getBarCodeMaxmmFont:(JCElementBaseView *)aElement;
/** 获取元素字体大小 */
+ (CGFloat)getElementFont:(JCElementBaseView *)aElement;
/** 编辑元素字体大小 */
+ (void)editElement:(JCElementBaseView *)aElement fontSize_mm:(CGFloat)mmFont;
/** 元素是否粗体 */
+ (BOOL)elementIsBold:(JCElementBaseView *)aElement;
/** 设置元素粗体与否 */
+ (void)editElement:(JCElementBaseView *)aElement bold:(BOOL)bold;
/** 元素是否斜体 */
+ (BOOL)elementIsItalic:(JCElementBaseView *)aElement;
/** 设置元素斜体与否 */
+ (void)editElement:(JCElementBaseView *)aElement italic:(BOOL)italic;
/** 元素是否有下划线 */
+ (BOOL)elementIsUnderLine:(JCElementBaseView *)aElement;
/** 设置元素下划线与否 */
+ (void)editElement:(JCElementBaseView *)aElement underLine:(BOOL)underLine;
/** 获取元素水平对齐方式 */
+ (NSTextAlignment)getElementTextAlignment:(JCElementBaseView *)aElement;
/** 设置元素水平对齐方式 */
+ (void)editElement:(JCElementBaseView *)aElement textAlignment:(NSTextAlignment)textAlignment;
/** 获取元素垂直对齐方式 */
+ (JCFontVertical)getElementTextVerticalAlignment:(JCElementBaseView *)aElement;
/** 设置元素垂直对齐方式 */
+ (void)editElement:(JCElementBaseView *)aElement verticalTextAlignment:(JCFontVertical)textVerticalAlignment;
/** 获取元素字间距 */
+ (CGFloat)getEementTextSpace:(JCElementBaseView *)aElement;
/** 设置元素字间距 */
+ (void)editElement:(JCElementBaseView *)aElement textSpace:(CGFloat)space;
/** 获取元素行间距 */
+ (CGFloat)getEementLineSpace:(JCElementBaseView *)aElement;
/** 设置元素行间距 */
+ (void)editElement:(JCElementBaseView *)aElement lineSpace:(CGFloat)space;
/** 获取条码样式 */
+ (NSInteger)getEementBarCodeIndex:(JCElementBaseView *)aElement;
/** 设置条码样式 */
+ (void)editElement:(JCElementBaseView *)aElement barCodeTextPosition:(NSInteger)index;
/** 获取条码或二维码的编码类型名称 */
+ (NSString *)getElementCodeTypeName:(JCElementBaseView *)aElement;
/** 编辑元素的编码类型：条码 二维码有效 */
+ (void)editElement:(JCElementBaseView *)aElement codeType:(JCCodeType)codeType;
/** 根据编码类型名称编辑元素 */
+ (void)editElement:(JCElementBaseView *)aElement codeTypeName:(NSString *)currentTypeName;
/** 获取元素线条类型：实线、虚线 */
+ (NSInteger)getEementLineType:(JCElementBaseView *)aElement;
/** 设置元素线条类型 */
+ (void)editElement:(JCElementBaseView *)aElement lineType:(NSInteger)lineType;
/** 获取线宽的毫米值 */
+ (CGFloat)getEementLine_mmWidth:(JCElementBaseView *)aElement;
/** 设置元素线宽毫米值 */
+ (void)editElement:(JCElementBaseView *)aElement mmLineWidth:(CGFloat)mmWidth;
/** 获取日期格式 */
+ (NSString *)getEementDateFormatt:(JCElementBaseView *)aElement;
/** 设置日期格式 */
+ (void)editElement:(JCElementBaseView *)aElement dateFormatt:(NSString *)dateFormatt;
/** 获取时间格式 */
+ (NSString *)getEementTimeFormatt:(JCElementBaseView *)aElement;
/** 设置时间格式 */
+ (void)editElement:(JCElementBaseView *)aElement timeFormatt:(NSString *)timeFormatt;
/** 获取日期显示的值 */
+ (NSString *)getEementDateValue:(JCElementBaseView *)aElement;
/** 获取时间显示的值 */
+ (NSString *)getEementTimeValue:(JCElementBaseView *)aElement;
/** 设置日期 */
+ (void)editElement:(JCElementBaseView *)aElement date:(NSDate *)date;
/** 获取形状的类型 */
+ (JCGraphType)getEementShapeType:(JCElementBaseView *)aElement;
/** 获取图片的阈值 */
+ (NSString *)getEementImageThresHoldValue:(JCElementBaseView *)aElement;
/** 修改图片的阈值 */
+ (void)editElement:(JCElementBaseView *)aElement imageThresHoldValue:(NSString *)value;
/** 获取图片是否允许自由拉伸 */
+ (BOOL)getElementImageAllowFreeZoom:(JCElementBaseView *)aElement;
/** 设置图片是否允许自由拉伸 */
+ (void)editElement:(JCElementBaseView *)aElement allowFreeZoom:(BOOL)value;
/** 设置形状的类型 */
+ (void)editElement:(JCElementBaseView *)aElement shapeType:(JCGraphType)graphType;
/** excel导入的元素重新选择列 */
+ (void)editElement:(JCElementBaseView *)aElement columnIndex:(NSInteger)column;
/** 获取excel导入后的选择列名 */
+ (NSString *)elementColumnName:(JCElementBaseView *)aElement;

//-------------------------------------------------------------------------- 以下针对表格的编辑
/** 表格的添加、删除、合并等操作，合并拆分需要传selectIndexPaths */
+ (void)editElement:(JCElementBaseView *)aElement event:(JCSheetAction)event withSelectIndexPaths:(NSMutableOrderedSet<NSIndexPath *> *)selectIndexPaths;
/** 某个单元格的行高 */
+ (CGFloat)rowHeight:(NSIndexPath *)indexPath;
/** 某个单元格的列宽 */
+ (CGFloat)columnWidth:(NSIndexPath *)indexPath;
//--------------------------------------------------------------------------
@end

NS_ASSUME_NONNULL_END
