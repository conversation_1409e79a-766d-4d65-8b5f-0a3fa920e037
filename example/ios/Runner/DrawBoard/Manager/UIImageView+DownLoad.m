//
//  UIImage+DownLoad.m
//  Runner
//
//  Created by xingling xu on 2021/1/6.
//

#import "UIImageView+DownLoad.h"

@implementation UIImageView (DownLoad)

- (void)jc_setImageWithUrl:(NSString *)imageUrl {
    [self jc_setImageWithUrl:imageUrl completed:^(UIImage * _Nonnull image, NSError * _Nonnull error, NSString * _Nonnull imageUrl) {
        
    }];
}

- (void)jc_setImageWithUrl:(NSString *)imageUrl completed:(void(^)(UIImage *image,NSError *error,NSString *imageUrl))completeBlock {
    // error
    if (!imageUrl || imageUrl.length == 0) {
        if (completeBlock) {
            completeBlock(nil,nil,imageUrl);
            return;
        }
    }
    
    

 
    // exist
    NSString *localPath = ImageLocalPath(imageUrl);
    UIImage *aImage = [[UIImage alloc] initWithContentsOfFile:localPath];
    if (aImage && completeBlock) {
        completeBlock(aImage,nil,imageUrl);
        self.image = aImage;
        return;
    }
    imageUrl = [imageUrl stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLFragmentAllowedCharacterSet]];
    NSString *localP = [[SDWebImageManager sharedManager] cacheKeyForURL:[NSURL URLWithString:imageUrl]];
    [self sd_setImageWithURL:[NSURL URLWithString:imageUrl] completed:^(UIImage * _Nullable image, NSError * _Nullable error, SDImageCacheType cacheType, NSURL * _Nullable imageURL) {
        if (image) {
            NSData *imageData = UIImagePNGRepresentation(image);
            [imageData writeToFile:localPath atomically:YES];
        }
        if (completeBlock) {
            completeBlock(image,error,imageUrl);
        }
    }];
    // request
//    [DCHTTPRequest downLoadFileWithParams:@{} UrlString:imageUrl savePath:localPath tagString:imageUrl Success:^(__kindof YTKBaseRequest * _Nonnull request, id  _Nonnull responseObject) {
//        UIImage *aImage = [[UIImage alloc] initWithContentsOfFile:localPath];
//        if (completeBlock) {
//            completeBlock(aImage,nil,imageUrl);
//        }
//        if (aImage) {
//            dispatch_async(dispatch_get_main_queue(), ^{
//                self.image = aImage;
//            });
//        }
//    } failure:^(YTKBaseRequest * _Nullable request, NSError * _Nullable error) {
//        UIImage *aImage = [[UIImage alloc] initWithContentsOfFile:localPath];
//        if (completeBlock) {
//            completeBlock(aImage,error,imageUrl);
//        }
//        if (aImage) {
//            dispatch_async(dispatch_get_main_queue(), ^{
//                self.image = aImage;
//            });
//        }
//    } downloadBlock:^(double percent) {
//        if (completeBlock) {
//            completeBlock(nil,nil,imageUrl);
//        }
//    }];
}


@end
