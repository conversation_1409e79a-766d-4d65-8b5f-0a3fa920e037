//
//  JCItemToElement.m
//  XYFrameWork
//
//  Created by xingling xu on 2020/1/6.
//  Copyright © 2020 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCItemToElement.h"
#import "UIFont+JCCustomFont.h"

@implementation JCItemToElement

#pragma mark -- 根据服务端item数据生成元素
+ (JCElementBaseView *)elementWithItem:(JCElementModel *)item {
    JCTemplateType type = item.templateType;
    JCElementBaseView *element;
    element.elementId = item.elementId;
    if (type == JCTemplateType_Text) {
        element = [self textImageElement:item];
    } else if (type == JCTemplateType_BarCode) {
        element = [self barCode:item];
    } else if (type == JCTemplateType_QRCode) {
        element = [self qrCode:item];
    } else if (type == JCTemplateType_Graph) {
        element = [self graphWith:item];
    } else if (type == JCTemplateType_Line) {
        element = [self line:item];
    } else if (type == JCTemplateType_Image) {
        element = [self logo:item];
    } else if (type == JCTemplateType_Date) {
        element = [self time:item];
    } else if (type == JCTemplateType_SerialNumber) {
        element = [self number:item];
    }
    element.elementId = item.elementId;
    element.excelIndex = DrawBoardInfo.currentPageIndex;
    element.rotate = item.rotate;
    element.zIndex = item.zIndex;
    element.isLock = item.isLock;
    element.isTitle = item.isTitle;
    return element;
}

#pragma mark -- 文本
+ (JCElementText *)textElement:(JCElementModel *)item {
    JCElementText *element = [JCElementText new];
    CGFloat mm2px = DrawBoardInfo.mm2pxScale;
    element.frame = (CGRect){item.x*mm2px,item.y*mm2px,item.width*mm2px,item.height*mm2px};
    if (IsExcelString(item.value)) {
        element.excelValue = [item.value getExcelValue];
    } else {
        element.text = item.value?item.value:@"";
    }
    element.placeHolder = XY_LANGUAGE_TITLE_NAMED(@"",element_text_placeholder);
    element.configure = [self textConfigureWithItem:item];
    return element;
}

+ (JCElementTextImage *)textImageElement:(JCElementModel *)item {
    JCElementTextImage *element = [JCElementTextImage new];
    CGFloat mm2px = DrawBoardInfo.mm2pxScale;
    element.frame = (CGRect){item.x*mm2px,item.y*mm2px,item.width*mm2px,item.height*mm2px};
    if (IsExcelString(item.value)) {
        element.excelValue = [item.value getExcelValue];
    } else {
        element.text = item.value?item.value:@"";
    }
    element.placeHolder = XY_LANGUAGE_TITLE_NAMED(@"",element_text_placeholder);
    element.configure = [self textConfigureWithItem:item];
    return element;
}

+ (JCElementTextConfigure *)textConfigureWithItem:(JCElementModel *)item {
    JCFontStyle fontStyle = JCFontStyleNone;
    if ([item.fontStyle isKindOfClass:[NSArray class]]) {
        
        if ([item.fontStyle containsObject:@"bold"]) {/** 1 代表粗体  0 代表正常 */
            fontStyle |= JCFontStyleBold;
        }
        if ([item.fontStyle containsObject:@"underline"]) {/** 下划线 */
            fontStyle |= JCFontStyleUnderLine;
        }
        if ([item.fontStyle containsObject:@"italic"]) {/** 斜体 */
            fontStyle |= JCFontStyleItalic;
        }
    }
    JCElementTextConfigure *configure = [JCElementTextConfigure new];
    configure.fontStyle = fontStyle;
    configure.textSpace = item.letterSpacing;
    configure.lineMode = item.lineMode;
    configure.fontName = item.fontFamily;
    configure.mmFont = item.fontSize;
    configure.lineSpace = item.lineSpacing;
    configure.fontCode = item.fontCode;
    configure.textAlignVertical = item.textAlignVertical;
    configure.textAlignment = item.textAlignHorizonral;
    return configure;
}

#pragma mark -- 一维码
+ (JCElementBarCode *)barCode:(JCElementModel *)item {
    BarCodeTextLocation location = item.textPosition;

    JCElementBarCodeConfigure *configure = [JCElementBarCodeConfigure new];
    configure.textLocation = location;
    configure.codeType = item.codeType;
    configure.textHeight = item.textHeight;
    configure.textConfigure = [self textConfigureWithItem:item];
    
    JCElementBarCode *element = [JCElementBarCode new];
    CGFloat mm2px = DrawBoardInfo.mm2pxScale;
    element.frame = (CGRect){item.x*mm2px,item.y*mm2px,item.width*mm2px,item.height*mm2px};
    if (IsExcelString(item.value)) {
        element.excelValue = [item.value getExcelValue];
    } else {
        element.text = item.value?item.value:@"";
    }
    
    /**  扫描打印绑定了商品信息关闭excel导入功能 */
    if (item.fieldName && item.fieldName.length > 0) {
        element.fieldName = item.fieldName;
        element.closeExcelImport = YES;
    }
    element.configure = configure;
    
    return element;
}

#pragma mark -- 二维码
+ (JCElementQRCode *)qrCode:(JCElementModel *)item {
    JCElementQRCode *element = [JCElementQRCode new];
    CGFloat mm2px = DrawBoardInfo.mm2pxScale;
    element.frame = (CGRect){item.x*mm2px,item.y*mm2px,item.width*mm2px,item.width*mm2px};
    if (IsExcelString(item.value)) {
        element.excelValue = [item.value getExcelValue];
    } else {
        element.qrCodeString = item.value?item.value:@"";
    }
    element.codeType = item.codeType;
    element.correctLevel = item.correctLevel;
    return element;
}

#pragma mark -- 形状
+ (JCElementGraph *)graphWith:(JCElementModel *)item {
    JCElementGraph *element = [JCElementGraph new];
    CGFloat mm2px = DrawBoardInfo.mm2pxScale;
    element.frame = (CGRect){item.x*mm2px,item.y*mm2px,item.width*mm2px,item.height*mm2px};
    JCEleBoxConfigure *configure = [JCEleBoxConfigure new];
    configure.graphType = item.graphType;
    configure.mmLineWidth = item.lineWidth;
    configure.lineWidth = item.lineWidth*DrawBoardInfo.mm2pxScale;
    configure.lineStyle = item.lineType;
    configure.cornerRadius = item.cornerRadius;
    element.configure = configure;
    return element;
}

#pragma mark -- 线条
+ (JCElementLine *)line:(JCElementModel *)item {
    JCElementLine *element = [[JCElementLine alloc] init];
    CGFloat mm2px = DrawBoardInfo.mm2pxScale;
    element.frame = (CGRect){item.x*mm2px,item.y*mm2px,item.width*mm2px,item.height*mm2px};
    JCEleLineConfigure *configure = [JCEleLineConfigure new];
    configure.mmLineWidth = item.lineWidth;
    configure.lineWidth = item.lineWidth*DrawBoardInfo.mm2pxScale;
    configure.lineStyle = item.lineType;
    element.configure = configure;
    return element;
}

#pragma mark -- 图形
+ (JCElementLogoView *)logo:(JCElementModel *)item {
    JCElementLogoView *element = [JCElementLogoView new];
    CGFloat mm2px = DrawBoardInfo.mm2pxScale;
    element.frame = (CGRect){item.x*mm2px,item.y*mm2px,item.width*mm2px,item.height*mm2px};
    NSString *base64Str = item.imageData;
    NSString *imageUrl = item.imageUrl;
    NSString *localPath = ImageLocalPath(imageUrl);
    __block UIImage *image;
    if ([[NSFileManager defaultManager] fileExistsAtPath:localPath]) {
        NSData *data = [NSData dataWithContentsOfFile:localPath];
        image = [UIImage imageWithData:data];
    } else if (base64Str.length > 0) {
        NSData *data = [[NSData alloc] initWithBase64EncodedString:base64Str options:NSDataBase64DecodingIgnoreUnknownCharacters];
        image = [UIImage imageWithData:data];
    } else if (imageUrl.length > 0) {
        [[UIImageView new] sd_setImageWithURL:[NSURL URLWithString:imageUrl] completed:^(UIImage *aImage, NSError *error, SDImageCacheType cacheType, NSURL *imageURL) {
            image = aImage;
            element.image = image;
        }];
    }
    if (!image) return nil;
    element.elementId = item.elementId;
    element.thresHoldValue = [NSString stringWithFormat:@"%@",item.imageProcessingValue.firstObject];
    element.image = image;
    element.imageUrl = imageUrl;
    element.materialId = item.materialId;
    element.allowFreeZoom = [item.materialType isEqualToString:@"2"];
    return element;
}

#pragma mark -- 时间
+ (JCElementTime *)time:(JCElementModel *)item {
    JCEleTimeConfigure *timeConfigure = [JCEleTimeConfigure new];
    NSTimeInterval interval = [item.value doubleValue];
    timeConfigure.date = [XYTool dateFromMsTimeStamp:interval];
    timeConfigure.dateFormat = item.dateFormat;
    timeConfigure.timeFormat = STR_IS_NIL(item.timeFormat)?@"":item.timeFormat;
    JCElementTime *element = [JCElementTime new];
    CGFloat mm2px = DrawBoardInfo.mm2pxScale;
    element.frame = (CGRect){item.x*mm2px,item.y*mm2px,item.width*mm2px,item.height*mm2px};
    element.text = [item getDateString];
    element.placeHolder = @"";
    element.configure = [self textConfigureWithItem:item];
    element.timeConfigure = timeConfigure;
    return element;
}

#pragma mark -- 流水号
+ (JCElementSerialNumber *)number:(JCElementModel *)item {
    /** 配置格式 */
    JCSerialStyleConfigure *styleConfigure = [JCSerialStyleConfigure new];
    styleConfigure.prefix = item.prefix;
    styleConfigure.suffix = item.suffix;
    styleConfigure.beginNumber = item.startNumber;
    styleConfigure.increaseNumber = item.incrementValue;
    JCElementSerialNumber *element = [JCElementSerialNumber new];
    CGFloat mm2px = DrawBoardInfo.mm2pxScale;
    element.frame = (CGRect){item.x*mm2px,item.y*mm2px,item.width*mm2px,item.height*mm2px};
    element.text = item.value?item.value:@"";
    element.placeHolder = @"";
    element.configure = [self textConfigureWithItem:item];
    element.styleConfigure = styleConfigure;
    return element;
}
@end
