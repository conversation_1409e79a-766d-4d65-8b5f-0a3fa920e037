//
//  JCScanCodeManager.m
//  XYFrameWork
//
//  Created by xingling xu on 2020/5/16.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import "JCScanCodeManager.h"
#import "QQLBXScanViewController.h"
#import "Global.h"
#import "StyleDIY.h"

@implementation JCScanCodeManager
+ (UIViewController *)scanCodeControllerWithResult:(void(^)(NSString *code))result {
    QQLBXScanViewController *vc = [QQLBXScanViewController new];
    vc.scanCreateType = 0;
    vc.libraryType = [Global sharedManager].libraryType;
    vc.scanCodeType = [Global sharedManager].scanCodeType;
    vc.style = [StyleDIY qqStyle];
    vc.scanBlock = result;
    vc.isVideoZoom = YES;
    return vc;;
}
@end
