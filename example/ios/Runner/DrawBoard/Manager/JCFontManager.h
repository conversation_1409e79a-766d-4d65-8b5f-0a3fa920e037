//
//  JCFontManager.h
//  XYFrameWork
//
//  Created by xingling xu on 2020/2/19.
//  Copyright © 2020 Jingchen Technology Co.Ltd . All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface JCFontManager : NSObject
@property (nonatomic, copy) NSArray *enFontArr;
@property (nonatomic, copy) NSArray *cnFontArr;
+ (id)sharedManager;

- (void)requestFontWithParams:(NSDictionary *)params
                    cacheInfo:(void(^)(NSArray *enFonts,NSArray *cnFonts))cacheInfo
                   updateInfo:(void(^)(NSArray *enFonts,NSArray *cnFonts))updateInfo;

- (void)downLoadFontRequestWithModel:(JCFontModel*)model finishBlock:(void(^)(BOOL isscuess))finishBlock;

// 配置字体信息
- (void)configureFontMsg;


/// 查询
/// @param format <#format description#>
- (JCFontModel *)queryFontModelWithFormat:(NSString *)format;

/// 更新数据库某一条记录
/// @param model 新的纪录模型
- (BOOL)updateFontDBWithParam:(JCFontModel *)model;
- (void)updateFontDBWithLastUseDate:(NSString *)lastUseDate where:(NSString *)format;
@end

NS_ASSUME_NONNULL_END
