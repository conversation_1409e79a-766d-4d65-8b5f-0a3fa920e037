//
//  JCElementEditManager.m
//  XYFrameWork
//
//  Created by xingling xu on 2019/12/27.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCElementEditManager.h"
#import "UIFont+JCCustomFont.h"

/** why kind:时间，流水号都属于文本可统一操作 */
#define  KindOf(className)                [aElement isKindOfClass:[className class]]
/** why member：时间、流水号不同的要区分对待 */
#define  ElementIs(className)             [aElement isMemberOfClass:[className class]]
/** 强转 */
#define  ElementFor(StrongClass)          StrongClass *element = (StrongClass *)aElement;

@implementation JCElementEditManager

+ (BOOL)canShowKeyBoard:(JCElementBaseView *)aElement {
    if (ElementIs(JCElementText) || ElementIs(JCElementTextImage) || ElementIs(JCElementQRCode)) {
        return YES;
    } else if (ElementIs(JCElementBarCode)) {
        ElementFor(JCElementBarCode)
        NSString *fieldName = element.fieldName;
        if ([fieldName isEqualToString:@"barcode"]) {
            return NO;
        } else  {
            return YES;
        }
    }
    return NO;
}

+ (NSString *)getElementContent:(JCElementBaseView *)aElement {
    NSString *value = @"";
    if (ElementIs(JCElementText)) {
        ElementFor(JCElementText);
        value = element.text;
    } else if (ElementIs(JCElementTextImage)) {
        ElementFor(JCElementTextImage);
        value = element.text;
    } else if (ElementIs(JCElementBarCode)) {
        ElementFor(JCElementBarCode)
        value = element.text;
    } else if (ElementIs(JCElementQRCode)) {
        ElementFor(JCElementQRCode)
        value = element.qrCodeString;
    }
    return value;
}

+ (void)checkTaskElementExcelValue:(NSString *)excelValue elementId:(NSString *)elementId content:(NSString *)content  {
    if (IsExcelString(excelValue)) {
        [DrawBoardInfo taskChangeFor:elementId index:DrawBoardInfo.currentPageIndex withContent:content];
    }
}

+ (void)editElement:(JCElementBaseView *)aElement content:(NSString *)content {
    if (!aElement) return;
    if (ElementIs(JCElementText)) {
        ElementFor(JCElementText)
        element.text = content;
    } else if (ElementIs(JCElementTextImage)) {
        ElementFor(JCElementTextImage)
        [self checkTaskElementExcelValue:element.excelValue elementId:element.elementId content:content];
        NSString *placeHolder = STR_IS_NIL(element.fieldName)?XY_LANGUAGE_TITLE_NAMED(@"",element_text_placeholder):@"";
        element.text =  STR_IS_NIL(content)?placeHolder:content;
    } else if (ElementIs(JCElementBarCode)) {
        ElementFor(JCElementBarCode)
        [self checkTaskElementExcelValue:element.excelValue elementId:element.elementId content:content];
        element.text = content;
    } else if (ElementIs(JCElementQRCode)) {
        ElementFor(JCElementQRCode)
        [self checkTaskElementExcelValue:element.excelValue elementId:element.elementId content:content];
        element.qrCodeString = content;
    }
}

+ (void)editElement:(JCElementBaseView *)aElement frame:(CGRect)frame {
    aElement.frame = frame;
    // 由于都是用sdk去绘制的图片，所以要刷新
    [aElement reloadSDKImage];
}

+ (NSString *)getFontName:(JCElementBaseView *)aElement {
    NSString *fontName = XY_LANGUAGE_TITLE_NAMED(text_default_font_name, @"思源黑体");
    /** 此处用isKindOfClass，统一设置文本、时间、流水号的字体等 */
    if (KindOf(JCElementText)) {
        ElementFor(JCElementText)
        fontName = element.configure.fontName;
    } else if (KindOf(JCElementTextImage)) {
        ElementFor(JCElementTextImage)
        fontName = element.configure.fontName;
    }
    return fontName;
}

+ (void)editElement:(JCElementBaseView *)aElement fontName:(NSString *)fontName  {
    if (KindOf(JCElementText)) {
        ElementFor(JCElementText)
        JCElementTextConfigure *configure = element.configure;
        configure.fontName = fontName;
        element.configure = configure;
    } else if (KindOf(JCElementTextImage)) {
        ElementFor(JCElementTextImage)
        JCElementTextConfigure *configure = element.configure;
        configure.fontName = fontName;
        element.configure = configure;
    }
}

+ (CGFloat)getBarCodeMaxmmFont:(JCElementBaseView *)aElement {
    NSArray *mmFonts = [JCFontSize mmFontSizes];
    NSString *lastMM = mmFonts.lastObject;
    __block float mmFont = lastMM.floatValue;
    if (ElementIs(JCElementBarCode)) {
        ElementFor(JCElementBarCode);
        CGFloat mmMaxTextHeight = element.height/DrawBoardInfo.mm2pxScale-3;
        for (NSInteger i = mmFonts.count-1; i >= 0; i --) {
            JCElementTextConfigure *textConfigure = element.configure.textConfigure;
            CGFloat currentFontSize = [mmFonts[i] floatValue];
            UIFont *currentFont = [UIFont jc_fontWithFontName:textConfigure.fontCode fontSize:currentFontSize italic:textConfigure.fontStyle&JCFontStyleItalic];
            CGFloat strHeight = [element.text jk_heightWithFont:currentFont constrainedToWidth:element.width/DrawBoardInfo.mm2pxScale];
            if (strHeight <= mmMaxTextHeight) {
                if (i < mmFonts.count-1) {
                    mmFont = [[mmFonts safeObjectAtIndex:i + 1] floatValue];
                } else {
                    mmFont = currentFontSize;
                }
                break;
            }
        }
    }
    return mmFont;
}

/** 获取元素字体code */
+ (NSString *)getFontCode:(JCElementBaseView *)aElement {
    NSString *value = text_default_font_code;
    /** 此处用isKindOfClass，统一设置文本、时间、流水号的字体等 */
    if (KindOf(JCElementText)) {
        ElementFor(JCElementText)
        value = element.configure.fontCode;
    } else if (KindOf(JCElementTextImage)) {
        ElementFor(JCElementTextImage)
        value = element.configure.fontCode;
    }
    return value;
}
/** 设置元素字体code */
+ (void)editElement:(JCElementBaseView *)aElement fontCode:(NSString *)fontCode {
    if (KindOf(JCElementText)) {
        ElementFor(JCElementText)
        JCElementTextConfigure *configure = element.configure;
        configure.fontCode = fontCode;
        element.configure = configure;
        //保存config
        [[NSUserDefaults standardUserDefaults] setObject:[configure yy_modelToJSONObject] forKey:@"kLastUsedTextFontConfig"];
    } else if (KindOf(JCElementTextImage)) {
        ElementFor(JCElementTextImage)
        JCElementTextConfigure *configure = element.configure;
        configure.fontCode = fontCode;
        element.configure = configure;
        
        //保存config
        [[NSUserDefaults standardUserDefaults] setObject:[configure yy_modelToJSONObject] forKey:@"kLastUsedTextFontConfig"];
    }
}


+ (CGFloat)getElementFont:(JCElementBaseView *)aElement {
    CGFloat fontSize = text_default_font_mm_size;
    /**  here use 'isKindOfClass' method，统一设置文本、时间、流水号的字体等 */
    if (KindOf(JCElementText)) {
        ElementFor(JCElementText)
        fontSize = element.configure.mmFont;
    }else if (KindOf(JCElementTextImage)) {
        ElementFor(JCElementTextImage)
        fontSize = element.configure.mmFont;
    } else if (KindOf(JCElementBarCode)) {
        ElementFor(JCElementBarCode)
        fontSize = element.configure.textConfigure.mmFont;
    }
    return fontSize;
}

+ (void)editElement:(JCElementBaseView *)aElement fontSize_mm:(CGFloat)mmFont {
    if (!aElement) return;
    if (KindOf(JCElementText)) {
        ElementFor(JCElementText)
        JCElementTextConfigure *configure = element.configure;
        configure.mmFont = mmFont;
        element.configure = configure;
    } else if (KindOf(JCElementTextImage)) {
        ElementFor(JCElementTextImage)
        JCElementTextConfigure *configure = element.configure;
        configure.mmFont = mmFont;
        element.configure = configure;
    } else if (KindOf(JCElementBarCode)) {
        ElementFor(JCElementBarCode)
        JCElementBarCodeConfigure *configure = element.configure;
        configure.textConfigure.mmFont = mmFont;
        CGFloat strHeight = [element.text jk_heightWithFont:[configure.textConfigure getFont] constrainedToWidth:element.width];
        CGFloat elementMMheight = element.height/DrawBoardInfo.mm2pxScale;
        configure.textHeight = MIN(elementMMheight-BarImageMinHeight, strHeight);
        element.configure = configure;
    }
}

+ (BOOL)elementIsBold:(JCElementBaseView *)aElement {
    BOOL bold = NO;
    if (KindOf(JCElementText)) {
        ElementFor(JCElementText)
        JCElementTextConfigure *configure = element.configure;
        /** style contain bold */
        if (configure.fontStyle & JCFontStyleBold) {
            bold = YES;
        }
    } else if (KindOf(JCElementTextImage)) {
        ElementFor(JCElementTextImage)
        JCElementTextConfigure *configure = element.configure;
        /** style contain bold */
        if (configure.fontStyle & JCFontStyleBold) {
            bold = YES;
        }
    }
    return bold;
}

+ (void)editElement:(JCElementBaseView *)aElement bold:(BOOL)bold {
    if (!aElement) return;
    if (KindOf(JCElementText)) {
        ElementFor(JCElementText)
        JCElementTextConfigure *configure = element.configure;
        JCFontStyle style = configure.fontStyle;
        /** 按位异或 */
        style = style^JCFontStyleBold;
        configure.fontStyle = style;
        element.configure = configure;
    } else if (KindOf(JCElementTextImage)) {
        ElementFor(JCElementTextImage)
        JCElementTextConfigure *configure = element.configure;
        JCFontStyle style = configure.fontStyle;
        /** 按位异或 */
        style = style^JCFontStyleBold;
        configure.fontStyle = style;
        element.configure = configure;
    }
}

+ (BOOL)elementIsItalic:(JCElementBaseView *)aElement {
    BOOL value = NO;
    if (KindOf(JCElementText)) {
        ElementFor(JCElementText)
        if (element.configure.fontStyle & JCFontStyleItalic) {
            value = YES;
        }
    } else if (KindOf(JCElementTextImage)) {
        ElementFor(JCElementTextImage)
        if (element.configure.fontStyle & JCFontStyleItalic) {
            value = YES;
        }
    }
    return value;
}

+ (void)editElement:(JCElementBaseView *)aElement italic:(BOOL)italic {
    if (!aElement) return;
    if (KindOf(JCElementText)) {
        ElementFor(JCElementText)
        JCElementTextConfigure *configure = element.configure;
        JCFontStyle style = configure.fontStyle;
        style = style^JCFontStyleItalic;
        configure.fontStyle = style;
        element.configure = configure;
    } else if (KindOf(JCElementTextImage)) {
        ElementFor(JCElementTextImage)
        JCElementTextConfigure *configure = element.configure;
        JCFontStyle style = configure.fontStyle;
        style = style^JCFontStyleItalic;
        configure.fontStyle = style;
        element.configure = configure;
    }
}

+ (BOOL)elementIsUnderLine:(JCElementBaseView *)aElement {
    BOOL value = NO;
    if (KindOf(JCElementText)) {
        ElementFor(JCElementText)
        JCElementTextConfigure *configure = element.configure;
        if (configure.fontStyle & JCFontStyleUnderLine) {
            value = YES;
        }
    } else if (KindOf(JCElementTextImage)) {
        ElementFor(JCElementTextImage)
        JCElementTextConfigure *configure = element.configure;
        if (configure.fontStyle & JCFontStyleUnderLine) {
            value = YES;
        }
    }
    return value;
}

+ (void)editElement:(JCElementBaseView *)aElement underLine:(BOOL)underLine {
    if (!aElement) return;
    if (KindOf(JCElementText)) {
        ElementFor(JCElementText)
        JCElementTextConfigure *configure = element.configure;
        JCFontStyle style = configure.fontStyle;
        /** 按位异或 */
        style = style^JCFontStyleUnderLine;
        configure.fontStyle = style;
        element.configure = configure;
    } else if (KindOf(JCElementTextImage)) {
        ElementFor(JCElementTextImage)
        JCElementTextConfigure *configure = element.configure;
        JCFontStyle style = configure.fontStyle;
        /** 按位异或 */
        style = style^JCFontStyleUnderLine;
        configure.fontStyle = style;
        element.configure = configure;
    }
}

+ (NSTextAlignment)getElementTextAlignment:(JCElementBaseView *)aElement {
    NSTextAlignment value = NSTextAlignmentLeft;
    if (KindOf(JCElementText)) {
        ElementFor(JCElementText)
        JCElementTextConfigure *configure = element.configure;
        value = configure.textAlignment;
    } else if (KindOf(JCElementTextImage)) {
        ElementFor(JCElementTextImage)
        JCElementTextConfigure *configure = element.configure;
        value = configure.textAlignment;
    }
    return value;
}

+ (void)editElement:(JCElementBaseView *)aElement textAlignment:(NSTextAlignment)textAlignment {
    if (!aElement) return;
    if (KindOf(JCElementText)) {
        ElementFor(JCElementText)
        JCElementTextConfigure *configure = element.configure;
        configure.textAlignment = textAlignment;
        element.configure = configure;
    } else if (KindOf(JCElementTextImage)) {
        ElementFor(JCElementTextImage)
        JCElementTextConfigure *configure = element.configure;
        configure.textAlignment = textAlignment;
        element.configure = configure;
    }
}


+ (void)editElement:(JCElementBaseView *)aElement verticalTextAlignment:(JCFontVertical)textVerticalAlignment {
    if (!aElement) return;
    if (KindOf(JCElementTextImage)) {
        ElementFor(JCElementTextImage)
        JCElementTextConfigure *configure = element.configure;
        configure.textAlignVertical = textVerticalAlignment;
        element.configure = configure;
    }
}

+ (CGFloat)getEementTextSpace:(JCElementBaseView *)aElement {
    CGFloat value = 1;
    if (KindOf(JCElementText)) {
        ElementFor(JCElementText)
        JCElementTextConfigure *configure = element.configure;
        value = configure.textSpace;
    } else if (KindOf(JCElementTextImage)) {
        ElementFor(JCElementTextImage)
        JCElementTextConfigure *configure = element.configure;
        value = configure.textSpace;
    }
    return value;
}

+ (void)editElement:(JCElementBaseView *)aElement textSpace:(CGFloat)space {
    if (!aElement) return;
    if (KindOf(JCElementText)) {
        ElementFor(JCElementText)
        JCElementTextConfigure *configure = element.configure;
        configure.textSpace = space;
        element.configure = configure;
    } else if (KindOf(JCElementTextImage)) {
        ElementFor(JCElementTextImage)
        JCElementTextConfigure *configure = element.configure;
        configure.textSpace = space;
        element.configure = configure;
    }
}

+ (CGFloat)getEementLineSpace:(JCElementBaseView *)aElement {
    CGFloat value = 1;
    if (KindOf(JCElementText)) {
        ElementFor(JCElementText)
        JCElementTextConfigure *configure = element.configure;
        value = configure.lineSpace;
    } else if (KindOf(JCElementTextImage)) {
        ElementFor(JCElementTextImage)
        JCElementTextConfigure *configure = element.configure;
        value = configure.lineSpace;
    }
    return value;
}

+ (void)editElement:(JCElementBaseView *)aElement lineSpace:(CGFloat)space {
    if (!aElement) return;
    if (KindOf(JCElementText)) {
        ElementFor(JCElementText)
        JCElementTextConfigure *configure = element.configure;
        configure.lineSpace = space;
        element.configure = configure;
    } else if (KindOf(JCElementTextImage)) {
        ElementFor(JCElementTextImage)
        JCElementTextConfigure *configure = element.configure;
        configure.lineSpace = space;
        element.configure = configure;
    }
}

+ (NSInteger)getEementBarCodeIndex:(JCElementBaseView *)aElement {
    if (ElementIs(JCElementBarCode)) {
        ElementFor(JCElementBarCode)
        return element.configure.textLocation;
    }
    return 0;
}

+ (void)editElement:(JCElementBaseView *)aElement barCodeTextPosition:(NSInteger)index {
    if (!aElement) return;
    if (ElementIs(JCElementBarCode)) {
        ElementFor(JCElementBarCode)
        JCElementBarCodeConfigure *configure = element.configure;
        configure.textLocation = index;
        element.configure = configure;
    }
}

+ (NSString *)getElementCodeTypeName:(JCElementBaseView *)aElement {
    NSString *value = @"";
    if (ElementIs(JCElementBarCode)) {
        ElementFor(JCElementBarCode)
        value = [bar_code_types objectForKey:StringFromInt(element.configure.codeType)];
    } else if (ElementIs(JCElementQRCode)) {
        ElementFor(JCElementQRCode)
        value = [qr_code_types objectForKey:StringFromInt(element.codeType)];
    }
    return value;
}

+ (void)editElement:(JCElementBaseView *)aElement codeType:(JCCodeType)codeType {
    if (!aElement) return;
    if (ElementIs(JCElementBarCode)) {
        ElementFor(JCElementBarCode)
        JCElementBarCodeConfigure *configure = element.configure;
        configure.codeType = codeType;
        // 以下条码文字内容只允许显示在条码下方
        if (codeType == JCCodeType_EAN8 || codeType == JCCodeType_UPCA || codeType == JCCodeType_UPCE || codeType == JCCodeType_EAN13) {
            configure.textLocation = BartTextLocationBottom;
        }
        element.configure = configure;
    } else if (ElementIs(JCElementQRCode)) {
        ElementFor(JCElementQRCode)
        element.codeType = codeType;
    }
}

+ (void)editElement:(JCElementBaseView *)aElement codeTypeName:(NSString *)currentTypeName {
    if (!aElement) return;
    if (ElementIs(JCElementBarCode)) {
        ElementFor(JCElementBarCode)
        JCElementBarCodeConfigure *configure = element.configure;
        [bar_code_types enumerateKeysAndObjectsUsingBlock:^(NSString *codeNum, NSString  *codeName, BOOL * _Nonnull stop) {
            if ([currentTypeName isEqualToString:codeName]) {
                [JCElementEditManager editElement:element codeType:codeNum.integerValue];
                *stop = YES;
            }
        }];
        element.configure = configure;
    } else if (ElementIs(JCElementQRCode)) {
        ElementFor(JCElementQRCode)
        [qr_code_types enumerateKeysAndObjectsUsingBlock:^(NSString *codeNum, NSString  *codeName, BOOL * _Nonnull stop) {
            if ([currentTypeName isEqualToString:codeName]) {
                [JCElementEditManager editElement:element codeType:codeNum.integerValue];
                *stop = YES;
            }
        }];
    }
}


+ (NSInteger)getEementLineType:(JCElementBaseView *)aElement {
    NSInteger value = JCLineStyleSolid;// 实线
    if (ElementIs(JCElementGraph)) {
        ElementFor(JCElementGraph);
        value = element.configure.lineStyle;
    } else if (ElementIs(JCElementLine)) {
        ElementFor(JCElementLine)
        value = element.configure.lineStyle;
    }
    return value;
}

+ (void)editElement:(JCElementBaseView *)aElement lineType:(NSInteger)lineType {
    if (!aElement) return;
    lineType += 1;
    if (ElementIs(JCElementGraph)) {
        ElementFor(JCElementGraph);
        JCEleBoxConfigure *configure = element.configure;
        configure.lineStyle = lineType;
        element.configure = configure;
    } else if (ElementIs(JCElementLine)) {
        ElementFor(JCElementLine)
        JCEleLineConfigure *configure = element.configure;
        configure.lineStyle = lineType;
        element.configure = configure;
    }
}

+ (CGFloat)getEementLine_mmWidth:(JCElementBaseView *)aElement {
    CGFloat width = 1;
    if (ElementIs(JCElementGraph)) {
        ElementFor(JCElementGraph);
        width = element.configure.mmLineWidth;
    } else if (ElementIs(JCElementLine)) {
        ElementFor(JCElementLine)
        width = element.configure.mmLineWidth;
    }
    return width;
}

+ (void)editElement:(JCElementBaseView *)aElement mmLineWidth:(CGFloat)mmWidth {
    if (!aElement) return;
    if (ElementIs(JCElementGraph)) {
        ElementFor(JCElementGraph);
        JCEleBoxConfigure *configure = element.configure;
        configure.mmLineWidth = mmWidth;
        configure.lineWidth = mmWidth *DrawBoardInfo.mm2pxScale;
        element.configure = configure;
    } else if (ElementIs(JCElementLine)) {
        ElementFor(JCElementLine)
        JCEleLineConfigure *configure = element.configure;
        configure.mmLineWidth = mmWidth;
        configure.lineWidth = mmWidth*DrawBoardInfo.mm2pxScale;
        element.configure = configure;
    }
}

+ (NSString *)getEementDateFormatt:(JCElementBaseView *)aElement {
    NSString *value = @"";
    if (ElementIs(JCElementTime)) {
        ElementFor(JCElementTime)
        value = element.timeConfigure.dateFormat;
    }
    return value;
}

+ (void)editElement:(JCElementBaseView *)aElement dateFormatt:(NSString *)dateFormatt {
    if (ElementIs(JCElementTime)) {
        ElementFor(JCElementTime)
        JCEleTimeConfigure *configure = element.timeConfigure;
        configure.dateFormat = dateFormatt;
        element.timeConfigure = configure;
    }
}

+ (NSString *)getEementTimeFormatt:(JCElementBaseView *)aElement {
    NSString *value = @"";
    if (ElementIs(JCElementTime)) {
        ElementFor(JCElementTime)
        value = element.timeConfigure.timeFormat;
    }
    return value;
}

+ (void)editElement:(JCElementBaseView *)aElement timeFormatt:(NSString *)timeFormatt {
    if (ElementIs(JCElementTime)) {
        ElementFor(JCElementTime)
        JCEleTimeConfigure *configure = element.timeConfigure;
        configure.timeFormat = timeFormatt;
        element.timeConfigure = configure;
    }
}

+ (NSString *)getEementDateValue:(JCElementBaseView *)aElement {
    NSString *value = @"";
    if (ElementIs(JCElementTime)) {
        ElementFor(JCElementTime)
        NSString *dateFormat = element.timeConfigure.dateFormat;
        NSDate *date = element.timeConfigure.date;
        value = (STR_IS_NIL(dateFormat)||[dateFormat isEqualToString:@"无"])?@"":[date stringWithDateFormat:dateFormat];
    }
    return value;
}

+ (NSString *)getEementTimeValue:(JCElementBaseView *)aElement {
    NSString *value = @"";
    if (ElementIs(JCElementTime)) {
        ElementFor(JCElementTime)
        NSString *timeFormat = element.timeConfigure.timeFormat;
        NSDate *date = element.timeConfigure.date;
        value = (STR_IS_NIL(timeFormat) || [timeFormat isEqualToString:@"无"])?@"":[date stringWithDateFormat:timeFormat];
    }
    return value;
}

+ (void)editElement:(JCElementBaseView *)aElement date:(NSDate *)date  {
    if (ElementIs(JCElementTime)) {
        ElementFor(JCElementTime)
        JCEleTimeConfigure *timeConfigure = element.timeConfigure;
        timeConfigure.date = date;
        element.timeConfigure = timeConfigure;
    }
}

+ (JCGraphType)getEementShapeType:(JCElementBaseView *)aElement {
    if (ElementIs(JCElementGraph)) {
        ElementFor(JCElementGraph)
        return element.configure.graphType;
    }
    return JCGraphTypeRect;
}

+ (void)editElement:(JCElementBaseView *)aElement shapeType:(JCGraphType)graphType {
    if (ElementIs(JCElementGraph)) {
        ElementFor(JCElementGraph)
        JCEleBoxConfigure *configure =  element.configure;
        configure.graphType = graphType;
        element.configure = configure;
    }
}

+ (NSString *)getEementImageThresHoldValue:(JCElementBaseView *)aElement  {
    if (ElementIs(JCElementLogoView)) {
        ElementFor(JCElementLogoView)
        return element.thresHoldValue;
    }
    return @"";
}

+ (void)editElement:(JCElementBaseView *)aElement imageThresHoldValue:(NSString *)value  {
    if (ElementIs(JCElementLogoView)) {
        ElementFor(JCElementLogoView)
        element.thresHoldValue = value;
    }
}

+ (BOOL)getElementImageAllowFreeZoom:(JCElementBaseView *)aElement {
    BOOL value = NO;
    if (ElementIs(JCElementLogoView)) {
        ElementFor(JCElementLogoView)
        value = element.allowFreeZoom;
    }
    return value;
}

+ (void)editElement:(JCElementBaseView *)aElement allowFreeZoom:(BOOL)value {
    if (ElementIs(JCElementLogoView)) {
        ElementFor(JCElementLogoView)
        element.allowFreeZoom = value;
    }
}

+ (NSString *)elementColumnName:(JCElementBaseView *)aElement {
    __block NSString *value = @"";
    NSArray *array = [aElement.excelValue componentsSeparatedByString:excel_component];
    if (array.count == 2) {
        NSInteger columnIndex = [array.lastObject integerValue];
        JCExcelForElement *excelInfo = DrawBoardInfo.excelInfo;
        NSArray *columnHeaders = excelInfo.columnHeaders;
        NSString *columnName = [columnHeaders safeObjectAtIndex:columnIndex];
        value = columnName;
    }
    return value;
}

+ (void)editElement:(JCElementBaseView *)aElement columnIndex:(NSInteger)column {
    /** 选择的列内容数组 */
    // 切换列 删除之前修改过的数据
    [DrawBoardInfo deleteTaskForElementId:aElement.elementId];
    aElement.excelValue = [NSString stringWithFormat:@"0%@%ld",excel_component,column];
    aElement.excelIndex = DrawBoardInfo.currentPageIndex;
    [[NSNotificationCenter defaultCenter] postNotificationName:JCNOTICATION_Element_ImportExcel object:nil];
}

//-------------------------------------------------------------------------- 以下针对表格的编辑
/** 表格的添加、删除等操作 */
+ (void)editElement:(JCElementBaseView *)aElement event:(JCSheetAction)event withSelectIndexPaths:(NSMutableOrderedSet<NSIndexPath *> *)selectIndexPaths {
    
}
/** 某个单元格的行高 */
+ (CGFloat)rowHeight:(NSIndexPath *)indexPath {
    return 0;
}
/** 某个单元格的列宽 */
+ (CGFloat)columnWidth:(NSIndexPath *)indexPath {
    return 0;
}
//--------------------------------------------------------------------------
@end
