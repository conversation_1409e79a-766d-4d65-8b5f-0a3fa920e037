//
//  JCFontDownLoadManager.m
//  Runner
//
//  Created by xingling xu on 2021/2/22.
//

#import "JCFontDownLoadManager.h"
#import "JCTemplateData+External.h"

@interface JCFontDownLoadManager ()
@property (nonatomic, strong) JCTemplateData *data;
@property (nonatomic, copy) NSArray *downLoadArr;
@property (nonatomic, assign) NSInteger currentIndex;
@property (nonatomic, copy) NSString *currentFontName;
@property (nonatomic, copy) NSString *currentFontCode;
@property (nonatomic, assign) BOOL stop;
@end

@implementation JCFontDownLoadManager

- (instancetype)initWithTemplateData:(JCTemplateData *)data {
    self = [super init];
    if (self) {
        self.data = data;
        self.downLoadArr = [self.data fontModelsNotDownload];
//        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(downLoadPressNotification:) name:JCNOTICATION_DOWNLOADPRESS_ChenYin object:nil];
    }
    return self;
}

- (void)downLoadCurrentFont {
    JCFontModel *fontModel = [self.downLoadArr safeObjectAtIndex:self.currentIndex];
    self.currentFontName = fontModel.name;
    self.currentFontCode = fontModel.fontCode;
    if (!fontModel || ![fontModel isKindOfClass:[JCFontModel class]]) return;
    NSString *saveUrlString = [NSString stringWithFormat:@"%@/font/%@.%@",DocumentsFontPath,fontModel.fontCode,fontModel.url.pathExtension];
    [DCHTTPRequest downLoadFileWithParams:@{} UrlString:fontModel.url savePath:saveUrlString tagString:fontModel.fontCode Success:^(__kindof YTKBaseRequest * _Nonnull request, id  _Nonnull responseObject) {
        [JCFontModel saveTime:[XYTool getNowTimeTimestamp] fontCode:fontModel.fontCode];
        // 继续下载
        self.currentIndex++;
        if (self.currentIndex == self.downLoadArr.count) {
            NSLog(@"全部下载完毕");return;
        }
        [self downLoadCurrentFont];
    } failure:^(YTKBaseRequest * _Nullable request, NSError * _Nullable error) {
       
    } downloadBlock:^(double percent) {

    }];
}

- (void)downLoadFonts:(void(^)(NSString *fontName,NSString *fontCode ,double percent))block completion:(void(^)(void))completion {
    if (self.stop) return;
    JCFontModel *fontModel = [self.downLoadArr safeObjectAtIndex:self.currentIndex];
    self.currentFontName = fontModel.name;
    self.currentFontCode = fontModel.fontCode;
    if (!fontModel || ![fontModel isKindOfClass:[JCFontModel class]]) return;
    NSString *saveUrlString = [NSString stringWithFormat:@"%@/font/%@.%@",DocumentsFontPath,fontModel.fontCode,fontModel.url.pathExtension];
    [DCHTTPRequest downLoadFileWithParams:@{} UrlString:fontModel.url savePath:saveUrlString tagString:fontModel.fontCode Success:^(__kindof YTKBaseRequest * _Nonnull request, id  _Nonnull responseObject) {
        [JCFontModel saveTime:[XYTool getNowTimeTimestamp] fontCode:fontModel.fontCode];
        // 继续下载
        self.currentIndex++;
        if (self.currentIndex == self.downLoadArr.count) {
            if (completion) {
                completion();
            }
            NSLog(@"全部下载完毕");return;
        }
        [self downLoadFonts:block completion:completion];
    } failure:^(YTKBaseRequest * _Nullable request, NSError * _Nullable error) {
        self.currentIndex++;
        if (self.currentIndex == self.downLoadArr.count) {
            if (completion) {
                completion();
            }
            NSLog(@"全部下载完毕");return;
        }
        [self downLoadFonts:block completion:completion];
    } downloadBlock:^(double percent) {
        if (block) {
            block(self.currentFontName,self.currentFontCode,percent);
        }
    }];
}

- (void)cancelDownLoad {
    self.stop = YES;
}


@end
