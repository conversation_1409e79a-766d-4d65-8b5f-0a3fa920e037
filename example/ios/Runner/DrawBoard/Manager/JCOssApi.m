//
//  JCOssApi.m
//  XYFrameWork
//
//  Created by APP on 2020/4/15.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import "JCOssApi.h"

@interface JCOssApi ()

@property(nonatomic,strong) NSString *urlPathStr;

@property(nonatomic,assign)  YTKRequestMethod httpMethod;

@property(nonatomic,strong) NSDictionary *paramtersDic;


@end

@implementation JCOssApi

- (instancetype)initWithUrl:(NSString *)url Method:(YTKRequestMethod)method Paramters:(NSDictionary *)paramters
{
    if (self = [super init]) {
        self.urlPathStr = url;
        self.httpMethod = method;
        self.paramtersDic =paramters;
    }
    return self;
}

-(YTKRequestMethod)requestMethod
{
    return self.httpMethod;
    
}

-(NSString *)requestUrl
{
    return self.urlPathStr;
}

-(id)requestArgument
{
    return self.paramtersDic;
}

- (NSDictionary *)requestHeaderFieldValueDictionary {
    return @{@"apikey": @"82b3nohbVk5NLcGKUnWcRGMleCAGMvDsF37N"};
}

@end
