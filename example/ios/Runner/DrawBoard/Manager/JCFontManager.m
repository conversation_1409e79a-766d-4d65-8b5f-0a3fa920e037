//
//  JCFontManager.m
//  XYFrameWork
//
//  Created by xingling xu on 2020/2/19.
//  Copyright © 2020 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCFontManager.h"
#import "JCFontModel.h"

static NSString *const cn_type = @"zh";
static NSString *const en_type = @"en";

@implementation JCFontManager

+ (id)sharedManager {
    static dispatch_once_t once;
    static id instance;
    dispatch_once(&once, ^{
        instance = [self new];
    });
    return instance;
}

- (void)configureFontMsg {
    [self copyFontToLocal];
    
    [self requestFontWithParams:@{@"type":@"1",@"page":@"1",@"limit":@"100",@"status":@"1"} cacheInfo:^(NSArray * _Nonnull enFonts, NSArray * _Nonnull cnFonts) {
        
    } updateInfo:^(NSArray * _Nonnull enFonts, NSArray * _Nonnull cnFonts) {
        
    }];
}

- (void)downLoadFontRequestWithModel:(JCFontModel*)model finishBlock:(void(^)(BOOL isscuess))finishBlock {
    if (NETWORK_STATE_ERROR) {
        if (finishBlock) {
            finishBlock(false);
        }
        return;
    }
    NSString *saveUrlString = [NSString stringWithFormat:@"%@/font/%@.%@",DocumentsFontPath,model.fontCode,model.url.pathExtension];
    [DCHTTPRequest downLoadFileWithParams:@{} UrlString:model.url savePath:saveUrlString tagString:model.fontCode Success:^(__kindof YTKBaseRequest * _Nonnull request, id  _Nonnull responseObject) {
        if (finishBlock) {
            finishBlock(true);
        }
    } failure:^(YTKBaseRequest * _Nullable request, NSError * _Nullable error) {
        if (finishBlock) {
            finishBlock(false);
        }
    } downloadBlock:^(double percent) {
        
    }];
}

- (void)requestFontWithParams:(NSDictionary *)params cacheInfo:(void(^)(NSArray *enFonts,NSArray *cnFonts))cacheInfo updateInfo:(void(^)(NSArray *enFonts,NSArray *cnFonts))updateInfo {
   __block NSArray *fonts = [[JCFMDB shareDatabase:DB_NAME]  jc_lookupTable:TABLE_FONTINFO dicOrModel:[JCFontModel class] whereFormat:nil];
    NSMutableArray *temp = [NSMutableArray arrayWithArray:self.cnFontArr];
    if (fonts.count > 0) {
        [temp addObjectsFromArray:fonts];
    }
    /** 回调缓存数据提高显示速度 */
    if (cacheInfo) {
        cacheInfo(nil,temp);
    }
    [DCHTTPRequest postWithParams:params ModelType:[JCFontModel class] Path:@"kraken/operation/fontlib/list" Json:YES Success:^(__kindof YTKBaseRequest * _Nonnull request, NSArray *models) {
        //迁移原来数据中的最后使用信息到新的数据库中
        for (JCFontModel *oldModel in fonts) {
            for (JCFontModel *newModel in models) {
                if ([oldModel.xyid isEqualToString:newModel.xyid]) {
                    newModel.lastUseTimeStamp = oldModel.lastUseTimeStamp;
                    continue;
                }
            }
        }
        /** 不区分中文英文 */
        NSMutableArray *temp = [NSMutableArray arrayWithArray:self.cnFontArr];
        if (models.count > 0) {
            [temp addObjectsFromArray:models];
        }
        if (updateInfo) {
            updateInfo(nil,temp);
        }
        //删除原来的数据
        [[JCFMDB shareDatabase:DB_NAME] jc_deleteAllDataFromTable:TABLE_FONTINFO];
        //记录数据
        [[JCFMDB shareDatabase:DB_NAME] jc_insertTable:TABLE_FONTINFO dicOrModelArray:models];
    } failure:^(YTKBaseRequest * _Nullable request, NSError * _Nullable error) {
        
    }];
}

- (void)copyFontToLocal {
    NSFileManager* fileManager=[NSFileManager defaultManager];
    NSString *fontPath = [NSString stringWithFormat:@"%@/font",DocumentsFontPath];
    if(![fileManager fileExistsAtPath:fontPath]){
        [fileManager createDirectoryAtPath:fontPath withIntermediateDirectories:YES attributes:nil error:nil];
    }

    NSString*cnDefaultFile =[fontPath stringByAppendingPathComponent:@"ZT001.ttf"];
    if(![fileManager fileExistsAtPath:cnDefaultFile]){
        NSString *resourcePath =[[NSBundle mainBundle] pathForResource:@"ZT001" ofType:@"ttf"];
        [fileManager copyItemAtPath:resourcePath toPath:cnDefaultFile error:nil];
    }
}

//- (NSArray *)copyPropertyNamed:(NSString *)name From:(NSArray *)fromModels to:(NSArray *)toModels {
//
//}

- (BOOL)updateFontDBWithParam:(JCFontModel *)model {
   return [[JCFMDB shareDatabase:DB_NAME] jc_updateTable:TABLE_FONTINFO dicOrModel:model whereFormat:[NSString stringWithFormat:@"where xyid = %@",model.xyid]];
}
- (void)updateFontDBWithLastUseDate:(NSString *)lastUseTimeStamp where:(NSString *)format {
   NSArray *arr = [[JCFMDB shareDatabase:DB_NAME] jc_lookupTable:TABLE_FONTINFO dicOrModel:[JCFontModel class] whereFormat:format];
    if (arr.count > 0) {
       JCFontModel *model = arr.firstObject;
        model.lastUseTimeStamp = lastUseTimeStamp;
        [[JCFMDB shareDatabase:DB_NAME] jc_updateTable:TABLE_FONTINFO dicOrModel:model whereFormat:nil];
    }else{
        
    }
}
#pragma mark - getter
- (NSArray *)enFontArr {
    if (!_enFontArr) {
        _enFontArr = [[JCFMDB shareDatabase:DB_NAME] jc_lookupTable:TABLE_FONTINFO dicOrModel:[JCFontModel class] whereFormat:@"where langType = 'en'"];
        if (_enFontArr.count == 0) {
            _enFontArr = @[[JCFontModel defaultEnFont]];
        }
    }
    return _enFontArr;
}

- (NSArray *)cnFontArr {
    if (!_cnFontArr) {
        _cnFontArr = [[JCFMDB shareDatabase:DB_NAME] jc_lookupTable:TABLE_FONTINFO dicOrModel:[JCFontModel class] whereFormat:@"where langType = 'zh'"];
        if (_cnFontArr.count == 0) {
            _cnFontArr = @[[JCFontModel defaultCnFont]];
        }
    }
    //设置默认字体的使用时间戳,用于排序
    ((JCFontModel *)_cnFontArr.firstObject).lastUseTimeStamp = [XYTool getNowTimeTimestamp];
    return _cnFontArr;
}
@end
