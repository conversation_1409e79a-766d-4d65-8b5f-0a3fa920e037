//
//  JCElementToItem.m
//  XYFrameWork
//
//  Created by xingling xu on 2020/1/6.
//  Copyright © 2020 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCElementToItem.h"
#import "UIFont+JCCustomFont.h"

#define  ElementIs(className)             [aElement isMemberOfClass:[className class]]
#define  ElementFor(StrongClass)          StrongClass *element = (StrongClass *)aElement;// 强转

@implementation JCElementToItem
+ (JCElementModel *)itemForElement:(JCElementBaseView *)aElement {
    JCElementModel *item = [JCElementModel new];
    CGRect frame = [aElement getOrignalFrame];
    if (ElementIs(JCElementText)) {
        ElementFor(JCElementText)
        item.type = @"text";
        NSString *value = (element.text && element.text.length > 0)?element.text:XY_LANGUAGE_TITLE_NAMED(@"",element_text_placeholder);
        item.value = IsExcelString(element.excelValue)?element.excelValue:value;
        /** 设置文本的相关属性 */
        [self configureItem:item with:element.configure];
    } else if (ElementIs(JCElementTextImage)) {
        ElementFor(JCElementTextImage)
        item.type = @"text";
        if (!STR_IS_NIL(element.fieldName)) item.fieldName = element.fieldName;
        NSString *value = STR_IS_NIL(element.text)?@"":element.text;;
        item.value = IsExcelString(element.excelValue)?element.excelValue:value;
        /** 设置文本的相关属性 */
        [self configureItem:item with:element.configure];
    }
    else if (ElementIs(JCElementBarCode)) {
        ElementFor(JCElementBarCode)
        item.type = @"barcode";
        NSString *value = element.text?element.text:@"";
        item.value = IsExcelString(element.excelValue)?element.excelValue:value;
        if (!STR_IS_NIL(element.fieldName)) item.fieldName = element.fieldName;
        JCElementBarCodeConfigure *configure = element.configure;
        [self configureItem:item with:configure.textConfigure];
        item.codeType = configure.codeType;
        BarCodeTextLocation textLocation = configure.textLocation;
        item.textHeight = configure.textHeight;
        item.textPosition = textLocation;
    } else if (ElementIs(JCElementQRCode)) {
        ElementFor(JCElementQRCode)
        item.type = @"qrcode";
        NSString *value = element.qrCodeString;
        item.value = IsExcelString(element.excelValue)?element.excelValue:value;
        item.codeType = element.codeType;
        item.correctLevel = element.correctLevel;
    } else if (ElementIs(JCElementTime)) {
        ElementFor(JCElementTime)
        /** 设置文本的相关属性 */
        [self configureItem:item with:element.configure];
        /** 设置时间相关属性 */
        JCEleTimeConfigure *timeConfigure = element.timeConfigure;
        item.dateFormat = timeConfigure.dateFormat;
        item.timeFormat = (STR_IS_NIL(timeConfigure.timeFormat)||[timeConfigure.timeFormat isEqualToString:@"无"])?@"":timeConfigure.timeFormat;
        /** 传送给服务器的是毫秒时间戳*/
        item.value = [XYTool timeStampFrom:timeConfigure.date];
        item.type = @"date";
    } else if (ElementIs(JCElementSerialNumber)) {
        ElementFor(JCElementSerialNumber)
        item.type = @"serial";
        NSString *value = element.text?element.text:@"";
        item.value = value;
        /** 设置文本的相关属性 */
        [self configureItem:item with:element.configure];
        JCSerialStyleConfigure *configure = element.styleConfigure;
        NSString *prefix = configure.prefix;
        NSString *suffix = configure.suffix;
        item.prefix = prefix;
        item.suffix = suffix;
        item.startNumber = configure.beginNumber;
        item.incrementValue = configure.increaseNumber;
        item.value = [NSString stringWithFormat:@"%@%@%@",prefix?prefix:@"",item.startNumber,suffix?suffix:@""];
    } else if (ElementIs(JCElementGraph)) {
        ElementFor(JCElementGraph)
        item.type = @"graph";
        JCEleBoxConfigure *configure = element.configure;
        item.graphType = configure.graphType;
        item.lineWidth = configure.mmLineWidth;
        float dashWidth = dash_line_default_width;
        item.dashwidth = @[@(dashWidth),@(dashWidth)];
        item.lineType = configure.lineStyle;
        item.cornerRadius = configure.cornerRadius;
    } else if (ElementIs(JCElementLine)) {
        ElementFor(JCElementLine)
        item.type = @"line";
        JCEleLineConfigure *configure = element.configure;
        item.lineWidth = configure.mmLineWidth;
        item.lineType = configure.lineStyle;
        float dashWidth = dash_line_default_width;
        item.dashwidth = @[@(dashWidth),@(dashWidth)];
    } else if (ElementIs(JCElementLogoView)) {
        ElementFor(JCElementLogoView)
        item.type = @"image";
        if (element.image) {
            NSData *data = UIImagePNGRepresentation(element.image);
            NSString *encodedImageStr = [data base64EncodedStringWithOptions:NSDataBase64Encoding64CharacterLineLength];
            item.imageData = encodedImageStr;
        }
        if (UN_NIL(element.imageUrl)) {
            item.imageUrl = element.imageUrl;
        }
        item.imageProcessingType = 1;
        NSString *thresHoldValue = element.thresHoldValue?element.thresHoldValue:@"127";
        item.imageProcessingValue = @[@(thresHoldValue.integerValue)];
        item.materialId = element.materialId;
        item.materialType = element.allowFreeZoom?@"2":@"1";
    }
    CGFloat mm2pxScale = DrawBoardInfo.mm2pxScale;
    item.x = frame.origin.x/mm2pxScale;
    item.y = frame.origin.y/mm2pxScale;
    item.width = frame.size.width/mm2pxScale;
    item.height = frame.size.height/mm2pxScale;
    item.rotate = aElement.rotate;
    item.elementId = aElement.elementId;
    item.zIndex = aElement.zIndex;
    item.isLock = aElement.isLock;
    item.isTitle = aElement.isTitle;
    return item;
}

+ (void)configureItem:(JCElementModel *)item  with:(JCElementTextConfigure *)configure {
    JCFontStyle style = configure.fontStyle;
    // 字体样式
    NSMutableArray *fontStyleArr = [NSMutableArray array];
    if (style & JCFontStyleBold) { [fontStyleArr addObject:@"bold"]; }
    if (style & JCFontStyleItalic) { [fontStyleArr addObject:@"italic"]; }
    if (style & JCFontStyleUnderLine) { [fontStyleArr addObject:@"underline"]; }
    
    // 当前模板mm转像素倍率
    CGFloat mm2pxScale = DrawBoardInfo.mm2pxScale;
    // 字体行高转换
//    BOOL italic = style & JCFontStyleItalic;// 是否倾斜
//    UIFont *font = [UIFont jc_fontWithFontName:configure.fontName fontSize:configure.mmFont*mm2pxScale italic:italic];
//    CGFloat lineHeight = font.lineHeight/mm2pxScale;
//    CGFloat pointSize = font.pointSize/mm2pxScale;
    item.fontFamily = configure.fontName;
    item.fontSize = configure.mmFont;
    item.textAlignHorizonral = configure.textAlignment;
    item.textAlignVertical = configure.textAlignVertical;
    item.lineMode = configure.lineMode;
    item.letterSpacing = configure.textSpace;
    // 行间距使用lineHeight
    item.lineSpacing = configure.lineSpace;
    item.fontStyle = fontStyleArr;
    item.fontCode = configure.fontCode;
}

@end
