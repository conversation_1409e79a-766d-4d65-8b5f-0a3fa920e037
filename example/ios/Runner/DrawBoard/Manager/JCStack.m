//
//  JCStack.m
//  XYFrameWork
//
//  Created by xingling xu on 2019/12/30.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCStack.h"

@interface JCStack ()
@property (nonatomic, strong) NSMutableArray *stack;
@property (nonatomic, assign) NSInteger capacity;
@property (nonatomic, assign) NSInteger max;
@end

@implementation JCStack

- (instancetype)init {
    if (self = [super init]) {
        self.capacity = 2;
        self.max = NSIntegerMax;
    }
    return self;
}

- (instancetype)initWithCapacity:(NSInteger)capacity {
    if (self = [super init]) {
        self.capacity = capacity;
        self.max = NSIntegerMax;
    }
    return self;
}

- (void)push:(id)object {
    if (!object) return;
    [self.stack addObject:object];
    if (self.stackLength > self.max) {
        [self.stack removeObject:self.stack[0]];
    }
}

- (id)pop {
    id object = self.stack.lastObject;
    if (object) {
        [self.stack removeLastObject];
    }
    return  object;
}

- (id)topObject {
    return self.stack.lastObject;
}

- (void)clear {
    [self.stack removeAllObjects];
}

- (BOOL)empty {
    return self.stack.count < 1;
}


- (NSInteger)stackLength {
    return self.stack.count;
}

- (JCStack *(^)(NSInteger value))maxLength {
    return ^(NSInteger value){
        self.max = value;
        return self;
    };
}

#pragma mark -- lazy
- (NSMutableArray *)stack {
    if (!_stack) {
        _stack = [NSMutableArray arrayWithCapacity:self.capacity];
    }
    return _stack;
}

@end
