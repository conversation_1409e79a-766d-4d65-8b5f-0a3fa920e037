//
//  JCDrawInfoManager.h
//  XYFrameWork
//
//  Created by xingling xu on 2019/12/13.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import <Foundation/Foundation.h>
#import "JCExcelForElement.h"

static NSInteger const default_sheet_index = 0;

/** 保存画板基础信息的单例 */
#define DrawBoardInfo  [JCDrawInfoManager sharedManager]

@class JCTemplateData;
/*
 单例，提供画板的额外信息 画板的配置信息等都可以存放
 */
@interface JCDrawInfoManager : NSObject

+ (instancetype)sharedManager;

/** 当前模板宽度，基于当前单位，如：毫米 */
@property (nonatomic, assign) CGFloat template_width_mm;
/** 当前模板高度，基于当前单位，如：毫米 */
@property (nonatomic, assign) CGFloat template_height_mm;
/** 画板的宽度，基于像素 */
@property (nonatomic, assign) CGFloat boardWidth;
/** 画板的高度，基于像素 */
@property (nonatomic, assign) CGFloat boardHeight;
/** 当前画板Excel导入后选择的页码 */
@property (nonatomic, assign) NSInteger currentPageIndex;
/** excel导入的最大页数 */
@property (nonatomic, assign) NSInteger excelPageNumMax;

@property (nonatomic, strong, readonly) JCExcelForElement *excelInfo;
/** excel数据 */
@property (nonatomic, strong) NSDictionary *externalData;
/** 修改过的数据 */
@property (nonatomic, strong) NSDictionary *task;
/** mm转像素系数 */
@property (nonatomic, assign) CGFloat mm2pxScale;
/** 当前打印机的系数 */
@property (nonatomic, assign) CGFloat dpiScale;

@property (nonatomic, assign) BOOL hasExcelElement;
// 当前画板添加元素
@property (nonatomic, assign) NSInteger addElementIndex;
// 扫码取模
@property (nonatomic, assign) BOOL isFromScanCode;
/** 标签纸ID */
@property (nonatomic, copy) NSString *labelId;
/** 标签尺寸id */
@property (nonatomic, copy) NSString *sizeId;
/** 设备系列id */
@property (nonatomic, copy) NSString *deviceId;
/** 最小浓度 */
@property (nonatomic, assign) NSInteger minConcentrate;
/** 最大浓度 */
@property (nonatomic, assign) NSInteger maxConcentrate;
/** 默认浓度 */
@property (nonatomic, assign) NSInteger defaultConcentrate;

// 根据model来更新
- (void)updateInfoWith:(JCTemplateData *)data;
// 更新模板excel导入最大页数
- (void)updatePageNumber:(NSInteger)pageNumber;

- (void)updateExcelInfo:(JCExcelForElement *)exc;
// 画板界面替换excel文件
- (void)changeExcel:(JCExcelForElement *)exc columnIndex:(NSInteger)columnIndex;
// 只能通过此方法来改变excel数据
- (void)updateExcelInfo:(JCExcelForElement *)exc columnIndex:(NSInteger)columnIndex;
// 获取当前excel文件的某一列的数据个数
- (NSInteger)getExcelNumbersWithExcelColumnInex:(NSInteger)columnIndex;
// 获取当前选择的列名
- (NSString *)columnName:(NSInteger)sheetIndex columnIndex:(NSInteger)column;
// 根据当前元素的格式化字符串 获取当前元素的显示内容
- (NSString *)currentValueFor:(NSString *)excelValue selectRowIndex:(NSInteger)rowIndex elementId:(NSString *)elementId;
// 导入excel后修改某一个元素的某一行的数据
- (void)taskChangeFor:(NSString *)elementId index:(NSInteger)pageIndex withContent:(NSString *)content;
// 将老数据中的被改变数据复制到新元素数据中
- (void)copyTaskInfoFrom:(NSString *)oldElementId to:(NSString *)newElementId;
// 切换列删除修改过的内容
- (void)deleteTaskForElementId:(NSString *)elementId;

- (void)clear;
@end

