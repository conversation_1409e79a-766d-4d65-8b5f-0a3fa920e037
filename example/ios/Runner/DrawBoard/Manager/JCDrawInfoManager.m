//
//  JCDrawInfoManager.m
//  XYFrameWork
//
//  Created by xingling xu on 2019/12/13.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCDrawInfoManager.h"
#import "JCApplicationManager.h"


@implementation JCDrawInfoManager {
    JCExcelForElement *_info;
    dispatch_semaphore_t _semaphore;
}

+ (instancetype)sharedManager
{
    static id instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[self alloc] init];
    });
    return instance;
}

- (instancetype)init {
    if (self) {
        self->_semaphore = dispatch_semaphore_create(1);
        self.excelPageNumMax = 1;
        self.currentPageIndex = 0;
    }
    return self;
}

- (void)updateInfoWith:(JCTemplateData *)data {
    DrawBoardInfo.template_width_mm = data.width;
    DrawBoardInfo.template_height_mm = data.height;
    self.externalData = data.externalData;
    self.task = data.task;
    _info = [JCExcelForElement excelInfoWith:data.externalData];
    self.hasExcelElement = [data hasExcelElement];
    self.currentPageIndex = data.currentPage == 0?0:data.currentPage-1;
    self.excelPageNumMax = [self getExcelPageNumMaxFrom:data];
    if (data.sizeId.integerValue > 0) {
        self.sizeId = data.sizeId;
    }
    if (data.labelId.integerValue > 0) {
        self.labelId = data.labelId;
    }
}

- (NSInteger)getExcelPageNumMaxFrom:(JCTemplateData *)data {
    if (![data hasExcelElement]) return 1;
    __block NSInteger __currentMaxPage = 1;
    [data.elements enumerateObjectsUsingBlock:^(JCElementModel *model, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([model hasExcelImport]) {
            __currentMaxPage = MAX(__currentMaxPage, [model currentExcelNumber]);
        }
    }];
    return __currentMaxPage;
}

- (NSInteger)getExcelNumbersWithExcelColumnInex:(NSInteger)columnIndex {
    NSArray *columnArr = self.excelInfo.columnArr;
    NSArray *currentRow = [columnArr safeObjectAtIndex:columnIndex];
    return currentRow.count;
}

- (void)updatePageNumber:(NSInteger)pageNumber {
    self.excelPageNumMax = MAX(self.excelPageNumMax, pageNumber);
}

- (void)setIsFromScanCode:(BOOL)isFromScanCode {
    _isFromScanCode = isFromScanCode;
}

// 更换excel文件
- (void)updateExcelInfo:(JCExcelForElement *)exc {
    NSDictionary *externalData = [exc convert2ExternalData];
    // 重置
    DrawBoardInfo.externalData = externalData;
    DrawBoardInfo.task = [NSDictionary dictionary];
    // 配置info
    dispatch_semaphore_wait(_semaphore, DISPATCH_TIME_FOREVER);
    _info = exc;
    dispatch_semaphore_signal(_semaphore);
}

- (void)changeExcel:(JCExcelForElement *)exc columnIndex:(NSInteger)columnIndex {
    [self updateExcelInfo:exc];
    self.task = nil;
    // columnIndex
    NSArray *columnArr = _info.columnArr;
    NSArray *currentRow = [columnArr safeObjectAtIndex:columnIndex];
    self.excelPageNumMax = currentRow.count;
}

- (void)updateExcelInfo:(JCExcelForElement *)exc columnIndex:(NSInteger)columnIndex {
    [self updateExcelInfo:exc];
    // columnIndex
    NSArray *columnArr = _info.columnArr;
    NSArray *currentRow = [columnArr safeObjectAtIndex:columnIndex];
    [self updatePageNumber:currentRow.count];
}

- (NSString *)columnName:(NSInteger)sheetIndex columnIndex:(NSInteger)column {
    NSArray *columnHeaders = self.excelInfo.columnHeaders;
    NSString *columnName = [columnHeaders safeObjectAtIndex:column];
    return UN_NIL(columnName);
}

- (NSString *)currentValueFor:(NSString *)excelValue selectRowIndex:(NSInteger)rowIndex elementId:(NSString *)elementId {
    NSString *value = @"";
    NSArray *array = [excelValue componentsSeparatedByString:excel_component];
    if (array.count == 2) {
        NSInteger columnIndex = [array.lastObject integerValue];
        NSArray *columnInfo = [self.excelInfo.columnArr safeObjectAtIndex:columnIndex];
        NSString *string = [columnInfo safeObjectAtIndex:rowIndex];
        value = UN_NIL(string);
        // 修改过的内容
        NSDictionary *modifyData = [self.task objectForKey:@"modifyData"];
        if (modifyData && [modifyData isKindOfClass:[NSDictionary class]]) {
            NSDictionary *temp = [modifyData objectForKey:elementId];
            if ([temp count] > 0 && [temp.allKeys containsObject:StringFromInt(rowIndex)]) {
                NSString *changeValue = [temp objectForKey:StringFromInt(rowIndex)];
                value = changeValue;
            }
        }
    }
    return value;
}
- (void)taskChangeFor:(NSString *)elementId index:(NSInteger)pageIndex withContent:(NSString *)content {
    NSMutableDictionary *modifyData = [NSMutableDictionary dictionary];
    id orignalModifyData = self.task[@"modifyData"];
    if (orignalModifyData && [orignalModifyData isKindOfClass:[NSDictionary class]]) {
        [modifyData addEntriesFromDictionary:orignalModifyData];
    }
    // set content
    NSMutableDictionary *modifyElementData = [NSMutableDictionary dictionary];
    NSDictionary *currentIdData = modifyData[elementId];
    if (currentIdData && [currentIdData isKindOfClass:[NSDictionary class]] && [currentIdData count] > 0) {
        [modifyElementData addEntriesFromDictionary:currentIdData];
    }
    [modifyElementData setObject:UN_NIL(content) forKey:StringFromInt(pageIndex)];
    [modifyData setObject:modifyElementData forKey:elementId];
    //
    NSMutableDictionary *__task = [NSMutableDictionary dictionaryWithCapacity:2];
    [__task setObject:@"1" forKey:@"externalDataID"];
    [__task setObject:modifyData forKey:@"modifyData"];
    self.task = __task;
}

- (void)copyTaskInfoFrom:(NSString *)oldElementId to:(NSString *)newElementId {
    NSMutableDictionary *modifyData = [NSMutableDictionary dictionary];
    id orignalModifyData = self.task[@"modifyData"];
    if (orignalModifyData && [orignalModifyData isKindOfClass:[NSDictionary class]]) {
        [modifyData addEntriesFromDictionary:orignalModifyData];
    }
    // copy
    id curObject = modifyData[oldElementId];
    if (curObject && [curObject count] > 0) {
        [modifyData setObject:curObject forKey:newElementId];
    }
    NSMutableDictionary *__task = [NSMutableDictionary dictionaryWithCapacity:2];
    [__task setObject:@"1" forKey:@"externalDataID"];
    [__task setObject:modifyData forKey:@"modifyData"];
    self.task = __task;
}

- (void)deleteTaskForElementId:(NSString *)elementId {
    NSMutableDictionary *modifyData = [NSMutableDictionary dictionary];
    id orignalModifyData = self.task[@"modifyData"];
    if (orignalModifyData && [orignalModifyData isKindOfClass:[NSDictionary class]]) {
        [modifyData addEntriesFromDictionary:orignalModifyData];
    }
    [modifyData  removeObjectForKey:elementId];
    NSMutableDictionary *__task = [NSMutableDictionary dictionaryWithCapacity:2];
    [__task setObject:@"1" forKey:@"externalDataID"];
    [__task setObject:modifyData forKey:@"modifyData"];
    self.task = __task;
}

- (void)setTask:(NSDictionary *)task {
    _task = task;
}

- (void)setExternalData:(NSDictionary *)externalData {
    _externalData = externalData;
    if (!externalData) {
        NSLog(@"nil ");
    }
}

- (NSString *)deviceId {
    return kJCPrinter_Id;
}

- (void)clear {
    _info = nil;
//    self.externalData = nil;
//    self.task = nil;
    self.addElementIndex = 0;
}

- (NSString *)sizeId {
    if ([_sizeId isEqualToString:@"0"]) {
        _sizeId = [kJCPrinter_Type hasPrefix:@"B21"]?@"1":@"2";
    }
    return _sizeId;
}

- (void)setLabelId:(NSString *)labelId {
    _labelId = labelId;
}

#pragma mark - get
-  (JCExcelForElement *)excelInfo {
    dispatch_semaphore_wait(_semaphore, DISPATCH_TIME_FOREVER);
    JCExcelForElement *ex = _info;
    dispatch_semaphore_signal(_semaphore);
    return ex;
}

#pragma - mark - lazy
- (CGFloat)mm2pxScale {
    if (self.template_width_mm > 0 && self.boardWidth > 0) {
        _mm2pxScale = 6.5;
//        _mm2pxScale = [JCAPI getDisplayMultiple:self.template_width_mm templateDisplayWidth:self.boardWidth];
    } else {
        _mm2pxScale = 0;
    }
    return _mm2pxScale;
}

- (CGFloat)dpiScale {
    if (_dpiScale == 0) {
        _dpiScale = 8;
    }
    return _dpiScale;
}

- (NSInteger)defaultConcentrate {
    if (_defaultConcentrate == 0) {
        _defaultConcentrate = 3;
    }
    return _defaultConcentrate;;
}

- (NSInteger)minConcentrate {
    if (_minConcentrate == 0) {
        _minConcentrate = 1;
    }
    return _minConcentrate;
}

- (NSInteger)maxConcentrate {
    if (_maxConcentrate == 0) {
        _maxConcentrate = 6;
    }
    return _maxConcentrate;
}

@end
