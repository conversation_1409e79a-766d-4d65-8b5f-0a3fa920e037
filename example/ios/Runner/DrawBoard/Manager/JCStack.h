//
//  JCStack.h
//  XYFrameWork
//
//  Created by xingling xu on 2019/12/30.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface JCStack : NSObject
@property (nonatomic, assign) NSInteger stackLength;
@property (nonatomic, assign) BOOL empty;

- (instancetype)init;

- (instancetype)initWithCapacity:(NSInteger)capacity;
/** 压栈 */
- (void)push:(id)object;
/** 栈顶元素出栈并返回 */
- (id)pop;
/** 返回栈顶元素 */
- (id)topObject;
/** 清空栈 */
- (void)clear;
/** 设置栈空间最大长度 */
- (JCStack *(^)(NSInteger value))maxLength;
@end

NS_ASSUME_NONNULL_END
