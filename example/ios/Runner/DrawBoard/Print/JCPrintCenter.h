//
//  JCPrintCenter.h
//  Runner
//
//  Created by xingling xu on 2020/12/24.
//

#import <Foundation/Foundation.h>
#import "JCUserModel.h"
#import "JCStatisticsModel.h"
#import "JCRecordModel.h"
#import "TMCache.h"

#define m_userModel             [JCPrintCenter sharedInstance].userModel

@interface JCPrintCenter : NSObject
{
    JCStatisticsModel *_statisticsModel;
}
@property (nonatomic, assign) BOOL isWifi;
@property (nonatomic, assign) BOOL isOnline;
@property (nonatomic, strong) JCUserModel *userModel;
@property (nonatomic, strong) JCStatisticsModel *statisticsModel; //打印统计数据
@property (nonatomic,strong) JCRecordsModel *recordsModel;  //埋点统计数据
@property (nonatomic, assign) BOOL isSendingPrintData;
@property (nonatomic, strong) NSArray<Optional> *printerNameArr;
@property (nonatomic, strong) NSArray<Optional> *printerArr;
//缓存
@property (nonatomic, strong) TMCache *cache_user;
@property (nonatomic, strong) TMCache *cache_statistics;
@property (nonatomic, strong) TMCache *cache_records;
@property (nonatomic, strong) TMCache *tabbar_info;
+ (instancetype)sharedInstance;
- (void)getPrinterList;

- (void)sendStatistics;
@end

