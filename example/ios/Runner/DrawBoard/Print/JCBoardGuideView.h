//
//  JCBoardGuideView.h
//  Runner
//
//  Created by xingling xu on 2021/3/1.
//

#import <UIKit/UIKit.h>

typedef NS_ENUM(NSUInteger, JCGuideViewType) {
    JCGuideViewTypeBoardFirstShow,//首次进入画板
    JCGuideViewTypeBoardPrintSameFirstShow,//首次从印同款进入画板
};
NS_ASSUME_NONNULL_BEGIN

#define kNotificationShowConnectDevice  @"kNotificationShowConnectDevice"

@interface JCBoardGuideView : UIView
@property(nonatomic,assign)bool isShow;
@property(nonatomic,copy)void(^dismissCallBlack)(void);
- (JCBoardGuideView *)initWithType:(JCGuideViewType)type;

- (void)show;

- (void)dismiss;
@end

NS_ASSUME_NONNULL_END
