//
//  JCDeviceFirmwareRemoteModel.m
//  XYFrameWork
//
//  Created by EG on 2018/10/25.
//  Copyright © 2018年 JingchenSoft. All rights reserved.
//

#import "JCDeviceFirmwareRemoteModel.h"

@implementation JCShutdownModel
+(JSONKeyMapper*)keyMapper {
    return [[JSONKeyMapper alloc] initWithModelToJSONDictionary:@{@"minute":@"time"
                                                                  }];
}
@end

@implementation JCDeviceFirmwareRemoteModel

- (instancetype)init{
    self = [super init];
    if(self){
        self.url = @"";
        self.md5 = @"";
        self.rfidNotSupportVersions = @[];
        self.shutdownTimes = (NSArray<JCShutdownModel>*)@[];
    }
    return self;
}
+(JSO<PERSON><PERSON>eyMapper*)keyMapper {
    return [[JSONKeyMapper alloc] initWithModelToJSONDictionary:@{@"updateInfo":@"description",
                                                                 
                                                                  }];
}

- (BOOL)needUpgrade
{
    return !STR_IS_NIL(self.url) && !STR_IS_NIL(self.md5);
}

//+(JSONKeyMapper*)keyMapper {
//    return [[JSONKeyMapper alloc] initWithModelToJSONDictionary:@{@"updateInfo":@"description",
//                                                                  @"paper_type":@"paperType",
//                                                                  @"device_name":@"deviceName",
//                                                                  @"machine_name":@"machineName",
//                                                                  @"speed_min":@"speedMin",
//                                                                  @"speed_max":@"speedMax",
//                                                                  @"updateMsg":@"description",
//                                                                  @"is_calibration":@"isCalibration",
//                                                                  @"is_auto_shutdown":@"isAutoShutdown",
//                                                                  @"shutdown_times":@"shutdownTimes",
//                                                                  @"is_calibration":@"isSupportCalibration",
//                                                                  }];
//}
@end
