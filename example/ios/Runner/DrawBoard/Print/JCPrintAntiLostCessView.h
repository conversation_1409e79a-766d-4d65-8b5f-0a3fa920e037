//
//  JCPrintAntiLostCessView.h
//  Runner
//
//  Created by xingling xu on 2021/1/5.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@class JCPrintAntiLostCessView;
@protocol JCPrintAntiLostCessViewDelegate <NSObject>
- (void)processViewCancel:(JCPrintAntiLostCessView *)processView;
@end

@interface JCPrintAntiLostCessView : UIView
@property (nonatomic, weak) id  delegate;
- (instancetype)init;

- (void)show;

- (void)dismiss;

- (void)refreshProgressWith:(NSInteger)currentNumber total:(NSInteger)totalNumber;

- (void)showProcessContent:(NSString *)text;

- (void)showOK;

- (void)showError:(NSString *)error;
@end

NS_ASSUME_NONNULL_END
