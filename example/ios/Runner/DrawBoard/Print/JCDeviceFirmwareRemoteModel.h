//
//  JCDeviceFirmwareRemoteModel.h
//  XYFrameWork
//
//  Created by EG on 2018/10/25.
//  Copyright © 2018年 JingchenSoft. All rights reserved.
//

#import "DCBaseModel.h"
NS_ASSUME_NONNULL_BEGIN



@interface JCShutdownModel : JSONModel

@property(nonatomic,strong) NSString <Optional>*gear;

@property(nonatomic,strong) NSString <Optional>*minute;
@end

@protocol JCShutdownModel  <NSObject>

@end

@interface JCDeviceFirmwareRemoteModel : DCBaseModel

@property(nonatomic,copy) NSString <Optional>*isUpdate;

@property(nonatomic,copy) NSString <Optional>*version;

@property(nonatomic,copy) NSString <Optional>*url;

@property(nonatomic,copy) NSArray <Optional> *paperType;

@property(nonatomic,copy) NSArray <Optional> *paperTypeNames;

@property(nonatomic,copy) NSString <Optional>*deviceName;

@property(nonatomic,copy) NSString <Optional>*machineName;

@property(nonatomic,copy) NSNumber <Optional>*speedMin;

@property(nonatomic,copy) NSNumber <Optional>*speedMax;

@property(nonatomic,copy) NSNumber <Optional>*widthSetStart;

@property(nonatomic,copy) NSNumber <Optional>*widthSetEnd;

@property(nonatomic,copy) NSString <Optional>*updateInfo;

@property(nonatomic,copy) NSString <Optional>*md5;

@property(nonatomic,copy) NSString <Optional>*hardwareFlags;

@property(nonatomic,copy) NSString <Optional>*isCalibration;
/**自动关机*/
@property(nonatomic,copy) NSString <Optional>*isAutoShutdown;

@property(nonatomic,copy) NSArray <Optional,JCShutdownModel> *shutdownTimes;


@property(nonatomic,copy) NSArray <Optional> *rfidNotSupportVersions;

- (BOOL)needUpgrade;

@end

NS_ASSUME_NONNULL_END
