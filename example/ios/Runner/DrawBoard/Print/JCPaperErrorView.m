//
//  JCPaperErrorView.m
//  Runner
//
//  Created by xingling xu on 2021/2/23.
//

#import "JCPaperErrorView.h"

@interface JCPaperErrorView ()
@property (nonatomic, strong) UIView *mainView;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *contentLabel;
@property (nonatomic, strong) UIButton *sureButton;
@property (nonatomic, strong) UIButton *cancelButton;
@property (nonatomic, copy) void(^printBlock)(void);
@end

@implementation JCPaperErrorView

- (instancetype)initWithContinuePrint:(void(^)(void))printBlock
{
    self = [super init];
    
    if ( self )
    {
        self.printBlock = printBlock;
        [self loadRootView];
    }
    
    return self;
}


- (void)loadRootView {
    self.type = MMPopupTypeCustom;

    [self mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(270, 282));
    }];
    
    [self addSubview:self.mainView];
    
    [self.mainView addSubview:self.titleLabel];
    [self.mainView addSubview:self.contentLabel];
    [self.mainView addSubview:self.sureButton];
    [self.mainView addSubview:self.cancelButton];

    self.titleLabel.top = 18;
    self.titleLabel.centerX = self.mainView.width/2;
    self.contentLabel.left = 20;
    
    UIView *line = [[UIView alloc] initWithFrame:(CGRect){0,182,270,1}];
    line.backgroundColor = XY_HEX_RGB(0xEBEBEB);
    [self.mainView addSubview:line];
    
    line = [[UIView alloc] initWithFrame:(CGRect){0,232,270,1}];
    line.backgroundColor = XY_HEX_RGB(0xEBEBEB);
    [self.mainView addSubview:line];
}

#pragma mark - selector
- (void)sure:(UIButton *)button {
    [self hide];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        if (self.printBlock) {
            self.printBlock();
        }
    });
    
}

- (void)cancel:(UIButton *)button {
    [self hide];
}

#pragma mark - lazy
- (UIView *)mainView {
    if (!_mainView) {
        _mainView = [[UIView alloc] initWithFrame:(CGRect){0,0,270,282}];
        _mainView.backgroundColor = [UIColor whiteColor];
        _mainView.layer.cornerRadius = 14;
        _mainView.clipsToBounds = YES;
    }
    return _mainView;
}
- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] initWithFrame:(CGRect){0,0,200,22}];
        _titleLabel.font = MY_FONT_Bold(16);
        _titleLabel.textColor = XY_HEX_RGB(0x262626);
        _titleLabel.textAlignment = NSTextAlignmentCenter;
        _titleLabel.text = @"标签纸异常，请尝试：";
    }
    return _titleLabel;
}

- (UILabel *)contentLabel {
    if (!_contentLabel) {
        _contentLabel = [[UILabel alloc] initWithFrame:(CGRect){20,59,230,101}];
        _contentLabel.font = MY_FONT_Regular(14);
        _contentLabel.textColor = XY_HEX_RGB(0x262626);
        _contentLabel.textAlignment = NSTextAlignmentLeft;
        _contentLabel.numberOfLines = 0;
        _contentLabel.text = @"1.请使用最新版精臣标签纸;\n\n2.您可按电源键，从新走纸校准; \n\n3.取出标签纸，卷紧后重新放入舱内.";
    }
    return _contentLabel;
}

- (UIButton *)sureButton {
    if (!_sureButton) {
        _sureButton = [UIButton buttonWithType:UIButtonTypeCustom];
        _sureButton.frame = (CGRect){0,182,270,50};
        [_sureButton setTitle:@"忽略异常，继续打印" forState:UIControlStateNormal];
        [_sureButton setTitleColor:XY_HEX_RGB(0x537FB7) forState:UIControlStateNormal];
        _sureButton.titleLabel.font = MY_FONT_Regular(17);
        [_sureButton addTarget:self action:@selector(sure:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _sureButton;
}

- (UIButton *)cancelButton {
    if (!_cancelButton) {
        _cancelButton = [UIButton buttonWithType:UIButtonTypeCustom];
        _cancelButton.frame = (CGRect){0,232,270,50};
        [_cancelButton setTitle:@"取消打印" forState:UIControlStateNormal];
        [_cancelButton setTitleColor:XY_HEX_RGB(0x595959) forState:UIControlStateNormal];
        _cancelButton.titleLabel.font = MY_FONT_Regular(17);
        [_cancelButton addTarget:self action:@selector(cancel:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _cancelButton;
}

@end
