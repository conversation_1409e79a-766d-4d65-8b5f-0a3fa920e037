//
//  JCMessageView.h
//  XYFrameWork
//
//  Created by xy on 2017/10/31.
//  Copyright © 2017年 xiaoyao. All rights reserved.
//

#import "XYView.h"

@interface JCMessageView : XYView


@property (weak, nonatomic) IBOutlet UIView *mainView;
@property (weak, nonatomic) IBOutlet UIButton *btn;
@property (copy, nonatomic) void (^connectPrinterBlock)(void);

+ (void)showWithView:(UIView*)mainView title:(NSString *)title message:(NSString *)message rightButtonName:(NSString *)button block:(XYBlock)block;

+ (void)showWithView:(UIView*)mainView title:(NSString *)title message:(NSString *)message leftButtonName:(NSString *)leftButton rightButtonName:(NSString *)rightButton is_NeedCenter:(BOOL)isNeed_Center block:(XYBlock)block;
+ (void)showViewCloseWith:(UIView*)mainView title:(NSString *)title message:(NSString *)message leftButtonName:(NSString *)leftButton rightButtonName:(NSString *)rightButton block:(XYBlock)block;

+ (void)showWithView:(UIView*)mainView title:(NSString *)title message:(NSString *)message confirmButtonName:(NSString *)button isTimeType:(BOOL)isTimeType block:(XYBlock)block;

+ (void)showWithView:(UIView*)mainView title:(NSString *)title message:(NSString *)message confirmButtonName:(NSString *)button block:(XYBlock)block;
@end



//=====

typedef enum : NSUInteger {
    JCAlertMode_Single_Choice = 1,
    JCAlertMode_Two_Choice,
    JCAlertMode_Three_Choice,
    JCAlertMode_InputField_Force,/**只有一个确认按钮，必须输入*/
    JCAlertMode_InputField,
    JCAlertMode_ImageHeader,/**顶部带有一个图片头部*/
    JCAlertMode_Progress,/**进度条*/
} JCAlertMode;

typedef BOOL(^fullfilConditionBlock)(NSString *inputString);

@interface JCAlertView : UIView

@property(nonatomic,strong) UIColor *defaultColor;

@property(nonatomic,strong) UIColor *leftItemColor;

@property(nonatomic,strong) UIColor *centerItemColor;

@property(nonatomic,strong) UIColor *rightItemColor;

@property(nonatomic,copy) fullfilConditionBlock judgement;

@property(nonatomic,strong) UILabel *progressLabel;

@property(nonatomic,strong) UILabel *dowmloadContentLabel;
@property(nonatomic,assign) CGFloat progress;
@property(nonatomic,assign) BOOL isSingleButton;
/**
 创建单选项Alert
 
 @param title 顶部标题
 @param message 顶部提示
 @param confirmTitle 按钮标题
 @param confirmClick 按钮回调
 @return 单选项Alert
 */
+ (JCAlertView *)alertWithTitle:(NSString *)title message:(NSString *)message confirmTitle:(NSString *)confirmTitle confirmClick:(void(^)(void)) confirmClick;


/**
 创建双选项Alert
 
 @param title 顶部标题
 @param message 顶部提示
 @param leftTitle 左边按钮标题
 @param rightTitle 右边按钮标题
 @param leftClick 按钮回调
 @param rightClick 按钮回调
 @return 双选项Alert
 */
+ (JCAlertView *)alertWithTitle:(NSString *)title message:(NSString *)message leftTitle:(NSString *)leftTitle rightTitle:(NSString *)rightTitle leftClick:(void(^)(void))leftClick rightClick:(void (^)(void))rightClick;


/**
 创建三选项Alert
 
 @param title 顶部标题
 @param message 顶部提示
 @param leftTitle 左边按钮标题
 @param centerTitle 中间按钮标题
 @param rightTitle 右边按钮标题
 @param leftClick 按钮回调
 @param centerClick 按钮回调
 @param rightClick 按钮回调
 @return 三选项Alert
 */
+ (JCAlertView *)alertWithTitle:(NSString *)title message:(NSString *)message leftTitle:(NSString *)leftTitle centerTitle:(NSString *)centerTitle rightTitle:(NSString *)rightTitle leftClick:(void(^)(void))leftClick centerClick:(void(^)(void))centerClick rightClick:(void(^)(void))rightClick;

/**
 创建单选项强制输入Alert
 
 @param title 顶部标题
 @param placeholder 键入框placeholder
 @param confirmTitle 按钮标题
 @param confirmClick 按钮回调，回调内容为输入框text
 
 @return 单选项Alert
 */
+ (JCAlertView *)inputAlertWithTitle:(NSString *)title placeholder:(NSString *)placeholder confirmTitle:(NSString *)confirmTitle confirmClick:(void(^)(NSString *input)) confirmClick;


/**
 创建双选项Alert

 @param title 顶部标题
 @param placeholder 键入框placeholder
 @param leftTitle 左边按钮标题
 @param rightTitle 右边按钮标题
 @param leftClick 按钮回调
 @param rightClick 按钮回调
 @return 双选项Alert
 */
+ (JCAlertView *)inputAlertWithTitle:(NSString *)title placeholder:(NSString *)placeholder leftTitle:(NSString *)leftTitle rightTitle:(NSString *)rightTitle leftClick:(void(^)(NSString *input))leftClick rightClick:(void(^)(NSString *input))rightClick;


/**
 创建顶部有图,双选项Alert
 
 @param headerImage 顶部图片
 @param title 顶部标题
 @param message 顶部提示
 @param leftTitle 左边按钮标题
 @param rightTitle 右边按钮标题
 @param leftClick 按钮回调
 @param rightClick 按钮回调
 @return 顶部有图,双选项Alert
 */
+  (JCAlertView *)alertWithHeaderImage:(NSString *)headerImage title:(NSString *)title message:(NSString *)message leftTitle:(NSString *)leftTitle rightTitle:(NSString *)rightTitle isSingleButton:(BOOL)isSingle leftClick:(void (^)(void))leftClick rightClick:(void (^)(void))rightClick;




/**
 创建顶部有图,双选项Alert
 
 @param headerImage 顶部图片
 @param title 顶部标题
 @param message 顶部提示
 @param warningInfo 警示语
 @param leftTitle 左边按钮标题
 @param rightTitle 右边按钮标题
 @param leftClick 按钮回调
 @param rightClick 按钮回调
 @return 顶部有图,双选项Alert
 */
+ (JCAlertView *)alertWithHeaderImage:(NSString *)headerImage title:(NSString *)title message:(NSString *)message warning:(NSString *)warningInfo  leftTitle:(NSString *)leftTitle rightTitle:(NSString *)rightTitle leftClick:(void(^)(void))leftClick rightClick:(void (^)(void))rightClick;


/**
 进度条Alert

 @param title 顶部标题
@param confirmTitle 确定按钮
 @param confirmClick 确认按钮回调
 @return 进度条Alert
 */
+ (JCAlertView *)progressAlertWithTitle:(NSString *)title  confirmTitle:(NSString *)confirmTitle confirmClick:(void(^)(void))confirmClick;

- (void)setTitleColor:(UIColor *)color;

/**
 设置标题提示背景色
 
 @param color 颜色
 */
- (void)setTitleBackgroundColor:(UIColor *)color;

/**
 设置提示背景色
 
 @param color 颜色
 */
- (void)setMessageBackgroundColor:(UIColor *)color;

/**
 设置底部选项颜色

 @param normalColor 正常状态颜色，取最前面的几项进行对应设置，未设置项取默认设置
 */
- (void)setItemsColor:(NSArray <UIColor *>*)normalColor;


/**
 设置蒙层背景色
 
 @param color 颜色
 */
- (void)setAlertBackgroundColor:(UIColor *)color;


/**
 设置alert背景色
 
 @param color 颜色
 */
- (void)setMaskBackgroundColor:(UIColor *)color;


- (void)show;

- (void)horizonSeperatorShow;

- (BOOL)isShowing;

+(void)hideAllAleart;


+ (void)hideAllAleartOnView:(UIView*)view;

@end
