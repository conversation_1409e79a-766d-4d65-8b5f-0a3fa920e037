//
//  JCMessageView.m
//  XYFrameWork
//
//  Created by xy on 2017/10/31.
//  Copyright © 2017年 xiaoyao. All rights reserved.
//

#import "JCMessageView.h"

@interface JCMessageView()
@property (weak, nonatomic) IBOutlet UIView *titleView;
@property (weak, nonatomic) IBOutlet UILabel *titleLabel;
@property (weak, nonatomic) IBOutlet UILabel *messageLabel;
@property (weak, nonatomic) IBOutlet UITextView *textView;
@property (weak, nonatomic) IBOutlet UIButton *rightButton;
@property (weak, nonatomic) IBOutlet UIButton *leftButton;
@property (weak, nonatomic) IBOutlet UIView *hLineVIew;
@property (weak, nonatomic) IBOutlet UIView *vLineView;
@property (weak, nonatomic) IBOutlet UIButton *closeButton;
@property (weak, nonatomic) IBOutlet UIButton *confirmButton;
@property (strong, nonatomic) NSString *confirmBtnString;
@property(nonatomic,strong) NSTimer *timer;
@property(nonatomic,assign) NSInteger currentSecound;
@property(nonatomic,assign) BOOL isTimeType;

@end

@implementation JCMessageView


static JCMessageView *view = nil;
static UIView *backView = nil;

- (void)awakeFromNib {
    [super awakeFromNib];
    [self.mainView xy_setCornerRadius:8];
}

+ (void)showWithView:(UIView*)mainView title:(NSString *)title message:(NSString *)message rightButtonName:(NSString *)button block:(XYBlock)block {
    //设置弹出视图
    if (view == nil) {
        view = [JCMessageView xy_xib];
        view.messageLabel.hidden = NO;
        view.textView.hidden = YES;
        view.titleLabel.text = title;
        view.messageLabel.text = message;
        view.confirmButton.hidden = YES;
        [view.rightButton setTitle:button forState:UIControlStateNormal];
        [view.leftButton setTitle:XY_LANGUAGE_TITLE_NAMED(@"app00030",@"取消") forState:UIControlStateNormal];
        [view setFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT)];
        view.clickViewBtnBlock = block;
    }
    if (backView == nil) {
        backView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT)];
        backView.backgroundColor = COLOR_BLACK;
        backView.alpha = 0.2;
    }
    //实现弹出方法
    backView.tag = 900001;
    view.tag = 900002;
    [mainView addSubview:backView];
    [mainView addSubview:view];
}

+ (void)showWithView:(UIView*)mainView title:(NSString *)title message:(NSString *)message confirmButtonName:(NSString *)button block:(XYBlock)block {
    //设置弹出视图
    if (view == nil) {
        view = [JCMessageView xy_xib];
        view.messageLabel.hidden = NO;
        view.textView.hidden = YES;
        view.titleLabel.text = title;
        view.messageLabel.text = message;
        [view.confirmButton setTitle:button forState:UIControlStateNormal];
        [view.confirmButton setTitleColor:XY_HEX_RGB(0xFFFFFF) forState:UIControlStateNormal];
        [view setFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT)];
        view.clickViewBtnBlock = block;
    }
    if (backView == nil) {
        backView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT)];
        backView.backgroundColor = COLOR_BLACK;
        backView.alpha = 0.2;
    }
    //实现弹出方法
    backView.tag = 900001;
    view.tag = 900002;
    [mainView addSubview:backView];
    [mainView addSubview:view];
}

+ (void)showWithView:(UIView*)mainView title:(NSString *)title message:(NSString *)message confirmButtonName:(NSString *)button isTimeType:(BOOL)isTimeType block:(XYBlock)block {
    //设置弹出视图
    if (view == nil) {
        view = [JCMessageView xy_xib];
        view.messageLabel.hidden = NO;
        view.textView.hidden = YES;
        view.titleLabel.text = title;
        view.messageLabel.text = message;
        view.confirmBtnString = button;
        view.leftButton.hidden = YES;
        view.closeButton.hidden = NO;
        view.rightButton.hidden = YES;
        [view.confirmButton setTitle:button forState:UIControlStateNormal];
        [view setFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT)];
        view.clickViewBtnBlock = block;
        view.isTimeType = YES;
        [view startSecondTimeFire];
    }
    if (backView == nil) {
        backView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT)];
        backView.backgroundColor = COLOR_BLACK;
        backView.alpha = 0.2;
    }
    //实现弹出方法
    backView.tag = 900001;
    view.tag = 900002;
    [mainView addSubview:backView];
    [mainView addSubview:view];
}

- (void)startSecondTimeFire{
    if(self.isTimeType){
        self.currentSecound = 10;
        self.confirmButton.layer.borderColor = XY_HEX_RGB(0x5C88C1).CGColor;
        self.confirmButton.layer.borderWidth = 2;
        self.confirmButton.layer.cornerRadius = 6;
        self.confirmButton.userInteractionEnabled = NO;
        self.confirmButton.titleLabel.adjustsFontSizeToFitWidth = YES;
        self.timer = [NSTimer scheduledTimerWithTimeInterval:1 target:self selector:@selector(refreshTimeView) userInfo:nil repeats:YES];
        [[NSRunLoop currentRunLoop] addTimer:self.timer forMode:NSDefaultRunLoopMode];
        [self.timer fire];
    }
}

+ (void)showWithView:(UIView*)mainView title:(NSString *)title message:(NSString *)message leftButtonName:(NSString *)leftButton rightButtonName:(NSString *)rightButton is_NeedCenter:(BOOL)isNeed_Center block:(XYBlock)block {
    //设置弹出视图
    if (view == nil) {
        view = [JCMessageView xy_xib];
    }
    view.titleLabel.text = title;
    view.titleLabel.textAlignment = isNeed_Center?NSTextAlignmentCenter:NSTextAlignmentLeft;
    view.messageLabel.hidden = isNeed_Center?NO:YES;
    view.textView.hidden = isNeed_Center?YES:NO;
    view.textView.text = message;
    view.messageLabel.text = message;
    view.confirmButton.hidden = YES;
    view.messageLabel.textAlignment = isNeed_Center?NSTextAlignmentCenter:NSTextAlignmentLeft;
    view.messageLabel.lineBreakMode = NSLineBreakByCharWrapping;
    [view.rightButton setTitle:rightButton forState:UIControlStateNormal];
    [view.leftButton setTitle:leftButton forState:UIControlStateNormal];
    [view.rightButton setBackgroundColor:XY_HEX_RGB(0xffffff)];
    [view.rightButton setTitleColor:XY_HEX_RGB(0x537FB7) forState:UIControlStateNormal];
    [view.leftButton setTitleColor:XY_HEX_RGB(0x333333) forState:UIControlStateNormal];
    view.vLineView.hidden = NO;
    view.vLineView.backgroundColor = XY_HEX_RGB(0xdddddd);
    view.hLineVIew.backgroundColor = XY_HEX_RGB(0xdddddd);
    [view setFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT)];
    view.clickViewBtnBlock = block;
    if (backView == nil) {
        backView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT)];
    }
    backView.backgroundColor = COLOR_BLACK;
    //实现弹出方法
    backView.tag = 900001;
    backView.alpha = 0.2;
    view.tag = 900002;
    [mainView addSubview:backView];
    [mainView addSubview:view];
}

+ (void)showViewCloseWith:(UIView*)mainView title:(NSString *)title message:(NSString *)message leftButtonName:(NSString *)leftButton rightButtonName:(NSString *)rightButton block:(XYBlock)block {
    //设置弹出视图
    if (view == nil) {
        view = [JCMessageView xy_xib];
    }
    view.titleLabel.text = title;
    view.titleLabel.textAlignment = NSTextAlignmentCenter;
    view.messageLabel.hidden = NO;
    view.textView.hidden = YES;
    view.textView.text = message;
    view.messageLabel.text = message;
    view.messageLabel.textAlignment = NSTextAlignmentCenter;
    view.messageLabel.lineBreakMode = NSLineBreakByCharWrapping;
    view.closeButton.hidden = NO;
    view.confirmButton.hidden = YES;
    [view.rightButton setTitle:rightButton forState:UIControlStateNormal];
    [view.leftButton setTitle:leftButton forState:UIControlStateNormal];
    [view.rightButton setBackgroundColor:XY_HEX_RGB(0xffffff)];
    [view.rightButton setTitleColor:XY_HEX_RGB(0x333333) forState:UIControlStateNormal];
    [view.leftButton setTitleColor:XY_HEX_RGB(0x333333) forState:UIControlStateNormal];
    view.vLineView.hidden = NO;
    view.vLineView.backgroundColor = XY_HEX_RGB(0xdddddd);
    view.hLineVIew.backgroundColor = XY_HEX_RGB(0xdddddd);
    [view setFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT)];
    view.clickViewBtnBlock = block;
    if (backView == nil) {
        backView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT)];
    }
    backView.backgroundColor = COLOR_BLACK;
    //实现弹出方法
    backView.tag = 900001;
    backView.alpha = 0.2;
    view.tag = 900002;
    [mainView addSubview:backView];
    [mainView addSubview:view];
}

- (void)refreshTimeView{
    [self.confirmButton setTitleColor:XY_HEX_RGB(0x999999) forState:UIControlStateNormal];
    [self.confirmButton setTitle:[NSString stringWithFormat:@"%@ %lds",self.confirmBtnString,self.currentSecound] forState:UIControlStateNormal];
    self.confirmButton.userInteractionEnabled = NO;
    if(self.currentSecound == 0){
        [self.timer invalidate];
        self.timer = nil;
        self.confirmButton.userInteractionEnabled = YES;
        [self.confirmButton setTitleColor:XY_HEX_RGB(0x5C88C1) forState:UIControlStateNormal];
        [self.confirmButton setTitle:[NSString stringWithFormat:@"%@",self.confirmBtnString] forState:UIControlStateNormal];
    }
    self.currentSecound--;
}


- (IBAction)clickBtn:(UIButton *)sender {
    [self.timer invalidate];
    if (sender.tag == 11) {
        if (self.clickViewBtnBlock) {
            self.clickViewBtnBlock(@"1");
        }
        [view removeFromSuperview];
        view = nil;
        [backView removeFromSuperview];
        backView = nil;
    } else if (sender.tag == 12) {
        if (self.clickViewBtnBlock) {
            self.clickViewBtnBlock(@"3");
        }
        [view removeFromSuperview];
        view = nil;
        [backView removeFromSuperview];
        backView = nil;
    } else{
        if (self.clickViewBtnBlock) {
            self.clickViewBtnBlock(@"2");
        }
        [view removeFromSuperview];
        view = nil;
        [backView removeFromSuperview];
        backView = nil;
    }
}

- (void)dealloc {
    view = nil;
    backView = nil;
    
}


@end

//========= 
#define DEFAULT_ITEM_COLOR [UIColor colorWithRed:51.0f/255.0f green:51.0f/255.0f blue:51.0f/255.0f alpha:1.0f]

typedef void(^alertUserClick)(void);
typedef void(^alertInputFieldClick)(NSString *);

@interface JCAlertView()<UITextFieldDelegate>

@property(nonatomic,assign) JCAlertMode mode;

@property(nonatomic,copy) NSString *title;

@property(nonatomic,copy) NSString *message;

@property(nonatomic,copy) NSString *placeholder;

@property(nonatomic,strong) UIView *backView;

@property(nonatomic,copy) NSString *leftTitle;

@property(nonatomic,copy) NSString *centerTitle;

@property(nonatomic,copy) NSString *rightTitle;

@property(nonatomic,copy) NSString *warningInfo;

//action
@property(nonatomic,copy) alertUserClick leftClick;

@property(nonatomic,copy) alertUserClick centerClick;

@property(nonatomic,copy) alertUserClick rightClick;

@property(nonatomic,copy) alertInputFieldClick fieldConfirmClick;

@property(nonatomic,copy) alertInputFieldClick anotherFieldConfirmClick;

//subviews
@property(nonatomic,strong) UIView *seperator;

@property(nonatomic,strong) UIView *seperator_vertical_left;
@property(nonatomic,strong) UIView *seperator_horizontal_top;
@property(nonatomic,strong) UIView *seperator_vertical_right;

@property(nonatomic,strong) UILabel *titleLabel;

@property(nonatomic,strong) UILabel *messageTextView;

@property (nonatomic,strong) UIButton *firstButton;

@property (nonatomic,strong) UIButton *secondButton;

@property (nonatomic,strong) UIButton *thirdButton;

@property (nonatomic,strong) UITextField *inputField;

@property (nonatomic,strong) UIImageView *headerImageView;

@property(nonatomic,strong) UIProgressView *progressView;

@property (nonatomic,strong) UIView *topLineView;
@property(nonatomic,strong) UIButton *warningShownButton;

@property(nonatomic,strong) NSTimer *timer;
@property(nonatomic,assign) NSInteger currentSecound;
@property(nonatomic,assign) BOOL isTimeType;
@end

@implementation JCAlertView

#pragma mark - initial

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:[UIScreen mainScreen].bounds]) {
        self.backgroundColor = [UIColor colorWithWhite:0.3 alpha:0.35];
        self.defaultColor = [UIColor jc_colorWithHexString:@"FFFFFF"];
        
        if (!self.leftItemColor) {
            self.leftItemColor = XY_HEX_RGB(0x757575);
        }
        
        if (!self.rightItemColor) {
            self.rightItemColor =XY_HEX_RGB(0x537FB7);
        }
        
        if (!self.centerItemColor) {
            self.centerItemColor = self.defaultColor;
        }
        self.isTimeType = NO;
    }
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
    switch (self.mode) {
        case JCAlertMode_Single_Choice:
        {
            [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerX.left.right.mas_equalTo(self.backView);
                make.top.mas_equalTo(self.backView.mas_top);
                make.height.mas_equalTo(40);
            }];
            
            [self.messageTextView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.left.mas_equalTo(self.backView).offset(24);
                make.right.mas_equalTo(self.backView).offset(-24);
                make.top.mas_equalTo(self.titleLabel.mas_bottom);
                make.bottom.mas_equalTo(self.backView.mas_bottom).offset(-50);
            }];
            
            [self.topLineView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.left.right.mas_equalTo(self.backView);
                make.bottom.mas_equalTo(self.backView).offset(-50);
                make.height.mas_equalTo(0.5);
            }];
            
            [self.firstButton mas_makeConstraints:^(MASConstraintMaker *make) {
                make.left.right.bottom.mas_equalTo(self.backView);
                make.height.mas_equalTo(50);
            }];
        }
            return;
        case JCAlertMode_Two_Choice:
        {
            XYWeakSelf;
            [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
                make.left.right.mas_equalTo(weakSelf.backView);
                make.top.mas_equalTo(weakSelf.backView.mas_top);
                make.height.mas_equalTo(40);
            }];
            
            [self.messageTextView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.left.mas_equalTo(weakSelf.backView).offset(24);
                make.right.mas_equalTo(weakSelf.backView).offset(-24);
                make.top.mas_equalTo(weakSelf.titleLabel.mas_bottom);
                make.bottom.mas_equalTo(weakSelf.backView.mas_bottom).offset(-50);
            }];
            
            [self.topLineView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.left.right.mas_equalTo(self.backView);
                make.bottom.mas_equalTo(weakSelf.backView.mas_bottom).offset(-50);
                make.height.mas_equalTo(0.5);
            }];
            
            [self.firstButton mas_makeConstraints:^(MASConstraintMaker *make) {
                make.left.bottom.mas_equalTo(self.backView);
                make.right.mas_equalTo(self.secondButton.mas_left);
                make.width.mas_equalTo(self.secondButton);
                make.height.mas_equalTo(50);
            }];
            
            [self.secondButton mas_makeConstraints:^(MASConstraintMaker *make) {
                make.left.mas_equalTo(self.firstButton.mas_right);
                make.right.bottom.mas_equalTo(self.backView);
                make.width.height.mas_equalTo(self.firstButton);
            }];
            
            [self.seperator_vertical_left mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerX.mas_equalTo(weakSelf.backView);
                make.top.bottom.mas_equalTo(weakSelf.secondButton);
                make.width.mas_equalTo(0.5);
            }];
        }
            return;
            
        case JCAlertMode_Three_Choice:
        {
            [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerX.left.right.mas_equalTo(self.backView);
                make.top.mas_equalTo(self.backView.mas_top);
                make.height.mas_equalTo(45);
            }];
            
            [self.messageTextView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.left.mas_equalTo(self.backView).offset(25);
                make.right.mas_equalTo(self.backView).offset(-25);
                make.top.mas_equalTo(self.titleLabel.mas_bottom);
                make.bottom.mas_equalTo(self.backView.mas_bottom).offset(-45);
            }];
            
            [self.firstButton mas_makeConstraints:^(MASConstraintMaker *make) {
                make.left.bottom.mas_equalTo(self.backView);
                make.right.mas_equalTo(self.secondButton.mas_left);
                make.width.mas_equalTo(self.secondButton);
                make.height.mas_equalTo(40);
            }];
            
            [self.secondButton mas_makeConstraints:^(MASConstraintMaker *make) {
                make.left.mas_equalTo(self.firstButton.mas_right);
                make.right.mas_equalTo(self.thirdButton.mas_left);
                make.bottom.mas_equalTo(self.backView);
                make.width.mas_equalTo(self.thirdButton);
                make.height.mas_equalTo(40);
            }];
            
            [self.thirdButton mas_makeConstraints:^(MASConstraintMaker *make) {
                make.left.mas_equalTo(self.secondButton.mas_right);
                make.right.bottom.mas_equalTo(self.backView);
                make.width.mas_equalTo(self.firstButton);
                make.height.mas_equalTo(40);
            }];
            
            [self.seperator_vertical_left mas_makeConstraints:^(MASConstraintMaker *make) {
                make.right.mas_equalTo(self.secondButton.mas_left);
                make.centerY.mas_equalTo(self.secondButton);
                make.size.mas_equalTo(CGSizeMake(0.5, 27));
            }];
            
            [self.seperator_vertical_right mas_makeConstraints:^(MASConstraintMaker *make) {
                make.right.mas_equalTo(self.thirdButton.mas_left);
                make.centerY.mas_equalTo(self.secondButton);
                make.size.mas_equalTo(CGSizeMake(0.5, 27));
            }];
        }
            return;
            
        case JCAlertMode_InputField_Force:
        {
            [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerX.left.right.mas_equalTo(self.backView);
                make.top.mas_equalTo(self.backView.mas_top);
                make.height.mas_equalTo(45);
            }];
            
            [self.inputField mas_makeConstraints:^(MASConstraintMaker *make) {
                make.left.mas_equalTo(self.backView).offset(25);
                make.right.mas_equalTo(self.backView).offset(-25);
                make.top.mas_equalTo(self.titleLabel.mas_bottom).offset(30);
                make.height.mas_equalTo(40);
            }];
            
            [self.firstButton mas_makeConstraints:^(MASConstraintMaker *make) {
                make.left.right.bottom.mas_equalTo(self.backView);
                make.height.mas_equalTo(40);
            }];
        }
            return;
            
        case JCAlertMode_InputField:
        {
            [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerX.left.right.mas_equalTo(self.backView);
                make.top.mas_equalTo(self.backView.mas_top);
                make.height.mas_equalTo(45);
            }];
            
            [self.inputField mas_makeConstraints:^(MASConstraintMaker *make) {
                make.left.mas_equalTo(self.backView).offset(25);
                make.right.mas_equalTo(self.backView).offset(-25);
                make.top.mas_equalTo(self.titleLabel.mas_bottom).offset(30);
                make.height.mas_equalTo(40);
            }];
            
            [self.firstButton mas_makeConstraints:^(MASConstraintMaker *make) {
                make.left.bottom.mas_equalTo(self.backView);
                make.right.mas_equalTo(self.secondButton.mas_left);
                make.width.mas_equalTo(self.secondButton);
                make.height.mas_equalTo(40);
            }];
            
            [self.secondButton mas_makeConstraints:^(MASConstraintMaker *make) {
                make.left.mas_equalTo(self.firstButton.mas_right);
                make.right.bottom.mas_equalTo(self.backView);
                make.width.mas_equalTo(self.firstButton);
                make.height.mas_equalTo(40);
            }];
            
            [self.seperator_vertical_left mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerX.mas_equalTo(self.backView);
                make.centerY.mas_equalTo(self.secondButton);
                make.size.mas_equalTo(CGSizeMake(0.5, 27));
            }];
        }
            return;
        case JCAlertMode_ImageHeader:
        {
            self.backView.size = CGSizeMake(250, 331.5);
            self.backView.center = self.center;
            self.titleLabel.numberOfLines = 0;
            [self.headerImageView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.top.mas_equalTo(self.backView);
                make.left.right.mas_equalTo(self.backView);
            }];
            
            [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
//                make.centerX.left.right.mas_equalTo(self.backView);
                make.left.equalTo(self.backView).offset(20);
                make.right.equalTo(self.backView).offset(-20);
                make.top.mas_equalTo(self.backView).offset(124.5);
                make.height.mas_equalTo(48);
            }];
            
            [self.messageTextView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.left.mas_equalTo(self.backView).offset(17.5);
                make.right.mas_equalTo(self.backView).offset(-17.5);
                make.top.mas_equalTo(self.titleLabel.mas_bottom);
                make.bottom.mas_equalTo(self.backView.mas_bottom).offset(-45);
            }];
        
            
            if(!self.isSingleButton){
                [self.firstButton mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.left.bottom.mas_equalTo(self.backView);
                    make.right.mas_equalTo(self.secondButton.mas_left);
                    make.width.mas_equalTo(self.secondButton);
                    make.height.mas_equalTo(44);
                }];
                
                [self.secondButton mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.left.mas_equalTo(self.firstButton.mas_right);
                    make.right.bottom.mas_equalTo(self.backView);
                    make.width.height.mas_equalTo(self.firstButton);
                }];
            }else{
                [self.secondButton mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.left.right.bottom.mas_equalTo(self.backView);
                    make.height.mas_equalTo(40);
                }];
                [self.seperator_horizontal_top mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.left.right.mas_equalTo(self.backView);
                    make.top.equalTo(self.secondButton.mas_top);
                    make.height.mas_equalTo(0.5);
                }];
            }
            
            
            [self.seperator_vertical_left mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerX.mas_equalTo(self.backView);
                make.centerY.mas_equalTo(self.secondButton);
                make.size.mas_equalTo(CGSizeMake(0.5, 44));
            }];
        }
            return;
        case JCAlertMode_Progress:
        {
            self.backView.size = CGSizeMake(300, 210);
            self.backView.center = self.center;
            [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerX.left.right.mas_equalTo(self.backView);
                make.top.mas_equalTo(self.backView);
                make.height.mas_equalTo(45);
            }];
            
            [self.topLineView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerX.left.right.mas_equalTo(self.backView);
                make.top.mas_equalTo(self.backView).offset(45);
                make.height.mas_equalTo(1);
            }];
            
            [self.progressView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.top.mas_equalTo(self.backView).offset(85);
                make.left.equalTo(self.backView).offset(30);
                make.right.equalTo(self.backView).offset(-30);
                make.height.mas_equalTo(9);
            }];
            
            [self.progressLabel mas_makeConstraints:^(MASConstraintMaker *make) {
                make.right.equalTo(self.backView).offset(-30);
                make.top.mas_equalTo(self.backView).offset(105);
                make.height.mas_equalTo(20);
            }];
            
            [self.dowmloadContentLabel mas_makeConstraints:^(MASConstraintMaker *make) {
                make.left.equalTo(self.backView).offset(30);
                make.top.mas_equalTo(self.backView).offset(105);
                make.width.mas_equalTo(220);
                make.height.mas_equalTo(20);
            }];
            
            self.firstButton.backgroundColor = COLOR_WHITE;
            [self.firstButton setTitleColor:XY_HEX_RGB(0x5C88C1) forState:UIControlStateNormal];
            self.firstButton.titleLabel.font = MY_FONT_Regular(15);
            self.firstButton.layer.borderColor = XY_HEX_RGB(0x5C88C1).CGColor;
            self.firstButton.layer.borderWidth = 2;
            self.firstButton.layer.cornerRadius = 6;
            
            [self.firstButton mas_makeConstraints:^(MASConstraintMaker *make) {
                make.left.equalTo(self.backView).offset(30);
                make.right.equalTo(self.backView).offset(-30);
                make.bottom.equalTo(self.backView).offset(-20);
                make.height.mas_equalTo(40);
            }];
        }
            return;
    }
}
#pragma mark - actions

- (void)show {
    [[UIApplication sharedApplication].keyWindow endEditing:true];
    [[UIApplication sharedApplication].keyWindow resignFirstResponder];
    [[UIApplication sharedApplication].keyWindow addSubview:self];
    if(self.isTimeType){
        self.secondButton.titleLabel.adjustsFontSizeToFitWidth = YES;
        self.secondButton.enabled = NO;
        self.currentSecound = 10;
        self.timer = [NSTimer scheduledTimerWithTimeInterval:1 target:self selector:@selector(refreshTimeView) userInfo:nil repeats:YES];
        [[NSRunLoop currentRunLoop] addTimer:self.timer forMode:NSDefaultRunLoopMode];
        [self.timer fire];
    }
}

- (BOOL)isShowing {
    NSArray *subviews = [UIApplication sharedApplication].keyWindow.subviews;
    if ([subviews containsObject:self]) {
        return YES;
    }
    return NO;
}

- (void)refreshTimeView{
    [self.secondButton setTitleColor:XY_HEX_RGB(0x999999) forState:UIControlStateNormal];
    [self.secondButton setTitle:[NSString stringWithFormat:@"%@ %lds",self.centerTitle,self.currentSecound] forState:UIControlStateNormal];
    if(self.currentSecound == 0){
        [self.timer invalidate];
        [self.secondButton setTitleColor:XY_HEX_RGB(0x5C88C1) forState:UIControlStateNormal];
        [self.secondButton setTitle:[NSString stringWithFormat:@"%@",self.centerTitle] forState:UIControlStateNormal];
    }
    self.currentSecound--;
}

- (void)alertUserClick:(UIButton *)sender {
    [self.timer invalidate];
    if ([sender isEqual:self.firstButton]) {
        if (self.leftClick){
            self.leftClick();
            [self removeFromSuperview];
            
        }else if (self.fieldConfirmClick) {
            [self endEditing:YES];
            
            if (!self.judgement) {
                self.fieldConfirmClick(self.inputField.text);
                [self removeFromSuperview];
                return;
            }
            
            BOOL examResult = self.judgement(self.inputField.text);
            if (examResult) {
                self.fieldConfirmClick(self.inputField.text);
                [self removeFromSuperview];
            }
            
        }else {
            [self removeFromSuperview];
        }
        
     }else if ([sender isEqual:self.secondButton]) {
         if (self.centerClick){
             self.centerClick();
             [self removeFromSuperview];
             
         }else if (self.anotherFieldConfirmClick){
             [self endEditing:YES];
             
             if (!self.judgement) {
                 self.anotherFieldConfirmClick(self.inputField.text);
                 [self removeFromSuperview];
                 return;
             }
             
             BOOL examResult = self.judgement(self.inputField.text);
             if (examResult) {
                 self.anotherFieldConfirmClick(self.inputField.text);
                 [self removeFromSuperview];
             }
             
         }else {
             [self removeFromSuperview];
         }
         
    }else {
        if (self.rightClick) self.rightClick();
        [self removeFromSuperview];
    }
}

- (void)horizonSeperatorShow {
    [self.backView addSubview:self.seperator];
    
    [self.seperator mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.mas_equalTo(self.backView);
        make.bottom.mas_equalTo(self.firstButton.mas_top);
        make.height.mas_equalTo(0.5);
    }];
}

- (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    [self endEditing:YES];
}

- (void)setTitleBackgroundColor:(UIColor *)color {
    if (color) {
        self.titleLabel.backgroundColor = color;
    }
}

- (void)setMessageBackgroundColor:(UIColor *)color {
    if (color) {
        self.messageTextView.backgroundColor = color;
    }
}

- (void)setAlertBackgroundColor:(UIColor *)color {
    if (color) {
        self.backView.backgroundColor = color;
    }
}

- (void)setMaskBackgroundColor:(UIColor *)color {
    if (color) {
        self.backgroundColor = color;
    }
}

- (void)setItemsColor:(NSArray *)normalColor {
    if (1 == normalColor.count) {
        [self.firstButton setTitleColor:normalColor.firstObject forState:UIControlStateNormal];
    }else if (2 == normalColor.count) {
        [self.firstButton setTitleColor:normalColor.firstObject forState:UIControlStateNormal];
        [self.secondButton setTitleColor:normalColor.lastObject forState:UIControlStateNormal];
    }else if (3 <=  normalColor.count) {
        [self.firstButton setTitleColor:normalColor.firstObject forState:UIControlStateNormal];
        [self.secondButton setTitleColor:normalColor[1] forState:UIControlStateNormal];
        [self.thirdButton setTitleColor:normalColor[2] forState:UIControlStateNormal];
    }
}

- (void)setTitleColor:(UIColor *)color {
    if (color) {
        self.titleLabel.textColor = color;
    }
}

#pragma mark - factory method

+ (JCAlertView *)alertWithTitle:(NSString *)title message:(NSString *)message confirmTitle:(NSString *)confirmTitle confirmClick:(void(^)(void))confirmClick {
    JCAlertView *alert = [JCAlertView new];
    alert.title = [title copy];
    alert.message = [message copy];
    alert.leftTitle = [confirmTitle copy];
    alert.mode = JCAlertMode_Single_Choice;
    alert.leftClick = confirmClick;
    return alert;
}

+ (JCAlertView *)alertWithTitle:(NSString *)title message:(NSString *)message leftTitle:(NSString *)leftTitle rightTitle:(NSString *)rightTitle leftClick:(void(^)(void))leftClick rightClick:(void(^)(void))rightClick {
    JCAlertView *alert = [JCAlertView new];
    alert.title = [title copy];
    alert.message = [message copy];
    alert.leftTitle = [leftTitle copy];
    alert.centerTitle = [rightTitle copy];
    alert.mode = JCAlertMode_Two_Choice;
    alert.leftClick = leftClick;
    alert.centerClick = rightClick;
    return alert;
}

+ (JCAlertView *)alertWithTitle:(NSString *)title message:(NSString *)message leftTitle:(NSString *)leftTitle centerTitle:(NSString *)centerTitle rightTitle:(NSString *)rightTitle leftClick:(void(^)(void))leftClick centerClick:(void(^)(void))centerClick rightClick:(void(^)(void))rightClick {
    
    JCAlertView *alert = [JCAlertView new];
    alert.title = [title copy];
    alert.message = [message copy];
    alert.leftTitle = [leftTitle copy];
    alert.centerTitle = [centerTitle copy];
    alert.rightTitle = [rightTitle copy];
    alert.mode = JCAlertMode_Three_Choice;
    alert.leftClick = leftClick;
    alert.centerClick = centerClick;
    alert.rightClick = rightClick;
    return alert;
}

+ (JCAlertView *)inputAlertWithTitle:(NSString *)title placeholder:(NSString *)placeholder  confirmTitle:(NSString *)confirmTitle confirmClick:(void (^)(NSString *))confirmClick {
    JCAlertView *alert = [JCAlertView new];
    alert.title = [title copy];
    alert.leftTitle = [confirmTitle copy];
    alert.placeholder = placeholder;
    alert.mode = JCAlertMode_InputField_Force;
    alert.fieldConfirmClick = confirmClick;
    return alert;
}

+ (JCAlertView *)inputAlertWithTitle:(NSString *)title placeholder:(NSString *)placeholder leftTitle:(NSString *)leftTitle rightTitle:(NSString *)rightTitle leftClick:(void (^)(NSString *))leftClick rightClick:(void (^)(NSString *))rightClick {
    JCAlertView *alert = [JCAlertView new];
    alert.title = [title copy];
    alert.leftTitle = [leftTitle copy];
    alert.centerTitle = [rightTitle copy];
    alert.placeholder = placeholder;
    alert.mode = JCAlertMode_InputField;
    alert.fieldConfirmClick = leftClick;
    alert.anotherFieldConfirmClick = rightClick;
    return alert;
}

+  (JCAlertView *)alertWithHeaderImage:(NSString *)headerImage title:(NSString *)title message:(NSString *)message leftTitle:(NSString *)leftTitle rightTitle:(NSString *)rightTitle isSingleButton:(BOOL)isSingle leftClick:(void (^)(void))leftClick rightClick:(void (^)(void))rightClick {
    if (!headerImage) {
        return [JCAlertView alertWithTitle:title message:message leftTitle:leftTitle rightTitle:rightTitle leftClick:leftClick rightClick:rightClick];
    }else {
        JCAlertView *alert = [JCAlertView new];
        alert.title = [title copy];
        alert.headerImageView.image = [UIImage imageNamed:headerImage];
        alert.message = [message copy];
        alert.leftTitle = [leftTitle copy];
        alert.centerTitle = [rightTitle copy];
        alert.isSingleButton = isSingle;
        alert.mode = JCAlertMode_ImageHeader;
        alert.leftClick = leftClick;
        alert.centerClick = rightClick;
//        alert.isTimeType = YES;
        return alert;
    }
}

+ (JCAlertView *)alertWithHeaderImage:(NSString *)headerImage title:(NSString *)title message:(NSString *)message warning:(NSString *)warningInfo leftTitle:(NSString *)leftTitle rightTitle:(NSString *)rightTitle leftClick:(void (^)(void))leftClick rightClick:(void (^)(void))rightClick {
    if (!headerImage) {
        return [JCAlertView alertWithTitle:title message:message leftTitle:leftTitle rightTitle:rightTitle leftClick:leftClick rightClick:rightClick];
    }else {
        JCAlertView *alert = [JCAlertView new];
        alert.title = [title copy];
        alert.headerImageView.image = [UIImage imageNamed:headerImage];
        alert.message = [message copy];
        alert.warningInfo = [warningInfo copy];
        alert.leftTitle = [leftTitle copy];
        alert.centerTitle = [rightTitle copy];
        alert.mode = JCAlertMode_ImageHeader;
        alert.leftClick = leftClick;
        alert.centerClick = rightClick;
        return alert;
    }
}

+ (JCAlertView *)progressAlertWithTitle:(NSString *)title confirmTitle:(NSString *)confirmTitle confirmClick:(void(^)(void))confirmClick {
    JCAlertView *alert = [JCAlertView new];
    alert.title = [title copy];
    alert.mode = JCAlertMode_Progress;
    alert.leftTitle = [confirmTitle copy];
    alert.leftClick = confirmClick;
    return alert;
}

#pragma mark - setter

- (void)setMode:(JCAlertMode)mode {
    _mode = mode;
    
    [self addSubview:self.backView];
    [self.backView addSubview:self.titleLabel];
    
    switch (mode) {
        case JCAlertMode_Single_Choice:
        {
            [self.backView addSubview:self.messageTextView];
            [self.backView addSubview:self.firstButton];
            [self.backView addSubview:self.topLineView];
            [self.firstButton setTitleColor:XY_HEX_RGB(0x262626) forState:UIControlStateNormal];
        }
            return;
            
        case JCAlertMode_Two_Choice:
        {
            [self.backView addSubview:self.topLineView];
            [self.backView addSubview:self.messageTextView];
            [self.backView addSubview:self.firstButton];
            [self.backView addSubview:self.secondButton];
            [self.backView addSubview:self.seperator_vertical_left];
        }
            return;
            
        case JCAlertMode_Three_Choice:
        {
            [self.backView addSubview:self.messageTextView];
            [self.backView addSubview:self.firstButton];
            [self.backView addSubview:self.secondButton];
            [self.backView addSubview:self.thirdButton];
            [self.backView addSubview:self.seperator_vertical_left];
            [self.backView addSubview:self.seperator_vertical_right];
        }
            return;
            
        case JCAlertMode_InputField_Force:
        {
            [self.backView addSubview:self.inputField];
            [self.backView addSubview:self.messageTextView];
            [self.backView addSubview:self.firstButton];
        }
            return;
            
        case JCAlertMode_InputField:
        {
            [self.backView addSubview:self.inputField];
            [self.backView addSubview:self.firstButton];
            [self.backView addSubview:self.secondButton];
            [self.backView addSubview:self.seperator_vertical_left];
        }
            return;
            
        case JCAlertMode_ImageHeader:
        {
            [self.backView insertSubview:self.headerImageView atIndex:0];
            [self.backView addSubview:self.messageTextView];
            [self.backView addSubview:self.secondButton];
            if(!self.isSingleButton){
                [self.backView addSubview:self.firstButton];
                [self.backView addSubview:self.seperator_vertical_left];
            }else{
                [self.backView addSubview:self.seperator_horizontal_top];
            }
            
            self.messageTextView.textAlignment = NSTextAlignmentLeft;
            self.messageTextView.textColor = [UIColor jc_colorWithHexString:@"#333333"];
            self.messageTextView.font = [UIFont systemFontOfSize:14];
        }
            return;
            
        case JCAlertMode_Progress:
        {
            [self.backView addSubview:self.topLineView];
            [self.backView addSubview:self.progressLabel];
            [self.backView addSubview:self.progressView];
            [self.backView addSubview:self.dowmloadContentLabel];
            [self.backView addSubview:self.firstButton];
        }
            return;
    }
    
    [self layoutIfNeeded];
}

- (void)setTitle:(NSString *)title {
    _title = title;
    self.titleLabel.text = title;
}

- (void)setMessage:(NSString *)message {
    _message = message;
    self.messageTextView.text = message;
}

- (void)setLeftTitle:(NSString *)leftTitle {
    _leftTitle = leftTitle;
    [self.firstButton setTitle:leftTitle forState:UIControlStateNormal];
}

- (void)setCenterTitle:(NSString *)centerTitle {
    _centerTitle = centerTitle;
    [self.secondButton setTitle:centerTitle forState:UIControlStateNormal];
}

- (void)setRightTitle:(NSString *)rightTitle {
    _rightTitle = rightTitle;
    [self.thirdButton setTitle:rightTitle forState:UIControlStateNormal];
}

- (void)setPlaceholder:(NSString *)placeholder {
    _placeholder = placeholder;
    self.inputField.placeholder = placeholder;
}

- (void)setProgress:(CGFloat)progress {
    _progress = progress;
    dispatch_async(dispatch_get_main_queue(), ^{
        if (JCAlertMode_Progress == self.mode) {
            self.progressView.progress = progress;
            if (progress == 1) {
                [self removeFromSuperview];
            }
        }        
    });
}

+(void)hideAllAleart
{
    [self hideAllAleartOnView:XY_KEYWindow];
    
}

+(void)hideAllAleartOnView:(UIView *)view
{
       NSArray *subviews = view.subviews;
       for (UIView *aView in subviews) {
           if ([aView isKindOfClass:self]) {
               [aView removeFromSuperview];
           }
       }
}

#pragma mark - lazy

- (UIView *)seperator {
    if (!_seperator ) {
        _seperator = [[UIView alloc]initWithFrame:CGRectZero];
        _seperator.backgroundColor = [UIColor colorWithWhite:0.7 alpha:1];
    }
    return _seperator;
}

- (UIView *)seperator_vertical_left {
    if (!_seperator_vertical_left ) {
        _seperator_vertical_left = [[UIView alloc]initWithFrame:CGRectZero];
        _seperator_vertical_left.backgroundColor = XY_HEX_RGB(0xD9D9D9);
    }
    return _seperator_vertical_left;
}

- (UIView *)seperator_horizontal_top {
    if (!_seperator_horizontal_top ) {
        _seperator_horizontal_top = [[UIView alloc]initWithFrame:CGRectZero];
        _seperator_horizontal_top.backgroundColor = XY_HEX_RGB(0xD9D9D9);
    }
    return _seperator_horizontal_top;
}

- (UIView *)seperator_vertical_right {
    if (!_seperator_vertical_right ) {
        _seperator_vertical_right = [[UIView alloc]initWithFrame:CGRectZero];
        _seperator_vertical_right.backgroundColor = XY_HEX_RGB(0xD9D9D9);
    }
    return _seperator_vertical_right;
    
}

- (UIView *)backView {
    if (!_backView) {
        _backView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, 270, 170)];
        _backView.center = self.center;
        _backView.backgroundColor = XY_HEX_RGB(0xFFFFFF);
        _backView.layer.cornerRadius = 12;
        _backView.layer.masksToBounds = YES;
    }
    return _backView;
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] initWithFrame:CGRectZero];
        _titleLabel.textAlignment = NSTextAlignmentCenter;
        _titleLabel.textColor = XY_HEX_RGB(0x262626);
        _titleLabel.font = [UIFont fontWithName:@"PingFang-SC-Medium" size:16];
        _titleLabel.backgroundColor = XY_HEX_RGB(0xFFFFFF);
    }
    return _titleLabel;
}

- (UIView *)topLineView {
    if (!_topLineView) {
        _topLineView = [[UIView alloc] initWithFrame:CGRectZero];
        _topLineView.backgroundColor = XY_HEX_RGB(0xD9D9D9);
    }
    return _topLineView;
}


- (UILabel *)messageTextView {
    if (!_messageTextView) {
        _messageTextView = [UILabel new];
        _messageTextView.numberOfLines = 0;
        _messageTextView.textAlignment = NSTextAlignmentCenter;
        _messageTextView.textColor = XY_HEX_RGB(0x262626);
        _messageTextView.font = [UIFont systemFontOfSize:14];
    }
    return _messageTextView;
}

- (UIButton *)firstButton {
    if (!_firstButton) {
        _firstButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_firstButton addTarget:self action:@selector(alertUserClick:) forControlEvents:UIControlEventTouchUpInside];
        [_firstButton setTitle:self.leftTitle forState:UIControlStateNormal];
        [_firstButton setTitleColor:XY_HEX_RGB(0x757575) forState:UIControlStateNormal];
        _firstButton.titleLabel.font = [UIFont systemFontOfSize:17];
        _firstButton.backgroundColor = [UIColor whiteColor];
    }
    return _firstButton;
}

- (UIButton *)secondButton {
    if (!_secondButton) {
        _secondButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_secondButton addTarget:self action:@selector(alertUserClick:) forControlEvents:UIControlEventTouchUpInside];
        [_secondButton setTitle:self.centerTitle forState:UIControlStateNormal];
        [_secondButton setTitleColor:XY_HEX_RGB(0x537FB7) forState:UIControlStateNormal];
        _secondButton.titleLabel.font = [UIFont systemFontOfSize:17];
        _secondButton.backgroundColor = [UIColor whiteColor];
    }
    return _secondButton;
}

- (UIButton *)thirdButton {
    if (!_thirdButton) {
        _thirdButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_thirdButton addTarget:self action:@selector(alertUserClick:) forControlEvents:UIControlEventTouchUpInside];
        [_thirdButton setTitle:self.rightTitle forState:UIControlStateNormal];
        [_thirdButton setTitleColor:DEFAULT_ITEM_COLOR forState:UIControlStateNormal];
        _thirdButton.titleLabel.font = [UIFont systemFontOfSize:16];
        _thirdButton.backgroundColor = [UIColor whiteColor];
    }
    return _thirdButton;
}

- (UITextField *)inputField {
    if (!_inputField) {
        _inputField = [[UITextField alloc]initWithFrame:CGRectZero];
        _inputField.backgroundColor = [UIColor whiteColor];
        _inputField.borderStyle = UITextBorderStyleRoundedRect;
        _inputField.placeholder = self.placeholder;
        _inputField.textColor = [UIColor jc_colorWithHexString:@"#666666"];
        _inputField.font = [UIFont systemFontOfSize:13];
        _inputField.delegate = self;
    }
    return _inputField;
}

- (UIImageView *)headerImageView {
    if (!_headerImageView) {
        _headerImageView = [[UIImageView alloc]initWithImage:[UIImage imageNamed:@"占位符"]];
    }
    return _headerImageView;
}

- (UIProgressView *)progressView {
    if (!_progressView) {
        _progressView = [[UIProgressView alloc]initWithFrame:CGRectZero];
        _progressView.progressTintColor = [UIColor jc_colorWithHexString:@"#537FB7"];
        _progressView.trackTintColor = [UIColor jc_colorWithHexString:@"#dddddd"];
    }
    return _progressView;
}

- (UILabel *)progressLabel {
     if (!_progressLabel) {
        _progressLabel = [[UILabel alloc] initWithFrame:CGRectZero];
        _progressLabel.textAlignment = NSTextAlignmentRight;
        _progressLabel.font = [UIFont systemFontOfSize:14];
        _progressLabel.textColor = XY_HEX_RGB(0x666666);
        _progressLabel.text = @"0/1";
    }
    return _progressLabel;
}

- (UILabel *)dowmloadContentLabel {
     if (!_dowmloadContentLabel) {
         _dowmloadContentLabel = [[UILabel alloc] initWithFrame:CGRectZero];
         _dowmloadContentLabel.textAlignment = NSTextAlignmentLeft;
         _dowmloadContentLabel.font = [UIFont fontWithName:@"PingFang-SC-Medium" size:14];
         _dowmloadContentLabel.textColor = XY_HEX_RGB(0x666666);
         _dowmloadContentLabel.text = XY_LANGUAGE_TITLE_NAMED(@"", @"正在下载");
    }
    return _dowmloadContentLabel;
}

- (UIButton *)warningShownButton {
    if (!_warningShownButton) {
        _warningShownButton = [UIButton buttonWithType:UIButtonTypeCustom];
        _warningShownButton.userInteractionEnabled = NO;
    }
    return _warningShownButton;
}

#pragma mark - inputfied delegate
 
- (void)textFieldDidBeginEditing:(UITextField *)textField {
    [UIView animateWithDuration:0.5 animations:^{
        self.backView.centerY = self.centerY - 150;
    }];
}

- (void)textFieldDidEndEditing:(UITextField *)textField {
    [UIView animateWithDuration:0.35 animations:^{
        self.backView.centerY = self.centerY;
    }];
}


@end
