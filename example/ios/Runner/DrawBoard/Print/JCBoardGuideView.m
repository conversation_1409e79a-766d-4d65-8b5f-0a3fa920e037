//
//  JCBoardGuideView.m
//  Runner
//
//  Created by xingling xu on 2021/3/1.
//

#import "JCBoardGuideView.h"

#define kGuideShow  @"kGuideShow"
#define kTagGuideShow  @"kTagGuideShow"

@interface JCBoardGuideView ()
@property (nonatomic, strong) UIButton *connectButton;
@property (nonatomic, strong) UIView *connectView;
@property (nonatomic, strong) UIView *tagView;
@property (nonatomic, strong) UIButton *sureButton;
@property(nonatomic,strong)UIView *componentsView;

@property(nonatomic,assign)JCGuideViewType type;

@end

@implementation JCBoardGuideView

- (JCBoardGuideView *)initWithType:(JCGuideViewType)type {
    JCBoardGuideView *guideView = [[JCBoardGuideView alloc] init];
    guideView.type = type;
    return guideView;
}

- (instancetype)init {
    self = [super initWithFrame:(CGRect){0,0,kSCREEN_WIDTH,kSCREEN_HEIGHT}];
    if (self) {
        self.backgroundColor = XY_HEX_RGBA(0x000000, 0.55);
    }
    return self;
}

- (void)show {
    if (self.type == JCGuideViewTypeBoardFirstShow) {
        self.isShow = true;
        [self loadRootView];
        NSString *value = [[NSUserDefaults standardUserDefaults] objectForKey:kGuideShow];
        if ([value isEqualToString:@"1"]) return;
        [XY_KEYWindow addSubview:self];
        [[NSUserDefaults standardUserDefaults] setObject:@"1" forKey:kGuideShow];
    }else{
        self.isShow = true;
        [self loadPrintSameGuideView];
        NSString *value = [[NSUserDefaults standardUserDefaults] objectForKey:kTagGuideShow];
        if ([value isEqualToString:@"1"]) return;
        [XY_KEYWindow addSubview:self];
        [[NSUserDefaults standardUserDefaults] setObject:@"1" forKey:kTagGuideShow];
    }
    
}

- (void)dismiss {
    [self removeFromSuperview];
    if (self.dismissCallBlack) {
        self.dismissCallBlack();
    }
}

- (void)loadPrintSameGuideView {
    UIImageView *tagGuideView = [[UIImageView alloc] initWithFrame:(CGRect){22,64,230,203}];
    tagGuideView.center = self.center;
    tagGuideView.image = [UIImage imageNamed:@"board_tag_guide"];
    tagGuideView.userInteractionEnabled = true;
    
    UIButton *btn = [[UIButton alloc] initWithFrame:(CGRect){0,203-44,230,44}];
    [tagGuideView addSubview:btn];
    
    [btn addTarget:self action:@selector(dismiss) forControlEvents:UIControlEventTouchUpInside];
//    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(dismiss)];
//    [tagGuideView addGestureRecognizer:tap];
    [self addSubview:tagGuideView];
}

- (void)loadRootView {
    UIImageView *leftArrow = [[UIImageView alloc] initWithFrame:(CGRect){22,64,64,88}];
    leftArrow.image = [UIImage imageNamed:@"left_guide_arrow"];
    [self addSubview:leftArrow];
    
    UIImageView *rightArrow = [[UIImageView alloc] initWithFrame:(CGRect){22,55,32,30}];
    rightArrow.image = [UIImage imageNamed:@"right_guide_arrow"];
    [self addSubview:rightArrow];
    rightArrow.left = kSCREEN_WIDTH-105;
    
    UIImageView *downArrow = [[UIImageView alloc] initWithFrame:(CGRect){22,55,32,30}];
    downArrow.image = [UIImage imageNamed:@"down_guide_arror"];
    [self addSubview:downArrow];
    downArrow.centerX = self.width/2;
    downArrow.bottom = kSCREEN_HEIGHT - 140;
    
    [self addSubview:self.connectView];
    [self addSubview:self.tagView];
    [self addSubview:self.sureButton];
    [self addSubview:self.componentsView];
    
    
    // layout
    self.sureButton.centerX = self.width/2;
    self.sureButton.centerY = self.height/2+30;
    
    self.connectView.top = leftArrow.bottom + 10;
    self.connectView.left = 64;
    
    self.tagView.right = kSCREEN_WIDTH - 50;
    self.tagView.top = rightArrow.bottom + 10;
    
    self.componentsView.centerX = self.width/2;
    self.componentsView.bottom = downArrow.top - 10;
}

- (void)connect:(UIButton *)button {
    [[NSNotificationCenter defaultCenter] postNotificationName:kNotificationShowConnectDevice object:nil];
    [self dismiss];
}

- (void)sure:(UIButton *)btn {
    [self dismiss];
}

#pragma mark - lazy
- (UIButton *)connectButton {
    if (!_connectButton) {
        _connectButton = [UIButton buttonWithType:UIButtonTypeCustom];
        _connectButton.frame = (CGRect){0,0,60,26};
        _connectButton.backgroundColor = XY_HEX_RGB(0xFB4B42);
        _connectButton.layer.cornerRadius = 13;
        _connectButton.layer.masksToBounds = YES;
        _connectButton.titleLabel.font = MY_FONT_Medium(13);
        [_connectButton setTitle:@"打印" forState:UIControlStateNormal];
        [_connectButton addTarget:self action:@selector(connect:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _connectButton;
}

- (UIView *)connectView {
    if (!_connectView) {
        _connectView = [[UIView alloc] initWithFrame:(CGRect){0,0,225,46}];
        _connectView.backgroundColor = [UIColor whiteColor];
        _connectView.layer.cornerRadius = 8;
        
        UILabel *label = [[UILabel alloc] initWithFrame:(CGRect){20,13,110,20}];
        label.font = MY_FONT_Medium(14);
        label.textColor = XY_HEX_RGB(0x595959);
        label.textAlignment = NSTextAlignmentLeft;
        label.text = @"1.请先连接打印机";
        [_connectView addSubview:label];
        
        [_connectView addSubview:self.connectButton];
        self.connectButton.centerY = 23;
        self.connectButton.right = 200;
    }
    return _connectView;
}

- (UIView *)tagView {
    if (!_tagView) {
        _tagView = [[UIView alloc] initWithFrame:(CGRect){0,0,164,40}];
        _tagView.backgroundColor = [UIColor whiteColor];
        _tagView.layer.cornerRadius = 8;
        
        UILabel *label = [[UILabel alloc] initWithFrame:(CGRect){20,10,130,20}];
        label.font = MY_FONT_Medium(14);
        label.textColor = XY_HEX_RGB(0x595959);
        label.textAlignment = NSTextAlignmentLeft;
        label.text = @"2.识别或选择标签纸";
        [_tagView addSubview:label];
    }
    return _tagView;
}

- (UIButton *)sureButton {
    if (!_sureButton) {
        _sureButton = [UIButton buttonWithType:UIButtonTypeCustom];
        _sureButton.frame = (CGRect){0,0,133,42};
        [_sureButton setImage:[UIImage imageNamed:@"guide_sure_button"] forState:UIControlStateNormal];
        [_sureButton addTarget:self action:@selector(sure:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _sureButton;
}

- (UIView *)componentsView {
    if (!_componentsView) {
        _componentsView = [[UIView alloc] initWithFrame:(CGRect){0,0,164,46}];
        _componentsView.backgroundColor = [UIColor whiteColor];
        _componentsView.layer.cornerRadius = 8;
        
        UILabel *label = [[UILabel alloc] initWithFrame:(CGRect){20,10,130,20}];
        label.font = MY_FONT_Medium(14);
        label.textColor = XY_HEX_RGB(0x595959);
        label.textAlignment = NSTextAlignmentLeft;
        label.text = @"3.组件区可以滑动哦";
        [_componentsView addSubview:label];
    }
    return  _componentsView;
}
- (void)drawRect:(CGRect)rect {
    if (self.type != JCGuideViewTypeBoardFirstShow) {
        return;
    }
    CGFloat dashArray[] = {
        4,1
    };
    CGFloat spacing = 5;
    CGContextRef context = UIGraphicsGetCurrentContext();
    CGRect connectRect = self.connectView.frame;
    CGRect tagRect = self.tagView.frame;
    CGRect componentRect = self.componentsView.frame;
    CGRect connectDashFrame = CGRectMake(connectRect.origin.x - spacing, connectRect.origin.y - spacing, connectRect.size.width + 2 * spacing, connectRect.size.height + 2 * spacing);
    CGRect tagDashFrame = CGRectMake(tagRect.origin.x - spacing, tagRect.origin.y - spacing, tagRect.size.width + 2 * spacing, tagRect.size.height + 2 * spacing);
    CGRect componentDashFrame = CGRectMake(componentRect.origin.x - spacing, componentRect.origin.y - spacing, componentRect.size.width + 2 * spacing, componentRect.size.height + 2 * spacing);
    
    CGPathRef rectPath = CGPathCreateWithRoundedRect(connectDashFrame, 8, 8, NULL);
    CGPathRef tagPath = CGPathCreateWithRoundedRect(tagDashFrame, 8, 8, NULL);
    CGPathRef componentPath = CGPathCreateWithRoundedRect(componentDashFrame, 8, 8, NULL);
    CGContextAddPath(context, rectPath);
    CGContextAddPath(context, tagPath);
    CGContextAddPath(context, componentPath);
    CGContextSetLineWidth(context, 1);
    CGContextSetStrokeColorWithColor(context, [UIColor whiteColor].CGColor);
    CGContextSetLineDash(context, 1, dashArray, 1);
    
    CGContextStrokePath(context);
    
    CGPathRelease(rectPath);
    CGPathRelease(tagPath);
    CGPathRelease(componentPath);
}


@end
