<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="17156" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="17125"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="iN0-l3-epB" customClass="JCMessageView">
            <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="8ur-cJ-4Oz">
                    <rect key="frame" x="47.5" y="233.5" width="280" height="200"/>
                    <subviews>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="T3f-PF-A4o">
                            <rect key="frame" x="0.0" y="0.0" width="280" height="45"/>
                            <subviews>
                                <button hidden="YES" opaque="NO" tag="12" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="jUf-Mr-LUf">
                                    <rect key="frame" x="220" y="0.0" width="60" height="45"/>
                                    <constraints>
                                        <constraint firstAttribute="width" constant="60" id="Iyp-mM-5vt"/>
                                    </constraints>
                                    <inset key="imageEdgeInsets" minX="22" minY="0.0" maxX="0.0" maxY="0.0"/>
                                    <state key="normal" image="关闭_Pop"/>
                                    <connections>
                                        <action selector="clickBtn:" destination="iN0-l3-epB" eventType="touchUpInside" id="Rr3-Ih-9cp"/>
                                    </connections>
                                </button>
                            </subviews>
                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstAttribute="trailing" secondItem="jUf-Mr-LUf" secondAttribute="trailing" id="C8n-12-faz"/>
                                <constraint firstItem="jUf-Mr-LUf" firstAttribute="top" secondItem="T3f-PF-A4o" secondAttribute="top" id="G6a-aM-pDD"/>
                                <constraint firstItem="jUf-Mr-LUf" firstAttribute="height" secondItem="T3f-PF-A4o" secondAttribute="height" id="fZj-C4-fI5"/>
                                <constraint firstAttribute="height" constant="45" id="hlr-uX-DCx"/>
                            </constraints>
                        </view>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="scZ-Nk-YEZ">
                            <rect key="frame" x="0.0" y="45" width="280" height="1"/>
                            <color key="backgroundColor" red="0.93333333333333335" green="0.93333333333333335" blue="0.93333333333333335" alpha="1" colorSpace="calibratedRGB"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="1" id="94Y-b1-aed"/>
                            </constraints>
                        </view>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="提示" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="3" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="cfH-xK-hKE">
                            <rect key="frame" x="15" y="0.0" width="250" height="45"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="45" id="e3b-Su-OUt"/>
                            </constraints>
                            <fontDescription key="fontDescription" name="PingFangSC-Medium" family="PingFang SC" pointSize="16"/>
                            <color key="textColor" red="0.20000000000000001" green="0.20000000000000001" blue="0.20000000000000001" alpha="1" colorSpace="calibratedRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="*打印时保持打印机与蓝牙均属于开启状态" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="y0q-Ma-7d2">
                            <rect key="frame" x="10" y="50" width="260" height="105"/>
                            <fontDescription key="fontDescription" type="system" pointSize="16"/>
                            <color key="textColor" red="0.20000000000000001" green="0.20000000000000001" blue="0.20000000000000001" alpha="1" colorSpace="calibratedRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <textView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" editable="NO" textAlignment="natural" translatesAutoresizingMaskIntoConstraints="NO" id="XcL-fV-SqK">
                            <rect key="frame" x="10" y="50" width="260" height="105"/>
                            <color key="textColor" red="0.40000000000000002" green="0.40000000000000002" blue="0.40000000000000002" alpha="1" colorSpace="calibratedRGB"/>
                            <fontDescription key="fontDescription" name="PingFangSC-Medium" family="PingFang SC" pointSize="15"/>
                            <textInputTraits key="textInputTraits" autocapitalizationType="sentences"/>
                        </textView>
                        <button opaque="NO" tag="11" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="F0z-2O-SLk">
                            <rect key="frame" x="0.0" y="160" width="140" height="40"/>
                            <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="calibratedRGB"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="40" id="mp3-Ax-cWo"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <state key="normal" title="取消">
                                <color key="titleColor" red="0.20000000000000001" green="0.20000000000000001" blue="0.20000000000000001" alpha="1" colorSpace="calibratedRGB"/>
                            </state>
                            <connections>
                                <action selector="clickBtn:" destination="iN0-l3-epB" eventType="touchUpInside" id="LgN-xg-jFp"/>
                            </connections>
                        </button>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="AQc-Q7-VPI">
                            <rect key="frame" x="140" y="160" width="140" height="40"/>
                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <state key="normal" title="去连接">
                                <color key="titleColor" red="0.20000000000000001" green="0.20000000000000001" blue="0.20000000000000001" alpha="1" colorSpace="calibratedRGB"/>
                            </state>
                            <connections>
                                <action selector="clickBtn:" destination="iN0-l3-epB" eventType="touchUpInside" id="JBI-6v-ThP"/>
                            </connections>
                        </button>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ZuV-Bm-mZI">
                            <rect key="frame" x="139.5" y="164" width="1" height="32"/>
                            <color key="backgroundColor" red="0.82352941176470584" green="0.82352941176470584" blue="0.82352941176470584" alpha="1" colorSpace="calibratedRGB"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="1" id="74F-YX-A9W"/>
                            </constraints>
                        </view>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="649-Mq-JG2">
                            <rect key="frame" x="30" y="145" width="220" height="40"/>
                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <fontDescription key="fontDescription" name="PingFangSC-Medium" family="PingFang SC" pointSize="14"/>
                            <state key="normal">
                                <color key="titleColor" red="0.36078431372549019" green="0.53333333333333333" blue="0.75686274509803919" alpha="1" colorSpace="calibratedRGB"/>
                            </state>
                            <connections>
                                <action selector="clickBtn:" destination="iN0-l3-epB" eventType="touchUpInside" id="6kY-fK-dhh"/>
                            </connections>
                        </button>
                    </subviews>
                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstItem="cfH-xK-hKE" firstAttribute="centerX" secondItem="8ur-cJ-4Oz" secondAttribute="centerX" id="04M-Ju-mZe"/>
                        <constraint firstItem="XcL-fV-SqK" firstAttribute="bottom" secondItem="y0q-Ma-7d2" secondAttribute="bottom" id="3Hb-Lf-HXp"/>
                        <constraint firstItem="cfH-xK-hKE" firstAttribute="top" secondItem="8ur-cJ-4Oz" secondAttribute="top" id="5MN-XV-ZnD"/>
                        <constraint firstItem="649-Mq-JG2" firstAttribute="height" secondItem="F0z-2O-SLk" secondAttribute="height" id="8FC-0p-E0R"/>
                        <constraint firstItem="T3f-PF-A4o" firstAttribute="top" secondItem="8ur-cJ-4Oz" secondAttribute="top" id="8LC-18-w0F"/>
                        <constraint firstItem="XcL-fV-SqK" firstAttribute="top" secondItem="y0q-Ma-7d2" secondAttribute="top" id="8qX-jk-zVq"/>
                        <constraint firstItem="F0z-2O-SLk" firstAttribute="top" secondItem="y0q-Ma-7d2" secondAttribute="bottom" constant="5" id="9P0-mv-WMU"/>
                        <constraint firstItem="y0q-Ma-7d2" firstAttribute="top" secondItem="cfH-xK-hKE" secondAttribute="bottom" constant="5" id="Btt-Hq-kib"/>
                        <constraint firstItem="AQc-Q7-VPI" firstAttribute="centerY" secondItem="F0z-2O-SLk" secondAttribute="centerY" id="E7p-es-7BT"/>
                        <constraint firstItem="ZuV-Bm-mZI" firstAttribute="top" secondItem="F0z-2O-SLk" secondAttribute="top" constant="4" id="HyL-2T-vPW"/>
                        <constraint firstItem="XcL-fV-SqK" firstAttribute="leading" secondItem="y0q-Ma-7d2" secondAttribute="leading" id="J3g-5S-MHA"/>
                        <constraint firstAttribute="trailing" secondItem="AQc-Q7-VPI" secondAttribute="trailing" id="L7J-k1-avx"/>
                        <constraint firstItem="AQc-Q7-VPI" firstAttribute="width" secondItem="F0z-2O-SLk" secondAttribute="width" id="LB4-h4-geO"/>
                        <constraint firstItem="scZ-Nk-YEZ" firstAttribute="top" secondItem="T3f-PF-A4o" secondAttribute="bottom" id="OQe-w2-SKP"/>
                        <constraint firstAttribute="trailing" secondItem="scZ-Nk-YEZ" secondAttribute="trailing" id="PNi-jo-oxX"/>
                        <constraint firstItem="AQc-Q7-VPI" firstAttribute="height" secondItem="F0z-2O-SLk" secondAttribute="height" id="Pen-S9-DPU"/>
                        <constraint firstAttribute="height" constant="200" id="Qdc-Os-Jbj"/>
                        <constraint firstItem="ZuV-Bm-mZI" firstAttribute="centerX" secondItem="8ur-cJ-4Oz" secondAttribute="centerX" id="SSV-FI-S4q"/>
                        <constraint firstItem="cfH-xK-hKE" firstAttribute="leading" secondItem="8ur-cJ-4Oz" secondAttribute="leading" constant="15" id="TLP-PP-jVc"/>
                        <constraint firstAttribute="trailing" secondItem="649-Mq-JG2" secondAttribute="trailing" constant="30" id="UU0-un-yAj"/>
                        <constraint firstAttribute="bottom" secondItem="F0z-2O-SLk" secondAttribute="bottom" id="V23-ly-Gmf"/>
                        <constraint firstAttribute="bottom" secondItem="ZuV-Bm-mZI" secondAttribute="bottom" constant="4" id="Vau-8g-paB"/>
                        <constraint firstItem="XcL-fV-SqK" firstAttribute="trailing" secondItem="y0q-Ma-7d2" secondAttribute="trailing" id="Yyd-ga-hXt"/>
                        <constraint firstItem="F0z-2O-SLk" firstAttribute="width" secondItem="8ur-cJ-4Oz" secondAttribute="width" multiplier="0.5" id="aLB-5i-U1m"/>
                        <constraint firstItem="649-Mq-JG2" firstAttribute="leading" secondItem="8ur-cJ-4Oz" secondAttribute="leading" constant="30" id="cvM-H8-9iX"/>
                        <constraint firstItem="T3f-PF-A4o" firstAttribute="leading" secondItem="8ur-cJ-4Oz" secondAttribute="leading" id="hOL-vE-CVv"/>
                        <constraint firstItem="y0q-Ma-7d2" firstAttribute="leading" secondItem="8ur-cJ-4Oz" secondAttribute="leading" constant="10" id="hhd-V9-1jB"/>
                        <constraint firstItem="649-Mq-JG2" firstAttribute="top" secondItem="F0z-2O-SLk" secondAttribute="top" constant="-15" id="kTw-GA-3nV"/>
                        <constraint firstAttribute="width" constant="280" id="t7b-Y3-DXK"/>
                        <constraint firstAttribute="trailing" secondItem="y0q-Ma-7d2" secondAttribute="trailing" constant="10" id="uiG-cN-cHD"/>
                        <constraint firstItem="scZ-Nk-YEZ" firstAttribute="leading" secondItem="8ur-cJ-4Oz" secondAttribute="leading" id="umO-VQ-ZCr"/>
                        <constraint firstAttribute="trailing" secondItem="cfH-xK-hKE" secondAttribute="trailing" constant="15" id="vLw-W4-8Yy"/>
                        <constraint firstAttribute="trailing" secondItem="T3f-PF-A4o" secondAttribute="trailing" id="w6i-zu-jKJ"/>
                        <constraint firstItem="F0z-2O-SLk" firstAttribute="leading" secondItem="8ur-cJ-4Oz" secondAttribute="leading" id="zIl-mt-7Ma"/>
                    </constraints>
                </view>
            </subviews>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="calibratedWhite"/>
            <constraints>
                <constraint firstItem="8ur-cJ-4Oz" firstAttribute="centerX" secondItem="iN0-l3-epB" secondAttribute="centerX" id="r57-bu-LHt"/>
                <constraint firstItem="8ur-cJ-4Oz" firstAttribute="centerY" secondItem="iN0-l3-epB" secondAttribute="centerY" id="zeJ-PG-MHm"/>
            </constraints>
            <connections>
                <outlet property="btn" destination="F0z-2O-SLk" id="pCX-BB-M6n"/>
                <outlet property="closeButton" destination="jUf-Mr-LUf" id="slT-We-fRx"/>
                <outlet property="confirmButton" destination="649-Mq-JG2" id="6FM-6t-Zj3"/>
                <outlet property="leftButton" destination="F0z-2O-SLk" id="VnM-s4-LIL"/>
                <outlet property="mainView" destination="8ur-cJ-4Oz" id="0nt-oN-nz1"/>
                <outlet property="messageLabel" destination="y0q-Ma-7d2" id="gf3-E1-EtE"/>
                <outlet property="rightButton" destination="AQc-Q7-VPI" id="NXj-RT-lCo"/>
                <outlet property="textView" destination="XcL-fV-SqK" id="2kF-qt-JD3"/>
                <outlet property="titleLabel" destination="cfH-xK-hKE" id="E9W-7d-3AP"/>
                <outlet property="vLineView" destination="ZuV-Bm-mZI" id="1ma-wO-VEu"/>
            </connections>
            <point key="canvasLocation" x="25.5" y="51.5"/>
        </view>
    </objects>
    <resources>
        <image name="关闭_Pop" width="13.5" height="13.5"/>
    </resources>
</document>
