//
//  JCPrintCessView.h
//  Runner
//
//  Created by xingling xu on 2021/1/5.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@class JCPrintCessView;
@protocol JCPrintCessViewDelegate <NSObject>
- (void)processViewCancel:(JCPrintCessView *)processView;
@end

@interface JCPrintCessView : UIView
@property (nonatomic, weak) id  delegate;
- (instancetype)init;

- (void)show;

- (void)dismiss;

- (void)refreshProgressWith:(NSInteger)currentNumber total:(NSInteger)totalNumber;

- (void)showContent:(NSString *)content;

- (void)showProcessContent:(NSString *)text;

- (void)showOK;

- (void)showError:(NSString *)error;
@end

NS_ASSUME_NONNULL_END
