//
//  JCRecordTool.h
//  XYFrameWork
//
//  Created by j c on 2019/2/26.
//  Copyright © 2019年 Jingchen Technology Co.Ltd . All rights reserved.
//

#import <Foundation/Foundation.h>

typedef enum: NSInteger{
    AppStart, //启动APP
    AppExit, //退出APP
    ClickSkipSelectDevice, //引导页-第一步-点击跳过选择设备
    ClickSelectDevice, //引导页-第一步-点击设备型号
    ClickConnectDevice, //引导页-第二步-点击连接设备
    ClickCreateTemplate, //引导页-第二步-点击创建模板
    ClickSkipBootpage, //引导页-第二步-点击跳过
    ClickIndexScanMode, //首页-点击扫码取模
    ClickIndexScanPrint, //首页-点击扫码打印
    ClickIndexIndustryTemplate, //首页-点击行业模板
    ClickIndexCreateTemplate, //首页-点击新建模板
    ConnectDevice, //连接设备
    ClickSettingShop,
    ClickIndexShop,
    ClickIndexBannerShop,
    ClickUseTemplateCreat,
    ClickUseEmptyTemplateCreat,
    ClickTemplateDetailBuy,
    Click_ad_number,
    Click_ad_link_number,
    Click_ad_close_number,
    click_ad_shop,//点击底部商城
    click_index_tutorial,//首页-顶部教程
    click_my_tutorial, //点击我的顶部教程
    click_set_wifi,
    click_artboard_choose_template, //点击画板选择模版
    click_artboard_scan_template,//点击画板扫码取模
    click_artboard_blank_template, //点击画板新建空白
    click_goods_choose_template, //点击扫码选择模版
    click_goods_scan_template,//点击我的顶部教程
    click_scan_print_callBack_success,       //扫描打印获取数据成功时埋点
    click_scan_print_callBack_faild,             //扫描打印获取数据失败时埋点
    click_print_no_paper_show,                   //点击打印缺纸 展示弹窗时埋点
    click_print_paper_err_show,             //点击打印出纸异常 展示弹窗时埋点
    click_print_paper_to_shop,             //打印纸张异常（缺纸、出纸异常）点击跳转商城时埋点
    click_print_rfid_err_show,                  //点击打印RFID识别异常 展示弹窗时埋点
    click_print_rfid_err_to_shop,              //打印RFID识别异常 弹窗后点击跳转商城时埋点
    click_share_template,             //  点击分享模板
    click_my_orders,                  //  点击我的订单
    detail_buy_button_show,           //  详情页购买按钮展示
    click_rfid_history_user_templates,         //点击RFID用户历史标签
    click_rfid_create_template,                     //rfid识别点击创建标签
}JCRECORD_ACTION;

NS_ASSUME_NONNULL_BEGIN

@interface JCRecordTool : NSObject
+(void)recordWithAction:(JCRECORD_ACTION)action;

+(void)recordWithAction:(JCRECORD_ACTION)action withContent:(NSString *)recordContent isClickEvent:(BOOL)isClickEvent;
@end

NS_ASSUME_NONNULL_END
