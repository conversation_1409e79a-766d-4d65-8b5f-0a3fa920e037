//
//  JCRecordTool.m
//  XYFrameWork
//
//  Created by j c on 2019/2/26.
//  Copyright © 2019年 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCRecordTool.h"
#import "JCRecordModel.h"
#import "JCBluetoothManager.h"

@implementation JCRecordTool

+(void)recordWithAction:(JCRECORD_ACTION)action{
    JCRecordsModel *cache_m = [JCPrintCenter sharedInstance].recordsModel;
    NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
    [formatter setDateFormat:@"YYYY-MM-dd HH:mm:ss"];
    NSDate *datenow = [NSDate date];
    NSString *currentTimeString = [formatter stringFromDate:datenow];
    BOOL isRecorded = NO;
    if(action != ConnectDevice){
        if(cache_m.models.count > 0){
            for(JCRecordModel *m in cache_m.models){
                if(m.code != nil && [m.code isEqualToString:[self getRecordCodeFromAction:action]]){
                    if(m.click_number){
                        m.click_number = [NSString stringWithFormat:@"%d",[m.click_number intValue] + 1];
                        m.operate_time = currentTimeString;
                        isRecorded = YES;
                    }
                    break;
                }
            }
        }
    }
    if(!isRecorded){
        JCRecordModel *model = [[JCRecordModel alloc] init];
        model.user_id = m_userModel.userId == nil ? @"" : m_userModel.userId;
        model.code = [self getRecordCodeFromAction:action];
        model.operate_time = currentTimeString;
        model.system_version = [[UIDevice currentDevice] systemVersion];
        model.phone_model = @"iPhone";
        model.click_number = @"1";
        model.machine_code = action == ConnectDevice ? JC_CURRENT_CONNECTED_PRINTER : @"";
        [cache_m.models addObject:model];
    }
    [JCPrintCenter sharedInstance].recordsModel = cache_m;
    [JCPrintCenter sharedInstance].recordsModel = cache_m;
}

+(void)recordWithAction:(JCRECORD_ACTION)action withContent:(NSString *)recordContent isClickEvent:(BOOL)isClickEvent{
    JCRecordsModel *cache_m = [JCPrintCenter sharedInstance].recordsModel;
    NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
    [formatter setDateFormat:@"YYYY-MM-dd HH:mm:ss"];
    NSDate *datenow = [NSDate date];
    NSString *currentTimeString = [formatter stringFromDate:datenow];
    BOOL isRecorded = NO;
    if(action != ConnectDevice){
        if(cache_m.models.count > 0){
            for(JCRecordModel *m in cache_m.models){
                if(m.code != nil && [m.code isEqualToString:[self getRecordCodeFromAction:action]]){
                    if(m.click_number){
                        m.click_number = isClickEvent?[NSString stringWithFormat:@"%d",[m.click_number intValue] + 1]:@"0";
                        m.operate_time = currentTimeString;
                        isRecorded = YES;
                    }
                    break;
                }
            }
        }
    }
    if(!isRecorded){
        JCRecordModel *model = [[JCRecordModel alloc] init];
        model.user_id = m_userModel.userId == nil ? @"" : m_userModel.userId;
        model.code = [self getRecordCodeFromAction:action];
        model.operate_time = currentTimeString;
        model.system_version = [[UIDevice currentDevice] systemVersion];
        model.phone_model = @"iPhone";
        model.content = UN_NIL(recordContent);
        model.click_number = isClickEvent?@"1":@"0";
        model.machine_code = action == ConnectDevice ? JC_CURRENT_CONNECTED_PRINTER : @"";
        [cache_m.models addObject:model];
    }
    [JCPrintCenter sharedInstance].recordsModel = cache_m;
}

+(NSString *)getRecordCodeFromAction:(JCRECORD_ACTION)action{
    NSString *str;
    switch (action) {
        case AppStart:
            str = @"app_start";
            break;
        case AppExit:
            str = @"app_exit";
            break;
        case ClickSkipSelectDevice:
            str = @"click_skip_select_device";
            break;
        case ClickSelectDevice:
            str = @"click_select_device";
            break;
        case ClickConnectDevice:
            str = @"click_connect_device";
            break;
        case ClickCreateTemplate:
            str = @"click_create_template";
            break;
        case ClickSkipBootpage:
            str = @"click_skip_bootpage";
            break;
        case ClickIndexScanMode:
            str = @"click_index_scan_mode";
            break;
        case ClickIndexScanPrint:
            str = @"click_index_scan_print";
            break;
        case ClickIndexIndustryTemplate:
            str = @"click_index_industry_template";
            break;
        case ClickIndexCreateTemplate:
            str = @"click_index_create_template";
            break;
        case ConnectDevice:
            str = @"connect_device";
            break;
        case ClickSettingShop:
            str = @"click_setting_shop";
            break;
        case ClickIndexShop:
            str = @"click_index_shop";
            break;
        case ClickIndexBannerShop:
            str = @"click_index_banner_shop";
            break;
        case ClickUseTemplateCreat:
            str = @"click_use_template_create";
            break;
        case ClickUseEmptyTemplateCreat:
            str = @"click_empty_template_create";
            break;
        case ClickTemplateDetailBuy:
            str = @"click_template_detail_buy";
            break;
        case Click_ad_link_number:
            str = @"Click_ad_link_number";
            break;
        case Click_ad_number:
            str = @"Click_ad_number";
            break;
        case Click_ad_close_number:
            str = @"Click_ad_close_number";
            break;
        case click_ad_shop:
            str = @"click_ad_shop";
            break;
        case click_index_tutorial:
            str = @"click_index_tutorial";
            break;
        case click_my_tutorial:
            str = @"click_my_tutorial";
            break;

        case click_set_wifi:
            str = @"wifi_config_success";
            break;
        case click_artboard_choose_template:
            str = @"click_artboard_choose_template";
            break;
        case click_artboard_scan_template:
            str = @"click_artboard_scan_template";
            break;
        case click_artboard_blank_template:
            str = @"click_artboard_blank_template";
            break;
        case click_goods_choose_template:
            str = @"click_goods_choose_template";
            break;
        case click_goods_scan_template:
            str = @"click_goods_scan_template";
            break;
        case click_scan_print_callBack_success:
            str = @"click_scan_print_callBack_success";
            break;
        case click_scan_print_callBack_faild:
            str = @"click_scan_print_callBack_faild";
            break;
        case click_print_no_paper_show:
            str = @"click_print_no_paper_show";
            break;
        case click_print_paper_err_show:
            str = @"click_print_paper_err_show";
            break;
        case click_print_paper_to_shop:
            str = @"click_print_paper_to_shop";
            break;
        case click_print_rfid_err_show:
            str = @"click_print_rfid_err_show";
            break;
        case click_print_rfid_err_to_shop:
            str = @"click_print_rfid_err_to_shop";
            break;
        case click_share_template:
            str = @"click_share_template";
            break;
        case click_my_orders:
            str = @"click_my_orders";
            break;
        case detail_buy_button_show:
            str = @"detail_buy_button_show";
            break;
        case click_rfid_history_user_templates:
            str = @"click_rfid_history_user_templates";
            break;
        case click_rfid_create_template:
            str = @"click_rfid_create_template";
            break;
    }
    return str;
}

@end
