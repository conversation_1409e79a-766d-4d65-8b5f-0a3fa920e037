//
//  JCRecordModel.h
//  XYFrameWork
//
//  Created by j c on 2019/3/4.
//  Copyright © 2019年 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "XYBaseModel.h"


@protocol JCRecordModel

@end

@interface JCRecordModel : XYBaseModel
@property (nonatomic, strong) NSString <Optional> *code;//       
@property (nonatomic, strong) NSString <Optional> *user_id;//       用户id
@property (nonatomic, strong) NSString <Optional> *operate_time;//     打印时间
@property (nonatomic, strong) NSString <Optional> *system_version;  //系统版本号 iOS-12.0 Android-5.0
@property (nonatomic, strong) NSString <Optional> *phone_model;    //手机品牌  iPhone、华为、小米
@property (nonatomic, strong) NSString <Optional> *click_number; //点击次数
@property (nonatomic, strong) NSString <Optional> *machine_code; //连接的设备名称

#pragma mark JAVA
//@property (nonatomic, strong) NSString <Optional> *userId;//       用户id
//@property (nonatomic, strong) NSString <Optional> *operateTime;//     打印时间
//@property (nonatomic, strong) NSString <Optional> *systemVersion;  //系统版本号 iOS-12.0 Android-5.0
//@property (nonatomic, strong) NSString <Optional> *phoneModel;    //手机品牌  iPhone、华为、小米
//@property (nonatomic, strong) NSString <Optional> *clickNumber; //点击次数
//@property (nonatomic, strong) NSString <Optional> *machineName; //连接的设备名称
@property (nonatomic, strong) NSString <Optional> *content;
@end


@interface JCRecordsModel : XYBaseModel
@property (nonatomic, strong) NSMutableArray<JCRecordModel> *models;
@end
