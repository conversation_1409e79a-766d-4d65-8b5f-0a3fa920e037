//
//  JCStatisticsModel.h
//  XYFrameWork
//
//  Created by xy on 2018/9/26.
//  Copyright © 2018年 xiaoyao. All rights reserved.
//

#import "XYBaseModel.h"

@protocol JCStatisticModel

@end
//data/printRecord 打印记录接口, rfid打印机的打印埋点记录
//model节点下的字段添加rfid序列号
//rfid_serial_number = rfid序列号
//one_code = 商品条码
@interface JCStatisticModel : XYBaseModel
@property (nonatomic, strong) NSString <Optional> *userId;//       用户id
@property (nonatomic, strong) NSString <Optional> *machineId;//   设备id
@property (nonatomic, strong) NSString <Optional> *machineType;//   设备分类
@property (nonatomic, strong) NSString <Optional> *templeteId;//  模板id
@property (nonatomic, assign) BOOL isCloudTemplate;//  是否来自云模版
@property (nonatomic, strong) NSString <Optional> *number;//       打印张数
@property (nonatomic, strong) NSString <Optional> *addTime;//     打印时间
@property (nonatomic, strong) NSString <Optional> *systemType;     //系统类型 iOS1  android2
@property (nonatomic, strong) NSString <Optional> *systemVersion;  //系统版本号 iOS-12.0 Android-5.0
@property (nonatomic, strong) NSString <Optional> *phoneBrand;    //手机品牌  iPhone、华为、小米
@property (nonatomic, strong) NSString <Optional> *country;//            国家
@property (nonatomic, strong) NSString <Optional> *province;//            省
@property (nonatomic, strong) NSString <Optional> *city;//                城市
@property (nonatomic, strong) NSString <Optional> *district;//            区
@property (nonatomic, strong) NSString <Optional> *street;//            街道
@property (nonatomic, strong) NSString <Optional> *latitude;//            维度
@property (nonatomic, strong) NSString <Optional> *longitude;//            经度
@property (nonatomic, strong) NSString <Optional> *rfidSerialNumber;//            经度
@property (nonatomic, strong) NSString <Optional> *oneCode;//            经度

@property (nonatomic, strong) NSString <Optional> *uniqueValue; //校验值
@property (nonatomic, strong) NSString <Optional> *rfidPrintNumber; // rfid中的已打张数，普通打印机的默认传0
@property (nonatomic, strong) NSString <Optional> *updateStatus; //1上传中，没得到服务器回复，默认为空
@property (nonatomic, strong) NSString <Optional> *recordType; //1上传中，没得到服务器回复，默认为空
@property (nonatomic, strong) NSString <Optional> *firmwareVersion;  //固件版本号
@property (nonatomic, strong) NSString <Optional> *hardwareVersion; //硬件版本号
@property (nonatomic, strong) NSString <Optional> *applicationVersion; //硬件版本号
@property (nonatomic, strong) NSString <Optional> *successTimes; //rfid识别成功次数
@property (nonatomic, strong) NSString <Optional> *allTimes; //rfid识别次数
@property (nonatomic, assign) BOOL machineStatus; //机器禁用状态
@property(nonatomic,assign) int printStyle; // 打印方式:1=蓝牙,2=局域网,3=云端,4=数据线

@property(nonatomic,copy)NSString *phone;

@end

@interface JCStatisticsModel : XYBaseModel
@property (nonatomic, strong) NSMutableArray<JCStatisticModel> *models;
@end

