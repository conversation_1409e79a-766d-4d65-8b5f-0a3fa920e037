//
//  JCWifiManager.h
//  XYFrameWork
//
//  Created by <PERSON> on 2020/8/8.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import <Foundation/Foundation.h>
#import "JCBluetoothModel.h"
NS_ASSUME_NONNULL_BEGIN

typedef void(^searchRelustBlock) (NSArray<JCBluetoothModel*> *modelArray);

typedef void(^wifiConfigBlock) (BOOL isConfig);

@interface JCWifiManager : NSObject

@property(nonatomic,assign,readonly) BOOL canWifiOperate;


+ (instancetype)shareInstance;



-(void)startScan;
/**
 配网
 @param password wifi密码
 
 @param completion 配网回调
 
 */
- (void)configWithpassname:(NSString*)password completion:(void(^)(NSDictionary *))callbackDic;

/**是否有配网*/
- (NSDictionary*)isConfigedWifi:(JCBluetoothModel *)model;

/**
 配网成功与否根据是否能搜索到此wifi设备
 
 @param model 当前配网模型
 
 @param finishedBlock 回调
 */
- (void)CheckconfigWithModel:(JCBluetoothModel*)model;

-(void)connectWith:(JCBluetoothModel *)model success:(XYNormalBlock)successBlock failure:(XYNormalBlock)failureBlock;

- (void)didWifiSerachEndWithResultBlock:(searchRelustBlock)result;

- (void)didConfigWithResultBlock:(wifiConfigBlock)result;

@end

NS_ASSUME_NONNULL_END
