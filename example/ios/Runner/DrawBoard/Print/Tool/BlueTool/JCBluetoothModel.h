//
//  JCBluetoothModel.h
//  XYFrameWork
//
//  Created by j c on 2019/1/22.
//  Copyright © 2019年 Jingchen Technology Co.Ltd . All rights reserved.
//

#import <Foundation/Foundation.h>
#import <CoreBluetooth/CoreBluetooth.h>
#import "JCPrinterModel.h"

NS_ASSUME_NONNULL_BEGIN

@class JCSelectDeviceModel;
@interface JCBluetoothModel : NSObject<NSCopying,NSCoding>

/**设备连接方式*/
@property(nonatomic,assign)JCConnectStateType state;

/**设备名称*/
@property(nonatomic,copy) NSString *name;

/**设备IP*/
@property(nonatomic,copy) NSString *host;
/**是否在线*/
@property(nonatomic,assign) BOOL onLine;

@property (nonatomic,strong) NSString *childrenType;

//1-精臣、2-芝柯、3-道臻（厂商的接口）
@property (nonatomic, assign) NSInteger  printerCode; //打印机类型分辨码

@property (nonatomic,copy) JCPrinterModel *printer;

@property (nonatomic,copy) JCSelectDeviceModel *printerModel;


@property (nonatomic,strong) NSNumber *RSSI;

@property (nonatomic,strong) NSDictionary *advertisementData;

@property (nonatomic,strong) CBPeripheral *peripheral;

@property (nonatomic, strong) NSString  *printer_type;


@property (nonatomic,strong) NSString *machineStatus;

@property(nonatomic,assign)NSInteger searchType;//1-从画板进入;2-从我的设备进入


@end

NS_ASSUME_NONNULL_END
