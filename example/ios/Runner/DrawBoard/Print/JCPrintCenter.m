//
//  JCPrintCenter.m
//  Runner
//
//  Created by xingling xu on 2020/12/24.
//

#import "JCPrintCenter.h"
#import "JCBluetoothManager.h"
#import "JCPrintDevice.h"
#import "JCUniqueModel.h"
#import "JCKeychainTool.h"
#import "JCApplicationManager.h"
#import "NSString+JC_Device.h"
#define XYCenterStatistics @"XYCenterStatistics"

@implementation JCPrintCenter

+ (instancetype)sharedInstance {
    static JCPrintCenter *manager = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        manager = [[JCPrintCenter alloc] init];
    });
    return manager;
}

- (instancetype)init
{
    if (self = [super init]) {
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(uploadStatisticInfo:) name:JCPrintEventStaticNotification object:nil];
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(networkReachability:) name:@"monitorNetworking" object:nil];
    }
    return self;
}

- (void)networkReachability:(NSNotification *)notification {
    id object = notification.object;
    NSInteger value = [object integerValue];
    if (value == 2) {
        self.isWifi = YES;
        self.isOnline = YES;
        [self sendStatistics];
    } else if (value == 1) {
        self.isWifi = NO;
        self.isOnline = YES;
        [self sendStatistics];
    } else {
        self.isWifi = NO;
        self.isOnline = NO;
    }
}

- (BOOL)checkPrintRFIDPrintStatus{
    BOOL isRFIDPrintStyle = NO;
    if(JC_CURRENT_PRINTER_RFID_STATUS != 0){
        isRFIDPrintStyle = YES;
    }
    return isRFIDPrintStyle;
}

- (JCStatisticsModel *)statisticsModel
{
    if (!_statisticsModel) {
        //找硬盘
        NSDictionary *data = [[JCPrintCenter sharedInstance].cache_statistics objectForKey:XYCenterStatistics];
        @try {
            _statisticsModel = [[JCStatisticsModel alloc]initWithDictionary:data error:nil];
        } @catch (NSException *exception) {
            DLog(@"Error:userModel");
        }
        if (!_statisticsModel) {
            _statisticsModel = [[JCStatisticsModel alloc] init];
        }
    }
    return _statisticsModel;
}

- (void)setStatisticsModel:(JCStatisticsModel *)statisticsModel
{
    _statisticsModel = statisticsModel;
    //保存disk
    NSDictionary *dic;
    @try {
        dic = [statisticsModel toDictionary];
    } @catch (NSException *exception) {
        DLog(@"Error:saveLoginData");
    }
    if (dic) {
        [[JCPrintCenter sharedInstance].cache_statistics setObject:dic forKey:XYCenterStatistics];
    } else {
        [[JCPrintCenter sharedInstance].cache_statistics removeObjectForKey:XYCenterStatistics];
    }
}

//上传打印数据
- (void)uploadStatisticInfo:(NSNotification *)notification
{
    NSLog(@"----------->上传打印数据");
    NSDictionary *notifiDic = notification.object;
    NSDictionary *deviceInfoDic = [JCBluetoothManager sharedInstance].deviceDict;
    NSString *currentPrintTimes = [notifiDic valueForKey:@"printed_count"];
    if([currentPrintTimes isEqualToString:@"0"] || STR_IS_NIL(currentPrintTimes)) return;
    JCStatisticsModel *cache_m = [JCPrintCenter sharedInstance].statisticsModel;
    __block JCStatisticModel *recordModel = [[JCStatisticModel alloc] init];
    recordModel.phone = JCAPP_Manager.user.phone;
    recordModel.uniqueValue = [NSString stringWithFormat:@"%ld%d",(long)([NSDate date].timeIntervalSince1970),arc4random()%100];
    for (JCStatisticModel *mo in cache_m.models) {
        if([mo.uniqueValue isEqualToString:recordModel.uniqueValue]){
            return;
        }
    }
    recordModel.userId = m_userModel.userId;                     ////初始化用户id
    [self initPrintRecordModelWithLocalInfo:recordModel];      //初始化设备位置信息
    [self initPrintRecordModelWithDeviceAppInfo:recordModel];  //初始化设备及APp信息
    [self initPrintRecordModelWithTemplateInfo:notifiDic recordModel:recordModel];    //初始化模板信息
    [self initPrintRecordModelWithPrinterInfo:deviceInfoDic recordModel:recordModel];    //初始化打印机信息
    if(JC_CURRENT_PRINTER_RFID_STATUS != 0){
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
//            [JCAPI getPrintInfo:18 sucess:^(NSDictionary *dicInfo) {
//                if([@"0" isEqualToString:dicInfo[@"statusCode"]]){
//                    NSDictionary *printDicInfo = dicInfo[@"result"];
//                    [JCBluetoothManager sharedInstance].rfidModel = [[JCRFIDDeviceModel alloc] initWithDictionary:printDicInfo];
//                }else{
//                    [JCBluetoothManager sharedInstance].rfidModel = nil;
//                }
//                [self sendStatisticslWith:recordModel notifiDic:notifiDic];
//            }];
        });
    }else{
        [self sendStatisticslWith:recordModel notifiDic:notifiDic];
    }
}

// 初始化打印机信息 及耗材信息
- (void)initPrintRecordModelWithPrinterInfo:(NSDictionary *)deviceInfoDic recordModel:(JCStatisticModel *)recordModel{
    recordModel.printStyle = 1;
    recordModel.machineId = JC_CURRENT_CONNECTED_PRINTER;//           设备id
    recordModel.recordType = [self checkPrintRFIDPrintStatus]? @"1" : @"0";
    recordModel.machineStatus = [[JCBluetoothManager sharedInstance].connectedModel.machineStatus integerValue] > 0;
    NSString *hardwareVersion = UN_NIL([deviceInfoDic valueForKey:PRINT_DEVICE_HARDWARE_TYPE]);
    if([hardwareVersion isEqualToString:@"UNRESOPN_ERROR"]){
        hardwareVersion = @"";
    }
    NSString *firwareVersion = UN_NIL([deviceInfoDic valueForKey:PRINT_DEVICE_FIRMWARE_TYPE]);
    if([firwareVersion isEqualToString:@"UNRESOPN_ERROR"]){
        firwareVersion = @"";
    }
    firwareVersion = [firwareVersion stringByReplacingOccurrencesOfString:@"V" withString:@""];
    recordModel.hardwareVersion = hardwareVersion;
    recordModel.firmwareVersion = firwareVersion;
    NSString *successTimes = [deviceInfoDic valueForKey:PRINT_DEVICE_RFID_SUCCESS_COUNT];
    NSString *allTimes = [deviceInfoDic valueForKey:PRINT_DEVICE_RFID_COUNT];
    if(JC_CURRENT_PRINTER_RFID_STATUS == 0){
        recordModel.successTimes = @"0";
        recordModel.allTimes = @"0";
    }else if(JC_CURRENT_PRINTER_RFID_STATUS == 1){
        recordModel.successTimes = STR_IS_NIL(successTimes)?@"0":successTimes;
        recordModel.allTimes = STR_IS_NIL(allTimes)?@"0":allTimes;
    }else if(JC_CURRENT_PRINTER_RFID_STATUS == 2){
        recordModel.successTimes = [NSString stringWithFormat:@"%@|%@",@"0",STR_IS_NIL(successTimes)?@"0":successTimes];
        recordModel.allTimes = [NSString stringWithFormat:@"%@|%@",@"0",STR_IS_NIL(allTimes)?@"0":allTimes];
    }
    recordModel.rfidSerialNumber = UN_NIL([JCBluetoothManager sharedInstance].rfidModel.rfid);
    JCRFIDDeviceModel *rfidModel = [JCBluetoothManager sharedInstance].rfidModel;
    if(JC_CURRENT_PRINTER_RFID_STATUS == 0){
        recordModel.rfidSerialNumber = @"";
    }else{
        if(STR_IS_NIL(rfidModel.rfid)){
            if(JC_CURRENT_PRINTER_RFID_STATUS == 1){
                recordModel.rfidSerialNumber = UN_NIL(rfidModel.rfid);
            }else if(JC_CURRENT_PRINTER_RFID_STATUS == 2){
                recordModel.rfidSerialNumber = @"-1|-1";
            }
        }else{
            if(JC_CURRENT_PRINTER_RFID_STATUS == 1){
                recordModel.rfidSerialNumber = UN_NIL(rfidModel.rfid);
            }else if(JC_CURRENT_PRINTER_RFID_STATUS == 2){
                recordModel.rfidSerialNumber = [NSString stringWithFormat:@"%@|%@",@"-1",STR_IS_NIL(rfidModel.rfid)?@"-1":rfidModel.rfid];
            }
        }
    }
}


//初始化位置信息
- (void)initPrintRecordModelWithLocalInfo:(JCStatisticModel *)recordModel{
    JCAddressModel *addressModel = m_userModel.address;
    recordModel.country = addressModel.country;//            国家
    recordModel.province = addressModel.province;//            省
    recordModel.city = addressModel.city;//                城市
    recordModel.district = addressModel.district;//            区
//    recordModel.street = addressModel.street;//            街道
//    recordModel.latitude = addressModel.latitude;//            维度
//    recordModel.longitude = addressModel.longitude;//            经度
//    NSLog(@"上传地理位置：%@ %@ %@ %@ %@", m_userModel.country,m_userModel.province,m_userModel.city,m_userModel.district,m_userModel.street);
}

//初始化打印设备及手机信息
- (void)initPrintRecordModelWithDeviceAppInfo:(JCStatisticModel *)recordModel{
    recordModel.systemType = @"1";//         系统类型 ios1  安卓2
    recordModel.systemVersion = [[UIDevice currentDevice] systemVersion];//    系统版本
    recordModel.phoneBrand = [NSString deviceName];//        手机品牌
    NSString *versionComplateString = [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleShortVersionString"];
    recordModel.applicationVersion = versionComplateString;
}

//初始化打印模板信息
- (void)initPrintRecordModelWithTemplateInfo:(NSDictionary *)notifiDic recordModel:(JCStatisticModel *)recordModel{
    NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
    [formatter setDateFormat:@"YYYY-MM-dd HH:mm:ss"];
    NSDate *datenow = [NSDate date];
    NSString *currentTimeString = [formatter stringFromDate:datenow];
    recordModel.addTime = currentTimeString;
    recordModel.templeteId = UN_NIL([notifiDic valueForKey:@"xyid"]);//
    recordModel.isCloudTemplate = STR_IS_NIL([notifiDic valueForKey:@"oneCode"])?0:1;
    recordModel.oneCode = UN_NIL([notifiDic valueForKey:@"oneCode"]);
}

// 初始化打印张数数据 并上传
- (void)sendStatisticslWith:(JCStatisticModel *)recordModel notifiDic:(NSDictionary *)notifiDic{
    NSLog(@"初始化打印数据并上传");
    JCRFIDDeviceModel *rfidModel = [JCBluetoothManager sharedInstance].rfidModel;
    if(JC_CURRENT_PRINTER_RFID_STATUS == 0){
        recordModel.rfidPrintNumber = @"0";
        recordModel.number = [notifiDic valueForKey:@"printed_count"];
    }else{
        if(STR_IS_NIL(rfidModel.rfid)){
            if(JC_CURRENT_PRINTER_RFID_STATUS == 1){
                recordModel.rfidPrintNumber = @"0";
                recordModel.number = [notifiDic valueForKey:@"printed_count"];
            }
        }else{
            if(JC_CURRENT_PRINTER_RFID_STATUS == 1){
                recordModel.number = [notifiDic valueForKey:@"printed_count"];
                recordModel.rfidPrintNumber = [NSString stringWithFormat:@"%ld",rfidModel.usedNumber.integerValue];
                rfidModel.usedNumber = recordModel.rfidPrintNumber;
            }
        }
    }
    NSString *log = [NSString stringWithFormat:@"打印完成开始上传非RFID打印机数据----addtime: %@-----unique_value: %@ ---machineID: %@ -----printerNumber = %@---templeteID:%@",recordModel.addTime,recordModel.uniqueValue,recordModel.machineId,recordModel.number,recordModel.templeteId];
    NSLog(@"%@",log);
    JCStatisticsModel *cache_m = [JCPrintCenter sharedInstance].statisticsModel;
    [cache_m.models addObject:recordModel];
    self.statisticsModel = cache_m;
    [self sendStatistics];
}


//上传打印数据
- (void)sendStatistics
{
    XYWeakSelf
    if(self.isSendingPrintData || (NETWORK_STATE_ERROR)){
        NSLog(@"重复上传时间：%@",[XYTool getCurrentTimes]);
        return;
    }
    JCStatisticsModel *cache_m = [JCPrintCenter sharedInstance].statisticsModel;
    if (cache_m) {
        if (cache_m.models.count > 0) {
            XYBlock updateBlock = ^(NSString *data){
                if (cache_m.models.count == 0){
                    return ;
                }
                NSDictionary *jsonDic = cache_m.toDictionary;
//                NSString *printJson = jsonDic.dc_toJSONString;
                weakSelf.isSendingPrintData  = YES;
                RebaseCY_URL
                NSLog(@"#SSS# 上传的打印数据为：%@ 上传时间：%@",jsonDic,[XYTool getCurrentTimes]);
                [DCHTTPRequest postWithParams:jsonDic ModelType:nil Path:JC_data_printRecord Json:YES Success:^(__kindof YTKBaseRequest * _Nonnull request, id  _Nonnull model) {
                    NSMutableArray *deleteArray = [NSMutableArray array];
                    for (JCStatisticModel *mo in cache_m.models) {
                        if([mo.updateStatus isEqualToString:@"1"]){
                            [deleteArray addObject:mo];
                        }
                    }
                    [cache_m.models removeObjectsInArray:deleteArray];
                    if(cache_m.models.count == 0){
                        [[JCPrintCenter sharedInstance].cache_statistics removeObjectForKey:@"JCPrintCenterStatistics"];
                    }else{
                        weakSelf.statisticsModel = cache_m;
                    }
                    NSLog(@"打印记录上传成功");
                    weakSelf.isSendingPrintData = NO;
                } failure:^(YTKBaseRequest * _Nullable request, NSError * _Nullable error) {
                    weakSelf.isSendingPrintData = NO;
                    NSLog(@"打印记录上传失败:%@===%@",request.baseUrl,error);
                }];
                
            };
            if (cache_m.models.count>0) {
                BOOL haveFail = NO;
                NSMutableString *ids = [NSMutableString string];
                NSInteger i = 0;
                NSMutableArray *idArr = [NSMutableArray array];
                for (JCStatisticModel *model in cache_m.models) {
                    if(STR_IS_NIL(model.userId)){
                        model.userId = UN_NIL(m_userModel.userId);
                    }
                    NSLog(@"打印记录: 设备序列号：%@ 打印张数:%@",model.machineId,model.number);
                    if([model.updateStatus isEqualToString:@"1"]){
                        haveFail = YES;
                    }
                    model.updateStatus = @"1";
                    [ids appendString:STR_IS_NIL(model.uniqueValue) ? @"" : model.uniqueValue];
                    [idArr addObject:STR_IS_NIL(model.uniqueValue) ? @"" : model.uniqueValue];
                    if(i != cache_m.models.count - 1){
                        [ids appendString:@","];
                    }
                    i++;
                }
                if(haveFail){
                    if(idArr.count > 0){
                        NSLog(@"------>#SSS# JC_machine_checkRecordUnique");
                        RebaseCY_URL
                        [DCHTTPRequest postWithParams:@{@"checkVersion":idArr} ModelType:[JCUniqueModel class] Path:JC_machine_checkRecordUnique Json:YES Success:^(__kindof YTKBaseRequest * _Nonnull request, id  _Nonnull model) {
                            if([model isKindOfClass:[NSArray class]]){
                                NSArray *arr = model;
                                NSMutableArray *arr1 = [NSMutableArray array];
                                for(JCUniqueModel *m in arr){
                                    if([m.has isEqualToString:@"1"]){
                                        for (JCStatisticModel *m1 in cache_m.models) {
                                            if([m1.uniqueValue isEqualToString:m.unique_value]){
                                                [arr1 addObject:m1];
                                            }
                                        }
                                    }
                                }
                                [cache_m.models removeObjectsInArray:arr1];
                            }
                            updateBlock(@"1");
                        } failure:^(YTKBaseRequest * _Nullable request, NSError * _Nullable error) {
                            
                        }];
                    }
                }else{
                    updateBlock(@"1");
                }
            }
        }
    }
}


- (JCUserModel *)userModel {
    return JCAPP_Manager.user;
}

- (NSArray<Optional> *)printerNameArr{
    NSMutableArray *printerNameArr = [NSMutableArray array];
    for (JCPrinterModel *printerModel in self.printerArr) {
        NSString *printerName = printerModel.name.mutableCopy;
        [printerNameArr addObject:printerName];
    }
    return printerNameArr;
}

- (void)getPrinterList {
    [self getPrinterListRequestWithHub:NO success:nil faild:nil];
}

- (void)getPrinterListRequestWithHub:(BOOL)isShowHub success:(XYBlock)success faild:(XYBlock)faild{
#warning 如果关闭，4为了调试“打架”器
    NSString *hubText = isShowHub?@"":nil;
    XYWeakSelf
    NSString *whereString = nil;
    NSArray *printerInfoArr = [[JCFMDB shareDatabase:DB_NAME] jc_lookupTable:TABLE_PRINTERINFO dicOrModel:[JCPrinterModel class] whereFormat:whereString];
    if(printerInfoArr.count > 0){
        weakSelf.printerArr = printerInfoArr.mutableCopy;
        NSMutableArray *printerNameArr = [NSMutableArray array];
        for (JCPrinterModel *model in weakSelf.printerArr) {
            [printerNameArr addObject:model.name];
        }
        weakSelf.printerNameArr = printerNameArr;
    }
    
    [DCHTTPRequest postWithParams:@{@"page":@"1",@"limit":@"100"} ModelType:[JCPrinterModel class] Path:@"kraken/system/device/hardware" Json:YES Success:^(__kindof YTKBaseRequest * _Nonnull request, JCPrinterModel *model) {
        [[JCFMDB shareDatabase:DB_NAME] jc_deleteTable:TABLE_PRINTERINFO whereFormat:@""];
        [[JCFMDB shareDatabase:DB_NAME] jc_insertTable:TABLE_PRINTERINFO dicOrModelArray:model.list];
        NSString *whereString = nil;
        NSArray *printerInfoArr = [[JCFMDB shareDatabase:DB_NAME] jc_lookupTable:TABLE_PRINTERINFO dicOrModel:[JCPrinterModel class] whereFormat:whereString];
        weakSelf.printerArr = printerInfoArr.mutableCopy;
        NSMutableArray *printerNameArr = [NSMutableArray array];
        for (JCPrinterModel *model in weakSelf.printerArr) {
            [printerNameArr addObject:model.name];
        }
        //用来保存数据到本地
        NSString *langFilePath = [NSString stringWithFormat:@"%@/%@",DocumentsPath,@"printer"];
        NSLog(@"============%@",langFilePath);
        NSDictionary *printerDic = model.toDictionary;
        NSString *langString = printerDic.dc_toJSONString;
        [JCPrintCenter writeToFile:langFilePath fileName:[NSString stringWithFormat:@"printerList.text"] data:langString];
        weakSelf.printerNameArr = printerNameArr;
    } failure:^(YTKBaseRequest * _Nullable request, NSError * _Nullable error) {

    }];
    
}

+ (void)writeToFile:(NSString *)savePath fileName:(NSString *)fileName data:(id)fileData{
    NSFileManager* fm=[NSFileManager defaultManager];
    if(![fm fileExistsAtPath:savePath]){
        [fm createDirectoryAtPath:savePath withIntermediateDirectories:YES attributes:nil error:nil];
    }
    savePath = [NSString stringWithFormat:@"%@/%@",savePath,fileName];
    NSString *langString = fileData;
    [langString writeToFile:savePath atomically:NO encoding:NSUTF8StringEncoding error:nil];
}

@end
