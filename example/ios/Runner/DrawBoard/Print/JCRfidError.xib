<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14490.70" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14490.49"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="iN0-l3-epB" customClass="JCRfidError">
            <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="JHa-aN-hwl">
                    <rect key="frame" x="72.5" y="275.5" width="269.5" height="345.5"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="标签纸异常" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="t5o-sf-ekA">
                            <rect key="frame" x="0.0" y="0.0" width="269.5" height="45"/>
                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="45" id="pIi-vQ-Esq"/>
                            </constraints>
                            <fontDescription key="fontDescription" name="PingFangSC-Medium" family="PingFang SC" pointSize="14"/>
                            <color key="textColor" red="0.59999999999999998" green="0.59999999999999998" blue="0.59999999999999998" alpha="1" colorSpace="calibratedRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="8zk-G6-cgj">
                            <rect key="frame" x="0.0" y="44" width="269.5" height="1"/>
                            <color key="backgroundColor" red="0.93333333333333335" green="0.93333333333333335" blue="0.93333333333333335" alpha="1" colorSpace="calibratedRGB"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="1" id="lUT-ks-q0b"/>
                            </constraints>
                        </view>
                        <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="解决方案:" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="4qf-jx-9a2">
                            <rect key="frame" x="17.5" y="56" width="56" height="19"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="19" id="RYB-xy-iQZ"/>
                            </constraints>
                            <fontDescription key="fontDescription" name="PingFangSC-Medium" family="PingFang SC" pointSize="13"/>
                            <color key="textColor" red="0.32549019610000002" green="0.49803921569999998" blue="0.71764705880000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="errorTip1" highlightedImage="errorTip1" translatesAutoresizingMaskIntoConstraints="NO" id="Wp9-Dy-22U">
                            <rect key="frame" x="17.5" y="62.5" width="19" height="19"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="19" id="0Ha-L9-HQk"/>
                                <constraint firstAttribute="height" constant="19" id="3FB-Cz-q1n"/>
                            </constraints>
                        </imageView>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" usesAttributedText="YES" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="i5d-Ir-BNb">
                            <rect key="frame" x="43" y="62.5" width="209" height="18.5"/>
                            <attributedString key="attributedText">
                                <fragment content="请使用">
                                    <attributes>
                                        <color key="NSBackgroundColor" red="0.40000000000000002" green="0.40000000000000002" blue="0.40000000000000002" alpha="0.0" colorSpace="custom" customColorSpace="sRGB"/>
                                        <font key="NSFont" size="13" name="PingFangSC-Regular"/>
                                        <paragraphStyle key="NSParagraphStyle" alignment="natural" lineBreakMode="wordWrapping" baseWritingDirection="natural" tighteningFactorForTruncation="0.0"/>
                                    </attributes>
                                </fragment>
                                <fragment content="精臣正版标签">
                                    <attributes>
                                        <color key="NSBackgroundColor" red="0.20000000000000001" green="0.20000000000000001" blue="0.20000000000000001" alpha="0.0" colorSpace="custom" customColorSpace="sRGB"/>
                                        <font key="NSFont" size="13" name="PingFangSC-Semibold"/>
                                        <paragraphStyle key="NSParagraphStyle" alignment="natural" lineBreakMode="wordWrapping" baseWritingDirection="natural" tighteningFactorForTruncation="0.0"/>
                                    </attributes>
                                </fragment>
                            </attributedString>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" usesAttributedText="YES" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Ps2-PZ-ykW">
                            <rect key="frame" x="43" y="96" width="209" height="18.5"/>
                            <attributedString key="attributedText">
                                <fragment content="请单击打印机电源键，">
                                    <attributes>
                                        <font key="NSFont" size="13" name="PingFangSC-Regular"/>
                                    </attributes>
                                </fragment>
                                <fragment content="重新走纸校准">
                                    <attributes>
                                        <color key="NSBackgroundColor" red="0.20000000000000001" green="0.20000000000000001" blue="0.20000000000000001" alpha="0.0" colorSpace="custom" customColorSpace="sRGB"/>
                                        <font key="NSFont" size="13" name="PingFangSC-Semibold"/>
                                    </attributes>
                                </fragment>
                            </attributedString>
                            <nil key="highlightedColor"/>
                        </label>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="errorTip2" highlightedImage="errorTip2" translatesAutoresizingMaskIntoConstraints="NO" id="TQB-uE-x5d">
                            <rect key="frame" x="17.5" y="96" width="19" height="19"/>
                        </imageView>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" usesAttributedText="YES" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="9bD-ie-xku">
                            <rect key="frame" x="43" y="129.5" width="209" height="21"/>
                            <attributedString key="attributedText">
                                <fragment content="请取出标签纸，">
                                    <attributes>
                                        <color key="NSBackgroundColor" red="0.40000000000000002" green="0.40000000000000002" blue="0.40000000000000002" alpha="0.0" colorSpace="custom" customColorSpace="sRGB"/>
                                        <font key="NSFont" size="13" name="PingFangSC-Regular"/>
                                    </attributes>
                                </fragment>
                                <fragment content="重新卷好放入 ">
                                    <attributes>
                                        <color key="NSBackgroundColor" red="0.20000000000000001" green="0.20000000000000001" blue="0.20000000000000001" alpha="0.0" colorSpace="custom" customColorSpace="sRGB"/>
                                        <font key="NSFont" size="13" name="PingFangSC-Semibold"/>
                                    </attributes>
                                </fragment>
                                <fragment content="                     ">
                                    <attributes>
                                        <font key="NSFont" metaFont="system" size="17"/>
                                    </attributes>
                                </fragment>
                            </attributedString>
                            <nil key="highlightedColor"/>
                        </label>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="errorTip3" highlightedImage="errorTip3" translatesAutoresizingMaskIntoConstraints="NO" id="K1k-Mz-cFO">
                            <rect key="frame" x="17.5" y="129.5" width="19" height="19"/>
                        </imageView>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="DcJ-a3-DbX">
                            <rect key="frame" x="59.5" y="166.5" width="150" height="37"/>
                            <subviews>
                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="uct-Tq-Qor">
                                    <rect key="frame" x="0.0" y="0.0" width="36.5" height="36.5"/>
                                    <constraints>
                                        <constraint firstAttribute="height" constant="36.5" id="eVw-Qy-PyZ"/>
                                        <constraint firstAttribute="width" constant="36.5" id="gra-Tq-j1J"/>
                                    </constraints>
                                    <state key="normal" image="错误未选中"/>
                                    <state key="selected" image="错误选中"/>
                                </button>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="一周内不再提示" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="chf-gE-1U5">
                                    <rect key="frame" x="66" y="10" width="84" height="17"/>
                                    <fontDescription key="fontDescription" name="PingFangSC-Regular" family="PingFang SC" pointSize="12"/>
                                    <color key="textColor" red="0.59999999999999998" green="0.59999999999999998" blue="0.59999999999999998" alpha="1" colorSpace="calibratedRGB"/>
                                    <nil key="highlightedColor"/>
                                </label>
                            </subviews>
                            <constraints>
                                <constraint firstItem="chf-gE-1U5" firstAttribute="centerY" secondItem="DcJ-a3-DbX" secondAttribute="centerY" id="1YN-c3-ndn"/>
                                <constraint firstAttribute="width" constant="150" id="8Av-K5-gau"/>
                                <constraint firstItem="uct-Tq-Qor" firstAttribute="leading" secondItem="DcJ-a3-DbX" secondAttribute="leading" id="KYc-Fb-if3"/>
                                <constraint firstItem="uct-Tq-Qor" firstAttribute="centerY" secondItem="DcJ-a3-DbX" secondAttribute="centerY" id="gdj-ZP-gO7"/>
                                <constraint firstAttribute="trailing" secondItem="chf-gE-1U5" secondAttribute="trailing" id="h5G-Pt-xmc"/>
                                <constraint firstAttribute="height" constant="37" id="rNc-b9-LOl"/>
                            </constraints>
                        </view>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="bfA-RQ-4zF">
                            <rect key="frame" x="17" y="205.5" width="235" height="40"/>
                            <color key="backgroundColor" red="0.36078431370000003" green="0.53333333329999999" blue="0.75686274509999996" alpha="1" colorSpace="calibratedRGB"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="40" id="Gh9-8S-LGE"/>
                            </constraints>
                            <state key="normal" title="重新打印">
                                <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </state>
                        </button>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="bNX-IC-Yyb">
                            <rect key="frame" x="17.5" y="255.5" width="101" height="40"/>
                            <subviews>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="忽略异常" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="jm5-GR-KmD">
                                    <rect key="frame" x="26.5" y="5" width="48" height="17"/>
                                    <fontDescription key="fontDescription" name="PingFangSC-Medium" family="PingFang SC" pointSize="12"/>
                                    <color key="textColor" red="0.36078431370000003" green="0.53333333329999999" blue="0.75686274509999996" alpha="1" colorSpace="calibratedRGB"/>
                                    <nil key="highlightedColor"/>
                                </label>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="可能导致打印不清晰" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="dRZ-Vf-LWL">
                                    <rect key="frame" x="14.5" y="23.5" width="72" height="11.5"/>
                                    <fontDescription key="fontDescription" name="PingFangSC-Medium" family="PingFang SC" pointSize="8"/>
                                    <nil key="textColor"/>
                                    <nil key="highlightedColor"/>
                                </label>
                            </subviews>
                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstAttribute="bottom" secondItem="dRZ-Vf-LWL" secondAttribute="bottom" constant="5" id="FAI-0c-dJ3"/>
                                <constraint firstItem="dRZ-Vf-LWL" firstAttribute="centerX" secondItem="bNX-IC-Yyb" secondAttribute="centerX" id="dSS-I6-sja"/>
                                <constraint firstItem="jm5-GR-KmD" firstAttribute="top" secondItem="bNX-IC-Yyb" secondAttribute="top" constant="5" id="gs3-k9-uoA"/>
                                <constraint firstItem="jm5-GR-KmD" firstAttribute="centerX" secondItem="bNX-IC-Yyb" secondAttribute="centerX" id="uQL-u8-lAV"/>
                            </constraints>
                        </view>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="UB3-II-0PK">
                            <rect key="frame" x="17.5" y="255.5" width="101" height="40"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="101" id="RrC-hs-VA6"/>
                                <constraint firstAttribute="height" constant="40" id="myg-wP-Ttc"/>
                            </constraints>
                            <fontDescription key="fontDescription" name="PingFangSC-Medium" family="PingFang SC" pointSize="14"/>
                            <state key="normal">
                                <color key="titleColor" red="0.32549019610000002" green="0.49803921569999998" blue="0.71764705880000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            </state>
                        </button>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="hma-Vb-cTb">
                            <rect key="frame" x="151" y="255.5" width="101" height="40"/>
                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <fontDescription key="fontDescription" name="PingFangSC-Medium" family="PingFang SC" pointSize="14"/>
                            <state key="normal" title="重新打印">
                                <color key="titleColor" red="1" green="0.40000000000000002" blue="0.34509803921568627" alpha="1" colorSpace="calibratedRGB"/>
                            </state>
                        </button>
                        <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="yKD-9g-SPo">
                            <rect key="frame" x="41" y="305.5" width="187" height="30"/>
                            <subviews>
                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="shoppingCart" highlightedImage="shoppingCart" translatesAutoresizingMaskIntoConstraints="NO" id="aMl-Lx-oIg">
                                    <rect key="frame" x="10" y="7" width="16" height="16"/>
                                    <constraints>
                                        <constraint firstAttribute="width" constant="16" id="HWB-z2-BrW"/>
                                        <constraint firstAttribute="height" constant="16" id="Vbf-0d-dRE"/>
                                    </constraints>
                                </imageView>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="前往商城购买耗材" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="SSx-GX-9SQ">
                                    <rect key="frame" x="31" y="6.5" width="96" height="17"/>
                                    <fontDescription key="fontDescription" name="PingFangSC-Medium" family="PingFang SC" pointSize="12"/>
                                    <color key="textColor" red="0.36078431370000003" green="0.53333333329999999" blue="0.75686274509999996" alpha="1" colorSpace="calibratedRGB"/>
                                    <nil key="highlightedColor"/>
                                </label>
                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="首购特惠" highlightedImage="首购特惠" translatesAutoresizingMaskIntoConstraints="NO" id="GWA-RY-BZI">
                                    <rect key="frame" x="135" y="7" width="47" height="16"/>
                                    <constraints>
                                        <constraint firstAttribute="width" constant="47" id="Cl5-cq-G4Z"/>
                                        <constraint firstAttribute="height" constant="16" id="inm-hh-q8d"/>
                                    </constraints>
                                </imageView>
                                <button opaque="NO" tag="2" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="8g0-de-cFS">
                                    <rect key="frame" x="0.0" y="0.0" width="187" height="30"/>
                                </button>
                            </subviews>
                            <constraints>
                                <constraint firstItem="aMl-Lx-oIg" firstAttribute="leading" secondItem="yKD-9g-SPo" secondAttribute="leading" constant="10" id="5sm-vd-VDu"/>
                                <constraint firstAttribute="bottom" secondItem="8g0-de-cFS" secondAttribute="bottom" id="9FR-rG-erO"/>
                                <constraint firstItem="8g0-de-cFS" firstAttribute="top" secondItem="yKD-9g-SPo" secondAttribute="top" id="JyU-Bi-VYn"/>
                                <constraint firstItem="SSx-GX-9SQ" firstAttribute="leading" secondItem="aMl-Lx-oIg" secondAttribute="trailing" constant="5" id="XZl-ck-xF1"/>
                                <constraint firstItem="GWA-RY-BZI" firstAttribute="centerY" secondItem="yKD-9g-SPo" secondAttribute="centerY" id="Zxh-vj-6rS"/>
                                <constraint firstAttribute="trailing" secondItem="8g0-de-cFS" secondAttribute="trailing" id="aKy-uU-FOj"/>
                                <constraint firstAttribute="trailing" secondItem="GWA-RY-BZI" secondAttribute="trailing" constant="5" id="dft-Uw-uE8"/>
                                <constraint firstAttribute="height" constant="30" id="fE7-Fg-Z0e"/>
                                <constraint firstItem="aMl-Lx-oIg" firstAttribute="centerY" secondItem="yKD-9g-SPo" secondAttribute="centerY" id="i9Y-h5-PL7"/>
                                <constraint firstItem="8g0-de-cFS" firstAttribute="leading" secondItem="yKD-9g-SPo" secondAttribute="leading" id="iqv-nY-zHB"/>
                                <constraint firstItem="SSx-GX-9SQ" firstAttribute="centerY" secondItem="aMl-Lx-oIg" secondAttribute="centerY" id="w4H-Ih-7dh"/>
                                <constraint firstAttribute="width" constant="187" id="yc6-gu-oaw"/>
                            </constraints>
                        </view>
                    </subviews>
                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstItem="yKD-9g-SPo" firstAttribute="centerX" secondItem="JHa-aN-hwl" secondAttribute="centerX" id="0o4-Qg-AWw"/>
                        <constraint firstItem="4qf-jx-9a2" firstAttribute="top" secondItem="t5o-sf-ekA" secondAttribute="bottom" constant="11" id="0pl-jb-jGP"/>
                        <constraint firstAttribute="trailing" secondItem="i5d-Ir-BNb" secondAttribute="trailing" constant="17.5" id="1Kg-MW-jnx"/>
                        <constraint firstItem="hma-Vb-cTb" firstAttribute="top" secondItem="UB3-II-0PK" secondAttribute="top" id="2h2-gI-Dsc"/>
                        <constraint firstItem="K1k-Mz-cFO" firstAttribute="top" secondItem="9bD-ie-xku" secondAttribute="top" id="318-M2-1WC"/>
                        <constraint firstItem="8zk-G6-cgj" firstAttribute="width" secondItem="t5o-sf-ekA" secondAttribute="width" id="3BJ-US-ygB"/>
                        <constraint firstItem="bfA-RQ-4zF" firstAttribute="top" secondItem="DcJ-a3-DbX" secondAttribute="bottom" constant="2" id="3L1-fW-PQL"/>
                        <constraint firstItem="bNX-IC-Yyb" firstAttribute="centerX" secondItem="UB3-II-0PK" secondAttribute="centerX" id="3kG-pI-egj"/>
                        <constraint firstItem="Ps2-PZ-ykW" firstAttribute="leading" secondItem="i5d-Ir-BNb" secondAttribute="leading" id="4xE-Qm-oDP"/>
                        <constraint firstItem="i5d-Ir-BNb" firstAttribute="leading" secondItem="Wp9-Dy-22U" secondAttribute="trailing" constant="6.5" id="5I2-hD-rfd"/>
                        <constraint firstItem="Wp9-Dy-22U" firstAttribute="top" secondItem="t5o-sf-ekA" secondAttribute="bottom" constant="17.5" id="8Iu-cp-fba"/>
                        <constraint firstItem="TQB-uE-x5d" firstAttribute="leading" secondItem="Wp9-Dy-22U" secondAttribute="leading" id="C7b-d3-4hs"/>
                        <constraint firstItem="Wp9-Dy-22U" firstAttribute="leading" secondItem="JHa-aN-hwl" secondAttribute="leading" constant="17.5" id="Ct2-RN-KHu"/>
                        <constraint firstItem="t5o-sf-ekA" firstAttribute="leading" secondItem="JHa-aN-hwl" secondAttribute="leading" id="D5R-qu-Umi"/>
                        <constraint firstItem="bNX-IC-Yyb" firstAttribute="centerY" secondItem="UB3-II-0PK" secondAttribute="centerY" id="DFA-JP-VTj"/>
                        <constraint firstItem="Ps2-PZ-ykW" firstAttribute="top" secondItem="i5d-Ir-BNb" secondAttribute="bottom" constant="15" id="ETM-95-LHx"/>
                        <constraint firstItem="i5d-Ir-BNb" firstAttribute="top" secondItem="Wp9-Dy-22U" secondAttribute="top" id="HJG-JF-bBR"/>
                        <constraint firstItem="hma-Vb-cTb" firstAttribute="width" secondItem="UB3-II-0PK" secondAttribute="width" id="HWQ-U9-vk8"/>
                        <constraint firstItem="DcJ-a3-DbX" firstAttribute="top" secondItem="9bD-ie-xku" secondAttribute="bottom" constant="16" id="J3x-iu-uB9"/>
                        <constraint firstAttribute="trailing" secondItem="Ps2-PZ-ykW" secondAttribute="trailing" constant="17.5" id="JCZ-OZ-0mm"/>
                        <constraint firstItem="hma-Vb-cTb" firstAttribute="height" secondItem="UB3-II-0PK" secondAttribute="height" id="KcH-bQ-JU2"/>
                        <constraint firstAttribute="bottom" secondItem="yKD-9g-SPo" secondAttribute="bottom" constant="10" id="P3k-XG-NIl"/>
                        <constraint firstItem="hma-Vb-cTb" firstAttribute="leading" secondItem="UB3-II-0PK" secondAttribute="trailing" constant="32.5" id="PsU-Tk-NRf"/>
                        <constraint firstItem="Ps2-PZ-ykW" firstAttribute="top" secondItem="TQB-uE-x5d" secondAttribute="top" id="RSt-XM-kwG"/>
                        <constraint firstItem="bNX-IC-Yyb" firstAttribute="width" secondItem="UB3-II-0PK" secondAttribute="width" id="UGP-8X-xON"/>
                        <constraint firstItem="UB3-II-0PK" firstAttribute="leading" secondItem="JHa-aN-hwl" secondAttribute="leading" constant="17.5" id="Vv2-P3-SSV"/>
                        <constraint firstAttribute="trailing" secondItem="9bD-ie-xku" secondAttribute="trailing" constant="17.5" id="Xk3-m6-3BH"/>
                        <constraint firstAttribute="trailing" secondItem="hma-Vb-cTb" secondAttribute="trailing" constant="17.5" id="Y0v-es-F1g"/>
                        <constraint firstAttribute="trailing" secondItem="bfA-RQ-4zF" secondAttribute="trailing" constant="17.5" id="a05-ch-3BM"/>
                        <constraint firstItem="bfA-RQ-4zF" firstAttribute="leading" secondItem="JHa-aN-hwl" secondAttribute="leading" constant="17" id="aHc-eb-Ywx"/>
                        <constraint firstAttribute="trailing" secondItem="t5o-sf-ekA" secondAttribute="trailing" id="cfC-Sc-9s6"/>
                        <constraint firstItem="TQB-uE-x5d" firstAttribute="width" secondItem="Wp9-Dy-22U" secondAttribute="width" id="cfx-fi-MbL"/>
                        <constraint firstItem="K1k-Mz-cFO" firstAttribute="height" secondItem="TQB-uE-x5d" secondAttribute="height" id="exm-eb-TSU"/>
                        <constraint firstItem="8zk-G6-cgj" firstAttribute="top" secondItem="t5o-sf-ekA" secondAttribute="bottom" constant="-1" id="fKG-bJ-p2R"/>
                        <constraint firstItem="9bD-ie-xku" firstAttribute="top" secondItem="Ps2-PZ-ykW" secondAttribute="bottom" constant="15" id="fWo-WG-Ksg"/>
                        <constraint firstItem="K1k-Mz-cFO" firstAttribute="leading" secondItem="TQB-uE-x5d" secondAttribute="leading" id="gtq-7w-OQc"/>
                        <constraint firstItem="8zk-G6-cgj" firstAttribute="centerX" secondItem="t5o-sf-ekA" secondAttribute="centerX" id="h05-Ac-ke2"/>
                        <constraint firstItem="bNX-IC-Yyb" firstAttribute="height" secondItem="UB3-II-0PK" secondAttribute="height" id="ixU-Xc-azy"/>
                        <constraint firstItem="t5o-sf-ekA" firstAttribute="top" secondItem="JHa-aN-hwl" secondAttribute="top" id="lye-3o-GY9"/>
                        <constraint firstItem="K1k-Mz-cFO" firstAttribute="width" secondItem="TQB-uE-x5d" secondAttribute="width" id="mj7-7a-i7v"/>
                        <constraint firstItem="UB3-II-0PK" firstAttribute="top" secondItem="bfA-RQ-4zF" secondAttribute="bottom" constant="10" id="noC-oy-C6F"/>
                        <constraint firstItem="4qf-jx-9a2" firstAttribute="leading" secondItem="JHa-aN-hwl" secondAttribute="leading" constant="17.5" id="pAm-cd-x8j"/>
                        <constraint firstItem="8zk-G6-cgj" firstAttribute="leading" secondItem="JHa-aN-hwl" secondAttribute="leading" id="sK0-Zt-6YE"/>
                        <constraint firstItem="DcJ-a3-DbX" firstAttribute="centerX" secondItem="JHa-aN-hwl" secondAttribute="centerX" id="t3F-1T-2k1"/>
                        <constraint firstItem="9bD-ie-xku" firstAttribute="leading" secondItem="Ps2-PZ-ykW" secondAttribute="leading" id="t7e-NL-6yP"/>
                        <constraint firstAttribute="bottom" secondItem="UB3-II-0PK" secondAttribute="bottom" constant="50" id="vkd-rq-j92"/>
                        <constraint firstItem="TQB-uE-x5d" firstAttribute="height" secondItem="Wp9-Dy-22U" secondAttribute="height" id="xba-cM-kA6"/>
                    </constraints>
                </view>
            </subviews>
            <color key="backgroundColor" red="0.94901960784313721" green="0.94901960784313721" blue="0.94901960784313721" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
            <constraints>
                <constraint firstItem="JHa-aN-hwl" firstAttribute="centerX" secondItem="iN0-l3-epB" secondAttribute="centerX" id="ENW-Bc-9td"/>
                <constraint firstItem="JHa-aN-hwl" firstAttribute="centerY" secondItem="iN0-l3-epB" secondAttribute="centerY" id="Nl6-45-dVk"/>
            </constraints>
            <connections>
                <outlet property="activiteImageView" destination="GWA-RY-BZI" id="9Qo-6i-QzK"/>
                <outlet property="bgView" destination="JHa-aN-hwl" id="ZNl-iV-c3N"/>
                <outlet property="bottomSpaceConstraint" destination="vkd-rq-j92" id="oXX-rQ-cTE"/>
                <outlet property="cancelBtn" destination="UB3-II-0PK" id="OZG-zC-hzH"/>
                <outlet property="cancelLabel" destination="jm5-GR-KmD" id="Dqb-zA-1xe"/>
                <outlet property="cancelTipLabel" destination="dRZ-Vf-LWL" id="oAb-Bi-Mwq"/>
                <outlet property="descLabel" destination="4qf-jx-9a2" id="bI8-XD-CyK"/>
                <outlet property="firstLabel" destination="i5d-Ir-BNb" id="QkN-M8-dqX"/>
                <outlet property="noShowBtn" destination="uct-Tq-Qor" id="TLW-un-xpJ"/>
                <outlet property="noShowDescLabel" destination="chf-gE-1U5" id="4Eg-bk-5HP"/>
                <outlet property="noShowViewWidthConstraint" destination="8Av-K5-gau" id="aUA-Ze-ESj"/>
                <outlet property="rePrintBtn" destination="bfA-RQ-4zF" id="NDj-jN-8ev"/>
                <outlet property="secondLabel" destination="Ps2-PZ-ykW" id="HZF-Z1-yy7"/>
                <outlet property="shopViewWidthConstraint" destination="yc6-gu-oaw" id="DQ3-z8-i2Z"/>
                <outlet property="stopPrint" destination="hma-Vb-cTb" id="NhD-Yv-gs2"/>
                <outlet property="thirdLabel" destination="9bD-ie-xku" id="fdi-x6-Tbt"/>
                <outlet property="titleLabel" destination="t5o-sf-ekA" id="d9O-Hf-qnW"/>
                <outlet property="toShopBtn" destination="8g0-de-cFS" id="32b-TG-jxL"/>
                <outlet property="toShopLabel" destination="SSx-GX-9SQ" id="MGa-2A-ugY"/>
                <outlet property="toShopView" destination="yKD-9g-SPo" id="fUI-dA-NbQ"/>
            </connections>
            <point key="canvasLocation" x="47.826086956521742" y="42.857142857142854"/>
        </view>
    </objects>
    <resources>
        <image name="errorTip1" width="14" height="16"/>
        <image name="errorTip2" width="16" height="16"/>
        <image name="errorTip3" width="16" height="16"/>
        <image name="shoppingCart" width="16" height="16"/>
        <image name="错误未选中" width="15.5" height="15.5"/>
        <image name="错误选中" width="15.5" height="15.5"/>
        <image name="首购特惠" width="47" height="16"/>
    </resources>
</document>
