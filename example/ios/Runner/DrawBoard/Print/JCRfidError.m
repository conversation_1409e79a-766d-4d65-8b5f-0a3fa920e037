//
//  JCRfidError.m
//  XYFrameWork
//
//  Created by j c on 2019/11/6.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCRfidError.h"

@interface JCRfidError()
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *bottomSpaceConstraint;
@property (weak, nonatomic) IBOutlet UIView *toShopView;
@property (weak, nonatomic) IBOutlet UILabel *toShopLabel;
@property (weak, nonatomic) IBOutlet UIImageView *activiteImageView;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *noShowViewWidthConstraint;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *shopViewWidthConstraint;
@property (weak, nonatomic) IBOutlet UILabel *cancelLabel;
@property (weak, nonatomic) IBOutlet UILabel *cancelTipLabel;

@end

@implementation JCRfidError

- (void)awakeFromNib{
    [super awakeFromNib];
    [self.bgView xy_setCornerRadius:20];
    [self mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(SCREEN_WIDTH,SCREEN_HEIGHT));
    }];
    self.backgroundColor = UIColor.clearColor;
    
    [self changeLanguage];
    
    self.cancelBtn.layer.borderWidth = 2;
    self.cancelBtn.layer.borderColor = XY_HEX_RGB(0x5C88C1).CGColor;
    self.cancelBtn.layer.cornerRadius = 10;
    self.cancelBtn.layer.masksToBounds = YES;
    
    self.stopPrint.layer.borderWidth = 2;
    self.stopPrint.layer.borderColor = XY_HEX_RGB(0xFF6658).CGColor;
    self.stopPrint.layer.cornerRadius = 10;
    self.stopPrint.layer.masksToBounds = YES;
    
    self.rePrintBtn.layer.cornerRadius = 10;
    self.rePrintBtn.layer.masksToBounds = YES;
    
    [self.noShowBtn addTarget:self action:@selector(dontShow:) forControlEvents:UIControlEventTouchUpInside];
    [self.stopPrint addTarget:self action:@selector(closeAction:) forControlEvents:UIControlEventTouchUpInside];
    [self.cancelBtn addTarget:self action:@selector(cancelErrorAction:) forControlEvents:UIControlEventTouchUpInside];
    [self.rePrintBtn addTarget:self action:@selector(rePrintAction:) forControlEvents:UIControlEventTouchUpInside];
    [self.toShopBtn addTarget:self action:@selector(toShopAction:) forControlEvents:UIControlEventTouchUpInside];
    JCNCAddOb(self, @selector(changeLanguage), JCNOTICATION_ChANGELANGUAGE, nil);
    JCNCAddOb(self, @selector(refreshFirstShopStatus), JCNOTICATION_FIRST_SHOP_STATUS, nil);
    if([XY_JC_LANGUAGE isEqualToString:@"en"]){
        self.bottomSpaceConstraint.constant = 20;
        self.toShopView.hidden = YES;
    }else{
        self.bottomSpaceConstraint.constant = 50;
        self.toShopView.hidden = NO;
    }
    [self refreshFirstShopStatus];
}


-(void)changeLanguage{
    self.noShowDescLabel.text = XY_LANGUAGE_TITLE_NAMED(@"app00885", @"一周内不再提示");
    self.descLabel.text = XY_LANGUAGE_TITLE_NAMED(@"app00878", @"解决方案：");
    self.titleLabel.text = XY_LANGUAGE_TITLE_NAMED(@"app00877", @"标签纸异常");
    CGFloat labelWidth = [XY_LANGUAGE_TITLE_NAMED(@"app00885", @"一周内不再提示") sizeWithAttributes:@{NSFontAttributeName:[UIFont fontWithName:@"PingFang-SC-Medium" size:13]}].width;
    self.noShowViewWidthConstraint.constant = 37 +  labelWidth - 5;
    NSString *first = XY_LANGUAGE_TITLE_NAMED(@"app00879", @"请使用精臣正版标签纸");
    NSString *firstD = XY_LANGUAGE_TITLE_NAMED(@"app00886", @"请使用");
    NSRange firstRange = [first rangeOfString:firstD];
    NSMutableAttributedString *attr1 = [[NSMutableAttributedString alloc] initWithString:first attributes:@{NSFontAttributeName:MY_FONT_Bold(13),NSForegroundColorAttributeName:XY_HEX_RGB(0x5C88C1)}];
    [attr1 addAttributes:@{NSFontAttributeName:MY_FONT_Regular(13),NSForegroundColorAttributeName:XY_HEX_RGB(0x666666)} range:firstRange];
    self.firstLabel.attributedText = attr1;
    self.toShopLabel.text = XY_LANGUAGE_TITLE_NAMED(@"app00985", @"前往商城购买耗材");
    NSString *second = XY_LANGUAGE_TITLE_NAMED(@"app00880", @"请单击打印机电源键，重新走纸校准");
    NSString *secondD = XY_LANGUAGE_TITLE_NAMED(@"app00888", @"请单击打印机电源键");
    NSRange secondRange = [second rangeOfString:secondD];
    secondRange.length =  secondRange.length + 1;
    NSMutableAttributedString *attr2 = [[NSMutableAttributedString alloc] initWithString:second attributes:@{NSFontAttributeName:MY_FONT_Bold(13),NSForegroundColorAttributeName:XY_HEX_RGB(0x5C88C1)}];
    [attr2 addAttributes:@{NSFontAttributeName:MY_FONT_Regular(13),NSForegroundColorAttributeName:XY_HEX_RGB(0x666666)} range:secondRange];
    self.secondLabel.attributedText = attr2;
    
    NSString *third = XY_LANGUAGE_TITLE_NAMED(@"app00881", @"请取出标签纸，重新卷好放入");
    NSString *thirdD = XY_LANGUAGE_TITLE_NAMED(@"app00887", @"请取出标签纸");
    NSRange thirdRange = [third rangeOfString:thirdD];
    thirdRange.length = thirdRange.length + 1;
    NSMutableAttributedString *attr3 = [[NSMutableAttributedString alloc] initWithString:third attributes:@{NSFontAttributeName:MY_FONT_Bold(13),NSForegroundColorAttributeName:XY_HEX_RGB(0x5C88C1)}];
    [attr3 addAttributes:@{NSFontAttributeName:MY_FONT_Regular(13),NSForegroundColorAttributeName:XY_HEX_RGB(0x666666)} range:thirdRange];
    self.thirdLabel.attributedText = attr3;
    
    self.cancelLabel.text = XY_LANGUAGE_TITLE_NAMED(@"app00882", @"忽略异常");
    self.cancelTipLabel.text = XY_LANGUAGE_TITLE_NAMED(@"app00974",@"可能会导致打印不清晰");
    [self.stopPrint setTitle:XY_LANGUAGE_TITLE_NAMED(@"app00986", @"中止打印") forState:UIControlStateNormal];
    [self.rePrintBtn setTitle:XY_LANGUAGE_TITLE_NAMED(@"app00883", @"重新打印") forState:UIControlStateNormal];
    [self layoutIfNeeded];
}

- (void)refreshFirstShopStatus{
//    if([m_userModel.is_FirstShop isEqualToString:@"1"]){
//        NSString *shopImageUrl = [XYCenter sharedInstance].firstShopModel.exception.path;
//        shopImageUrl = [shopImageUrl
//        stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet
//                                                            URLFragmentAllowedCharacterSet]];
//        if(STR_IS_NIL(shopImageUrl)){
//            [self refreshShopViewWith:NO imageUrl:@""];
//        }else{
//            [self refreshShopViewWith:YES imageUrl:shopImageUrl];
//        }
//    }else{
        [self refreshShopViewWith:NO imageUrl:@""];;
//    }
}

- (void)refreshShopViewWith:(BOOL)isFirstShop imageUrl:(NSString *)shopImageUrl{
    if(isFirstShop){
        self.shopViewWidthConstraint.constant = 187;
        self.activiteImageView.hidden = NO;
        [self.activiteImageView sd_setImageWithURL:[NSURL URLWithString:shopImageUrl] placeholderImage:nil];
    }else{
        self.shopViewWidthConstraint.constant = 140;
        self.activiteImageView.hidden = YES;
    }
}


-(void)dealloc{
    [[NSNotificationCenter defaultCenter] removeObserver:self name:JCNOTICATION_ChANGELANGUAGE object:nil];
     [[NSNotificationCenter defaultCenter] removeObserver:self name:JCNOTICATION_FIRST_SHOP_STATUS object:nil];
}

+(BOOL)isShowThis{
    NSDate *date = [[NSUserDefaults standardUserDefaults] valueForKey:@"JC_A_WEEK_DONOT_SHOW_ME"];
    if(date){
        NSDate *now = [NSDate date];
        NSTimeInterval interval = [now timeIntervalSinceDate:date];
        if(interval < 7 * 24 * 3600){
            return NO;
        }
    }
    return YES;
}

-(void)dontShow:(UIButton *)sender{
    sender.selected = !sender.selected;
    if(sender.selected){
        NSDate *date = [NSDate date];
        [[NSUserDefaults standardUserDefaults] setValue:date forKeyPath:@"JC_A_WEEK_DONOT_SHOW_ME"];
        [[NSUserDefaults standardUserDefaults] synchronize];
    }else{
        [[NSUserDefaults standardUserDefaults] setValue:nil forKeyPath:@"JC_A_WEEK_DONOT_SHOW_ME"];
        [[NSUserDefaults standardUserDefaults] synchronize];
    }
}


-(void)closeAction:(UIButton *)sender{
    if(sender.selected){
        [[NSUserDefaults standardUserDefaults] setValue:nil forKeyPath:@"JC_A_WEEK_DONOT_SHOW_ME"];
        [[NSUserDefaults standardUserDefaults] synchronize];
    }
    if(self.clickAction){
        self.clickAction(@"2");
    }
}

-(void)cancelErrorAction:(UIButton *)sender{
    sender.enabled = NO;
    if(self.clickAction){
        self.clickAction(@"0");
    }
}

-(void)rePrintAction:(UIButton *)sender{
    sender.enabled = NO;
    if(self.clickAction){
        self.clickAction(@"1");
    }
}

-(void)toShopAction:(UIButton *)sender{
    sender.enabled = NO;
    if(self.clickAction){
        self.clickAction(@"3");
    }
}


@end
