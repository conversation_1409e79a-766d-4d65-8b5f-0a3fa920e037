//
//  JCPrintCessView.m
//  Runner
//
//  Created by xingling xu on 2021/1/5.
//

#import "JCPrintCessView.h"

@interface JCPrintCessView ()
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *contentLabel;
@property (nonatomic, strong) UIProgressView *progressView;
@property (nonatomic, strong) UIButton *cancelButton;
@property (nonatomic, strong) UIImageView *okImgView;
@property (nonatomic, strong) UILabel *progressLabel;
@property (nonatomic, strong) UIView *mainView;
@property (nonatomic, strong) UIView *line;
@property (nonatomic, assign) BOOL hasShow;
@end

@implementation JCPrintCessView

- (instancetype)init {
    self = [super initWithFrame:(CGRect){0,0,kSCREEN_WIDTH,kSCREEN_HEIGHT}];
    if (self) {
        self.backgroundColor = XY_HEX_RGBA(0x000000, 0.35);
        [self initRootViews];
    }
    return self;
}

- (void)show {
    UIWindow *keyWindow = [[UIApplication sharedApplication].delegate window];
    [keyWindow addSubview:self];
    self.hasShow = YES;
}

- (void)dismiss {
    [self removeFromSuperview];
    self.hasShow = NO;
}

- (void)initRootViews {
    [self addSubview:self.mainView];
    [self.mainView addSubview:self.titleLabel];
    [self.mainView addSubview:self.contentLabel];
    [self.mainView addSubview:self.progressLabel];
    [self.mainView addSubview:self.progressView];
    [self.mainView addSubview:self.cancelButton];
    [self.mainView addSubview:self.okImgView];
    [self.mainView addSubview:self.line];
    [self.mainView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(270);
        make.height.mas_equalTo(219);
        make.center.equalTo(self);
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(self.mainView);
        make.top.equalTo(self.mainView).offset(16);
        make.height.mas_equalTo(22);
    }];

    [self.okImgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.mainView);
        make.width.height.mas_equalTo(48);
    }];
    
    [self.progressView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(224);
        make.height.mas_equalTo(6);
        make.centerX.equalTo(self.mainView);
        make.top.equalTo(self.progressLabel.mas_bottom).offset(20);
    }];
    
    [self.line mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(self.mainView);
        make.height.mas_equalTo(0.5);
        make.bottom.equalTo(self.mainView).offset(-51);
    }];
    
}

- (void)refreshProgressWith:(NSInteger)currentNumber total:(NSInteger)totalNumber {
    float cur = (float)currentNumber;
    float total = (float)totalNumber;
    float pro =  cur/total;
    [self.progressView setProgress:pro animated:YES];
    self.progressLabel.text = [NSString stringWithFormat:@"%ld/%ld",(long)currentNumber,(long)totalNumber];
    [self.mainView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(219);
        make.width.mas_equalTo(270);
        make.center.equalTo(self);
    }];
    [self.titleLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(self.mainView);
        make.top.equalTo(self.mainView).offset(16);
        make.height.mas_equalTo(22);
    }];
}

- (void)showOK:(BOOL)isOk message:(NSString *)title {
    if (!self.hasShow) [self show];
    NSString *imageName = isOk?@"print_finished":@"print_error";
    [self.okImgView setImage:[UIImage imageNamed:imageName]];
    self.okImgView.hidden = NO;
    self.titleLabel.text = title;
    self.contentLabel.hidden = YES;
    self.cancelButton.hidden = YES;
    self.progressView.hidden = YES;
    self.progressLabel.hidden = YES;
    self.line.hidden = YES;
    [self.mainView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(170);
        make.width.mas_equalTo(270);
        make.center.equalTo(self);
    }];
   
    [self.titleLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(self.mainView);
        make.top.equalTo(self.okImgView.mas_bottom).offset(9);
        make.height.mas_equalTo(22);
    }];
    
    [self.okImgView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.width.height.mas_equalTo(48);
        make.centerX.equalTo(self.mainView);
        make.top.equalTo(self.mainView).offset(47);
    }];
}

- (void)showOK {
    [self showOK:YES message:@"打印完成"];
}

- (void)showError:(NSString *)error {
    [self showOK:NO message:error];
}

- (void)showContent:(NSString *)content {
    self.contentLabel.text = content;
}

- (void)showProcessContent:(NSString *)text {
    self.progressLabel.text = text;
}

- (void)cancelPrint:(UIButton *)btn {
    if (self.delegate && [self.delegate respondsToSelector:@selector(processViewCancel:)]) {
        [self.delegate processViewCancel:self];
    }
}

#pragma mark - lazy
- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] initWithFrame:(CGRect){0,16,self.mainView.width,22}];
        _titleLabel.text = @"正在打印...";
        _titleLabel.font = MY_FONT_Bold(16);
        _titleLabel.textColor = XY_HEX_RGB(0x262626);
        _titleLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _titleLabel;
}

- (UILabel *)contentLabel {
    if (!_contentLabel) {
        _contentLabel = [[UILabel alloc] initWithFrame:(CGRect){0,50,self.mainView.width,20}];
        _contentLabel.text = @"打印完记得去广场分享哦!";
        _contentLabel.font = MY_FONT_Regular(14);
        _contentLabel.textColor = XY_HEX_RGB(0x262626);
        _contentLabel.numberOfLines = 0;
        _contentLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _contentLabel;
}

- (UILabel *)progressLabel {
    if (!_progressLabel) {
        _progressLabel = [[UILabel alloc] initWithFrame:(CGRect){0,95,self.mainView.width,20}];
        _progressLabel.font = MY_FONT_Regular(14);
        _progressLabel.textColor = XY_HEX_RGB(0x262626);
        _progressLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _progressLabel;
}

- (UIProgressView *)progressView {
    if (!_progressView) {
        _progressView = [[UIProgressView alloc] initWithFrame:(CGRect){23,0,224,6}];
        _progressView.backgroundColor = XY_HEX_RGB(0xebebeb);
        _progressView.progressTintColor = XY_HEX_RGB(0x537FB7);
        _progressView.trackTintColor = XY_HEX_RGB(0xebebeb);
    }
    return _progressView;
}

- (UIButton *)cancelButton {
    if (!_cancelButton) {
        _cancelButton = [UIButton buttonWithType:UIButtonTypeCustom];
        _cancelButton.frame = (CGRect){0,self.mainView.height-50,self.mainView.width,50};
        [_cancelButton setTitleColor:XY_HEX_RGB(0xFB4B42) forState:UIControlStateNormal];
        _cancelButton.titleLabel.font = MY_FONT_Regular(17);
        [_cancelButton setTitle:@"取消打印" forState:UIControlStateNormal];
        [_cancelButton addTarget:self action:@selector(cancelPrint:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _cancelButton;
}

- (UIImageView *)okImgView {
    if (!_okImgView) {
        _okImgView = [[UIImageView alloc] init];
        _okImgView.image = [UIImage imageNamed:@"print_finished"];
        _okImgView.hidden = YES;
    }
    return _okImgView;
}

- (UIView *)mainView {
    if (!_mainView) {
        _mainView = [[UIView alloc] initWithFrame:(CGRect){(kSCREEN_WIDTH-219)/2,(kSCREEN_HEIGHT-219)/2-130,270,219}];
        _mainView.backgroundColor = [UIColor whiteColor];
        _mainView.layer.borderWidth = 1;
        _mainView.layer.borderColor = XY_HEX_RGB(0xEAEAEA).CGColor;
        _mainView.layer.cornerRadius = 12;
        _mainView.layer.masksToBounds = YES;
    }
    return _mainView;
}

- (UIView *)line {
    if (!_line) {
        _line = [[UIView alloc] initWithFrame:(CGRect){0,self.mainView.height-51,self.mainView.width,1}];
        _line.backgroundColor = XY_HEX_RGB(0xD9D9D9);
    }
    return _line;
}
@end
