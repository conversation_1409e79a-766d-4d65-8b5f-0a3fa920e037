//
//  VenderMaCro.h
//  XYFrameWork
//
//  Created by xia<PERSON><PERSON> on 16/4/22.
//  Copyright © 2016年 xiaoyao. All rights reserved.
//

#ifndef VenderMaCro_h
#define VenderMaCro_h
/*********************************************************
 *
 *  自定义宏区域
 *
 *********************************************************/


//资源路径
#define ZipName   @"JCprinter"
//#define DocumentsPath [NSSearchPathForDirectoriesInDomains(NSCachesDirectory, NSUserDomainMask, YES) lastObject]
#define DocumentsPath1   [[BoudleResourcePath substringToIndex:[BoudleResourcePath rangeOfString:@"Library"].location] stringByAppendingFormat:@"Desktop"]
#define DocumentsPath [NSHomeDirectory() stringByAppendingString:@"/Documents/JCPrintCache"]
//#define DocumentsPath [NSString stringWithFormat:@"%@/%@",DocumentsPath1,@"JCPrintCache"]

#define DocumentsFontPath [NSHomeDirectory() stringByAppendingString:@"/Documents"]

#define RESOURCE_DEFAULT_FONT_PATH [NSString stringWithFormat:@"%@/%@",DocumentsPath,@"DefaultFontFile"]
#define RESOURCE_H5_PATH [NSString stringWithFormat:@"%@/%@",DocumentsPath,ZipName]
#define RESOURCE_DB_PATH [NSString stringWithFormat:@"%@/%@",DocumentsPath,@"JCPrintDB"]
#define RESOURCE_LANGUAGE_PATH [NSString stringWithFormat:@"%@/%@",DocumentsPath,@"Lang"]
#define RESOURCE_EXCEL_PATH [NSString stringWithFormat:@"%@/%@",DocumentsPath,@"Excel"]
#define RESOURCE_IMAGE_PATH [NSString stringWithFormat:@"%@/%@",DocumentsPath,@"Image"]
//#define RESOURCE__IMAGE_SWIPER_PATH [NSString stringWithFormat:@"%@/%@",RESOURCE_IMAGE_PATH,@"Swiper"]
//#define RESOURCE__IMAGE_FIRSTSHOP_PATH [NSString stringWithFormat:@"%@/%@",RESOURCE_IMAGE_PATH,@"FirstShop"]
//#define RESOURCE_IMAGE_TEMPLATE_PATH [NSString stringWithFormat:@"%@/%@",RESOURCE_IMAGE_PATH,@"Template"]
//#define RESOURCE_IMAGE_CLOULD_TEMPLATE_PATH [NSString stringWithFormat:@"%@/%@",RESOURCE_IMAGE_PATH,@"Clould_Template"]
//#define RESOURCE_IMAGE_LOG_PATH [NSString stringWithFormat:@"%@/%@",RESOURCE_IMAGE_PATH,@"Log"]
#define RESOURCE_IMAGE_THUMB_BACK_PATH [NSString stringWithFormat:@"%@/%@",RESOURCE_IMAGE_TEMPLATE_PATH,@"ThumbBack"]
#define RESOURCE_IMAGE_CLOILD_THUMB_BACK_PATH [NSString stringWithFormat:@"%@/%@",RESOURCE_IMAGE_CLOULD_TEMPLATE_PATH,@"Clould_ThumbBack"]

//#define RESOURCE_ELEMENT_PATH [NSString stringWithFormat:@"%@/%@",RESOURCE_IMAGE_TEMPLATE_PATH,@"Element"]

#define RESOURCE_CLOILD_ELEMENT_PATH [NSString stringWithFormat:@"%@/%@",RESOURCE_IMAGE_CLOULD_TEMPLATE_PATH,@"Clould_Element"]
#define Resource_ThumbBackImage_Path(x) x?RESOURCE_IMAGE_CLOILD_THUMB_BACK_PATH:RESOURCE_IMAGE_THUMB_BACK_PATH
#define Resource_ElementImage_Path(x) x?RESOURCE_CLOILD_ELEMENT_PATH:RESOURCE_ELEMENT_PATH
#define RESOURCE_FIRM_PATH [NSString stringWithFormat:@"%@/%@",DocumentsPath,@"Firm"]
#define RESOURCE_ERROR_INFO_PATH [NSString stringWithFormat:@"%@/%@",DocumentsPath,@"Error"]

// 臣印图片存储
#define IMAGE_PATH_Icon                 [NSString stringWithFormat:@"%@/%@",RESOURCE_IMAGE_PATH,@"Icon"]
#define ImageLocalPath(imageUrl)  [NSString stringWithFormat:@"%@/%@.png",IMAGE_PATH_Icon,[imageUrl jc_urlContent]]

#define MYLOG(string) [JCAdTool writeToFileWith:string];

// 单图背景图地址
#define SigleBackImageLocalPath(templateId,isCloud) [NSString stringWithFormat:@"%@/%@_back_%@.png",Resource_ThumbBackImage_Path(isCloud),templateId,XY_JC_LANGUAGE]

#define LogoImageLocalPath(logoId) [NSString stringWithFormat:@"Logo/%@_back_%@.png",templateId,XY_JC_LANGUAGE]

// 多图背景图本地地址
#define MutipleBackImageLocalPath(templateId,isCloud,index) [NSString stringWithFormat:@"%@/%@_back_%ld_%@.png",Resource_ThumbBackImage_Path(isCloud),templateId,(long)index, XY_JC_LANGUAGE]

// 缩略图本地地址   模板id   是否云模板
#define ThumbLocalPath(templateId,isCloud) [NSString stringWithFormat:@"%@/%@_%@.png",Resource_ThumbBackImage_Path(isCloud),templateId,XY_JC_LANGUAGE]

// 元素图本地地址   元素id   是否云模板
#define ElementLocalPath(elementId,isCloud) [NSString stringWithFormat:@"%@/%@_%@.png",Resource_ElementImage_Path(isCloud),elementId,XY_JC_LANGUAGE]


#define JCLocalFileExist(localPath)  [[NSFileManager defaultManager] fileExistsAtPath:localPath]


/*********************************************************
 *
 *  常用宏区域（固定）
 *
 *********************************************************/ 

#endif /* VenderMaCro_h */
