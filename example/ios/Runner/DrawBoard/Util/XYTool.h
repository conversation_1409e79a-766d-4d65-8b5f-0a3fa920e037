//
//  XYTool.h
//  XYFrameWork
//
//  Created by xiaoyao on 16/7/20.
//  Copyright © 2016年 xiaoyao. All rights reserved.
//

#import <Foundation/Foundation.h>
#define SXServicePhone @"************"

@interface XYTool : NSObject
+ (void)xy_print_connect;
+ (void)xy_print_stop;
+ (void)xy_print; 

+ (CGRect)getScreenRectFromX:(int)x y:(int)y w:(int)w h:(int)h mm_w:(int)mm_w mm_h:(int)mm_h;
+ (CGRect)getRectFromScreenX:(int)x y:(int)y w:(int)w h:(int)h mm_w:(int)mm_w mm_h:(int)mm_h;

+ (float)getPmmWithmm:(NSString *)mm templeateWidth:(NSString *)widthString;
+ (float)getScreenNumberWithRealNumber:(float)realNumber;
+ (float)getRealNumberWithScreenNumber:(float)screenNumber;
+ (float)getRealNumberWithScreenMM:(float)screenMM;
+ (float)getMMWithScreenNumber:(float)screenNumber;
+ (float)getMMWithRealNumber:(float)realNumber;
+ (float)getScreenNumberWithMM:(float)mm;
+(NSString *)reviseString:(NSNumber *)number;
+(NSString *)getSystemLanguageNameFromCommonString:(NSString *)nationalString;

+(NSString *)getNationalLanguageNameFromSystemLanguage:(NSString *)systemLanguge;

+ (NSMutableArray *)getRangeStr:(NSString *)text findText:(NSString *)findText;

+(NSString *)getNowTimeTimestamp;
/** 毫秒时间戳转NSDate */
+ (NSDate *)dateFromMsTimeStamp:(NSTimeInterval)msInterval;
/** 获取毫秒时间戳 */
+ (NSString *)timeStampFrom:(NSDate *)date;

+ (NSString*)getCurrentTimes;

+ (NSString *)randomElementId;

+ (NSUInteger)sizeAtPath:(NSString *)path;

+ (NSString *)getTemplateWidthHegith:(NSString *)value;

// 获取小数点后两位格式化，如果后两位为00 那么只取整数位
+ (NSString *)getNumberFormatValue:(CGFloat)number;

//获取本地沙盒字体
+ (UIFont*)customFontWithPath:(NSString*)path size:(CGFloat)size;

+ (NSString *)formatStringFromGPKString:(NSString *)gpkString;
//获取本地沙盒字体 斜体
+ (UIFont *)otfFontXieWithPath:(NSString *)path size:(CGFloat)size matrix:(CGAffineTransform *)matrix attributes:(NSDictionary *)attributes;

+ (UIImage *)imageWithBorderW:(CGFloat)borderW borderColor:(UIColor *)color image:(UIImage *)image;

// 获取当前磁盘中的字体文件
+ (NSDictionary *)fonts4SDK;

//+ (NSString*)deviceName;

+ (NSString *)makeUnicodeToString:(NSString *)orignalString;

+ (UIImage*)compressImage:(UIImage*)sourceImage maxFileSize:(NSUInteger)maxSize;
@end
