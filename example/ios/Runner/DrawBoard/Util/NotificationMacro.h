//
//  NotificationMacro.h
//  XYFrameWork
//
//  Created by x<PERSON><PERSON><PERSON> on 16/7/20.
//  Copyright © 2016年 xiaoyao. All rights reserved.
//

#ifndef NotificationMacro_h
#define NotificationMacro_h

#define EXCELSTRINGCHANGE           @"EXCELSTRINGCHANGE"           //excel导入的数量
#define EXCELSELECTCHANGE           @"EXCELSELECTCHANGE"           //excel当前选中的index
#define EXCELSELECTTEXTCHANGE           @"EXCELSELECTTEXTCHANGE"                 //excel文本
#define EXCELSELECTBARCODECHANGE           @"EXCELSELECTBARCODECHANGE"           //excel条码
#define EXCELSELECTQRCODECHANGE           @"EXCELSELECTQRCODECHANGE"             //excel二维码

#define LABEL_FONT_DOWNLOAD_SUCCESS           @"LABEL_FONT_DOWNLOAD_SUCCESS"             //字体下载成功
#define LOGIN_CHANGED               @"LOGIN_CHANGED"                 //登录状态改变
//#define LOGIN_CHANGED1               @"LOGIN_CHANGED1"                 //登录状态改变
#define TEMPLATE_CHANGED               @"TEMPLATE_CHANGED"                 //模板变更首页刷新
#define ADMIN_TEMPLATE_CHANGED               @" ADMIN_TEMPLATE_CHANGED"                 //模板变更首页刷新
#define CLEAN_CAECH               @"CLEAN_CAECH"                 //清除缓存
#define LOGIN_SUCCESS               @"LOGIN_SUCCESS"                 //登录成功
#define LOGOUT_SUCCESS              @"LOGOUT_SUCCESS"                //退出登录成功

#define SXNOTIFICATION_MSGNUM       @"SXNOTIFICATION_MSG"          //所有消息数量
#define SXBECOMEACTIVE              @"SXBECOMEACTIVE"
//#define SXNOTIFICATION_SHOPPINGNUM  @"SXNOTIFICATION_SHOPPING"     //购物车数量
//#define SXNOTIFICATION_ORDERNUM     @"SXNOTIFICATION_ORDERNUM"     //订单数量
#define JCNOTICATION_LOGIN      @"JCNOTICATION_LOGIN"       //TOKEN验证失效,code = 900,902
#define JCNOTICATION_ChANGELANGUAGE @"ChANGELANGUAGE"       //语言切换
#define JCNOTICATION_FIRST_SHOP_STATUS @"JCNOTICATION_FIRST_SHOP_STATUS"       //首购特惠通知
#define JCNOTICATION_NewChANGELANGUAGE @"JCNOTICATION_NewChANGELANGUAGE"
#define JCNOTICATION_DOWNLOADPRESS @"DownLoadPress"       //语言切换
#define JCNOTICATION_DOWNLOADPRESS_ChenYin @"DownLoadPress_CheYin"       //语言切换
#define JCNOTICATION_TEMPLATE_SYNCH_CHANGE @"JCNOTICATION_TEMPLATE_SYNCH_CHANGE"       //网络状态发生变化
#define JCNOTICATION_TEMPLATE_UPDATE_INSET_2 @"JCNOTICATION_TEMPLATE_UPDATE_INSET_2"   //画板保存
#define JCNOTICATION_TEMPLATE_UPDATE_INSET_1 @"JCNOTICATION_TEMPLATE_UPDATE_INSET_1"   //同步保存
#define JCNOTICATION_TEMPLATE_REFRESH @"JCNOTICATION_TEMPLATE_REFRESH"   //画板刷新
#define JCNOTICATION_SYNC_FINISH      @"JCNOTICATION_SYNC_FINISH"  //数据同步完成
#define JCNOTICATION_EXCEL_UPLOAD      @"JCNOTICATION_EXCEL_UPLOAD"  //excel上传
#define JCNOTICATION_EXCEL_DETAIL_DONE      @"JCNOTICATION_EXCEL_DETAIL_DONE"  //excel加载完毕
#define JCNOTICATION_Element_ImportExcel      @"JCNOTICATION_Element_ImportExcel"  //excel导入
#define  PrinterStatusNotification @"PrinterStatusNotification"
#define  JCPrintEventStaticNotification @"JCPrintEventStaticNotification"
#define  RfidPrinterConnectedNotification @"RfidPrinterConnectedNotification"
#define  PrinterCoverOpenNotification @"PrinterCoverOpenNotification"
#define  PrinterBatteryPowerNotification @"PrinterBatteryPowerNotification"
#define  JCElementSheetCanCombine @"JCElementSheetCanCombine"
#define JCMALLSTATUSBARSTATUSCHANGE @"JCMALLSTATUSBARSTATUSCHANGE"

#endif /* NotificationMacro_h */
