//
//  DeviceMacro.h
//  XYFrameWork
//
//  Created by xia<PERSON><PERSON> on 16/4/27.
//  Copyright © 2016年 xiaoyao. All rights reserved.
//

#ifndef DeviceMacro_h
#define DeviceMacro_h

/*********************************************************
 *
 *  自定义宏区域
 *
 *********************************************************/


 

/*********************************************************
 *
 *  常用宏区域（固定）
 *
 *********************************************************/ 

/** 主屏幕的高度比例 */
#define SCREEN_HEIGHT_SCALE (M_SCREEN_H/640)
/** 主屏幕的宽度比例 */
#define SCREEN_WIDTH_SCALE (M_SCREEN_W/320)


/** 屏幕的分辨率 当结果为1时，显示的是普通屏幕，结果为2时，显示的是Retian屏幕 */
#define XY_M_SCREEN_SCALE [[UIScreen mainScreen] scale]
#define XY_CURRENTWINDOW [[UIApplication sharedApplication ] keyWindow] 

/** 除去信号区的屏幕的frame */
#define XY_APP_FRAME  [[UIScreen mainScreen] applicationFrame]
/** 应用程序的屏幕高度 */
#define XY_APP_FRAME_H   [[UIScreen mainScreen] applicationFrame].size.height
/** 应用程序的屏幕宽度 */
#define XY_APP_FRAME_W    [[UIScreen mainScreen] applicationFrame].size.width


/** 系统控件的默认高度 */
#define XY_D_STATUS_BAR_H   (20.f)
#define XY_D_TOP_BAR_H      (44.f)
#define XY_D_BOTTOM_BAR_H   (49.f)
#define XY_D_CELL_H (44.f)

/** 中英状态下键盘的高度 */
#define XY_ENG_KEY_BOARD_H  (216.f)
#define XY_CHN_KEY_BOARD_H  (252.f)


#define XY_IS_IOS6 (SYSTEM_VERSION >= 6.0 && SYSTEM_VERSION < 7)
#define XY_IS_IOS7 (SYSTEM_VERSION >= 7.0 && SYSTEM_VERSION < 8.0)
#define XY_IS_IOS8 (SYSTEM_VERSION >= 8.0 && SYSTEM_VERSION < 9.0)
#define XY_IS_IOS9 (SYSTEM_VERSION >= 9.0 && SYSTEM_VERSION < 10.0)

/** 设备判断 */
#define XY_IS_IPHONE [[UIDevice currentDevice] userInterfaceIdiom] == UIUserInterfaceIdiomPhone
#define XY_IS_PAD (UI_USER_INTERFACE_IDIOM() == UIUserInterfaceIdiomPad)

/** iPhone的型号 */
#define XY_IS_IPHONE4 ([[UIScreen mainScreen] bounds].size.height == 480)
#define XY_IS_IPHONE5 ([[UIScreen mainScreen] bounds].size.height == 568)
#define XY_IS_IPHONE6 ([[UIScreen mainScreen] bounds].size.height == 667)
#define XY_IS_IPHONE6_PLUS ([[UIScreen mainScreen] bounds].size.height == 736)

/** 系统的版本号 */
#define XY_SYSTEM_VERSION [[[UIDevice currentDevice] systemVersion] floatValue]

/** APP版本号 */
#define XY_APP_VERSION  [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleShortVersionString"]

/** APP BUILD 版本号 */
#define XY_APP_BUILD_VERSION  [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleVersion"]

/** APP名字 */
#define XY_APP_DISPLAY_NAME  [[NSBundle mainBundle] objectForInfoDictionaryKey:@"CFBundleDisplayName"]

/** 当前语言 */
#define XY_LOCAL_LANGUAGE [[NSLocale currentLocale] objectForKey:NSLocaleLanguageCode]

/** 本地化语言 */
#define LOCAL_LANGUAGE(x)  NSLocalizedString(x,nil)

/** 发布通知 */
#define JCNCPost3p(broadcastName, oParam, infoParam) [[NSNotificationCenter defaultCenter] postNotificationName:broadcastName object:oParam userInfo:infoParam]
#define JCNCPost2p(broadcastName, object) JCNCPost3p(broadcastName, object, nil)
#define JCNCPost(broadcastName) JCNCPost3p(broadcastName, nil, nil)
/** 添加通知观察者 */
#define JCNCAddOb(ObParam, selParam, nameParam, objParam)    [[NSNotificationCenter defaultCenter] addObserver:ObParam selector:selParam name:nameParam object:objParam]
#define JCNCRemoveOb(ObParam, nameParam, objParam)   [[NSNotificationCenter defaultCenter] removeObserver:ObParam name:nameParam object:objParam]
#define NETWORK_STATE_ERROR ![JCPrintCenter sharedInstance].isWifi && ![JCPrintCenter sharedInstance].isOnline
#define Is_MODELPARSING [XYCenter sharedInstance].isModelParsing
/** 当前国家 */
#define XY_LOCAL_COUNTRY [[NSLocale currentLocale] objectForKey:NSLocaleCountryCode]
#define BoudleResourcePath [[NSBundle mainBundle] resourcePath]

#define NetWorkErrorReturn \
        if (NETWORK_STATE_ERROR) {\
            [DCHUDHelper showMessage:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];\
            return;\
        }

/** 当前使用Xcode iPhone OS SDK 的版本
#if __IPHONE_OS_VERSION_MAX_ALLOWED > __IPHONE_8_0
NSLog(@"当前使用Xcode iPhone OS SDK 8.0 以后版本的处理");
#else
NSLog(@"当前使用Xcode iPhone OS SDK 8.0 之前版本的处理");
#endif
*/
#define ImageURL(x) ([NSURL URLWithString     :XY_HttpString(x)])    //图片地址
/*********************************************************
 *
 *  常用宏区域（固定）
 *
 *********************************************************/
#define XY_URLWithString(x) [NSURL URLWithString:x]
/**
 *  http合格校验
 */
#define XY_HttpString(x) (([x hasPrefix:@"http"]) ? (x) : ([NSString stringWithFormat:@"%@%@", @"https://www.jcsjzx.cn/index.php/Erp/", x]))
/** 判断设备室真机还是模拟器 */
#if TARGET_OS_IPHONE
#endif

#if TARGET_IPHONE_SIMULATOR
#endif

/** 判断系统为64位还是32位
#if __LP64__
NSLog(@"64");
#else
NSLog(@"32");
#endif
*/

#endif /* DeviceMacro_h */
