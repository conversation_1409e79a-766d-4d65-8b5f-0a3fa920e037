
//
//  XYTool.m
//  XYFrameWork
//
//  Created by xiaoya<PERSON> on 16/7/20.
//  Copyright © 2016年 xiaoyao. All rights reserved.
//

#import "XYTool.h"
#import <CoreText/CoreText.h>
#import "sys/utsname.h"
#import <UIImage+JKSuperCompress.h>
@implementation XYTool

+(NSString *)reviseString:(NSNumber *)number
{
    NSNumberFormatter *formatter = [[NSNumberFormatter alloc] init];
    
    formatter.numberStyle = NSNumberFormatterDecimalStyle;
    
    CGFloat floatValues = [[formatter stringFromNumber:number] floatValue];
    
    NSString *str = [NSString stringWithFormat:@"%.2f",floatValues];
    return str;
}

+ (void)xy_print_connect
{
    // 打开打印机
}

+ (void)xy_print_stop
{

}

+ (void)xy_print
{
    
} 
//宽：实际mm * 1mm对应的屏幕像素 = 实际屏幕宽
//高：实际mm * 1mm对应的屏幕高像素 = 实际屏幕高
+ (CGRect)getScreenRectFromX:(int)x y:(int)y w:(int)w h:(int)h mm_w:(int)mm_w mm_h:(int)mm_h
{
    int page_w = SCREEN_WIDTH - 45;
    CGFloat scale = page_w / mm_w; //1毫米多少屏幕像素
    CGFloat mm_x1 = x / 8;
    CGFloat mm_y1 = y / 8;
    CGFloat mm_w1 = w / 8;  //打印实际毫米w
    CGFloat mm_h1 = h / 8;  //打印实际毫米h
    CGFloat x1 = mm_x1 * scale;
    CGFloat y1 = mm_y1 * scale;
    CGFloat w1 = mm_w1 * scale;
    CGFloat h1 = mm_h1 * scale;
    
    return CGRectMake(x1, y1, w1, h1);
}

//宽： 打印像素
+ (CGRect)getRectFromScreenX:(int)x y:(int)y w:(int)w h:(int)h mm_w:(int)mm_w mm_h:(int)mm_h
{
    //打印像素转实际mm
    int page_w = SCREEN_WIDTH - 45;
    CGFloat scale = page_w / mm_w; //1mm对应的屏幕像素
    CGFloat mm_x1 = x / scale; //实际mm
    CGFloat mm_y1 = y / scale; //实际mm
    CGFloat mm_w1 = w / scale; //实际mm
    CGFloat mm_h1 = h / scale; //实际mm
    CGFloat x1 = mm_x1 * 8;
    CGFloat y1 = mm_y1 * 8;
    CGFloat w1 = mm_w1 * 8;
    CGFloat h1 = mm_h1 * 8;
    return CGRectMake(x1, y1, w1, h1);
}

+ (float)getPmmWithmm:(NSString *)mm templeateWidth:(NSString *)widthString{
    float with = SCREEN_WIDTH;
    CGFloat sc_w = [[UIScreen mainScreen] bounds].size.width;
    return (sc_w - 16)/widthString.floatValue * mm.floatValue;
}

+ (float)getScreenNumberWithRealNumber:(float)realNumber{
    int page_w = SCREEN_WIDTH - 45;
    CGFloat scale = page_w / DrawBoardInfo.template_width_mm; //1毫米多少屏幕像素
    CGFloat mm_Number1 = realNumber / 8;
    CGFloat screenNumber = mm_Number1 * scale;
    return screenNumber;
}

+ (float)getRealNumberWithScreenNumber:(float)screenNumber{
    int page_w = SCREEN_WIDTH - 45;
    CGFloat scale = page_w / DrawBoardInfo.template_width_mm; //1毫米多少屏幕像素
    CGFloat mm_screenNumber = screenNumber / scale; //屏幕mm
    CGFloat realNumber = mm_screenNumber * 8;
    return realNumber;
}

+ (float)getMMWithRealNumber:(float)realNumber{
    int page_w = SCREEN_WIDTH - 45;
    CGFloat scale = page_w / DrawBoardInfo.template_width_mm; //1毫米多少屏幕像素
    CGFloat mm_Number1 = realNumber / 8;
    return mm_Number1;
}

+ (float)getMMWithScreenNumber:(float)screenNumber{
    int page_w = SCREEN_WIDTH - 45;
    CGFloat scale = page_w / DrawBoardInfo.template_width_mm; //1毫米多少屏幕像素
    CGFloat mm_Number1 = screenNumber / scale;
    return mm_Number1;
}

+ (float)getScreenNumberWithMM:(float)mm {
    int page_w = SCREEN_WIDTH - 45;
    CGFloat scale = page_w / DrawBoardInfo.template_width_mm; //1毫米多少屏幕像素
    CGFloat mm_Number1 = mm*scale;
    return mm_Number1;
}

+ (float)getRealNumberWithScreenMM:(float)screenMM{
    return screenMM * 8;
}

+(NSString *)getSystemLanguageNameFromCommonString:(NSString *)nationalString{
    NSString *systemLanguageName = @"";
    if([nationalString rangeOfString:@"zh-"].location != NSNotFound){
        if([nationalString isEqualToString:@"zh-cn-t"] || [nationalString isEqualToString:@"zh-Hant"]){
            systemLanguageName = @"zh-Hant";
        }else{
            systemLanguageName = @"zh-Hans";
        }
    } else if([nationalString hasPrefix:@"ja"]){
        systemLanguageName = @"ja";
    }else if([nationalString hasPrefix:@"ru"]){
        systemLanguageName = @"ru";
    }
    else if([nationalString hasPrefix:@"ko"]){
        systemLanguageName = @"ko";
    }
    else{
        NSString *appLanguage = [[NSUserDefaults standardUserDefaults] valueForKey:@"appLanguage"];
        if([nationalString isEqualToString:appLanguage]){
            systemLanguageName = appLanguage;
        }else if(STR_IS_NIL(appLanguage)){
            systemLanguageName = nationalString;
        }else{
            systemLanguageName = @"en";
        }
    }
    return  systemLanguageName;
}

+ (NSString *)getNationalLanguageNameFromSystemLanguage:(NSString *)systemLanguge{
    NSString *nationalLanguageName = @"";
    if ([systemLanguge isEqualToString:@"zh-Hans"]) {
        nationalLanguageName = @"zh-cn";
    }
    else if([systemLanguge isEqualToString:@"zh-Hant"]){
        nationalLanguageName = @"zh-cn-t";
    }else if([systemLanguge hasPrefix:@"ja"]){
        nationalLanguageName = @"ja";
    }else if([systemLanguge hasPrefix:@"ru"]){
        nationalLanguageName = @"ru";
    }
    else if([systemLanguge hasPrefix:@"ko"]){
        nationalLanguageName = @"ko";
    }
    else{
        NSString *appLanguage = [[NSUserDefaults standardUserDefaults] valueForKey:@"appLanguage"];
        if([appLanguage isEqualToString:systemLanguge]){
           nationalLanguageName = systemLanguge;
        }
        else if(STR_IS_NIL(appLanguage)){
            nationalLanguageName = systemLanguge;
        }else{
           nationalLanguageName = @"en";
        }
    }
    return  nationalLanguageName;
}

+ (NSMutableArray *)getRangeStr:(NSString *)text findText:(NSString *)findText
{
    
    NSMutableArray *arrayRanges = [NSMutableArray arrayWithCapacity:3];
    
    NSString *newStr = text;
    NSString *temp = nil;
    for(int i =0; i < [newStr length]; i++)
    {
        temp = [newStr substringWithRange:NSMakeRange(i, 1)];
        if ([temp isEqualToString:findText]) {
            NSLog(@"第%d个字是:%@", i, temp);
            [arrayRanges addObject:[NSNumber numberWithInteger:i]];
        }
    }
    
    return arrayRanges;
    
}

+ (NSDate *)dateFromMsTimeStamp:(NSTimeInterval)msInterval {
    NSTimeInterval interval = msInterval/1000.0;
    NSDate *date = [NSDate dateWithTimeIntervalSince1970:interval];
    return date;
}

+ (NSString *)timeStampFrom:(NSDate *)date {
    
    NSDateFormatter *formatter = [[NSDateFormatter alloc] init] ;
    
    [formatter setDateStyle:NSDateFormatterMediumStyle];
    
    [formatter setTimeStyle:NSDateFormatterShortStyle];
    
    [formatter setDateFormat:@"YYYY-MM-dd HH:mm:ss SSS"]; // ----------设置你想要的格式,hh与HH的区别:分别表示12小时制,24小时制
    
    //设置时区,这个对于时间的处理有时很重要
    
    NSTimeZone* timeZone = [NSTimeZone timeZoneWithName:@"Asia/Shanghai"];
    
    [formatter setTimeZone:timeZone];
    
    
    NSString *timeSp = [NSString stringWithFormat:@"%.0f", (long)[date timeIntervalSince1970]*1000.0];
    
    return timeSp;
}

+(NSString *)getNowTimeTimestamp{
    return [self timeStampFrom:[NSDate date]];
}

+ (NSString *)randomElementId {
    NSInteger randomNum = arc4random() % 100;
    return  [NSString stringWithFormat:@"%@%ld",[XYTool getNowTimeTimestamp],randomNum];
}

+(NSString*)getCurrentTimes{
    
    NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
    [formatter setDateFormat:@"YYYY-MM-dd HH:mm:ss"];
    NSDate *datenow = [NSDate date];
    NSString *currentTimeString = [formatter stringFromDate:datenow];
    return currentTimeString;
}

+ (NSUInteger)sizeAtPath:(NSString *)path {
    NSFileManager *fm = [NSFileManager defaultManager];
    BOOL isDir = YES;
    if (![fm fileExistsAtPath:path isDirectory:&isDir]) {
        return 0;
    };
    NSUInteger fileSize = 0;
    // directory
    if (isDir) {
        NSDirectoryEnumerator *enumerator = [fm enumeratorAtPath:path];
        while (enumerator.nextObject) {
            // 下面注释掉的代码作用：不递归遍历子文件夹
            // if ([enumerator.fileAttributes.fileType isEqualToString:NSFileTypeDirectory]) {
            //      [enumerator skipDescendants];
            // }
            fileSize += enumerator.fileAttributes.fileSize;
        }
    } else {
        fileSize = [fm attributesOfItemAtPath:path error:nil].fileSize;
    }
    return fileSize;
}

+ (NSString *)getTemplateWidthHegith:(NSString *)value{
    if([value rangeOfString:@"."].location != NSNotFound){
        if([[value pathExtension] isEqualToString:@"00"] || [[value pathExtension] isEqualToString:@"0"]){
            value = [value substringWithRange:NSMakeRange(0, [value rangeOfString:@"."].location)];
        }
    }
    if([value rangeOfString:@"."].location != NSNotFound){
        if([value hasSuffix:@"0"]){
            value = [value stringByReplacingCharactersInRange:NSMakeRange(value.length -1, 1) withString:@""];
        }
    }
    return value;
}


+ (NSString *)getNumberFormatValue:(CGFloat)number {
    NSString *temp = [NSString stringWithFormat:@"%.2f",number];
    NSString *lastSum = [temp substringWithRange:(NSRange){temp.length-1,1}];
    NSString *lastSecondSum = [temp substringWithRange:(NSRange){temp.length-2,1}];
    NSArray *array = [temp componentsSeparatedByString:@"."];
    NSString *integerSum = array.firstObject;
    NSString *componets = @".";
    if ([lastSum isEqualToString:@"0"]) {
        lastSum = @"";
        if ([lastSecondSum isEqualToString:@"0"]) {
            lastSecondSum = @"";
            componets = @"";
        }
    }
    NSString *content = [NSString stringWithFormat:@"%@%@%@%@",integerSum,componets,lastSecondSum,lastSum];
    return content;
}

//获取本地沙盒字体
+ (UIFont*)customFontWithPath:(NSString*)path size:(CGFloat)size
{
    NSURL *fontUrl = [NSURL fileURLWithPath:path];
    CGDataProviderRef fontDataProvider = CGDataProviderCreateWithURL((__bridge CFURLRef)fontUrl);
    CGFontRef fontRef = CGFontCreateWithDataProvider(fontDataProvider);
    CGDataProviderRelease(fontDataProvider);
    CTFontManagerRegisterGraphicsFont(fontRef, NULL);
    NSString *fontName = CFBridgingRelease(CGFontCopyPostScriptName(fontRef));
    UIFont *font = [UIFont fontWithName:fontName size:size];
    CGFontRelease(fontRef);
    return font;
}

//获取本地沙盒字体 斜体

+ (UIFont *)otfFontXieWithPath:(NSString *)path size:(CGFloat)size matrix:(CGAffineTransform *)matrix attributes:(NSDictionary *)attributes {
    NSURL *fontUrl = [NSURL fileURLWithPath:path];
    CGDataProviderRef fontDataProvider = CGDataProviderCreateWithURL((__bridge CFURLRef)fontUrl);
    CGFontRef fontRef = CGFontCreateWithDataProvider(fontDataProvider);
    CGDataProviderRelease(fontDataProvider);
    CTFontManagerRegisterGraphicsFont(fontRef, NULL);
    NSString *fontName = CFBridgingRelease(CGFontCopyPostScriptName(fontRef));
    UIFont *font1 = [UIFont fontWithName:fontName size:size];
    CGFontRelease(fontRef);
    CTFontRef ctFont =CTFontCreateWithName((CFStringRef)font1.fontName,
                                           font1.pointSize,
                                           NULL);
    CTFontRef font = CTFontCreateCopyWithAttributes(ctFont, size, matrix, CTFontDescriptorCreateWithAttributes((__bridge CFDictionaryRef)attributes));
    CFRelease(ctFont);
    
    UIFont *returnFont = (__bridge UIFont *)font;
    CFRelease(font);
    return returnFont;
}

+ (NSString *)formatStringFromGPKString:(NSString *)gpkString{
    NSString *resultString = nil;
    NSData *data=[gpkString dataUsingEncoding:NSUTF8StringEncoding];
    NSStringEncoding enc = CFStringConvertEncodingToNSStringEncoding(kCFStringEncodingGB_18030_2000);
    NSString *dealwithStr = [[NSString alloc] initWithData:data encoding:enc];
    //如果扫描中文乱码则需要处理，否则不处理
    if (dealwithStr){
        NSInteger max = [gpkString length];
        char *nbytes = malloc(max + 1);
        for (NSInteger i = 0; i < max; i++){
            unichar ch = [gpkString  characterAtIndex: i];
            nbytes[i] = (char) ch;
        }
        nbytes[max] = '\0';
        resultString = [NSString stringWithCString: nbytes encoding: enc];
    }
    return resultString;
}

+ (NSDictionary *)fonts4SDK {
    NSString *fontPath = [NSString stringWithFormat:@"%@/font",DocumentsFontPath];
    NSArray *fontNameArr = [[NSFileManager defaultManager] contentsOfDirectoryAtPath:fontPath error:nil];
    NSMutableDictionary *temp = [NSMutableDictionary dictionaryWithCapacity:fontNameArr.count];
    [fontNameArr enumerateObjectsUsingBlock:^(NSString *obj, NSUInteger idx, BOOL * _Nonnull stop) {
        // 把 name.ttf 拆分出
        NSArray *componets = [obj componentsSeparatedByString:@"."];
        NSString *fontName = componets.firstObject;
        if (obj && fontName && fontName.length > 0) {
            [temp setObject:obj forKey:fontName];
        }
    }];
    [temp setObject:[NSString stringWithFormat:@"%@.ttf",text_default_font_code] forKey:@"fontDefault"];
    return temp;
}

+ (NSString *)makeUnicodeToString:(NSString *)orignalString
{
    NSString *tempStr1 = [orignalString stringByReplacingOccurrencesOfString:@"\\u"withString:@"\\U"];
    NSString *tempStr2 = [tempStr1 stringByReplacingOccurrencesOfString:@"\""withString:@"\\\""];
    NSString *tempStr3 = [[@"\""stringByAppendingString:tempStr2] stringByAppendingString:@"\""];
    NSData *tempData = [tempStr3 dataUsingEncoding:NSUTF8StringEncoding];
    //NSString* returnStr = [NSPropertyListSerialization propertyListFromData:tempData mutabilityOption:NSPropertyListImmutable format:NULL errorDescription:NULL];
    
    NSString *returnStr = [NSPropertyListSerialization propertyListWithData:tempData options:NSPropertyListMutableContainersAndLeaves format:NULL error:NULL];
    
    return [returnStr stringByReplacingOccurrencesOfString:@"\\r\\n"withString:@"\n"];
}


//+ (NSString*)deviceName{
//    struct utsname systemInfo;
//    uname(&systemInfo);
//    NSString * deviceString = [NSString stringWithCString:systemInfo.machine encoding:NSUTF8StringEncoding];
//    NSLog(@"%@",deviceString);
//    NSString * phoneType = [NSString stringWithCString: systemInfo.machine encoding:NSASCIIStringEncoding];
//    NSLog(@"%@",phoneType);
//    //iPhone
//    if ([deviceString isEqualToString:@"iPhone1,1"])    return @"iPhone 1G";
//    if ([deviceString isEqualToString:@"iPhone1,2"])    return @"iPhone 3G";
//    if ([deviceString isEqualToString:@"iPhone2,1"])    return @"iPhone 3GS";
//    if ([deviceString isEqualToString:@"iPhone3,1"])    return @"iPhone 4";
//    if ([deviceString isEqualToString:@"iPhone3,2"])    return @"Verizon iPhone 4";
//    if ([deviceString isEqualToString:@"iPhone4,1"])    return @"iPhone 4S";
//    if ([deviceString isEqualToString:@"iPhone5,1"])    return @"iPhone 5";
//    if ([deviceString isEqualToString:@"iPhone5,2"])    return @"iPhone 5";
//    if ([deviceString isEqualToString:@"iPhone5,3"])    return @"iPhone 5C";
//    if ([deviceString isEqualToString:@"iPhone5,4"])    return @"iPhone 5C";
//    if ([deviceString isEqualToString:@"iPhone6,1"])    return @"iPhone 5S";
//    if ([deviceString isEqualToString:@"iPhone6,2"])    return @"iPhone 5S";
//    if ([deviceString isEqualToString:@"iPhone7,1"])    return @"iPhone 6 Plus";
//    if ([deviceString isEqualToString:@"iPhone7,2"])    return @"iPhone 6";
//    if ([deviceString isEqualToString:@"iPhone8,1"])    return @"iPhone 6s";
//    if ([deviceString isEqualToString:@"iPhone8,2"])    return @"iPhone 6s Plus";
//    if ([deviceString isEqualToString:@"iPhone8,4"])    return @"iPhone SE";
//    if ([deviceString isEqualToString:@"iPhone9,1"])    return @"iPhone 7";
//    if ([deviceString isEqualToString:@"iPhone9,2"])    return @"iPhone 7 Plus";
//    if ([deviceString isEqualToString:@"iPhone10,1"])   return @"iPhone 8";
//    if ([deviceString isEqualToString:@"iPhone10,4"])   return @"iPhone 8";
//    if ([deviceString isEqualToString:@"iPhone10,2"])   return @"iPhone 8 Plus";
//    if ([deviceString isEqualToString:@"iPhone10,5"])   return @"iPhone 8 Plus";
//    if ([deviceString isEqualToString:@"iPhone10,3"])   return @"iPhone X";
//    if ([deviceString isEqualToString:@"iPhone10,6"])   return @"iPhone X";
//    if ([deviceString isEqualToString:@"iPhone11,8"])   return @"iPhone XR";
//    if ([deviceString isEqualToString:@"iPhone11,2"])   return @"iPhone XS";
//    if ([deviceString isEqualToString:@"iPhone11,4"])   return @"iPhone XS Max";
//    if ([deviceString isEqualToString:@"iPhone11,6"])   return @"iPhone XS Max";
//    if ([deviceString isEqualToString:@"iPhone12,1"])   return @"iPhone 11";
//    if ([deviceString isEqualToString:@"iPhone12,3"])   return @"iPhone 11 Pro";
//    if ([deviceString isEqualToString:@"iPhone12,5"])   return @"iPhone 11 Pro Max";
//    return deviceString;
//}



+ (UIImage*)compressImage:(UIImage*)sourceImage maxFileSize:(NSUInteger)maxSize {
//    CGFloat width = sourceImage.size.width;
    
    CGFloat factor = 0.9;
    NSData *imgData = UIImageJPEGRepresentation(sourceImage, 1);
    //获取原图片的大小尺寸
    while (imgData.length > maxSize && factor > 0) {
        CGSize imageSize = sourceImage.size;
        CGFloat width = imageSize.width;
        CGFloat height = imageSize.height;
        //    //根据目标图片的宽度计算目标图片的高度
        //    CGFloat targetHeight = (targetWidth / width) * height;
        //开启图片上下文
        UIGraphicsBeginImageContext(CGSizeMake(width * factor , height * factor));
        //绘制图片
        [sourceImage drawInRect:CGRectMake(0,0, width * factor , height * factor)];
        //从上下文中获取绘制好的图片
        sourceImage = UIGraphicsGetImageFromCurrentImageContext();
        imgData = UIImageJPEGRepresentation(sourceImage, 1);
        factor -= 0.1;
        //关闭图片上下文
        UIGraphicsEndImageContext();
    }
    
    return sourceImage;
}
@end
