//
//  NSString+XYCategory.h
//  XYFrameWork
//
//  Created by x<PERSON><PERSON><PERSON> on 16/5/10.
//  Copyright © 2016年 xiaoyao. All rights reserved.
//

#import <Foundation/Foundation.h>

#define IsExcelString(string) [string isJCExcelString]

@interface NSString (XYCategory)


@property (readonly, nonatomic, strong, nullable) NSDictionary *xy_toDictionary;


/**
 *  判断字段是否为数字0~9 ，eg -- 1238546
 */
- (BOOL)isPureNumandCharacters;

/**
 * 判断字段是否为小数 eg -- 233.22121
 */
- (BOOL)isFloatNumandCharacters;


/**
    字符串中是否存在系统键盘emoji
 */
- (BOOL)stringContainsEmoji;

/**
 *  判断字符串中是否存在emoji
 */
- (BOOL)hasEmoji;

/**
 判断是不是九宫格
 */
-(BOOL)isNineKeyBoard;

// 将 ${0⊙1}  转化成 0⊙1
- (NSString *)getExcelValue;

- (BOOL)isJCExcelString;

- (NSString *)jc_MD5String;

- (NSString *)jc_urlContent;

@end
