//
//  JCDrawBoardHeader.h
//  Runner
//
//  Created by xingling xu on 2020/12/8.
//

#ifndef JCDrawBoardHeader_h
#define JCDrawBoardHeader_h

#import "JCElements.h"
#import "JCContext.h"
#import "MBProgressHUD.h"
#import "JCDrawInfoManager.h"
#import "JCTemplateData.h"
#import "JCTemplateData+Transfer.h"
#import "JCTemplateData+SDK.h"
#import "JCElementModel+Transfer.h"
#import "XYTool.h"
#import "NSArray+EasyExtend.h"
#import "NSString+XYCategory.h"
#import "UIView+EasyExtend.h"
#import "VenderMaCro.h"
#import "NotificationMacro.h"
#import "DeviceMacro.h"
#import "JCAPI.h"
#import "UIView+JKBlockGesture.h"
#import "JCFontModel.h"
#import "NSDate+XHExtension.h"
#import "JKCategories.h"
#import "UIImage+Tool.h"

//LBXScan 如果需要使用LBXScanViewController控制器代码，那么下载了那些模块，请定义对应的宏
#define LBXScan_Define_Native  //包含native库
#define LBXScan_Define_ZXing   //包含ZXing库
#define LBXScan_Define_ZBar   //包含ZBar库
#define LBXScan_Define_UI     //包含界面库

#endif /* JCDrawBoardHeader_h */
