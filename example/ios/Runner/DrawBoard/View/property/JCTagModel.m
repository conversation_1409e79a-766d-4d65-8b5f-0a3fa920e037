//
//  JCTagModel.m
//  XYFrameWork
//
//  Created by xingling xu on 2020/11/25.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import "JCTagModel.h"
@implementation JCTagModel
+(JSONKeyMapper *)keyMapper
{
    return [[JSONKeyMapper alloc] initWithModelToJSONDictionary:@{@"idStr":@"id"}];
}
@end

@implementation JCTagImageModel
+(JSONKeyMapper *)keyMapper
{
    return [[JSONKeyMapper alloc] initWithModelToJSONDictionary:@{@"labelId":@"id",@"url":@"thumbnail",@"sizeId":@"categoryId"}];
}
@end


@implementation MachineInfo



@end
