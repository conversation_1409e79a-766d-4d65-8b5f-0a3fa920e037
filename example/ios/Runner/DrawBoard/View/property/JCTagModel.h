//
//  JCTagModel.h
//  XYFrameWork
//
//  Created by xingling xu on 2020/11/25.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import <Foundation/Foundation.h>
#import "DCBaseModel.h"

NS_ASSUME_NONNULL_BEGIN

@class MachineInfo;
@protocol MachineInfo
@end
/** 标签尺寸分类model */
@interface JCTagModel : DCBaseModel
@property (nonatomic, copy) NSString<Optional> *idStr;
@property (nonatomic, copy) NSString<Optional> *name;
@property (nonatomic, copy) NSString<Optional> *status;
@property (nonatomic, copy) NSString<Optional> *width;
@property (nonatomic, copy) NSString<Optional> *height;
@property (nonatomic, copy) NSArray<MachineInfo *> *adaptMachines;
@property (nonatomic, copy) NSString<Optional> *deviceId;
@end

/** 标签图片model */
@interface JCTagImageModel : DCBaseModel
@property (nonatomic, copy) NSString<Optional> *labelId;
@property (nonatomic, copy) NSString<Optional> *url;
@property (nonatomic, copy) NSString<Optional> *name;
@property (nonatomic, copy) NSString<Optional> *status;
@property (nonatomic, copy) NSString<Optional> *sizeId;
@property (nonatomic, copy) NSString<Optional> *width;
@property (nonatomic, copy) NSString<Optional> *height;
@property (nonatomic, copy) NSString<Optional> *size;
@property (nonatomic, copy) NSString<Optional> *templateId;
@property (nonatomic, copy) NSArray<Optional> *backgroundImages;
@property (nonatomic, copy) NSArray<MachineInfo *><MachineInfo> *adaptMachines;
@property (nonatomic, assign)NSNumber<Optional>* isUpperShelf;//当前标签纸是否能购买
@property (nonatomic, assign)NSNumber<Optional>* showShoppingCart;//是否显示购物车
@property (nonatomic, copy) NSString<Optional> *barcode;//用户跳转购买详情
@end

/** 适配机型model */
@interface MachineInfo : DCBaseModel
@property (nonatomic, copy) NSString<Optional> *machineId;
@property (nonatomic, copy) NSString<Optional> *machineName;
@end



NS_ASSUME_NONNULL_END
