//
//  JCPrintSettingView.m
//  XYFrameWork
//
//  Created by xingling xu on 2020/11/17.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import "JCPrintSettingView.h"

@interface JCPrintSettingView () <UITextFieldDelegate>
@property (nonatomic, strong) UIButton *concentrateDecButton;
@property (nonatomic, strong) UIButton *concentrateIncButton;
@property (nonatomic, strong) UIButton *numberDecButton;
@property (nonatomic, strong) UIButton *numberIncButton;
@property (nonatomic, assign) NSInteger number;
@property (nonatomic, assign) NSInteger concentrate;
@property (nonatomic, strong) UILabel *concentrateLabel;
@property (nonatomic, strong) UITextField *numberField;
@property (nonatomic, strong) UIButton *cancelButton;
@property (nonatomic, strong) UIButton *sureButton;
@property (nonatomic, assign) NSInteger minConcentrate;
@property (nonatomic, assign) NSInteger maxConcentrate;
// 半透明背景
@property (nonatomic, strong) UIView *maskView;
@end

@implementation JCPrintSettingView

- (void)show {
    UIWindow *keyWindow = [[UIApplication sharedApplication].delegate window];
    self.tag = 10086;
    [keyWindow addSubview:self.maskView];
    [self.maskView addSubview:self];
    self.centerX = keyWindow.width/2;
    self.centerY = keyWindow.height/2-20;
}

- (void)dismiss {
    [self.maskView removeFromSuperview];
}

- (instancetype)initWithDefaultConcentrate:(NSInteger)defaultConcentrate   min:(NSInteger)min max:(NSInteger)max {
    self = [super initWithFrame:(CGRect){(kSCREEN_WIDTH-270)/2,kSCREEN_HEIGHT/2,270,252}];
    if (self) {
        self.backgroundColor = [UIColor whiteColor];
        self.layer.borderWidth = 1;
        self.layer.borderColor = XY_HEX_RGB(0xEAEAEA).CGColor;
        self.layer.cornerRadius = 12;
        self.layer.masksToBounds = YES;
        self.minConcentrate = min;
        self.maxConcentrate = max;
        self.concentrate = defaultConcentrate;
        self.number = 1;
        [self loadRootViews];
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(keyboardWillChangeFrameNotification:) name:UIKeyboardWillChangeFrameNotification object:nil];
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(keyboardWillHideNotification:) name:UIKeyboardWillHideNotification object:nil];
    }
    return self;
}

- (void)loadRootViews {
    UILabel *titleLabel = [[UILabel alloc] initWithFrame:(CGRect){0,18,self.width,22}];
    titleLabel.textColor = XY_HEX_RGB(0x262626);
    titleLabel.font = MY_FONT_Bold(16);
    titleLabel.textAlignment = NSTextAlignmentCenter;
    titleLabel.text = @"打印标签";
    [self addSubview:titleLabel];
    
    // 打印浓度
    UILabel *concenTitleLabel = [[UILabel alloc] initWithFrame:(CGRect){23,83,60,20}];
    concenTitleLabel.textColor = XY_HEX_RGB(0x262626);
    concenTitleLabel.font = MY_FONT_Regular(14);
    concenTitleLabel.textAlignment = NSTextAlignmentLeft;
    concenTitleLabel.text = @"打印浓度";
    [self addSubview:concenTitleLabel];
    
    [self addSubview:self.concentrateDecButton];
    [self addSubview:self.concentrateLabel];
    [self addSubview:self.concentrateIncButton];
    self.concentrateDecButton.centerY = concenTitleLabel.centerY;
    self.concentrateIncButton.centerY = concenTitleLabel.centerY;
    self.concentrateLabel.centerY = concenTitleLabel.centerY;
    self.concentrateIncButton.right = self.width - 23;
    self.concentrateLabel.right = self.concentrateIncButton.left;
    self.concentrateDecButton.right = self.concentrateLabel.left;
    
    // 打印份数
    UILabel *numberTitleLabel = [[UILabel alloc] initWithFrame:(CGRect){23,143,60,20}];
    numberTitleLabel.textColor = XY_HEX_RGB(0x262626);
    numberTitleLabel.font = MY_FONT_Regular(14);
    numberTitleLabel.textAlignment = NSTextAlignmentLeft;
    numberTitleLabel.text = @"打印份数";
    [self addSubview:numberTitleLabel];
    
    [self addSubview:self.numberDecButton];
    [self addSubview:self.numberField];
    [self addSubview:self.numberIncButton];
    self.numberDecButton.centerY = numberTitleLabel.centerY;
    self.numberField.centerY = numberTitleLabel.centerY;
    self.numberIncButton.centerY = numberTitleLabel.centerY;
    self.numberIncButton.right = self.width - 23;
    self.numberField.right = self.numberIncButton.left;
    self.numberDecButton.right = self.numberField.left;
    
    // button
    [self addSubview:self.cancelButton];
    [self addSubview:self.sureButton];
    
    UIView *line = [[UIView alloc] initWithFrame:(CGRect){0,self.height-50,self.width,1}];
    line.backgroundColor = XY_HEX_RGB(0XEBEBEB);
    [self addSubview:line];
    
    UIView *verticalLine = [[UIView alloc] initWithFrame:(CGRect){self.width/2,self.height-50,1,50}];
    verticalLine.backgroundColor = XY_HEX_RGB(0XEBEBEB);
    [self addSubview:verticalLine];
    
    [self refreshButtonState];
}

- (void)setNumber:(NSInteger)number {
    _number = number;
    [self refreshButtonState];
}


#pragma mark - custom selector
- (void)concentrateDecrease:(UIButton *)button {
    if (self.concentrate <= 1) return;
    self.concentrate--;
    [self refreshButtonState];
}

- (void)concentrateIncrease:(UIButton *)button {
    if (self.concentrate >= self.maxConcentrate) return;
    self.concentrate++;
    [self refreshButtonState];
}

- (void)numberDecrease:(UIButton *)button {
    if (self.number <= 1) return;
    self.number--;
    [self refreshButtonState];
}

- (void)numberIncrease:(UIButton *)button {
    if (self.number >= 999) return;
    self.number++;
    [self refreshButtonState];
}

- (void)cancel:(UIButton *)button {
    [self dismiss];
}

- (void)sure:(UIButton *)button {
    [self dismiss];
    NSInteger num = MAX(self.number, 1);
    if (self.delegate && [self.delegate respondsToSelector:@selector(settingView:concentrate:number:)]) {
        [self.delegate settingView:self concentrate:self.concentrate number:num];
    }
}

- (void)refreshButtonState {
    self.concentrateLabel.text = StringFromInt(self.concentrate);
    NSString *value = StringFromInt(self.number);
    self.numberField.text = value;
    self.concentrateDecButton.enabled = self.concentrate > self.minConcentrate;
    self.concentrateIncButton.enabled = self.concentrate < self.maxConcentrate;
    self.numberDecButton.enabled = self.number > 1;
    self.numberIncButton.enabled = self.number < 999;
}

- (UIButton *)customButtonWithImageName:(NSString *)imageName {
    UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
    button.layer.cornerRadius = 10;
    button.layer.masksToBounds = YES;
    button.size = (CGSize){44,44};
    [button setBackgroundImage:[UIImage imageWithColor:XY_HEX_RGB(0xf5f5f5)] forState:UIControlStateNormal];
    [button setBackgroundImage:[UIImage imageWithColor:XY_HEX_RGB(0xFFECEB)] forState:UIControlStateSelected];
    [button setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@_normal",imageName]] forState:UIControlStateNormal];
    [button setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@_selected",imageName]] forState:UIControlStateSelected];
    [button setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@_unable",imageName]] forState:UIControlStateDisabled];
    return button;
}

#pragma mark - textField Notification
- (void)keyboardWillChangeFrameNotification:(NSNotification *)notification {
    // 获取键盘基本信息（动画时长与键盘高度）
    NSDictionary *userInfo = [notification userInfo];
    CGRect rect = [userInfo[UIKeyboardFrameEndUserInfoKey] CGRectValue];
    CGFloat keyboardHeight = CGRectGetHeight(rect);
    self.centerY = kSCREEN_HEIGHT-keyboardHeight-10-self.height/2;
}

- (void)keyboardWillHideNotification:(NSNotification *)notification {
    self.centerY = kSCREEN_HEIGHT/2-20;
}

#pragma mark - textField

- (BOOL)textField:(UITextField *)textField shouldChangeCharactersInRange:(NSRange)range replacementString:(NSString *)string {
    NSString * toBeString = [textField.text stringByReplacingCharactersInRange:range withString:string];
    if (toBeString.integerValue > 999) {
        JCPerformSelectorWithOneObject(self.context.baseController, sel(@"showMessage:"), @"数字超限");
        return NO;
    }
    if (toBeString.length > 0) {
        _number = toBeString.integerValue;
        self.numberDecButton.enabled = self.number > 1;
        self.numberIncButton.enabled = self.number < 999;
    } else {
        _number = 0;
    }
    return YES;
}

#pragma mark - lazy
- (UIButton *)concentrateDecButton {
    if (!_concentrateDecButton) {
        _concentrateDecButton = [self customButtonWithImageName:@"decrease_action"];
        [_concentrateDecButton addTarget:self action:@selector(concentrateDecrease:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _concentrateDecButton;
}

- (UIButton *)concentrateIncButton {
    if (!_concentrateIncButton) {
        _concentrateIncButton = [self customButtonWithImageName:@"increase_action"];
        [_concentrateIncButton addTarget:self action:@selector(concentrateIncrease:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _concentrateIncButton;
}

- (UIButton *)numberDecButton {
    if (!_numberDecButton) {
        _numberDecButton = [self customButtonWithImageName:@"decrease_action"];
        [_numberDecButton addTarget:self action:@selector(numberDecrease:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _numberDecButton;
}

- (UIButton *)numberIncButton {
    if (!_numberIncButton) {
        _numberIncButton = [self customButtonWithImageName:@"increase_action"];
        [_numberIncButton addTarget:self action:@selector(numberIncrease:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _numberIncButton;
}

- (UILabel *)concentrateLabel {
    if (!_concentrateLabel) {
        _concentrateLabel = [[UILabel alloc] initWithFrame:(CGRect){0,0,48,44}];
        _concentrateLabel.textColor = XY_HEX_RGB(0x262626);
        _concentrateLabel.font = MY_FONT_Bold(16);
        _concentrateLabel.text = @"1";
        _concentrateLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _concentrateLabel;
}

- (UITextField *)numberField {
    if (!_numberField) {
        _numberField = [[UITextField alloc] initWithFrame:(CGRect){0,0,48,44}];
        _numberField.font = MY_FONT_Bold(16);
        _numberField.textColor = XY_HEX_RGB(0x333333);
        _numberField.delegate = self;
        _numberField.keyboardType = UIKeyboardTypeNumberPad;
        _numberField.textAlignment = NSTextAlignmentCenter;
        _numberField.text = @"1";
    }
    return _numberField;
}

- (UIButton *)cancelButton {
    if (!_cancelButton) {
        _cancelButton = [UIButton buttonWithType:UIButtonTypeCustom];
        _cancelButton.frame = (CGRect){0,self.height-50,self.width/2,50};
        _cancelButton.titleLabel.font = MY_FONT_Regular(17);
        [_cancelButton setTitle:@"取消" forState:UIControlStateNormal];
        [_cancelButton setTitleColor:XY_HEX_RGB(0x595959) forState:UIControlStateNormal];
        [_cancelButton addTarget:self action:@selector(cancel:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _cancelButton;
}

- (UIButton *)sureButton {
    if (!_sureButton) {
        _sureButton = [UIButton buttonWithType:UIButtonTypeCustom];
        _sureButton.frame = (CGRect){self.width/2,self.height-50,self.width/2,50};
        _sureButton.titleLabel.font = MY_FONT_Regular(17);
        [_sureButton setTitle:@"确定" forState:UIControlStateNormal];
        [_sureButton setTitleColor:XY_HEX_RGB(0x537fb7) forState:UIControlStateNormal];
        [_sureButton addTarget:self action:@selector(sure:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _sureButton;
}

- (UIView *)maskView {
    if (!_maskView) {
        _maskView = [[UIView alloc] initWithFrame:(CGRect){0,0,kSCREEN_WIDTH,kSCREEN_HEIGHT}];
        _maskView.backgroundColor = XY_HEX_RGBA(0x000000,0.2);
    }
    return _maskView;
}

@end
