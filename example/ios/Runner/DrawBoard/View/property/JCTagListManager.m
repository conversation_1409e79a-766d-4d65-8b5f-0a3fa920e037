//
//  JCTagListManager.m
//  XYFrameWork
//
//  Created by xingling xu on 2020/12/4.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import "JCTagListManager.h"

#define UserUsedTagId @"1"

@interface JCTagListManager ()
@property (nonatomic, copy) NSArray *tagList;
@end

@implementation JCTagListManager

+ (id)sharedManager {
    static dispatch_once_t once;
    static id instance;
    dispatch_once(&once, ^{
        instance = [self new];
    });
    return instance;
}

- (void)requestTagSizes:(NSString *)machineId {
    [self requestTagSizes:machineId completion:nil];
}

- (void)requestTagSizes:(NSString *)machineId completion:(void(^)(void))completion {
    // 缓存
    NSArray *temp = [[JCFMDB shareDatabase:DB_NAME] jc_lookupTable:TABLE_CYTagListModel dicOrModel:[JCTagModel class] whereFormat:@"where deviceId = '%@'",machineId];
    if (temp.count > 0) {
        self.tagList = temp;
        if (completion) {
            completion();
        }
    }
    // request
    NSDictionary *params = @{@"deviceId":UN_NIL(machineId),@"status":@(YES)};
    NSString *path = @"kraken/print/label/category/list";
    [DCHTTPRequest postWithParams:params ModelType:[JCTagModel class] Path:path Json:YES Success:^(__kindof YTKBaseRequest * _Nonnull request, NSArray *array) {
        if ([array isKindOfClass:[NSArray class]] && array.count > 0) {
            [array enumerateObjectsUsingBlock:^(JCTagModel *obj, NSUInteger idx, BOOL * _Nonnull stop) {
                obj.deviceId = machineId;
            }];
            self.tagList = array;
            [[JCFMDB shareDatabase:DB_NAME] jc_deleteAllDataFromTable:TABLE_CYTagListModel];
            [[JCFMDB shareDatabase:DB_NAME] jc_insertTable:TABLE_CYTagListModel dicOrModelArray:array];
        }
        if (completion) {
            completion();
        }
    } failure:^(YTKBaseRequest * _Nullable request, NSError * _Nullable error) {
        if (completion) {
            completion();
        }
    }];
}

- (NSArray *)getTagList {
    JCTagModel *usedTagModel = [JCTagModel new];
    usedTagModel.idStr = UserUsedTagId;
    usedTagModel.name = @"用过";
    NSMutableArray *temp = [NSMutableArray arrayWithArray:self.tagList];
    [temp insertObject:usedTagModel atIndex:0];
    return temp;
}

- (void)saveList:(NSArray<JCTagImageModel *> *)array {
    [array enumerateObjectsUsingBlock:^(JCTagImageModel * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        NSString *where = [NSString stringWithFormat:@"where templateId = '%@'",obj.templateId];
        [[JCFMDB shareDatabase:DB_NAME] jc_inDatabase:^{
            NSArray *temp = [[JCFMDB shareDatabase:DB_NAME] jc_lookupTable:TABLE_CYTagImageModel dicOrModel:[JCTagImageModel class] whereFormat:where];
            if (temp.count > 0) {
                [[JCFMDB shareDatabase:DB_NAME] jc_updateTable:TABLE_CYTagImageModel dicOrModel:obj whereFormat:where];
            } else {
                [[JCFMDB shareDatabase:DB_NAME] jc_insertTable:TABLE_CYTagImageModel dicOrModel:obj];
            }
        }];
    }];
}

- (NSArray *)getDBDataFrom:(JCTagModel *)tagModel {
    NSString *where = [NSString stringWithFormat:@"where categoryId = '%@'",tagModel.idStr];
    NSArray *temp = [[JCFMDB shareDatabase:DB_NAME] jc_lookupTable:TABLE_CYTagImageModel dicOrModel:[JCTagImageModel class] whereFormat:where];
    return temp;
}

@end
