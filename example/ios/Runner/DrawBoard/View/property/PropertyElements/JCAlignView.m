//
//  JCAlignView.m
//  XYFrameWork
//
//  Created by xingling xu on 2020/11/13.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import "JCAlignView.h"
#import "JCSelectionView.h"
#import "JCMoveView.h"

/** 微调每次移动距离,单位毫米 */
static CGFloat distance_move_every_time = 0.2;

@interface JCAlignView () <JCSelectionViewDelegate>
@property (nonatomic, strong) JCSelectionView *horizontalView;
@property (nonatomic, strong) JCSelectionView *verticalView;
@property (nonatomic, strong) JCMoveView *moveView;
@end

@implementation JCAlignView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self loadRootView];
    }
    return self;
}


- (void)loadRootView {
    [self addSubview:self.horizontalView];
    [self addSubview:self.verticalView];
    [self addSubview:self.moveView];
}

- (void)layoutSubviews {
    [super layoutSubviews];
    self.horizontalView.left = 20;
    self.horizontalView.top = 55;
    self.verticalView.left = 20;
    self.verticalView.top = self.horizontalView.bottom + 20;
    self.moveView.centerY = self.horizontalView.bottom + 10;
    self.moveView.right = SCREEN_WIDTH - 20;
}

- (void)setElement:(JCElementBaseView *)element {
    [super setElement:element];
    NSLog(@"%@",element);
//    NSInteger hIndex = 0;
//    NSInteger vIndex = 0;
//    if (element.alignPosition.horizontal == JCAlignStyleLeft) {
//        hIndex = 0;
//    }else if (element.alignPosition.horizontal == JCAlignStyleRight) {
//        hIndex = 2;
//    }else if (element.alignPosition.horizontal == JCAlignStyleCenterX) {
//        hIndex = 1;
//    }
//
//    if (element.alignPosition.vertical == JCAlignStyleTop) {
//        vIndex = 0;
//    }else if (element.alignPosition.vertical == JCAlignStyleBottom) {
//        vIndex = 2;
//    }else if (element.alignPosition.vertical == JCAlignStyleCenterY) {
//        vIndex = 1;
//    }
//    if (hIndex != 0) {
//        self.horizontalView.selectIndex = hIndex;
//    }
//    if (vIndex != 0) {
//        self.verticalView.selectIndex = vIndex;
//    }
}
#pragma mark -- JCSelectionViewDelegate
//注释掉了关于对齐位置的记录,因为元素会随时在画板中被拖动,所以只在点击
- (void)selectionView:(JCSelectionView *)selectionView selectIndex:(NSInteger)index {
    if (self.element.isLock) {
        JCPerformSelector(self.context.baseController, sel(@"alignNotAllow"));
        return;
    }
    CGFloat boardWidth = [JCDrawInfoManager sharedManager].boardWidth;
    CGFloat boardHeight = [JCDrawInfoManager sharedManager].boardHeight;
//    JCAlignStyle horizontal = self.element.alignPosition.horizontal;
//    JCAlignStyle vertical = self.element.alignPosition.vertical;
    if ([selectionView isEqual:self.horizontalView] ){
        if (index == 0) {
            self.element.left = 0;
//            horizontal = JCAlignStyleLeft;
        } else if (index == 1) {
            self.element.centerX = boardWidth/2;
//            horizontal = JCAlignStyleCenterX;
        } else if (index == 2) {
            self.element.right = boardWidth;
//            horizontal = JCAlignStyleRight;
        }
    } else {
        if (index == 0) {
            self.element.top = 0;
//            vertical = JCAlignStyleTop;
        } else if (index == 1) {
            self.element.centerY = boardHeight/2;
//            vertical = JCAlignStyleCenterY;
        } else if (index == 2) {
            self.element.bottom = boardHeight;
//            vertical = JCAlignStyleBottom;
        }
    }
//    JCAlignPosition p = {horizontal,vertical};
//    self.element.alignPosition =  p;
}

#pragma mark -- JCMoveViewDelegate
- (void)moveView:(JCMoveView *)moveView derection:(MoveDerection)derection {
    if (self.element.isLock) {
        JCPerformSelector(self.context.baseController, sel(@"alignNotAllow"));
        return;
    }
    CGFloat moveDistance = [XYTool getScreenNumberWithMM:distance_move_every_time];
    switch (derection) {
        case MoveTop:
        {
            self.element.top -= moveDistance;
        }break;
        case MoveRight:
        {
            self.element.left += moveDistance;
        }break;
        case MoveLeft:
        {
            self.element.left -= moveDistance;
        }break;
        case MoveBottom:
        {
            self.element.top += moveDistance;
        }break;
        default:
            break;
    }
}

#pragma mark -- lazy
- (JCSelectionView *)horizontalView {
    if (!_horizontalView) {
        _horizontalView = [[JCSelectionView alloc] initWithFrame:(CGRect){0,0,150,40} normalImageNames:@[@"horizontal_left_normal",@"horizontal_center_normal",@"horizontal_right_normal"] selectImageNames:@[@"horizontal_left_select",@"horizontal_center_select",@"horizontal_right_select"]];
        _horizontalView.delegate = self;
        _horizontalView.selectButtonHidden = YES;
    }
    return _horizontalView;
}

- (JCSelectionView *)verticalView {
    if (!_verticalView) {
        _verticalView = [[JCSelectionView alloc] initWithFrame:(CGRect){0,0,150,40} normalImageNames:@[@"vertical_top_normal",@"vertical_center_normal",@"vertical_bottom_normal"] selectImageNames:@[@"vertical_top_select",@"vertical_center_select",@"vertical_bottom_select"]];
        _verticalView.delegate = self;
        _verticalView.selectButtonHidden = YES;
    }
    return _verticalView;
}

- (JCMoveView *)moveView {
    if (!_moveView) {
        _moveView = [[JCMoveView alloc] initWithFrame:(CGRect){0,0,128,128}];
        _moveView.delegate = self;
    }
    return _moveView;
}


@end
