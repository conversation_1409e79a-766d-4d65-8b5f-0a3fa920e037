//
//  JCBorderCollectionCell.m
//  XYFrameWork
//
//  Created by xingling xu on 2020/11/23.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import "JCBorderCollectionCell.h"

@interface JCBorderCollectionCell ()
@property (nonatomic, strong) UIImageView *icon;
@end

@implementation JCBorderCollectionCell

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        self.layer.borderWidth = 1;
        self.layer.borderColor = XY_HEX_RGB(0xEBEBEB).CGColor;
        self.layer.cornerRadius = 10;
        [self addSubview:self.icon];
    }
    return self;
}

#pragma mark - setter
- (void)setObject:(JCIConObject *)object {
    _object = object;
    [self.icon jc_setImageWithUrl:object.imageUrl];
}

#pragma mark - lazy
- (UIImageView *)icon {
    if (!_icon) {
        _icon = [[UIImageView alloc] initWithFrame:(CGRect){10,10,[JCBorderCollectionCell width4Border]-20,[JCBorderCollectionCell height4Border]-20}];
        _icon.contentMode = UIViewContentModeScaleAspectFit;
    }
    return _icon;
}

#pragma mark - caculate
+ (CGFloat)width4Border {
    return (kSCREEN_WIDTH-48-15)/2;
}

+ (CGFloat)height4Border {
    return 60;
}
@end
