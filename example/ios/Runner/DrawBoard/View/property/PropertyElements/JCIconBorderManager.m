//
//  JCIconBorderManager.m
//  XYFrameWork
//
//  Created by xingling xu on 2020/11/26.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import "JCIconBorderManager.h"

#define JCIconTitleModels<PERSON>ey   @"JCIconTitleModelsKey"
#define JCIconBorderModelsKey  @"JCIconBorderModelsKey"

@interface JCIconBorderManager ()
@property (nonatomic, copy) NSArray *iconModels;
@property (nonatomic, copy) NSArray *borderModels;
@end

@implementation JCIconBorderManager
+ (id)sharedManager {
    static dispatch_once_t once;
    static id instance;
    dispatch_once(&once, ^{
        instance = [self new];
    });
    return instance;
}

- (void)requestIconAndBorderBaseInfo {
    [self requestIconItem:nil];
    [self requestBorderItem:nil];
}

- (void)requestIconItem:(void(^)(BOOL success))completion {
    // 获取图标小类
    [DCHTTPRequest getWithParams:@{@"type":@"2"} ModelType:[JCLogoCategoryModel class] url:JC_Icon_Category_Path Success:^(__kindof YTKBaseRequest * _Nonnull request, id  _Nonnull responseObject) {
        if ([responseObject isKindOfClass:[NSArray class]]) {
            [self saveIconTitles:responseObject];
        }
        if (completion) {
            completion(YES);
        }
    } failure:^(YTKBaseRequest * _Nullable request, NSError * _Nullable error) {
        if (completion) {
            completion(NO);
        }
    }];
}

- (void)requestBorderItem:(void(^)(BOOL success))completion {
    // 获取边框小类
    [DCHTTPRequest getWithParams:@{@"type":@"3"} ModelType:[JCLogoCategoryModel class] url:JC_Icon_Category_Path Success:^(__kindof YTKBaseRequest * _Nonnull request, id  _Nonnull responseObject) {
        if ([responseObject isKindOfClass:[NSArray class]]) {
            [self saveBorderTitles:responseObject];
        }
        if (completion) {
            completion(YES);
        }
    } failure:^(YTKBaseRequest * _Nullable request, NSError * _Nullable error) {
        if (completion) {
            completion(NO);
        }
    }];
}

// 图标
- (void)saveIconTitles:(NSArray *)array {
    if (!array || array.count == 0) return;
    self.iconModels = array;
    [[JCFMDB shareDatabase:DB_NAME] jc_deleteTable:TABLE_LogoTitleModels whereFormat:@"where industryId = '2'"];
    [[JCFMDB shareDatabase:DB_NAME] jc_insertTable:TABLE_LogoTitleModels dicOrModelArray:array];
}

// 边框
- (void)saveBorderTitles:(NSArray *)array {
    if (!array || array.count == 0) return;
    self.borderModels = array;
    [[JCFMDB shareDatabase:DB_NAME] jc_deleteTable:TABLE_LogoTitleModels whereFormat:@"where industryId = '3'"];
    [[JCFMDB shareDatabase:DB_NAME] jc_insertTable:TABLE_LogoTitleModels dicOrModelArray:array];
}

- (NSArray *)iconTitleModels {
    return self.iconModels;
}

- (NSArray *)borderTitleModels {
    return self.borderModels;
}

#pragma mark - get
- (NSArray *)iconModels {
    if (!_iconModels) {
        _iconModels = [NSArray array];
        NSArray *temp = [[JCFMDB shareDatabase:DB_NAME] jc_lookupTable:TABLE_LogoTitleModels dicOrModel:[JCLogoCategoryModel class] whereFormat:@"where industryId = '2'"];
        if (temp.count > 0) _iconModels = temp;
    }
    return _iconModels;
}

- (NSArray *)borderModels {
    if (!_borderModels) {
        _borderModels = [NSArray array];
        NSArray *temp = [[JCFMDB shareDatabase:DB_NAME] jc_lookupTable:TABLE_LogoTitleModels dicOrModel:[JCLogoCategoryModel class] whereFormat:@"where industryId = '3'"];
        if (temp.count > 0) _borderModels = temp;
    }
    return _borderModels;
}

@end
