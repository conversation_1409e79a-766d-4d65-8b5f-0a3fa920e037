//
//  JCFontSelectView.m
//  XYFrameWork
//
//  Created by xingling xu on 2020/11/13.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import "JCFontSelectView.h"
#import "JCFontManager.h"
#import "JCFontTableViewCell.h"
#import "JCFontModel.h"

@interface JCFontSelectView () <UITableViewDelegate,UITableViewDataSource>
@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) NSMutableArray<JCFontModel *> *datas;
@property (nonatomic, copy) NSString *selectFontCode;
@property (nonatomic, assign) NSInteger currentDownLoadCount;
@end

@implementation JCFontSelectView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self loadRootView];
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(downLoadPressNotification:) name:JCNOTICATION_DOWNLOADPRESS_ChenYin object:nil];
    }
    return self;
}

- (void)loadRootView {
    [self addSubview:self.tableView];
    [self requestFontData];
}

- (void)requestFontData {
    [[JCFontManager sharedManager] requestFontWithParams:@{@"status":@"1",@"type":@"1"} cacheInfo:^(NSArray * _Nonnull enFonts, NSArray * _Nonnull cnFonts) {
        [self reloadCollvWithEn:enFonts cn:cnFonts];
    } updateInfo:^(NSArray * _Nonnull enFonts, NSArray * _Nonnull cnFonts) {
        [self reloadCollvWithEn:enFonts cn:cnFonts];
    }];
}

- (void)reloadCollvWithEn:(NSArray *)en cn:(NSArray *)cn {
    NSMutableArray *temp = [NSMutableArray array];
    if (en.count > 0) {
        [temp addObjectsFromArray:en];
    }
    if (cn.count > 0) {
        [temp addObjectsFromArray:cn];
    }
    self.datas = temp;
    /** 标记已经下载的model */
    [self.datas each:^(JCFontModel *obj) {
        obj.hasDownLoad = [obj fontHasDownload]?YES:NO;
    }];

    [self sortDatas];
}

/** 根据下载先后对字体进行排序 */
- (void)sortDatas {
    [self.datas enumerateObjectsUsingBlock:^(JCFontModel *obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if (obj.hasDownLoad) {
//            obj.downloadTime = [JCFontModel dowloadTimeForCode:obj.fontCode];
            if (obj.lastUseTimeStamp.length == 0) {
                obj.lastUseTimeStamp = @"1";
            }
        }
    }];
    
    /** 对下载过的字体根据下载时间排序 */
    [self.datas sortUsingComparator:^NSComparisonResult(JCFontModel *obj1, JCFontModel *obj2) {
        return obj1.lastUseTimeStamp.integerValue < obj2.lastUseTimeStamp.integerValue;
    }];

[self makeSelectFontInTheFirstPosition];
//    [self.tableView endUpdates];
    [self.tableView reloadData];
    //滚动到最上面,因为选中的一定在最上面
//    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
//        [self.tableView scrollToRowAtIndexPath:[NSIndexPath indexPathForRow:0 inSection:0] atScrollPosition:UITableViewScrollPositionNone animated:YES];
//
//    });
}

- (void)currentFontVisible {
    [self makeSelectFontInTheFirstPosition];
    [self.tableView reloadData];
//    NSString *fontCode = [JCFontModel fontHasDownLoad:[JCElementEditManager getFontCode:self.element]]?[JCElementEditManager getFontCode:self.element]:text_default_font_code;
//    __block NSInteger __currentIndex = 0;
//    [self.datas enumerateObjectsUsingBlock:^(JCFontModel *obj, NSUInteger idx, BOOL * _Nonnull stop) {
//        if ([obj.fontCode isEqualToString:fontCode]) {
//            __currentIndex = idx;
//            *stop = YES;
//        }
//    }];
    [self.tableView scrollToRowAtIndexPath:[NSIndexPath indexPathForRow:0 inSection:0] atScrollPosition:UITableViewScrollPositionNone animated:false];
}
- (void)makeSelectFontInTheFirstPosition {
    //下面逻辑是为了将选中的字体排在第一位
    NSMutableArray *temArr = [self.datas mutableCopy];
    JCFontModel *model;
    for (NSInteger i = 0;i<self.datas.count;i++) {
        model = self.datas[i];
        if ([model.fontCode isEqualToString:self.selectFontCode]) {
            [temArr removeObjectAtIndex:i];
            break;
        }
    }
    [temArr insertObject:model atIndex:0];
    self.datas = temArr;//end
}
#pragma mark - down load font
- (void)downLoadFontRequestWithUrlString:(JCFontModel *)fontModel tag:(NSInteger)tag{
    NetWorkErrorReturn
    NSString *saveUrlString = [NSString stringWithFormat:@"%@/font/%@.%@",DocumentsFontPath,fontModel.fontCode,fontModel.url.pathExtension];
    fontModel.isDownLoading = YES;
    self.currentDownLoadCount++;
    [DCHTTPRequest downLoadFileWithParams:@{} UrlString:fontModel.url savePath:saveUrlString tagString:fontModel.fontCode Success:^(__kindof YTKBaseRequest * _Nonnull request, id  _Nonnull responseObject) {
        [JCFontModel saveTime:[XYTool getNowTimeTimestamp] fontCode:fontModel.fontCode];
        fontModel.hasDownLoad = YES;
        fontModel.isDownLoading = NO;
        [self.tableView reloadData];
        self.currentDownLoadCount--;
    } failure:^(YTKBaseRequest * _Nullable request, NSError * _Nullable error) {
        if (NETWORK_STATE_ERROR) {
            [DCHUDHelper showMessage:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
        }
        fontModel.isDownLoading = NO;
        fontModel.hasDownLoad = NO;
        [self.tableView reloadData];
        self.currentDownLoadCount--;
    } downloadBlock:^(double percent) {
        
    }];
}

#pragma mark - UITableView Delegate & DataSource
- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return 50;
}
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.datas.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    JCFontTableViewCell *cell = [JCFontTableViewCell cellWithTableView:tableView];
    JCFontModel *model = [self.datas safeObjectAtIndex:indexPath.row];
    cell.fontModel = model;
    cell.choosed = [model.fontCode isEqualToString:self.selectFontCode];
    cell.progress = [JCFontModel progressForCode:model.fontCode];
    return cell;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    JCFontModel *model = [self.datas safeObjectAtIndex:indexPath.row];
    if (model.hasDownLoad) {
        self.selectFontCode = model.fontCode;
        BeginUndoWithKey(k_fontcode, [JCElementEditManager getFontCode:self.element]);
        [JCElementEditManager editElement:self.element fontCode:self.selectFontCode];
        EndUndoWithKey(k_fontcode, model.fontCode);
        model.lastUseTimeStamp = [XYTool getNowTimeTimestamp];
        [[JCFontManager sharedManager] updateFontDBWithParam:model];
    } else {
        if (model.isDownLoading) return;
        if (self.currentDownLoadCount >= 3) {
            [DCHUDHelper showMessage:@"最多进行三个任务" hideAfterDelay:2];
            return;
        }
        [self downLoadFontRequestWithUrlString:model tag:(indexPath.row + indexPath.section * 1000)];
    }
    [self.tableView reloadData];
}


#pragma mark - lazy
- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:(CGRect){0,0,self.width,self.height} style:UITableViewStylePlain];
        _tableView.backgroundColor = [UIColor whiteColor];
        _tableView.showsVerticalScrollIndicator = NO;
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.delegate = self;
        _tableView.dataSource = self;
    }
    return _tableView;
}

- (NSString *)selectFontCode {
    if (!_selectFontCode) {
        _selectFontCode = text_default_font_code;
    }
    return _selectFontCode;
}

#pragma mark -- data refresh
- (void)setElement:(JCElementBaseView *)element {
    [super setElement:element];
    // load data
    NSString *fontCode = [JCFontModel fontHasDownLoad:[JCElementEditManager getFontCode:element]]?[JCElementEditManager getFontCode:element]:text_default_font_code;
    self.selectFontCode = fontCode;
    //选中画板元素之后,记录最近使用字体
    [[JCFontManager sharedManager] updateFontDBWithLastUseDate:[XYTool getNowTimeTimestamp] where:[NSString stringWithFormat:@"where fontCode = '%@'",fontCode]];
    [self currentFontVisible];
    NSLog(@"===============%p",self);
}

- (void)downLoadPressNotification:(NSNotification *)notification{
    NSArray *arr = notification.object;
    NSString *fontCode = [arr safeObjectAtIndex:0];// fontCode
    NSString *pressString = [arr safeObjectAtIndex:1];//progress
    NSString *typeDownloadState = [arr safeObjectAtIndex:2];// 1：成功 0：失败
    CGFloat progress = pressString.floatValue;
    [JCFontModel saveProgress:progress fontCode:fontCode];
    dispatch_async(dispatch_get_main_queue(), ^{
        [self.tableView reloadData];
    });
}
@end
