//
//  JCTextPropertyView.m
//  ChenYin
//
//  Created by xingling xu on 2020/11/12.
//  Copyright © 2020 JCChenYin. All rights reserved.
//

#import "JCTextStyleView.h"
//#import "JCFontSliderView.h"
#import "JCSelectionView.h"
#import "JCSliderView.h"

static const CGFloat align_btn_width = 51;
static const NSInteger lineSpaceIndexMax = 25;
static const NSInteger wordSpaceIndexMax = 25;
//行间距和字间距步长
static const CGFloat spaceStep = 0.2;
@interface JCTextStyleView () <JCSliderViewDelegate>
@property (nonatomic, strong) UIView *sizeView;
@property (nonatomic, strong) UIView *styleView;
@property (nonatomic, strong) UIView *spaceView;
//@property (nonatomic, strong) JCFontSliderView *sliderView;
@property (nonatomic, strong) JCSliderView *slider;
@property (nonatomic, strong) UIButton *fontDecreaseBtn;
@property (nonatomic, strong) UIButton *fontIncreaseBtn;
// style
@property (nonatomic, strong) UIButton *boldButton;
@property (nonatomic, strong) UIButton *underlineButton;
@property (nonatomic, strong) UIButton *italicButton;
@property (nonatomic, strong) JCSelectionView *selectionView;
// linespace
@property (nonatomic, strong) UIButton *lineSpaceDecreaseBtn;
@property (nonatomic, strong) UIButton *lineSpaceIncreaseBtn;
@property (nonatomic, strong) UILabel *lineSpaceLabel;
// word space
@property (nonatomic, strong) UIButton *wordSpaceDecreaseBtn;
@property (nonatomic, strong) UIButton *wordSpaceIncreaseBtn;
@property (nonatomic, strong) UILabel *wordSpaceLabel;

@property (nonatomic, copy) NSArray *lineSpaceValues;
@property (nonatomic, copy) NSArray *wordSpaceValues;
@property (nonatomic, assign) NSInteger lineSpaceIndex;
@property (nonatomic, assign) NSInteger wordSpaceIndex;
@end

@implementation JCTextStyleView
- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        self.lineSpaceIndex = 0;
        self.wordSpaceIndex = 0;
        [self addSubview:self.sizeView];
        [self addSubview:self.styleView];
        [self addSubview:self.spaceView];
        self.sizeView.top = 18;
        self.styleView.top = self.sizeView.bottom + 20;
        self.spaceView.top = self.styleView.bottom + 24;
        [self loadRootView];
    }
    return self;
}


- (void)loadRootView {
    [self loadSizeView];
    [self loadStyleView];
    [self loadSpaceView];
}

- (void)updateSubview {
    
}
- (void)setAccessoryType:(JCSliderAccessory)accessoryType{
    _accessoryType = accessoryType;
    self.slider.type = accessoryType;
}
//==========================================    Font Size
- (void)loadSizeView {
    UILabel *titleLabel = [self labelWithTitle:@"字号"];
    titleLabel.left = 20;
    [self.sizeView addSubview:titleLabel];
//    [self.sizeView addSubview:self.sliderView];
    [self.sizeView addSubview:self.slider];
}

- (void)refreshSizeView {
    
}
//==========================================    Font Style
- (void)loadStyleView {
    
    
    [self.styleView addSubview:self.boldButton];
    [self.styleView addSubview:self.underlineButton];
    [self.styleView addSubview:self.italicButton];
    [self.styleView addSubview:self.selectionView];
    
    self.boldButton.left = 20;
    self.underlineButton.left = self.boldButton.right + 10;
    self.italicButton.left = self.underlineButton.right + 10;
    self.selectionView.right = kSCREEN_WIDTH - 20;
    self.selectionView.top = 2;
}

- (void)refreshStyleView {
    
}
//==========================================    Font Space
- (void)loadSpaceView {
    UILabel *lineTitleLabel = [self labelWithTitle:@"行距"];
    lineTitleLabel.left = 20;
    [self.spaceView addSubview:lineTitleLabel];
    
    UILabel *wordTitleLabel = [self labelWithTitle:@"字距"];
    wordTitleLabel.left = kSCREEN_WIDTH - 172;
    [self.spaceView addSubview:wordTitleLabel];
    [self.spaceView addSubview:self.lineSpaceDecreaseBtn];
    [self.spaceView addSubview:self.lineSpaceIncreaseBtn];
    [self.spaceView addSubview:self.wordSpaceDecreaseBtn];
    [self.spaceView addSubview:self.wordSpaceIncreaseBtn];
    [self.spaceView addSubview:self.lineSpaceLabel];
    [self.spaceView addSubview:self.wordSpaceLabel];
    self.lineSpaceDecreaseBtn.top = self.lineSpaceIncreaseBtn.top = self.wordSpaceDecreaseBtn.top = self.wordSpaceIncreaseBtn.top = 28;
    self.lineSpaceDecreaseBtn.left = 20;
    self.lineSpaceIncreaseBtn.left = self.lineSpaceDecreaseBtn.right + 64;
    self.wordSpaceIncreaseBtn.right = SCREEN_WIDTH - 20;
    self.wordSpaceDecreaseBtn.right = self.wordSpaceIncreaseBtn.left - 64;
    self.lineSpaceLabel.centerY = self.wordSpaceLabel.centerY = self.lineSpaceDecreaseBtn.centerY;
    self.lineSpaceLabel.left = self.lineSpaceDecreaseBtn.right;
    self.wordSpaceLabel.left = self.wordSpaceDecreaseBtn.right;
    
}

- (void)refreshSpaceView {
    
}

#pragma mark - common
- (UILabel *)labelWithTitle:(NSString *)title {
    UILabel *label = [[UILabel alloc] initWithFrame:(CGRect){0,0,30,20}];
    label.font = MY_FONT_Regular(14);
    label.textColor = XY_HEX_RGB(0x595959);
    label.text = title;
    label.textAlignment = NSTextAlignmentLeft;
    return label;
}

#pragma mark -- btn selector
/** 粗体 */
- (void)boldButtonClick:(UIButton *)btn {
    BeginUndoWithKey(k_bold, self.boldButton.selected?@"1":@"0");
    //-----------------------------------------------------------------------------------实际操作
    self.boldButton.selected = !self.boldButton.selected;
    [JCElementEditManager editElement:self.element bold:self.boldButton.selected];
    //------------------------------------------------------------------------------------------
    EndUndoWithKey(k_bold, self.boldButton.selected?@"1":@"0")
}

/** 下划线 */
- (void)underlineButtonClick:(UIButton *)btn {
    BeginUndoWithKey(k_underline, self.underlineButton.selected?@"1":@"0")
    //-----------------------------------------------------------------------------------实际操作
    self.underlineButton.selected = !self.underlineButton.selected;
    [JCElementEditManager editElement:self.element underLine:self.underlineButton.selected];
    //------------------------------------------------------------------------------------------
    EndUndoWithKey(k_underline, self.underlineButton.selected?@"1":@"0");
}

/** 斜体 */
- (void)italicButtonClick:(UIButton *)btn {
    BeginUndoWithKey(k_italic, self.italicButton.selected?@"1":@"0");
    //-----------------------------------------------------------------------------------实际操作
    self.italicButton.selected = !self.italicButton.selected;
    [JCElementEditManager editElement:self.element italic:self.italicButton.selected];
    //------------------------------------------------------------------------------------------
    EndUndoWithKey(k_italic, self.italicButton.selected?@"1":@"0");
}

- (void)decreaseLineSpace:(UIButton *)btn {
    if (self.lineSpaceIndex == 0) return;
    self.lineSpaceIndex--;
    [self editLineSpaceStartIndex:self.lineSpaceIndex+1 endIndex:self.lineSpaceIndex];
    [self refreshButtonState];
}

- (void)increaseLineSpace:(UIButton *)btn {
    if (self.lineSpaceIndex == lineSpaceIndexMax) return;
    self.lineSpaceIndex++;
    [self editLineSpaceStartIndex:self.lineSpaceIndex-1 endIndex:self.lineSpaceIndex];
    [self refreshButtonState];
}

- (void)decreaseFontsize:(UIButton *)btn {
    
}
- (void)increaseFontSize:(UIButton *)btn {
    
}

- (void)decreaseWordSpace:(UIButton *)btn {
    if (self.wordSpaceIndex == 0) return;
    self.wordSpaceIndex--;
    [self editWordSpaceStartIndex:self.wordSpaceIndex+1 endIndex:self.wordSpaceIndex];
    [self refreshButtonState];
}

- (void)increaseWordSpace:(UIButton *)btn {
    if (self.wordSpaceIndex == wordSpaceIndexMax) return;
    self.wordSpaceIndex++;
    [self editWordSpaceStartIndex:self.wordSpaceIndex-1 endIndex:self.wordSpaceIndex];
    [self refreshButtonState];
}

- (void)editLineSpaceStartIndex:(NSInteger)orignalIndex endIndex:(NSInteger)endIndex {
    BeginUndoWithKey(k_linespace, [self.lineSpaceValues safeObjectAtIndex:orignalIndex]);
    [JCElementEditManager editElement:self.element lineSpace:[[self.lineSpaceValues safeObjectAtIndex:endIndex] floatValue]];
    EndUndoWithKey(k_linespace, [self.lineSpaceValues safeObjectAtIndex:endIndex]);
}

- (void)editWordSpaceStartIndex:(NSInteger)orignalIndex endIndex:(NSInteger)endIndex {
    BeginUndoWithKey(k_wordspace, [self.wordSpaceValues safeObjectAtIndex:orignalIndex]);
    [JCElementEditManager editElement:self.element textSpace:[[self.lineSpaceValues safeObjectAtIndex:endIndex] floatValue]];
    EndUndoWithKey(k_wordspace, [self.wordSpaceValues safeObjectAtIndex:endIndex]);
}

- (void)refreshButtonState {
    CGFloat lineS = [JCElementEditManager getEementLineSpace:self.element];
    CGFloat wordS = [JCElementEditManager getEementTextSpace:self.element];
    self.lineSpaceLabel.text = [NSString stringWithFormat:@"%.1f",lineS];
    self.wordSpaceLabel.text = [NSString stringWithFormat:@"%.1f",wordS];
    self.lineSpaceDecreaseBtn.enabled = lineS > 0;
    self.lineSpaceIncreaseBtn.enabled = lineS < 5;
    self.wordSpaceDecreaseBtn.enabled = wordS > 0;
    self.wordSpaceIncreaseBtn.enabled = wordS < 5;

}


#pragma mark -- JCSelectionViewDelegate  选择文本对齐格式
- (void)selectionView:(JCSelectionView *)selectionView selectIndex:(NSInteger)index {
    BeginUndoWithKey(k_textalignment, ([NSString stringWithFormat:@"%ld",[JCElementEditManager getElementTextAlignment:self.element]]));
    //-----------------------------------------------------------------------------------实际操作
    [JCElementEditManager editElement:self.element textAlignment:index];
    //------------------------------------------------------------------------------------------
    EndUndoWithKey(k_textalignment, ([NSString stringWithFormat:@"%ld",index]))
}

#pragma mark -- JCSliderViewDelegate
- (void)sliderView:(JCSliderView *)sliderView selectIndex:(NSInteger)index  selectValue:(NSString *)value {
    CGFloat mmFont = [[[JCFontSize mmFontSizes] safeObjectAtIndex:index] floatValue];
    [JCElementEditManager editElement:self.element fontSize_mm:mmFont];
}

/** 修改之前的字体 */
- (void)sliderView:(JCSliderView *)sliderView selectIndex:(NSInteger)index  valueChangeBegin:(NSString *)beginValue {
    BeginUndoWithKey(k_font, [[JCFontSize mmFontSizes] safeObjectAtIndex:index])
}
/** 修改之后的字体 */
- (void)sliderView:(JCSliderView *)sliderView selectIndex:(NSInteger)index  valueChangeEnd:(NSString *)endValue {
    EndUndoWithKey(k_font, [[JCFontSize mmFontSizes] safeObjectAtIndex:index])
}

#pragma mark - getter
- (UIView *)sizeView {
    if (!_sizeView) {
        _sizeView = [[UIView alloc] initWithFrame:(CGRect){0,18,kSCREEN_WIDTH,72}];
    }
    return _sizeView;
}

- (UIView *)styleView {
    if (!_styleView) {
        _styleView = [[UIView alloc] initWithFrame:(CGRect){0,0,kSCREEN_WIDTH,44}];
    }
    return _styleView;
}

- (UIView *)spaceView {
    if (!_spaceView) {
        _spaceView = [[UIView alloc] initWithFrame:(CGRect){0,0,kSCREEN_WIDTH,72}];
    }
    return _spaceView;
}

- (UIButton *)boldButton {
    if (!_boldButton) {
        _boldButton = [self customButtonWithImageName:@"text_bold"];
        [_boldButton addTarget:self action:@selector(boldButtonClick:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _boldButton;
}

- (UIButton *)underlineButton {
    if (!_underlineButton) {
        _underlineButton = [self customButtonWithImageName:@"text_underline"];
        [_underlineButton addTarget:self action:@selector(underlineButtonClick:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _underlineButton;
}

- (UIButton *)italicButton {
    if (!_italicButton) {
        _italicButton = [self customButtonWithImageName:@"text_italic"];
        [_italicButton addTarget:self action:@selector(italicButtonClick:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _italicButton;
}

- (UIButton *)lineSpaceDecreaseBtn {
    if (!_lineSpaceDecreaseBtn) {
        _lineSpaceDecreaseBtn = [self customButtonWithImageName:@"decrease_action"];
        [_lineSpaceDecreaseBtn addTarget:self action:@selector(decreaseLineSpace:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _lineSpaceDecreaseBtn;
}

- (UIButton *)lineSpaceIncreaseBtn {
    if (!_lineSpaceIncreaseBtn) {
        _lineSpaceIncreaseBtn = [self customButtonWithImageName:@"increase_action"];
        [_lineSpaceIncreaseBtn addTarget:self action:@selector(increaseLineSpace:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _lineSpaceIncreaseBtn;
}

- (UIButton *)fontDecreaseBtn {
    if (!_fontDecreaseBtn) {
        _fontDecreaseBtn = [self customButtonWithImageName:@"decrease_action"];
        [_fontDecreaseBtn addTarget:self action:@selector(decreaseFontsize:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _fontDecreaseBtn;
}

- (UIButton *)fontIncreaseBtn {
    if (!_fontIncreaseBtn) {
        _fontIncreaseBtn = [self customButtonWithImageName:@"decrease_action"];
        [_fontIncreaseBtn addTarget:self action:@selector(increaseFontSize:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _fontIncreaseBtn;
}


- (UIButton *)wordSpaceDecreaseBtn {
    if (!_wordSpaceDecreaseBtn) {
        _wordSpaceDecreaseBtn = [self customButtonWithImageName:@"decrease_action"];
        [_wordSpaceDecreaseBtn addTarget:self action:@selector(decreaseWordSpace:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _wordSpaceDecreaseBtn;
}

- (UIButton *)wordSpaceIncreaseBtn {
    if (!_wordSpaceIncreaseBtn) {
        _wordSpaceIncreaseBtn = [self customButtonWithImageName:@"increase_action"];
        [_wordSpaceIncreaseBtn addTarget:self action:@selector(increaseWordSpace:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _wordSpaceIncreaseBtn;
}

- (UILabel *)lineSpaceLabel {
    if (!_lineSpaceLabel) {
        _lineSpaceLabel = [[UILabel alloc] initWithFrame:(CGRect){0,0,64,24}];
        _lineSpaceLabel.font = MY_FONT_Bold(16);
        _lineSpaceLabel.textColor = XY_HEX_RGB(0x262626);
        _lineSpaceLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _lineSpaceLabel;
}

- (UILabel *)wordSpaceLabel {
    if (!_wordSpaceLabel) {
        _wordSpaceLabel = [[UILabel alloc] initWithFrame:(CGRect){0,0,64,24}];
        _wordSpaceLabel.font = MY_FONT_Bold(16);
        _wordSpaceLabel.textColor = XY_HEX_RGB(0x262626);
        _wordSpaceLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _wordSpaceLabel;
}

- (UIButton *)customButtonWithImageName:(NSString *)imageName {
    UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
    button.layer.cornerRadius = 10;
    button.layer.masksToBounds = YES;
    button.size = (CGSize){44,44};
    [button setBackgroundImage:[UIImage imageWithColor:XY_HEX_RGB(0xf5f5f5)] forState:UIControlStateNormal];
    [button setBackgroundImage:[UIImage imageWithColor:XY_HEX_RGB(0xFFECEB)] forState:UIControlStateSelected];
    [button setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@_normal",imageName]] forState:UIControlStateNormal];
    [button setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@_selected",imageName]] forState:UIControlStateSelected];
    [button setImage:[UIImage imageNamed:[NSString stringWithFormat:@"%@_unable",imageName]] forState:UIControlStateDisabled];
    return button;
}


- (JCSelectionView *)selectionView {
    if (!_selectionView) {
        _selectionView = [[JCSelectionView alloc] initWithFrame:(CGRect){0,0,align_btn_width*3,40} normalImageNames:@[@"text_alignleft_normal",@"text_aligncenter_normal",@"text_aligncenter_normal"] selectImageNames:@[@"text_alignleft_selected",@"text_aligncenter_selected",@"text_alignright_selected"]];
        _selectionView.delegate = self;
    }
    return _selectionView;
}

- (NSArray *)lineSpaceValues {
    if (!_lineSpaceValues) {
        NSMutableArray *temp = [NSMutableArray arrayWithCapacity:26];
        for (float i = 0; i <= 5.0; i += spaceStep) {
            [temp addObject:[NSString stringWithFormat:@"%.1f",i]];
        }
        _lineSpaceValues = temp.copy;
    }
    return _lineSpaceValues;
}

- (NSArray *)wordSpaceValues {
    if (!_wordSpaceValues) {
        NSMutableArray *temp = [NSMutableArray arrayWithCapacity:26];
        for (float i = 0; i <= 5.0; i += spaceStep) {
            [temp addObject:[NSString stringWithFormat:@"%.1f",i]];
        }
        _wordSpaceValues = temp.copy;
    }
    return _wordSpaceValues;
}

- (JCSliderView *)slider {
    if (!_slider) {
        NSArray *target;
        if([XY_JC_LANGUAGE isEqualToString:@"zh-cn"]){
            target = [JCFontSize cnFontSizes];
        }else{
            target = [JCFontSize ptFontSizes];
        }
        _slider = [[JCSliderView alloc] initWithFrame:(CGRect){20,28,kSCREEN_WIDTH-40,44} values:target type:JCSliderAccessoryButton];
        _slider.delegate = self;
    }
    return _slider;
}

#pragma mark -- data refresh
- (void)setElement:(JCElementBaseView *)element {
    [super setElement:element];
    // load data
    self.boldButton.selected = [JCElementEditManager elementIsBold:element];
    self.italicButton.selected = [JCElementEditManager elementIsItalic:element];
    self.underlineButton.selected = [JCElementEditManager elementIsUnderLine:element];
    self.selectionView.selectIndex = [JCElementEditManager getElementTextAlignment:element];
    self.lineSpaceLabel.text = [NSString stringWithFormat:@"%.1f",[JCElementEditManager getEementLineSpace:element]];
    self.wordSpaceLabel.text = [NSString stringWithFormat:@"%.1f",[JCElementEditManager getEementTextSpace:element]];
    // font
    NSString *mmFont = [NSString stringWithFormat:@"%.1f",[JCElementEditManager getElementFont:element]];
    NSArray *mmFonts = [JCFontSize mmFontSizes];
    NSInteger index = [mmFonts indexOfObject:mmFont];
    NSArray *target;
    if([XY_JC_LANGUAGE isEqualToString:@"zh-cn"]){
        target = [JCFontSize cnFontSizes];
    }else{
        target = [JCFontSize ptFontSizes];
    }
//    NSLog(@"%lf=======%lf",[JCElementEditManager getEementLineSpace:self.element],[JCElementEditManager getEementTextSpace:self.element]);
//    NSLog(@"%@",self.lineSpaceValues);
    
    //此处会存在浮点数精度问题,不能直接使用浮点数做除法,先转成整数再做除法
    NSString *lineS = [NSString stringWithFormat:@"%.0f", 10 *[JCElementEditManager getEementLineSpace:self.element]];
    NSString *wordS = [NSString stringWithFormat:@"%.0f",10 *[JCElementEditManager getEementTextSpace:self.element]];
    self.lineSpaceIndex = [lineS integerValue] / (spaceStep * 10);
    self.wordSpaceIndex = [wordS integerValue] / (spaceStep * 10);
//    NSLog(@"%ld======%ld",self.lineSpaceIndex,self.wordSpaceIndex);
    [self.slider selectValue:[target safeObjectAtIndex:index] animated:NO];
    [self refreshButtonState];
}
@end
