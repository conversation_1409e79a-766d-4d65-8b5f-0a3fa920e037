//
//  JCDateFormatView.m
//  XYFrameWork
//
//  Created by xingling xu on 2019/12/10.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCDateFormatView.h"
#import "JCStyleButton.h"
#import "JCDatePicker.h"
#import "JCNormalPicker.h"
#import "NSDate+XHExtension.h"

@interface JCDateFormatView ()
@property (nonatomic, strong) JCStyleButton *dateValueBtn;
@property (nonatomic, strong) JCStyleButton *timeValueBtn;
//@property (nonatomic, strong) JCStyleButton *dateFormatBtn;
//@property (nonatomic, strong) JCStyleButton *timeFormatBtn;
@property(nonatomic,strong)JCNormalPicker *dateFormatpicker;
@property(nonatomic,strong)JCNormalPicker *timeFormatpicker;

@property(nonatomic,copy)NSArray *dateFormats;
@property(nonatomic,copy)NSArray *timeFormats;
@end

#define LeftSpace 20
#define Spacing 24

#define PickerLeft 12
#define PickerSpacing 8
@implementation JCDateFormatView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setUp];
    }
    return self;
}

- (void)setUp {
    [self addSubview:self.dateValueBtn];
    [self addSubview:self.timeValueBtn];
//    [self addSubview:self.dateFormatBtn];
//    [self addSubview:self.timeFormatBtn];
    [self addSubview:self.dateFormatpicker];
    [self addSubview:self.timeFormatpicker];
}

- (void)layoutSubviews {
    [super layoutSubviews];
    self.dateValueBtn.left = LeftSpace;
    self.dateValueBtn.centerY = 48;
    self.timeValueBtn.right = SCREEN_WIDTH - LeftSpace;
    self.timeValueBtn.centerY = 48;
//    self.dateFormatBtn.left = 24;
//    self.dateFormatBtn.centerY = 112;
//    self.timeFormatBtn.right = SCREEN_WIDTH - LeftSpace;
//    self.timeFormatBtn.centerY = 112;
    
    self.dateFormatpicker.top = self.timeValueBtn.bottom + 20;
    self.dateFormatpicker.left = PickerLeft;
    
    self.timeFormatpicker.top = self.dateFormatpicker.top;
    self.timeFormatpicker.right = SCREEN_WIDTH -PickerLeft;
    
}

#pragma mark -- btn selector
- (void)showDatePicker:(UIButton *)btn {
    JCElementTime *dateElement = (JCElementTime *)self.element;
    JCDatePickerMode datePickerModel;
    if (btn.tag == 0) {
        datePickerModel = JCDatePickerModeYearMonthDay;
    }else{
        datePickerModel = JCDatePickerModeHourMinuteSeconds;
    }
    
    [JCDatePicker showDate:[self checkDateFormatNone] mode:datePickerModel complete:^(NSDate *date) {
        [JCElementEditManager editElement:self.element date:date];
        if (btn.tag == 0) {
            NSString *dateFormatt = [JCElementEditManager getEementDateFormatt:dateElement];
            if (STR_IS_NIL(dateFormatt)) {
                // 如果用户直接选择时间，而且当前时间格式为:"无" , 默认打到@"yyyy年MM月dd日" 格式上
                NSString *dateFormatt = [XY_JC_LANGUAGE isEqualToString:@"zh-cn"]?@"yyyy年MM月dd日":@"dd-MM-yyyy";;
                [JCElementEditManager editElement:self.element dateFormatt:dateFormatt];
                self.dateFormatpicker.defaultSelectedIndex = 3;
            }
            [self.dateValueBtn setTitle:[JCElementEditManager getEementDateValue:self.element] forState:UIControlStateNormal];
            
        }else{
            NSString *dateFormatt = [JCElementEditManager getEementTimeFormatt:dateElement];
            if (STR_IS_NIL(dateFormatt)) {
                // 如果用户直接选择时间，而且当前时间格式为:"无" , 默认打到@"yyyy年MM月dd日" 格式上
                NSString *dateFormatt = [XY_JC_LANGUAGE isEqualToString:@"zh-cn"]?@"HH:mm":@"HH:mm";;
                [JCElementEditManager editElement:self.element timeFormatt:dateFormatt];
                self.timeFormatpicker.defaultSelectedIndex = 2;
            }
            [self.timeValueBtn setTitle:[JCElementEditManager getEementTimeValue:self.element] forState:UIControlStateNormal];
        }
    }];
}

- (NSDate *)checkDateFormatNone {
    JCElementTime *timeElement = (JCElementTime *)self.element;
//    NSString *dateFormatt = [JCElementEditManager getEementDateFormatt:timeElement];
    NSDate *currentDate = timeElement.timeConfigure.date; // 之前选择的日期
//    if (STR_IS_NIL(dateFormatt)) currentDate = [currentDate date_YMD_useNow];
    [JCElementEditManager editElement:self.element date:currentDate];
    return currentDate;
}

#pragma mark -- lazy

- (NSArray *)dateFormats {
    if (!_dateFormats) {
        _dateFormats = @[@"yyyy-MM-dd",@"yyyy-MM",@"MM-dd",@"yyyy年MM月dd日",@"yyyy年MM月",@"MM月dd日",@"yyyy/MM/dd",@"yyyy/MM",@"MM/dd",@"MM-dd-yyyy",@"dd-MM-yyyy",@"dd/MM/yyyy",XY_LANGUAGE_TITLE_NAMED(@"app00144", @"无")];
    }
    return _dateFormats;
}
- (NSArray *)timeFormats {
    if (!_timeFormats) {
        _timeFormats = @[XY_LANGUAGE_TITLE_NAMED(@"app00144", @"无"),/*@"HH时mm分ss秒",@"HH时mm分",@"mm分ss秒",*/@"HH:mm:ss",@"HH:mm",@"mm:ss"/*,@"HH-mm-ss",@"HH-mm",@"mm-ss"*/];
    }
    return _timeFormats;
}
- (JCStyleButton *)dateValueBtn {
    if (!_dateValueBtn) {
        _dateValueBtn = [[JCStyleButton alloc] initWithFrame:(CGRect){0,0,(SCREEN_WIDTH-LeftSpace*2-Spacing)/2,40}];
        _dateValueBtn.tag = 0;
        [_dateValueBtn.titleLabel setFont:MY_FONT_Medium(14)];
        [_dateValueBtn addTarget:self action:@selector(showDatePicker:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _dateValueBtn;
}

- (JCStyleButton *)timeValueBtn {
    if (!_timeValueBtn) {
        _timeValueBtn = [[JCStyleButton alloc] initWithFrame:(CGRect){0,0,(SCREEN_WIDTH-LeftSpace*2-Spacing)/2,40}];
        _timeValueBtn.tag = 1;
        [_timeValueBtn.titleLabel setFont:MY_FONT_Medium(14)];
        [_timeValueBtn addTarget:self action:@selector(showDatePicker:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _timeValueBtn;
}

- (JCNormalPicker *)dateFormatpicker {
    if (!_dateFormatpicker) {
        _dateFormatpicker =  [JCNormalPicker pickerWithFrame:CGRectMake(0, 0, (SCREEN_WIDTH-PickerLeft*2-PickerSpacing) * 0.5, 200) dataSource:self.dateFormats selectValue:[JCElementEditManager getEementDateFormatt:self.element] complete:^(NSNumber *selecIndex) {
            NSString *formatt = [self.dateFormats safeObjectAtIndex:selecIndex.integerValue];
            if ([formatt isEqualToString:XY_LANGUAGE_TITLE_NAMED(@"app00144", @"无")]) {
                formatt = @"";
            }
            [JCElementEditManager editElement:self.element dateFormatt:formatt];
            [self checkDateFormatNone];
            //        [[NSUserDefaults standardUserDefaults] setValue:formatt forKey:DateFormat];
            //        [[NSUserDefaults standardUserDefaults] synchronize];
            
            if ([formatt isEqualToString:@""]) {
                [self.dateValueBtn setTitle:XY_LANGUAGE_TITLE_NAMED(@"app00144", @"无") forState:UIControlStateNormal];
            }else{
                [self.dateValueBtn setTitle:[JCElementEditManager getEementDateValue:self.element] forState:UIControlStateNormal];
            }
        }];
        _dateFormatpicker.defaultSelectedIndex = 3;
    }
    return _dateFormatpicker;
}

- (JCNormalPicker *)timeFormatpicker {
    if (!_timeFormatpicker) {
        _timeFormatpicker = [JCNormalPicker pickerWithFrame:CGRectMake(0, 0, (SCREEN_WIDTH-PickerLeft*2-PickerSpacing) * 0.5, 200) dataSource:self.timeFormats selectValue:[JCElementEditManager getEementDateFormatt:self.element] complete:^(NSNumber *selecIndex) {
            NSString *formatt = [self.timeFormats safeObjectAtIndex:selecIndex.integerValue];
            if ([formatt isEqualToString:XY_LANGUAGE_TITLE_NAMED(@"app00144", @"无")]) {
                formatt = @"";
            }
            [JCElementEditManager editElement:self.element timeFormatt:formatt];
            [self checkDateFormatNone];
            //        [[NSUserDefaults standardUserDefaults] setValue:formatt forKey:DateFormat];
            //        [[NSUserDefaults standardUserDefaults] synchronize];
            if ([formatt isEqualToString:@""]) {
                [self.timeValueBtn setTitle:XY_LANGUAGE_TITLE_NAMED(@"app00144", @"无") forState:UIControlStateNormal];
            }else{
                [self.timeValueBtn setTitle:[JCElementEditManager getEementTimeValue:self.element] forState:UIControlStateNormal];
            }
        }];
        _timeFormatpicker.defaultSelectedIndex = 0;
    }
    return _timeFormatpicker;
}
#pragma mark -- refresh
- (void)setElement:(JCElementBaseView *)element {
    [super setElement:element];
    NSString *dateValue = [JCElementEditManager getEementDateValue:element];
    if (STR_IS_NIL(dateValue)) {
        dateValue = XY_LANGUAGE_TITLE_NAMED(@"app00144", @"无");
    }
    [self.dateValueBtn setTitle:dateValue forState:UIControlStateNormal];
    
    NSString *timeValue = [JCElementEditManager getEementTimeValue:element];
    if (STR_IS_NIL(timeValue)) {
        timeValue = XY_LANGUAGE_TITLE_NAMED(@"app00144", @"无");
    }
    [self.timeValueBtn setTitle:timeValue forState:UIControlStateNormal];
    
    
    NSString *format = [JCElementEditManager getEementDateFormatt:element];
    if (STR_IS_NIL(format) || [format isEqualToString:@"无"]) {
        format = XY_LANGUAGE_TITLE_NAMED(@"app00144", @"无");
    }
    for (NSInteger i = 0; i<self.dateFormats.count; i++) {
        if ([format isEqualToString:self.dateFormats[i]]) {
            self.dateFormatpicker.defaultSelectedIndex = i;
            break;
        }
    }
//    [self.dateFormatBtn setTitle:format forState:UIControlStateNormal];
    
    NSString *timeFormat = [JCElementEditManager getEementTimeFormatt:element];
    if (STR_IS_NIL(timeFormat) || [timeFormat isEqualToString:@"无"]) {
        timeFormat = XY_LANGUAGE_TITLE_NAMED(@"app00144", @"无");
    }
    for (NSInteger i = 0; i<self.timeFormats.count; i++) {
        if ([timeFormat isEqualToString:self.timeFormats[i]]) {
            self.timeFormatpicker.defaultSelectedIndex = i;
            break;
        }
    }
//    [self.timeValueBtn setTitle:timeFormat forState:UIControlStateNormal];
}


@end
