//
//  JCIConObject.h
//  XYFrameWork
//
//  Created by xingling xu on 2020/11/17.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import <Foundation/Foundation.h>
#import "XYBaseModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface JCIConObject : XYBaseModel
@property (nonatomic, copy) NSString<Optional> *imageUrl;
@property (nonatomic, copy) NSString<Optional> *idStr;
@property (nonatomic, copy) NSString<Optional> *materialIndustryId;
@property (nonatomic, copy) NSString<Optional> *materialIndustryName;
@property (nonatomic, copy) NSString<Optional> *materialCategoryId;
@property (nonatomic, copy) NSString<Optional> *materialCategoryName;
@property (nonatomic, copy) NSString<Optional> *createTime;
@property (nonatomic, copy) NSString<Optional> *orderNo;
@property (nonatomic, copy) NSString<Optional> *userId;
// 当前userId使用的时间
@property (nonatomic, copy) NSString<Optional> *usedTime;

- (void)appenUserId:(NSString *)userId;

- (NSString *)usedTime4:(NSString *)user;

@end

NS_ASSUME_NONNULL_END
