//
//  JCIConObject.m
//  XYFrameWork
//
//  Created by xingling xu on 2020/11/17.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import "JCIConObject.h"

@implementation JCIConObject

+(JSONKeyMapper *)keyMapper
{
    return [[JSONKeyMapper alloc] initWithModelToJSONDictionary:@{@"idStr":@"id",@"imageUrl":@"image"}];
}

- (void)appenUserId:(NSString *)newUserId {
    NSString *time = [XYTool getNowTimeTimestamp];
    
    NSData *jsonData = [self.userId dataUsingEncoding:NSUTF8StringEncoding];
    NSDictionary *dic = [NSJSONSerialization JSONObjectWithData:jsonData options:NSJSONReadingMutableContainers error:nil];
    if (!dic) {
        dic = [NSDictionary dictionary];
    }
    NSMutableDictionary *temp = [NSMutableDictionary dictionaryWithDictionary:dic];
    [temp setObject:time forKey:[NSString stringWithFormat:@"*%@*",newUserId]];
    
    NSData *jsonD = [NSJSONSerialization dataWithJSONObject:temp options:NSJSONWritingPrettyPrinted error:nil];
    NSString * str = [[NSString alloc] initWithData:jsonD encoding:NSUTF8StringEncoding];
    if (str && str.length > 0) {
        self.userId = str;
    }
}

- (NSString *)usedTime4:(NSString *)user {
    NSData *jsonData = [self.userId dataUsingEncoding:NSUTF8StringEncoding];
    NSDictionary *dic = [NSJSONSerialization JSONObjectWithData:jsonData options:NSJSONReadingMutableContainers error:nil];
    NSString *time = dic[[NSString stringWithFormat:@"*%@*",user]];
    if (time && time.length > 0) {
        return time;
    }
    return @"";
}

@end
