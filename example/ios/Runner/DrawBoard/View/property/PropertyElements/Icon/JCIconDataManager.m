//
//  JCIconDataManager.m
//  XYFrameWork
//
//  Created by xingling xu on 2020/12/3.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import "JCIconDataManager.h"

@implementation JCIconDataManager

+ (id)sharedManager {
    static dispatch_once_t once;
    static id instance;
    dispatch_once(&once, ^{
        instance = [self new];
    });
    return instance;
}

- (void)saveDatas:(NSArray<JCIConObject *> *)array {
    [array enumerateObjectsUsingBlock:^(JCIConObject * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        NSString *where = [NSString stringWithFormat:@"where materialIndustryId = '%@' and materialCategoryId = '%@' and idStr = '%@'",obj.materialIndustryId,obj.materialCategoryId,obj.idStr];
        [[JCFMDB shareDatabase:DB_NAME] jc_inDatabase:^{
            NSArray *temp = [[JCFMDB shareDatabase:DB_NAME] jc_lookupTable:TABLE_CYLogoModel dicOrModel:[JCIConObject class] whereFormat:where];
            if (temp.count > 0) {
                JCIConObject *orignalObject = temp.firstObject;
                if (orignalObject) {
                    obj.userId = orignalObject.userId;
                    obj.usedTime = orignalObject.usedTime;
                }
                [[JCFMDB shareDatabase:DB_NAME] jc_updateTable:TABLE_CYLogoModel dicOrModel:obj whereFormat:where];
            } else {
                [[JCFMDB shareDatabase:DB_NAME] jc_insertTable:TABLE_CYLogoModel dicOrModel:obj];
            }
        }];
    }];
}


- (NSArray *)getIconsWith:(NSString *)industyId smallId:(NSString *)smallId {
    NSString *where = [NSString stringWithFormat:@"where materialIndustryId = '%@' and materialCategoryId = '%@'",industyId,smallId];
    NSArray *temp = [[JCFMDB shareDatabase:DB_NAME] jc_lookupTable:TABLE_CYLogoModel dicOrModel:[JCIConObject class] whereFormat:where];
    return temp;
}

- (NSArray *)getIconsWith:(NSString *)industyId userId:(NSString *)userId {
    NSString *where = [NSString stringWithFormat:@"where materialIndustryId = '%@' and userId like '%%*%@*%%'",industyId,userId];
    NSArray *temp = [[JCFMDB shareDatabase:DB_NAME] jc_lookupTable:TABLE_CYLogoModel dicOrModel:[JCIConObject class] whereFormat:where];
    [temp enumerateObjectsUsingBlock:^(JCIConObject *obj, NSUInteger idx, BOOL * _Nonnull stop) {
        obj.usedTime = [obj usedTime4:userId];
    }];
    // 排序
    NSSortDescriptor*sorter=[[NSSortDescriptor alloc]initWithKey:@"usedTime" ascending:NO];
    NSMutableArray *sortDescriptors=[[NSMutableArray alloc]initWithObjects:&sorter count:1];
    NSArray *sortArray=[temp sortedArrayUsingDescriptors:sortDescriptors];
    return sortArray;
}

- (void)user:(NSString *)userId used:(JCIConObject *)icon {
    [icon appenUserId:userId];
    NSString *where = [NSString stringWithFormat:@"where materialIndustryId = '%@' and materialCategoryId = '%@' and idStr = '%@'",icon.materialIndustryId,icon.materialCategoryId,icon.idStr];
    BOOL use = [[JCFMDB shareDatabase:DB_NAME] jc_updateTable:TABLE_CYLogoModel dicOrModel:icon whereFormat:where];
    NSString *value = use?@"使用保存成功":@"失败";
    NSLog(@"save is %@",value);
}
@end
