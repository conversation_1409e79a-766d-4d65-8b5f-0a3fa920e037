//
//  JCIconDataManager.h
//  XYFrameWork
//
//  Created by xingling xu on 2020/12/3.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import <Foundation/Foundation.h>
#import "JCIConObject.h"

NS_ASSUME_NONNULL_BEGIN

@interface JCIconDataManager : NSObject

+ (id)sharedManager;

// 保存图标、边框等素材
- (void)saveDatas:(NSArray<JCIConObject *> *)array;
// 根据大小类获取图标、边框等素材
- (NSArray *)getIconsWith:(NSString *)industyId smallId:(NSString *)smallId;
// 根据大小类获取用户使用过的图标、边框等素材
- (NSArray *)getIconsWith:(NSString *)industyId userId:(NSString *)userId;
// 根据用户保存相应的图标边框素材
- (void)user:(NSString *)userId used:(JCIConObject *)icon;
@end

NS_ASSUME_NONNULL_END
