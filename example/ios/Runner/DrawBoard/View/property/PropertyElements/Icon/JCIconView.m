//
//  JCIconView.m
//  XYFrameWork
//
//  Created by xingling xu on 2020/11/17.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import "JCIconView.h"
#import "JCIconCollectionViewCell.h"
#import "JCIconBorderManager.h"
#import "JCBarData.h"
#import "JCLogoCategoryModel.h"
#import "JCIConObject.h"
#import "JCIconDataManager.h"
#import "UIView+PlaceHolder.h"
#import "JCApplicationManager.h"

static NSString * identifier = @"JCIconCollectionViewCell";
static NSInteger __currentLimit = 30;
static NSString *__iconIndustryId = @"2";
#define CurrentDataKey(materialCategoryId) [NSString stringWithFormat:@"industryId_2_categoryId_%@",materialCategoryId]

@interface JCIconView () <UICollectionViewDelegate, UICollectionViewDelegateFlowLayout>
@property (nonatomic, strong) UICollectionView *collectionView;
@property (nonatomic, strong) NSMutableArray *currentDatas;
@property (nonatomic, strong) NSMutableDictionary *allData;
// 因为都是图标，只考虑小类id，大类id统一为2
@property (nonatomic, copy) NSString *currentMaterialCategoryId;
@property (nonatomic, assign) NSInteger page;
@property (nonatomic, assign) NSInteger limit;
@end

@implementation JCIconView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = [UIColor whiteColor];
        [self loadRootViews];
    }
    return self;
}

- (void)loadRootViews {
    [self addSubview:self.collectionView];
    [self showUsedData];
}

// 先取内存----> 数据库 ------ > 网络请求 
- (void)selectCategoryId:(NSString *)materialCategoryId {
    [self.currentDatas removeAllObjects];
    [self.collectionView reloadData];
    self.collectionView.mj_header.hidden = NO;
    self.collectionView.mj_footer.hidden = YES;
    self.currentMaterialCategoryId = materialCategoryId;
    // 先取内存
    self.page = [self getMemoryPage:materialCategoryId];
    NSArray *temp = [self getMemoryDatas:materialCategoryId];
    if (temp.count > 0) {
        [self.currentDatas addObjectsFromArray:temp];
        [self refreshCollectionView];
    } else {
        // 先从数据库获取数据
        NSArray *dbData = [[JCIconDataManager sharedManager] getIconsWith:__iconIndustryId smallId:materialCategoryId];
        if (dbData.count > 0) {
            [self.currentDatas addObjectsFromArray:dbData];
        }
        [self.collectionView reloadData];
        // 请求数据
        [self.collectionView.mj_header beginRefreshing];
        [self refreshData];
    }
    [self.collectionView scrollRectToVisible:CGRectMake(0, 0, 1, 1) animated:YES];
}

- (void)refreshData {
    if (self.collectionView.mj_header.hidden) return;
    self.page = 1;
    [self requestIconsWithPage:self.page];
}

- (void)loadNextData {
    if (self.collectionView.mj_footer.hidden) return;
    self.page ++;
    [self requestIconsWithPage:self.page];
}

// 使用过
- (void)showUsedData {
    NSArray *temp = [[JCIconDataManager sharedManager] getIconsWith:__iconIndustryId
                                                             userId:JCAPP_Manager.user.userId];
    [self.currentDatas removeAllObjects];
    if (temp.count > 0) {
        [self.currentDatas addObjectsFromArray:temp];
    } else {
        
    }
    
    [self refreshCollectionView];
    self.collectionView.mj_header.hidden = YES;
    self.collectionView.mj_footer.hidden = YES;
    
    __weak typeof(self) weakSelf = self;
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)),
                   dispatch_get_main_queue(), ^{
        [weakSelf.collectionView scrollRectToVisible:CGRectMake(0, 0, 1, 1) animated:YES];
    });
}

- (void)refreshCollectionView {
    [self.collectionView reloadData];
    [self checkFooter];
    [self checkNoData];
}

- (void)checkFooter {
    if (self.currentDatas.count < __currentLimit) {
        self.collectionView.mj_footer.hidden = YES;
    } else {
        self.collectionView.mj_footer.hidden = NO;
    }
}

- (void)checkNoData {
    if (self.currentDatas.count == 0) {
        [self.collectionView showNoDataTipView];
    } else {
        [self.collectionView hiddenNoDataTipView];
    }
}


- (void)requestIconsWithPage:(NSInteger)page {
    NSDictionary *params = @{@"materialIndustryId":__iconIndustryId,@"materialCategoryId":self.currentMaterialCategoryId,@"page":StringFromInt(page),@"limit":StringFromInt(__currentLimit)};
    [DCHTTPRequest postWithParams:params Path:JC_Icons_Borders_Path Json:YES Success:^(__kindof YTKBaseRequest * _Nonnull request, id  _Nonnull responseObject) {
        if (page == 1) [self.currentDatas removeAllObjects];
        NSArray *list = [JCIConObject arrayOfModelsFromDictionaries:[responseObject objectForKey:@"list" ] error:nil];
        [self.currentDatas addObjectsFromArray:list];
        [[JCIconDataManager sharedManager] saveDatas:list];
        // 内存数据
        [self saveCurrentData:self.currentDatas page:page materialCategoryId:self.currentMaterialCategoryId];
        [self.collectionView.mj_header endRefreshing];
        if (list.count < __currentLimit) {
            [self.collectionView.mj_footer endRefreshingWithNoMoreData];
        } else {
            [self.collectionView.mj_footer endRefreshing];
        }
        [self refreshCollectionView];
    } failure:^(YTKBaseRequest * _Nullable request, NSError * _Nullable error) {
        self.collectionView.mj_footer.hidden = YES;
        [self.collectionView.mj_header endRefreshing];
        [self.collectionView.mj_footer endRefreshing];
        [DCHUDHelper showMessage:@"网络异常，请尝试切换网络!" hideAfterDelay:2];
    }];
}

#pragma mark - Memory Data
// 内存缓存数据
- (void)saveCurrentData:(NSArray *)datas page:(NSInteger)page materialCategoryId:(NSString *)materialCategoryId {
    NSDictionary *dict = @{@"data":[NSArray arrayWithArray:datas],@"page":StringFromInt(page)};
    [self.allData setObject:dict forKey:CurrentDataKey(materialCategoryId)];
}

- (NSArray *)getMemoryDatas:(NSString *)materialCategoryId {
    NSArray *temp = [[self.allData objectForKey:CurrentDataKey(materialCategoryId)] objectForKey:@"data"];
    return temp;
}

- (NSInteger)getMemoryPage:(NSString *)materialCategoryId {
    NSInteger page = [[[self.allData objectForKey:CurrentDataKey(materialCategoryId)] objectForKey:@"page"] integerValue];
    return page;
}

#pragma mark - UICollectionView DataSource
- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return self.currentDatas.count;
}

// The cell that is returned must be retrieved from a call to -dequeueReusableCellWithReuseIdentifier:forIndexPath:
- (__kindof UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    JCIconCollectionViewCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:identifier forIndexPath:indexPath];
    JCIConObject *object = [self.currentDatas safeObjectAtIndex:indexPath.row];
    cell.object = object;
    return cell;
}

#pragma mark - UICollectionView Delegate
- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
    JCIConObject *object = [self.currentDatas safeObjectAtIndex:indexPath.row];
    [[JCIconDataManager sharedManager] user:JCAPP_Manager.user.userId used:object];
    JCPerformSelectorWithOneObject(self.context.drawBoard, sel(@"addIcon:"),object);
}

#pragma mark - UICollectionViewDelegateFlowLayout
- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath {
    CGFloat width = [JCIconCollectionViewCell widthForIcon];
    return CGSizeMake(width, width);
}

#pragma mark - lazy
- (UICollectionView *)collectionView {
    if (!_collectionView) {
        UICollectionViewFlowLayout *layout = [[UICollectionViewFlowLayout alloc] init];
        CGFloat itemW = [JCIconCollectionViewCell widthForIcon];
        CGFloat itemH = [JCIconCollectionViewCell widthForIcon];
        layout.itemSize = CGSizeMake(itemW, itemH);
        layout.sectionInset = UIEdgeInsetsMake(20, 16, 15, 16);
        layout.scrollDirection = UICollectionViewScrollDirectionVertical;
        layout.minimumLineSpacing = 15;
        layout.minimumInteritemSpacing = 11;
        
        _collectionView = [[UICollectionView alloc] initWithFrame:(CGRect){0,0,kSCREEN_WIDTH,self.bounds.size.height} collectionViewLayout:layout];
        _collectionView.backgroundColor = [UIColor whiteColor];
        _collectionView.showsVerticalScrollIndicator = NO;
        [_collectionView registerClass:[JCIconCollectionViewCell class] forCellWithReuseIdentifier:identifier];
        _collectionView.showsHorizontalScrollIndicator = NO;
        _collectionView.delegate = self;
        _collectionView.dataSource = self;
        
        _collectionView.mj_header = [MJRefreshNormalHeader headerWithRefreshingTarget:self refreshingAction:@selector(refreshData)];
        _collectionView.mj_footer = [MJRefreshAutoNormalFooter footerWithRefreshingTarget:self refreshingAction:@selector(loadNextData)];
        _collectionView.mj_footer.hidden = YES;
    }
    return _collectionView;
}

- (NSMutableArray *)currentDatas {
    if (!_currentDatas) {
        _currentDatas = [NSMutableArray array];
    }
    return _currentDatas;
}

- (NSMutableDictionary *)allData {
    if (!_allData) {
        _allData = [NSMutableDictionary dictionary];
    }
    return _allData;
}

#pragma mark -- data refresh
- (void)setElement:(JCElementBaseView *)element {
    [super setElement:element];
    // load data
}

+ (NSArray *)iconTitleDatas {
    NSArray *array = [[JCIconBorderManager sharedManager] iconTitleModels];
    NSMutableArray *temp = [NSMutableArray arrayWithCapacity:array.count+1];
    
    [temp addObject:[JCBarData dataWithTitle:@"用过" userInfo:@""]];
    [array enumerateObjectsUsingBlock:^(JCLogoCategoryModel *model, NSUInteger idx, BOOL * _Nonnull stop) {
        JCBarData *data = [JCBarData dataWithTitle:model.name userInfo:model];
        if (data) [temp addObject:data];
    }];
    return temp;
}

@end
