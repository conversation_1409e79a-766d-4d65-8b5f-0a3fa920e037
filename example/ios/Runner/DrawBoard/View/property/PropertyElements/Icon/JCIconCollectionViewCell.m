//
//  JCIconCollectionViewCell.m
//  XYFrameWork
//
//  Created by xingling xu on 2020/11/17.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import "JCIconCollectionViewCell.h"

@interface JCIconCollectionViewCell ()

@property (nonatomic, strong) UIImageView *icon;

@end

@implementation JCIconCollectionViewCell
- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        self.layer.borderWidth = 1;
        self.layer.borderColor =XY_HEX_RGB(0xEBEBEB).CGColor;
        self.layer.cornerRadius = 10;
        [self addSubview:self.icon];
        
    }
    return self;
}

#pragma mark - setter
- (void)setObject:(JCIConObject *)object {
    _object = object;
    [self.icon jc_setImageWithUrl:object.imageUrl];
}

#pragma mark - lazy
- (UIImageView *)icon {
    if (!_icon) {
        _icon = [[UIImageView alloc] initWithFrame:(CGRect){10,10,[JCIconCollectionViewCell widthForIcon]-20,[JCIconCollectionViewCell widthForIcon]-20}];
        _icon.contentMode = UIViewContentModeScaleAspectFit;
    }
    return _icon;
}

+ (CGFloat)widthForIcon {
    return (kSCREEN_WIDTH - 44 - 32)/5;
}

@end
