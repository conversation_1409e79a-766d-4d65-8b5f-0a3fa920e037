//
//  JCImagePropertyView.m
//  XYFrameWork
//
//  Created by xingling xu on 2020/5/12.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import "JCImagePropertyView.h"
#import "JCSliderView.h"
#import "JCStyleTitleLabel.h"

#define CenterSlider 66.0
#define LabelOffSets 4//slide的中心不是滑动条的中心,需要添加偏移量

@interface JCImagePropertyView ()
@property (nonatomic,strong) JCStyleTitleLabel *sliderLeftTitleLabel;
@property (nonatomic,strong) JCStyleTitleLabel *sliderRightTitleLabel;
@property (nonatomic,strong) JCStyleTitleLabel *zoomLabel;
@property (nonatomic,strong) JCSliderView *slider;
@property (nonatomic, strong) UISwitch *sw;
@end

@implementation JCImagePropertyView
-(instancetype)initWithFrame:(CGRect)frame{
    self = [super initWithFrame:frame];
    if(self){
        [self setUp];
    }
    return self;
}

-(void)setUp{
    [self addSubview:self.sliderLeftTitleLabel];
    [self addSubview:self.sliderRightTitleLabel];
    [self addSubview:self.slider];
//    [self addSubview:self.zoomLabel];
//    [self addSubview:self.sw];
}
- (void)setAccessoryType:(JCSliderAccessory)accessoryType{
    _accessoryType = accessoryType;
    self.slider.type = accessoryType;
}
- (void)layoutSubviews {
    self.sliderLeftTitleLabel.centerY = CenterSlider + LabelOffSets;
    self.sliderLeftTitleLabel.left = 24;
    self.sliderRightTitleLabel.centerY = CenterSlider + LabelOffSets;
    self.sliderRightTitleLabel.right = SCREEN_WIDTH -24;
    self.slider.centerY = CenterSlider;
    self.slider.centerX = SCREEN_WIDTH * 0.5;
//    self.zoomLabel.left = 24;
//    self.zoomLabel.centerY = 115;
//    self.sw.right = self.width-24;
//    self.sw.centerY = self.zoomLabel.centerY;
}

#pragma mark -- JCSliderViewDelegate
- (void)sliderView:(JCSliderView *)sliderView selectIndex:(NSInteger)index  selectValue:(NSString *)value {
    [JCElementEditManager editElement:self.element imageThresHoldValue:value];
}

/** 修改之前的字体 */
- (void)sliderView:(JCSliderView *)sliderView selectIndex:(NSInteger)index  valueChangeBegin:(NSString *)beginValue {
    BeginUndoWithKey(k_image_threshold, beginValue)
}
/** 修改之后的字体 */
- (void)sliderView:(JCSliderView *)sliderView selectIndex:(NSInteger)index  valueChangeEnd:(NSString *)endValue {
    EndUndoWithKey(k_image_threshold, endValue)
}

- (void)switchAction:(UISwitch *)switchWidget {
    [JCElementEditManager editElement:self.element allowFreeZoom:switchWidget.isOn];
}

#pragma  mark - lazy
- (JCStyleTitleLabel *)sliderLeftTitleLabel {
    if (!_sliderLeftTitleLabel) {
        _sliderLeftTitleLabel = [[JCStyleTitleLabel alloc] initWithText:XY_LANGUAGE_TITLE_NAMED(@"app01019",@"浅")];
        _sliderLeftTitleLabel.textAlignment = NSTextAlignmentCenter;
//        _sliderLeftTitleLabel.frame = CGRectMake(0, 0, 100, 44);
//        _sliderLeftTitleLabel.backgroundColor = [UIColor redColor];
    }
    return _sliderLeftTitleLabel;
}

- (JCStyleTitleLabel *)sliderRightTitleLabel {
    if (!_sliderRightTitleLabel) {
        _sliderRightTitleLabel = [[JCStyleTitleLabel alloc] initWithText:XY_LANGUAGE_TITLE_NAMED(@"app01020",@"深")];
        _sliderRightTitleLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _sliderRightTitleLabel;
}


- (JCStyleTitleLabel *)zoomLabel {
    if (!_zoomLabel) {
        _zoomLabel = [[JCStyleTitleLabel alloc] initWithOneLineText:XY_LANGUAGE_TITLE_NAMED(@"app01068",@"自由拉伸")];
    }
    return _zoomLabel;
}

- (JCSliderView *)slider {
    if (!_slider) {
        NSMutableArray *imageValues = [NSMutableArray arrayWithCapacity:256];
        for (NSInteger i = 0; i <= 255; i++) {
            [imageValues addObject:StringFromInt(i)];
        }
        CGFloat remainWidth = SCREEN_WIDTH-80 * 2;
        _slider = [[JCSliderView alloc] initWithFrame:(CGRect){0,0,remainWidth,24} values:imageValues];
        _slider.delegate = self;
    }
    return _slider;
}

- (UISwitch *)sw {
    if (!_sw) {
        _sw = [[UISwitch alloc] initWithFrame:(CGRect){0,0,54,30}];
        [_sw addTarget:self action:@selector(switchAction:) forControlEvents:UIControlEventValueChanged];
        _sw.onTintColor = COLOR_APPLE;
    }
    return _sw;
}

#pragma mark -- data refresh
- (void)setElement:(JCElementBaseView *)element {
    [super setElement:element];
    [self.slider selectValue:[JCElementEditManager getEementImageThresHoldValue:element] animated:NO];
    [self.sw setOn:[JCElementEditManager getElementImageAllowFreeZoom:element]];
}

@end
