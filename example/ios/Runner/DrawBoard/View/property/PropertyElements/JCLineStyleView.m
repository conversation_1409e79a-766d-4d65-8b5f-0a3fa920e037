//
//  JCLineStyleView.m
//  XYFrameWork
//
//  Created by xingling xu on 2019/12/13.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCLineStyleView.h"
#import "JCStyleTitleLabel.h"
#import "JCSelectionView.h"
#import "JCSliderView.h"

@interface JCLineStyleView () <JCSelectionViewDelegate>
@property (nonatomic, strong) JCStyleTitleLabel *lineStyleLabel;
@property (nonatomic, strong) JCStyleTitleLabel *lineWidthLabel;
@property (nonatomic, strong) JCStyleTitleLabel *rightLineWidthLabel;
@property (nonatomic, strong) JCSelectionView *selectionView;
@property (nonatomic,strong) JCSliderView *slider;
@end

@implementation JCLineStyleView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setUp];
    }
    return self;
}

- (void)setUp {
    [self addSubview:self.lineStyleLabel];
    [self addSubview:self.lineWidthLabel];
    [self addSubview:self.rightLineWidthLabel];
    [self addSubview:self.selectionView];
    [self addSubview:self.slider];
}
- (void)setAccessoryType:(JCSliderAccessory)accessoryType{
    _accessoryType = accessoryType;
    self.slider.type = accessoryType;
}
- (void)layoutSubviews {
    [super layoutSubviews];
    self.lineStyleLabel.left = 24;
    self.lineStyleLabel.centerY = 48;
    self.lineWidthLabel.left = 24;
    self.lineWidthLabel.centerY = 112 + 4;
    self.rightLineWidthLabel.right = self.width - 24;
    self.rightLineWidthLabel.centerY = 112 + 4;
    self.selectionView.centerY = self.lineStyleLabel.centerY;
    self.selectionView.right = self.width-24;
    self.slider.right = self.rightLineWidthLabel.left-24;
    self.slider.centerY = self.lineWidthLabel.centerY;
}

#pragma mark -- JCSelectionViewDelegate  选择实线、虚线
- (void)selectionView:(JCSelectionView *)selectionView selectIndex:(NSInteger)index {
    BeginUndoWithKey(k_linetype, ([NSString stringWithFormat:@"%ld",[JCElementEditManager getEementLineType:self.element]]))
    [JCElementEditManager editElement:self.element lineType:index];
    EndUndoWithKey(k_linetype, ([NSString stringWithFormat:@"%ld",index]))
}

#pragma mark -- JCSliderViewDelegate 更改线宽
- (void)sliderView:(JCSliderView *)sliderView selectIndex:(NSInteger)index  selectValue:(NSString *)value {
    NSLog(@"============>%.2f",value.floatValue);
    [JCElementEditManager editElement:self.element mmLineWidth:value.floatValue];
}

/** 修改之前的线宽 */
- (void)sliderView:(JCSliderView *)sliderView selectIndex:(NSInteger)index  valueChangeBegin:(NSString *)beginValue {
    BeginUndoWithKey(k_linewidth, beginValue)
}
/** 修改之后的线宽 */
- (void)sliderView:(JCSliderView *)sliderView selectIndex:(NSInteger)index  valueChangeEnd:(NSString *)endValue {
    EndUndoWithKey(k_linewidth, endValue)
}


#pragma mark -- lazy
- (JCStyleTitleLabel *)lineStyleLabel {
    if (!_lineStyleLabel) {
        _lineStyleLabel = [[JCStyleTitleLabel alloc] initWithOneLineText:XY_LANGUAGE_TITLE_NAMED(@"app01021", @"描边")];
    }
    return _lineStyleLabel;
}

- (JCStyleTitleLabel *)lineWidthLabel {
    if (!_lineWidthLabel) {
        _lineWidthLabel = [[JCStyleTitleLabel alloc] initWithText:XY_LANGUAGE_TITLE_NAMED(@"app00154",@"细")];
    }
    return _lineWidthLabel;
}

- (JCStyleTitleLabel *)rightLineWidthLabel {
    if (!_rightLineWidthLabel) {
        _rightLineWidthLabel = [[JCStyleTitleLabel alloc] initWithText:XY_LANGUAGE_TITLE_NAMED(@"app00154",@"粗")];
    }
    return _rightLineWidthLabel;
}

- (JCSelectionView *)selectionView {
    if (!_selectionView) {
        _selectionView = [[JCSelectionView alloc] initWithFrame:(CGRect){0,0,96,40} normalImageNames:@[@"line_solid_normal",@"line_dotted_normal"] selectImageNames:@[@"line_solid_select",@"line_dotted_select"]];
        _selectionView.delegate = self;
    }
    return _selectionView;
}

- (JCSliderView *)slider {
    if (!_slider) {
        NSMutableArray *widthArr = [NSMutableArray arrayWithCapacity:51];
        //double精度丢失造成宽度少值
        for (CGFloat width = 0.2; width <= 3.01; width+=0.1) {
            [widthArr addObject:[NSString stringWithFormat:@"%.1f",width]];
        }
        //|-24-left label-24-slide view-24-right label-24|
        CGFloat remainWidth = SCREEN_WIDTH-self.lineWidthLabel.width-(self.rightLineWidthLabel.width + 48)-48;
        _slider = [[JCSliderView alloc] initWithFrame:(CGRect){0,0,remainWidth,24} values:widthArr];
        _slider.delegate = self;
    }
    return _slider;
}

#pragma mark -- refresh
- (NSArray *)sliderValues:(JCElementBaseView *)element {
    NSMutableArray *temp = [NSMutableArray array];
    for (float width = 0.2; width <= 3; width+=0.1) {
        [temp addObject:[NSString stringWithFormat:@"%.1f",width]];
    }
    return temp;
}
- (void)setElement:(JCElementBaseView *)element {
    [super setElement:element];
    // 表格的边框线宽范围和线条不一样
//    self.slider.values = [self sliderValues:element];
    [self.slider selectValue:[NSString stringWithFormat:@"%.1f",[JCElementEditManager getEementLine_mmWidth:element]] animated:NO];
    [self.selectionView setSelectIndex:[JCElementEditManager getEementLineType:element]-1];
}
@end
