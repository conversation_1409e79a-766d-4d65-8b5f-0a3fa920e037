//
//  JCFontTableViewCell.m
//  XYFrameWork
//
//  Created by xingling xu on 2020/11/16.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import "JCFontTableViewCell.h"
#import "JCProgressView.h"
#import "UIFont+JCCustomFont.h"

@interface JCFontTableViewCell ()
@property (nonatomic, strong) UIImageView *fontImageView;
@property (nonatomic, strong) UILabel *fontLabel;
@property (nonatomic, strong) UIImageView *selectIcon;
@property (nonatomic, strong) UIImageView *downLoadIcon;
@property (nonatomic, strong) JCProgressView *progressView;
@end

@implementation JCFontTableViewCell
+ (JCFontTableViewCell *)cellWithTableView:(UITableView *)tableView {
    static NSString *cellID = @"JCFontTableViewCell";
    JCFontTableViewCell *myCell = (JCFontTableViewCell *)[tableView dequeueReusableCellWithIdentifier:cellID];
    if (myCell == nil) {
        myCell = [[JCFontTableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:cellID];
    }
    return myCell;
}

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        self.contentView.backgroundColor = XY_HEX_RGB(0xffffff);
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        
        [self.contentView addSubview:self.selectIcon];
        [self.contentView addSubview:self.fontLabel];
        [self.contentView addSubview:self.fontImageView];
        [self.contentView addSubview:self.downLoadIcon];
        [self.contentView addSubview:self.progressView];
        
        UIView *line = [[UIView alloc] initWithFrame:(CGRect){40,49.5,kSCREEN_WIDTH-40,0.5}];
        line.backgroundColor = XY_HEX_RGB(0xebebeb);
        [self.contentView addSubview:line];
        
        self.downLoadIcon.centerX = self.progressView.centerX;
    }
    return self;
}

#pragma mark - setter
- (void)setFontModel:(JCFontModel *)fontModel {
    _fontModel = fontModel;
    self.fontLabel.text = fontModel.name;
    UIColor *textColor = fontModel.hasDownLoad?XY_HEX_RGB(0x262626):XY_HEX_RGB(0xbebebe);
    self.fontLabel.textColor = textColor;
    UIFont *font = [UIFont jc_fontWithFontName:fontModel.fontCode fontSize:15 italic:NO];
    self.fontLabel.font = font?font:MY_FONT_Medium(15);
    if (fontModel.hasDownLoad) {
        self.downLoadIcon.hidden = YES;
        self.progressView.hidden = YES;
    } else {
        if (fontModel.isDownLoading) {
            self.downLoadIcon.hidden = YES;
            self.progressView.hidden = NO;
        } else {
            self.downLoadIcon.hidden = NO;
            self.progressView.hidden = YES;
        }
    }
}

- (void)setChoosed:(BOOL)choosed {
    self.selectIcon.hidden = !choosed;
}

- (void)setProgress:(CGFloat)progress {
    _progress = progress;
    self.progressView.progress = progress;
}

#pragma mark - lazy
- (UIImageView *)fontImageView {
    if (!_fontImageView) {
        _fontImageView = [[UIImageView alloc] init];
    }
    return _fontImageView;
}

- (UILabel *)fontLabel {
    if (!_fontLabel) {
        _fontLabel = [[UILabel alloc] initWithFrame:(CGRect){40,0,200,50}];
        _fontLabel.font = MY_FONT_Medium(15);
        _fontLabel.textColor = XY_HEX_RGB(0x262626);
        _fontLabel.textAlignment = NSTextAlignmentLeft;
    }
    return _fontLabel;
}

- (UIImageView *)selectIcon {
    if (!_selectIcon) {
        _selectIcon = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"font_selected"]];
        _selectIcon.frame = (CGRect){16,19,16,12};
    }
    return _selectIcon;
}

- (UIImageView *)downLoadIcon {
    if (!_downLoadIcon) {
        _downLoadIcon = [[UIImageView  alloc] initWithImage:[UIImage imageNamed:@"text_font_download"]];
        _downLoadIcon.frame = (CGRect){kSCREEN_WIDTH-23,20,13,13};
    }
    return _downLoadIcon;
}

- (JCProgressView *)progressView {
    if (!_progressView) {
        _progressView = [[JCProgressView alloc] initWithFrame:(CGRect){kSCREEN_WIDTH-37,16,19,19}];
    }
    return _progressView;
}

@end
