//
//  JCBorderView.m
//  XYFrameWork
//
//  Created by xingling xu on 2020/11/23.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import "JCBorderView.h"
#import "JCBorderCollectionCell.h"
#import "JCIconBorderManager.h"
#import "JCBarData.h"
#import "JCLogoCategoryModel.h"
#import "JCIConObject.h"
#import "JCIconDataManager.h"
#import "UIView+PlaceHolder.h"

static NSString * identifier = @"JCBorderCollectionCell";
static NSInteger __currentLimit = 30;
static NSString *__borderIndustryId = @"3";
#define CurrentBorderDataKey(materialCategoryId) [NSString stringWithFormat:@"industryId_3_categoryId_%@",materialCategoryId]


@interface JCBorderView () <UICollectionViewDelegate,UICollectionViewDataSource>
@property (nonatomic, strong) UICollectionView *collectionView;
@property (nonatomic, strong) NSMutableArray *currentDatas;
@property (nonatomic, strong) NSMutableDictionary *allData;
// 因为都是图标，只考虑小类id，大类id统一为2
@property (nonatomic, copy) NSString *currentMaterialCategoryId;
@property (nonatomic, assign) NSInteger page;
@property (nonatomic, assign) NSInteger limit;
@end

@implementation JCBorderView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = [UIColor whiteColor];
        [self loadRootViews];
    }
    return self;
}

- (void)loadRootViews {
    [self addSubview:self.collectionView];
    [self showUsedData];
}

// 先取内存----> 数据库 ------ > 网络请求
- (void)selectCategoryId:(NSString *)materialCategoryId {
    self.collectionView.mj_header.hidden = NO;
    self.collectionView.mj_footer.hidden = NO;
    self.currentMaterialCategoryId = materialCategoryId;
    // 先取内存
    self.page = [self getMemoryPage:materialCategoryId];
    NSArray *temp = [self getMemoryDatas:materialCategoryId];
    if (temp.count > 0) {
        [self.currentDatas removeAllObjects];
        [self.currentDatas addObjectsFromArray:temp];
        [self refreshCollectionView];
    } else {
        // 先从数据库获取数据
        NSArray *dbData = [[JCIconDataManager sharedManager] getIconsWith:__borderIndustryId smallId:self.currentMaterialCategoryId];
        [self.currentDatas removeAllObjects];
        if (dbData.count > 0) {
            [self.currentDatas addObjectsFromArray:dbData];   
        }
        [self refreshCollectionView];
        // 请求数据
        [self refreshData];
    }
    [self.collectionView scrollRectToVisible:CGRectMake(0, 0, 1, 1) animated:YES];
}

- (void)refreshData {
    self.page = 1;
    [self requestIconsWithPage:self.page];
}

- (void)loadNextData {
    self.page ++;
    [self requestIconsWithPage:self.page];
}

// 使用过
- (void)showUsedData {
    NSArray *temp = [[JCIconDataManager sharedManager] getIconsWith:__borderIndustryId userId:@"user3"];
    [self.currentDatas removeAllObjects];
    if (temp.count > 0) {
        [self.currentDatas addObjectsFromArray:temp];
    } else {
        
    }
    [self refreshCollectionView];
    [self.collectionView scrollRectToVisible:CGRectMake(0, 0, 1, 1) animated:YES];
    self.collectionView.mj_header.hidden = YES;
    self.collectionView.mj_footer.hidden = YES;
}

- (void)refreshCollectionView {
    if (self.currentDatas.count < __currentLimit) {
        self.collectionView.mj_footer.hidden = YES;
    } else {
        self.collectionView.mj_footer.hidden = NO;
    }
    [self.collectionView reloadData];
    if (self.currentDatas.count == 0) {
        [self.collectionView showNoDataTipView];
    } else {
        [self.collectionView hiddenNoDataTipView];
    }
}


- (void)requestIconsWithPage:(NSInteger)page {
    NSDictionary *params = @{@"materialIndustryId":__borderIndustryId,@"materialCategoryId":self.currentMaterialCategoryId,@"page":StringFromInt(page),@"limit":StringFromInt(__currentLimit)};
    [DCHTTPRequest postWithParams:params Path:JC_Icons_Borders_Path Json:YES Success:^(__kindof YTKBaseRequest * _Nonnull request, id  _Nonnull responseObject) {
        if (page == 1) [self.currentDatas removeAllObjects];
        NSArray *list = [JCIConObject arrayOfModelsFromDictionaries:[responseObject objectForKey:@"list" ] error:nil];
        [self.currentDatas addObjectsFromArray:list];
        [[JCIconDataManager sharedManager] saveDatas:list];
        // 内存数据
        [self saveCurrentData:self.currentDatas page:page materialCategoryId:self.currentMaterialCategoryId];
        [self.collectionView.mj_header endRefreshing];
        if (list.count < __currentLimit) {
            [self.collectionView.mj_footer endRefreshingWithNoMoreData];
        } else {
            [self.collectionView.mj_footer endRefreshing];
        }
        [self refreshCollectionView];
    } failure:^(YTKBaseRequest * _Nullable request, NSError * _Nullable error) {
        [self.collectionView.mj_header endRefreshing];
        self.collectionView.mj_footer.hidden = YES;
        [self.collectionView.mj_footer endRefreshing];
        [DCHUDHelper showMessage:@"网络异常，请尝试切换网络!" hideAfterDelay:2];
    }];
}
#pragma mark - Memory Data
// 内存缓存数据
- (void)saveCurrentData:(NSArray *)datas page:(NSInteger)page materialCategoryId:(NSString *)materialCategoryId {
    NSDictionary *dict = @{@"data":[NSArray arrayWithArray:datas],@"page":StringFromInt(page)};
    [self.allData setObject:dict forKey:CurrentBorderDataKey(materialCategoryId)];
}

- (NSArray *)getMemoryDatas:(NSString *)materialCategoryId {
    NSArray *temp = [[self.allData objectForKey:CurrentBorderDataKey(materialCategoryId)] objectForKey:@"data"];
    return temp;
}

- (NSInteger)getMemoryPage:(NSString *)materialCategoryId {
    NSInteger page = [[[self.allData objectForKey:CurrentBorderDataKey(materialCategoryId)] objectForKey:@"page"] integerValue];
    return page;
}
#pragma mark - UICollectionView DataSource
- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return self.currentDatas.count;
}

// The cell that is returned must be retrieved from a call to -dequeueReusableCellWithReuseIdentifier:forIndexPath:
- (__kindof UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    JCBorderCollectionCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:identifier forIndexPath:indexPath];
    JCIConObject *object = [self.currentDatas safeObjectAtIndex:indexPath.row];
    cell.object = object;
    return cell;
}

#pragma mark - UICollectionView Delegate
- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
    JCIConObject *object = [self.currentDatas safeObjectAtIndex:indexPath.row];
    [[JCIconDataManager sharedManager] user:@"user3" used:object];
    JCPerformSelectorWithOneObject(self.context.drawBoard, sel(@"addBorder:"), object);
}

#pragma mark - UICollectionViewDelegateFlowLayout
- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath {
    return CGSizeMake([JCBorderCollectionCell width4Border], [JCBorderCollectionCell height4Border]);
}
#pragma mark - lazy
- (UICollectionView *)collectionView {
    if (!_collectionView) {
        UICollectionViewFlowLayout *layout = [[UICollectionViewFlowLayout alloc] init];
        CGFloat itemW = [JCBorderCollectionCell width4Border];
        CGFloat itemH = [JCBorderCollectionCell height4Border];
        layout.itemSize = CGSizeMake(itemW, itemH);
        layout.sectionInset = UIEdgeInsetsMake(20, 16, 15, 16);
        layout.scrollDirection = UICollectionViewScrollDirectionVertical;
        layout.minimumLineSpacing = 15;
        layout.minimumInteritemSpacing = 11;
        
        _collectionView = [[UICollectionView alloc] initWithFrame:(CGRect){0,0,kSCREEN_WIDTH,Property_height} collectionViewLayout:layout];
        _collectionView.backgroundColor = [UIColor whiteColor];
        _collectionView.showsVerticalScrollIndicator = NO;
        [_collectionView registerClass:[JCBorderCollectionCell class] forCellWithReuseIdentifier:identifier];
        _collectionView.showsHorizontalScrollIndicator = NO;
        _collectionView.delegate = self;
        _collectionView.dataSource = self;
        _collectionView.mj_header = [MJRefreshNormalHeader headerWithRefreshingTarget:self refreshingAction:@selector(refreshData)];
        _collectionView.mj_footer = [MJRefreshAutoNormalFooter footerWithRefreshingTarget:self refreshingAction:@selector(loadNextData)];
    }
    return _collectionView;
}

- (NSMutableArray *)currentDatas {
    if (!_currentDatas) {
        _currentDatas = [NSMutableArray array];
    }
    return _currentDatas;
}

- (NSMutableDictionary *)allData {
    if (!_allData) {
        _allData = [NSMutableDictionary dictionary];
    }
    return _allData;
}

#pragma mark -- data refresh
- (void)setElement:(JCElementBaseView *)element {
    [super setElement:element];
    // load data
}

+ (NSArray *)borderTitleDatas {
    NSArray *array = [[JCIconBorderManager sharedManager] borderTitleModels];
    NSMutableArray *temp = [NSMutableArray arrayWithCapacity:array.count+1];
    [temp addObject:[JCBarData dataWithTitle:@"用过" userInfo:@""]];
    [array enumerateObjectsUsingBlock:^(JCLogoCategoryModel *model, NSUInteger idx, BOOL * _Nonnull stop) {
        JCBarData *data = [JCBarData dataWithTitle:model.name userInfo:model];
        if (data) [temp addObject:data];
    }];
    return temp;
}
@end
