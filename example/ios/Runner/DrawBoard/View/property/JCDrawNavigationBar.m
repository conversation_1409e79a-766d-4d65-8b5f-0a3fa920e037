//
//  JCDrawNavigationBar.m
//  XYFrameWork
//
//  Created by xingling xu on 2020/12/9.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import "JCDrawNavigationBar.h"
#import "JCBoardRightItemView.h"
#import "JCApplicationManager.h"
#import "JCBluetoothManager.h"

@interface JCDrawNavigationBar ()
@property (nonatomic, strong) UIButton *closeBtn;
@property (nonatomic, strong) UIButton *deviceButton;
@property (nonatomic, strong) UIImageView *connectImageView;
@property (nonatomic, strong) JCBoardRightItemView *rightItemView;
@end

@implementation JCDrawNavigationBar
- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = [UIColor whiteColor];
        [self loadRootView];
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(printerStatusNotification:) name:PrinterStatusNotification object:nil];
    }
    return self;
}

- (void)loadRootView {
    [self addSubview:self.closeBtn];
    [self addSubview:self.deviceButton];
    [self addSubview:self.rightItemView];
    [self addSubview:self.connectImageView];
    self.rightItemView.bottom = self.height;
    self.rightItemView.right = kSCREEN_WIDTH-16;
    self.closeBtn.bottom = self.height;
    self.deviceButton.bottom = self.height;
    self.deviceButton.centerY = self.closeBtn.centerY;
    self.deviceButton.left = self.closeBtn.right + 8;
    self.connectImageView.left = self.deviceButton.right - 7;
    self.connectImageView.top = self.deviceButton.bottom - 7;
    [self refreshDeviceState:kJCPrinter_Type];
}

- (void)printerStatusNotification:(NSNotification *)noti {
    [self refreshDeviceState:JC_CURRENT_CONNECTED_PRINTER];
}

- (void)refreshSizeTitle:(NSString *)title {
    self.rightItemView.title = title;
}

- (void)refreshDeviceState:(NSString *)deviceType {
    if (STR_IS_NIL(deviceType)) deviceType = kJCPrinter_Type;
    // 机型
    NSString *deviceImageName = @"device_B21";
    if ([deviceType hasPrefix:@"B21"]) {
        deviceImageName = @"device_B21";
    } else if ([deviceType hasPrefix:@"D110"]){
        deviceImageName = @"device_D110";
    } else {
        deviceImageName = @"device_D11";
    }
    UIImage *deviceImage = [UIImage imageNamed:deviceImageName];
    self.deviceButton.size = deviceImage.size;
    self.deviceButton.centerY = self.closeBtn.centerY;
    self.deviceButton.left = self.closeBtn.right + 8;
    [self.deviceButton setImage:deviceImage forState:UIControlStateNormal];
    // 机器是否连接上
    NSString *iconImgName = JC_IS_CONNECTED_PRINTER?@"icn_connected":@"icn_disconnected";
    self.connectImageView.image = [UIImage imageNamed:iconImgName];
}

#pragma mark - selector
- (void)close:(UIButton *)button {
    JCPerformSelector(self.context.baseController, sel(@"close"))
}

- (void)device:(UIButton *)button {
    JCPerformSelector(self.context.baseController, sel(@"device"))
}


#pragma mark - lazy
- (UIButton *)closeBtn {
    if (!_closeBtn) {
        _closeBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        _closeBtn.frame = (CGRect){0,44,46,44};
        [_closeBtn setImage:[UIImage imageNamed:@"left_btn_return"] forState:UIControlStateNormal];
        [_closeBtn addTarget:self action:@selector(close:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _closeBtn;
}

- (UIButton *)deviceButton {
    if (!_deviceButton) {
        _deviceButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_deviceButton setImage:[UIImage imageNamed:@"device_D11"] forState:UIControlStateNormal];
        _deviceButton.frame = (CGRect){60,0,23,30};
        [_deviceButton addTarget:self action:@selector(device:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _deviceButton;
}

- (JCBoardRightItemView *)rightItemView {
    if (!_rightItemView) {
        _rightItemView = [[JCBoardRightItemView alloc] initWithMaxWidth:kSCREEN_WIDTH-98-16];
                          
//                          WithFrame:(CGRect){0,44,kSCREEN_WIDTH-115,48}];
    }
    return _rightItemView;
}

- (UIImageView *)connectImageView {
    if (!_connectImageView) {
        _connectImageView = [[UIImageView alloc] initWithFrame:(CGRect){0,0,13,13}];
        [_connectImageView setImage:[UIImage imageNamed:@"icn_disconnected"]];
    }
    return _connectImageView;
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self name:PrinterStatusNotification object:nil];
}
@end
