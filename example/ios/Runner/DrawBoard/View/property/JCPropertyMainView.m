//
//  JCPropertyMainView.m
//  ChenYin
//
//  Created by xingling xu on 2020/11/10.
//  Copyright © 2020 JCChenYin. All rights reserved.
//

#import "JCPropertyMainView.h"
#import "JCStyleContentView.h"
#import "JCTextStyleView.h"
#import "JCAlignView.h"
#import "JCFontSelectView.h"
#import "JCInputTextView.h"
#import "JCElementPropertyBar.h"
#import "JCMutablePropertyView.h"
#import "JCIconView.h"
#import "JCBorderView.h"
#import "JCLogoCategoryModel.h"
#import "JCIconBorderManager.h"
#import "JCLineStyleView.h"
#import "JCImagePropertyView.h"
#import "JCDateFormatView.h"
#import "JCSliderView.h"
#define ShowContent(className) [cls isEqual:[className class]]

#define BarData_Text @[BarData(@"写入", @"JCInputTextView"),BarData(@"样式", @"JCTextStyleView"),BarData(@"字体", @"JCFontSelectView"),BarData(@"对齐", @"JCAlignView")]

#define BarData_QRCode @[BarData(@"写入", @"JCInputTextView"),BarData(@"对齐", @"JCAlignView")]

#define BarData_BarCode BarData_QRCode

#define BarData_Icon @[BarData(@"对齐", @"JCAlignView")]

#define BarData_Border BarData_Icon

#define BarData_Line @[BarData(@"属性", @"JCLineStyleView"),BarData(@"对齐", @"JCAlignView")]

#define BarData_Image @[BarData(@"微调", @"JCImagePropertyView"),BarData(@"对齐", @"JCAlignView")]

#define BarData_DateFormat @[BarData(@"时间", @"JCDateFormatView"),BarData(@"样式", @"JCTextStyleView"),BarData(@"字体", @"JCFontSelectView"),BarData(@"对齐", @"JCAlignView")]

#define BarData_MutableElements @[BarData(@"对齐", @"JCAlignView")]



@interface JCPropertyMainView()
@property (nonatomic, strong) JCStyleContentView *contentView;
/** 位移、格式等切换条 */
@property (nonatomic, strong) JCElementPropertyBar *bar;
/** 当前元素 */
@property (nonatomic, strong) JCElementBaseView *element;
/** 上一个选中的元素 */
@property (nonatomic, strong) JCElementBaseView *previousElement;
//  ↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓以下为各种属性编辑view
@property (nonatomic, strong) JCTextStyleView *textStyleView;
@property (nonatomic, strong) JCAlignView *alignView;
@property (nonatomic, strong) JCFontSelectView *fontSelectView;
@property (nonatomic, strong) JCInputTextView *inputTextView;
@property (nonatomic, strong) JCMutablePropertyView *mutableView;
@property (nonatomic, strong) JCIconView *iconView;
@property (nonatomic, strong) JCBorderView *borderView;
@property(nonatomic,strong)JCLineStyleView *lineStyleView;
@property(nonatomic,strong)JCImagePropertyView *imagePropertyView;
@property(nonatomic,strong)JCDateFormatView *dateFormatView;

@end

@implementation JCPropertyMainView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = [UIColor whiteColor];
        self.layer.borderWidth = 1;
        self.layer.borderColor = XY_HEX_RGB(0xf5f5f5).CGColor;
        UIBezierPath *maskPath = [UIBezierPath bezierPathWithRoundedRect:self.bounds byRoundingCorners:UIRectCornerTopLeft | UIRectCornerTopRight cornerRadii:CGSizeMake(12, 12)];
        CAShapeLayer *maskLayer = [[CAShapeLayer alloc] init];
        maskLayer.frame = self.bounds;
        maskLayer.path = maskPath.CGPath;
        self.layer.mask = maskLayer;
        [self loadRootView];
    }
    return self;
}

- (void)loadRootView {
    [self addSubview:self.bar];
    [self addSubview:self.contentView];
}

- (void)refreshElementInfo {
    if (self.element) {
        self.contentView.element = self.element;
    }
}

- (void)hiddenContentView {
    self.previousElement = nil;
}

- (void)setElementInfo:(NSDictionary *)elementInfo {
    JCElementBaseView *element = elementInfo[JCKEY_Element];
    BOOL mutableSelect = [elementInfo[JCKEY_Mutable] integerValue] > 0;
    NSArray *mutableElements = (NSArray *)elementInfo[JCKEY_MutableElements];
    if (mutableSelect) {
        [self mutableSelectWith:mutableElements];
    } else {
        [self sigleSelectWith:element];
    }
}

- (void)showKeyBoardWith:(JCElementBaseView *)element {
    if (!element) return;
    self.element = element;
    [self.contentView removeFromSuperview];
    if ([self.element isKindOfClass:[JCElementTextImage class]]) {
        [self.bar refrehWithDatas:BarData_Text select:0];
    } else if ([self.element isKindOfClass:[JCElementQRCode class]]) {
        [self.bar refrehWithDatas:BarData_QRCode select:0];
    } else if ([self.element isKindOfClass:[JCElementBarCode class]]) {
        [self.bar refrehWithDatas:BarData_BarCode select:0];
    }
    [self showContentViewWithContentClass:[JCInputTextView class]];
    self.contentView.element = self.element;
    [self addSubview:self.contentView];
    self.previousElement = element;
    JCPerformSelectorWithOneObject(self.context.baseController, sel(@"showCommonKeyBoard:"), self.element);
}

//- (void)showIconAndBorderPropertyViewWith:(JCElementBaseView *)element {
//    if (!element) return;
//    self.element = element;
//    [self.contentView removeFromSuperview];
//    switch (((JCElementLogoView *)element).type) {
//        case JCElementLogoViewTypeIcon:
//            [self.bar refrehWithDatas:BarData_Icon select:0];
//            break;
//        case JCElementLogoViewTypeBorder:
//            [self.bar refrehWithDatas:BarData_Border select:0];
//            break;
//        default:
//            break;
//    }
//  
//    [self showContentViewWithContentClass:[JCAlignView class]];
//    self.contentView.element = self.element;
//    [self addSubview:self.contentView];
//    self.previousElement = element;
//}
//线条
- (void)showLineViewWith:(JCElementBaseView *)element {
    if (!element) return;
    self.element = element;
    [self.contentView removeFromSuperview];
    [self.bar refrehWithDatas:BarData_Line select:0];
    [self showContentViewWithContentClass:[JCLineStyleView class]];
    self.contentView.element = self.element;
    [self addSubview:self.contentView];
    self.previousElement = element;
    //不用选中上次的属性
//    [self selectBarAtIndex:element.selectedBarIndex];
//    JCPerformSelectorWithOneObject(self.context.baseController, sel(@"showCommonKeyBoard:"), self.element);
}
- (void)showImageViewWith:(JCElementBaseView *)element{
    [self showImageViewWith:element isFirstAdd:false];
}
//图片:其中 border, icon, image公用图片属性,根据情况选择标签
- (void)showImageViewWith:(JCElementBaseView *)element isFirstAdd:(BOOL)isFirstAdd {
    if (!element) return;
    self.element = element;
    [self.contentView removeFromSuperview];
    switch (((JCElementLogoView *)element).type) {
        case JCElementLogoViewTypeImage:
          
            [self.bar refrehWithDatas:BarData_Image select:0];
            [self showContentViewWithContentClass:[JCImagePropertyView class]];
           
            break;
        case JCElementLogoViewTypeIcon:
            if (!isFirstAdd) {
                [self.bar refrehWithDatas:BarData_Icon select:0];
                [self showContentViewWithContentClass:[JCAlignView class]];
            }
            break;
        case JCElementLogoViewTypeBorder:
            if (!isFirstAdd) {
                [self.bar refrehWithDatas:BarData_Border select:0];
                [self showContentViewWithContentClass:[JCAlignView class]];
            }
            break;
        default:
            break;
    }
    
    self.contentView.element = self.element;
    [self addSubview:self.contentView];
    self.previousElement = element;
    //不用选中上次的属性
//    [self selectBarAtIndex:element.selectedBarIndex];
//    JCPerformSelectorWithOneObject(self.context.baseController, sel(@"showCommonKeyBoard:"), self.element);
}
//日期
- (void)showDateFormatViewWith:(JCElementBaseView *)element{
    if (!element) return;
    self.element = element;
    [self.contentView removeFromSuperview];
    [self.bar refrehWithDatas:BarData_DateFormat select:0];
    [self showContentViewWithContentClass:[JCDateFormatView class]];
    self.contentView.element = self.element;
    [self addSubview:self.contentView];
    self.previousElement = element;
    //不用选中上次的属性
//    [self selectBarAtIndex:element.selectedBarIndex];
//    JCPerformSelectorWithOneObject(self.context.baseController, sel(@"showCommonKeyBoard:"), self.element);
}


- (void)sigleSelectWith:(JCElementBaseView *)element {
    if (!element) return;
    self.element = element;
    // 如果之前选择的元素和现在选择的元素类型不同，那么切换属性，加载默认属性，否则保持属性框一致并刷新内容
    if (![self.previousElement.class isEqual:element.class]) {
        [self.contentView removeFromSuperview];
        [self loadDefaultContentViewWithElement:element];
    }
    self.contentView.element = self.element;
    [self addSubview:self.contentView];
    self.previousElement = element;
}

- (void)mutableSelectWith:(NSArray *)selectElements {
    if (selectElements.count == 0) return;
    [self.contentView removeFromSuperview];
    [self.bar refrehWithDatas:BarData_MutableElements select:0];
    self.mutableView.mutableElements = selectElements;
    self.contentView = self.mutableView;
    JCPerformSelectorWithOneObject(self.context.drawBoard, sel(@"currentSetAligment:"),@"1");
    [self addSubview:self.contentView];
}

// 展示图标
- (void)showIconListView {
    if ([JCIconView iconTitleDatas].count <= 1) {
        [[JCIconBorderManager sharedManager] requestIconItem:^(BOOL success) {
            [self.bar refrehWithDatas:[JCIconView iconTitleDatas] select:0];
        }];
    } else {
        [self.bar refrehWithDatas:[JCIconView iconTitleDatas] select:0];
    }
    [self showContentViewWithContentClass:[JCIconView class]];
    
}


// 展示边框
- (void)showBorderListView {
    if ([JCBorderView borderTitleDatas].count <= 1) {
        [[JCIconBorderManager sharedManager] requestBorderItem:^(BOOL success) {
            [self.bar refrehWithDatas:[JCBorderView borderTitleDatas] select:0];
        }];
    } else {
        [self.bar refrehWithDatas:[JCBorderView borderTitleDatas] select:0];
    }
    
    [self showContentViewWithContentClass:[JCBorderView class]];
}

- (BOOL)currentContentViewNeedStayVisible {
    return ([self.contentView isKindOfClass:[JCIconView class]] || [self.contentView isKindOfClass:[JCBorderView class]]);
}

- (void)showContentViewWithContentClass:(Class)cls {
    [self.contentView removeFromSuperview];
    if (ShowContent(JCTextStyleView)) {
        self.textStyleView.accessoryType = JCSliderAccessoryButton;
        self.contentView = self.textStyleView;
    } else if (ShowContent(JCAlignView)) {
        self.contentView = self.alignView;
    } else if (ShowContent(JCFontSelectView)) {
        self.contentView = self.fontSelectView;
        //每次都要刷新字体列表,因为需要重新排序
            [self.fontSelectView sortDatas];
    } else if (ShowContent(JCInputTextView)) {
        self.contentView = self.inputTextView;
    } else if (ShowContent(JCIconView)) {
        self.contentView = self.iconView;
        [self.iconView showUsedData];
    } else if (ShowContent(JCBorderView)) {
        self.contentView = self.borderView;
        [self.borderView showUsedData];
    }else if (ShowContent(JCLineStyleView)) {
        self.lineStyleView.accessoryType = JCSliderAccessoryNone;
        self.contentView = self.lineStyleView;
//        [self.bar  refrehWithDatas:BarData_Line select:0];
//        [self.borderView showUsedData];
    }else if(ShowContent(JCImagePropertyView)) {
        //
        self.imagePropertyView.accessoryType = JCSliderAccessoryNone;
        self.contentView = self.imagePropertyView;
//        [self.bar  refrehWithDatas:BarData_Image select:0];
//        [self.borderView showUsedData];
    }else if(ShowContent(JCDateFormatView)) {
        self.contentView = self.dateFormatView;
//        [self.bar  refrehWithDatas:BarData_Image select:0];
//        [self.borderView showUsedData];
    }
    [self addSubview:self.contentView];
    if (self.element) {
        self.contentView.element = self.element;
    }
}
#pragma mark - 选中画板时弹出对应的属性编辑器
- (void)loadDefaultContentViewWithElement:(JCElementBaseView *)element {
    if (!element) return;
    if ([element isMemberOfClass:[JCElementTextImage class]]) {
        [self showContentViewWithContentClass:[JCTextStyleView class]];
    } else if ([element isKindOfClass:[JCElementQRCode class]]) {
        [self showContentViewWithContentClass:[JCAlignView class]];
    } else if ([element isKindOfClass:[JCElementLogoView class]]) {
        [self showContentViewWithContentClass:[JCAlignView class]];
    }else if ([element isKindOfClass:[JCElementLine class]]) {
        [self showContentViewWithContentClass:[JCLineStyleView class]];
    }else if ([element isKindOfClass:[JCElementLogoView class]]) {
        [self showContentViewWithContentClass:[JCImagePropertyView class]];
    }else if ([element isKindOfClass:[JCElementTime class]]) {
        [self showContentViewWithContentClass:[JCDateFormatView class]];
    }
    [self refreshBar];
}

- (void)selectBarAtIndex:(NSInteger)index {
    [self.bar selectIndex:index];
}

- (void)refreshBar {
    if ([self.element isKindOfClass:[JCElementTextImage class]]) {
        [self.bar refrehWithDatas:BarData_Text select:1];
    } else if ([self.element isKindOfClass:[JCElementQRCode class]]) {
        [self.bar refrehWithDatas:BarData_QRCode select:1];
    } else if ([self.element isKindOfClass:[JCElementLogoView class]]) {
        [self.bar refrehWithDatas:BarData_MutableElements select:0];
    } else if ([self.element isKindOfClass:[JCElementLine class]]) {
        [self.bar refrehWithDatas:BarData_Line select:0];
    } else if ([self.element isKindOfClass:[JCElementTime class]]) {
        [self.bar refrehWithDatas:BarData_DateFormat select:0];
    }
}
#pragma mark - 属性详情顶部bar点击事件
- (void)propertyBar:(JCElementPropertyBar *)bar didSelectIndex:(NSInteger)index withData:(JCBarData *)data {
    // 针对图标边框
    if (data.userInfo) {
        JCLogoCategoryModel *model = (JCLogoCategoryModel *)data.userInfo;
        if ([model isKindOfClass:[JCLogoCategoryModel class]]) {
            if ([self.contentView isKindOfClass:[JCIconView class]]) {
                [self.iconView selectCategoryId:model.categoryId];
            } else if ([self.contentView isKindOfClass:[JCBorderView class]]) {
                [self.borderView selectCategoryId:model.categoryId];
            }
        } else {
            if ([self.contentView isKindOfClass:[JCIconView class]]) {
                [self.iconView showUsedData];
            } else if ([self.contentView isKindOfClass:[JCBorderView class]]) {
                [self.borderView showUsedData];
            }
        }
    } else {// 其他
        NSString *propertyClsName = data.propertyClsName;
        self.element.selectedBarIndex = index;
        if ([propertyClsName isEqualToString:@"JCInputTextView"]) {
            JCPerformSelectorWithOneObject(self.context.baseController, sel(@"showCommonKeyBoard:"), self.element);
        } else {
            [self showContentViewWithContentClass:NSClassFromString(propertyClsName)];
            JCPerformSelector(self.context.baseController, sel(@"selectOtherBarItem"))
        }
    }
}


#pragma mark - lazy
- (JCElementPropertyBar *)bar {
    if (!_bar) {
        _bar = [[JCElementPropertyBar alloc] initWithFrame:(CGRect){0,0,kSCREEN_WIDTH,PropertyBarHeight}];
        _bar.delegate = self;
    }
    return _bar;
}

- (JCStyleContentView *)contentView {
    if (!_contentView) {
        _contentView = [[JCStyleContentView alloc] initWithFrame:(CGRect){0,PropertyBarHeight,SCREEN_WIDTH,Property_height}];
        _contentView.backgroundColor = [UIColor whiteColor];
    }
    return _contentView;
}

- (JCTextStyleView *)textStyleView {
    if (!_textStyleView) {
        _textStyleView = [[JCTextStyleView alloc] initWithFrame:(CGRect){0,PropertyBarHeight,kSCREEN_WIDTH,Property_height}];
    }
    return _textStyleView;
}

- (JCAlignView *)alignView {
    if (!_alignView) {
        _alignView = [[JCAlignView alloc] initWithFrame:(CGRect){0,PropertyBarHeight,kSCREEN_WIDTH,Property_height}];
    }
    return _alignView;
}

- (JCFontSelectView *)fontSelectView {
    if (!_fontSelectView) {
        _fontSelectView = [[JCFontSelectView alloc] initWithFrame:(CGRect){0,PropertyBarHeight,kSCREEN_WIDTH,Property_height}];
    }
    return _fontSelectView;
}

- (JCInputTextView *)inputTextView {
    if (!_inputTextView) {
        _inputTextView = [[JCInputTextView alloc] initWithFrame:(CGRect){0,PropertyBarHeight,kSCREEN_WIDTH,Property_height}];
    }
    return _inputTextView;
}

- (JCMutablePropertyView *)mutableView {
    if (!_mutableView) {
        _mutableView = [[JCMutablePropertyView alloc] initWithFrame:(CGRect){0,PropertyBarHeight,kSCREEN_WIDTH,Property_height}];
    }
    return _mutableView;
}

- (JCIconView *)iconView {
    if (!_iconView) {
        _iconView = [[JCIconView alloc] initWithFrame:(CGRect){0,PropertyBarHeight,kSCREEN_WIDTH,Property_height}];
    }
    return _iconView;
}

- (JCBorderView *)borderView {
    if (!_borderView) {
        _borderView = [[JCBorderView alloc] initWithFrame:(CGRect){0,PropertyBarHeight,kSCREEN_WIDTH,Property_height}];
    }
    return _borderView;
}

- (JCLineStyleView *)lineStyleView {
    if (!_lineStyleView) {
        _lineStyleView = [[JCLineStyleView alloc] initWithFrame:(CGRect){0,PropertyBarHeight,kSCREEN_WIDTH,Property_height}];
    }
    return _lineStyleView;
}

//imagePropertyView
- (JCImagePropertyView *)imagePropertyView {
    if (!_imagePropertyView) {
        _imagePropertyView = [[JCImagePropertyView alloc] initWithFrame:(CGRect){0,PropertyBarHeight,kSCREEN_WIDTH,Property_height}];
    }
    return _imagePropertyView;
}

- (JCDateFormatView *)dateFormatView {
    if (!_dateFormatView) {
        _dateFormatView = [[JCDateFormatView alloc] initWithFrame:(CGRect){0,PropertyBarHeight,kSCREEN_WIDTH,Property_height}];
    }
    return _dateFormatView;
}

@end
