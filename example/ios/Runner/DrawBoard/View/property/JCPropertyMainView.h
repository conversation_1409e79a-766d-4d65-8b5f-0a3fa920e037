//
//  JCPropertyMainView.h
//  ChenYin
//
//  Created by xingling xu on 2020/11/10.
//  Copyright © 2020 JCChenYin. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface JCPropertyMainView : UIView

@property (nonatomic, strong) NSDictionary *elementInfo;

- (void)refreshElementInfo;

- (void)showContentViewWithContentClass:(Class)cls;

- (void)selectBarAtIndex:(NSInteger)index;

/// 显示对齐属性view
/// @param element 
//- (void)showIconAndBorderPropertyViewWith:(JCElementBaseView *)element;
// 展示图标
- (void)showIconListView;
// 展示边框
- (void)showBorderListView;
// 展示线条
- (void)showLineViewWith:(JCElementBaseView *)element;
- (BOOL)currentContentViewNeedStayVisible;

- (void)showKeyBoardWith:(JCElementBaseView *)element;

/// 展示logoimage的属性页面
/// @param element
- (void)showImageViewWith:(JCElementBaseView *)element;

/// 展示logoimage的属性页面
/// @param element
/// @param isFirstAdd 是否为第一次添加;如果元素为第一次添加,那么不显示对应的属性页面,让用户可以继续添加图形
- (void)showImageViewWith:(JCElementBaseView *)element isFirstAdd:(BOOL)isFirstAdd;
- (void)showDateFormatViewWith:(JCElementBaseView *)element;
@end

NS_ASSUME_NONNULL_END
