//
//  JCTagListLeftCell.m
//  XYFrameWork
//
//  Created by xingling xu on 2020/11/24.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import "JCTagListLeftCell.h"

@interface JCTagListLeftCell ()
@property (nonatomic, strong) UILabel *label;
@end

@implementation JCTagListLeftCell

+ (JCTagListLeftCell *)cellWithTableView:(UITableView *)tableView {
    static NSString *cellID = @"JCTagListLeftCell";
    JCTagListLeftCell *myCell = (JCTagListLeftCell *)[tableView dequeueReusableCellWithIdentifier:cellID];
    if (myCell == nil) {
        myCell = [[JCTagListLeftCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:cellID];
    }
    return myCell;
}

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        self.contentView.backgroundColor = XY_HEX_RGB(0xffffff);
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        
        [self.contentView addSubview:self.label];
        [self.label mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.trailing.top.bottom.equalTo(self.contentView);
        }];
    }
    return self;
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];
    self.label.textColor = selected?XY_HEX_RGB(0xFB4B42):XY_HEX_RGB(0x1d1d1d);
    self.label.font = selected?MY_FONT_Bold(14):MY_FONT_Regular(14);
}

- (void)setTitle:(NSString *)title {
    _title = title;
    self.label.text = title;
    [self setNeedsLayout];
}


#pragma mark - lazy
- (UILabel *)label {
    if (!_label) {
        _label = [[UILabel alloc] init];
        _label.textAlignment = NSTextAlignmentCenter;
        _label.font = MY_FONT_Regular(14);
        _label.textColor = XY_HEX_RGB(0x1d1d1d);
    }
    return _label;
}
@end
