//
//  JCTagImageTableViewCell.m
//  XYFrameWork
//
//  Created by xingling xu on 2020/11/24.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import "JCTagImageTableViewCell.h"

@interface JCTagImageTableViewCell ()
@property (nonatomic, strong) UIImageView *icon;
@property (nonatomic, strong) UILabel *label;
@property (nonatomic, strong) UIButton *buyButton;
@end

@implementation JCTagImageTableViewCell

+ (JCTagImageTableViewCell *)cellWithTableView:(UITableView *)tableView {
    static NSString *cellID = @"JCTagImageTableViewCell";
    JCTagImageTableViewCell *myCell = (JCTagImageTableViewCell *)[tableView dequeueReusableCellWithIdentifier:cellID];
    if (myCell == nil) {
        myCell = [[JCTagImageTableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:cellID];
    }
    return myCell;
}

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        self.contentView.backgroundColor = [UIColor whiteColor];
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        
        [self.contentView addSubview:self.icon];
        [self.contentView addSubview:self.label];
        [self.contentView addSubview:self.buyButton];
//        [self.maskView mas_makeConstraints:^(MASConstraintMaker *make) {
//            make.leading.trailing.top.bottom.equalTo(self.icon);
//        }];
        [self.label mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(self.icon);
            make.top.equalTo(self.icon.mas_bottom).offset(10);
            make.height.mas_equalTo(18);
            make.width.mas_equalTo(200);
        }];
        [self.buyButton mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.height.width.mas_equalTo(40);
                    make.centerY.mas_equalTo(self.icon);
                    make.trailing.mas_equalTo(self.contentView.mas_trailing).offset(-10);
        }];
    }
    return self;
}

- (void)setModel:(JCTagImageModel *)model {
    _model = model;
    [self reloadImage];
}

- (void)reloadImage {
    if (!self.model) {
        self.icon.image = nil;
        return;
    }
    CGFloat width = self.model.width.floatValue;
    CGFloat height = self.model.height.floatValue;
    CGFloat iconW,iconH;
    if (width >= height) {
        iconW = 200;
        iconH = 200*height/width;
    } else {
        iconW = 150*width/height;
        iconH = 150;
    }
    [self.icon mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(iconW);
        make.height.mas_equalTo(iconH);
        make.top.equalTo(self.contentView).offset(15);
        make.centerX.equalTo(self.contentView).offset(-16);//向左偏移一个buybutton的宽度
    }];
    NSString *url = self.model.url;
    NSArray *backimgs = self.model.backgroundImages;
    if (backimgs.count > 0) {
        url = backimgs.firstObject;
    }
    [self.icon jc_setImageWithUrl:url];
    self.label.text = self.model.name;
    self.buyButton.hidden = ![self.model.showShoppingCart boolValue];
    if ([self.model.isUpperShelf boolValue]) {
        [self.buyButton setImage:[UIImage imageNamed:@"shopping_cart"] forState:UIControlStateNormal];
    }else{
        [self.buyButton setImage:[UIImage imageNamed:@"shopping_cart_disable"] forState:UIControlStateNormal];
    }
}


- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];
    self.icon.layer.borderColor = selected?XY_HEX_RGB(0x999999).CGColor:XY_HEX_RGB(0xe2e2e2).CGColor;
    self.icon.layer.borderWidth = 0.5;
    self.icon.layer.cornerRadius = 14;
    self.icon.layer.masksToBounds = YES;
}

- (void)buyAction{
    if (![self.model.isUpperShelf boolValue]) {
        return;
    }
    if (self.buyActionCallBack) {
        self.buyActionCallBack(self.model);
    }
}
#pragma mark - lazy
- (UILabel *)label {
    if (!_label) {
        _label = [[UILabel alloc] init];
        _label.textAlignment = NSTextAlignmentCenter;
        _label.font = MY_FONT_Regular(13);
        _label.textColor = XY_HEX_RGB(0x595959);
    }
    return _label;
}

- (UIImageView *)icon {
    if (!_icon) {
        _icon = [[UIImageView alloc] init];
    }
    return _icon;
}

- (UIButton *)buyButton {
    if (!_buyButton) {
        _buyButton = [[UIButton alloc] init];
        _buyButton.layer.cornerRadius = 20;
        _buyButton.backgroundColor = [UIColor jk_colorWithHex:0xF5F5F5];
        _buyButton.hidden = true;
        [_buyButton addTarget:self action:@selector(buyAction) forControlEvents:UIControlEventTouchUpInside];
        [_buyButton setImage:[UIImage imageNamed:@"shopping_cart"] forState:UIControlStateNormal];
    }
    return _buyButton;
}
#pragma mark - static
+ (CGFloat)heightFor:(JCTagImageModel *)model {
    if (!model) return 65;
    CGFloat width,height;
    width = model.width.floatValue;
    height = model.height.floatValue;
    CGFloat imageHeight;
    if (width >= height) {
        imageHeight = 200*height/width;
    } else {
        imageHeight = 150;
    }
    return imageHeight + 43;
}

@end
