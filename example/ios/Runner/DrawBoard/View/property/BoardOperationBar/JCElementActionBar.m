//
//  JCElementActionBar.m
//  ChenYin
//
//  Created by xingling xu on 2020/11/10.
//  Copyright © 2020 JCChenYin. All rights reserved.
//

#import "JCElementActionBar.h"
#import "JCActionButton.h"

static const CGFloat itemHeight = 44.f;
static const CGFloat topOffset = 10.f;
static const CGFloat horizontalOffset = 10.f;
static const NSInteger subviewBaseTag = 2000;

@interface JCElementActionBar ()
@property (nonatomic, strong) NSArray *actionTypes;
@property (nonatomic, assign) CGFloat itemWidth;

@property (nonatomic, strong) UIView *subviewsContent;

/** 防丢器模式 */
@property (nonatomic, assign) JCBoardMode boardMode;
@end

@implementation JCElementActionBar

- (instancetype)initWithFrame:(CGRect)frame
                    boardMode:(JCBoardMode)boardMode {
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = XY_HEX_RGB(0xf2f2f2);
        self.boardMode = boardMode;
        [self setUp];
    }
    return self;
}

- (BOOL)isAntiLostMode {
    return _boardMode == JCBoardModeAntiLost;
}

- (void)setUp {
    if (self.isAntiLostMode) {
        self.actionTypes = @[@(JCElementAction_Delete),@(JCElementAction_Copy),@(JCElementAction_Rotate),
                             @(JCElementAction_NoUndo),@(JCElementAction_NoRedo),@(JCElementAction_Reset)];
    } else {
        self.actionTypes = @[@(JCElementAction_Delete),@(JCElementAction_Copy),@(JCElementAction_Rotate),
                             @(JCElementAction_NoUndo),@(JCElementAction_NoRedo),@(JCElementAction_Lock),
                             @(JCElementAction_SigleSelect),@(JCElementAction_Reset)];
    }

    self.itemWidth = (kSCREEN_WIDTH - horizontalOffset * 2)/(_actionTypes.count);
    
    [self addSubview:self.subviewsContent];
    [self loadRootItem];
}

- (void)loadRootItem {
    JCWeakSelf
    for (NSInteger i = 0; i < [_actionTypes count]; i ++) {
        JCElementAction type = [_actionTypes[i] integerValue];
        ElementButtonInfo *info = [ElementButtonInfo buttonInfoWithAction:type];
        JCActionButton *btn = [[JCActionButton alloc] initWithWidth:_itemWidth buttonInfo:info click:^(ElementButtonInfo * _Nonnull info) {
            JCStrongSelf
            JCPerformSelectorWithOneObject(self.context.baseController, sel(@"actionElementWith:"), info.selName)
        }];
        btn.tag = subviewBaseTag + i;
        
        btn.frame = (CGRect){_itemWidth*i,0+topOffset,_itemWidth,itemHeight};
        [self.subviewsContent addSubview:btn];
    }
    
    [self objectHandleActionUnable];
    
    ElementButtonInfo *info = [ElementButtonInfo buttonInfoWithAction:JCElementAction_Reset];
    [[self findButtonWithType:JCElementAction_Reset] refreshButtonWith:info scale:1.0 click:^(ElementButtonInfo * _Nonnull info) {
        JCStrongSelf
        JCPerformSelector(self.context.drawBoard, sel(info.selName))
    }];
}

- (JCActionButton *)findButtonWithType:(JCElementAction)type {
    if ([_actionTypes containsObject:@(type)]) {
        NSInteger index = [_actionTypes indexOfObject:@(type)];
        return [self.subviewsContent viewWithTag:subviewBaseTag + index];
    }
    return nil;
}

#pragma mark - selector
- (void)elementIsLock:(BOOL)lock {
    JCActionButton *lockButton = [self findButtonWithType:JCElementAction_Lock];
    JCElementAction action = lock?JCElementAction_UnLock:JCElementAction_Lock;
    [lockButton refreshButtonWith:[ElementButtonInfo buttonInfoWithAction:action] click:nil];
}

- (void)canUndo:(BOOL)canUndo {
    JCActionButton *button = [self findButtonWithType:JCElementAction_NoUndo];
    JCElementAction action = canUndo?JCElementAction_Undo:JCElementAction_NoUndo;
    [button refreshButtonWith:[ElementButtonInfo buttonInfoWithAction:action] click:nil];
}

- (void)canRedo:(BOOL)canRedo {
    JCActionButton *button = [self findButtonWithType:JCElementAction_NoRedo];
    JCElementAction action = canRedo?JCElementAction_Redo:JCElementAction_NoRedo;
    [button refreshButtonWith:[ElementButtonInfo buttonInfoWithAction:action] click:nil];
}

- (void)mutableSelected:(BOOL)mutableSelect {
    if (self.isAntiLostMode) {
        return;
    }
    JCActionButton *button = [self findButtonWithType:JCElementAction_SigleSelect];
    JCElementAction action = mutableSelect?JCElementAction_Mutiple:JCElementAction_SigleSelect;
    [button refreshButtonWith:[ElementButtonInfo buttonInfoWithAction:action] click:nil];
}

- (void)refreshScale:(CGFloat)scale {
    JCActionButton *button = [self findButtonWithType:JCElementAction_Reset];
    [button refreshButtonWith:[ElementButtonInfo buttonInfoWithAction:JCElementAction_Reset] scale:scale click:nil];
}

- (void)objectHandleActionEnable  {
    [[self findButtonWithType:JCElementAction_Delete] setEnable:YES];
    [[self findButtonWithType:JCElementAction_Copy] setEnable:YES];
    [[self findButtonWithType:JCElementAction_Rotate] setEnable:YES];
    [[self findButtonWithType:JCElementAction_Lock] setEnable:YES];
}

- (void)objectHandleActionUnable {
    [[self findButtonWithType:JCElementAction_Delete] setEnable:NO];
    [[self findButtonWithType:JCElementAction_Copy] setEnable:NO];
    [[self findButtonWithType:JCElementAction_Rotate] setEnable:NO];
    [[self findButtonWithType:JCElementAction_Lock] setEnable:NO];
}

#pragma mark - getter
- (UIView *)subviewsContent {
    if (!_subviewsContent) {
        _subviewsContent = [[UIView alloc] initWithFrame:(CGRect){horizontalOffset,0,_itemWidth * self.actionTypes.count,itemHeight}];
        _subviewsContent.backgroundColor = [UIColor clearColor];
    }
    return _subviewsContent;
}

@end
