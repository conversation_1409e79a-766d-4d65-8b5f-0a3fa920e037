//
//  JCElementActionBar.h
//  ChenYin
//
//  Created by xingling xu on 2020/11/10.
//  Copyright © 2020 JCChenYin. All rights reserved.
//
/// 画板工具栏
#import <UIKit/UIKit.h>
#import "JCTemplateEditController.h"

NS_ASSUME_NONNULL_BEGIN

@interface JCElementActionBar : UIView

- (instancetype)initWithFrame:(CGRect)frame
                    boardMode:(JCBoardMode)boardMode;

// 是否锁定状态
- (void)elementIsLock:(BOOL)lock;
// 是否可撤销
- (void)canUndo:(BOOL)canUndo;
// 是否可恢复
- (void)canRedo:(BOOL)canRedo;

- (void)refreshScale:(CGFloat)scale;

- (void)mutableSelected:(BOOL)mutableSelect;

// 选中时对象操作的按钮 可用性修改
- (void)objectHandleActionEnable;
- (void)objectHandleActionUnable;

@end

NS_ASSUME_NONNULL_END
