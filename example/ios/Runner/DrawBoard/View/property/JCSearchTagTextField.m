//
//  UITextField+OptionalView.m
//  XYFrameWork
//
//  Created by xingling xu on 2020/11/23.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import "JCSearchTagTextField.h"

@implementation JCSearchTagTextField

- (CGRect)leftViewRectForBounds:(CGRect)bounds {
    CGRect iconRect = [super leftViewRectForBounds:bounds];
    iconRect.origin.x += 12; //像右边偏12
    return iconRect;
}

- (CGRect)rightViewRectForBounds:(CGRect)bounds {
    CGRect iconRect = [super rightViewRectForBounds:bounds];
    iconRect.origin.x -= 12; //像左边偏移12
    return iconRect;
}

- (CGRect)textRectForBounds:(CGRect)bounds {
    return CGRectInset(bounds, 34, 0);
}

//控制文本的位置
- (CGRect)editingRectForBounds:(CGRect)bounds {
    
    return CGRectInset(bounds, 34, 0);
}
@end
