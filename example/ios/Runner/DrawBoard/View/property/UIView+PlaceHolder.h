//
//  UIView+PlaceHolder.h
//  Runner
//
//  Created by xingling xu on 2020/12/29.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN
typedef NS_ENUM(NSUInteger, JCNoDataAction) {
    JCNoDataActionNone,
    JCNoDataActionReload,
    JCNoDataActionBackforward,
};
@interface UIView (PlaceHolder)

- (void)showNoDataTipView:(NSString *)message;
- (void)showNoDataTipView;
- (void)hiddenNoDataTipView;
- (void)showNoNetWorkView:(NSString *)message;
- (void)showNoNetWorkView:(NSString *)message Action:(JCNoDataAction)action completion:(void(^)(JCNoDataAction type))completion;
@end

NS_ASSUME_NONNULL_END
