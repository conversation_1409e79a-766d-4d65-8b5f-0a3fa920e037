//
//  JCDrawNavigationBar4AntiLost.m
//  XYFrameWork
//
//  Created by xingling xu on 2020/12/9.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import "JCDrawNavigationBar4AntiLost.h"
#import "JCBoardRightItemView.h"
#import "JCApplicationManager.h"
#import "JCBluetoothManager.h"
#import "JCDrawNavigationBar.h"

@interface JCDrawNavigationBar4AntiLost ()
@property (nonatomic, strong) UIButton *closeBtn;
@property (nonatomic, strong) UIButton *printButton;
@property (nonatomic, strong) UILabel *titleTextView;
@end

@implementation JCDrawNavigationBar4AntiLost
- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = [UIColor whiteColor];
        [self loadRootView];
    }
    return self;
}

- (void)refreshSizeTitle:(NSString *)title {
    [self.titleTextView setText:title ?: @"防丢器"];
}

- (UIButton *)printButton {
    if (!_printButton) {
        _printButton = [UIButton buttonWithType:UIButtonTypeCustom];
        _printButton.frame = (CGRect){self.width-62,6,62,32};
        _printButton.backgroundColor = XY_HEX_RGB(0xFB4B42);
        _printButton.layer.cornerRadius = 16;
        _printButton.layer.masksToBounds = YES;
        _printButton.titleLabel.font = MY_FONT_Medium(14);
        [_printButton setTitle:@"同步" forState:UIControlStateNormal];
        [_printButton addTarget:self action:@selector(print:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _printButton;
}

- (UILabel *)titleTextView {
    if (!_titleTextView) {
        _titleTextView = [[UILabel alloc] init];
        [_titleTextView setFont:[UIFont systemFontOfSize:17.0f]];
        [_titleTextView setTextColor:XY_HEX_RGB(0x262626)];
        [_titleTextView setTextAlignment:NSTextAlignmentCenter];
        _titleTextView.numberOfLines = 1;
        _titleTextView.lineBreakMode = NSLineBreakByTruncatingTail;
    }
    return _titleTextView;
}

- (void)print:(UIButton *)button {
    JCPerformSelector(self.context.baseController, sel(@"printTemplate"))
}

- (void)loadRootView {
    [self addSubview:self.closeBtn];
    [self addSubview:self.printButton];
    [self addSubview:self.titleTextView];

    self.closeBtn.bottom = self.height;
    self.printButton.right = self.width - 16;
    self.printButton.bottom = self.height - 8;
    
    JCWeakSelf
    [self.titleTextView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(weakSelf).with.offset(80);
        make.right.equalTo(weakSelf).with.offset(-80);
        make.height.mas_equalTo(46);
        make.bottom.equalTo(weakSelf);
    }];
}

#pragma mark - selector
- (void)close:(UIButton *)button {
    JCPerformSelector(self.context.baseController, sel(@"close"))
}

- (void)device:(UIButton *)button {
    JCPerformSelector(self.context.baseController, sel(@"device"))
}


#pragma mark - lazy
- (UIButton *)closeBtn {
    if (!_closeBtn) {
        _closeBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        _closeBtn.frame = (CGRect){0,44,46,44};
        [_closeBtn setImage:[UIImage imageNamed:@"left_btn_return"] forState:UIControlStateNormal];
        [_closeBtn addTarget:self action:@selector(close:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _closeBtn;
}

- (void)dealloc {
}
@end
