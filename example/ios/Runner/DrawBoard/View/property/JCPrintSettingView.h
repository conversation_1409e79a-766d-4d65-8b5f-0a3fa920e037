//
//  JCPrintSettingView.h
//  XYFrameWork
//
//  Created by xingling xu on 2020/11/17.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN
@class JCPrintSettingView;
@protocol JCPrintSettingViewDelegate <NSObject>
- (void)settingView:(JCPrintSettingView *)view concentrate:(NSInteger)concentrate number:(NSInteger)number;
@end

@interface JCPrintSettingView : UIView
@property (nonatomic, weak) id  delegate;
- (instancetype)initWithDefaultConcentrate:(NSInteger)defaultConcentrate   min:(NSInteger)min max:(NSInteger)max;
- (void)show;
- (void)dismiss;
@end

NS_ASSUME_NONNULL_END
