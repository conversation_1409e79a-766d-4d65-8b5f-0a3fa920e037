//
//  JCBarCollectionCell.m
//  ChenYin
//
//  Created by xingling xu on 2020/11/10.
//  Copyright © 2020 JCChenYin. All rights reserved.
//

#import "JCBarCollectionCell.h"

@interface JCBarCollectionCell ()
@property (nonatomic, strong) UILabel *label;
@property (nonatomic, strong) UIView *backView;
@end

@implementation JCBarCollectionCell

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self addSubview:self.backView];
        [self addSubview:self.label];
    }
    return self;
}

- (void)setSelected:(BOOL)selected {
    [super setSelected:selected];
    UIColor *textColor = selected?XY_HEX_RGB(0xFB4B42):XY_HEX_RGB(0x262626);
    UIFont *font = selected?MY_FONT_Bold(14):MY_FONT_Regular(14);
    self.label.textColor = textColor;
    self.label.font = font;
    UIColor *backColor = selected?XY_HEX_RGB(0xF5F5F5):[UIColor clearColor];
    self.backView.backgroundColor = backColor;
}

#pragma mark - setter
- (void)setTitle:(NSString *)title {
    _title = title;
    
    UIFont *font = self.selected?MY_FONT_Bold(14):MY_FONT_Regular(14);
    CGSize strSize = [title sizeWithAttributes:@{NSFontAttributeName: font}];
    CGSize size = (CGSize){strSize.width+26,30};
    self.label.text = title;
    self.label.size = size;
    
    self.backView.size = size;
}

#pragma mark - getter
- (UILabel *)label {
    if (!_label) {
        _label = [[UILabel alloc] init];
        _label.font = MY_FONT_Regular(14);
        _label.textColor = XY_HEX_RGB(0x262626);
        _label.textAlignment = NSTextAlignmentCenter;
    }
    return _label;
}

- (UIView *)backView {
    if (!_backView) {
        _backView = [[UIView alloc] init];
        _backView.layer.cornerRadius = 15;
    }
    return _backView;;
}


+ (CGSize)size4Title:(NSString *)title {
    CGSize strSize = [title sizeWithAttributes:@{NSFontAttributeName: MY_FONT_Regular(14)}];
    CGSize size = (CGSize){strSize.width+26,30};
    return size;
}
@end
