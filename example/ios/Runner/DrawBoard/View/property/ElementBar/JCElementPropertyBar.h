//
//  JCElementPropertyBar.h
//  ChenYin
//
//  Created by xingling xu on 2020/11/10.
//  Copyright © 2020 JCChenYin. All rights reserved.
//
/// 属性详情导航条
#import <UIKit/UIKit.h>
#import "JCBarData.h"


@class JCElementPropertyBar;
@protocol PropertyBarDelegate <NSObject>
@optional
- (void)propertyBar:(JCElementPropertyBar *)bar didSelectIndex:(NSInteger)index withData:(JCBarData *)data;
@end


@interface JCElementPropertyBar : UIView
@property (nonatomic, weak) id  delegate;

- (instancetype)initWithDatas:(NSArray<JCBarData *> *)datas;
- (void)selectIndex:(NSInteger)selectIndex;
- (void)refrehWithDatas:(NSArray<JCBarData *> *)datas select:(NSInteger)selectedIndex;
- (void)folderButtonHidden;
@end


