//
//  JCBarData.h
//  XYFrameWork
//
//  Created by xingling xu on 2020/12/1.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//
/// 构造某个元素属性试图的data provider
#import <Foundation/Foundation.h>

#define BarData(title,clsName)  [JCBarData dataWithTitle:title propertyClassName:clsName]
#define BarInfo(title,info)     [JCBarData dataWithTitle:title userInfo:info]

NS_ASSUME_NONNULL_BEGIN

@interface JCBarData : NSObject
@property (nonatomic, copy) NSString *title;
@property (nonatomic, copy) NSString *propertyClsName;
@property (nonatomic, strong) id userInfo;// 扩展信息
+ (JCBarData *)dataWithTitle:(NSString *)title propertyClassName:(NSString *)clsName;
+ (JCBarData *)dataWithTitle:(NSString *)title userInfo:(id)info;
@end

NS_ASSUME_NONNULL_END
