//
//  JCElementPropertyBar.m
//  ChenYin
//
//  Created by xingling xu on 2020/11/10.
//  Copyright © 2020 JCChenYin. All rights reserved.
//

#import "JCElementPropertyBar.h"
#import "JCBarCollectionCell.h"

static NSString * identifier = @"JCBarCollectionCell";

@interface JCElementPropertyBar () <UICollectionViewDelegate,UICollectionViewDataSource>
@property (nonatomic, strong) UICollectionView *collectionView;
@property (nonatomic, copy) NSArray *datas;
@property (nonatomic, assign) NSInteger selectIndex;
@property (nonatomic, strong) UIButton *folderBtn;
@end

@implementation JCElementPropertyBar

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:(CGRect){0,0,kSCREEN_WIDTH,50}];
    if (self) {
        [self initBaseUI];
    }
    return self;
}

- (instancetype)initWithDatas:(NSArray<JCBarData *> *)datas {
    self = [super initWithFrame:(CGRect){0,0,kSCREEN_WIDTH,50}];
    if (self) {
        self.datas = datas;
        [self initBaseUI];
    }
    return self;
}

- (void)folderButtonHidden {
    self.folderBtn.hidden = YES;
    self.collectionView.width = kSCREEN_WIDTH;
    self.layer.borderWidth = 0;
}

- (void)initBaseUI {
    self.backgroundColor = [UIColor clearColor];
    self.layer.borderWidth = 1;
    self.layer.borderColor = XY_HEX_RGB(0xf5f5f5).CGColor;
    [self addSubview:self.collectionView];
    [self addSubview:self.folderBtn];
    [self.collectionView reloadData];
    [self.collectionView selectItemAtIndexPath:[NSIndexPath indexPathForRow:1 inSection:0] animated:NO scrollPosition:UICollectionViewScrollPositionNone];
}


- (void)selectIndex:(NSInteger)selectIndex {
    [self.collectionView selectItemAtIndexPath:[NSIndexPath indexPathForRow:selectIndex inSection:0] animated:NO scrollPosition:UICollectionViewScrollPositionNone];
    if (self.delegate && [self.delegate respondsToSelector:@selector(propertyBar:didSelectIndex:withData:)]) {
        [self.delegate propertyBar:self didSelectIndex:selectIndex withData:[self.datas safeObjectAtIndex:selectIndex]];
    }
}

- (void)selectBarAt:(NSString *)indexStr {
    [self.collectionView selectItemAtIndexPath:[NSIndexPath indexPathForRow:indexStr.integerValue inSection:0] animated:NO scrollPosition:UICollectionViewScrollPositionNone];
}

- (void)refrehWithDatas:(NSArray<JCBarData *> *)datas select:(NSInteger)selectedIndex {
    self.datas = datas;
    [self.collectionView reloadData];
    [self.collectionView selectItemAtIndexPath:[NSIndexPath indexPathForRow:0 inSection:selectedIndex] animated:NO scrollPosition:UICollectionViewScrollPositionNone];
}

- (void)folder:(UIButton *)button {
    JCPerformSelector(self.context.baseController, sel(@"folderProperty"));
}

#pragma mark - UICollectionView DataSource
- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return self.datas.count;
}

// The cell that is returned must be retrieved from a call to -dequeueReusableCellWithReuseIdentifier:forIndexPath:
- (__kindof UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    JCBarData *data = [self.datas safeObjectAtIndex:indexPath.row];
    NSString *title = data.title;
    JCBarCollectionCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:identifier forIndexPath:indexPath];
    cell.title = title;
    return cell;
}

#pragma mark - UICollectionView Delegate
- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
    if (self.delegate && [self.delegate respondsToSelector:@selector(propertyBar:didSelectIndex:withData:)]) {
        [self.delegate propertyBar:self didSelectIndex:indexPath.row withData:[self.datas safeObjectAtIndex:indexPath.row]];
    }
}
#pragma mark - UICollectionViewDelegateFlowLayout
- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath {
    JCBarData *data = [self.datas safeObjectAtIndex:indexPath.row];
    return [JCBarCollectionCell size4Title:data.title];
}
#pragma mark - getter
- (UICollectionView *)collectionView {
    if (!_collectionView) {
        UICollectionViewFlowLayout *layout = [[UICollectionViewFlowLayout alloc] init];
        
        CGFloat itemW = 42;
        CGFloat itemH = 30;
        layout.itemSize = CGSizeMake(itemW, itemH);
        layout.sectionInset = UIEdgeInsetsMake(10, 10, 10, 10);
        layout.scrollDirection = UICollectionViewScrollDirectionHorizontal;
        layout.minimumLineSpacing = 10;
        layout.minimumInteritemSpacing = 10;
        _collectionView = [[UICollectionView alloc] initWithFrame:(CGRect){0,0,kSCREEN_WIDTH-self.folderBtn.width,50} collectionViewLayout:layout];
        _collectionView.backgroundColor = [UIColor whiteColor];
        [_collectionView registerClass:[JCBarCollectionCell class] forCellWithReuseIdentifier:identifier];
        _collectionView.showsHorizontalScrollIndicator = NO;
        _collectionView.delegate = self;
        _collectionView.dataSource = self;
    }
    return _collectionView;
}

- (UIButton *)folderBtn {
    if (!_folderBtn) {
        _folderBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        _folderBtn.frame = (CGRect){self.width-44,0,44,self.height-1};
        [_folderBtn setBackgroundImage:[UIImage imageNamed:@"property_folder"] forState:UIControlStateNormal];
        [_folderBtn addTarget:self action:@selector(folder:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _folderBtn;
}

@end
