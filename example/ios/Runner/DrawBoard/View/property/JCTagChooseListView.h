//
//  JCTagChooseListView.h
//  XYFrameWork
//
//  Created by xingling xu on 2020/11/23.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import <UIKit/UIKit.h>

@class JCTagChooseListView;
@protocol ChooseTagDelegate <NSObject>
- (void)taglistView:(JCTagChooseListView *)view requestTemplate:(NSString *)templateId;
- (void)closeTagListView;
- (void)scanForTagInfo;
- (void)cancelScanSearchResult;
- (void)tagListView:(JCTagChooseListView *)view barCode:(NSString *)barCode;
@end

NS_ASSUME_NONNULL_BEGIN

@interface JCTagChooseListView : UIView
@property (nonatomic, weak) id  delegate;
@property(nonatomic,copy)JCTemplateData *templateData;
- (void)showUsedData;
- (void)showScanResult:(JCTemplateData *)data;
@end

NS_ASSUME_NONNULL_END
