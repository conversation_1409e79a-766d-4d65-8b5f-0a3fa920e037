//
//  UIView+PlaceHolder.m
//  Runner
//
//  Created by xingling xu on 2020/12/29.
//

#import "UIView+PlaceHolder.h"

static CGFloat __placeHolderImageTag = 2376.f;

void(^nodataAction)(JCNoDataAction type);
JCNoDataAction noDataType;

@implementation UIView (PlaceHolder)

- (void)showNoDataTipView {
    if (NETWORK_STATE_ERROR) {
        [self showNoNetWorkView:@"当前网络不佳，请稍候再试哦~"];
    } else {
        [self showNoDataTipView:@"可惜，当前没有内容哦~~"];
    }
}


- (void)showNoDataTipView:(NSString *)message {
    [self hiddenNoDataTipView];
    UIImage *image = [UIImage imageNamed:@"nothing_exist"];
    CGSize size = image.size;
    CGFloat width = self.width;
    CGFloat height = self.height;
    CGFloat imgHeight = width*size.height/size.width;
    UIImageView *noDataImgView = [[UIImageView alloc] initWithImage:image];
//    noDataImgView.frame = (CGRect){0,height/2-imgHeight/2-20,width,imgHeight};
    noDataImgView.tag = __placeHolderImageTag;
    [self addSubview:noDataImgView];
    [noDataImgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(width);
        make.height.mas_equalTo(imgHeight);
        make.center.equalTo(self);
    }];
   
    UILabel *label = [[UILabel alloc] init];
    label.font = MY_FONT_Regular(14);
    label.textColor = XY_HEX_RGB(0x999999);
    label.textAlignment = NSTextAlignmentCenter;
    label.text = message;
    label.tag = __placeHolderImageTag+1;
    CGSize labelSize = [label sizeThatFits:(CGSize){width,CGFLOAT_MAX}];
//    label.frame = (CGRect){0,noDataImgView.bottom + 20,labelSize};
//    label.centerX = self.width/2;
    [self addSubview:label];
    [label mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(labelSize);
        make.centerX.equalTo(self);
        make.top.equalTo(noDataImgView.mas_bottom).offset(20);
    }];
}


- (void)hiddenNoDataTipView {
    UIView *view = [self viewWithTag:__placeHolderImageTag];
    if (view)[view removeFromSuperview];
    UIView *label = [self viewWithTag:__placeHolderImageTag+1];
    if (label)[label removeFromSuperview];
}

- (void)showNoNetWorkView:(NSString *)message {
    [self showNoNetWorkView:message Action:JCNoDataActionNone completion:nil];
}

- (void)showNoNetWorkView:(NSString *)message Action:(JCNoDataAction)action completion:(void(^)(JCNoDataAction type))completion; {
    nodataAction = completion;
    noDataType = action;
    [self hiddenNoDataTipView];
    UIImage *image = [UIImage imageNamed:@"no_network"];
    CGSize size = image.size;
    CGFloat width = self.width;
    CGFloat imgHeight = width*size.height/size.width;
    UIImageView *noDataImgView = [[UIImageView alloc] initWithImage:image];
    noDataImgView.tag = __placeHolderImageTag;
    [self addSubview:noDataImgView];
    [noDataImgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(width);
        make.height.mas_equalTo(imgHeight);
        make.center.equalTo(self);
    }];
   
    UILabel *label = [[UILabel alloc] init];
    label.font = MY_FONT_Regular(14);
    label.textColor = XY_HEX_RGB(0x999999);
    label.textAlignment = NSTextAlignmentCenter;
    label.text = message;
    label.tag = __placeHolderImageTag+1;
    CGSize labelSize = [label sizeThatFits:(CGSize){width,CGFLOAT_MAX}];
    [self addSubview:label];
    [label mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(labelSize);
        make.centerX.equalTo(self);
        make.top.equalTo(noDataImgView.mas_bottom).offset(20);
    }];
   
    if (action == JCNoDataActionBackforward) {
        UIButton *backBtn = [[UIButton alloc] initWithFrame:CGRectZero];
        [self addSubview:backBtn];
        [backBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(label.mas_bottom).offset(20);
            make.width.mas_equalTo(120);
            make.height.mas_equalTo(40);
            make.centerX.mas_equalTo(self);
        }];
        [backBtn setTitle:@"点击返回" forState:UIControlStateNormal];
        backBtn.layer.cornerRadius = 20;
        backBtn.layer.borderWidth = 1;
        [backBtn.titleLabel setFont:MY_FONT_Regular(12)];
        [backBtn setTitleColor:COLOR_GRAY_3 forState:UIControlStateNormal];
        backBtn.layer.borderColor = COLOR_GRAY_3.CGColor;
        [backBtn addTarget:self action:@selector(backAction) forControlEvents:UIControlEventTouchUpInside];
    }
}
- (void)backAction {
    if(nodataAction){
        nodataAction(noDataType);
    }
}

- (void)hiddenTipView {
    UIView *view = [self viewWithTag:__placeHolderImageTag];
    if (view)[view removeFromSuperview];
    UIView *label = [self viewWithTag:__placeHolderImageTag+1];
    if (label)[label removeFromSuperview];
}
@end
