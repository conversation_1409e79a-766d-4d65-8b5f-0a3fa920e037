//
//  JCProgressView.m
//  XYFrameWork
//
//  Created by xingling xu on 2020/11/16.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import "JCProgressView.h"

static const CGFloat kLineWidth = 2.0f;

@interface JCProgressView ()
/** 图片 */
@property (nonatomic, strong) UIImageView *imageView;
/** 背景layer */
@property (nonatomic, strong) CAShapeLayer *backgroundLayer;
/** 进度条layer */
@property (nonatomic, strong) CAShapeLayer *progressLayer;

@end

@implementation JCProgressView

- (id)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [self buildView];
    }
    return self;
}

- (void)buildView {
    // 设置self
    self.transform = CGAffineTransformMakeRotation(-M_PI_2);
    self.imageView.transform = CGAffineTransformMakeRotation(M_PI_2);
    self.layer.cornerRadius = self.frame.size.width * 0.5;
    self.layer.masksToBounds = YES;
    // 添加专辑图片
    [self addSubview:self.imageView];
    self.imageView.image = [UIImage imageNamed:@"img1418"];
    [self addSubview:self.imageView];
    // 设置进度条的背景 Layer
    [self.layer addSublayer:self.backgroundLayer];
    // 设置进度条 Layer
    [self.layer addSublayer:self.progressLayer];
    self.progressLayer.strokeEnd = 0;
}

- (void)setProgress:(CGFloat)progress {
    [CATransaction begin];

    [CATransaction setAnimationTimingFunction:[CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionLinear]];
    [CATransaction setAnimationDuration:0.1];
    
    self.progressLayer.strokeEnd = progress;
    
    [CATransaction commit];
}

- (UIImageView *)imageView {
    if (!_imageView) {
        _imageView = [[UIImageView alloc] initWithFrame:self.bounds];
        _imageView.contentMode = UIViewContentModeScaleAspectFit;
    }
    return _imageView;
}

- (CALayer *)backgroundLayer {
    if (!_backgroundLayer) {
        _backgroundLayer = [self buildShapeLayerColor:XY_HEX_RGB(0xEBEBEB) lineWidth:kLineWidth];
    }
    return _backgroundLayer;
}

- (CALayer *)progressLayer {
    if (!_progressLayer) {
        _progressLayer =  [self buildShapeLayerColor:XY_HEX_RGB(0x04BF53) lineWidth:kLineWidth];
    }
    return _progressLayer;
}

- (CAShapeLayer *)buildShapeLayerColor:(UIColor *)color lineWidth:(CGFloat)width  {
    CAShapeLayer *layer = [CAShapeLayer layer];
    CGRect rect = {kLineWidth * 0.5, kLineWidth * 0.5, self.frame.size.width - kLineWidth, self.frame.size.height - kLineWidth};
    // 设置path
    UIBezierPath *path = [UIBezierPath bezierPathWithOvalInRect:rect];
    layer.path = path.CGPath;
    // 设置layer
    layer.strokeColor = color.CGColor;
    layer.fillColor = [UIColor clearColor].CGColor;
    layer.lineWidth = width;
    layer.lineCap = kCALineCapRound;
    return layer;
}
@end
