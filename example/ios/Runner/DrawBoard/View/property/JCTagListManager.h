//
//  JCTagListManager.h
//  XYFrameWork
//
//  Created by xingling xu on 2020/12/4.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import <Foundation/Foundation.h>
#import "JCTagModel.h"

@interface JCTagListManager : NSObject
+ (id)sharedManager;
- (void)requestTagSizes:(NSString *)machineId;
- (void)requestTagSizes:(NSString *)machineId completion:(void(^)(void))completion;
- (NSArray *)getTagList;
- (void)saveList:(NSArray<JCTagImageModel *> *)list;
- (NSArray *)getDBDataFrom:(JCTagModel *)tagModel;
@end

