//
//  JCPrintSamePreview.m
//  Runner
//
//  Created by long he on 2021/5/13.
//

#import "JCPrintSamePreview.h"
#import "JCApplicationManager.h"
#import "JCTemplateEditController.h"
@interface JCPrintSamePreview()
@property(nonatomic,strong)UIImageView *iconImageView;
@property(nonatomic,strong)UILabel *detailLabel;
@property(nonatomic,strong)UILabel *subtitleLabel;
@property(nonatomic,strong)UIImageView *previewImageView;
@property(nonatomic,strong)UIButton *buyButton;

@end
@implementation JCPrintSamePreview
- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = [UIColor jk_colorWithHex:0x000000 andAlpha:0.06];
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    [self addSubview:self.iconImageView];
    [self addSubview:self.detailLabel];
    [self addSubview:self.previewImageView];
    [self addSubview:self.buyButton];
    [self addSubview:self.subtitleLabel];
    
    
    self.buyButton.right = SCREEN_WIDTH - 20;
    self.previewImageView.right = self.buyButton.left - 16;
    self.buyButton.centerY = self.centerY;
    self.detailLabel.centerY = self.centerY - 11;
    self.subtitleLabel.centerY = self.centerY + 11;
    self.previewImageView.centerY = self.centerY;
    
}

- (void)setTemplateData:(JCTemplateData *)templateData {
    _templateData = templateData;
    [self.iconImageView sd_setImageWithURL:[NSURL URLWithString:templateData.profileURL]];
    NSString *detailStr;
    if (![templateData.profile.extrain.userId isEqual:JCAPP_Manager.user.userId]) {
//        detailStr = [NSString stringWithFormat:@"%@使用了\n%@",templateData.nickName,templateData.labelName];
        detailStr = [NSString stringWithFormat:@"%@使用了",templateData.nickName];
    }else{
//        detailStr = [NSString stringWithFormat:@"我使用了\n%@",templateData.labelName];
        detailStr = @"我使用了";
    }
    [[NSAttributedString alloc] initWithString:@"" attributes:@{
            NSForegroundColorAttributeName:[UIColor redColor],
    }];
    self.detailLabel.text = detailStr;
    self.subtitleLabel.text = templateData.labelName;
    if (templateData.isUpperShelf && templateData.profile.barcode.length > 0) {//上架状态,并且barcode有值说明能购买
        [self.buyButton setEnabled:true];
    }else{
        [self.buyButton setEnabled:false];
    }
//    [self.buyButton setEnabled: templateData.isUpperShelf];//isUpperShelf可能不准确
}
- (void)setContext:(JCContext *)context {
    _context = context;
}
#pragma mark - action
- (void)previewAction:(UILongPressGestureRecognizer *)sender{
    XYWeakSelf;
    JCTemplateEditController *controller = (JCTemplateEditController *)weakSelf.context.baseController;
    switch (sender.state) {
        case UIGestureRecognizerStateBegan:
            
            [controller downloadImagesFromTemplateData:self.templateData containOldElements:true option:DownAll shouldReplaceOld: false];
            break;
        case UIGestureRecognizerStateEnded:
            [controller resetBoardByPreview];
            break;
        default:
            break;
    }
}

- (void)buyTag {
    if (self.buyTagFromPrintSame) {
        self.buyTagFromPrintSame(self.templateData.profile.barcode);
    }
}
#pragma mark - UI
- (UIImageView *)iconImageView {
    if (!_iconImageView) {
        _iconImageView = [[UIImageView alloc] initWithFrame:CGRectMake(20, 9, 32, 32)];
        _iconImageView.layer.cornerRadius = 16;
        _iconImageView.layer.masksToBounds = true;
    }
    return _iconImageView;
}
- (UILabel *)detailLabel {
    if (!_detailLabel) {
        _detailLabel = [[UILabel alloc] initWithFrame:CGRectMake(68, 8, SCREEN_WIDTH - 68 - 32 * 2 - 60, 36)];
        _detailLabel.font = MY_FONT_Regular(12);
        _detailLabel.numberOfLines = 1;
//        [_detailLabel setValue:@(30) forKey:@"lineSpacing"];
        _detailLabel.textColor = [UIColor jk_colorWithHex:0x262626];
        _detailLabel.textAlignment = NSTextAlignmentLeft;
    }
    return  _detailLabel;
}

- (UILabel *)subtitleLabel {
    if (!_subtitleLabel) {
        _subtitleLabel = [[UILabel alloc] initWithFrame:CGRectMake(68, 8, SCREEN_WIDTH - 68 - 32 * 2 - 60, 36)];
        _subtitleLabel.font = MY_FONT_Regular(12);
        _subtitleLabel.numberOfLines = 1;
        _subtitleLabel.textColor = [UIColor jk_colorWithHex:0x262626];
        _subtitleLabel.textAlignment = NSTextAlignmentLeft;
    }
    return  _subtitleLabel;
}

- (UIImageView *)previewImageView {
    if (!_previewImageView) {
        _previewImageView = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, 32, 32)];
        [_previewImageView setImage:[UIImage imageNamed:@"board_eye"]];
        _previewImageView.userInteractionEnabled = true;
        UILongPressGestureRecognizer *longPressGesture = [[UILongPressGestureRecognizer alloc] initWithTarget:self action:@selector(previewAction:)];
        [_previewImageView addGestureRecognizer:longPressGesture];
    }
    return _previewImageView;
}
- (UIButton *)buyButton {
    if (!_buyButton) {
        _buyButton = [[UIButton alloc] initWithFrame:CGRectMake(0, 0, 32, 32)];
        [_buyButton setImage:[UIImage imageNamed:@"board_shoping_cart"] forState:UIControlStateNormal];
        [_buyButton addTarget:self action:@selector(buyTag) forControlEvents:UIControlEventTouchUpInside];
        [_buyButton setImage:[UIImage imageNamed:@"board_shoping_cart_disapble"] forState:UIControlStateDisabled];
    }
    return _buyButton;
}

@end
