//
//  JCTemplateTagModel.m
//  XYFrameWork
//
//  Created by xingling xu on 2020/12/14.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import "JCTemplateTagModel.h"

@implementation JCTemplateTagModel
+(JSO<PERSON>KeyMapper *)keyMapper
{
    return [[JSONKeyMapper alloc] initWithModelToJSONDictionary:@{@"idStr":@"id"}];
}
@end

@implementation JCTemplateTagImageModel
+(JSONKeyMapper *)keyMapper
{
    return [[JSONKeyMapper alloc] initWithModelToJSONDictionary:@{@"labelId":@"id",@"sizeId":@"categoryId"}];
}
@end
