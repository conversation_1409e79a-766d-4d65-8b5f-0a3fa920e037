//
//  JCTemplateHorizontalCell.m
//  XYFrameWork
//
//  Created by xingling xu on 2020/12/14.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import "JCTemplateHorizontalCell.h"

@interface JCTemplateHorizontalCell ()
@property (nonatomic, strong) UIImageView *icon;
@end

@implementation JCTemplateHorizontalCell
- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
//        self.layer.borderWidth = 1;
//        self.layer.borderColor =HEX_RGB(0xEBEBEB).CGColor;
//        self.layer.cornerRadius = 10;
//        self.layer.masksToBounds = YES;
        [self addSubview:self.icon];
    }
    return self;
}

#pragma mark - setter
- (void)setModel:(JCTemplateTagImageModel *)model {
    _model = model;
    self.icon.size = (CGSize){[JCTemplateHorizontalCell width4Template:model],[JCTemplateHorizontalCell height4Template:model]};
    [self.icon jc_setImageWithUrl:model.thumbnail];
}

#pragma mark - lazy
- (UIImageView *)icon {
    if (!_icon) {
        _icon = [[UIImageView alloc] initWithFrame:(CGRect){0,0,130,56}];
    }
    return _icon;
}

#pragma mark - caculate
+ (CGFloat)width4Template:(JCTemplateTagImageModel *)model {
    return (kSCREEN_WIDTH-76)/2;
}

+ (CGFloat)height4Template:(JCTemplateTagImageModel *)model {
    CGFloat height = model.height.floatValue;
    CGFloat width = model.width.floatValue;
    return [self width4Template:model]*height/width;
}
@end
