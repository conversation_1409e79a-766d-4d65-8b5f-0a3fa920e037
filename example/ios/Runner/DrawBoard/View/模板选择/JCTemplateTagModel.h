//
//  JCTemplateTagModel.h
//  XYFrameWork
//
//  Created by xingling xu on 2020/12/14.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import <Foundation/Foundation.h>
#import "DCBaseModel.h"
NS_ASSUME_NONNULL_BEGIN

@interface JCTemplateTagModel : DCBaseModel
@property (nonatomic, copy) NSString<Optional> *idStr;
@property (nonatomic, copy) NSString<Optional> *name;
@property (nonatomic, copy) NSString<Optional> *enable;
@property (nonatomic, copy) NSString<Optional> *sort;
@end


@interface JCTemplateTagImageModel : DCBaseModel
@property (nonatomic, copy) NSString<Optional> *labelId;
@property (nonatomic, copy) NSString<Optional> *sizeId;
@property (nonatomic, copy) NSString<Optional> *contentThumbnail;
@property (nonatomic, copy) NSString<Optional> *isPrivate;
@property (nonatomic, copy) NSString<Optional> *size;
@property (nonatomic, copy) NSString<Optional> *width;
@property (nonatomic, copy) NSString<Optional> *height;
@property (nonatomic, copy) NSString<Optional> *templateId;
@property (nonatomic, copy) NSString<Optional> *thumbnail;
@property (nonatomic, copy) NSString<Optional> *userId;
@end
NS_ASSUME_NONNULL_END
