//
//  JCTemplateCollectionView.m
//  XYFrameWork
//
//  Created by xingling xu on 2020/12/14.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import "JCTemplateCollectionView.h"
#import "JCTemplateHorizontalCell.h"
#import "JCTemplateVerticalCell.h"
#import "JCTemplateImageManager.h"
#import "UIView+PlaceHolder.h"

static NSString * horizontalIdentifier = @"JCTemplateHorizontalCell";
static NSString * verticalIdentifier = @"JCTemplateVerticalCell";
static NSInteger __limit = 30;

@interface JCTemplateCollectionView () <UICollectionViewDelegate,UICollectionViewDataSource>
@property (nonatomic, strong) UICollectionView *collectionView;
@property (nonatomic, strong) NSMutableArray *datas;
@property (nonatomic, strong) UICollectionViewFlowLayout *horizontalLayout;
@property (nonatomic, strong) UICollectionViewFlowLayout *verticalLayout;
@property (nonatomic, copy) NSString *sizeId;
@property (nonatomic, copy) NSString *keyWord;
@property (nonatomic, assign) NSInteger currentPage;
@property (nonatomic, strong) JCTemplateTagModel *tagModel;
@end

@implementation JCTemplateCollectionView
- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self addSubview:self.collectionView];
    }
    return self;
}

- (void)showContent:(NSString *)sizeId model:(JCTemplateTagModel *)model {
    self.sizeId = sizeId;
    self.tagModel = model;
    if (self.datas.count == 0) [self refreshData];
}

- (void)showContent:(NSString *)sizeId keyWord:(NSString *)keyWord {
    self.keyWord = keyWord;
    [self showContent:sizeId model:nil];
}

- (void)refreshListView {
    if (self.datas.count < __limit) {
        self.collectionView.mj_footer.hidden = YES;
    } else {
        self.collectionView.mj_footer.hidden = NO;
    }
    [self.collectionView reloadData];
    if (self.currentPage == 1 && self.datas.count > 0) {
        [self.collectionView scrollToItemAtIndexPath:[NSIndexPath indexPathForRow:0 inSection:0] atScrollPosition:UICollectionViewScrollPositionTop animated:YES];
    }
    if (self.datas.count == 0) {
        [self.collectionView showNoDataTipView];
    } else {
        [self.collectionView hiddenNoDataTipView];
    }
}

- (void)requestWith:(NSString *)categoryId sizeId:(NSString *)sizeId page:(NSInteger)page limit:(NSInteger)limit {
    if (page == 1) [self.datas removeAllObjects];
    NSString *path = @"kraken/imprint/template/list";
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    if (!STR_IS_NIL(categoryId)) {
        [params setObject:categoryId forKey:@"categoryId"];
    }
    if (!STR_IS_NIL(sizeId)) {
        [params setObject:sizeId forKey:@"sizeId"];
    }
    if (!STR_IS_NIL(self.keyWord)) {
        [params setObject:self.keyWord forKey:@"text"];
    }
    [params setObject:StringFromInt(page) forKey:@"page"];
    [params setObject:StringFromInt(limit) forKey:@"limit"];
    [params setObject:DrawBoardInfo.deviceId forKey:@"deviceId"];
    [DCHTTPRequest postWithParams:params ModelType:nil Path:path Json:YES Success:^(__kindof YTKBaseRequest * _Nonnull request, NSDictionary  *responseObject) {
        NSArray *list = [responseObject objectForKey:@"list"];
        if (list && list.count > 0) {
            NSArray *temp = [JCTemplateTagImageModel arrayOfModelsFromDictionaries:list error:nil];
            [self.datas addObjectsFromArray:temp];
        }
        [self.collectionView.mj_header endRefreshing];
        if (list.count < __limit) {
            [self.collectionView.mj_footer endRefreshingWithNoMoreData];
        } else {
            [self.collectionView.mj_footer endRefreshing];
        }
        [self refreshListView];
    } failure:^(YTKBaseRequest * _Nullable request, NSError * _Nullable error) {
        [self.collectionView.mj_header endRefreshing];
        [self.collectionView.mj_footer endRefreshing];
        [self refreshListView];
    }];
}

- (void)refreshData {
    self.currentPage = 1;
    [self requestWith:self.tagModel.idStr sizeId:DrawBoardInfo.sizeId page:self.currentPage limit:__limit];
}

- (void)loadMoreData {
    self.currentPage++;
    [self requestWith:self.tagModel.idStr sizeId:DrawBoardInfo.sizeId page:self.currentPage limit:__limit];
}
#pragma mark - UICollectionView DataSource
- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return self.datas.count;
}

// The cell that is returned must be retrieved from a call to -dequeueReusableCellWithReuseIdentifier:forIndexPath:
- (__kindof UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    JCTemplateHorizontalCell * cell  = [collectionView dequeueReusableCellWithReuseIdentifier:horizontalIdentifier forIndexPath:indexPath];
    JCTemplateTagImageModel *model = [self.datas safeObjectAtIndex:indexPath.row];
    if (model) {
        cell.model = model;
    }
    return cell;
}

#pragma mark - UICollectionView Delegate
- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
    JCTemplateTagImageModel *model = [self.datas safeObjectAtIndex:indexPath.row];
    if (!model) return;;
    if (self.delegate && [self.delegate respondsToSelector:@selector(templateCollectionView:selectModel:)]) {
        [self.delegate templateCollectionView:self selectModel:model];
    }
}

#pragma mark - UICollectionViewDelegateFlowLayout
- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath {
    JCTemplateTagImageModel *model = [self.datas safeObjectAtIndex:indexPath.row];
    return (CGSize){[JCTemplateHorizontalCell width4Template:model],[JCTemplateHorizontalCell height4Template:model]};
}

- (UIEdgeInsets)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout insetForSectionAtIndex:(NSInteger)section {
    return (UIEdgeInsets){15,23,15,23};
}
- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout minimumLineSpacingForSectionAtIndex:(NSInteger)section {
    return 30;
}
- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout minimumInteritemSpacingForSectionAtIndex:(NSInteger)section {
    return 30;
}

- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout referenceSizeForHeaderInSection:(NSInteger)section {
    return (CGSize){kSCREEN_WIDTH,15};
}

#pragma mark - lazy
- (UICollectionView *)collectionView {
    if (!_collectionView) {
        UICollectionViewFlowLayout *layout = [[UICollectionViewFlowLayout alloc] init];
        _collectionView = [[UICollectionView alloc] initWithFrame:self.bounds collectionViewLayout:layout];
        _collectionView.backgroundColor = [UIColor whiteColor];
        [_collectionView registerClass:[JCTemplateHorizontalCell class] forCellWithReuseIdentifier:horizontalIdentifier];
        [_collectionView registerClass:[JCTemplateVerticalCell class] forCellWithReuseIdentifier:verticalIdentifier];
        _collectionView.showsHorizontalScrollIndicator = NO;
        _collectionView.delegate = self;
        _collectionView.dataSource = self;
        _collectionView.mj_header = [MJRefreshNormalHeader headerWithRefreshingTarget:self refreshingAction:@selector(refreshData)];
        _collectionView.mj_footer = [MJRefreshAutoNormalFooter footerWithRefreshingTarget:self refreshingAction:@selector(loadMoreData)];
        _collectionView.mj_footer.hidden = YES;
    }
    return _collectionView;
}

- (NSMutableArray *)datas {
    if (!_datas) {
        _datas = [NSMutableArray array];
    }
    return _datas;
}

@end
