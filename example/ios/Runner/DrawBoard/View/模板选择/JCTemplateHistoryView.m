//
//  JCTemplateHistoryView.m
//  XYFrameWork
//
//  Created by xingling xu on 2020/12/15.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import "JCTemplateHistoryView.h"
#import "JCTag.h"

#define JCTempalteSearchHistory  @"JCTempalteSearchHistory"

@interface JCTemplateHistoryView ()
@property (nonatomic, strong) UIButton *deleteButton;
@property (nonatomic, strong) UIScrollView *scrollView;

@end

@implementation JCTemplateHistoryView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = XY_HEX_RGB(0xf5f5f5);
        [self loadRootView];
    }
    return self;
}

- (void)loadRootView {
    [self addSubview:self.scrollView];
    UILabel *label = [[UILabel alloc] initWithFrame:(CGRect){20,20,100,21}];
    label.font = MY_FONT_Bold(16);
    label.textColor = XY_HEX_RGB(0x262626);
    label.text = @"搜索历史";
    [self.scrollView addSubview:label];
    [self.scrollView addSubview:self.deleteButton];
}

- (void)refreshHistoryDatas {
    [self.scrollView.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
    [self loadRootView];
    NSArray *array = [[NSUserDefaults standardUserDefaults] objectForKey:JCTempalteSearchHistory];
    JCWeakSelf;
    CGFloat offsetX = 20;
    CGFloat offsetY = 62;
    for (NSInteger i = 0; i < array.count; i ++) {
        NSString *content = [array safeObjectAtIndex:i];
        JCTag *tag = [[JCTag alloc] initWithContent:content clickBlock:^(NSString *tag) {
            JCStrongSelf;
            if (self.delegate && [self.delegate respondsToSelector:@selector(historyView:key:)]) {
                [self.delegate historyView:self key:content];
            }
        }];
        if (offsetX+tag.width >= kSCREEN_WIDTH-40) {
            offsetX = 20;
            offsetY += 39;
        }
        // layout
        tag.left = offsetX;
        tag.top = offsetY;
        // reset
        offsetX += tag.width + 12;
        [self.scrollView addSubview:tag];
    }
}

- (void)deleteHistory:(UIButton *)button {
    [JCTemplateHistoryView removeAllHistory];
    [self refreshHistoryDatas];
}

#pragma mark - lazy
- (UIButton *)deleteButton {
    if (!_deleteButton) {
        _deleteButton = [UIButton buttonWithType:UIButtonTypeCustom];
        _deleteButton.frame = (CGRect){kSCREEN_WIDTH-50,10,40,40};
        [_deleteButton setImage:[UIImage imageNamed:@"search_list_clear"] forState:UIControlStateNormal];
        [_deleteButton addTarget:self action:@selector(deleteHistory:) forControlEvents:UIControlEventTouchUpInside];
    }
    return  _deleteButton;
}

- (UIScrollView *)scrollView {
    if (!_scrollView) {
        _scrollView = [[UIScrollView alloc] initWithFrame:(CGRect){0,0,self.width,self.height}];
        _scrollView.backgroundColor = XY_HEX_RGB(0xf5f5f5);
        _scrollView.bounces = NO;
        _scrollView.showsVerticalScrollIndicator = NO;
    }
    return _scrollView;
}

#pragma mark - extends
+ (void)addHistoryKey:(NSString *)key {
    NSArray *array = [[NSUserDefaults standardUserDefaults] objectForKey:JCTempalteSearchHistory];
    NSMutableArray *temp = [NSMutableArray array];
    if (array && array.count > 0) {
        [temp addObjectsFromArray:array];
    }
    if ([temp containsObject:key]) {
        [temp removeObject:key];
    }
    [temp insertObject:key atIndex:0];
    [[NSUserDefaults standardUserDefaults] setObject:temp forKey:JCTempalteSearchHistory];
    [[NSUserDefaults standardUserDefaults] synchronize];
}

+ (void)removeAllHistory {
    [[NSUserDefaults standardUserDefaults] removeObjectForKey:JCTempalteSearchHistory];
}
@end
