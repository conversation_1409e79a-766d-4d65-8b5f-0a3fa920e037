//
//  JCTagView.m
//  ChenYin
//
//  Created by xingling xu on 2020/6/19.
//  Copyright © 2020 JCChenYin. All rights reserved.
//

#import "JCTag.h"

@interface JCTag ()
@property (nonatomic, copy) NSString *content;
@property (nonatomic, copy) void(^clickBlock)(NSString *tag);
@end

@implementation JCTag

- (instancetype)initWithContent:(NSString *)content clickBlock:(void(^)(NSString *tag))clickBlock {
    self = [super init];
    if (self) {
        self.content = content;
        self.clickBlock = clickBlock;
        [self loadRootView];
    }
    return self;
}

- (void)loadRootView {
    UILabel *label = [[UILabel alloc] init];
    label.font = MY_FONT_Regular(12);
    label.textColor = XY_HEX_RGB(0x262626);
    label.textAlignment = NSTextAlignmentCenter;
    label.text = self.content;
    CGSize size = [label sizeThatFits:(CGSize){kSCREEN_WIDTH-40-30-30,17}];
    label.size = size;
    self.width = size.width + 26;
    self.height = 28;
    label.left = 13;
    label.centerY = self.height/2;
    [self addSubview: label];
    
    UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
    button.frame = self.bounds;
    [self addSubview:button];
    [button addTarget:self action:@selector(clickTag:) forControlEvents:UIControlEventTouchUpInside];
    
    // corner
    self.backgroundColor = [UIColor whiteColor];
    self.layer.cornerRadius = self.height/2;
    self.layer.masksToBounds = YES;
}

- (void)clickTag:(UIButton *)button {
    if (self.clickBlock) {
        self.clickBlock(self.content);
    }
}


@end
