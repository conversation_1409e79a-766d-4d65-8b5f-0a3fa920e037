//
//  JCTemplateHistoryView.h
//  XYFrameWork
//
//  Created by xingling xu on 2020/12/15.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@class JCTemplateHistoryView;
@protocol JCTemplateHistoryViewDelegate <NSObject>
- (void)historyView:(JCTemplateHistoryView *)view key:(NSString *)key;
@end

@interface JCTemplateHistoryView : UIView
@property (nonatomic, weak) id  delegate;
// 刷新搜索历史数据
- (void)refreshHistoryDatas;
// 添加历史搜索
+ (void)addHistoryKey:(NSString *)key;
// 清除记录
+ (void)removeAllHistory;
@end

NS_ASSUME_NONNULL_END
