//
//  JCTemplateChoosView.m
//  XYFrameWork
//
//  Created by xingling xu on 2020/12/14.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import "JCTemplateChoosView.h"
#import "JCTemplateCollectionView.h"
#import "JCTemplateTagModel.h"
#import "JCTemplateCollectionView.h"
#import "JCElementPropertyBar.h"
#import "JCSearchTagTextField.h"
#import "JCTemplateHistoryView.h"

static NSInteger collection_tag = 1000;
static NSInteger choose_view_tag = 12368;

@interface JCTemplateChoosView () <UITextFieldDelegate,UIScrollViewDelegate>
@property (nonatomic, strong) UIScrollView *scrollView;
@property (nonatomic, copy) NSArray *tagArr;;
@property (nonatomic, strong) JCElementPropertyBar *bar;
@property (nonatomic, strong) JCSearchTagTextField *textField;
@property (nonatomic, strong) UIButton *cancelButton;
@property (nonatomic, strong) JCTemplateHistoryView *historyView;
@property (nonatomic, strong) JCTemplateCollectionView *searchResultView;
@property (nonatomic, weak) id  delegate;
@end

@implementation JCTemplateChoosView

+ (void)showChooseTemplateViewWith:(NSString *)sizeId delegate:(id)delegate {
    UIWindow *keyWindow = [[UIApplication sharedApplication].delegate window];
    UIView *maskView = [[UIView alloc] initWithFrame:(CGRect){0,0,kSCREEN_WIDTH,kSCREEN_HEIGHT}];
    maskView.backgroundColor = XY_HEX_RGBA(0x000000, 0.25);
    maskView.tag = choose_view_tag;
    [keyWindow addSubview:maskView];
    
    JCTemplateChoosView *chooseView = [[JCTemplateChoosView alloc] initWithFrame:(CGRect){0,kSCREEN_HEIGHT,kSCREEN_WIDTH,kSCREEN_HEIGHT-56}];
    chooseView.sizeId = sizeId;
    chooseView.delegate = delegate;
    [maskView addSubview:chooseView];
    [UIView animateWithDuration:0.3 animations:^{
        chooseView.top = 56;
    }];
}

+ (void)dismissChooseView {
    UIWindow *keyWindow = [[UIApplication sharedApplication].delegate window];
    UIView *view = [keyWindow viewWithTag:choose_view_tag];
    [view removeFromSuperview];
}

- (void)close:(UIButton *)button {
    [JCTemplateChoosView dismissChooseView];
}

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = [UIColor whiteColor];
        self.layer.borderWidth = 1;
        self.layer.borderColor = XY_HEX_RGB(0xEAEAEA).CGColor;
        self.layer.cornerRadius = 12;
        self.layer.masksToBounds = YES;
        [self loadRootView];
    }
    return self;
}

- (void)loadRootView {
    [self addSubview:self.textField];
    [self addSubview:self.bar];
    [self addSubview:self.scrollView];
    [self addSubview:self.cancelButton];
    self.cancelButton.centerY = self.textField.centerY;
    
    UIView *line = [[UIView alloc] initWithFrame:(CGRect){0,0,self.bar.width,1}];
    line.backgroundColor = XY_HEX_RGBA(0xeeeeee, 1);
    [self.bar addSubview:line];
    [line mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.bottom.equalTo(self.bar);
        make.height.mas_equalTo(1);
    }];
    
    // title
    UILabel *titleLabel = [[UILabel alloc] initWithFrame:(CGRect){0,16,100,24}];
    titleLabel.font = MY_FONT_Bold(17);
    titleLabel.textAlignment = NSTextAlignmentCenter;
    titleLabel.textColor = XY_HEX_RGB(0x262626);
    titleLabel.text = @"标签模板";
    titleLabel.centerX = self.width/2;
    [self addSubview:titleLabel];
    
    UIButton *closeBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    closeBtn.frame = (CGRect){kSCREEN_WIDTH-46,44,46,44};
    [closeBtn setImage:[UIImage imageNamed:@"left_btn_return"] forState:UIControlStateNormal];
    [closeBtn addTarget:self action:@selector(close:) forControlEvents:UIControlEventTouchUpInside];
    [self addSubview:closeBtn];
    closeBtn.centerY = titleLabel.centerY;
    
    [self.bar mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.equalTo(self);
        make.top.mas_equalTo(95);
        make.height.mas_equalTo(50);
    }];
    
    [self.scrollView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.bottom.equalTo(self);
        make.top.equalTo(self.bar.mas_bottom);
    }];
    
    // history view
    [self addSubview:self.historyView];
    [self.historyView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.bottom.equalTo(self);
        make.top.mas_equalTo(95);
    }];
    // 搜索结果
    [self addSubview:self.searchResultView];
    
    [self requestTemplateTags];
}

- (void)requestTemplateTags {
    NSString *path = @"kraken/system/category/list";
    [DCHTTPRequest getWithParams:@{} ModelType:[JCTemplateTagModel class] url:path Success:^(__kindof YTKBaseRequest * _Nonnull request, NSArray *array) {
        if (array.count > 0) {
            self.tagArr = array;
            
            NSMutableArray *temp = [NSMutableArray arrayWithCapacity:self.tagArr.count+1];
            JCTemplateTagModel *usedModel = [JCTemplateTagModel new];
            usedModel.idStr = @"1";
            usedModel.name = @"用过";
            [temp addObject:[JCBarData dataWithTitle:@"用过" userInfo:usedModel]];
            [self.tagArr enumerateObjectsUsingBlock:^(JCTemplateTagModel *model, NSUInteger idx, BOOL * _Nonnull stop) {
                JCBarData *data = [JCBarData dataWithTitle:model.name userInfo:model];
                if (data) [temp addObject:data];
            }];
            self.tagArr = temp;
            
            [self.bar refrehWithDatas:temp select:0];
            [self loadCollectionView];
            
            JCTemplateCollectionView *currentCollectionView = [self.scrollView viewWithTag:collection_tag];
            [currentCollectionView showContent:self.sizeId model:usedModel];
        }
    } failure:^(YTKBaseRequest * _Nullable request, NSError * _Nullable error) {
        
    }];
}

- (void)loadCollectionView {
    for (NSInteger i = 0; i < self.tagArr.count; i ++) {
        JCTemplateCollectionView *collectionView = [[JCTemplateCollectionView alloc] initWithFrame:(CGRect){i*kSCREEN_WIDTH,0,kSCREEN_WIDTH,self.scrollView.height}];
        collectionView.delegate = self;
        collectionView.tag = collection_tag + i;
        [self.scrollView addSubview:collectionView];
    }
    self.scrollView.contentSize = CGSizeMake(self.tagArr.count * kSCREEN_WIDTH, self.scrollView.height);
}

#pragma mark - JCTemplateCollectionViewDelegate
- (void)templateCollectionView:(JCTemplateCollectionView *)collectionView selectModel:(JCTemplateTagImageModel *)model {
    NSString *printId = model.labelId;
    if (STR_IS_NIL(printId)) return;
    if (self.delegate && [self.delegate respondsToSelector:@selector(templateChooseView:printId:)]) {
        [self.delegate templateChooseView:self printId:printId];
    }
}

- (void)propertyBar:(JCElementPropertyBar *)bar didSelectIndex:(NSInteger)index withData:(JCBarData *)data {
    JCTemplateTagModel *model = (JCTemplateTagModel *)data.userInfo;
    if ([model isKindOfClass:[JCTemplateTagModel class]]) {
        self.scrollView.contentOffset = (CGPoint){index*kSCREEN_WIDTH,0};
        JCTemplateCollectionView *currentCollectionView = [self.scrollView viewWithTag:collection_tag+index];
        [currentCollectionView showContent:self.sizeId model:model];
    }
}

- (void)cancelSearch:(UIButton *)button {
    [UIView animateWithDuration:0.3 animations:^{
        self.textField.width = kSCREEN_WIDTH-24;
    }];
    [self.textField resignFirstResponder];
    self.textField.text = @"";
    self.cancelButton.hidden = YES;
    self.searchResultView.hidden = YES;
}

#pragma mark - UITextField Delegate
- (BOOL)textFieldShouldBeginEditing:(UITextField *)textField {
    [UIView animateWithDuration:0.3 animations:^{
        self.textField.width = kSCREEN_WIDTH-24-42;
    }];
    self.cancelButton.hidden = NO;
    self.historyView.hidden = NO;
    [self.historyView refreshHistoryDatas];
    return YES;
}

- (void)textFieldDidEndEditing:(UITextField *)textField {
    self.historyView.hidden = YES;
}

- (BOOL)textFieldShouldReturn:(UITextField *)textField {
    [self showResultView:textField.text];
    return YES;
}

- (void)showResultView:(NSString *)keyWord {
    [JCTemplateHistoryView addHistoryKey:keyWord];
    [self.textField resignFirstResponder];
    self.historyView.hidden = YES;
    self.searchResultView.hidden = NO;
    [self.searchResultView showContent:self.sizeId keyWord:keyWord];
}

#pragma mark - JCTemplateHistoryView Delegate
- (void)historyView:(JCTemplateHistoryView *)view key:(NSString *)key {
    self.textField.text = key;
    [self showResultView:key];
}

#pragma mark - lazy
- (UIScrollView *)scrollView {
    if (!_scrollView) {
        _scrollView = [[UIScrollView alloc] initWithFrame:(CGRect){0,50,kSCREEN_WIDTH,self.height-143}];
        _scrollView.scrollEnabled = YES;
//        _scrollView.pagingEnabled = YES;
        _scrollView.delegate = self;
    }
    return _scrollView;
}

- (NSArray *)tagArr {
    if (!_tagArr) {
        _tagArr = [NSArray array];
    }
    return _tagArr;
}

- (JCElementPropertyBar *)bar {
    if (!_bar) {
        _bar = [[JCElementPropertyBar alloc] initWithFrame:(CGRect){0,0,kSCREEN_WIDTH,50}];
        [_bar folderButtonHidden];
        _bar.delegate = self;
    }
    return _bar;
}

- (JCSearchTagTextField *)textField {
    if (!_textField) {
        _textField = [[JCSearchTagTextField alloc] initWithFrame:(CGRect){12,48,kSCREEN_WIDTH-24,34}];
        _textField.backgroundColor = XY_HEX_RGB(0xF5F5F5);
        _textField.layer.cornerRadius = 18;
        _textField.layer.masksToBounds = YES;
        _textField.delegate = self;
        _textField.font = MY_FONT_Medium(15);
        _textField.placeholder = @"搜索模板内容";
        _textField.returnKeyType = UIReturnKeySearch;
        _textField.clearButtonMode = UITextFieldViewModeWhileEditing;
        //left view
        UIImageView *leftImgView = [[UIImageView alloc] initWithFrame:(CGRect){0,0,136,16}];
        leftImgView.image = [UIImage imageNamed:@"search_tag"];
        _textField.leftView = leftImgView;
        _textField.leftViewMode = UITextFieldViewModeAlways;
    }
    return _textField;
}

- (UIButton *)cancelButton {
    if (!_cancelButton) {
        _cancelButton = [UIButton buttonWithType:UIButtonTypeCustom];
        _cancelButton.frame = (CGRect){kSCREEN_WIDTH-42,0,32,22};
        [_cancelButton setTitleColor:XY_HEX_RGB(0x262626) forState:UIControlStateNormal];
        [_cancelButton setTitle:@"取消" forState:UIControlStateNormal];
        _cancelButton.titleLabel.font = MY_FONT_Bold(16);
        [_cancelButton addTarget:self action:@selector(cancelSearch:) forControlEvents:UIControlEventTouchUpInside];
        _cancelButton.hidden = YES;
    }
    return _cancelButton;
}

- (JCTemplateHistoryView *)historyView {
    if (!_historyView) {
        _historyView = [[JCTemplateHistoryView alloc] initWithFrame:(CGRect){0,95,kSCREEN_WIDTH,self.height-95}];
        _historyView.delegate = self;
        _historyView.hidden = YES;
    }
    return _historyView;
}

- (JCTemplateCollectionView *)searchResultView {
    if (!_searchResultView) {
        _searchResultView = [[JCTemplateCollectionView alloc] initWithFrame:(CGRect){0,95,kSCREEN_WIDTH,self.height-95}];
        _searchResultView.hidden = YES;
    }
    return _searchResultView;
}

@end
