//
//  JCTemplateChoosView.h
//  XYFrameWork
//
//  Created by xingling xu on 2020/12/14.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@class JCTemplateChoosView;
@protocol JCTemplateChoosViewDelegate <NSObject>
- (void)templateChooseView:(JCTemplateChoosView *)chooseView printId:(NSString *)printId;
@end

@interface JCTemplateChoosView : UIView

@property (nonatomic, copy) NSString *sizeId;

+ (void)showChooseTemplateViewWith:(NSString *)sizeId delegate:(id)delegate;

+ (void)dismissChooseView;
@end

NS_ASSUME_NONNULL_END
