//
//  JCTemplateCollectionView.h
//  XYFrameWork
//
//  Created by xingling xu on 2020/12/14.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import <UIKit/UIKit.h>
#import "JCTemplateTagModel.h"

@class JCTemplateCollectionView;
@protocol JCTemplateCollectionViewDelegate <NSObject>
- (void)templateCollectionView:(JCTemplateCollectionView *)collectionView selectModel:(JCTemplateTagImageModel *)model;
@end

@interface JCTemplateCollectionView : UIView
@property (nonatomic, weak) id  delegate;
// 根据标签名查询
- (void)showContent:(NSString *)sizeId model:(JCTemplateTagModel *)model;
// 带关键字搜索
- (void)showContent:(NSString *)sizeId keyWord:(NSString *)keyWord;
@end
