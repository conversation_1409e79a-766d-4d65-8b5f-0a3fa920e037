//
//  JCAddElementBar.m
//  XYFrameWork
//
//  Created by xingling xu on 2020/11/12.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import "JCAddElementBar.h"
#import "JCActionButton.h"

@interface JCAddElementBar ()
@property (nonatomic, strong) UIScrollView *scrollView;
@property (nonatomic, strong) NSArray *types;
@end

@implementation JCAddElementBar

- (instancetype)initWithDataTypes:(NSArray *)types {
    self = [super initWithFrame:(CGRect){0,0,kSCREEN_WIDTH,BoardAddElementHeight}];
    if (self) {
        self.backgroundColor = [UIColor whiteColor];
        self.types = types;
        [self loadRootView];
    }
    return self;
}

- (void)loadRootView {
    [self addSubview:self.scrollView];
    NSInteger count = self.types.count;
//    CGFloat space = (kSCREEN_WIDTH-42-buttonWidth*4)/3;
//    // > 4 个菜单则不是四均分，第五个菜单漏出边缘
//    if (count > 4) {
//        space -= 14;
//    }
    CGFloat space = (kSCREEN_WIDTH - buttonWidth * 5) / 6;
    self.scrollView.contentSize = (CGSize){buttonWidth*count + space * (count + 1)};
    @autoreleasepool {
        for (NSInteger i = 0; i < count; i ++) {
            JCAddAction type = [self.types[i] integerValue];
            ElementButtonInfo *info = [ElementButtonInfo buttonInfoWithType:type];
            JCActionButton *btn = [[JCActionButton alloc] initWithAddButtonInfo:info click:^(ElementButtonInfo * _Nonnull info) {
                NSLog(@"%@调用addElementWithSelName:参数为:%@",self.context.baseController, info.selName);
                JCPerformSelectorWithOneObject(self.context.baseController, sel(@"addElementWithSelName:"), info.selName);
            }];
            btn.frame = (CGRect){(space+buttonWidth)*i+21,20,btn.size};
            [self.scrollView addSubview:btn];
        }
    }
}

#pragma mark - getter
- (UIScrollView *)scrollView {
    if (!_scrollView) {
        _scrollView = [[UIScrollView alloc] initWithFrame:(CGRect){0,0,kSCREEN_WIDTH,BoardAddElementHeight}];
        _scrollView.delegate = self;
        _scrollView.showsHorizontalScrollIndicator = NO;
//        _scrollView.bounces = false;
    }
    return _scrollView;
}

@end
