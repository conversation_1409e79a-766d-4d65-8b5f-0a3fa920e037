//
//  JCAddElementView.m
//  XYFrameWork
//
//  Created by xingling xu on 2019/12/14.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCAddElementView.h"
#import "JCActionButton.h"

@interface JCAddElementView () <UIScrollViewDelegate>
@property (nonatomic, strong) UIScrollView *scrollView;
@property (nonatomic, strong) NSArray *types;
@property (nonatomic, strong) UIPageControl *pageControl;
@end

@implementation JCAddElementView

- (instancetype)initWithFrame:(CGRect)frame typeDatas:(NSArray *)types {
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = XY_HEX_RGB(0xF7F7F7);
        self.types = types;
        [self setUp];
    }
    return self;
}


- (void)setUp {
    [self addSubview:self.scrollView];
    [self addSubview:self.pageControl];
    NSInteger count = self.types.count;
    NSInteger pageNumber = (count-1)/8+1;
    self.pageControl.hidden = pageNumber <= 1;
    self.pageControl.numberOfPages = pageNumber;
    self.scrollView.contentSize = (CGSize){SCREEN_WIDTH*pageNumber,200};
    /** 每个按钮横向距离 */
    CGFloat hWidth = (self.scrollView.width-80-buttonWidth*4)/3;
    /** 每个按钮纵向距离 */
    CGFloat hHeight = self.scrollView.height-32-buttonHeight*2;
    @autoreleasepool {
        for (NSInteger i = 0; i < count; i ++) {
            JCAddAction type = [self.types[i] integerValue];
            ElementButtonInfo *info = [ElementButtonInfo buttonInfoWithType:type];
            JCActionButton *btn = [[JCActionButton alloc] initWithAddButtonInfo:info click:^(ElementButtonInfo * _Nonnull info) {
                JCPerformSelectorWithOneObject(self.context.baseController, sel(@"addElementWithSelName:"), info.selName);
            }];
            if (i < 8) {
                btn.frame = (CGRect){40+(buttonWidth+hWidth)*(i%4),16+(buttonHeight+hHeight)*(i/4),btn.size};
            } else {
                btn.frame = (CGRect){SCREEN_WIDTH+40+(buttonWidth+hWidth)*((i-8)%4),16+(buttonHeight+hHeight)*((i-8)/4),btn.size};
            }
            [self.scrollView addSubview:btn];
        }
    }
}

#pragma mark  UIScrollViewDelegate
-(void)scrollViewDidScroll:(UIScrollView *)scrollView{
    self.pageControl.currentPage = scrollView.contentOffset.x / self.scrollView.width;
}

#pragma mark -- lazy
- (UIScrollView *)scrollView {
    if (!_scrollView) {
        _scrollView = [[UIScrollView alloc] initWithFrame:(CGRect){0,0,SCREEN_WIDTH,200}];
        _scrollView.bounces = NO;
        _scrollView.pagingEnabled = YES;
        _scrollView.delegate = self;
        _scrollView.showsHorizontalScrollIndicator = NO;
    }
    return _scrollView;
}

-(UIPageControl *)pageControl{
    if(!_pageControl){
        _pageControl = [[UIPageControl alloc] initWithFrame:CGRectMake(0, 0, 200, 40)];
        _pageControl.centerX = self.width/2;
        _pageControl.bottom = self.height;
        _pageControl.enabled = NO;
        _pageControl.pageIndicatorTintColor = XY_HEX_RGB(0xB7BABF);
        _pageControl.currentPageIndicatorTintColor = XY_HEX_RGB(0x537FB7);
    }
    return _pageControl;
}
@end
