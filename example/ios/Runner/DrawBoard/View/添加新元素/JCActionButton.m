//
//  JCActionButton.m
//  XYFrameWork
//
//  Created by xingling xu on 2019/12/16.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCActionButton.h"

@interface JCActionButton ()
@property (nonatomic, strong) UIButton *button;
@end


@implementation JCActionButton

- (instancetype)initWithActionButtonInfo:(ElementButtonInfo *)info click:(void(^)(ElementButtonInfo *info))block {
    self = [super initWithFrame:(CGRect){0,0,46,48}];
    if (self) {
        UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
        button.frame = (CGRect){0,0,46,48};
        [button setImage:XY_IMAGE_NAMED(info.normalName) forState:UIControlStateNormal];
        [button setImage:XY_IMAGE_NAMED(info.highligtedName) forState:UIControlStateHighlighted];
        CGFloat bottomOffset = STR_IS_NIL(info.title)?0:14;
        [button setImageEdgeInsets:(UIEdgeInsets){0,0,bottomOffset,0}];
        [button jk_addActionHandler:^(NSInteger tag) {
            if (block) block(info);
        }];
        
        UILabel *label = [[UILabel alloc] init];
        label.text = info.title;
        label.font = MY_FONT_Regular(10);
        label.textColor = XY_HEX_RGB(0x666666);
        [label sizeToFit];
        label.top = 26;
        label.centerX = self.width/2;

        [self addSubview:button];
        [self addSubview:label];
    }
    return self;
}
- (instancetype)initWithAddButtonInfo:(ElementButtonInfo *)info click:(void(^)(ElementButtonInfo *info))block {
    self = [super initWithFrame:(CGRect){0,0,buttonWidth,buttonHeight}];
    if (self) {
        UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
        button.frame = (CGRect){0,0,buttonWidth,buttonWidth};
        [button setImage:XY_IMAGE_NAMED(info.normalName) forState:UIControlStateNormal];
        [button setImage:XY_IMAGE_NAMED(info.highligtedName) forState:UIControlStateHighlighted];
        [button jk_addActionHandler:^(NSInteger tag) {
            if (block) block(info);
        }];
        UILabel *label = [[UILabel alloc] init];
        label.text = info.title;
        label.font = MY_FONT_Regular(12);
        label.textColor = XY_HEX_RGB(0x666666);
        [label sizeToFit];
        label.top = button.bottom;
        label.centerX = self.width/2;
        
        [self addSubview:button];
        [self addSubview:label];
    }
    return self;
}

- (instancetype)initWithWidth:(CGFloat)width buttonInfo:(ElementButtonInfo *)info click:(void(^)(ElementButtonInfo *info))block {
    self = [super initWithFrame:(CGRect){0,0,width,44}];
    if (self) {
        self.button = [UIButton buttonWithType:UIButtonTypeCustom];
        self.button.frame = (CGRect){0,0,width,44};
        [self.button setImage:XY_IMAGE_NAMED(info.normalName) forState:UIControlStateNormal];
        [self.button setImage:XY_IMAGE_NAMED(info.highligtedName) forState:UIControlStateHighlighted];
        if (info.unableImageName.length > 0) {
            [self.button setImage:XY_IMAGE_NAMED(info.unableImageName) forState:UIControlStateDisabled];
        }
        [self.button jk_addActionHandler:^(NSInteger tag) {
            if (block) block(info);
        }];
        [self addSubview:self.button];
    }
    return self;
}

// refresh
- (void)refreshButtonWith:(ElementButtonInfo *)info click:(void(^)(ElementButtonInfo *info))block {
    [self.button setImage:XY_IMAGE_NAMED(info.normalName) forState:UIControlStateNormal];
    [self.button setImage:XY_IMAGE_NAMED(info.highligtedName) forState:UIControlStateHighlighted];
    if (block) {
        [self.button jk_addActionHandler:^(NSInteger tag) {
            if (block) block(info);
        }];
    }
}


- (void)setEnable:(BOOL)enable {
    self.button.enabled = enable;
}


- (void)refreshButtonWith:(ElementButtonInfo *)info scale:(CGFloat)scale click:(void(^)(ElementButtonInfo *info))block {
    if (info.actionType == JCElementAction_Reset) {
        if (![self viewWithTag:1000]) {
            UIImageView *background = [[UIImageView alloc] initWithImage:XY_IMAGE_NAMED(info.normalName)];
            background.contentMode = UIViewContentModeCenter;
            background.frame = self.button.frame;
            background.tag = 1000;
            [self insertSubview:background belowSubview:self.button];
        }
        
        [self.button setImage:nil forState:UIControlStateNormal];
        [self.button setImage:nil forState:UIControlStateHighlighted];
        [self.button setTitle:[NSString stringWithFormat:@"%.1fx",scale] forState:UIControlStateNormal];
        [self.button setTitleColor:XY_HEX_RGB(0x171717) forState:UIControlStateNormal];
        self.button.titleLabel.font = MY_FONT_Bold(8.0);
//        self.button.titleEdgeInsets = UIEdgeInsetsMake(3, 0, 3, 0);
    }
    if (block) {
        [self.button jk_addActionHandler:^(NSInteger tag) {
            if (block) block(info);
        }];
    }
}

@end

@implementation ElementButtonInfo

+ (ElementButtonInfo *)buttonInfoWithType:(JCAddAction)action {
    NSString *normalName = @"";
    NSString *highName = @"";
    NSString *title = @"";
    NSString *selName = @"";
    switch (action) {
        case JCAddAction_Text:
        {
            normalName = @"add_text";
            highName = @"add_text";
            title = XY_LANGUAGE_TITLE_NAMED(@"app00002",@"文本");
            selName = @"add_wenben";
        }
            break;
        case JCAddAction_Barcode:
        {
            normalName = @"编辑_一维码_normal";
            highName = @"编辑_一维码_press";
            title = XY_LANGUAGE_TITLE_NAMED(@"app00003",@"一维码");
            selName = @"add_yiweima";
        }
            break;
        case JCAddAction_Qrcode:
        {
            normalName = @"add_qrcode";
            highName = @"add_qrcode";
            title = XY_LANGUAGE_TITLE_NAMED(@"app00004",@"二维码");
            selName = @"add_erweima";
        }
            break;
        case JCAddAction_Graph:
        {
            normalName = @"编辑_形状_normal";
            highName = @"编辑_形状_press";
            title = XY_LANGUAGE_TITLE_NAMED(@"app00011",@"形状");
            selName = @"add_xingzhuang";
        }
            break;
        case JCAddAction_Table:
        {
            normalName = @"编辑_表格_normal";
            highName = @"编辑_表格_press";
            title = XY_LANGUAGE_TITLE_NAMED(@"app00005",@"表格");
            selName = @"add_biaoge";
        }
            break;
        case JCAddAction_Line:
        {
            normalName = @"add_xiantiao";
            highName = @"add_xiantiao";
            title = XY_LANGUAGE_TITLE_NAMED(@"app00013",@"线条");
            selName = @"add_xiantiao";
        }
            break;
        case JCAddAction_Image:
        {
            normalName = @"add_tupian";
            highName = @"add_tupian";
            title = XY_LANGUAGE_TITLE_NAMED(@"app00006",@"图片");
            selName = @"add_tupian";
        }
            break;
        case JCAddAction_Date:
        {
            normalName = @"add_shijian";
            highName = @"add_shijian";
            title = XY_LANGUAGE_TITLE_NAMED(@"app00010",@"时间");
            selName = @"add_shijian";
        }
            break;
        case JCAddAction_Serial:
        {
            normalName = @"编辑_流水号_normal";
            highName = @"编辑_流水号_press";
            title = XY_LANGUAGE_TITLE_NAMED(@"app00008",@"流水号");
            selName = @"add_liushuihao";
        }
            break;
        case JCAddAction_Logo:
        {
            normalName = @"编辑_行业logo_normal";
            highName = @"编辑_行业logo_press";
            title = XY_LANGUAGE_TITLE_NAMED(@"app00835",@"素材");
            selName = @"add_hangyelogo";
        }
            break;
        case JCAddAction_ExcelImport:
        {
            normalName = @"编辑_Excel导入_normal";
            highName = @"编辑_Excel导入_press";
            title = XY_LANGUAGE_TITLE_NAMED(@"app00009",@"Excel导入");
            selName = @"add_excel";
        }
            break;
        case JCAddAction_Scan:
        {
            normalName = @"编辑_扫描_normal";
            highName = @"编辑_扫描_press";
            title = XY_LANGUAGE_TITLE_NAMED(@"app00007",@"扫描");
            selName = @"add_saomiao";
        }
            break;
        case JCAddAction_Ocr:
        {
            normalName = @"编辑_智能识别_normal";
            highName = @"编辑_智能识别_press";
            title = XY_LANGUAGE_TITLE_NAMED(@"app00014",@"识别");
            selName = @"add_zhinengshibie";
        }
            break;
        case JCAddAction_Icon:
        {
            normalName = @"add_icon";
            highName = @"add_icon";
            title = XY_LANGUAGE_TITLE_NAMED(@"",@"图标");
            selName = @"add_icon";
        }
            break;
        case JCAddAction_Border:
        {
            normalName = @"add_box";
            highName = @"add_box";
            title = XY_LANGUAGE_TITLE_NAMED(@"",@"边框");
            selName = @"add_border";
        }
            break;
        case JCAddAction_Template:
        {
            normalName = @"add_template";
            highName = @"add_template";
            title = XY_LANGUAGE_TITLE_NAMED(@"",@"模板");
            selName = @"add_template";
        }
            break;
            
        default:
            break;
    }
    ElementButtonInfo *info = [ElementButtonInfo new];
    info.normalName = normalName;
    info.highligtedName = highName;
    info.title = title;
    info.addAction = action;
    info.selName = selName;
    return info;
}

+ (ElementButtonInfo *)buttonInfoWithAction:(JCElementAction)action {
    NSString *normalName = @"";
    NSString *highName = @"";
    NSString *title = @"";
    NSString *selName = @"";
    NSString *unableName = @"";
    switch (action) {
        case JCElementAction_Clear:
        {
            normalName = @"board_clear_normal";
            highName = @"board_clear_select";
            title = XY_LANGUAGE_TITLE_NAMED(@"",@"清空");
            selName = @"item_clear";
        }
            break;
        case JCElementAction_Delete:
        {
            normalName = @"action_delete";
            highName = @"action_delete";
            unableName = @"action_delete_unable";
            title = XY_LANGUAGE_TITLE_NAMED(@"app01059",@"删除");
            selName = @"item_shanchu";
        }
            break;
        case JCElementAction_Rotate:
        {
            normalName = @"action_rotate";
            highName = @"action_rotate";
            unableName = @"action_rotate_unable";
            title = XY_LANGUAGE_TITLE_NAMED(@"app00358",@"旋转");
            selName = @"item_xuanzhuan";
        }
            break;
        case JCElementAction_Mutiple:
        {
            normalName = @"action_mutable_selected";
            highName = @"action_mutable_selected";
            title = XY_LANGUAGE_TITLE_NAMED(@"",@"多选");
            selName = @"item_duoxuan";
        }
            break;
        case JCElementAction_SigleSelect:
        {
            normalName = @"action_mutable_normal";
            highName = @"action_mutable_normal";
            title = XY_LANGUAGE_TITLE_NAMED(@"app00102",@"单选");
            selName = @"item_duoxuan";
        }
            break;
        case JCElementAction_Undo:
        {
            normalName = @"action_undo_enable";
            highName = @"action_undo_enable";
            title = XY_LANGUAGE_TITLE_NAMED(@"app00359",@"撤销");
            selName = @"item_chexiao";
        }
            break;
        case JCElementAction_NoUndo:
        {
            normalName = @"action_undo_unable";
            highName = @"action_undo_unable";
            title = XY_LANGUAGE_TITLE_NAMED(@"app00359",@"撤销");
            selName = @"item_chexiao";
        }
            break;
        case JCElementAction_Redo:
        {
            normalName = @"action_redo_enable";
            highName = @"action_redo_enable";
            title = XY_LANGUAGE_TITLE_NAMED(@"app00360",@"恢复");
            selName = @"item_huifu";
        }
            break;
        case JCElementAction_NoRedo:
        {
            normalName = @"action_redo_unable";
            highName = @"action_redo_unable";
            title = XY_LANGUAGE_TITLE_NAMED(@"app00360",@"恢复");
            selName = @"item_huifu";
        }
            break;
        case JCElementAction_Copy:
        {
            normalName = @"action_copy";
            highName = @"action_copy";
            unableName = @"action_copy_unable";
            title = XY_LANGUAGE_TITLE_NAMED(@"app00361",@"复制");
            selName = @"item_fuzhi";
        }
            break;
        case JCElementAction_Lock:
        {
            normalName = @"action_lock";
            highName = @"action_lock";
            unableName = @"action_lock_unable";
            title = XY_LANGUAGE_TITLE_NAMED(@"app00362",@"锁定");
            selName = @"item_suoding";
        }
            break;
        case JCElementAction_UnLock:
        {
            normalName = @"action_unlock";
            highName = @"action_unlock";
            unableName = @"action_lock_unable";
            title = XY_LANGUAGE_TITLE_NAMED(@"",@"解锁");
            selName = @"item_suoding";
        }
            break;
        case JCElementAction_ShowMain:
        {
            normalName = @"组件_normal";
            highName = @"组件_press";
            title = XY_LANGUAGE_TITLE_NAMED(@"app01034",@"组件");
            selName = @"item_showmain";
        }
            break;
        case JCElementAction_Reset:
        {
            normalName = @"action_scale";
            highName = @"action_scale";
            title = XY_LANGUAGE_TITLE_NAMED(@"",@"复位");
            selName = @"item_reset";
        }
            break;
            
        default:
            break;
    }
    ElementButtonInfo *info = [ElementButtonInfo new];
    info.normalName = normalName;
    info.highligtedName = highName;
    info.unableImageName = unableName;
    info.title = title;
    info.actionType = action;
    info.selName = selName;
    return info;
}
@end
