//
//  JCActionButton.h
//  XYFrameWork
//
//  Created by xingling xu on 2019/12/16.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import <UIKit/UIKit.h>

static CGFloat buttonWidth = 50;
static CGFloat buttonHeight = 67;

@class ElementButtonInfo;
@interface JCActionButton : UIView
/** 删除、锁定等功能按钮 */
- (instancetype)initWithActionButtonInfo:(ElementButtonInfo *)info click:(void(^)(ElementButtonInfo *info))block;
/** 添加元素等功能按钮 */
- (instancetype)initWithAddButtonInfo:(ElementButtonInfo *)info click:(void(^)(ElementButtonInfo *info))block;

// 固定宽度
- (instancetype)initWithWidth:(CGFloat)width buttonInfo:(ElementButtonInfo *)info click:(void(^)(ElementButtonInfo *info))block;

- (void)refreshButtonWith:(ElementButtonInfo *)info click:(void(^)(ElementButtonInfo *info))block;

- (void)setEnable:(BOOL)enable;

//  缩放按钮
- (void)refreshButtonWith:(ElementButtonInfo *)info scale:(CGFloat)scale click:(void(^)(ElementButtonInfo *info))block;
@end

/** 每一个添加元素按钮的信息 */
@interface ElementButtonInfo : NSObject
@property (nonatomic, copy) NSString *normalName;
@property (nonatomic, copy) NSString *highligtedName;
@property (nonatomic, copy) NSString *unableImageName;
@property (nonatomic, copy) NSString *title;
@property (nonatomic, copy) NSString *selName;
@property (nonatomic, assign)  JCAddAction addAction;
@property (nonatomic, assign)  JCElementAction actionType;

+ (ElementButtonInfo *)buttonInfoWithType:(JCAddAction)action;

+ (ElementButtonInfo *)buttonInfoWithAction:(JCElementAction)action;
@end

