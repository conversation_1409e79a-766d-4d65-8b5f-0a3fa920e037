//
//  JCElementEditBar.m
//  XYFrameWork
//
//  Created by xingling xu on 2019/12/14.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCElementEditBar.h"
#import "JCActionButton.h"

@interface JCElementEditBar ()
@property (nonatomic, strong) UIScrollView *scrollView;
@property (nonatomic, strong) UIView *leftView;
@property (nonatomic, strong) UIView *rightView;
@property (nonatomic, strong) UIImageView *triangleView;
//@property (nonatomic, strong) UIView *folderBackView;
@end

@implementation JCElementEditBar

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = COLOR_WHITE;
        self.location = LocationTop;
        [self setUp];
    }
    return self;
}

- (void)setUp {
    UIView *line = [[UIView alloc] initWithFrame:(CGRect){0,0,SCREEN_WIDTH,1}];
    line.backgroundColor = XY_HEX_RGBA(0xeeeeee, 1);
    [self addSubview:line];
    
    line = [[UIView alloc] initWithFrame:(CGRect){0,47.5,SCREEN_WIDTH,0.5}];
    line.backgroundColor = XY_HEX_RGBA(0xeeeeee, 1);
    [self addSubview:line];
    
    [self addSubview:self.scrollView];
    [self.scrollView addSubview:self.leftView];
    [self.scrollView addSubview:self.rightView];
}

#pragma mark -- setter
- (void)setLeftActionTypes:(NSArray *)leftActionTypes {
    _leftActionTypes = leftActionTypes;
    [self.leftView.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
    
    UIView *line = [[UIView alloc] initWithFrame:(CGRect){47,10,1,28}];
    line.backgroundColor = XY_HEX_RGB(0xD4D4D4);
    [self.leftView addSubview:line];
    // 白色背景图
//    [self.leftView addSubview:self.folderBackView];
    for (NSInteger i = 0; i < _leftActionTypes.count; i ++) {
        JCElementAction type = [_leftActionTypes[i] integerValue];
        ElementButtonInfo *info = [ElementButtonInfo buttonInfoWithAction:type];
        JCWeakSelf
        JCActionButton *btn = [[JCActionButton alloc] initWithActionButtonInfo:info click:^(ElementButtonInfo * _Nonnull info) {
            JCStrongSelf
            JCPerformSelectorWithOneObject(self.context.baseController, sel(@"actionElementWith:"), info.selName)
        }];
        btn.frame = (CGRect){46*i,0,46,48};
        [self.leftView addSubview:btn];
    }
    
    [self.leftView addSubview:self.triangleView];
    
}

- (void)setRightActionTypes:(NSArray *)rightActionTypes {
    _rightActionTypes = rightActionTypes;
    [self.rightView.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
    for (NSInteger i = _rightActionTypes.count-1; i >= 0; i --) {
        JCElementAction type = [_rightActionTypes[i] integerValue];
        ElementButtonInfo *info = [ElementButtonInfo buttonInfoWithAction:type];
        JCWeakSelf
        JCActionButton *btn = [[JCActionButton alloc] initWithActionButtonInfo:info click:^(ElementButtonInfo * _Nonnull info) {
            JCStrongSelf
            JCPerformSelectorWithOneObject(self.context.baseController, sel(@"actionElementWith:"), info.selName)
        }];
        btn.frame = (CGRect){self.rightView.width-46*(_rightActionTypes.count - i),0,46,48};
        [self.rightView addSubview:btn];
    }
}

- (void)setShowComponent:(BOOL)showComponent {
    _showComponent = showComponent;
    self.triangleView.hidden = !showComponent;
//    UIColor *color = showComponent?XY_HEX_RGB(0xF7F7F7):[UIColor clearColor];
//    self.folderBackView.backgroundColor = color;
}

#pragma mark -- lazy
- (UIScrollView *)scrollView {
    if (!_scrollView) {
        _scrollView = [[UIScrollView alloc] initWithFrame:(CGRect){0,0,SCREEN_WIDTH,48}];
        _scrollView.bounces = NO;
        _scrollView.showsHorizontalScrollIndicator = NO;
    }
    return _scrollView;
}

- (UIView *)leftView {
    if (!_leftView) {
        _leftView = [[UIView alloc] initWithFrame:(CGRect){0,0,48,48}];
    }
    return _leftView;
}

- (UIView *)rightView {
    if (!_rightView) {
        _rightView = [[UIView alloc] initWithFrame:(CGRect){48,0,self.scrollView.width - 48,48}];
    }
    return _rightView;
}

- (UIImageView *)triangleView {
    if (!_triangleView) {
        _triangleView = [[UIImageView alloc] initWithFrame:(CGRect){17,41,14,7}];
        _triangleView.image = [UIImage imageNamed:@"board_bar_triagle"];
    }
    return _triangleView;
}

//- (UIView *)folderBackView {
//    if (!_folderBackView) {
//        _folderBackView = [[UIView alloc] initWithFrame:(CGRect){0,1,50,50}];
//        _folderBackView.backgroundColor = XY_HEX_RGB(0xF7F7F7);
//    }
//    return _folderBackView;
//}
@end



