//
//  JCSelectionView.m
//  XYFrameWork
//
//  Created by xingling xu on 2019/12/12.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCSelectionView.h"

static NSInteger base_tag = 1111;

@interface JCSelectionView ()

@property (nonatomic, strong) NSArray *normalNames;
@property (nonatomic, strong) NSArray *selectNames;
@property (nonatomic, strong) UIButton *selectButton;

@end

@implementation JCSelectionView

- (instancetype)initWithFrame:(CGRect)frame normalImageNames:(NSArray *)normalNames selectImageNames:(NSArray *)selectNames {
    self = [super initWithFrame:frame];
    if (self) {
        self.normalNames = normalNames;
        self.selectNames = selectNames;
        [self setUp];
    }
    return self;
}

- (void)setUp {
    /** 图片数量要求一致 */
    if (self.normalNames.count != self.selectNames.count)return;
    [self.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
    self.backgroundColor = XY_HEX_RGB(0xF7F7F7);
    self.layer.cornerRadius = 10;
    self.layer.masksToBounds = YES;
    
    NSInteger count = self.normalNames.count;
    CGFloat everyWidth = self.width/count;
    for (NSInteger i = 0; i < count ; i ++) {
        UIImage *image = [UIImage imageNamed:self.normalNames[i]];
        UIImageView *imgView = [[UIImageView alloc] initWithFrame:(CGRect){0,0,image.size}];
        imgView.image = image;
        imgView.centerX = i*everyWidth + everyWidth/2;
        imgView.centerY = self.height/2;
        imgView.tag = base_tag + i;
        [self addSubview:imgView];
    }
    
    [self addSubview:self.selectButton];
    
    /** 默认选第一个 */
    self.selectIndex = 0;
    
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(choose:)];
    [self addGestureRecognizer:tap];
}

- (void)checkDisableState {
    NSArray *imageArr = _disabled?self.disableImageNames:self.normalNames;
    NSInteger count = self.normalNames.count;
    for (NSInteger i = 0; i < count ; i ++) {
        UIImageView *imgV = [self viewWithTag:base_tag+i];
        imgV.image =  [UIImage imageNamed:[imageArr safeObjectAtIndex:i]];
    }
    [self.selectButton setImage:[UIImage imageNamed:self.selectNames[_selectIndex]] forState:UIControlStateNormal];
}

#pragma mark -- setter
- (void)setSelectIndex:(NSInteger)selectIndex {
    _selectIndex = selectIndex;
    CGFloat everyWidth = self.width/self.normalNames.count;
    self.selectButton.centerX = selectIndex*everyWidth + everyWidth/2;
    [self checkDisableState];
}

- (void)setDisabled:(BOOL)disabled {
    _disabled = disabled;
    [self checkDisableState];
}


- (void)setSelectButtonHidden:(BOOL)selectButtonHidden {
    _selectButtonHidden = selectButtonHidden;
    self.selectButton.hidden = selectButtonHidden;
}

- (JCSelectionView *(^)(NSArray *normalNames,NSArray *selectNames))changeImage {
    return ^(NSArray *normalNames,NSArray *selectNames){
        self.normalNames = normalNames;
        self.selectNames = selectNames;
        [self setUp];
        return self;
    };
}
#pragma mark -- gesture
- (void)choose:(UITapGestureRecognizer *)recognizer {
    if (self.disabled) return;
    CGPoint location = [recognizer locationInView:self];
    CGFloat everyWidth = self.width/self.normalNames.count;
    NSInteger index = location.x/everyWidth;
    NSString *imageName = self.selectNames[index];
    [UIView animateWithDuration:0.1 animations:^{
        self.selectButton.centerX = index*everyWidth + everyWidth/2;
    } completion:^(BOOL finished) {
        [self.selectButton setImage:[UIImage imageNamed:imageName] forState:UIControlStateNormal];
        if (self.delegate && [self.delegate respondsToSelector:@selector(selectionView:selectIndex:)]) {
            [self.delegate selectionView:self selectIndex:index];
        }
    }];
}

#pragma mark -- lazy
- (UIButton *)selectButton {
    if (!_selectButton) {
        _selectButton = [UIButton buttonWithType:UIButtonTypeCustom];
        _selectButton.layer.cornerRadius = 10;
        _selectButton.layer.masksToBounds = YES;
        _selectButton.size = (CGSize){46,32};
        _selectButton.left = 4;
        _selectButton.centerY = self.height/2;;
        [_selectButton setBackgroundColor:[UIColor whiteColor]];
    }
    return _selectButton;
}
@end
