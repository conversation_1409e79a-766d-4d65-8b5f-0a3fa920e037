//
//  JCSelectionView.h
//  XYFrameWork
//
//  Created by xingling xu on 2019/12/12.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN
@class JCSelectionView;
@protocol JCSelectionViewDelegate <NSObject>

- (void)selectionView:(JCSelectionView *)selectionView selectIndex:(NSInteger)index;

@end

@interface JCSelectionView : UIView

@property (nonatomic, assign) NSInteger selectIndex;
/** 针对位移编辑区的显示，不需要选中样式 */
@property (nonatomic, assign) BOOL selectButtonHidden;
@property (nonatomic, weak) id<JCSelectionViewDelegate> delegate;
@property (nonatomic, assign) BOOL disabled;
@property (nonatomic, copy) NSArray *disableImageNames;

- (instancetype)initWithFrame:(CGRect)frame normalImageNames:(NSArray *)normalNames selectImageNames:(NSArray *)selectNames;

- (JCSelectionView *(^)(NSArray *normalNames,NSArray *selectNames))changeImage;
@end

NS_ASSUME_NONNULL_END
