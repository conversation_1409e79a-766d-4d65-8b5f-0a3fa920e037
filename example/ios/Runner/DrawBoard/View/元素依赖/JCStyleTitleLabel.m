//
//  JCStyleTitleLabel.m
//  XYFrameWork
//
//  Created by xingling xu on 2019/12/10.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCStyleTitleLabel.h"

@implementation JCStyleTitleLabel

- (instancetype)initWithText:(NSString *)text {
    self = [super init];
    if (self) {
        self.font = [UIFont systemFontOfSize:12];
        self.textAlignment = NSTextAlignmentCenter;
        self.numberOfLines = 0;
        self.lineBreakMode = NSLineBreakByWordWrapping;
        self.textColor = XY_HEX_RGB(0x666666);
        self.text = text;
        BOOL isZH = [XY_JC_LANGUAGE hasPrefix:@"zh-"];
        CGFloat maxWidth = isZH?100:64;
        CGSize size = [self sizeThatFits:(CGSize){maxWidth,CGFLOAT_MAX}];
        CGFloat width = isZH?size.width:64;
        self.size = (CGSize){width,size.height};
    }
    return self;
}

- (instancetype)initWithOneLineText:(NSString *)text {
    self = [super init];
    if (self) {
        self.font = [UIFont systemFontOfSize:12];
        self.numberOfLines = 0;
        self.textColor = XY_HEX_RGB(0x666666);
        self.text = text;
        self.lineBreakMode = NSLineBreakByWordWrapping;
        CGSize size = [self sizeThatFits:(CGSize){300,CGFLOAT_MAX}];
        self.size = size;
    }
    return self;
}
@end
