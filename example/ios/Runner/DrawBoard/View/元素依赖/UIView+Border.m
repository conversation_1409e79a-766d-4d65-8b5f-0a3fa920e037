//
//  UIView+Border.m
//  XYFrameWork
//
//  Created by xingling xu on 2019/11/29.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "UIView+Border.h"

@implementation UIView(Border)

- (void)showSelectBorder {
    self.layer.borderWidth = 1;
    self.layer.borderColor = XY_HEX_RGB(0x00a7ff).CGColor;
}

- (void)showLockedBorder {
    self.layer.borderWidth = 1;
    self.layer.borderColor = XY_HEX_RGB(0xdddddd).CGColor;
}

- (void)hiddenBorder {
    self.layer.borderWidth = 0;
}

@end
