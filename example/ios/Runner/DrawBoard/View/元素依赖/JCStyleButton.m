//
//  JCStyleButton.m
//  XYFrameWork
//
//  Created by xingling xu on 2019/12/13.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCStyleButton.h"

@interface JCStyleButton ()
@property (nonatomic, copy) NSString *placeHolder;
@end

@implementation JCStyleButton

+ (void)load {
//    [EasyKit swizzleMethod:@selector(setTitle:forState:) with:@selector(jc_setTitle:forState:) in:NSClassFromString(@"JCStyleButton")];
    [self swizzleMethod:@selector(setTitle:forState:) in:NSClassFromString(@"JCStyleButton") with:@selector(jc_setTitle:forState:) in:NSClassFromString(@"JCStyleButton")];
}

+ (BOOL)swizzleMethod:(SEL)originalSelector in:(Class)klass with:(SEL)anotherSelector in:(Class)anotherKlass {
  Method originalMethod = class_getInstanceMethod(klass, originalSelector);
  Method anotherMethod  = class_getInstanceMethod(anotherKlass, anotherSelector);
  if(!originalMethod || !anotherMethod) {
    return NO;
  }
  IMP originalMethodImplementation = class_getMethodImplementation(klass, originalSelector);
  IMP anotherMethodImplementation  = class_getMethodImplementation(anotherKlass, anotherSelector);
  if(class_addMethod(klass, originalSelector, originalMethodImplementation, method_getTypeEncoding(originalMethod))) {
    originalMethod = class_getInstanceMethod(klass, originalSelector);
  }
  if(class_addMethod(anotherKlass, anotherSelector,  anotherMethodImplementation,  method_getTypeEncoding(anotherMethod))) {
    anotherMethod = class_getInstanceMethod(anotherKlass, anotherSelector);
  }
  method_exchangeImplementations(originalMethod, anotherMethod);
  return YES;
}
- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        self.contentHorizontalAlignment = UIControlContentHorizontalAlignmentLeft;
        self.layer.cornerRadius = 10;
        self.layer.masksToBounds = YES;
        self.titleLabel.font = [UIFont systemFontOfSize:14];
        self.titleEdgeInsets = (UIEdgeInsets){0,16,0,16};
        self.imageEdgeInsets = (UIEdgeInsets){0,self.width-16,0,0};
        [self setImage:[UIImage imageNamed:@"right_arrow"] forState:UIControlStateNormal];
        [self setTitleColor:XY_HEX_RGB(0x666666) forState:UIControlStateNormal];
        [self setBackgroundImage:[UIImage imageWithColor:XY_HEX_RGB(0xf7f7f7)] forState:UIControlStateNormal];
    }
    return self;
}

- (void)jc_setTitle:(NSString *)title forState:(UIControlState)state {
    NSString *value = @"";
    if (![title isKindOfClass:[NSNull class]] && title && title.length > 0) {
        value = title;
        [self setTitleColor:XY_HEX_RGB(0x666666) forState:UIControlStateNormal];
    } else {
        if (self.placeHolder && self.placeHolder.length > 0) {
            value = self.placeHolder;
            [self setTitleColor:XY_HEX_RGB(0xcccccc) forState:UIControlStateNormal];
        }
    }
    [self jc_setTitle:value forState:state];
}

- (JCStyleButton *(^)(BOOL))showImage {
    return ^(BOOL show){
        if (show) {
            [self setImage:[UIImage imageNamed:@"right_arrow"] forState:UIControlStateNormal];
        } else {
            [self setImage:nil forState:UIControlStateNormal];
        }
        return self;
    };
}

- (JCStyleButton *(^)(NSString *value))jcPlaceHolder {
    return ^(NSString *value){
        self.placeHolder = value;
        return self;
    };
}

- (void)setEnabled:(BOOL)enabled {
    [super setEnabled:enabled];
    if (enabled) {
        [self setTitleColor:XY_HEX_RGB(0x666666) forState:UIControlStateNormal];
    } else {
        [self setTitleColor:XY_HEX_RGB(0xcccccc) forState:UIControlStateNormal];
    }
}

@end
