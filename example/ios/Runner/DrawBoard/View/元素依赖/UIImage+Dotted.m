

//
//  UIImageView+Dotted.m
//  XYFrameWork
//
//  Created by xingling xu on 2019/11/26.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "UIImage+Dotted.h"

@implementation UIImage (Dotted)

#pragma mark -- 虚线

+ (UIImage *)dottedLineImage:(CGFloat)width height:(CGFloat)height {
    UIGraphicsBeginImageContext(CGSizeMake(width, height)); //开始画线 划线的frame
    CGContextRef currentContext = UIGraphicsGetCurrentContext();
    //设置虚线颜色
    CGContextSetStrokeColorWithColor(currentContext, COLOR_BLACK.CGColor);
    //设置虚线宽度
    CGContextSetLineWidth(currentContext, height*2);
    //设置虚线绘制起点
    CGContextMoveToPoint(currentContext, 0, 0);
    //设置虚线绘制终点
    if(width > height){
        CGContextAddLineToPoint(currentContext, width, 0);
    }else {
        CGContextAddLineToPoint(currentContext, 0, height);
    }
    //设置虚线排列的宽度间隔:下面的arr中的数字表示先绘制5个点再绘制5个点
    CGFloat arr[] = {5,5};
    //下面最后一个参数“2”代表排列的个数。
    CGContextSetLineDash(currentContext, 0, arr, sizeof(arr)/sizeof(arr[0]));
    CGContextDrawPath(currentContext, kCGPathStroke);
    UIImage *image =   UIGraphicsGetImageFromCurrentImageContext();  // 返回的就是image
    CGContextEndPage(currentContext);
    CGContextRelease(currentContext);
    return image;
}


@end
