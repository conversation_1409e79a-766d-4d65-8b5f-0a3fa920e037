//
//  JCSliderView.h
//  Runner
//
//  Created by xingling xu on 2021/1/14.
//

#import <UIKit/UIKit.h>

@class JCSliderView;
@protocol JCSliderViewDelegate <NSObject>
/** 滑动的过程中获取当前选择的值：该值来自于values */
- (void)sliderView:(JCSliderView *)sliderView selectIndex:(NSInteger)index  selectValue:(NSString *)value;
/** 获取滑动之前的value */
- (void)sliderView:(JCSliderView *)sliderView selectIndex:(NSInteger)index  valueChangeBegin:(NSString *)beginValue;
/** 获取滑动操作完毕后的value */
- (void)sliderView:(JCSliderView *)sliderView selectIndex:(NSInteger)index  valueChangeEnd:(NSString *)endValue;

- (void)sliderView:(JCSliderView *)sliderView maxValue:(NSString *)maxValue;

- (void)sliderView:(JCSliderView *)sliderView inputWithCurrentValue:(NSString *)value;
@end

typedef NS_ENUM(NSUInteger, JCSliderAccessory) {
    JCSliderAccessoryNone,
    JCSliderAccessoryButton,
};

@interface JCSliderView : UIView
- (instancetype)initWithFrame:(CGRect)frame values:(NSArray *)values type:(JCSliderAccessory)type;
- (instancetype)initWithFrame:(CGRect)frame values:(NSArray *)values;
@property (nonatomic, weak) id delegate;
@property(nonatomic,assign)JCSliderAccessory type;

/** 设置 */
- (void)selectValue:(NSString *)selectValue animated:(BOOL)animated;
@end


