//
//  JCFontSliderView.m
//  XYFrameWork
//
//  Created by xingling xu on 2020/10/27.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import "JCFontSliderView.h"
#import "UIImage+EasyExtend.h"

static const CGFloat slider_height = 46.f;
static const CGFloat slier_right_width = 120.f;
static const NSInteger dot_tag = 1000;
static const float ratio = 0.7;// 超过宽度的70% 自动吸附到下一个节点

@interface JCFontSliderView ()
@property (nonatomic, strong) UIButton *decreaseBtn;
@property (nonatomic, strong) UIButton *increaseBtn;
@property (nonatomic, strong) UISlider *slider;
@property (nonatomic, strong) UILabel *resultLabel;
@property (nonatomic, assign) NSInteger oldSelectIndex;
@property (nonatomic, assign) BOOL firstNearMax;
// 是否可输入修改
@property (nonatomic, assign) BOOL canInput;
// 输入按钮
@property (nonatomic, strong) UIButton *resultButton;
@end

@implementation JCFontSliderView

- (instancetype)initWithFrame:(CGRect)frame values:(NSArray *)values {
    return [self initWithFrame:frame values:values canInput:NO];
}

- (instancetype)initWithFrame:(CGRect)frame values:(NSArray *)values canInput:(BOOL)canInput {
    self = [super initWithFrame:frame];
    if (self) {
        self.currentMaxIndex = NSIntegerMax;
        self.values = values;
        self.canInput = canInput;
        [self setUp];
    }
    return self;
}
- (void)setUp {
    [self addSubview:self.decreaseBtn];
    [self addSubview:self.increaseBtn];
    [self addSubview:self.slider];
}

- (void)layoutSubviews {
    [super layoutSubviews];
    self.decreaseBtn.left = 0;
    self.decreaseBtn.top = 0;
    self.increaseBtn.right = self.width;
    self.increaseBtn.centerY = self.decreaseBtn.centerY;
    self.slider.left = self.decreaseBtn.right + 20;
    self.slider.centerY = self.decreaseBtn.centerY;
    
    
    
    self.resultLabel.top = self.decreaseBtn.bottom + 5;
    self.resultLabel.centerX = 0;
}

- (void)inputValue:(UIButton *)button {
    if (self.delegate && [self.delegate respondsToSelector:@selector(sliderView:inputWithCurrentValue:)]) {
        [self.delegate sliderView:self inputWithCurrentValue:self.resultLabel.text];
    }
}

#pragma mark -- setter
- (void)setValues:(NSArray *)values {
    NSAssert(values.count > 1, @"values must contain 2 elements at least");
    _values = values;
}

- (void)setResultColor:(UIColor *)resultColor {
    _resultColor = resultColor;
    self.resultLabel.textColor = resultColor;
}

- (void)setEnabled:(BOOL)enabled {
    _enabled = enabled;
    self.slider.enabled = enabled;
}

- (void)refreshResultWith:(NSString *)result {
    self.resultLabel.text = result;
    [self.resultButton setTitle:result forState:UIControlStateNormal];
}

#pragma mark - delegate
- (void)notifySliderChangeValue:(NSString *)value withIndex:(NSInteger)index {
    if (self.delegate && [self.delegate respondsToSelector:@selector(sliderView:selectIndex:selectValue:)]) {
        [self.delegate sliderView:self selectIndex:index selectValue:value];
    }
}
- (void)selectValue:(NSString *)selectValue animated:(BOOL)animated {
    CGFloat sliderWidth = self.width-slier_right_width;
    CGFloat everyWidth = sliderWidth/(self.values.count-1);
    NSInteger index = [self.values indexOfObject:selectValue];
    self.oldSelectIndex = index;
    [self.slider setValue:index*everyWidth/sliderWidth animated:animated];
    [self refreshResultWith:selectValue];
    [self.slider setThumbImage:[self thumbViewWithValue:selectValue] forState:UIControlStateNormal];
}

- (void)sliderValueChanged:(id)sender {
    UISlider *slider = (UISlider *)sender;
    CGFloat sliderWidth = self.width-slier_right_width;
    CGFloat everyWidth = sliderWidth/(self.values.count-1);
    CGFloat selectWidth = sliderWidth*slider.value;
    NSInteger index = selectWidth/everyWidth;
    if (self.oldSelectIndex != index) {
        if (index >= self.currentMaxIndex) {
            if (self.oldSelectIndex < self.currentMaxIndex) {// 只有第一次超过范围才告知，以后来回在最大值处波动不处理
                if (self.delegate && [self.delegate respondsToSelector:@selector(sliderView:maxValue:)]) {
                    [self.delegate sliderView:self maxValue:[self.values safeObjectAtIndex:self.currentMaxIndex]];
                }
            }
            self.oldSelectIndex = self.currentMaxIndex;
            NSString *currentValue = [self.values safeObjectAtIndex:self.oldSelectIndex];
            [self selectValue:currentValue animated:NO];
            [self.slider setThumbImage:[self thumbViewWithValue:currentValue] forState:UIControlStateNormal];
        } else {
            self.oldSelectIndex = index;
            NSString *currentValue = [self.values safeObjectAtIndex:index];
            [self refreshResultWith:currentValue];
            [self notifySliderChangeValue:self.resultLabel.text withIndex:index];
            [self.slider setThumbImage:[self thumbViewWithValue:self.resultLabel.text] forState:UIControlStateNormal];
        }
    }
    [self refreshButtonState];
}

- (void)zoomDragBegin: (UIControl *) c withEvent:touches
{
    if (self.delegate && [self.delegate respondsToSelector:@selector(sliderView:selectIndex:valueChangeBegin:)]) {
        [self.delegate sliderView:self selectIndex:self.oldSelectIndex  valueChangeBegin:self.resultLabel.text];
    }
}

- (void)zoomDragEnded: (UIControl *) c withEvent:touches
{
    /**  添加自动吸附功能 */
    float value = self.slider.value;
    CGFloat sliderWidth = self.width-slier_right_width;
    CGFloat everyWidth = sliderWidth/(self.values.count-1);
    CGFloat curLeftLength = sliderWidth*value;
    NSInteger index = curLeftLength/everyWidth;
    CGFloat extraLength = curLeftLength-index*everyWidth;
    float x = extraLength/everyWidth;
    if (x > ratio) {
        index++;
    }
    self.oldSelectIndex = index;
    [self refreshResultWith:[self.values safeObjectAtIndex:index]];
    [self.slider setValue:index*everyWidth/sliderWidth animated:YES];
    if (self.delegate && [self.delegate respondsToSelector:@selector(sliderView:selectIndex:valueChangeEnd:)]) {
        [self.delegate sliderView:self selectIndex:index valueChangeEnd:self.resultLabel.text];
    }
    [self notifySliderChangeValue:self.resultLabel.text withIndex:index];
    [self.slider setThumbImage:[self thumbViewWithValue:self.resultLabel.text] forState:UIControlStateNormal];
}

- (void)decrease:(UIButton *)button {
    if (self.oldSelectIndex == 0) return;
    NSInteger index = MAX(self.oldSelectIndex-1, 0);
    CGFloat sliderWidth = self.width-slier_right_width;
    CGFloat everyWidth = sliderWidth/(self.values.count-1);
    self.oldSelectIndex = index;
    [self refreshResultWith:[self.values safeObjectAtIndex:index]];
    [self.slider setValue:index*everyWidth/sliderWidth animated:YES];
    if (self.delegate && [self.delegate respondsToSelector:@selector(sliderView:selectIndex:valueChangeEnd:)]) {
        [self.delegate sliderView:self selectIndex:index valueChangeEnd:self.resultLabel.text];
    }
    [self notifySliderChangeValue:self.resultLabel.text withIndex:index];
    [self.slider setThumbImage:[self thumbViewWithValue:self.resultLabel.text] forState:UIControlStateNormal];
    [self refreshButtonState];
}

- (void)increase:(UIButton *)button {
    NSInteger index = MIN(self.oldSelectIndex+1, self.values.count-1);
    if (self.oldSelectIndex != index) {
        if (index > self.currentMaxIndex) {
            if (self.delegate && [self.delegate respondsToSelector:@selector(sliderView:maxValue:)]) {
                [self.delegate sliderView:self maxValue:[self.values safeObjectAtIndex:self.currentMaxIndex]];
            }
            self.oldSelectIndex = self.currentMaxIndex;
            NSString *currentValue = [self.values safeObjectAtIndex:self.oldSelectIndex];
            [self selectValue:currentValue animated:NO];
            [self.slider setThumbImage:[self thumbViewWithValue:currentValue] forState:UIControlStateNormal];
        } else {
            CGFloat sliderWidth = self.width-slier_right_width;
            CGFloat everyWidth = sliderWidth/(self.values.count-1);
            self.oldSelectIndex = index;
            [self refreshResultWith:[self.values safeObjectAtIndex:index]];
            [self.slider setValue:index*everyWidth/sliderWidth animated:YES];
            if (self.delegate && [self.delegate respondsToSelector:@selector(sliderView:selectIndex:valueChangeEnd:)]) {
                [self.delegate sliderView:self selectIndex:index valueChangeEnd:self.resultLabel.text];
            }
            [self notifySliderChangeValue:self.resultLabel.text withIndex:index];
            [self.slider setThumbImage:[self thumbViewWithValue:self.resultLabel.text] forState:UIControlStateNormal];
        }
    }
    [self refreshButtonState];
}

- (void)refreshButtonState {
    self.increaseBtn.enabled = self.oldSelectIndex < 18;
    self.decreaseBtn.enabled = self.oldSelectIndex > 0;
}

- (UIImage *)thumbViewWithValue:(NSString *)value {
    UIView *view = [[UIView alloc] initWithFrame:(CGRect){0,0,60,105}];
    view.backgroundColor = [UIColor clearColor];
    
    UIImageView *bubble = [[UIImageView alloc] initWithFrame:(CGRect){0,0,60,45}];
    bubble.image = [UIImage imageNamed:@"font_bubble"];
    [view addSubview:bubble];
    
    UIImageView *icon = [[UIImageView alloc] initWithFrame:(CGRect){view.width/2-17.5,view.height/2-17.5+2,35,35}];
    icon.image = [UIImage imageNamed:@"font_circle"];
    [view addSubview:icon];
    
    UILabel *resultLabel = [[UILabel alloc] initWithFrame:(CGRect){10,8,60,20}];
    resultLabel.font = MY_FONT_Bold(13);
    resultLabel.textColor = XY_HEX_RGB(0x262626);
    resultLabel.textAlignment = NSTextAlignmentCenter;
    resultLabel.text = value;
    resultLabel.centerX = bubble.width/2;
    [bubble addSubview:resultLabel];
    
    UIGraphicsBeginImageContextWithOptions(view.frame.size, NO, [UIScreen mainScreen].scale);
    [view.layer renderInContext:UIGraphicsGetCurrentContext()];
    UIImage *snapshotImage = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    
    return snapshotImage;
}

#pragma mark -- lazy
- (UISlider *)slider {
    if (!_slider) {
        _slider = [[UISlider alloc] initWithFrame:(CGRect){60,0,self.width-(20+44)*2,self.height}];
        UIImage *stetchLeftTrack = [UIImage resizableImageWithColor:XY_HEX_RGB(0xd9d9d9) cornerRadius:4];
        UIImage *stetchRightTrack = [UIImage resizableImageWithColor:XY_HEX_RGB(0xf5f5f5) cornerRadius:4];
        [_slider setMinimumTrackImage:stetchLeftTrack forState:UIControlStateNormal];
        [_slider setMaximumTrackImage:stetchRightTrack forState:UIControlStateNormal];
        [_slider setThumbImage:[self thumbViewWithValue:@"小五"] forState:UIControlStateNormal];
        [_slider addTarget:self action:@selector(sliderValueChanged:) forControlEvents:UIControlEventValueChanged];
        // 此处为记录修改前后的值、为撤销恢复
        [_slider addTarget:self action:@selector(zoomDragBegin:withEvent: )forControlEvents: UIControlEventTouchDown];
        [_slider addTarget:self action:@selector(zoomDragEnded:withEvent: )forControlEvents: UIControlEventTouchUpInside];
    }
    return _slider;
}

- (UILabel *)resultLabel {
    if (!_resultLabel) {
        _resultLabel = [[UILabel alloc] initWithFrame:(CGRect){0,0,100,18}];
        _resultLabel.font = [UIFont systemFontOfSize:12];
        _resultLabel.backgroundColor = [UIColor redColor];
        _resultLabel.textColor = XY_HEX_RGB(0x5c88c1);
        _resultLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _resultLabel;
}

- (UIButton *)resultButton {
    if (!_resultButton) {
        _resultButton = [UIButton buttonWithType:UIButtonTypeCustom];
        _resultButton.size = (CGSize){40,40};
        _resultButton.backgroundColor = XY_HEX_RGB(0xF7F7F7);
        _resultButton.layer.cornerRadius = 10;
        _resultButton.titleLabel.font = MY_FONT_Medium(12);
        [_resultButton setTitleColor:XY_HEX_RGB(0x5C88C1) forState:UIControlStateNormal];
        [_resultButton setBackgroundImage:[UIImage imageWithColor:XY_HEX_RGBA(0x000000, 0.3)] forState:UIControlStateHighlighted];
        [_resultButton addTarget:self action:@selector(inputValue:) forControlEvents:UIControlEventTouchUpInside];
        _resultButton.layer.masksToBounds = YES;
    }
    return _resultButton;
}

- (UIButton *)decreaseBtn {
    if (!_decreaseBtn) {
        _decreaseBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        _decreaseBtn.frame = (CGRect){0,0,44,44};
        _decreaseBtn.backgroundColor = XY_HEX_RGB(0xf5f5f5);
        _decreaseBtn.layer.cornerRadius = 10;
        [_decreaseBtn setImage:[UIImage imageNamed:@"decrease_action_normal"] forState:UIControlStateNormal];
        [_decreaseBtn setImage:[UIImage imageNamed:@"decrease_action_unable"] forState:UIControlStateDisabled];
        [_decreaseBtn addTarget:self action:@selector(decrease:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _decreaseBtn;
}

- (UIButton *)increaseBtn {
    if (!_increaseBtn) {
        _increaseBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        _increaseBtn.frame = (CGRect){0,0,44,44};
        _increaseBtn.backgroundColor = XY_HEX_RGB(0xf5f5f5);
        _increaseBtn.layer.cornerRadius = 10;
        [_increaseBtn setImage:[UIImage imageNamed:@"increase_action_normal"] forState:UIControlStateNormal];
        [_increaseBtn setImage:[UIImage imageNamed:@"increase_action_unable"] forState:UIControlStateDisabled];
        [_increaseBtn addTarget:self action:@selector(increase:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _increaseBtn;
}

@end
