

//
//  JCElementDottedView.m
//  XYFrameWork
//
//  Created by xingling xu on 2019/11/29.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCElementDottedView.h"

/** 默认长度 */
static const CGFloat defaultLength = 10000.f;
/** 默认粗细 */
static const CGFloat defaultThickness = 1.f;

@interface JCElementDottedView ()
@property (nonatomic, strong) UIView *topLine;
@property (nonatomic, strong) UIView *leftLine;
@property (nonatomic, strong) UIView *bottomLine;
@property (nonatomic, strong) UIView *rightLine;
@property (nonatomic, assign) CGFloat top;
@property (nonatomic, assign) CGFloat left;
@property (nonatomic, assign) CGFloat bottom;
@property (nonatomic, assign) CGFloat right;
@end

@implementation JCElementDottedView

- (instancetype)init {
    if (self = [super init]) {
        self.backgroundColor = [UIColor clearColor];
        [self initUI];
    }
    return self;
}

- (void)initUI {
    [self addSubview:self.topLine];
    [self addSubview:self.leftLine];
    [self addSubview:self.bottomLine];
    [self addSubview:self.rightLine];
}

- (void)show {
    self.hidden = NO;
}

- (void)hidden {
    self.hidden = YES;
}

- (void)setDottedLineConstraintWith:(NSArray *)viewArray fromView:(UIView *)fromView toView:(UIView *)toView {
    if (viewArray.count == 0) return;
    __block CGFloat left = 10000,top = 10000,bottom = -10000,right = -10000;
    [viewArray enumerateObjectsUsingBlock:^(UIView  *obj, NSUInteger idx, BOOL * _Nonnull stop) {
        left = MIN(obj.left, left);
        top = MIN(obj.top, top);
        bottom = MAX(obj.bottom, bottom);
        right = MAX(obj.right, right);
    }];
    CGPoint leftTop = [fromView convertPoint:(CGPoint){left,top} toView:toView];
    CGPoint rightDown = [fromView convertPoint:(CGPoint){right,bottom} toView:toView];
    self.topLine.frame = (CGRect){0,leftTop.y,defaultLength,defaultThickness};
    self.bottomLine.frame = (CGRect){0,rightDown.y,defaultLength,defaultThickness};
    self.leftLine.frame = (CGRect){leftTop.x,0,defaultThickness,defaultLength};
    self.rightLine.frame = (CGRect){rightDown.x,0,defaultThickness,defaultLength};
}

#pragma mark -- getter
- (UIView *)topLine {
    if (!_topLine) {
        _topLine = [[UIView alloc] initWithFrame:(CGRect){0,0,defaultLength,defaultThickness}];
        [self addBorderToLayer:_topLine];
    }
    return _topLine;
}

- (UIView *)leftLine {
    if (!_leftLine) {
        _leftLine = [[UIView alloc] initWithFrame:(CGRect){0,0,defaultThickness,defaultLength}];
        [self addBorderToLayer:_leftLine];
    }
    return _leftLine;
}

- (UIView *)bottomLine {
    if (!_bottomLine) {
        _bottomLine = [[UIView alloc] initWithFrame:(CGRect){0,0,defaultLength,defaultThickness}];
        [self addBorderToLayer:_bottomLine];
    }
    return _bottomLine;
}

- (UIView *)rightLine {
    if (!_rightLine) {
        _rightLine = [[UIView alloc] initWithFrame:(CGRect){0,0,defaultThickness,defaultLength}];
        [self addBorderToLayer:_rightLine];
    }
    return _rightLine;
}


#pragma mark -- draw dotted line 画虚线
- (void)addBorderToLayer:(UIView *)aView {
    aView.layer.masksToBounds = YES;
    CAShapeLayer *border = [CAShapeLayer layer];
    border.strokeColor = XY_HEX_RGB(0x00a7ff).CGColor;
    border.fillColor = nil;
    UIBezierPath *pat = [UIBezierPath bezierPath];
    [pat moveToPoint:CGPointMake(0, 0)];
    if (CGRectGetWidth(aView.frame) > CGRectGetHeight(aView.frame)) {
        [pat addLineToPoint:CGPointMake(aView.bounds.size.width, 0)];
    }else{
        [pat addLineToPoint:CGPointMake(0, aView.bounds.size.height)];
    }
    border.path = pat.CGPath;
    border.frame = aView.bounds;
    border.lineWidth = 1;
    border.lineCap = @"butt";
    border.lineDashPattern = @[@6, @3];
    [aView.layer addSublayer:border];
}
@end
