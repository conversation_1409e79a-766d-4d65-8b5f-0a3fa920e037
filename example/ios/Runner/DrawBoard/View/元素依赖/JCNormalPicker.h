//
//  JCNormalPicker.h
//  XYFrameWork
//
//  Created by xingling xu on 2019/12/24.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface JCNormalPicker : UIView
@property(nonatomic,assign)NSInteger defaultSelectedIndex;
/** 单列展示的Picker */
+ (instancetype)showPickerWith:(NSArray *)dataSource selectValue:(NSString *)selectValue complete:(XYBlock)completeBlock;

/// 初始化picker,不作为窗口弹出, 当做subview,  没有top bar
+ (instancetype)pickerWithFrame:(CGRect)frame dataSource:(NSArray *)dataSource selectValue:(NSString *)selectValue complete:(XYBlock)completeBlock;

- (void)selectAtIndex:(NSInteger)index;

@end

NS_ASSUME_NONNULL_END
