//
//  JCFontSliderView.h
//  XYFrameWork
//
//  Created by xingling xu on 2020/10/27.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import <UIKit/UIKit.h>

@class JCFontSliderView;
@protocol JCFontSliderViewDelegate <NSObject>
/** 滑动的过程中获取当前选择的值：该值来自于values */
- (void)sliderView:(JCFontSliderView *)sliderView selectIndex:(NSInteger)index  selectValue:(NSString *)value;
/** 获取滑动之前的value */
- (void)sliderView:(JCFontSliderView *)sliderView selectIndex:(NSInteger)index  valueChangeBegin:(NSString *)beginValue;
/** 获取滑动操作完毕后的value */
- (void)sliderView:(JCFontSliderView *)sliderView selectIndex:(NSInteger)index  valueChangeEnd:(NSString *)endValue;

- (void)sliderView:(JCFontSliderView *)sliderView maxValue:(NSString *)maxValue;

- (void)sliderView:(JCFontSliderView *)sliderView inputWithCurrentValue:(NSString *)value;
@end

NS_ASSUME_NONNULL_BEGIN

@interface JCFontSliderView : UIView
/** 是否每个位置显示一个点 */
@property (nonatomic, assign) BOOL showDot;
/** 右侧结果文本颜色 */
@property (nonatomic, strong) UIColor *resultColor;
/** 重置内容 */
@property (nonatomic, copy) NSArray *values;
/** 当前可滑动的最大值，超过此值则显示当前设置的最大值 */
@property (nonatomic, assign) NSInteger currentMaxIndex;

@property (nonatomic, weak) id delegate;

@property (nonatomic, assign) BOOL enabled;

- (instancetype)initWithFrame:(CGRect)frame values:(NSArray *)values;
// 表格可输入修改
- (instancetype)initWithFrame:(CGRect)frame values:(NSArray *)values canInput:(BOOL)canInput;
/** 设置 */
- (void)selectValue:(NSString *)selectValue animated:(BOOL)animated;

@end

NS_ASSUME_NONNULL_END
