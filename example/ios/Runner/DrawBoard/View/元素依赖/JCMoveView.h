//
//  JCMoveView.h
//  XYFrameWork
//
//  Created by xingling xu on 2019/12/12.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN
typedef enum : NSUInteger {
    MoveTop = 1,
    MoveLeft,
    MoveBottom,
    MoveRight
} MoveDerection;

@class JCMoveView;
@protocol JCMoveViewDelegate <NSObject>
- (void)moveView:(JCMoveView *)moveView derection:(MoveDerection)derection;
@end

@interface JCMoveView : UIView
@property (nonatomic, weak) id delegate;
@end

NS_ASSUME_NONNULL_END
