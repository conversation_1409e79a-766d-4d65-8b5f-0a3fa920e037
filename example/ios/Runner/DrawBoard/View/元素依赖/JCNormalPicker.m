//
//  JCNormalPicker.m
//  XYFrameWork
//
//  Created by xingling xu on 2019/12/24.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#define FORMAT_PICKER_HEIGHT 200
#import "JCNormalPicker.h"

typedef NS_ENUM(NSUInteger, JCFormatPickerType) {
    JCFormatPickerTypeModal,
    JCFormatPickerTypeNormal,
};

@interface JCNormalPicker ()<UIPickerViewDelegate,UIPickerViewDataSource>
@property (nonatomic, strong) UIPickerView *pickerView;
@property (nonatomic, strong) UIView *bar;
@property (nonatomic, strong) UIView *mainView;
@property (nonatomic, strong) NSDate *currentDate;
@property (nonatomic, copy) XYBlock doneBlock;
@property (nonatomic, strong) NSArray *dataSource;
@property (nonatomic, assign) NSInteger selectIndex;


@end


NSInteger previousIndex;
JCFormatPickerType type;
@implementation JCNormalPicker


+ (instancetype)pickerWithFrame:(CGRect)frame dataSource:(NSArray *)dataSource selectValue:(NSString *)selectValue complete:(XYBlock)completeBlock {
    type = JCFormatPickerTypeNormal;
    JCNormalPicker *picker = [[JCNormalPicker alloc] initWithFrame:frame];
    picker.doneBlock = completeBlock;
    picker.dataSource = dataSource;
    NSInteger row = [dataSource indexOfObject:selectValue];
    if (row == NSNotFound) {row = 0;}
    [picker.pickerView selectRow:row inComponent:0 animated:NO];
    picker.selectIndex = row;
    previousIndex = row;
    return picker;
}

+ (instancetype)showPickerWith:(NSArray *)dataSource selectValue:(NSString *)selectValue complete:(XYBlock)completeBlock {
    type = JCFormatPickerTypeModal;
    JCNormalPicker *picker = [[JCNormalPicker alloc] initWithFrame:(CGRect){0,0,SCREEN_WIDTH,SCREEN_HEIGHT}];
    picker.doneBlock = completeBlock;
    picker.dataSource = dataSource;
    NSInteger row = [dataSource indexOfObject:selectValue];
    if (row == NSNotFound) {row = 0;}
    [picker.pickerView selectRow:row inComponent:0 animated:NO];
    picker.selectIndex = row;
    [picker show];
    previousIndex = row;
    return picker;
}

- (void)show {
    UIWindow *keyWindow = [[UIApplication sharedApplication].delegate window];
    [keyWindow addSubview:self];
    self.mainView.bottom = SCREEN_HEIGHT+self.mainView.height;
    [UIView animateWithDuration:0.3 animations:^{
        self.mainView.bottom = SCREEN_HEIGHT;
    }];
}

- (void)dismiss {
    [UIView animateWithDuration:0.3 animations:^{
        self.mainView.top = SCREEN_HEIGHT;
    } completion:^(BOOL finished) {
        [self removeFromSuperview];
    }];
}

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        self.backgroundColor = XY_HEX_RGBA(0x666666, 0.5);
        [self setUp];
    }
    return self;
}


- (void)setUp {
    if (type == JCFormatPickerTypeModal) {
        [self addSubview:self.mainView];
        [self.mainView addSubview:self.bar];
        [self.mainView addSubview:self.pickerView];
        [self jk_addTapActionWithBlock:^(UIGestureRecognizer *gestureRecoginzer) {
            CGPoint point = [gestureRecoginzer locationInView:self];
            if (point.y < SCREEN_HEIGHT-self.mainView.height) {
                [self dismiss];
            }
        }];
    }else{
        [self addSubview:self.pickerView];
    }
}

- (void)layoutSubviews {
    [super layoutSubviews];
    if (type == JCFormatPickerTypeModal) {
        self.bar.left = 0;
        self.bar.top = 0;
        self.pickerView.top = 49;
        self.pickerView.left = 0;
    }else{
        self.pickerView.top = 0;
        self.pickerView.left = 0;
    }
}

- (void)setDefaultSelectedIndex:(NSInteger)defaultSelectedIndex {
    _defaultSelectedIndex = defaultSelectedIndex;
    [_pickerView selectRow:defaultSelectedIndex inComponent:0 animated:true];
}

- (void)selectAtIndex:(NSInteger)index {
    [_pickerView selectRow:index inComponent:0 animated:true];
}
#pragma mark -- UIPickerViewDelegate
- (CGFloat)pickerView:(UIPickerView *)pickerView rowHeightForComponent:(NSInteger)component {
    return 40;
}

- (UIView *)pickerView:(UIPickerView *)pickerView viewForRow:(NSInteger)row forComponent:(NSInteger)component reusingView:(nullable UIView *)view {
    UILabel *customLabel = (UILabel *)view;
    if (!customLabel) {
        customLabel = [[UILabel alloc] init];
        customLabel.textAlignment = NSTextAlignmentCenter;
    }
    customLabel.font = MY_FONT_Medium(16);
    customLabel.textColor = XY_HEX_RGB(0x666666);
    customLabel.text = UN_NIL(self.dataSource[row]);
    return customLabel;
}

- (void)pickerView:(UIPickerView *)pickerView didSelectRow:(NSInteger)row inComponent:(NSInteger)component
{
    UILabel *label = (UILabel *)[pickerView viewForRow:row forComponent:component];
    label.font = MY_FONT_Medium(20);
    label.textColor = XY_HEX_RGB(0x5C88C1);
    self.selectIndex = row;
    self.doneBlock(@(self.selectIndex));
}

#pragma mark -- UIPickerViewDataSource
- (NSInteger)numberOfComponentsInPickerView:(UIPickerView *)pickerView {
    return 1;
}

// returns the # of rows in each component..
- (NSInteger)pickerView:(UIPickerView *)pickerView numberOfRowsInComponent:(NSInteger)component {
    return self.dataSource.count;
}

#pragma mark -- btn selector
- (void)cancel:(UIButton *)btn {
    self.doneBlock(@(previousIndex));
    [self dismiss];
}

- (void)sure:(UIButton *)btn {
    if (self.doneBlock) {
        self.doneBlock(@(self.selectIndex));
        [self dismiss];
    }
}

#pragma mark -- lazy
-(UIView *)mainView {
    if (!_mainView) {
        _mainView = [[UIView alloc] initWithFrame:(CGRect){0,0,SCREEN_WIDTH,249}];
    }
    return _mainView;
}
- (UIPickerView *)pickerView {
    if (!_pickerView) {
        if (type == JCFormatPickerTypeModal) {
            _pickerView = [[UIPickerView alloc] initWithFrame:(CGRect){0,0,self.bounds.size.width,self.bounds.size.height}];
        }else{
        _pickerView = [[UIPickerView alloc] initWithFrame:self.frame];
        }
        _pickerView.backgroundColor = [UIColor whiteColor];
        _pickerView.showsSelectionIndicator = NO;
        _pickerView.delegate = self;
        _pickerView.dataSource = self;
    }
    return _pickerView;
}

- (UIView *)bar {
    if (!_bar) {
        _bar = [[UIView alloc] initWithFrame:(CGRect){0,0,SCREEN_WIDTH,49}];
        _bar.backgroundColor = [UIColor whiteColor];
        UIView *line = [[UIView alloc] initWithFrame:(CGRect){0,0,SCREEN_WIDTH,1}];
        line.backgroundColor = XY_HEX_RGB(0xcccccc);
        [_bar addSubview:line];
        line = [[UIView alloc] initWithFrame:(CGRect){0,48,SCREEN_WIDTH,1}];
        line.backgroundColor = XY_HEX_RGB(0xcccccc);
        [_bar addSubview:line];
        UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
        [btn setTitle:XY_LANGUAGE_TITLE_NAMED(@"app00030", @"取消") forState:UIControlStateNormal];
        [btn setTitleColor:XY_HEX_RGB(0x5C88C1) forState:UIControlStateNormal];
        [btn.titleLabel setFont:MY_FONT_Medium(14)];
        [btn addTarget:self action:@selector(cancel:) forControlEvents:UIControlEventTouchUpInside];
        btn.frame = (CGRect){16,0,50,49};
        [_bar addSubview:btn];
        btn = [UIButton buttonWithType:UIButtonTypeCustom];
        [btn setTitle:XY_LANGUAGE_TITLE_NAMED(@"app00048",@"确定") forState:UIControlStateNormal];
        [btn setTitleColor:XY_HEX_RGB(0x5C88C1) forState:UIControlStateNormal];
        [btn.titleLabel setFont:MY_FONT_Medium(14)];
        [btn addTarget:self action:@selector(sure:) forControlEvents:UIControlEventTouchUpInside];
        btn.frame = (CGRect){SCREEN_WIDTH-50,0,50,49};
        [_bar addSubview:btn];
    }
    return _bar;
}
@end
