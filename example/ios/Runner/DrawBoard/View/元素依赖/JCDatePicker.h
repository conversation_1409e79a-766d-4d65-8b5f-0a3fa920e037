//
//  JCDatePicker.h
//  XYFrameWork
//
//  Created by xingling xu on 2019/12/24.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN
typedef NS_ENUM(NSInteger, JCDatePickerMode) {
    JCDatePickerModeYearMonthDayHourMinute  = 0,   // 年月日时分
    JCDatePickerModeMonthDayHourMinute,            // 月日时分
    JCDatePickerModeYearMonthDay,                  // 年月日
    JCDatePickerModeYearMonth,                     // 年月
    JCDatePickerModeMonthDay,                      // 月日
    JCDatePickerModeHourMinute,                    // 时分
    JCDatePickerModeHourMinuteSeconds              // 时分秒
};

@interface JCDatePicker : UIView
@property (nonatomic, strong) NSDate *minimumDate; // 限制最大时间（default is nil）
@property (nonatomic, strong) NSDate *maximumDate; // 限制最小时间（default is nil）
@property (nonatomic, strong) NSDate *date;
@property (nonatomic, assign) JCDatePickerMode mode;

///模态弹出
+ (instancetype)showDate:(NSDate *)date mode:(JCDatePickerMode)mode complete:(XYBlock)completeBlock;
///当做subview,  没有top bar
+ (instancetype)datePickerWithFrame:(CGRect)frame date:(NSDate *)date mode:(JCDatePickerMode)mode complete:(XYBlock)completeBlock;
@end

NS_ASSUME_NONNULL_END
