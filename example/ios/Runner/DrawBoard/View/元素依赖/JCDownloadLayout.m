//
//  JCDownloadLayout.m
//  XYFrameWork
//
//  Created by APP on 2020/1/9.
//  Copyright © 2020 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCDownloadLayout.h"

static const CGFloat colunmnHeight = 40;

static const CGFloat columnMargin = 16;

static const CGFloat downImageWidht = 18;


@interface JCDownloadLayout ()

/**存放所有的布局属性*/
@property(nonatomic,strong) NSMutableArray *attrsArr;


@end

@implementation JCDownloadLayout

- (void)prepareLayout
{
    [super  prepareLayout];
    
    [self.attrsArr removeAllObjects];
    
    for (int i = 0; i < self.data.count; i++) {
        NSIndexPath *indexPath = [NSIndexPath indexPathForItem:i inSection:0];
        
        //获取indexPath位置上cell对应的布局属性
        UICollectionViewLayoutAttributes *attrs = [self layoutAttributesForItemAtIndexPath:indexPath];
        
        [self.attrsArr addObject:attrs];
        
      
    }
   
}

- (CGSize)collectionViewContentSize
{
  
    CGFloat width = columnMargin;
    for (JCFontModel *model in self.data) {
        width = width + [model.width floatValue]+ columnMargin;

    }
    return CGSizeMake(width, colunmnHeight);
    
}

-(UICollectionViewLayoutAttributes *)layoutAttributesForItemAtIndexPath:(NSIndexPath *)indexPath
{
    UICollectionViewLayoutAttributes *attibute = [UICollectionViewLayoutAttributes layoutAttributesForCellWithIndexPath:indexPath];
    JCFontModel *model = self.data[indexPath.row];
 
    attibute.size = CGSizeMake([model.width floatValue], colunmnHeight);
    
    CGFloat x = columnMargin;
    for (int i = 0; i <indexPath.row; i++) {
        JCFontModel *tempMode = self.data[i];
        x = x + ([tempMode.width floatValue] + columnMargin);
    }
    CGFloat y = (self.collectionView.bounds.size.height - colunmnHeight)/2;
    attibute.frame = CGRectMake(x, y, attibute.size.width, colunmnHeight);
    
    return attibute;
}

-(NSArray<UICollectionViewLayoutAttributes *> *)layoutAttributesForElementsInRect:(CGRect)rect
{
    return self.attrsArr;



}


- (void)setData:(NSMutableArray *)data
{
    _data = data;

    for (JCFontModel *model in data) {
        CGSize size = [model.name boundingRectWithSize:CGSizeMake(MAXFLOAT, colunmnHeight) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:MY_FONT_Medium(12)} context:nil].size;
        CGFloat width =size.width + 50 ;
        if (!model.isDownload) {
            width = width + downImageWidht;
        }
        model.width = [NSString stringWithFormat:@"%f",width];
    }
    [self invalidateLayout];
}

-(NSMutableArray *)attrsArr
{
    if (!_attrsArr) {
        _attrsArr = [NSMutableArray array];
    }
    return _attrsArr;
}


@end
