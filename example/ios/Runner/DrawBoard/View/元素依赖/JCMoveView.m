//
//  JCMoveView.m
//  XYFrameWork
//
//  Created by xingling xu on 2019/12/12.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCMoveView.h"

@interface JCMoveView ()
@property (nonatomic, strong) UIButton *topButton;
@property (nonatomic, strong) UIButton *leftButton;
@property (nonatomic, strong) UIButton *bottomButton;
@property (nonatomic, strong) UIButton *rightButton;
@property (nonatomic, strong) NSTimer *timer;
@property (nonatomic, strong) UIView *selectBtn;
@end

@implementation JCMoveView
- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self addSubview:self.topButton];
        [self addSubview:self.leftButton];
        [self addSubview:self.bottomButton];
        [self addSubview:self.rightButton];
    }
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];
    self.topButton.top = 0;
    self.topButton.centerX = self.width/2;
    self.leftButton.left = 0;
    self.leftButton.centerY = self.height/2;
    self.bottomButton.centerX = self.width/2;
    self.bottomButton.bottom = self.height;
    self.rightButton.right = self.width;
    self.rightButton.centerY = self.height/2;
}

#pragma mark -- btn selector
- (void)longPress:(UILongPressGestureRecognizer *)recongizer {
    self.selectBtn = recongizer.view;
    UIGestureRecognizerState state = recongizer.state;
    if (state == UIGestureRecognizerStateBegan) {
        [self.timer setFireDate:[NSDate date]];
    } else if (state == UIGestureRecognizerStateEnded) {
        [self.timer setFireDate:[NSDate distantFuture]];
    }
}

- (void)tapPress:(UITapGestureRecognizer *)recongizer {
    self.selectBtn = recongizer.view;
    [self cotinueMove];
}

- (void)cotinueMove {
    NSInteger tag = self.selectBtn.tag;
    if (self.delegate && [self.delegate respondsToSelector:@selector(moveView:derection:)]) {
        [self.delegate moveView:self derection:tag];
    }
}


#pragma mark -- lazy
- (UIButton *)topButton {
    if (!_topButton) {
        _topButton = [self buttonWithImageName:@"move_top_normal" highlightedName:@"move_top_select"];
        _topButton.tag = MoveTop;
    }
    return _topButton;
}

- (UIButton *)bottomButton {
    if (!_bottomButton) {
        _bottomButton = [self buttonWithImageName:@"move_bottom_normal" highlightedName:@"move_bottom_select"];
        _bottomButton.tag = MoveBottom;
    }
    return _bottomButton;
}

- (UIButton *)leftButton {
    if (!_leftButton) {
        _leftButton = [self buttonWithImageName:@"move_left_normal" highlightedName:@"move_left_select"];
        _leftButton.tag = MoveLeft;
    }
    return _leftButton;
}

- (UIButton *)rightButton {
    if (!_rightButton) {
        _rightButton = [self buttonWithImageName:@"move_right_normal" highlightedName:@"move_right_select"];
        _rightButton.tag = MoveRight;
    }
    return _rightButton;
}

- (NSTimer *)timer {
    if (!_timer) {
        _timer = [NSTimer timerWithTimeInterval:0.1 target:self selector:@selector(cotinueMove) userInfo:nil repeats:YES];
        [[NSRunLoop currentRunLoop] addTimer:_timer forMode:NSRunLoopCommonModes];
    }
    return _timer;
}

- (UIButton *)buttonWithImageName:(NSString *)normalName highlightedName:(NSString *)highlightedName {
    UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
    button.size = (CGSize){40,40};
    [button setImage:[UIImage imageNamed:normalName] forState:UIControlStateNormal];
    [button setImage:[UIImage imageNamed:highlightedName] forState:UIControlStateHighlighted];
    button.backgroundColor = XY_HEX_RGB(0xf7f7f7);
    button.layer.cornerRadius = 10;
    button.layer.masksToBounds = YES;
    // 长按事件:一直移动
    UILongPressGestureRecognizer *longPress = [[UILongPressGestureRecognizer alloc] initWithTarget:self action:@selector(longPress:)];
    [button addGestureRecognizer:longPress];
    // 单击事件：移动一次
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(tapPress:)];
    [button addGestureRecognizer:tap];
    return button;
}

- (void)willMoveToSuperview:(UIView *)newSuperview {
    [self.timer invalidate];
    self.timer = nil;
}

@end
