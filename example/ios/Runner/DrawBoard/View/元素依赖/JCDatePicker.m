//
//  JCDatePicker.m
//  XYFrameWork
//
//  Created by xingling xu on 2019/12/24.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCDatePicker.h"
#import "NSDate+XHExtension.h"

typedef NS_ENUM(NSInteger, JCDateType) {
    JCDateTypeYear,
    JCDateTypeMonth,
    JCDateTypeDay,
    JCDateTypeHour,
    JCDateTypeMinute,
    JCDateTypeSecond,
};

typedef NS_ENUM(NSUInteger, JCDatePickerType) {
    JCDatePickerTypeModal,
    JCDatePickerTypeNormal,
};


@interface JCDatePicker () <UIPickerViewDelegate,UIPickerViewDataSource> {
    NSArray *_rowsDataArray;
    NSArray *_dateTypeArray;
    NSArray *_textDataArray;
    BOOL _isRepeatMonth;
}
@property (nonatomic, strong) UIPickerView *pickerView;
@property (nonatomic, strong) UIView *bar;
@property (nonatomic, strong) UIView *mainView;
@property (nonatomic, strong) NSDate *currentDate;
//@property (nonatomic, strong) NSDate *previousDate;
@property (nonatomic, copy) XYBlock doneBlock;
@end

NSDate *previousDate;
JCDatePickerType dateType;
JCDatePickerMode pickerMode;
@implementation JCDatePicker



+ (instancetype)datePickerWithFrame:(CGRect)frame date:(NSDate *)date mode:(JCDatePickerMode)mode complete:(XYBlock)completeBlock {
    dateType = JCDatePickerTypeNormal;
    pickerMode = mode;
    JCDatePicker *picker = [[JCDatePicker alloc] initWithFrame:frame];
    picker.minimumDate = [NSDate date:@"1770-01-01 12:12" WithFormat:@"yyyy-MM-dd HH:mm"];
    picker.maximumDate = [NSDate date:@"2099-12-12 12:12" WithFormat:@"yyyy-MM-dd HH:mm"];
    picker.mode = mode;
    picker.date = date;
    picker.doneBlock = completeBlock;
    previousDate = date;
    return picker;
}

+ (instancetype)showDate:(NSDate *)date mode:(JCDatePickerMode)mode complete:(XYBlock)completeBlock {
    dateType = JCDatePickerTypeModal;
    pickerMode = mode;
    JCDatePicker *picker = [[JCDatePicker alloc] initWithFrame:(CGRect){0,0,SCREEN_WIDTH,SCREEN_HEIGHT}];
    picker.minimumDate = [NSDate date:@"1770-01-01 12:12" WithFormat:@"yyyy-MM-dd HH:mm"];
    picker.maximumDate = [NSDate date:@"2099-12-12 12:12" WithFormat:@"yyyy-MM-dd HH:mm"];
    picker.mode = mode;
    picker.date = date;
    picker.doneBlock = completeBlock;
    [picker show];
    previousDate = date;
    return picker;
}

- (void)show {
    UIWindow *keyWindow = [[UIApplication sharedApplication].delegate window];
    [keyWindow addSubview:self];
    self.mainView.bottom = SCREEN_HEIGHT+self.mainView.height;
    [UIView animateWithDuration:0.3 animations:^{
        self.mainView.bottom = SCREEN_HEIGHT;
    }];
}

- (void)dismiss {
    [UIView animateWithDuration:0.3 animations:^{
        self.mainView.top = SCREEN_HEIGHT;
    } completion:^(BOOL finished) {
         [self removeFromSuperview];
    }];
}

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        self.backgroundColor = XY_HEX_RGBA(0x666666, 0.5);
        [self setUp];
    }
    return self;
}


- (void)setUp {
    if (dateType == JCDatePickerTypeModal) {
    [self addSubview:self.mainView];
    [self.mainView addSubview:self.bar];
    [self.mainView addSubview:self.pickerView];
    [self jk_addTapActionWithBlock:^(UIGestureRecognizer *gestureRecoginzer) {
        CGPoint point = [gestureRecoginzer locationInView:self];
        if (point.y < SCREEN_HEIGHT-self.mainView.height) {
            [self dismiss];
        }
    }];
    }else{
        [self addSubview:self.pickerView];
    }
}

- (void)layoutSubviews {
    [super layoutSubviews];
    self.bar.left = 0;
    self.bar.top = 0;
    self.pickerView.top = 49;
    self.pickerView.left = 0;
}

#pragma mark -- UIPickerViewDelegate
- (CGFloat)pickerView:(UIPickerView *)pickerView rowHeightForComponent:(NSInteger)component {
    return 40;
}

- (UIView *)pickerView:(UIPickerView *)pickerView viewForRow:(NSInteger)row forComponent:(NSInteger)component reusingView:(nullable UIView *)view {
    UILabel *customLabel = (UILabel *)view;
    if (!customLabel) {
        customLabel = [[UILabel alloc] init];
        customLabel.textAlignment = NSTextAlignmentCenter;
    }
    customLabel.font = MY_FONT_Medium(16);
    customLabel.textColor = XY_HEX_RGB(0x666666);
    customLabel.text = [self pickerView:pickerView titleForRow:row forComponent:component];
    return customLabel;
}

- (void)pickerView:(UIPickerView *)pickerView didSelectRow:(NSInteger)row inComponent:(NSInteger)component
{
    UILabel *label = (UILabel *)[pickerView viewForRow:row forComponent:component];
    label.font = MY_FONT_Medium(20);
    label.textColor = XY_HEX_RGB(0x5C88C1);
    NSInteger rowData = label.text.integerValue;
    
    // 月份是否循环滚动
    if ([_dateTypeArray[component] integerValue] == JCDateTypeMonth && _isRepeatMonth) {
        NSInteger year = row/12+1;
        self.currentDate = [_currentDate dateByAddingYears:year - _currentDate.year];
    }
    
    [self updateCurrentDateWithRowData:rowData dateType:[_dateTypeArray[component] integerValue]];
}
- (NSString *)pickerView:(UIPickerView *)pickerView titleForRow:(NSInteger)row forComponent:(NSInteger)component {
    JCDateType type = [_dateTypeArray[component] integerValue];
    NSString *title;
    switch (type) {
        case JCDateTypeYear:
        case JCDateTypeDay:
            title = [NSString stringWithFormat:@"%d",(int)row+1];
            break;
        case JCDateTypeMonth:
            title = [NSString stringWithFormat:@"%d",(int)(row)%12+1];
            break;
        case JCDateTypeHour:
        case JCDateTypeMinute:
        case JCDateTypeSecond:
            title = [NSString stringWithFormat:@"%.2d",(int)row];
            break;
    }
    return title;
}

#pragma mark -- UIPickerViewDataSource
- (NSInteger)numberOfComponentsInPickerView:(UIPickerView *)pickerView {
    return _textDataArray.count;
}

// returns the # of rows in each component..
- (NSInteger)pickerView:(UIPickerView *)pickerView numberOfRowsInComponent:(NSInteger)component {
    return [_rowsDataArray[component] integerValue];
}

#pragma mark -- setter
- (void)setMode:(JCDatePickerMode)mode {
    _mode = mode;
    switch (mode) {
        case JCDatePickerModeYearMonthDayHourMinute:
            _rowsDataArray = @[@(10000), @(12), @(31), @(24), @(60)];
            _dateTypeArray = @[@(JCDateTypeYear),@(JCDateTypeMonth),@(JCDateTypeDay),@(JCDateTypeHour),@(JCDateTypeMinute)];
            _textDataArray = @[@"年",@"月",@"日",@"时",@"分"];
            _isRepeatMonth = NO;
            break;
        case JCDatePickerModeMonthDayHourMinute:
            _rowsDataArray = @[@(12*10000), @(31), @(24), @(60)];
            _dateTypeArray = @[@(JCDateTypeMonth),@(JCDateTypeDay),@(JCDateTypeHour),@(JCDateTypeMinute)];
            _textDataArray = @[@"月",@"日",@"时",@"分"];
            _isRepeatMonth = YES;
            break;
        case JCDatePickerModeYearMonthDay:
            _rowsDataArray = @[@(10000), @(12), @(31)];
            _dateTypeArray = @[@(JCDateTypeYear),@(JCDateTypeMonth),@(JCDateTypeDay)];
            _textDataArray = @[@"年",@"月",@"日"];
            _isRepeatMonth = NO;
            break;
        case JCDatePickerModeYearMonth:
            _rowsDataArray = @[@(10000), @(12)];
            _dateTypeArray = @[@(JCDateTypeYear),@(JCDateTypeMonth)];
            _textDataArray = @[@"年",@"月"];
            _isRepeatMonth = NO;
            break;
        case JCDatePickerModeMonthDay:
            _rowsDataArray = @[@(12*10000), @(31)];
            _dateTypeArray = @[@(JCDateTypeMonth),@(JCDateTypeDay)];
            _textDataArray = @[@"月",@"日"];
            _isRepeatMonth = YES;
            break;
        case JCDatePickerModeHourMinute:
            _rowsDataArray = @[@(24), @(60)];
            _dateTypeArray = @[@(JCDateTypeHour),@(JCDateTypeMinute)];
            _textDataArray = @[@"时",@"分"];
            _isRepeatMonth = NO;
            break;
        case JCDatePickerModeHourMinuteSeconds:
            _rowsDataArray = @[@(24), @(60),@(60)];
            _dateTypeArray = @[@(JCDateTypeHour),@(JCDateTypeMinute),@(JCDateTypeSecond)];
            _textDataArray = @[@"时",@"分",@"秒"];
            _isRepeatMonth = NO;
            break;
    }
    [self.pickerView reloadAllComponents];
    [self scrollToCurrentDateWithAnimated:NO];
}

- (void)setDate:(NSDate *)date {
    _date = date;
    _currentDate = date;
    if ([self currentDateInRangeWithAnimated:NO]) {
        [self scrollToCurrentDateWithAnimated:NO];
    }
}

- (void)setMinimumDate:(NSDate *)minimumDate {
    _minimumDate = minimumDate;
    [self currentDateInRangeWithAnimated:NO];
}

- (void)setMaximumDate:(NSDate *)maximumDate {
    _maximumDate = maximumDate;
    [self currentDateInRangeWithAnimated:NO];
}

- (void)setCurrentDate:(NSDate *)currentDate {
    _currentDate = currentDate;
    [self currentDateInRangeWithAnimated:YES];
}

// 更新当前选择的时间
- (void)updateCurrentDateWithRowData:(NSInteger)rowData dateType:(JCDateType)dateType {
    NSInteger days = 0;
    BOOL isUpdateDays = NO;
    NSDate *tmpDate = [NSDate date];
    switch (dateType) {
        case JCDateTypeYear:
            days = [self getDaysfromYear:rowData andMonth:_currentDate.month];
            if (_currentDate.day>days) {
                isUpdateDays = YES;
            }
            tmpDate = [_currentDate dateByAddingYears:rowData - _currentDate.year];
            break;
        case JCDateTypeMonth:
            days = [self getDaysfromYear:_currentDate.year andMonth:rowData];
            if (_currentDate.day>days) {
                isUpdateDays = YES;
            }
            tmpDate = [_currentDate dateByAddingMonths:rowData - _currentDate.month];
            break;
        case JCDateTypeDay:
            days = [self getDaysfromYear:_currentDate.year andMonth:_currentDate.month];
            if (rowData>days) {
                rowData = days;
                isUpdateDays = YES;
            }
            tmpDate = [_currentDate dateByAddingDays:rowData - _currentDate.day];
            break;
        case JCDateTypeHour:
            tmpDate = [_currentDate dateByAddingHours:rowData - _currentDate.hour];
            break;
        case JCDateTypeMinute:
            tmpDate = [_currentDate dateByAddingMinutes:rowData - _currentDate.minute];
            break;
        case JCDateTypeSecond:
            tmpDate = [_currentDate dateByAddingSeconds:rowData - _currentDate.seconds];
            break;
    }
    
    if (isUpdateDays) {
        [self selectRow:days-1 component:[_dateTypeArray indexOfObject:@(JCDateTypeDay)] animated:YES];
    }
    
    self.currentDate = tmpDate;
    //实时显示时间
    self.doneBlock(self.currentDate);
}

// 通过年月求每月天数
- (NSInteger)getDaysfromYear:(NSInteger)year andMonth:(NSInteger)month {
    BOOL isrunNian = year%4==0 ? (year%100==0? (year%400==0?YES:NO):YES):NO;
    switch (month) {
        case 1:case 3:case 5:case 7:case 8:case 10:case 12:
            return 31;
        case 4:case 6:case 9:case 11:
            return 30;
        case 2:
            return isrunNian ? 29 : 28;
    }
    return 0;
}

// 判断当前时间是否在限定范围内  (YES:在限定范围内 NO:不在限定范围内)
- (BOOL)currentDateInRangeWithAnimated:(BOOL)animated {
    BOOL isScroll = NO;
    
    if (_minimumDate && [_currentDate compare:_minimumDate] == NSOrderedAscending) {
        _currentDate = _minimumDate;
        isScroll = YES;
    } else if (_maximumDate && [_currentDate compare:_maximumDate] == NSOrderedDescending) {
        _currentDate = _maximumDate;
        isScroll = YES;
    }
    if (isScroll) {
        [self scrollToCurrentDateWithAnimated:animated];
    }
    return !isScroll;
    
}

// 滚动到当前时间
- (void)scrollToCurrentDateWithAnimated:(BOOL)animated {
    if (!_currentDate) return;
    
    NSArray *indexArray;
    NSInteger yearIndex = _currentDate.year-1;
    NSInteger monthIndex = _currentDate.month-1;
    NSInteger dayIndex = _currentDate.day-1;
    NSInteger hourIndex = _currentDate.hour;
    NSInteger minuteIndex = _currentDate.minute;
    NSInteger secondIndex = _currentDate.seconds;
    
    switch (_mode) {
        case JCDatePickerModeYearMonthDayHourMinute:
            indexArray = @[@(yearIndex),@(monthIndex),@(dayIndex),@(hourIndex),@(minuteIndex)];
            break;
        case JCDatePickerModeMonthDayHourMinute:
            indexArray = @[@(monthIndex+(yearIndex*12)),@(dayIndex),@(hourIndex),@(minuteIndex)];
            break;
        case JCDatePickerModeYearMonthDay:
            indexArray = @[@(yearIndex),@(monthIndex),@(dayIndex)];
            break;
        case JCDatePickerModeYearMonth:
            indexArray = @[@(yearIndex),@(monthIndex)];
            break;
        case JCDatePickerModeMonthDay:
            indexArray = @[@(monthIndex+(yearIndex*12)),@(dayIndex)];
            break;
        case JCDatePickerModeHourMinute:
            indexArray = @[@(hourIndex),@(minuteIndex)];
            break;
        case JCDatePickerModeHourMinuteSeconds:
            indexArray = @[@(hourIndex),@(minuteIndex),@(secondIndex)];
            break;
    }
    
    for (NSInteger i=0; i<indexArray.count; i++) {
        [self selectRow:[indexArray[i] integerValue] component:i animated:animated];
    }
}

- (void)selectRow:(NSInteger)row component:(NSInteger)component animated:(BOOL)animated {
    [self.pickerView selectRow:row inComponent:component animated:animated];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        UILabel *label = (UILabel *)[self.pickerView viewForRow:row forComponent:component];
        label.font = MY_FONT_Medium(20);
        label.textColor = XY_HEX_RGB(0x5C88C1);
    });
}


#pragma mark -- btn selector
- (void)cancel:(UIButton *)btn {
    self.doneBlock(previousDate);
    previousDate = nil;
    [self dismiss];
}

- (void)sure:(UIButton *)btn {
    if (self.doneBlock) {
        self.doneBlock(self.currentDate);
        [self dismiss];
    }
}
#pragma mark -- lazy
-(UIView *)mainView {
    if (!_mainView) {
        _mainView = [[UIView alloc] initWithFrame:(CGRect){0,0,SCREEN_WIDTH,PropertyMainViewHeight}];
    }
    return _mainView;
}
- (UIPickerView *)pickerView {
    if (!_pickerView) {
        if (dateType == JCDatePickerTypeModal) {
            _pickerView = [[UIPickerView alloc] initWithFrame:(CGRect){0,0,SCREEN_WIDTH,PropertyMainViewHeight - 49}];
        }else{
            _pickerView = [[UIPickerView alloc] initWithFrame:self.frame];
        }
        _pickerView.backgroundColor = [UIColor whiteColor];
        _pickerView.showsSelectionIndicator = NO;
        _pickerView.delegate = self;
        _pickerView.dataSource = self;
    }
    return _pickerView;
}

- (UIView *)bar {
    UILabel *titleLbl;
    if (!_bar) {
        _bar = [[UIView alloc] initWithFrame:(CGRect){0,0,SCREEN_WIDTH,49}];
        _bar.backgroundColor = [UIColor whiteColor];
        UIView *line = [[UIView alloc] initWithFrame:(CGRect){0,0,SCREEN_WIDTH,1}];
        line.backgroundColor = XY_HEX_RGB(0xcccccc);
        [_bar addSubview:line];
        line = [[UIView alloc] initWithFrame:(CGRect){0,48,SCREEN_WIDTH,1}];
        line.backgroundColor = XY_HEX_RGB(0xcccccc);
        [_bar addSubview:line];
        UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
//        [btn setTitle:XY_LANGUAGE_TITLE_NAMED(@"app00030", @"取消") forState:UIControlStateNormal];
        [btn setImage:[UIImage imageNamed:@"date_picker_cancel"] forState:UIControlStateNormal];
        [btn setTitleColor:XY_HEX_RGB(0x5C88C1) forState:UIControlStateNormal];
        [btn.titleLabel setFont:MY_FONT_Medium(14)];
        [btn addTarget:self action:@selector(cancel:) forControlEvents:UIControlEventTouchUpInside];
        btn.frame = (CGRect){16,0,50,49};
        [_bar addSubview:btn];
        btn = [UIButton buttonWithType:UIButtonTypeCustom];
//        [btn setTitle:XY_LANGUAGE_TITLE_NAMED(@"app00048",@"确定") forState:UIControlStateNormal];
        [btn setImage:[UIImage imageNamed:@"date_picker_sure"] forState:UIControlStateNormal];
        [btn setTitleColor:XY_HEX_RGB(0x5C88C1) forState:UIControlStateNormal];
        [btn.titleLabel setFont:MY_FONT_Medium(14)];
        [btn addTarget:self action:@selector(sure:) forControlEvents:UIControlEventTouchUpInside];
        btn.frame = (CGRect){SCREEN_WIDTH-50,0,40,49};
        [_bar addSubview:btn];
        //
        titleLbl = [[UILabel alloc] initWithFrame:CGRectMake((SCREEN_WIDTH- 80) * 0.5, 0, 80, 49)];
        titleLbl.font = MY_FONT_Regular(14);
        titleLbl.textColor = XY_HEX_RGB(0x595959);
        titleLbl.textAlignment = NSTextAlignmentCenter;
        [_bar addSubview:titleLbl];
    }
    if (pickerMode == JCDatePickerModeYearMonthDay) {
        titleLbl.text = @"日期";
    }else{
        titleLbl.text = @"时间";
    }
    return _bar;
}

@end
