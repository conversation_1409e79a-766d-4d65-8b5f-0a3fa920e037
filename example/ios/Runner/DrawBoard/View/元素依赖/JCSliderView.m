//
//  JCSliderView.m
//  Runner
//
//  Created by xingling xu on 2021/1/14.
//

#import "JCSliderView.h"
#define TapTip_W       35.0
#define SLiderLine_H   4.0
//#if ShouldHideIncreaseAndDecreaseButton
//#define LeftSpace      20
//#else
//#define LeftSpace      64
//#endif

@interface JCSliderView ()
@property (nonatomic, strong) UIButton *leftButton;
@property (nonatomic, strong) UIButton *rightButton;
@property (nonatomic, strong) UIView *lineView;
@property (nonatomic, strong) UIView *leftTrackView;
@property (nonatomic, strong) UIView *rightTrackView;
@property (nonatomic, strong) UIImageView *thumbView;
@property (nonatomic, strong) UIView *resultView;
@property (nonatomic, strong) UILabel *resultLabel;
@property (nonatomic, copy) NSArray *values;
// 每一隔的间距
@property (nonatomic, assign) CGFloat everyWidth;
@property (nonatomic, assign) NSInteger oldSelectIndex;
@property (nonatomic, assign) NSInteger currentIndex;

@end

CGFloat LeftSpace;
bool ShouldHideIncreaseAndDecreaseButton = false;
@implementation JCSliderView

- (instancetype)initWithFrame:(CGRect)frame values:(NSArray *)values type:(JCSliderAccessory)type {
    self = [super initWithFrame:frame];
    if (self) {
        if (type == JCSliderAccessoryNone) {
            LeftSpace = 20;
            ShouldHideIncreaseAndDecreaseButton = true;
        }else if(type == JCSliderAccessoryButton){
            LeftSpace = 64;
            ShouldHideIncreaseAndDecreaseButton = false;
        }
        self.type = type;
        self.values = values;
        self.everyWidth = (self.width - LeftSpace*2)/(self.values.count - 1);
        [self setupUI];
    }
    return self;
}
- (instancetype)initWithFrame:(CGRect)frame values:(NSArray *)values {
    return [self initWithFrame:frame values:values type:JCSliderAccessoryNone];
}

-(void)setupUI{
    [self addSubview:self.leftButton];
    [self addSubview:self.rightButton];
    [self addSubview:self.lineView];
    [self addSubview:self.resultView];
    [self addSubview:self.thumbView];
    self.resultView.centerX = LeftSpace;
}

- (void)setType:(JCSliderAccessory)accessoryType {
    _type = accessoryType;
    if (_type == JCSliderAccessoryNone) {
        LeftSpace = 20;
        ShouldHideIncreaseAndDecreaseButton = true;
    }else if(_type == JCSliderAccessoryButton){
        LeftSpace = 64;
        ShouldHideIncreaseAndDecreaseButton = false;
    }
    [self reloadThumbImgView];
}
/** 设置 */
- (void)selectValue:(NSString *)selectValue animated:(BOOL)animated {
    NSInteger temp = [self.values indexOfObject:selectValue];
    self.currentIndex = temp;
    self.oldSelectIndex = temp;
    [self reloadThumbImgView];
}

- (void)reloadThumbImgView {
    CGFloat width = self.currentIndex*self.everyWidth + LeftSpace;
    self.thumbView.centerX = width;
    self.resultView.centerX = width;
    NSString *value = [self.values safeObjectAtIndex:self.currentIndex];
    self.resultLabel.text = value;
    // left and right position
    
    self.rightTrackView.left = width-LeftSpace;
    self.rightTrackView.width = self.lineView.width - width + LeftSpace;
}

- (void)setCurrentIndex:(NSInteger)currentIndex {
    _currentIndex = currentIndex;
    self.leftButton.enabled = !(_currentIndex == 0);
    self.rightButton.enabled = !(_currentIndex == self.values.count - 1);
}

#pragma mark - btn selector
- (void)decrease:(UIButton *)button {
    if (self.oldSelectIndex == 0) return;
    self.currentIndex = self.oldSelectIndex-1;
    [self refreshIndexInfo];
    self.oldSelectIndex -= 1;
    [self reloadThumbImgView];
}

- (void)increase:(UIButton *)button {
    if (self.oldSelectIndex == self.values.count - 1) return;
    self.currentIndex = self.oldSelectIndex+1;
    [self refreshIndexInfo];
    self.oldSelectIndex += 1;
    [self reloadThumbImgView];
}

- (void)refreshIndexInfo {
    // begin
    NSString *beginValue = [self.values safeObjectAtIndex:self.oldSelectIndex];
    if (self.delegate && [self.delegate respondsToSelector:@selector(sliderView:selectIndex:valueChangeBegin:)]) {
        [self.delegate sliderView:self selectIndex:self.oldSelectIndex  valueChangeBegin:beginValue];
    }
    //end
    NSString *endValue = [self.values safeObjectAtIndex:self.currentIndex];
    if (self.delegate && [self.delegate respondsToSelector:@selector(sliderView:selectIndex:valueChangeEnd:)]) {
        [self.delegate sliderView:self selectIndex:self.currentIndex valueChangeEnd:endValue];
    }
    // refresh element
    if (self.delegate && [self.delegate respondsToSelector:@selector(sliderView:selectIndex:selectValue:)]) {
        [self.delegate sliderView:self selectIndex:self.currentIndex selectValue:endValue];
    }
}

#pragma mark - gesture
- (void)PanEvent:(UIPanGestureRecognizer *)gesture{
    NSString *indexString;
    if (gesture.state == UIGestureRecognizerStateBegan) {
        if (self.delegate && [self.delegate respondsToSelector:@selector(sliderView:selectIndex:valueChangeBegin:)]) {
            [self.delegate sliderView:self selectIndex:self.oldSelectIndex  valueChangeBegin:self.resultLabel.text];
        }
    } else if (gesture.state == UIGestureRecognizerStateChanged) {
        CGPoint point = [gesture translationInView:self];
        CGFloat y = gesture.view.center.y;
        CGFloat x = gesture.view.center.x +point.x;
        if (x <self.lineView.frame.origin.x) {
            x = self.lineView.frame.origin.x;
        }
        if (x > self.lineView.width+LeftSpace) {
            x =  self.lineView.width+LeftSpace;
        }
        gesture.view.center = CGPointMake(x, y);
        [gesture setTranslation:CGPointMake(0, 0) inView:self];
        self.resultView.centerX=x;
        self.rightTrackView.left = x-LeftSpace;
        self.rightTrackView.width = self.lineView.width - x + LeftSpace;
        // value
        indexString = [NSString stringWithFormat:@"%.0f",(x-LeftSpace)/self.everyWidth];
        self.currentIndex = indexString.integerValue;
//        self.currentIndex = (x-LeftSpace)/self.everyWidth;
        NSString *value = [self.values safeObjectAtIndex:self.currentIndex];
//        NSLog(@"=======>%ld========%f=======%@",self.currentIndex, (x-LeftSpace)/self.everyWidth, value);
        self.resultLabel.text = value;
        if (self.delegate && [self.delegate respondsToSelector:@selector(sliderView:selectIndex:selectValue:)]) {
            [self.delegate sliderView:self selectIndex:self.currentIndex selectValue:value];
        }
    } else if(gesture.state == UIGestureRecognizerStateEnded){
        self.oldSelectIndex = self.currentIndex;
        if (self.delegate && [self.delegate respondsToSelector:@selector(sliderView:selectIndex:valueChangeEnd:)]) {
            [self.delegate sliderView:self selectIndex:self.currentIndex valueChangeEnd:self.resultLabel.text];
        }
    }
}

#pragma mark - lazy
- (UIView *)lineView{
    if (!_lineView) {
        _lineView = [[UIView alloc] initWithFrame:CGRectMake(LeftSpace, self.height/2, self.width-LeftSpace*2, SLiderLine_H)];
        _lineView.backgroundColor = [UIColor clearColor];
        [_lineView addSubview:self.leftTrackView];
        [_lineView addSubview:self.rightTrackView];
    }
    return _lineView;
}

- (UIImageView *)thumbView{
    if (!_thumbView) {
        _thumbView = [[UIImageView alloc] initWithFrame:CGRectMake(LeftSpace, 0, TapTip_W, TapTip_W)];
        _thumbView.userInteractionEnabled = YES;
        _thumbView.image = [UIImage imageNamed:@"font_circle"];
        _thumbView.center = CGPointMake(LeftSpace, self.height/2+SLiderLine_H);
//        _thumbView.layer.cornerRadius = TapTip_W/2;
//        _thumbView.layer.masksToBounds = YES;
//        _thumbView.backgroundColor = [UIColor whiteColor];
//        _thumbView.alpha = 1;
//        // 阴影颜色
//        _thumbView.layer.shadowColor = [UIColor blackColor].CGColor;
//            // 阴影偏移，默认(0, -3)
//        _thumbView.layer.shadowOffset = CGSizeMake(3,3);
//            // 阴影透明度，默认0
//        _thumbView.layer.shadowOpacity = 1;
//            // 阴影半径，默认3
//        _thumbView.layer.shadowRadius = 5;
        UIPanGestureRecognizer *pan = [[UIPanGestureRecognizer alloc] initWithTarget:self action:@selector(PanEvent:)];
        [_thumbView addGestureRecognizer:pan];
    }
    return _thumbView;
}

- (UIButton *)leftButton {
    if (!_leftButton) {
        _leftButton = [UIButton buttonWithType:UIButtonTypeCustom];
        CGFloat width = 44;
        if (ShouldHideIncreaseAndDecreaseButton){
        width = 0;
        }
        _leftButton.frame = (CGRect){0,0,width,44};
        _leftButton.backgroundColor = XY_HEX_RGB(0xf5f5f5);
        _leftButton.layer.cornerRadius = 10;
        [_leftButton setImage:[UIImage imageNamed:@"decrease_action_normal"] forState:UIControlStateNormal];
        [_leftButton setImage:[UIImage imageNamed:@"decrease_action_unable"] forState:UIControlStateDisabled];
        [_leftButton addTarget:self action:@selector(decrease:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _leftButton;
}

- (UIButton *)rightButton {
    if (!_rightButton) {
        _rightButton = [UIButton buttonWithType:UIButtonTypeCustom];
        CGFloat width = 44;
        if (ShouldHideIncreaseAndDecreaseButton){
        width = 0;
        }
        _rightButton.frame = (CGRect){self.width-44,0,width,44};
        _rightButton.backgroundColor = XY_HEX_RGB(0xf5f5f5);
        _rightButton.layer.cornerRadius = 10;
        [_rightButton setImage:[UIImage imageNamed:@"increase_action_normal"] forState:UIControlStateNormal];
        [_rightButton setImage:[UIImage imageNamed:@"increase_action_unable"] forState:UIControlStateDisabled];
        [_rightButton addTarget:self action:@selector(increase:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _rightButton;
}

- (UIView *)leftTrackView {
    if (!_leftTrackView) {
        _leftTrackView = [[UIView alloc] init];
        _leftTrackView.frame = CGRectMake(0, 0, _lineView.frame.size.width, _lineView.frame.size.height);
        _leftTrackView.backgroundColor = XY_HEX_RGB(0xD9D9D9);
        _leftTrackView.layer.cornerRadius = SLiderLine_H/2;
    }
    return _leftTrackView;
}

- (UIView *)rightTrackView {
    if (!_rightTrackView) {
        _rightTrackView = [[UIView alloc] init];
        _rightTrackView.frame = CGRectMake(0, 0, _lineView.frame.size.width, _lineView.frame.size.height);
        _rightTrackView.backgroundColor = XY_HEX_RGB(0xF5F5F5);
        _rightTrackView.layer.cornerRadius = SLiderLine_H/2;
    }
    return _rightTrackView;
}

- (UIView *)resultView {
    if (!_resultView) {
        _resultView = [[UIView alloc] initWithFrame:(CGRect){0,-35,60,45.5}];
        UIImageView *bubbleView = [[UIImageView alloc] initWithFrame:_resultView.bounds];
        bubbleView.image = [UIImage imageNamed:@"font_bubble"];
        [_resultView addSubview:bubbleView];
        _resultView.userInteractionEnabled = true;
        [_resultView addSubview:self.resultLabel];
    }
    return _resultView;
}

- (UILabel *)resultLabel {
    if (!_resultLabel) {
        _resultLabel = [[UILabel alloc] initWithFrame:(CGRect){0,8,60,20}];
        _resultLabel.font = MY_FONT_Bold(13);
        _resultLabel.textColor = XY_HEX_RGB(0x262626);
        _resultLabel.textAlignment = NSTextAlignmentCenter;
        _resultLabel.text = @"3";
        _resultLabel.userInteractionEnabled = true;
        UIPanGestureRecognizer *pan = [[UIPanGestureRecognizer alloc] initWithTarget:self action:@selector(PanEvent:)];
        [_resultLabel addGestureRecognizer:pan];
    }
    return _resultLabel;
}
@end
