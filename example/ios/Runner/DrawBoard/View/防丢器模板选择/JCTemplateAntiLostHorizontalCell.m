//
//  JCTemplateAntiLostHorizontalCell.m
//  XYFrameWork
//
//  Created by xingling xu on 2020/12/14.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import "JCTemplateAntiLostHorizontalCell.h"

@interface JCTemplateAntiLostHorizontalCell ()
@property (nonatomic, strong) UIImageView *icon;
@end

@implementation JCTemplateAntiLostHorizontalCell
- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
//        self.layer.borderWidth = 1;
//        self.layer.borderColor =HEX_RGB(0xEBEBEB).CGColor;
//        self.layer.cornerRadius = 10;
//        self.layer.masksToBounds = YES;
        [self addSubview:self.icon];
    }
    return self;
}

#define LEFT_RIGHT_GAP 98

#pragma mark - setter
- (void)setModel:(JCTemplateTagImageModel *)model {
    _model = model;
    self.icon.left = LEFT_RIGHT_GAP;
    self.icon.size = (CGSize) {
        [JCTemplateAntiLostHorizontalCell width4Template:model] - LEFT_RIGHT_GAP * 2,
        [JCTemplateAntiLostHorizontalCell height4Template:model]
    };
    
    self.icon.layer.borderWidth = 3;
    self.icon.layer.borderColor = [UIColor jk_colorWithHexString:@"#EBEBEB"].CGColor;
    self.icon.layer.cornerRadius = 12.0f;
    self.icon.layer.masksToBounds = YES;
    
    if (!model.thumbnail || model.thumbnail.length == 0) {
        [self.icon setImage:nil];
    } else {
        [self.icon jc_setImageWithUrl:model.thumbnail];
    }
}

#pragma mark - lazy
- (UIImageView *)icon {
    if (!_icon) {
        _icon = [[UIImageView alloc] initWithFrame:(CGRect){0,0,130,56}];
    }
    return _icon;
}

#pragma mark - caculate
+ (CGFloat)width4Template:(JCTemplateTagImageModel *)model {
    return kSCREEN_WIDTH;
}

+ (CGFloat)height4Template:(JCTemplateTagImageModel *)model {
    CGFloat height = model.height ? model.height.floatValue : 12.2f;
    CGFloat width = model.width ? model.width.floatValue : 25.0f;
    return ([self width4Template:model] - LEFT_RIGHT_GAP * 2)*height/width;
}
@end
