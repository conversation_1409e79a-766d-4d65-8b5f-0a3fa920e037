//
//  JCTemplateAntiLostChooseView.h
//  XYFrameWork
//
//  Created by xingling xu on 2020/12/14.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@class JCTemplateAntiLostChooseView;
@protocol JCTemplateAntiLostChooseViewViewDelegate <NSObject>
- (void)templateAntiChooseView:(JCTemplateAntiLostChooseView *)chooseView printId:(NSString *)printId;
@end

@interface JCTemplateAntiLostChooseView : UIView

+ (JCTemplateAntiLostChooseView *)showChooseTemplateViewWithDelegate:(id)delegate
                                                            deviceId:(NSString *)deviceId;

+ (void)dismissChooseView;
@end

NS_ASSUME_NONNULL_END
