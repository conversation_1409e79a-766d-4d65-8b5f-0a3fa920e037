//
//  JCTemplateAntiLostChooseView.m
//  XYFrameWork
//
//  Created by xingling xu on 2020/12/14.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import "JCTemplateAntiLostChooseView.h"
#import "JCTemplateTagModel.h"
#import "JCTemplateAntiLostCollectionView.h"
#import "JCElementPropertyBar.h"
#import "JCSearchTagTextField.h"
#import "JCTemplateHistoryView.h"
#import "JCApplicationManager.h"

#define FRAME_HEIGHT (400)

static NSInteger collection_tag = 1000;
static NSInteger choose_view_tag = 12368;

@interface JCTemplateAntiLostChooseView () <UITextFieldDelegate,UIScrollViewDelegate>
@property (nonatomic, strong) UIScrollView *scrollView;
@property (nonatomic, copy) NSArray *tagArr;;
@property (nonatomic, strong) JCElementPropertyBar *bar;
@property (nonatomic, strong) UIButton *cancelButton;
@property (nonatomic, weak) id  delegate;
@property (nonatomic, strong) NSString *deviceId;
@end

@implementation JCTemplateAntiLostChooseView

+ (JCTemplateAntiLostChooseView *)showChooseTemplateViewWithDelegate:(id)delegate
                                                           deviceId:(NSString *)deviceId {
    UIWindow *keyWindow = [[UIApplication sharedApplication].delegate window];
    UIView *maskView = [[UIView alloc] initWithFrame:(CGRect){0,0,kSCREEN_WIDTH,kSCREEN_HEIGHT}];
    maskView.backgroundColor = XY_HEX_RGBA(0x000000, 0.25);
    maskView.tag = choose_view_tag;
    [keyWindow addSubview:maskView];
    
    UIView *tapView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, XY_KEY_WONDOW.width, XY_KEY_WONDOW.height - FRAME_HEIGHT)];
    tapView.backgroundColor = [UIColor clearColor];
    [maskView addSubview:tapView];
    
    JCTemplateAntiLostChooseView *chooseView = [[JCTemplateAntiLostChooseView alloc] initWithFrame:(CGRect){0,kSCREEN_HEIGHT,kSCREEN_WIDTH,FRAME_HEIGHT}];
    chooseView.delegate = delegate;
    chooseView.deviceId = deviceId;
    [maskView addSubview:chooseView];
    [UIView animateWithDuration:0.3 animations:^{
        chooseView.top = XY_KEY_WONDOW.height - FRAME_HEIGHT;
    }];

    UITapGestureRecognizer *gesture = [[UITapGestureRecognizer alloc] initWithTarget:chooseView
                                                                              action:@selector(close:)];
    [tapView addGestureRecognizer:gesture];
    
    return chooseView;
}

+ (void)dismissChooseView {
    UIWindow *keyWindow = [[UIApplication sharedApplication].delegate window];
    UIView *view = [keyWindow viewWithTag:choose_view_tag];
    [view removeFromSuperview];
}


- (void)close:(UIButton *)button {
    [JCTemplateAntiLostChooseView dismissChooseView];
}

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = [UIColor whiteColor];
        self.layer.borderWidth = 1;
        self.layer.borderColor = XY_HEX_RGB(0xEAEAEA).CGColor;
        self.layer.cornerRadius = 12;
        self.layer.masksToBounds = YES;
        [self loadRootView];
    }
    return self;
}

- (void)loadRootView {
    [self addSubview:self.bar];
    [self addSubview:self.scrollView];
    [self addSubview:self.cancelButton];
    
    [self.bar mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.equalTo(self);
        make.top.mas_equalTo(0);
        make.height.mas_equalTo(50);
    }];
    
    [self.scrollView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.bottom.equalTo(self);
        make.top.equalTo(self.bar.mas_bottom);
    }];
}

- (void)setDeviceId:(NSString *)deviceId {
    _deviceId = deviceId;
    [self requestTemplateTags];
}

#define KEY_ANTI_LOST_CATEGORY_LIST @"anti.lost.category.list-"

- (void)requestTemplateTags {
    NSString *path = @"kraken/system/category/list";
    [DCHTTPRequest getWithParams:@{@"deviceId": self.deviceId ?: @""}
                       ModelType:nil
                             url:path
                         Success:^(__kindof YTKBaseRequest * _Nonnull request,
                                   NSArray *list) {
        if (list.count > 0) {
            // 存储已加载的模板分类
            NSString *string = [list yy_modelToJSONString];
            [[NSUserDefaults standardUserDefaults] setValue:string
                                                     forKey:[KEY_ANTI_LOST_CATEGORY_LIST stringByAppendingString:
                                                             [JCApplicationManager shareInstance].user.userId]];
            
            self.tagArr = [JCTemplateTagModel arrayOfModelsFromDictionaries:list
                                                                      error:nil];
            
            NSMutableArray *temp = [NSMutableArray arrayWithCapacity:self.tagArr.count+1];
            JCTemplateTagModel *usedModel = [JCTemplateTagModel new];
            usedModel.idStr = @"1";
            usedModel.name = @"我的";
            [temp addObject:[JCBarData dataWithTitle:@"我的" userInfo:usedModel]];
            [self.tagArr enumerateObjectsUsingBlock:^(JCTemplateTagModel *model, NSUInteger idx, BOOL * _Nonnull stop) {
                JCBarData *data = [JCBarData dataWithTitle:model.name userInfo:model];
                if (data) [temp addObject:data];
            }];
            self.tagArr = temp;
            
            [self.bar refrehWithDatas:temp select:0];
            [self reloadCollectionView];
            
            JCTemplateAntiLostCollectionView *currentCollectionView = [self.scrollView viewWithTag:collection_tag];
            [currentCollectionView showContentWithModel:usedModel];
        }
    } failure:^(YTKBaseRequest * _Nullable request, NSError * _Nullable error) {
        // 已加载缓存的模板分类
        NSString *listJsonString = [[NSUserDefaults standardUserDefaults] valueForKey:[KEY_ANTI_LOST_CATEGORY_LIST stringByAppendingString:
                                                                                      [JCApplicationManager shareInstance].user.userId]];
        if ([listJsonString isKindOfClass:[NSString class]] &&
            [listJsonString length] > 0) {
            NSArray *array = [JCTemplateTagModel arrayOfModelsFromString:listJsonString
                                                                   error:nil];
            if ([array isKindOfClass:[NSArray class]] &&
                [array count] > 0) {
                self.tagArr = array;
                
                NSMutableArray *temp = [NSMutableArray arrayWithCapacity:self.tagArr.count+1];
                JCTemplateTagModel *usedModel = [JCTemplateTagModel new];
                usedModel.idStr = @"1";
                usedModel.name = @"我的";
                [temp addObject:[JCBarData dataWithTitle:@"我的" userInfo:usedModel]];
                [self.tagArr enumerateObjectsUsingBlock:^(JCTemplateTagModel *model, NSUInteger idx, BOOL * _Nonnull stop) {
                    JCBarData *data = [JCBarData dataWithTitle:model.name userInfo:model];
                    if (data) [temp addObject:data];
                }];
                self.tagArr = temp;
                
                [self.bar refrehWithDatas:temp select:0];
                [self reloadCollectionView];
                
                JCTemplateAntiLostCollectionView *currentCollectionView = [self.scrollView viewWithTag:collection_tag];
                [currentCollectionView showContentWithModel:usedModel];
            }
        }
    }];
}

- (void)reloadCollectionView {
    for (NSInteger index = self.scrollView.subviews.count - 1; index > -1; index --) {
        [[self.scrollView.subviews objectAtIndex:index] removeFromSuperview];
    }
    for (NSInteger i = 0; i < self.tagArr.count; i ++) {
        JCTemplateAntiLostCollectionView *collectionView = [[JCTemplateAntiLostCollectionView alloc] initWithFrame:(CGRect){i*kSCREEN_WIDTH,0,kSCREEN_WIDTH,self.scrollView.height}];
        collectionView.delegate = self;
        collectionView.tag = collection_tag + i;
        [self.scrollView addSubview:collectionView];
    }
    self.scrollView.contentSize = CGSizeMake(self.tagArr.count * kSCREEN_WIDTH, self.scrollView.height);
}

#pragma mark - JCTemplateCollectionViewDelegate
- (void)templateCollectionView:(JCTemplateAntiLostCollectionView *)collectionView selectModel:(JCTemplateTagImageModel *)model {
    NSString *printId = model.labelId;
    if (STR_IS_NIL(printId)) return;
    if (self.delegate && [self.delegate respondsToSelector:@selector(templateAntiChooseView:printId:)]) {
        [self.delegate templateAntiChooseView:self printId:printId];
    }
}

- (void)propertyBar:(JCElementPropertyBar *)bar didSelectIndex:(NSInteger)index withData:(JCBarData *)data {
    JCTemplateTagModel *model = (JCTemplateTagModel *)data.userInfo;
    if ([model isKindOfClass:[JCTemplateTagModel class]]) {
        self.scrollView.contentOffset = (CGPoint){index*kSCREEN_WIDTH,0};
        JCTemplateAntiLostCollectionView *currentCollectionView = [self.scrollView viewWithTag:collection_tag+index];
        [currentCollectionView showContentWithModel:model];
    }
}

#pragma mark - lazy
- (UIScrollView *)scrollView {
    if (!_scrollView) {
        _scrollView = [[UIScrollView alloc] initWithFrame:(CGRect){0,50,kSCREEN_WIDTH,FRAME_HEIGHT-50}];
        _scrollView.scrollEnabled = NO;
//        _scrollView.pagingEnabled = YES;
        _scrollView.delegate = self;
    }
    return _scrollView;
}

- (NSArray *)tagArr {
    if (!_tagArr) {
        _tagArr = [NSArray array];
    }
    return _tagArr;
}

- (JCElementPropertyBar *)bar {
    if (!_bar) {
        _bar = [[JCElementPropertyBar alloc] initWithFrame:(CGRect){0,0,kSCREEN_WIDTH,50}];
//        [_bar folderButtonHidden];
        _bar.delegate = self;
    }
    return _bar;
}

@end
