//
//  JCTemplateAntiLostCollectionView.h
//  XYFrameWork
//
//  Created by xingling xu on 2020/12/14.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import <UIKit/UIKit.h>
#import "JCTemplateTagModel.h"

@class JCTemplateAntiLostCollectionView;
@protocol JCTemplateAntiLostCollectionViewDelegate <NSObject>
- (void)templateCollectionView:(JCTemplateAntiLostCollectionView *)collectionView selectModel:(JCTemplateTagImageModel *)model;
@end

@interface JCTemplateAntiLostCollectionView : UIView
@property (nonatomic, weak) id  delegate;
// 根据标签名查询
- (void)showContentWithModel:(JCTemplateTagModel *)model;
// 带关键字搜索
- (void)showContentWithKeyWord:(NSString *)keyWord;
@end
