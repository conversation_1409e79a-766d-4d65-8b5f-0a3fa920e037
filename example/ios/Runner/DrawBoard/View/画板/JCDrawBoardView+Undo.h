//
//  JCDrawBoardView+Undo.h
//  XYFrameWork
//
//  Created by xingling xu on 2020/8/14.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import <UIKit/UIKit.h>
#import "JCDrawBoardView.h"

#define UndoObject(params)       [ActionObject objectWith:self sel:@selector(undoActionWith:) object:params]

@interface JCDrawBoardView (Undo)
// 撤销
-(void)item_chexiao;
// 恢复
-(void)item_huifu;
// 撤销恢复实际执行的动作
- (void)undoActionWith:(NSDictionary *)params;

/** 保存修改前的属性：万能方法 */
- (void)beginUndo:(NSDictionary *)params;
/** 保存修改后的属性：万能方法 */
- (void)endUndo:(NSDictionary *)params;

// 添加单个元素
- (void)undo_addElement:(JCElementBaseView *)element;
// 删除单个元素
- (void)undo_deleteElement:(JCElementBaseView *)element;
// 旋转某个元素
- (void)undo_rotateElement:(JCElementBaseView *)element;

// 添加多个元素
- (void)undo_addMutibleElements:(NSArray *)elements;
// 删除多个元素
- (void)undo_deleteMutibleElements:(NSArray *)elements;
// 旋转多个元素
- (void)undo_rotateMutibleElements:(NSArray *)elements;

@end

