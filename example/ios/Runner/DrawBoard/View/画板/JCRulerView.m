//
//  JCRulerView.m
//  XYFrameWork
//
//  Created by <PERSON><PERSON><PERSON> on 2019/5/30.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCRulerView.h"

@implementation JCRulerView

- (instancetype)initWithFrame:(CGRect)frame withType:(NSInteger)type{
    self = [super initWithFrame:frame];
    if(self){
        [self commonUIWithType:type];
    }
    return self;
}

- (void)commonUIWithType:(NSInteger)type{
    if(type == 1){
        //数字所在位置方向
        self.clipsToBounds = YES;
        self.numberDirection = numberBottom;
        if (self.numberDirection == numberTop) {
             //刻度起始位置
           self.shortScaleStart = JCRuleWidth - JCShortScaleLength;
          self.longScaleStart = JCRuleWidth - JCLongScaleLength;
          
        }else{
            self.shortScaleStart = 0.0;
            self.longScaleStart = 0.0;
        }
      

        self.shortScaleLength = JCShortScaleLength;
        self.longScaleLength = JCLongScaleLength;
        //刻度宽度
        self.scaleWidth = 1;
        //刻度颜色
        self.scaleColor = UIColorFromHex(0x999999);
        //刻度之间的距离
        
        //刻度距离数字的距离
        self.distanceFromScaleToNumber = JCRuleWidth*0.05;
        //指示视图属性设置
        self.pointSize = CGSizeMake(2, 0);
        self.pointColor = UIColorFromHex(0x20c6ba);
        self.pointStart = 0;
        //文字属性
        self.numberFont = [UIFont systemFontOfSize:10];
        self.numberColor = UIColorFromHex(0x999999);
        //取值范围
        self.min = 0;
       
        self.backgroundColor = [UIColor clearColor];
        self.tag = 0;
    }else{
        //刻度高度
        self.clipsToBounds = YES;
        //数字所在位置方向
        self.numberDirection = numberRight;
        if (self.numberDirection == numberLeft) {
             //刻度起始位置
               self.shortScaleStart = JCRuleWidth - JCShortScaleLength;
               self.longScaleStart = JCRuleWidth - JCLongScaleLength;
        }else{
            //刻度起始位置
            self.shortScaleStart = 0.0;
            self.longScaleStart = 0.0;
        }
        
        self.shortScaleLength = JCShortScaleLength;
        self.longScaleLength = JCLongScaleLength;
        //刻度宽度
        self.scaleWidth = 1;
        //刻度颜色
        self.scaleColor = UIColorFromHex(0x999999);
        
        //刻度距离数字的距离
        self.distanceFromScaleToNumber = JCRuleWidth*0.1;
        //指示视图属性设置
        self.pointSize = CGSizeMake(2, 20);
        self.pointColor = UIColorFromHex(0x20c6ba);
        self.pointStart = 0;
        //文字属性
        self.numberFont = [UIFont systemFontOfSize:10];
        self.numberColor = UIColorFromHex(0x999999);
        //取值范围
        self.min = 0;
     
        //使用小数类型
        //选中
        self.backgroundColor = [UIColor clearColor];
        self.tag = 2;
    }
}
/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/

@end
