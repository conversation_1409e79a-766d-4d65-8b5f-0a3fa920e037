//
//  JCBoardTitleView.h
//  XYFrameWork
//
//  Created by xingling xu on 2020/2/21.
//  Copyright © 2020 Jingchen Technology Co.Ltd . All rights reserved.
//

#import <UIKit/UIKit.h>

@class JCBoardTitleView;
@protocol JCBoardTitleViewDelegate <NSObject>
@optional
- (void)titleViewDidClick:(JCBoardTitleView *)titleView;
@end

// 画板页面顶部导航条Title区域
@interface JCBoardTitleView : UIView

@property (nonatomic, weak) id delegate;
/**
 * 刷新画板titleView
 *
 * @param    currentPage  画板当前选择页码
 * @param    totalPages   画板总页码
 * @param    sizeString   画板尺寸组成的字符串 "80x50mm"
 *
 */
- (void)refreshWithCurrentPage:(NSInteger)currentPage totalPages:(NSInteger)totalPages drawBoardSizeString:(NSString *)sizeString;

- (void)reloadData;
@end

