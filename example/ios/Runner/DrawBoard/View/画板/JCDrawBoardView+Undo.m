//
//  JCDrawBoardView+Undo.m
//  XYFrameWork
//
//  Created by xingling xu on 2020/8/14.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import "JCDrawBoardView+Undo.h"
#import "JCElementEditManager.h"

@implementation JCDrawBoardView (Undo)

#pragma mark --  撤销、恢复
-(void)item_chexiao{
    [self.jcUndoManager undo];
}

-(void)item_huifu{
    [self.jcUndoManager redo];
}

- (void)undo_addElement:(JCElementBaseView *)element {
    ActionObject *redoObject = UndoObject((@{k_element:element,k_action:k_add}));
    ActionObject *undoObject = UndoObject((@{k_element:element,k_action:k_delete}));
    undoObject.redoObject = redoObject;
    [self.jcUndoManager registerUndoWithUndo:undoObject];
}

- (void)undo_deleteElement:(JCElementBaseView *)element {
    // 添加撤销恢复操作
    ActionObject *redoObject = UndoObject((@{k_element:element,k_action:k_delete}));
    ActionObject *undoObject = UndoObject((@{k_element:element,k_action:k_add}));
    undoObject.redoObject = redoObject;
    [self.jcUndoManager registerUndoWithUndo:undoObject];
}

- (void)undo_rotateElement:(JCElementBaseView *)element {
    CGFloat beginRotate = element.rotate;
    [self beginUndo:@{k_element:element,k_action:k_rotate,k_rotate:[NSString stringWithFormat:@"%f",beginRotate]}];
    CGFloat endRotate = (element.rotate + 90 >= 360)?0:(element.rotate + 90);
    [self endUndo:@{k_element:element,k_action:k_rotate,k_rotate:[NSString stringWithFormat:@"%f",endRotate]}];
}

#pragma mark - 多选记录撤销恢复等
- (void)undo_addMutibleElements:(NSArray *)elements {
    ActionObject *redoObject = UndoObject((@{k_muti_elements:elements,k_action:k_add}));
    ActionObject *undoObject = UndoObject((@{k_muti_elements:elements,k_action:k_delete}));
    undoObject.redoObject = redoObject;
    [self.jcUndoManager registerUndoWithUndo:undoObject];
}

- (void)undo_deleteMutibleElements:(NSArray *)elements {
    ActionObject *redoObject = UndoObject((@{k_muti_elements:elements,k_action:k_delete}));
    ActionObject *undoObject = UndoObject((@{k_muti_elements:elements,k_action:k_add}));
    undoObject.redoObject = redoObject;
    [self.jcUndoManager registerUndoWithUndo:undoObject];
}

- (void)undo_rotateMutibleElements:(NSArray *)elements {
    
}

/** 保存修改前的属性 */
- (void)beginUndo:(NSDictionary *)params {
    [self.jcUndoManager beginUndoWith:UndoObject(params)];
}
/** 保存修改后的属性 */
- (void)endUndo:(NSDictionary *)params {
    [self.jcUndoManager endUndoWith:UndoObject(params)];
}

- (void)undoActionWith:(NSDictionary *)params {
    NSString *action = params[k_action];
    if ([action isEqualToString:k_add]) {
        NSArray *elements = params[k_muti_elements];
        if (elements && elements.count > 0) {// 添加多个元素
            [self addElements:elements];
        } else {
            JCElementBaseView *element = params[k_element];
            [self addElement:element recordUndo:NO];
        }
    } else if ([action isEqualToString:k_delete]) {
        NSArray *elements = params[k_muti_elements];
        if (elements && elements.count > 0) {// 删除多个元素
            [self deleteElements:elements];
        } else {
            JCElementBaseView *element = params[k_element];
            [self deleteElement:element recordUndo:NO];
        }
    } else if ([action isEqualToString:k_frame]) {
        JCElementBaseView *element = params[k_element];
        CGRect frame = CGRectFromString(params[k_frame]);
        [JCElementEditManager editElement:element frame:frame];
    } else if ([action isEqualToString:k_rotate]) {
        JCElementBaseView *element = params[k_element];
        CGFloat rotate = [params[k_rotate] floatValue];
        element.rotate = rotate;
    } else if ([action isEqualToString:k_font]) {
        JCElementBaseView *element = params[k_element];
        CGFloat fontSize = [params[k_font] floatValue];
        [JCElementEditManager editElement:element fontSize_mm:fontSize];
    } else if ([action isEqualToString:k_bold]) {
        JCElementBaseView *element = params[k_element];
        BOOL value = [params[k_bold] isEqualToString:@"1"];
        [JCElementEditManager editElement:element bold:value];
    } else if ([action isEqualToString:k_underline]) {
        JCElementBaseView *element = params[k_element];
        BOOL value = [params[k_underline] isEqualToString:@"1"];
        [JCElementEditManager editElement:element underLine:value];
    } else if ([action isEqualToString:k_italic]) {
        JCElementBaseView *element = params[k_element];
        BOOL value = [params[k_italic] isEqualToString:@"1"];
        [JCElementEditManager editElement:element italic:value];
    } else if ([action isEqualToString:k_textalignment]) {
        JCElementBaseView *element = params[k_element];
        NSInteger value = [params[k_textalignment] integerValue];
        [JCElementEditManager editElement:element textAlignment:value];
    }  else if ([action isEqualToString:k_linespace]) {
        JCElementBaseView *element = params[k_element];
        CGFloat value = [params[k_linespace] floatValue];
        [JCElementEditManager editElement:element lineSpace:value];
    } else if ([action isEqualToString:k_wordspace]) {
        JCElementBaseView *element = params[k_element];
        CGFloat value = [params[k_wordspace] floatValue];
        [JCElementEditManager editElement:element textSpace:value];
    } else if ([action isEqualToString:k_barcodename]) {
        JCElementBaseView *element = params[k_element];
        NSString *value = params[k_barcodename];
        [JCElementEditManager editElement:element codeTypeName:value];
    } else if ([action isEqualToString:k_barcodestyle]) {
        JCElementBaseView *element = params[k_element];
        NSInteger value = [params[k_barcodestyle] integerValue];
        [JCElementEditManager editElement:element barCodeTextPosition:value];
    } else if ([action isEqualToString:k_linetype]) {
        JCElementBaseView *element = params[k_element];
        NSInteger value = [params[k_linetype] integerValue];
        [JCElementEditManager editElement:element lineType:value];
    } else if ([action isEqualToString:k_linewidth]) {
        JCElementBaseView *element = params[k_element];
        CGFloat value = [params[k_linewidth] floatValue];
        [JCElementEditManager editElement:element mmLineWidth:value];
    } else if ([action isEqualToString:k_text]) {
        JCElementBaseView *element = params[k_element];
        NSString *value = params[k_text];
        [JCElementEditManager editElement:element content:value];
    } else if ([action isEqualToString:k_shape]) {
        JCElementBaseView *element = params[k_element];
        NSInteger value = [params[k_shape] integerValue];
        [JCElementEditManager editElement:element shapeType:value];
    } else if ([action isEqualToString:k_fontcode]) {
        JCElementBaseView *element = params[k_element];
        NSString *value = params[k_fontcode];
        [JCElementEditManager editElement:element fontCode:value];
    } else if ([action isEqualToString:k_image_threshold]) {
        JCElementBaseView *element = params[k_element];
        NSString *value = params[k_image_threshold];
        [JCElementEditManager editElement:element imageThresHoldValue:value];
    } else if ([action isEqualToString:k_textverticalalignment]) {
        JCElementBaseView *element = params[k_element];
        NSInteger value = [params[k_textverticalalignment] integerValue];
        [JCElementEditManager editElement:element verticalTextAlignment:value];
    }
    JCPerformSelector(self.context.baseController, sel(@"undoNeedRefreshProperty"));
}

@end
