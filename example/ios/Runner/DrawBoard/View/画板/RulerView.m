//
//  RulerView.m
//  TYFitFore
//
//  Created by apple on 2018/7/5.
//  Copyright © 2018年 tangpeng. All rights reserved.
//

#import "RulerView.h"
#import "RulerLayout.h"

#define kDirectionHorizontal (self.rulerLayout.scrollDirection == UICollectionViewScrollDirectionHorizontal)

static NSString *const rulerCollectionViewCellIdentifier = @"rulerCollectionViewCellIdentifier";

@interface RulerView () <UICollectionViewDataSource, UICollectionViewDelegate,UIScrollViewDelegate>

@property (nonatomic, strong) RulerLayout *rulerLayout;

@property (nonatomic, strong) UIImageView *indicatorView;                                   /**< 指示器视图  */
//layer层，渐变layer
@property (nonatomic, strong) CAGradientLayer *startGradientLayer;
@property (nonatomic, strong) CAGradientLayer *endGradientLayer;

@property (nonatomic, assign) NSInteger selectIndex;                                        /**< 当前选中的下标  */
@property (nonatomic, assign) BOOL activeDelegate;                                          /**< 允许调用代理方法  */



@end

@implementation RulerView

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {

    }
    return self;
}

- (void)willMoveToSuperview:(UIView *)newSuperview {
    [super willMoveToSuperview:newSuperview];
    //视图布局
    if (newSuperview) {
        [self layoutViews];
    }
}



-(void)setScaleWidth:(CGFloat)scaleWidth{
    _scaleWidth = scaleWidth;
    if(!self.rulerLayout) return;
    if (self.numberDirection == numberTop || self.numberDirection == numberBottom) {
        //水平方向
        self.rulerLayout.scrollDirection = UICollectionViewScrollDirectionHorizontal;
        self.rulerLayout.itemSize = CGSizeMake(self.scaleWidth, CGRectGetHeight(self.frame));
      
    } else {
        //垂直方向
        self.rulerLayout.scrollDirection = UICollectionViewScrollDirectionVertical;
        self.rulerLayout.itemSize = CGSizeMake(CGRectGetWidth(self.frame), self.scaleWidth);
    }
    [self.rulerCollectionView reloadData];
    
}

-(void)layoutSubviews{
    [super layoutSubviews];
    if(self.rulerLayout){
        self.rulerCollectionView.frame = self.bounds;
    }
}

#pragma mark - 视图布局
- (void)layoutViews {
    
    //添加渐变层
    if (self.useGradient) {
        [self addStartGradientLayer];
        [self addEndGradientLayer];
    }
    
    UIView *separateLine = [[UIView alloc] init];
    separateLine.backgroundColor = XY_HEX_RGB(0xcccccc);
    
    //计算cell的size
    self.rulerLayout.spacing = self.distanceBetweenScale;
  
    if (self.numberDirection == numberTop || self.numberDirection == numberBottom) {
        //水平方向
        self.rulerLayout.scrollDirection = UICollectionViewScrollDirectionHorizontal;
        self.rulerLayout.itemSize = CGSizeMake(self.scaleWidth, CGRectGetHeight(self.frame));
        if (self.numberDirection == numberTop) {
            separateLine.frame = CGRectMake(0,self.height-1, self.width, 1);
          
        }else{
            separateLine.frame = CGRectMake(0,0, self.width, 1);
           
        }
      
    } else {
        //垂直方向
        self.rulerLayout.scrollDirection = UICollectionViewScrollDirectionVertical;
        self.rulerLayout.itemSize = CGSizeMake(CGRectGetWidth(self.frame), self.scaleWidth);
        
        if (self.numberDirection == numberLeft) {
              separateLine.frame = CGRectMake(self.width-1,0, 1, self.height);
        }else{
              separateLine.frame = CGRectMake(0,0, 1, self.height);
        }
      
    }
    
    self.rulerCollectionView = [[UICollectionView alloc] initWithFrame:self.bounds collectionViewLayout:self.rulerLayout];
    self.rulerCollectionView.clipsToBounds = NO;
    self.rulerCollectionView.delegate = self;
    self.rulerCollectionView.dataSource = self;
    self.rulerCollectionView.scrollEnabled = NO;
    self.rulerCollectionView.showsVerticalScrollIndicator = NO;
    self.rulerCollectionView.showsHorizontalScrollIndicator = NO;
    self.rulerCollectionView.backgroundColor = [[UIColor whiteColor] colorWithAlphaComponent:0];
 
    [self.rulerCollectionView registerClass:[RulerCollectionViewCell class] forCellWithReuseIdentifier:rulerCollectionViewCellIdentifier];
    [self addSubview:self.rulerCollectionView];
    [self.rulerCollectionView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
    [self addSubview:separateLine];
    
    

    
    
}



-(void)setDistanceBetweenScale:(CGFloat)distanceBetweenScale{
    _distanceBetweenScale = distanceBetweenScale;
    if(!self.rulerLayout) return;
    self.rulerLayout.spacing = self.distanceBetweenScale;
//    [self.rulerCollectionView reloadData];
}

#pragma mark - UICollectionView代理方法
- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    //校验数据
    if (self.max == 0 || self.min >= self.max) {
        return 0;
    }
    
    //因为是从0开始，所以需要在最大值基础上 + 1
    NSInteger items = self.max - self.min;
    if (self.isDecimal) {
        //如果是一位小数类型，则数据扩大10倍
        return items * 10 + 1;
    } else {
        return items + 1;
    }
}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    RulerCollectionViewCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:rulerCollectionViewCellIdentifier forIndexPath:indexPath];
    cell.index =  indexPath.item;
    //刻度属性设置
    cell.shortScaleLength = self.shortScaleLength;
    cell.longScaleLength = self.longScaleLength;
    cell.scaleWidth = self.scaleWidth;
    cell.scaleColor = self.scaleColor;
    cell.shortScaleStart = self.shortScaleStart;
    cell.longScaleStart = self.longScaleStart;
    cell.numberFont = self.numberFont;
    cell.numberColor = self.numberColor;
    cell.numberDirection = self.numberDirection;
    cell.distanceFromScaleToNumber = self.distanceFromScaleToNumber;
    cell.isDecimal = self.isDecimal;
    cell.min = self.min;
    cell.templateScale = self.templateScale;
   
    [cell setNeedsLayout];
    [cell makeCellHiddenText];
    
    return cell;
}

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
//    CGFloat offset = (kDirectionHorizontal ?
//                      (scrollView.contentOffset.x + self.rulerCollectionView.contentInset.left) :
//                      (scrollView.contentOffset.y + self.rulerCollectionView.contentInset.top)) ;
//    
//    NSInteger index = roundl(offset / (self.scaleWidth + self.distanceBetweenScale));
//    self.selectIndex = index;
//    
//    double value = 0;
//    //判断是否是小数
//    if (self.isDecimal) {
//        value = index * 1.0 / 10.0 + self.min;
//    } else {
//        value = index * 1.0 + self.min;
//    }
//    
//    //保证数据在范围内
//    if (value >= self.min && value <= self.max && self.activeDelegate) {
//        if ([self.delegate respondsToSelector:@selector(rulerSelectValue:tag:)]) {
//            [self.delegate rulerSelectValue:value tag:self.tag];
//        }
////        NSLog(@"value = %lf, self.selectIndex = %li", value, self.selectIndex);
//    }
//    NSLog(@"x = %f ,y = %f",scrollView.contentOffset.x,scrollView.contentOffset.y);
}

- (void)scrollViewWillBeginDragging:(UIScrollView *)scrollView {
//    [self resetCell];
//    //指示器视图居中显示
//    [self centerPointView];
}

- (void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView {
//    BOOL scrollToScrollStop = !scrollView.tracking && !scrollView.dragging && !scrollView.decelerating;
//    if (scrollToScrollStop) {
//        [self selectCell];
//    }
}

- (void)scrollViewDidEndDragging:(UIScrollView *)scrollView willDecelerate:(BOOL)decelerate {
//    if (!decelerate) {
//        BOOL dragToDragStop = scrollView.tracking && !scrollView.dragging && !scrollView.decelerating;
//        if (dragToDragStop) {
//            [self selectCell];
//        }
//    }
}

- (void)scrollViewDidEndScrollingAnimation:(UIScrollView *)scrollView {
//    [self selectCell];
}

- (void)selectCell {
    if (self.selectionEnable) {
        NSInteger min = self.selectIndex - 5;
        NSInteger max = self.selectIndex + 5;
        for (NSInteger i=min; i<max; i++) {
            if (i >= 0 && i < [self collectionView:self.rulerCollectionView numberOfItemsInSection:0]) {
                RulerCollectionViewCell *cell = (RulerCollectionViewCell *)[self.rulerCollectionView cellForItemAtIndexPath:[NSIndexPath indexPathForRow:i inSection:0]];
                [cell makeCellHiddenText];
            }
        }
        
        RulerCollectionViewCell *cell = (RulerCollectionViewCell *)[self.rulerCollectionView cellForItemAtIndexPath:[NSIndexPath indexPathForRow:self.selectIndex inSection:0]];
        [cell makeCellSelect];
    }
}

- (void)resetCell {
    if (self.selectionEnable) {
        NSInteger min = self.selectIndex - 5;
        NSInteger max = self.selectIndex + 5;
        for (NSInteger i=min; i<max; i++) {
            if (i >= 0 && i < [self collectionView:self.rulerCollectionView numberOfItemsInSection:0]) {
                RulerCollectionViewCell *cell = (RulerCollectionViewCell *)[self.rulerCollectionView cellForItemAtIndexPath:[NSIndexPath indexPathForRow:i inSection:0]];
                [cell setNeedsLayout];
            }
        }
    }
}

#pragma mark - 渐变透明层
- (void)addStartGradientLayer {
    //初始化CAGradientlayer对象，使它的大小为UIView的大小
    self.startGradientLayer = [CAGradientLayer layer];
    self.startGradientLayer.frame = self.bounds;
    
    //将CAGradientlayer对象添加在我们要设置背景色的视图的layer层
    [self.layer addSublayer:self.startGradientLayer];
    
    //设置渐变区域的起始和终止位置（颜色渐变范围为0-1）
    self.startGradientLayer.startPoint = CGPointMake(0, 0);
    self.startGradientLayer.endPoint = CGPointMake(1, 0);
    
    //设置颜色数组
    self.startGradientLayer.colors = @[(__bridge id)[UIColor whiteColor].CGColor,
                                       (__bridge id)[[UIColor whiteColor]colorWithAlphaComponent:0.0f].CGColor];
    
    //设置颜色分割点（区域渐变范围：0-1）
    self.startGradientLayer.locations = @[@(0.0f), @(0.3f)];
}

- (void)addEndGradientLayer {
    //初始化CAGradientlayer对象，使它的大小为UIView的大小
    self.endGradientLayer = [CAGradientLayer layer];
    self.endGradientLayer.frame = self.bounds;
    
    //将CAGradientlayer对象添加在我们要设置背景色的视图的layer层
    [self.layer addSublayer:self.endGradientLayer];
    
    //设置渐变区域的起始和终止位置（颜色渐变范围为0-1）
    self.endGradientLayer.startPoint = CGPointMake(0, 0);
    self.endGradientLayer.endPoint = CGPointMake(1, 0);
    
    //设置颜色数组
    self.endGradientLayer.colors = @[(__bridge id)[[UIColor whiteColor]colorWithAlphaComponent:0.0f].CGColor,
                                     (__bridge id)[UIColor whiteColor].CGColor];
    
    //设置颜色分割点（区域渐变范围：0-1）
    self.endGradientLayer.locations = @[@(0.7f), @(1.0f)];
}

#pragma mark - Tool
/** 传入字符串是否是数字 */
+ (BOOL)isPureInt:(NSString*)string {
    if (!string) {
        return false;
    }
    //string不是浮点型
    if (![string containsString:@"."]) {
        NSScanner *scan = [NSScanner scannerWithString:string];
        int val;
        return[scan scanInt:&val] && [scan isAtEnd];
    }
    //string是浮点型
    else {
        NSArray *numberArray = [string componentsSeparatedByString:@"."];
        if (numberArray.count != 2) {
            return false;
        }
        else {
            NSString *behindNumber = numberArray[1];
            //如果小数点后的数字等于0，则是整数
            return ([behindNumber integerValue] == 0);
        }
    }
}

-(RulerLayout *)rulerLayout
{
    if (!_rulerLayout) {
        _rulerLayout = [[RulerLayout alloc] init];
    }
    return _rulerLayout;
}


@end
