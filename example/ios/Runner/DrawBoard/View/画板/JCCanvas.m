//
//  JCCanvas.m
//  XYFrameWork
//
//  Created by j c on 2019/11/19.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCCanvas.h"
#import "JCBoardConst.h"

@interface JCCanvas()

@property (nonatomic,assign) CGRect originRect;
@property (nonatomic,strong) UIView *ppV;

@end

@implementation JCCanvas

-(instancetype)init{
    self = [super init];
    if(self){
       
        self.originRect = CGRectZero;
        self.backgroundColor = XY_HEX_RGB(0xf2f2f2);

    }
    return self;
}



-(UIView *)viewForZoomingInScrollView:(UIScrollView *)scrollView{
    return self;
}

- (void)scrollViewWillBeginZooming:(UIScrollView *)scrollView withView:(nullable UIView *)view
{
//    CGFloat scale = scrollView.zoomScale;
//    NSLog(@"scrollViewWillBeginZooming-----Sacle=%f",scale);
}

- (void)scrollViewDidEndZooming:(UIScrollView *)scrollView withView:(nullable UIView *)view atScale:(CGFloat)scale
{
//    NSLog(@"DidEndZooming-----Sacle=%f",scale);
   
    if (self.operate) {
        self.operate(scrollView.contentOffset.x, scrollView.contentOffset.y,scale);
    }
}


-(UIView *)hitTest:(CGPoint)point withEvent:(UIEvent *)event
{
    UIView *view = [super hitTest:point withEvent:event];
    BOOL scrollEnable = NO;
    if([view isKindOfClass:NSClassFromString(@"JCElementMainView")] || [view isKindOfClass:NSClassFromString(@"JCCanvas")]){
        scrollEnable = YES;
        view = self;
    }
    if ([self.superview isKindOfClass:[UIScrollView class]]) {
        UIScrollView *scrollView  = (UIScrollView*)self.superview;
        scrollView.scrollEnabled = scrollEnable;
    }
    return view;
}

- (void)scrollViewDidScroll:(UIScrollView *)scrollView
{
    NSLog(@"scrollViewDidScroll---scale---%f=======offset: %@ ",scrollView.zoomScale,NSStringFromCGPoint(scrollView.contentOffset));
    if (self.operate) {
        self.operate(scrollView.contentOffset.x, scrollView.contentOffset.y,scrollView.zoomScale);
    }
   
  
}


- (void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView
{
   
//     NSLog(@"scrollViewDidEndDecelerating---scale---%f",scrollView.zoomScale);
    if (self.operate) {
        self.operate(scrollView.contentOffset.x, scrollView.contentOffset.y,scrollView.zoomScale);
    }
}



@end
