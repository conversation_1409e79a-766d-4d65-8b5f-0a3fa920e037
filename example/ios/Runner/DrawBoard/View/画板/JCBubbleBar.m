//
//  JCBubbleBar.m
//  XYFrameWork
//
//  Created by xingling xu on 2020/4/15.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import "JCBubbleBar.h"
#import "JCBubbleTableViewCell.h"

static NSInteger const base_tag = 1000;

@interface JCBubbleBar () <UITableViewDelegate,UITableViewDataSource>
@property (nonatomic, copy) NSArray *images;
@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, copy) void(^select)(NSInteger index);
@property (nonatomic, strong) UIView *targetView;
@property (nonatomic, assign) CGFloat longWdith;
@end

@implementation JCBubbleBar

- (instancetype)initWithFrame:(CGRect)frame images:(NSArray *)images selectIndex:(void(^)(NSInteger index))select {
    self = [super initWithFrame:frame];
    if (self) {
        self.longWdith = frame.size.height;
        self.images = images;
        self.select = select;
        [self setUp];
    }
    return self;
}

- (void)setUp {
    [self addSubview:self.targetView];
    [self addSubview:self.tableView];
    [self hiddenListWith:0];
}

/** 隐藏列表 */
- (void)hiddenListWith:(NSInteger)index {
    self.tableView.hidden = YES;
    self.targetView.hidden = NO;
    UIImageView *imageView = (UIImageView *)[self.targetView viewWithTag:base_tag];
    if (imageView && [imageView isKindOfClass:[UIImageView class]]) {
        imageView.image = [self.images safeObjectAtIndex:index];
    }
    UILabel *label = (UILabel *)[self.targetView viewWithTag:base_tag+1];
    if (label && [label isKindOfClass:[UILabel class]]) {
        label.text = [NSString stringWithFormat:@"%ld/%ld",index+1,self.images.count];
    }
    self.height = self.targetView.height;
}

#pragma mark - UITableView Delegate
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView{
    return 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{
    return self.images.count;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    return 45;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    JCBubbleTableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:@"JCBubbleTableViewCell"];
    UIImage *image = [self.images safeObjectAtIndex:indexPath.row];
    if (image) {
        [cell setBubbleImage:image title:[NSString stringWithFormat:@"%ld",indexPath.row+1]];
    }
    return cell;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    [self hiddenListWith:indexPath.row];
    if (self.select) {
        self.select(indexPath.row);
    }
}

#pragma mark - gesture
- (void)chooseImage:(UIGestureRecognizer *)recognizer {
    self.tableView.hidden = NO;
    self.targetView.hidden = YES;
    self.height = self.longWdith;
}

#pragma mark - lazy
- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:self.bounds style:UITableViewStylePlain];
        _tableView.backgroundColor = XY_HEX_RGBA(0x000000, 0.3);
//        _tableView.sectionHeaderHeight =5;
//        _tableView.sectionFooterHeight = 5;
        [_tableView registerNib:[UINib nibWithNibName:@"JCBubbleTableViewCell" bundle:nil]   forCellReuseIdentifier:@"JCBubbleTableViewCell"];
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.layer.cornerRadius = 25;
        _tableView.delegate = self;
        _tableView.dataSource = self;
    }
    return _tableView;
}

- (UIView *)targetView {
    if (!_targetView) {
        _targetView = [[UIView alloc] initWithFrame:(CGRect){0,0,self.width,self.width}];
        _targetView.backgroundColor = XY_HEX_RGBA(0x000000, 0.3);
        _targetView.layer.cornerRadius = self.width/2;
        // image
        UIImageView *icon = [[UIImageView alloc] initWithFrame:(CGRect){11,5,28,25}];
        icon.contentMode = UIViewContentModeScaleAspectFit;
        icon.tag = base_tag;
        [_targetView addSubview:icon];
        // title
        UILabel *label = [[UILabel alloc] initWithFrame:CGRectMake(0, 34, 50, 12)];
        label.font = [UIFont systemFontOfSize:11];
        label.textColor = COLOR_WHITE;
        label.textAlignment = NSTextAlignmentCenter;
        label.tag = base_tag + 1;
        [_targetView addSubview:label];
        
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(chooseImage:)];
        [_targetView addGestureRecognizer:tap];
    }
    return _targetView;
}

@end
