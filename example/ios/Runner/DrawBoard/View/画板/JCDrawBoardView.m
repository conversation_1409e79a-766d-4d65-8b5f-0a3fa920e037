//
//  JCDrawBoardView.m
//  XYFrameWork
//
//  Created by j c on 2019/7/9.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCDrawBoardView.h"
#import "JCBaseScrollView.h"
#import "JCRulerView.h"
#import "JCElementMainView.h"
#import "JCCanvas.h"
#import "JCScaleResertView.h"
#import "JCElementDottedView.h"
#import "JCDefaultElementManager.h"
#import "JCElementEditManager.h"
#import "JCElementToItem.h"
#import "JCBoardConst.h"
#import "JCItemToElement.h"
#import "JCBubbleBar.h"
#import "JCDrawBoardView+Undo.h"
#import "JCIConObject.h"
//#import "JCFontManager.h"


//画板在手机中展示的宽度
#define JCtemplatePixelWidth  SCREEN_WIDTH_RULER- JCRuleWidth - JCRuleWidth

#define P_ADDRESS(f) [NSString stringWithFormat:@"%p",f]
#define Element(ClassName)       [JCDefaultElementManager defaultElementWithClass:[ClassName class]]
#define TemplateRuleMargin      0//(([self oneScaleDistanceForWidth]+JCScaleWidth)*1.0 >12 ?10.0: ([self oneScaleDistanceForWidth]+JCScaleWidth)*1.0)
#define TemplateOffsetMargin    (TemplateRuleMargin + JCRuleWidth)
@interface JCDrawBoardView ()<JCElementProtocol,UIGestureRecognizerDelegate>

@property (nonatomic,strong) JCBaseScrollView *bgScroll;//画板容器子视图,缩放
@property (nonatomic,strong) UIImageView *backImgV; //背景图片

@property (nonatomic, strong) JCRulerView *numberTopRulerView;                    //数字居上带选中刻度尺
@property (nonatomic, strong) JCRulerView *numberLeftRulerView;                 //数字居左

@property (nonatomic, strong) JCElementMainView *templateView;//画板中的模板


@property (nonatomic, strong) NSDictionary *tempDic;//服务器模版元素数据

@property (nonatomic, strong) JCElementBaseView *selectView; //当前选中编辑中的view

@property(nonatomic,strong) JCScaleResertView *resertView;

@property (nonatomic,strong) JCCanvas *canvas;//画板容器

@property (nonatomic, strong)  JCTemplateData *templateInfo;
@property (nonatomic, strong) JCElementDottedView *dottedView;
/** 保存所有元素的字典 */
@property (nonatomic, strong) NSMutableDictionary *elementViews;
/** 多选views */
@property (nonatomic, strong) NSMutableDictionary *mutableSelectViews;
@property (nonatomic, strong) JCBubbleBar *bubbleSelectBar;//标签样式浮动按钮
@property (nonatomic, assign) NSInteger backgroundImageIndex;
@property (nonatomic, assign) CGFloat lastWidth;

@property (nonatomic, copy) NSString *type;           //模板类型
@property (nonatomic, assign) BOOL hasChange;
// 当前正在调对齐属性
@property (nonatomic, assign) BOOL currentSetAligment;
@property (nonatomic, assign) NSInteger zIndex;
@property (nonatomic, assign) CGFloat mm2pxScale;
//进入画板的原始模型
@property(nonatomic,strong)JCTemplateData *originTemplateInfo;
@end

@implementation JCDrawBoardView

- (instancetype)initWithFrame:(CGRect)frame
                    withModel:(JCTemplateData *)model
                         type:(NSString *)type
                 defaultScale:(CGFloat)scale
                    boardMode:(JCBoardMode)boardMode {
    self = [super initWithFrame:frame];
    if (self) {
        self.boardMode = boardMode;
        self.type = type;
        [DrawBoardInfo updateInfoWith:model];
        self.templateInfo = model;
        self.originTemplateInfo = [model copy];
        [self prepareData];
        self.hasChange = NO;
        [self setUp];
        [self performSelector:@selector(resetScale:) withObject:@(scale) afterDelay:0];
        
    }
    return self;
}

- (void)resetScale:(NSNumber *)number {
    CGFloat scale = [number floatValue];
    if (scale > 0 && scale != 1) {
        [self.bgScroll setZoomScale:MAX(JCBoardMinScale, scale) animated:YES];
        CGFloat templateWidth = self.templateInfo.width * self.mm2pxScale;
        CGFloat templateCenterX = self.templateView.centerX;
        CGFloat halfTemplateWidth = templateWidth/2;
        CGFloat leftSpace = SCREEN_WIDTH/2 - templateWidth/2;
        CGFloat leftOffset = scale*(templateCenterX-halfTemplateWidth)-leftSpace;
        
        CGFloat templateHeight= self.templateInfo.height * self.mm2pxScale;
        CGFloat templateCenterY = self.templateView.centerY;
        CGFloat halfTemplateHeight = templateHeight/2;
        CGFloat topOffset = scale*(templateCenterY-halfTemplateHeight) - 70;//JCRuleWidth;
        

        self.bgScroll.contentOffset= CGPointMake(leftOffset, topOffset);
    }
}

- (void)prepareData {
    CGFloat maxPxWidth = SCREEN_WIDTH-45;
    CGFloat navibarHeight = iPhoneX?88:64;
    CGFloat bottomBarHeight = iPhoneX?75:56;
    CGFloat maxPxHeight = SCREEN_HEIGHT - 248 - bottomBarHeight - navibarHeight - 10 - JCRuleWidth;
    self.mm2pxScale = maxPxWidth/self.templateInfo.width;
    CGFloat needHeight = self.templateInfo.height * self.mm2pxScale;
    if (needHeight > maxPxHeight) {
        self.mm2pxScale = maxPxHeight/self.templateInfo.height;
    }
    DrawBoardInfo.mm2pxScale = self.mm2pxScale;
}

//刻度之间的距离
- (CGFloat)oneScaleDistanceForWidth
{
        
        CGFloat  templatePxWidth = self.templateInfo.width * self.mm2pxScale;
        CGFloat mmWidthCount = self.templateInfo.width;
        CGFloat distanceBetweenScale = (templatePxWidth - (mmWidthCount + 1)*JCScaleWidth)/mmWidthCount;
        return distanceBetweenScale;
}

- (CGFloat)oneScaleDistanceForHeight
{

        CGFloat  templatePxHeight = self.height - 15 - 10 - 248;
        CGFloat mmHeightCount = self.templateInfo.height;
        CGFloat distanceBetweenScale = (templatePxHeight - (mmHeightCount + 1)*JCScaleWidth)/mmHeightCount;
        return distanceBetweenScale;

}
// 1刻度本身的宽度+ 刻度之间的距离
- (CGFloat)oneScaleWidth
{
    CGFloat oneScale = [self oneScaleDistanceForWidth] + JCScaleWidth;
    return oneScale;
}

- (void)updateWithModel:(JCTemplateData *)newData withDeviceTemplate:(JCTemplateData *)deviceData {
    [self updateWithModel:newData];
//    if (deviceData) {
//        if (deviceData.width < self.lastWidth) {
//            [self.bgScroll setZoomScale:deviceData.width/self.lastWidth animated:YES];
////            self.lastWidth = 0;
//        }
//    } else {
//        self.lastWidth = newData.width;
//    }
}


// 更换模板
- (void)updateWithModel:(JCTemplateData *)newData  {
    [self recover];
    [DrawBoardInfo updateInfoWith:newData];
        self.templateInfo = newData;//不要把印同款赋值给当前模板
    [self prepareData];
    [self setUp];
    JCPerformSelector(self.context.baseController, sel(@"refreshTitleView"));
    [self tapBlank:nil];
}

-(void)setUp {
    XYWeakSelf
    //进入画板之后次比率不边,不然切换标签纸的时候会出现元素大小变化
  CGFloat oneScale = [self oneScaleWidth]; //每一个刻度的总宽度
    
    CGFloat defaultOffX = JCBoardWidthOffDistnceNum * oneScale;  //左右可滑动距离
    CGFloat defaultOffY = JCBoardHeightOffDistnceNum * oneScale;  //上下可滑动距离
   
    CGFloat heightSacle = JCRuleWidth;
    CGFloat contentsizeW = (self.templateInfo.width*oneScale + JCScaleWidth) + 2 *defaultOffX;
    CGFloat contentsizeH = (self.templateInfo.height*oneScale + JCScaleWidth) + 2 *defaultOffY;
    JCCanvas *bg = [[JCCanvas alloc] init];

    bg.operate = ^(CGFloat x, CGFloat y, CGFloat scale) {
        [weakSelf.dottedView hidden];
        NSMutableString *logStr = [NSMutableString stringWithFormat:@"----x = %f, -----y= %f",x,y];
        if (weakSelf.bgScroll.zooming) {
            [logStr appendString:[NSString stringWithFormat:@"---scale=%f",scale]];
            CGFloat contentWidth = weakSelf.bgScroll.contentSize.width;
            CGFloat contentHeight = weakSelf.bgScroll.contentSize.height;
            CGFloat originTopDistanceBetweenScale = (contentWidth - (weakSelf.templateInfo.width + 2*JCBoardWidthOffDistnceNum+1)*JCScaleWidth)/(weakSelf.templateInfo.width + 2*JCBoardWidthOffDistnceNum);
            CGFloat originLeftDistanceBetweenScale = (contentHeight - (weakSelf.templateInfo.height + 2*JCBoardHeightOffDistnceNum+1)*JCScaleWidth)/(weakSelf.templateInfo.height + 2*JCBoardHeightOffDistnceNum);
            weakSelf.numberTopRulerView.distanceBetweenScale = originTopDistanceBetweenScale;
            weakSelf.numberLeftRulerView.distanceBetweenScale = originLeftDistanceBetweenScale;
            weakSelf.scaleBlock(scale);
        }
        CGFloat tempRuleHeight = TemplateOffsetMargin*scale;
        
        if (x -tempRuleHeight >= 0) {
            weakSelf.numberTopRulerView.rulerCollectionView.contentOffset = CGPointMake(x - tempRuleHeight , 0);
            
        }else{
            weakSelf.numberTopRulerView.rulerCollectionView.contentOffset = CGPointMake(0 , 0);
        }
        if (y - tempRuleHeight >=0) {
            weakSelf.numberLeftRulerView.rulerCollectionView.contentOffset = CGPointMake(0, y - tempRuleHeight);
        }else{
            weakSelf.numberLeftRulerView.rulerCollectionView.contentOffset = CGPointMake(0, 0);
        }
    };
    

     self.canvas = bg;
     self.bgScroll.delegate = self.canvas;
    
    [self addSubview:self.bgScroll];
    [self addSubview:self.numberTopRulerView];
    [self addSubview:self.numberLeftRulerView];
    [self.bgScroll addSubview:bg];
    [bg addSubview:self.templateView];
    
    [self.templateView addSubview:self.backImgV];
    
    [self.numberTopRulerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(weakSelf);
        make.top.equalTo(weakSelf);
        make.height.mas_equalTo(heightSacle);
        make.trailing.equalTo(weakSelf);
    }];
    
    [self.numberLeftRulerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(weakSelf);
        make.leading.equalTo(weakSelf);
        make.width.mas_equalTo(heightSacle);
        make.height.equalTo(weakSelf);
    }];
    
    [self.bgScroll mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.top.equalTo(weakSelf);
    }];
    
    [bg mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(weakSelf.bgScroll);
        make.width.equalTo(@(contentsizeW));
        make.height.equalTo(@(contentsizeH));
    }];
    

    CGFloat templateHeight = self.templateInfo.height* oneScale + JCScaleWidth;
    
    [self.templateView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(bg.mas_leading).offset(defaultOffX +TemplateOffsetMargin);
        make.height.equalTo(@( templateHeight));
        make.width.mas_equalTo(self.templateInfo.width* oneScale +JCScaleWidth);
        make.top.equalTo(bg).offset(defaultOffY + TemplateOffsetMargin);
    }];
    
    [self.backImgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(weakSelf.templateView);
    }];
    [self layoutIfNeeded];
    

    // 添加气泡选择条
    [self addSubview:self.bubbleSelectBar];
    self.bubbleSelectBar.hidden = YES;
    
    [self setTemplateBackgroundImageView];
    
    /** 保存当前画板的宽高信息  */
    [JCDrawInfoManager sharedManager].boardWidth = [self drawBoardWidth];
    [JCDrawInfoManager sharedManager].boardHeight = [self drawBoardHieght];
    
    // 取消选中手势
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(tapBlank:)];
    tap.delegate = self;
    [self addGestureRecognizer:tap];
    
    self.numberTopRulerView.rulerCollectionView.contentOffset =CGPointMake((defaultOffX -TemplateOffsetMargin +JCScaleWidth), 0);
    self.numberLeftRulerView.rulerCollectionView.contentOffset = CGPointMake(0,(defaultOffY -TemplateOffsetMargin +JCScaleWidth));

    NSArray *tempElements = [self.templateInfo.elements sortedArrayUsingComparator:^NSComparisonResult(JCElementModel  *obj1, JCElementModel *obj2) {
        return obj1.zIndex > obj2.zIndex;
    }];
    // elements中元素预加载
    [tempElements enumerateObjectsUsingBlock:^(JCElementModel * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        JCElementBaseView *view = [JCItemToElement elementWithItem:obj];
        if (view) {
            view.selected = NO;
            view.zIndex = idx;self.zIndex = idx;
            if ([view isKindOfClass:[JCElementLogoView class]]) {
                JCElementLogoView *iconView = (JCElementLogoView *)view;
                if (iconView.allowFreeZoom) {
                    [self.templateView insertSubview:iconView aboveSubview:self.backImgV];
                } else {
                    [self.templateView addSubview:view];
                }
            } else {
                [self.templateView addSubview:view];
            }
            
            view.delegate = self;
            [self.elementViews setObject:view forKey:P_ADDRESS(view)];
        }
    }];
    
    if (tempElements.count == 0) {
        [self addDefaultTextElement];
    }
    
    [self recoverScale];
    NSLog(@"%f=====%f",self.templateInfo.width, self.originTemplateInfo.width);
    CGFloat ratio = self.templateInfo.width / self.originTemplateInfo.width;
    if (ratio < 0.3) {
        ratio = 0.3;
    }else if(ratio < 1){
        
    }else{
        ratio = 1;
    }
    if (self.templateInfo.width / self.originTemplateInfo.width < 1) {
        
        [self scaleToFitWithScale:ratio];
    }
//    [self.bgScroll setZoomScale:ratio];
    
//    [self.bgScroll zoomToRect:CGRectMake(defaultOffX +TemplateOffsetMargin, defaultOffY + TemplateOffsetMargin, (self.templateInfo.width* oneScale +JCScaleWidth) / ratio , templateHeight / ratio) animated:YES];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        JCPerformSelector(self.context.baseController, sel(@"updateOrignalData"))
    });
}
//适配缩放
- (void)scaleToFitWithScale:(CGFloat)ratio {

    CGFloat templateWidth = self.templateInfo.width * self.mm2pxScale;
    CGFloat templateCenterX = self.templateView.centerX ;
    CGFloat halfTemplateWidth = templateWidth/2  ;
    CGFloat leftSpace = (SCREEN_WIDTH/2 - templateWidth/2) ;
    CGFloat leftOffset = templateCenterX-halfTemplateWidth-leftSpace;
    CGFloat templateHeight= self.templateInfo.height * self.mm2pxScale ;
    CGFloat templateCenterY = self.templateView.centerY ;
    CGFloat halfTemplateHeight = templateHeight/2  ;
    CGFloat topOffset = templateCenterY-halfTemplateHeight - 70;//JCRuleWidth;
    
    
    CGFloat distanceBetweenScale = [self oneScaleDistanceForWidth]  * ratio;
    CGFloat oneScale = distanceBetweenScale + JCScaleWidth; //每一个刻度的总宽度
    CGFloat defaultOffY =  JCBoardHeightOffDistnceNum* oneScale * ratio;  //上下偏移距离
    self.bgScroll.zoomScale = ratio;

    self.resertView.scale = ratio;
    if (self.scaleBlock) {
        self.scaleBlock(ratio);
    }
    
    self.bgScroll.contentOffset= CGPointMake(leftOffset * ratio, topOffset * ratio - 35);
    NSLog(@"=====%@",NSStringFromCGPoint(self.bgScroll.contentOffset));
    self.numberTopRulerView.distanceBetweenScale = distanceBetweenScale;
    self.numberLeftRulerView.distanceBetweenScale = distanceBetweenScale;
    self.numberTopRulerView.rulerCollectionView.contentOffset =CGPointMake(leftOffset-JCRuleWidth, 0);
    self.numberLeftRulerView.rulerCollectionView.contentOffset = CGPointMake(0,(defaultOffY -TemplateOffsetMargin +JCScaleWidth));
}
//比例复原
- (void)recoverScale{
    CGFloat templateWidth = self.templateInfo.width * self.mm2pxScale;
    CGRect templateFrame = self.templateView.frame;
    CGFloat templateCenterX = self.templateView.centerX;
    CGFloat halfTemplateWidth = templateWidth/2;
    CGFloat leftSpace = SCREEN_WIDTH/2 - templateWidth/2;
    CGFloat leftOffset = templateCenterX-halfTemplateWidth-leftSpace;
    CGFloat templateHeight= self.templateInfo.height * self.mm2pxScale;
    CGFloat templateCenterY = self.templateView.centerY;
    CGFloat halfTemplateHeight = templateHeight/2;
    CGFloat topOffset = templateCenterY-halfTemplateHeight - 70;//JCRuleWidth;
    
    
    CGFloat distanceBetweenScale = [self oneScaleDistanceForWidth];
    CGFloat oneScale = distanceBetweenScale + JCScaleWidth; //每一个刻度的总宽度
    CGFloat defaultOffX = JCBoardWidthOffDistnceNum * oneScale;  //左右偏移距离
    CGFloat defaultOffY =  JCBoardHeightOffDistnceNum* oneScale;  //上下偏移距离
    self.bgScroll.zoomScale = 1;
    self.resertView.scale = 1;
    if (self.scaleBlock) {
        self.scaleBlock(1);
    }
    
    self.bgScroll.contentOffset= CGPointMake(leftOffset, topOffset);
    NSLog(@"=====%@",NSStringFromCGPoint(self.bgScroll.contentOffset));
    self.numberTopRulerView.distanceBetweenScale = distanceBetweenScale;
    self.numberLeftRulerView.distanceBetweenScale = distanceBetweenScale;
    self.numberTopRulerView.rulerCollectionView.contentOffset =CGPointMake(leftOffset-JCRuleWidth, 0);
    self.numberLeftRulerView.rulerCollectionView.contentOffset = CGPointMake(0,(defaultOffY -TemplateOffsetMargin +JCScaleWidth));
}


- (void)showPrintSamePreview:(JCTemplateData *)tempData {
    
   CGFloat oneScale = [self oneScaleWidth];
    CGFloat templateHeight;
    CGFloat templateWidth;

        templateHeight  = tempData.height* oneScale + JCScaleWidth;
        templateWidth = tempData.width* oneScale +JCScaleWidth;
    
    NSLog(@">>>>>>>>height:%f=======width:%f========scale:%f====tem width:%f====tem height:%f",templateHeight,self.templateInfo.width* oneScale +JCScaleWidth, oneScale, self.templateInfo.width, self.templateInfo.height);
    [self.templateView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(@(templateHeight));
        make.width.mas_equalTo(@(templateWidth));
    }];
    [self layoutIfNeeded];
    // 添加气泡选择条
    [self.bubbleSelectBar removeFromSuperview];
    self.bubbleSelectBar = nil;
   
    NSArray *imageArr = [self getPrintSameBackGroundImagesWithData:tempData];
    CGFloat height = MIN(imageArr.count, 6)*45+10;
    XYWeakSelf;
    _bubbleSelectBar = [[JCBubbleBar alloc] initWithFrame:(CGRect){SCREEN_WIDTH - 62, 55, 50, height} images:imageArr selectIndex:^(NSInteger index) {
        weakSelf.backgroundImageIndex = index;
        tempData.mutableBackCurrentIndex = index;
        [self setPreviewBackgroundViewWithModel:tempData];
    }];
    
    [self addSubview:self.bubbleSelectBar];
    self.bubbleSelectBar.hidden = YES;
    
    [self setPreviewBackgroundViewWithModel:tempData];
    
}
- (void)resetByPreview {
    CGFloat oneScale = [self oneScaleWidth];
    CGFloat templateHeight;
    CGFloat templateWidth;

        templateHeight  = self.templateInfo.height* oneScale + JCScaleWidth;
        templateWidth = self.templateInfo.width* oneScale +JCScaleWidth;

    [self.templateView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(@(templateHeight));
        make.width.mas_equalTo(@(templateWidth));
    }];
    [self layoutIfNeeded];
    // 添加气泡选择条
    [self.bubbleSelectBar removeFromSuperview];
    self.bubbleSelectBar = nil;
    [self addSubview:self.bubbleSelectBar];
    self.bubbleSelectBar.hidden = YES;
    
    [self setTemplateBackgroundImageView];
}
- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer shouldReceiveTouch:(UITouch *)touch {
    if ([touch.view isKindOfClass:[JCElementMainView class]] || [touch.view isKindOfClass:[JCCanvas class]]) {
        return YES;
    }
    return NO;
}

- (NSArray *)getPrintSameBackGroundImagesWithData:(JCTemplateData *)tempData {

        NSMutableArray *images = [NSMutableArray array];
        BOOL is_cloud = [tempData.profile.extrain.templateType isEqualToString:@"1"];
    //    NSString *imageId = is_cloud?self.templateInfo.profile.extrain.sourceId:self.templateInfo.idStr;
        NSArray *bubbleArr = [tempData.backgroundImage componentsSeparatedByString:@","];
        if (bubbleArr.count > 1) {
            for (NSInteger i = 0; i < bubbleArr.count; i ++) {
                NSString *url = [bubbleArr safeObjectAtIndex:i];
                NSString *imagePath = ImageLocalPath(url);
                UIImage *image = [UIImage imageWithData:[NSData dataWithContentsOfFile:imagePath]];
                if (image) [images addObject:image];
            }
        } else {
            NSString *imagePath = ImageLocalPath(UN_NIL(bubbleArr.firstObject));
            UIImage *image = [UIImage imageWithData:[NSData dataWithContentsOfFile:imagePath]];
            if (image) [images addObject:image];
        }
        return images;
    
}


- (NSArray *)getBackGroundImages {
        NSMutableArray *images = [NSMutableArray array];
        BOOL is_cloud = [self.templateInfo.profile.extrain.templateType isEqualToString:@"1"];
    //    NSString *imageId = is_cloud?self.templateInfo.profile.extrain.sourceId:self.templateInfo.idStr;
        NSArray *bubbleArr = [self.templateInfo.backgroundImage componentsSeparatedByString:@","];
        if (bubbleArr.count > 1) {
            for (NSInteger i = 0; i < bubbleArr.count; i ++) {
                NSString *url = [bubbleArr safeObjectAtIndex:i];
                NSString *imagePath = ImageLocalPath(url);
                UIImage *image = [UIImage imageWithData:[NSData dataWithContentsOfFile:imagePath]];
                if (image) [images addObject:image];
            }
        } else {
            NSString *imagePath = ImageLocalPath(UN_NIL(bubbleArr.firstObject));
            UIImage *image = [UIImage imageWithData:[NSData dataWithContentsOfFile:imagePath]];
            if (image) [images addObject:image];
        }
        return images;

}

- (void)setPreviewBackgroundViewWithModel:(JCTemplateData *)tempData {
    if (!STR_IS_NIL(tempData.backgroundImage)){
        NSArray *images = [self getPrintSameBackGroundImagesWithData:tempData];
        if (images.count > 1) {// 多图
            self.bubbleSelectBar.hidden = NO;
            UIImage *currentImage = [images safeObjectAtIndex:self.backgroundImageIndex];
            self.backImgV.image = currentImage?currentImage:XY_IMAGE_NAMED(@"主图_占位");
        } else {
            self.bubbleSelectBar.hidden = YES;
            self.backImgV.image = images.firstObject?images.firstObject:XY_IMAGE_NAMED(@"主图_占位");
        }
    } else{
        self.backImgV.image = nil;
        self.backImgV.backgroundColor = COLOR_WHITE;
        self.backImgV.layer.borderColor = XY_HEX_RGBA(0xcccccc, 0.8).CGColor;
        self.backImgV.layer.borderWidth = 1;
        self.backImgV.layer.cornerRadius = self.boardMode == JCBoardModeAntiLost ? 30 : 12;
    }
}
//设置背景图
- (void)setTemplateBackgroundImageView{
    JCTemplateData *temD;
        temD = self.templateInfo;
    if (!STR_IS_NIL(temD.backgroundImage)){
        NSArray *images = [self getBackGroundImages];
        if (images.count > 1) {// 多图
            self.bubbleSelectBar.hidden = NO;
            UIImage *currentImage = [images safeObjectAtIndex:self.backgroundImageIndex];
            self.backImgV.image = currentImage?currentImage:XY_IMAGE_NAMED(@"主图_占位");
        } else {
            self.bubbleSelectBar.hidden = YES;
            self.backImgV.image = images.firstObject?images.firstObject:XY_IMAGE_NAMED(@"主图_占位");
        }
    }else{
        self.backImgV.image = nil;
        self.backImgV.backgroundColor = COLOR_WHITE;
        self.backImgV.layer.borderColor = XY_HEX_RGBA(0xcccccc, 0.8).CGColor;
        self.backImgV.layer.borderWidth = 1;
        self.backImgV.layer.cornerRadius = self.boardMode == JCBoardModeAntiLost ? 30 : 12;
    }
}

- (void)tapBlank:(UITapGestureRecognizer *)recognizer {
    [self allElementsCancelSelectedState];
    [self refreshToBarLockState];
    JCPerformSelector(self.context.baseController, sel(@"cancenMutableSelect"));
}

// 所有元素取消选中
- (void)allElementsCancelSelectedState {
    if(self.isMutableSelect){// 多选状态点击空白区域，取消所有选中状态
        [self.mutableSelectViews enumerateKeysAndObjectsUsingBlock:^(id  _Nonnull key, JCElementBaseView *element, BOOL * _Nonnull stop) {
            element.selected = NO;
        }];
        [self.mutableSelectViews removeAllObjects];
    }else{// 单选状态下点击空白区域，取消当前元素的选中状态
        if(self.selectView){
            self.selectView.selected = NO;
            self.selectView = nil;
        }
    }
    [self hiddenProperty];
    [self changeTopbarItems];
    // 隐藏刻度尺
    [self currentSetAligment:@"0"];
}

- (BOOL)templateHasChange {
    return NO;
}

- (void)setSelectView:(JCElementBaseView *)selectView {
    _selectView = selectView;
    /** 设置顶部操作区域样式 */
    [self changeTopbarItems];
}

//画板宽度
-(CGFloat)drawBoardWidth{
    return self.templateView.frame.size.width;
}

-(CGFloat)drawBoardHieght{
    return self.templateView.frame.size.height;
}


-(void)layoutSubviews{
    [super layoutSubviews];
    [self.bgScroll mas_updateConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self);
    }];
}

- (void)refreshAllElements {
    [self.elementViews enumerateKeysAndObjectsUsingBlock:^(id  _Nonnull key, JCElementBaseView  *obj, BOOL * _Nonnull stop) {
        obj.excelIndex = DrawBoardInfo.currentPageIndex;
    }];
}

- (void)refreshSerialNumberElement {
    [self.elementViews enumerateKeysAndObjectsUsingBlock:^(id  _Nonnull key, JCElementBaseView  *obj, BOOL * _Nonnull stop) {
        if ([obj isKindOfClass:[JCElementSerialNumber class]]) {
            obj.excelIndex = DrawBoardInfo.currentPageIndex;
        }
    }];
}

- (void)checkMutableElements {
    if (!self.isMutableSelect)return;
    if (self.mutableSelectViews.count > 0) {
        [self currentSetAligment:@"1"];
    } else {
        [self currentSetAligment:@"0"];
    }
}

/** 清空之前导入的不同excel数据 */
- (void)clearPriorExcelElementExcept:(JCElementBaseView *)remainElement {
    // 替换excel 页数清0
    DrawBoardInfo.currentPageIndex = 0;
    [self.elementViews enumerateKeysAndObjectsUsingBlock:^(id  _Nonnull key, JCElementBaseView *element, BOOL * _Nonnull stop) {
        if (IsExcelString(element.excelValue) && ![element.elementId isEqualToString:remainElement.elementId]) {
            // 移除
            [self.elementViews removeObjectForKey:P_ADDRESS(element)];
            [self.mutableSelectViews removeObjectForKey:P_ADDRESS(element)];
            [element removeFromSuperview];
            [self hiddenGuideLine];
        }
    }];
}

-(void)doSaoMiaoActionGetResult:(NSString *)result withTagType:(NSInteger)type barCodeType:(NSString *)codeTypeStr {
    if (type == 1) {  //生成文本
        [self doCreateLabelWithString:result];
    } else if (type == 2) {   //生成条码
        NSArray *componetArr = [codeTypeStr componentsSeparatedByString:@"."];
        NSString *codeType = componetArr.lastObject;
        __block NSInteger cType = JCCodeType_CODE128;
        [bar_code_types enumerateKeysAndObjectsUsingBlock:^(NSString *codeNumber, NSString *value, BOOL * _Nonnull stop) {
            if ([value isEqualToString:codeType]) {
                cType = codeNumber.integerValue;
                *stop = YES;
            }
        }];
        [self doCreate1DBarWithString:result withBarCodeType:cType];
    } else if (type == 3) {   //生成二维码
        [self doCreate2DBarWithString:result];
    }
}

#pragma mark 通过实例创建文本 一维码 二维码 表格
- (void)doCreateLabelWithString:(NSString *)string
{
    JCElementTextImage *temp = [JCElementTextImage new];
    temp.frame = (CGRect){10,10,20*DrawBoardInfo.mm2pxScale,24};
    temp.placeHolder = XY_LANGUAGE_TITLE_NAMED(@"",element_text_placeholder);
    temp.text = string;
    temp.configure = [JCElementTextConfigure defaultConfigure];
    temp.elementId = [XYTool randomElementId];
    [self addElement:temp];
}

- (void)doCreate1DBarWithString:(NSString *)string withBarCodeType:(NSInteger)barcodeType
{
    JCElementBarCode *temp = [JCElementBarCode new];
    temp.frame = (CGRect){10,10,20*DrawBoardInfo.mm2pxScale,10*DrawBoardInfo.mm2pxScale};
    JCElementBarCodeConfigure *configure = [JCElementBarCodeConfigure defaultConfigure];
    configure.codeType = barcodeType;
    temp.configure = configure;
    temp.text = string;
    temp.elementId = [XYTool randomElementId];
    [self addElement:temp];
}

- (void)doCreate2DBarWithString:(NSString *)string
{
    JCElementQRCode *temp = [JCElementQRCode new];
    temp.frame = (CGRect){10,10,10*DrawBoardInfo.mm2pxScale,10*DrawBoardInfo.mm2pxScale};
    temp.qrCodeString = string;
    temp.codeType = JCCodeType_QRCODE;
    temp.elementId = [XYTool randomElementId];
    [self addElement:temp];
}

- (void)addElement:(JCElementBaseView *)element {
    JCPerformSelector(self.context.baseController, sel(@"cancenMutableSelect"));
    [self addElement:element recordUndo:YES];
    JCPerformSelectorWithOneObject(self.context.baseController, sel(@"firstAddElement:"), element)
}

- (void)addElements:(NSArray *)elements {
    [elements enumerateObjectsUsingBlock:^(JCElementBaseView *obj, NSUInteger idx, BOOL * _Nonnull stop) {
        [self addElement:obj recordUndo:NO];
    }];
}

// 添加元素，是否记录撤销恢复 （撤销恢复中的添加、删除不必再重复记录撤销恢复）
- (void)addElement:(JCElementBaseView *)element recordUndo:(BOOL)record {
    if (record) {
        element.zIndex = self.zIndex;self.zIndex++;
        element.delegate = self;
        [self.elementViews setObject:element forKey:P_ADDRESS(element)];
        [self.templateView addSubview:element];
        [self sigleElementViewSelected:element];
        /** 撤销恢复操作预加载 */
        [self undo_addElement:element];
    } else {
        element.delegate = self;
        [self.elementViews setObject:element forKey:P_ADDRESS(element)];
        [self.templateView addSubview:element];
        if (element.selected) {
            [self sigleElementViewSelected:element];
        }
    }
    [self mutableStateAddElement:element];
    // 更新excel导入状态
    JCPerformSelector(self.context.baseController, sel(@"refreshTitleState"));
}

#pragma mark JCElementProtocol
- (void)elementViewTouchBegin:(JCElementBaseView *)element{
    [self sigleElementViewSelected:element];
    [self beginUndo:@{k_element:element,k_action:k_frame,k_frame:NSStringFromCGRect(element.frame)}];
}

- (void)elementViewDragBegin:(JCElementBaseView *)element {
    [self beginUndo:@{k_element:element,k_action:k_frame,k_frame:NSStringFromCGRect(element.frame)}];
}

- (void)elementViewDoubleClick:(JCElementBaseView *)element{
    if (self.isMutableSelect) return;
//    [self showKeyboardWith:element];
//    JCPerformSelector(self.context.baseController, sel(@"hiddenCommonKeyBoard"))
    NSDictionary *info = @{JCKEY_Element:element,JCKEY_Mutable:@(self.isMutableSelect),JCKEY_MutableElements:self.mutableSelectViews.allValues};
    JCPerformSelectorWithOneObject(self.context.baseController, sel(@"doubleClick:"),info)
}

- (void)elementViewFrameShouldChange:(JCElementBaseView *)element{
    if (element.selected) [self showGuideLine];
    [self bringSubviewToFront:self.dottedView];
    NSArray *elementsArr = self.isMutableSelect? self.mutableSelectViews.allValues:@[element];
    [self.dottedView setDottedLineConstraintWith:elementsArr fromView:self.templateView toView:self];
}
- (void)elementViewFrameDidChange:(JCElementBaseView *)element{
    [self hiddenGuideLine];
    [self endUndo:@{k_element:element,k_action:k_frame,k_frame:NSStringFromCGRect(element.frame)}];
    // 刷新元素
    [self refreshElementPropertyView];
}
- (void)elementViewDidClick:(JCElementBaseView *)element{
    [self hiddenGuideLine];
    [self mutableSelectElementDidClicked:element];
}

// 刷新属性编辑区域
- (void)refreshElementPropertyView {
    if (self.isMutableSelect) return;
    JCPerformSelector(self.context.propertyMainView, sel(@"refreshElementInfo"));
}

#pragma mark --
/** 只处理单选元素 */
- (void)sigleElementViewSelected:(JCElementBaseView *)element {
    if ([self.selectView isEqual:element] || self.isMutableSelect)return;
    /** 取消其他选中的元素 */
    [self.elementViews enumerateKeysAndObjectsUsingBlock:^(NSString *key, JCElementBaseView *obj, BOOL * _Nonnull stop) {
        if (![obj isEqual:element]) obj.selected = NO;
    }];
    /** 选中当前选择的元素 */
    self.selectView = element;
    self.selectView.selected = YES;
    if (element) {
//        JCPerformSelector(self.context.baseController, sel(@"hiddenCommonKeyBoard"))
        NSDictionary *info = @{JCKEY_Element:element,JCKEY_Mutable:@(self.isMutableSelect),JCKEY_MutableElements:self.mutableSelectViews.allValues};
        JCPerformSelectorWithOneObject(self.context.baseController, sel(@"sigleClick:"),info)
        // 锁定状态刷新
        [self refreshToBarLockState];
    }
}

- (void)mutableStateAddElement:(JCElementBaseView *)element {
    if (!self.isMutableSelect) return;
    [self.mutableSelectViews setObject:element forKey:P_ADDRESS(element)];
    element.selected = YES;
    if (element) {
        NSDictionary *info = @{JCKEY_Element:element,JCKEY_Mutable:@(self.isMutableSelect),JCKEY_MutableElements:self.mutableSelectViews.allValues};
        JCPerformSelectorWithOneObject(self.context.baseController, sel(@"mutibleSelectElementsInfo:"),info)
        JCPerformSelector(self.context.baseController, sel(@"hiddenCommonKeyBoard"))
        // 锁定状态刷新
        [self refreshToBarLockState];
    }
}

- (void)showKeyboardWith:(JCElementBaseView *)element {
    if (self.isMutableSelect) return;
    JCPerformSelectorWithOneObject(self.context.baseController, sel(@"showCommonKeyBoard:"), element)
}

- (void)hiddenProperty {
    BOOL mutableShowProperty = self.isMutableSelect && self.mutableSelectViews.count > 0;
    if (!mutableShowProperty) {
        JCPerformSelector(self.context.baseController, sel(@"hiddenProperty"))
    }
    JCPerformSelector(self.context.baseController, sel(@"hiddenCommonKeyBoard"))
}

- (BOOL)hasSelectedElement {
    __block BOOL value = NO;
    [self.elementViews enumerateKeysAndObjectsUsingBlock:^(id  _Nonnull key, JCElementBaseView *obj, BOOL * _Nonnull stop) {
        if (obj.selected) {
            value = YES;
            *stop = YES;
        }
    }];
    return value;
}

- (void)showGuideLine {
//    self.numberLeftRulerView.hidden = NO;
//    self.numberTopRulerView.hidden = NO;
    [self.dottedView show];
}

- (void)hiddenGuideLine {
    [self.dottedView hidden];
    if (!self.currentSetAligment){
//        self.numberLeftRulerView.hidden = YES;
//        self.numberTopRulerView.hidden = YES;
    }
}

- (void)currentSetAligment:(NSString *)value {
    self.currentSetAligment = [value isEqualToString:@"1"];
    if (self.currentSetAligment) {
//        self.numberLeftRulerView.hidden = NO;
//        self.numberTopRulerView.hidden = NO;
    } else {
//        self.numberLeftRulerView.hidden = YES;
//        self.numberTopRulerView.hidden = YES;
    }
}

// 多选对齐方式
- (void)mutableElementsAlign:(NSString *)position {
    if (!self.isMutableSelect || self.mutableSelectViews.count == 0) return;
    __block BOOL hasLockElement = NO;
    // 包含锁定的元素，不允许对齐
    JCElementBaseView *lockView = [self.mutableSelectViews.allValues find:^BOOL(JCElementBaseView *obj) {
        return obj.isLock == YES;
    }];
    if (lockView) {
        JCPerformSelector(self.context.baseController, sel(@"alignNotAllow")) return;
    }
    __block CGFloat left = 10000,top = 10000,bottom = -10000,right = -10000;
    [self.mutableSelectViews.allValues enumerateObjectsUsingBlock:^(UIView  *obj, NSUInteger idx, BOOL * _Nonnull stop) {
        left = MIN(obj.left, left);
        top = MIN(obj.top, top);
        bottom = MAX(obj.bottom, bottom);
        right = MAX(obj.right, right);
    }];
    // 如果只有一个元素那么以画板为基准对齐
    if (self.mutableSelectViews.count == 1) {
        left = 0;
        top = 0;
        bottom = DrawBoardInfo.boardHeight;
        right = DrawBoardInfo.boardWidth;
    }
    if ([position isEqualToString:@"top"]) {// 最高的元素为基准
        [self.mutableSelectViews.allValues enumerateObjectsUsingBlock:^(UIView  *obj, NSUInteger idx, BOOL * _Nonnull stop) {
            obj.top = top;
        }];
    } else if ([position isEqualToString:@"bottom"]) {// 最低的元素为基准
        [self.mutableSelectViews.allValues enumerateObjectsUsingBlock:^(UIView  *obj, NSUInteger idx, BOOL * _Nonnull stop) {
            obj.bottom = bottom;
        }];
    } else if ([position isEqualToString:@"left"]) {// 最左侧的元素为基准
        [self.mutableSelectViews.allValues enumerateObjectsUsingBlock:^(UIView  *obj, NSUInteger idx, BOOL * _Nonnull stop) {
            obj.left = left;
        }];
    } else if ([position isEqualToString:@"right"]) {// 最右侧的元素为基准
        [self.mutableSelectViews.allValues enumerateObjectsUsingBlock:^(UIView  *obj, NSUInteger idx, BOOL * _Nonnull stop) {
            obj.right = right;
        }];
    } else if ([position isEqualToString:@"vertical_center"]) {// 竖直居中
        CGFloat centerY = (bottom-top)/2+top;
        [self.mutableSelectViews.allValues enumerateObjectsUsingBlock:^(UIView  *obj, NSUInteger idx, BOOL * _Nonnull stop) {
            obj.centerY = centerY;
        }];
    } else if ([position isEqualToString:@"horizontal_center"]) {// 横向居中
        CGFloat centerX = (right - left)/2+left;
        [self.mutableSelectViews.allValues enumerateObjectsUsingBlock:^(UIView  *obj, NSUInteger idx, BOOL * _Nonnull stop) {
            obj.centerX = centerX;
        }];
    } else if ([position isEqualToString:@"horizontal_equidistant"]) {// 横向等间距
        if (self.mutableSelectViews.count < 3) return;
        NSArray *centerXArray = [self.mutableSelectViews.allValues sortedArrayUsingComparator:^NSComparisonResult(JCElementBaseView  *obj1, JCElementBaseView *obj2) {
            return (obj1.centerX > obj2.centerX)?NSOrderedDescending:NSOrderedAscending;
        }];
        NSInteger count = centerXArray.count;
        UIView *leftView = centerXArray.firstObject;
        UIView *rightView = centerXArray.lastObject;
        CGFloat equalSpace = (rightView.centerX-leftView.centerX)/(count-1);
        for (NSInteger i = 0; i < centerXArray.count; i ++) {
            UIView *view = centerXArray[i];
            view.centerX = leftView.centerX+i*equalSpace;
        }
    } else if ([position isEqualToString:@"vertical_equidistant"]) {// 竖直等间距
        if (self.mutableSelectViews.count < 3) return;
        NSArray *centerYArray = [self.mutableSelectViews.allValues sortedArrayUsingComparator:^NSComparisonResult(JCElementBaseView  *obj1, JCElementBaseView *obj2) {
            return (obj1.centerY > obj2.centerY)?NSOrderedDescending:NSOrderedAscending;
        }];
        NSInteger count = centerYArray.count;
        UIView *topView = centerYArray.firstObject;
        UIView *bottomView = centerYArray.lastObject;
        CGFloat equalSpace = (bottomView.centerY-topView.centerY)/(count-1);
        for (NSInteger i = 0; i < centerYArray.count; i ++) {
            UIView *view = centerYArray[i];
            view.centerY = topView.centerY+i*equalSpace;
        }
    }
}

/** 只处理多选功能 */
- (void)mutableSelectElementDidClicked:(JCElementBaseView *)element {
    if (!self.isMutableSelect)return;
    element.selected = !element.selected;
    if (element.selected) {
        [self.mutableSelectViews setObject:element forKey:P_ADDRESS(element)];
    } else {
        [self.mutableSelectViews removeObjectForKey:P_ADDRESS(element)];
    }
    [self checkMutableElements];
    if (self.mutableSelectViews.count > 0) {
        NSDictionary *info = @{JCKEY_Element:element,JCKEY_Mutable:@(self.isMutableSelect),JCKEY_MutableElements:self.mutableSelectViews.allValues};
        JCPerformSelectorWithOneObject(self.context.baseController, sel(@"mutibleSelectElementsInfo:"), info)
        JCPerformSelector(self.context.baseController, sel(@"hiddenCommonKeyBoard"))
    } else {
        [self hiddenProperty];
    }
    [self changeTopbarItems];
    [self refreshToBarLockState];
}

// 设置顶部公共操作条
- (void)changeTopbarItems {
    NSInteger selectElementCount = 0;
    if (self.selectView) selectElementCount ++;
    selectElementCount += self.mutableSelectViews.count;
    JCPerformSelectorWithOneObject(self.context.propertyMainView, sel(@"changeTopBarItemsWith:"), ([NSString stringWithFormat:@"%ld",selectElementCount]));
}

- (void)refreshToBarLockState {
    // lock
    if (self.isMutableSelect) {
        if (self.mutableSelectViews.count == 0) {
            
        } else {
            __block BOOL currentLock = YES;
            [self.mutableSelectViews enumerateKeysAndObjectsUsingBlock:^(id  _Nonnull key, JCElementBaseView *obj, BOOL * _Nonnull stop) {
                if (!obj.isLock) {
                    currentLock = NO;
                }
            }];
            JCPerformSelectorWithOneObject(self.context.baseController, sel(@"elementIsLock:"), currentLock?@"1":@"0");
        }
    } else {
        JCPerformSelectorWithOneObject(self.context.baseController, sel(@"elementIsLock:"), self.selectView.isLock?@"1":@"0");
    }
    
    BOOL elementHasSelected = [self hasSelectedElement];
    JCPerformSelectorWithOneObject(self.context.baseController, sel(@"elementHasSelected:"), elementHasSelected?@"1":@"0");
}

#pragma mark 操作区
- (void)addDefaultTextElement {
    JCElementTextImage *textElement = (JCElementTextImage *)Element(JCElementTextImage);
    [self addElement:textElement];
    textElement.centerX = self.templateView.width/2;
    textElement.centerY = self.templateView.height/2;
    textElement.selected = NO;
    [self allElementsCancelSelectedState];
    //初始化的时候修改默认字体的最后使用时间,后面字体排序要用
//    [[JCFontManager sharedManager] updateFontDBWithLastUseDate:[XYTool getNowTimeTimestamp] where:[NSString stringWithFormat:@"where fontCode = '%@'",textElement.configure.fontCode]];
}
-(void)add_wenben{
    [self addElement:Element(JCElementTextImage)];
}

-(void)add_yiweima{
    [self addElement:Element(JCElementBarCode)];
}

-(void)add_erweima{
    [self addElement:Element(JCElementQRCode)];
}

- (void)addIcon:(JCIConObject *)object {
    [self allElementsCancelSelectedState];
    JCElementLogoView *element = [JCDefaultElementManager imageWithIcon:object.imageUrl];
    element.type = JCElementLogoViewTypeIcon;
    JCPerformSelector(self.context.baseController, sel(@"cancenMutableSelect"));
    element.delegate = self;
    element.allowFreeZoom = NO;
    element.selected = YES;
    self.selectView = element;
    NSInteger count = DrawBoardInfo.addElementIndex ++ % 8;
    element.left = 10 + count * 16;
    element.top = 10 + count * 16;
    
    [self.elementViews setObject:element forKey:P_ADDRESS(element)];
    element.zIndex = self.zIndex++;
        if (element.image != nil) {
            [self.templateView addSubview:element];
        }
    /** 撤销恢复操作预加载 */
    [self undo_addElement:element];
    [self mutableStateAddElement:element];
    JCPerformSelectorWithOneObject(self.context.baseController, sel(@"firstAddElement:"), element)
    [self refreshToBarLockState];
}

- (void)addBorder:(JCIConObject *)object {
    [self allElementsCancelSelectedState];
    JCElementLogoView *element = [JCDefaultElementManager imageWithBorder:object.imageUrl];
    element.type = JCElementLogoViewTypeBorder;
    JCPerformSelector(self.context.baseController, sel(@"cancenMutableSelect"));
    element.delegate = self;
    element.allowFreeZoom = YES;
    element.selected = YES;
    self.selectView = element;
    [self.elementViews setObject:element forKey:P_ADDRESS(element)];
    element.zIndex = 1;
    [self.templateView insertSubview:element aboveSubview:self.backImgV];
    /** 撤销恢复操作预加载 */
    [self undo_addElement:element];
    [self mutableStateAddElement:element];
    JCPerformSelectorWithOneObject(self.context.baseController, sel(@"firstAddElement:"), element)
    [self refreshToBarLockState];
}

-(void)add_liushuihao{
    [self addElement:Element(JCElementSerialNumber)];
}

-(void)add_shijian{
    [self addElement:Element(JCElementTime)];
}

-(void)add_xingzhuang{
    [self addElement:Element(JCElementGraph)];
}

-(void)add_xiantiao{
    [self addElement:Element(JCElementLine)];
}

-(void)addPhotoItem:(UIImage *)img{
    JCElementLogoView *element = [JCDefaultElementManager imageWithPhoto:img];
    element.type = JCElementLogoViewTypeImage;
    [self addElement:element];
}

#pragma mark -- 顶部公共操作区
- (void)item_clear {
    [self.elementViews.allValues makeObjectsPerformSelector:@selector(removeFromSuperview)];
    [self.elementViews  removeAllObjects];
}

- (void)item_reset {
    [self recoverScale];
}

-(void)item_shanchu {
    if(self.isMutableSelect){
        NSArray *elements = self.mutableSelectViews.allValues;
        [elements enumerateObjectsUsingBlock:^(JCElementBaseView *obj, NSUInteger idx, BOOL * _Nonnull stop) {
            [self deleteElement:obj recordUndo:NO];
            obj = nil;
        }];
        [self.mutableSelectViews removeAllObjects];
        [self undo_deleteMutibleElements:elements];
    }else{
        [self deleteElement:self.selectView];
    }
}

- (void)deleteElement:(JCElementBaseView *)element {
    [self deleteElement:element recordUndo:YES];
}

- (void)deleteElements:(NSArray *)elements {
    [elements enumerateObjectsUsingBlock:^(JCElementBaseView *obj, NSUInteger idx, BOOL * _Nonnull stop) {
        [self deleteElement:obj recordUndo:NO];
        obj = nil;
    }];
}

- (void)deleteElement:(JCElementBaseView *)element recordUndo:(BOOL)record {
    if (record) {
        if (!element) return;
        // 添加撤销恢复操作
        [self undo_deleteElement:element];
        // 移除
        [self.elementViews removeObjectForKey:P_ADDRESS(element)];
        [element removeFromSuperview];
        [self hiddenGuideLine];
        self.selectView = nil;
    } else {
        if ([self.selectView isEqual:element]) { // 删除元素的同时，删除属性编辑view
            self.selectView = nil;
        }
        [self.elementViews removeObjectForKey:P_ADDRESS(element)];
        [element removeFromSuperview];
    }
    [self.mutableSelectViews removeObjectForKey:P_ADDRESS(element)];
    [self hiddenProperty];
    [self changeTopbarItems];
    // 更新excel导入状态
    JCPerformSelector(self.context.baseController, sel(@"refreshTitleState"));
    [self refreshToBarLockState];
}

-(void)item_xuanzhuan{
    if(self.isMutableSelect){
        [self.mutableSelectViews enumerateKeysAndObjectsUsingBlock:^(NSString *key, JCElementBaseView *obj, BOOL * _Nonnull stop) {
            [self rotateElement:obj];
        }];
    }else{
        [self rotateElement:self.selectView];
    }
}

- (void)rotateElement:(JCElementBaseView *)element {
    if (!element || element.isLock) return;
    [self undo_rotateElement:element];
    CGFloat rotate = (element.rotate + 90 >= 360)?0:(element.rotate + 90);
    element.rotate = rotate;
}

- (void)setIsMutableSelect:(BOOL)isMutableSelect {
    _isMutableSelect = isMutableSelect;
    // 如果从多选切换为单选 取消所有选中状态
    if (!_isMutableSelect) {
        [self.mutableSelectViews enumerateKeysAndObjectsUsingBlock:^(NSString *key, JCElementBaseView *obj, BOOL * _Nonnull stop) {
            obj.selected = NO;
        }];
        self.selectView.selected = NO;
        self.selectView = nil;
        [self hiddenProperty];
        [self allElementsCancelSelectedState];
        [self.mutableSelectViews removeAllObjects];
    } else {
        if (self.selectView) {
            [self mutableStateAddElement:self.selectView];
        }
    }
    [self refreshToBarLockState];
}

-(void)item_fuzhi{
    if(self.isMutableSelect){
        NSArray *elements = self.mutableSelectViews.allValues;
        NSMutableArray *copyElements = [NSMutableArray arrayWithCapacity:elements.count];
        [elements enumerateObjectsUsingBlock:^(JCElementBaseView *obj, NSUInteger idx, BOOL * _Nonnull stop) {
            JCElementBaseView *copyElement = [self copyElement:obj];
            [copyElements addObject:copyElement];
        }];
        [self undo_addMutibleElements:copyElements];
    }else{
        JCElementBaseView *copyView = [self copyElement:self.selectView];
        [self undo_addElement:copyView];
    }
}

- (JCElementBaseView *)copyElement:(JCElementBaseView *)element {
    if (!element) return nil;
    JCElementBaseView *copyView = [JCDefaultElementManager copyElement:element];
    [self addElement:copyView recordUndo:NO];
    // 多选复制全部选中
    if (self.isMutableSelect) {
        copyView.selected = YES;
        [self.mutableSelectViews setObject:copyView forKey:P_ADDRESS(copyView)];
    }
    return copyView;
}

- (void)item_suoding {
    if (self.isMutableSelect) {
        __block BOOL currentLock = YES;
        [self.mutableSelectViews enumerateKeysAndObjectsUsingBlock:^(id  _Nonnull key, JCElementBaseView *element, BOOL * _Nonnull stop) {
            if (!element.isLock) {
                currentLock = NO;
            }
        }];
        [self.mutableSelectViews enumerateKeysAndObjectsUsingBlock:^(id  _Nonnull key, JCElementBaseView *element, BOOL * _Nonnull stop) {
            element.isLock = !currentLock;
        }];
    } else {
        self.selectView.isLock = !self.selectView.isLock;
    }
    [self refreshToBarLockState];
}

- (void)item_showmain {
    [self tapBlank:nil];
}

#pragma mark - get template data
- (JCTemplateData *)getTemplateData {
    JCTemplateData *tData = self.templateInfo;
    tData.width = DrawBoardInfo.template_width_mm;
    tData.height = DrawBoardInfo.template_height_mm;
    tData.unit = @"mm";
    if (STR_IS_NIL(tData.idStr)) {
        tData.idStr = [XYTool randomElementId];
    }
    tData.currentPage = DrawBoardInfo.currentPageIndex+1;
    tData.totalPage = DrawBoardInfo.excelPageNumMax;
    tData.externalData = DrawBoardInfo.externalData;
    tData.task = DrawBoardInfo.task;
    JCProfile *profile = tData.profile;
    JCProfileExtrain *extrain = profile.extrain;
    extrain.userId = m_userModel.userId;
    
    NSMutableArray *elements = [NSMutableArray arrayWithCapacity:self.elementViews.count];
    NSArray *values = [self.elementViews.allValues sortedArrayUsingComparator:^NSComparisonResult(JCElementBaseView  *obj1, JCElementBaseView *obj2) {
        return obj1.zIndex > obj2.zIndex;
    }];
    [values enumerateObjectsUsingBlock:^(JCElementBaseView *obj, NSUInteger idx, BOOL * _Nonnull stop) {
        JCElementModel *model = [JCElementToItem itemForElement:obj];
        [elements addObject:model];
    }];

    tData.elements = elements.copy;
    tData.profile = profile;
    return tData;
}

- (void)updateTemplateId:(NSString *)templateId {
    self.templateInfo.idStr = templateId;
}

- (void)updateTemplateType:(NSString *)templateType {
    self.templateInfo.profile.extrain.templateType = templateType;
}



// 让界面复原
- (void)recover {
    [self.bgScroll removeFromSuperview];
    self.bgScroll = nil;
    [self.backImgV removeFromSuperview];
    self.backImgV = nil;
    [self.templateView removeFromSuperview];
    self.templateView = nil;
    [self.numberTopRulerView removeFromSuperview];
    self.numberTopRulerView = nil;
    [self.numberLeftRulerView removeFromSuperview];
    self.numberLeftRulerView = nil;
    [self.resertView removeFromSuperview];
    self.resertView = nil;
    [self.dottedView removeFromSuperview];
    self.dottedView = nil;
    self.elementViews = nil;
    self.mutableSelectViews = nil;
    self.jcUndoManager = nil;
    [self.bubbleSelectBar removeFromSuperview];
    self.bubbleSelectBar = nil;
    
}
#pragma mark - lazy

-(JCBaseScrollView *)bgScroll{
    if(!_bgScroll){
        _bgScroll = [[JCBaseScrollView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT)];
        _bgScroll.bounces = YES;
        _bgScroll.bouncesZoom = NO;
        _bgScroll.showsHorizontalScrollIndicator = NO;
        _bgScroll.showsVerticalScrollIndicator = NO;
        _bgScroll.scrollsToTop = NO;
        _bgScroll.minimumZoomScale = JCBoardMinScale;
        _bgScroll.maximumZoomScale = JCBoardMaxScale;
        _bgScroll.backgroundColor =  XY_HEX_RGB(0xf7f7f7);
        _bgScroll.decelerationRate = UIScrollViewDecelerationRateFast;
    }
    return _bgScroll;
}


-(UIImageView *)backImgV{
    if(!_backImgV){
        _backImgV = [[UIImageView alloc] init];
        _backImgV.contentMode = UIViewContentModeScaleToFill;
        _backImgV.tag = 9000; //这里必须设置9000.否则影响一些地方的过滤
    }
    return _backImgV;
}

-(JCElementMainView *)templateView{
    if(!_templateView){
        _templateView = [[JCElementMainView alloc] init];
        _templateView.backgroundColor = UIColor.clearColor;
    }
    return _templateView;
}

- (JCRulerView *)numberTopRulerView {
    if (!_numberTopRulerView) {
        _numberTopRulerView = [[JCRulerView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH_RULER, JCRuleWidth) withType:1];
        _numberTopRulerView.max = self.templateInfo.width + JCBoardWidthOffDistnceNum;
        _numberTopRulerView.min = -JCBoardWidthOffDistnceNum;
        _numberTopRulerView.templateScale =(int)self.templateInfo.width +JCTopRuleExtendScaleLength ;
        _numberTopRulerView.distanceBetweenScale =  [self oneScaleDistanceForWidth]; //刻度图标的宽度为1
        _numberTopRulerView.hidden = YES;
    }
    return _numberTopRulerView;
}

- (JCRulerView *)numberLeftRulerView {
    if (!_numberLeftRulerView) {
        _numberLeftRulerView = [[JCRulerView alloc] initWithFrame:CGRectMake(0, 0, JCRuleWidth, self.height) withType:2];
        CGFloat distance = [self oneScaleDistanceForWidth];
        _numberLeftRulerView.max =  self.templateInfo.height + JCBoardHeightOffDistnceNum;
        _numberLeftRulerView.min = - JCBoardHeightOffDistnceNum;
        _numberLeftRulerView.templateScale =(int)self.templateInfo.height +JCTopRuleExtendScaleLength;
        //刻度之间的距离
        _numberLeftRulerView.distanceBetweenScale =distance;
        _numberLeftRulerView.hidden = YES;
    }
    return _numberLeftRulerView;
}

- (JCScaleResertView *)resertView
{
    if (!_resertView) {
        _resertView = [[JCScaleResertView alloc] initWithFrame:CGRectZero];
    }
    return _resertView;
}

- (JCElementDottedView *)dottedView {
    if (!_dottedView) {
        _dottedView = [[JCElementDottedView alloc] init];
        [self addSubview:_dottedView];
        [_dottedView hidden];
    }
    return _dottedView;
}

- (NSMutableDictionary *)elementViews {
    if (!_elementViews) {
        _elementViews = [NSMutableDictionary dictionary];
    }
    return _elementViews;
}

- (NSMutableDictionary *)mutableSelectViews {
    if (!_mutableSelectViews) {
        _mutableSelectViews = [NSMutableDictionary dictionary];
    }
    return _mutableSelectViews;
}

- (JCElementOperateManager *)jcUndoManager {
    if (!_jcUndoManager) {
        _jcUndoManager = [[JCElementOperateManager alloc] init];
    }
    return _jcUndoManager;
}


- (JCBubbleBar *)bubbleSelectBar {
    if (!_bubbleSelectBar) {
        NSArray *imageArr = [self getBackGroundImages];
        CGFloat height = MIN(imageArr.count, 6)*45+10;
        XYWeakSelf;
        _bubbleSelectBar = [[JCBubbleBar alloc] initWithFrame:(CGRect){SCREEN_WIDTH - 62, 55, 50, height} images:imageArr selectIndex:^(NSInteger index) {
            weakSelf.backgroundImageIndex = index;
            self.templateInfo.mutableBackCurrentIndex = index;
            [weakSelf setTemplateBackgroundImageView];
        }];
    }
    return _bubbleSelectBar;
}
@end
