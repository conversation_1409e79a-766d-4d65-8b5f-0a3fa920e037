//
//  JCBoardConst.h
//  XYFrameWork
//
//  Created by APP on 2020/4/3.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import <UIKit/UIKit.h>

UIKIT_EXTERN const CGFloat JCBoardMaxScale ;  //画板缩放最大倍率
UIKIT_EXTERN const CGFloat JCBoardMinScale ;  //画板缩放最小倍率
UIKIT_EXTERN const CGFloat JCScaleWidth ;     //1刻度尺宽度
UIKIT_EXTERN const NSInteger JCBoardWidthOffDistnceNum;  //画板左或右可移动距离
UIKIT_EXTERN const NSInteger JCBoardHeightOffDistnceNum;  //画板上下可移动距离
UIKIT_EXTERN const CGFloat JCRuleWidth;  //刻度尺宽度
UIKIT_EXTERN const CGFloat JCBottomOffset;  //画布底部的距离
UIKIT_EXTERN const CGFloat JCscaleMax;  //刻度阈值
UIKIT_EXTERN const CGFloat JCShortScaleLength;  //短刻度高
UIKIT_EXTERN const CGFloat JCLongScaleLength;  //长刻度度高
UIKIT_EXTERN const CGFloat JCTopRuleExtendScaleLength;  //上方刻度延长长度
UIKIT_EXTERN const CGFloat JCLeftRuleExtendScaleLength;  //左方刻度延长长度

