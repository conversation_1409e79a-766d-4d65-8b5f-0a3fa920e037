//
//  JCScaleRestView.m
//  XYFrameWork
//
//  Created by APP on 2019/12/23.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCScaleResertView.h"

@interface JCScaleResertView ()

@property(nonatomic,strong) UILabel *scaleLabel;

@property(nonatomic,strong) UIButton *resertButton;



@end

@implementation JCScaleResertView

- (instancetype)initWithFrame:(CGRect)frame
{
    if (self = [super initWithFrame:frame]) {
        
        self.backgroundColor = COLOR_WHITE;
        self.layer.cornerRadius = 20;
        
         [self setupUI];
    }
    return self;
}

- (void)setupUI
{
    UILabel *titleLable = [[UILabel alloc] init];
    titleLable.text = @"比例";
    titleLable.textColor = XY_HEX_RGB(0xCCCCCC);
    titleLable.font = [UIFont systemFontOfSize:12];
    titleLable.textAlignment = NSTextAlignmentCenter;
    [self addSubview:titleLable];
    
    [self addSubview:self.scaleLabel];
    [self addSubview:self.resertButton];
    
    [titleLable mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self).mas_equalTo(16);
        make.centerY.equalTo(self);
        make.size.mas_equalTo(CGSizeMake(25, 17));
    
    }];
    
    [self.scaleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(titleLable.mas_trailing).mas_equalTo(6);
        make.centerY.equalTo(self);
        make.size.mas_equalTo(CGSizeMake(38, 14));
    }];
    
    [self.resertButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.scaleLabel.mas_trailing);
        make.centerY.equalTo(self.scaleLabel);
        make.trailing.equalTo(self);
        make.height.mas_equalTo(15);

    }];
    
    
}

- (void)resetButtonDidClick:(UIButton*)sender
{
    if (self.resertBlock) {
        self.resertBlock();
    }
    
}

- (void)setScale:(CGFloat)scale
{
    _scale = scale;
    int scaleNum = scale *100;
    _scaleLabel.text = [NSString stringWithFormat:@"%d%%",scaleNum];
}

- (UILabel *)scaleLabel
{
    if (!_scaleLabel) {
        _scaleLabel = [[UILabel alloc] init];
        _scaleLabel.font = MY_FONT_Bold(12);
        _scaleLabel.textAlignment = NSTextAlignmentCenter;
        _scaleLabel.text = @"100%";
        _scaleLabel.textColor = XY_HEX_RGB(0x666666);

    }
    return _scaleLabel;
}

- (UIButton *)resertButton
{
    if (!_resertButton) {
        _resertButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_resertButton setImage:XY_IMAGE_NAMED(@"resert_icon") forState:UIControlStateNormal];
        [_resertButton addTarget:self action:@selector(resetButtonDidClick:) forControlEvents:UIControlEventTouchUpInside];
        
        
    }
    return _resertButton;
}

@end
