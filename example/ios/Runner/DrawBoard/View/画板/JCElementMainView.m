
//
//  JCElementMainView.m
//  XYFrameWork
//
//  Created by xingling xu on 2019/12/13.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCElementMainView.h"

@implementation JCElementMainView

-(UIView *)hitTest:(CGPoint)point withEvent:(UIEvent *)event
{
    if (!self.userInteractionEnabled || self.hidden || self.alpha <= 0.01) {
        return nil;
    }    
    UIView *view = [super hitTest:point withEvent:event];
    UIView *elementView = view.superview;
    CGRect flineRect = elementView.frame;
    // 找边框下的文本
    if (elementView && [elementView isKindOfClass:NSClassFromString(@"JCElementGraph")]) {
        NSArray *currentElements = self.subviews;
        __block UIView *chooseText ;
        [currentElements enumerateObjectsUsingBlock:^(UIView *obj, NSUInteger idx, BOOL * _Nonnull stop) {
            if ([obj isKindOfClass:NSClassFromString(@"JCElementTextImage")] || [obj isKindOfClass:NSClassFromString(@"JCElementTime")] || [obj isKindOfClass:NSClassFromString(@"JCElementSerialNumber")]) {
                CGPoint textPoint = [obj convertPoint:point fromView:self];
                CGRect newF = (CGRect){0,0,obj.size.width+10,obj.size.height+10};
                if (CGRectContainsPoint(newF, textPoint)) {
                    chooseText = obj;
                    *stop = YES;
                }
            }
        }];
        if (chooseText) {
            CGPoint convertPoint = [chooseText convertPoint:point fromView:self];
            UIView *targetView = [chooseText hitTest:convertPoint withEvent:event];
            if (targetView) {
                return targetView;
            }
        }
        if (chooseText) return chooseText;
    }
    // 找边框下的边框
    if (elementView && [elementView isKindOfClass:NSClassFromString(@"JCElementGraph")]) {
        NSArray *currentElements = self.subviews;
        __block UIView *chooseGraph ;
        [currentElements enumerateObjectsUsingBlock:^(UIView *obj, NSUInteger idx, BOOL * _Nonnull stop) {
            if ([obj isKindOfClass:NSClassFromString(@"JCElementGraph")] && ![obj isEqual:elementView]) {
                CGPoint graphPoint = [obj convertPoint:point fromView:self];
                CGRect newF = (CGRect){0,0,obj.size.width+10,obj.size.height+10};
                if (CGRectContainsPoint(newF, graphPoint)) {
                    chooseGraph = obj;
                    *stop = YES;
                }
            }
        }];
        if (chooseGraph) {
            CGPoint convertPoint = [chooseGraph convertPoint:point fromView:self];
            UIView *targetView = [chooseGraph hitTest:convertPoint withEvent:event];
            if (targetView) {
                return targetView;
            }
        }
        if (chooseGraph) return chooseGraph;
    }
//    if(view.superview != nil && [view.superview isKindOfClass:NSClassFromString(@"JCElementGraph")] && ![view isKindOfClass:[UIButton class]]){
//            CGRect flineRect = view.frame;
//
//            UIView *line4View = view.superview;
//            for (UIView *v in line4View.superview.subviews) {
//                if ([v isKindOfClass:NSClassFromString(@"JCElementGraph")] || v.tag == 9000) {
//                    if([v isEqual:view]){
//                        continue;
//                    }else{
//                        if(CGRectContainsRect(flineRect, v.frame)){
//                            NSLog(@"包含");
//                        }else{
//                            continue;
//                        }
//                    }
//                }
//                CGRect f = v.frame;
//                CGRect newF = (CGRect){f.origin,f.size.width + 20,f.size.height + 20};
//                if (CGRectContainsPoint(newF, point)) {
//                    for (UIButton *subView in v.subviews) {
//                        CGPoint myPoint = [subView convertPoint:point fromView:self];
//                        if (CGRectContainsPoint(subView.bounds, myPoint)) {
//                            if ([subView isKindOfClass:[UIButton class]]) {
//                                if (subView.isEnabled) {
//                                    return subView;
//                                } else {
//                                    return v;
//                                }
//                            }
//                        }
//                    }
//                    if ([v isKindOfClass:NSClassFromString(@"JCElementGraph")]) {
//                        return v;
//                    } else {
//                        return v.superview;
//                    }
//                }
//            }
//            return line4View;
//        }
//        if ([view isKindOfClass:NSClassFromString(@"JCElementGraph")]) {
//            CGRect flineRect = view.frame;
//            for (UIView *v in view.superview.subviews) {
//                if(v.tag == 9000){
//                    continue;
//                }
//                if ([v isKindOfClass:NSClassFromString(@"JCElementGraph")]) {
//                    CGRect flineRect2 = v.frame;
//                    if([v isEqual:view]){
//                        continue;
//                    }else{
//                        if(CGRectContainsRect(flineRect, flineRect2)){
//                            NSLog(@"包含");
//                        }else{
//                            continue;
//                        }
//                    }
//                }
//                CGRect f = v.frame;
//                if (CGRectContainsPoint(f, point)) {
//                    [self insertSubview:v aboveSubview:view];
//                    if ([v isKindOfClass:NSClassFromString(@"JCElementSheet")]) {
//                        return [v hitTest:point withEvent:event];
//                    }
//                    return v;
//                }
//            }
//        }
    for(UIView *sb in [self.subviews reverseObjectEnumerator]){
        /** 超出模板响应手势 */
        if([sb isKindOfClass:NSClassFromString(@"JCElementBaseView")]){
            CGPoint convertPoint = [sb convertPoint:point fromView:self];
            UIView *targetView = [sb hitTest:convertPoint withEvent:event];
            if (targetView) {
                return targetView;
            }
        }
    }
    return [super hitTest:point withEvent:event];
}

@end
