//
//  JCPrintDirectionView.m
//  XYFrameWork
//
//  Created by xingling xu on 2020/8/11.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import "JCPrintDirectionView.h"

static CGFloat space = 9.0f;

@interface JCPrintDirectionView()
@property (nonatomic, strong) UILabel *label;
@property (nonatomic, strong) UIImageView *icon;
@property (nonatomic, assign) JCPrintDiretion direction;
@end

@implementation JCPrintDirectionView

- (instancetype)initWithDirection:(JCPrintDiretion)direction {
    self = [super init];
    if (self) {
        self.direction = direction;
        [self loadRootViews];
    }
    return self;
}

- (void)loadRootViews {
    [self addSubview:self.label];
    [self addSubview:self.icon];
    NSString *imageName;
    CGSize selfSize;
    if (self.direction == JCPrintDiretion_Up) {
        imageName = @"print_up";
    } else if (self.direction == JCPrintDiretion_Down) {
        imageName = @"print_down";
    } else if (self.direction == JCPrintDiretion_Left) {
        imageName = @"print_left";
    } else if (self.direction == JCPrintDiretion_Right) {
        imageName = @"print_right";
    }
    UIImage *iconImage = [UIImage imageNamed:imageName];
    CGSize imageSize = iconImage.size;
    self.icon.image = iconImage;
    CGSize size = [self.label sizeThatFits:(CGSize){CGFLOAT_MAX,16}];
    self.label.frame = (CGRect){space + imageSize.width,0,size};
    self.icon.size = imageSize;
    self.icon.left = 0;
    self.icon.centerY = size.height/2;
    self.size = (CGSize){size.width+space+self.icon.width,16};
//    if (self.direction == JCPrintDiretion_Right) {
//        imageName = @"print_right";
//        UIImage *iconImage = [UIImage imageNamed:imageName];
//        CGSize imageSize = iconImage.size;
//        self.icon.image = iconImage;
//        CGSize size = [self.label sizeThatFits:(CGSize){CGFLOAT_MAX,14}];
//        self.label.frame = (CGRect){0,0,size};
//        self.icon.size = imageSize;
//        self.icon.left = self.label.right + space;
//        self.icon.centerY = size.height/2;
//        self.size = (CGSize){size.width+space+self.icon.width,14};
//    } else {
//        if (self.direction == JCPrintDiretion_Up) {
//            imageName = @"print_up";
//        } else if (self.direction == JCPrintDiretion_Down) {
//            imageName = @"print_down";
//        } else if (self.direction == JCPrintDiretion_Left) {
//            imageName = @"print_left";
//        }
//        UIImage *iconImage = [UIImage imageNamed:imageName];
//        CGSize imageSize = iconImage.size;
//        self.icon.image = iconImage;
//        self.icon.left = 0;
//        self.icon.centerY = 7;
//        CGSize size = [self.label sizeThatFits:(CGSize){CGFLOAT_MAX,14}];
//        self.label.frame = (CGRect){self.icon.right+space,0,size};
//        self.size = (CGSize){size.width+space+self.icon.width,14};
//    }
}

// 有竖直方向版本
//- (void)loadRootViews {
//    [self addSubview:self.label];
//    [self addSubview:self.icon];
//    NSString *imageName;
//    CGSize selfSize;
//    if (self.direction == JCPrintDiretion_Up) {
//        self.label.numberOfLines = 0;
//        imageName = @"print_up";
//        self.icon.image = [UIImage imageNamed:imageName];
//        self.icon.left = 1;
//        self.icon.top = 0;
//        CGSize size = [self.label sizeThatFits:(CGSize){14,CGFLOAT_MAX}];
//        self.label.frame = (CGRect){0,self.icon.bottom + space,size};
//        self.size = (CGSize){14,size.height+space+self.icon.height};
//    } else if (self.direction == JCPrintDiretion_Down) {
//        self.label.numberOfLines = 0;
//        imageName = @"print_down";
//        self.icon.image = [UIImage imageNamed:imageName];
//        CGSize size = [self.label sizeThatFits:(CGSize){14,CGFLOAT_MAX}];
//        self.label.frame = (CGRect){0,0,size};
//        self.icon.left = 1;
//        self.icon.top = self.label.bottom + space;
//        self.size = (CGSize){14,size.height+space+self.icon.height};
//    } else if (self.direction == JCPrintDiretion_Left) {
//        self.label.numberOfLines = 1;
//        imageName = @"print_left";
//        self.icon.image = [UIImage imageNamed:imageName];
//        self.icon.left = 0;
//        self.icon.top = 1;
//        CGSize size = [self.label sizeThatFits:(CGSize){CGFLOAT_MAX,14}];
//        self.label.frame = (CGRect){self.icon.right+space,0,size};
//        self.size = (CGSize){size.width+space+self.icon.width,14};
//    } else if (self.direction == JCPrintDiretion_Right) {
//        self.label.numberOfLines = 1;
//        imageName = @"print_right";
//        self.icon.image = [UIImage imageNamed:imageName];
//        CGSize size = [self.label sizeThatFits:(CGSize){CGFLOAT_MAX,14}];
//        self.label.frame = (CGRect){0,0,size};
//        self.icon.left = self.label.right + space;
//        self.icon.top = 1;
//        self.size = (CGSize){size.width+space+self.icon.width,14};
//    }
//}

#pragma mark - lazy
- (UILabel *)label {
    if (!_label) {
        _label = [[UILabel alloc] init];
        _label.text = XY_LANGUAGE_TITLE_NAMED(@"app01137",@"出纸方向");
        _label.textColor = XY_HEX_RGB(0x999999);
        _label.font = MY_FONT_Regular(12);
    }
    return _label;
}

- (UIImageView *)icon {
    if (!_icon) {
        _icon = [[UIImageView alloc] initWithFrame:(CGRect){0,0,12,12}];
    }
    return _icon;
}

@end
