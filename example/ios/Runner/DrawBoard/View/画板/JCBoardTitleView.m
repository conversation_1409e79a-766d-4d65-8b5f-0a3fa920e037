//
//  JCBoardTitleView.m
//  XYFrameWork
//
//  Created by xingling xu on 2020/2/21.
//  Copyright © 2020 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCBoardTitleView.h"

@interface JCBoardTitleView ()
@property (nonatomic, strong) UILabel *label;
@property (nonatomic, strong) UIImageView *icon;
@property (nonatomic, strong) UIButton *button;
@property (nonatomic, copy) NSString *sizeString;
@end

@implementation JCBoardTitleView

- (instancetype)init {
    self = [super init];
    if (self) {
        [self initUI];
    }
    return self;
}

- (void)initUI {
    [self addSubview:self.label];
    [self addSubview:self.icon];
    [self addSubview:self.button];
}

- (void)reloadData {
    if (DrawBoardInfo.hasExcelElement) {
        [self refreshWithCurrentPage:DrawBoardInfo.currentPageIndex+1 totalPages:DrawBoardInfo.excelPageNumMax drawBoardSizeString:self.sizeString];
    } else {
        [self refreshWithCurrentPage:1 totalPages:1 drawBoardSizeString:self.sizeString];
    }
}

- (void)refreshSubviews {
    CGFloat right = 0;
    if (DrawBoardInfo.excelPageNumMax > 1 && DrawBoardInfo.hasExcelElement) {
        self.icon.hidden = NO;
        self.button.hidden = NO;
        self.icon.centerY = self.label.centerY;
        self.icon.left = self.label.right + 3;
        right = self.icon.right;
    } else {
        self.icon.hidden = YES;
        self.button.hidden = YES;
        right = self.label.right;
    }
    self.width = right;
    self.height = self.label.height;
    self.button.frame = self.bounds;
    
}

- (void)refreshWithCurrentPage:(NSInteger)currentPage totalPages:(NSInteger)totalPages drawBoardSizeString:(NSString *)sizeString {
    self.sizeString = sizeString;
    /** 显示分页数如: 1/200  */
    if (currentPage < 1) currentPage = 1;
    NSString *top = [NSString stringWithFormat:@"%ld/%ld",currentPage,totalPages];
    NSString *title = [NSString stringWithFormat:@"%@\n%@",top,sizeString];
    NSMutableAttributedString *string = [[NSMutableAttributedString alloc] initWithString:title];
    NSDictionary *topAttribute = @{NSFontAttributeName:[UIFont fontWithName:@"PingFangSC-Medium" size:16],NSForegroundColorAttributeName:XY_HEX_RGB(0xffffff)};
    NSDictionary *bottomAttribute = @{NSFontAttributeName:[UIFont fontWithName:@"PingFangSC-Medium" size:10],NSForegroundColorAttributeName:XY_HEX_RGBA(0xffffff,0.5)};
    [string addAttributes:topAttribute range:(NSRange){0,top.length}];
    [string addAttributes:bottomAttribute range:(NSRange){top.length,sizeString.length+1}];
    self.label.attributedText = string;
    [self.label sizeToFit];
    CGSize size = self.label.size;
    self.label.width = size.width + 10;
    [self refreshSubviews];
}

#pragma mark - button selector
- (void)btnClick:(id)sendet {
    if (self.delegate && [self.delegate respondsToSelector:@selector(titleViewDidClick:)]) {
        [self.delegate titleViewDidClick:self];
    }
}

#pragma mark - lazy
- (UILabel *)label {
    if (!_label) {
        _label = [[UILabel alloc] init];
        _label.numberOfLines = 2;
        _label.textAlignment = NSTextAlignmentCenter;
    }
    return _label;
}

- (UIImageView *)icon {
    if (!_icon) {
        UIImage *image = [UIImage imageNamed:@"element_title_arrow"];
        _icon = [[UIImageView alloc] init];
        _icon.size = image.size;
        _icon.image = image;
    }
    return _icon;
}

- (UIButton *)button {
    if (!_button) {
        _button = [UIButton buttonWithType:UIButtonTypeCustom];
        [_button addTarget:self action:@selector(btnClick:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _button;
}
@end
