//
//  JCDrawBoardView.h
//  XYFrameWork
//
//  Created by j c on 2019/7/9.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import <UIKit/UIKit.h>
#import "JCElementOperateManager.h"
#import "JCExcelForElement.h"
#import "JCTemplateEditController.h"

@interface JCDrawBoardView : UIView
/** 防丢器模式 */
@property (nonatomic, assign) JCBoardMode boardMode;
@property (nonatomic,copy) void(^scaleBlock)(CGFloat scale);//记录放大缩小的系数
@property (nonatomic,assign) BOOL isMutableSelect;     //是否多选，默认单选;
/** 管理撤销恢复的类 */
@property (nonatomic, strong) JCElementOperateManager *jcUndoManager;

- (instancetype)initWithFrame:(CGRect)frame
                    withModel:(JCTemplateData *)model
                         type:(NSString *)type
                 defaultScale:(CGFloat)scale
                    boardMode:(JCBoardMode)boardMode;
- (void)addPhotoItem:(UIImage *)img;//添加图片或者logo元素
- (void)importWith:(JCExcelForElement *)excelInfo selectColumnModels:(NSArray *)models;
- (void)doSaoMiaoActionGetResult:(NSString *)result withTagType:(NSInteger)type barCodeType:(NSString *)codeTypeStr;
- (void)currentElement:(JCElementBaseView *)baseView importWith:(JCExcelForElement *)excelInfo selectKey:(NSString *)aColumnIndex barCodeType:(JCCodeType)codeType;

- (void)recoverScale;

- (JCTemplateData *)getTemplateData;

- (void)updateTemplateId:(NSString *)templateId;
- (void)updateTemplateType:(NSString *)templateType;

- (void)refreshAllElements;
- (void)refreshSerialNumberElement;
- (BOOL)hasSelectedElement;

- (BOOL)templateHasChange;
- (void)allElementsCancelSelectedState;

- (void)updateWithModel:(JCTemplateData *)newData;

- (void)updateWithModel:(JCTemplateData *)newData withDeviceTemplate:(JCTemplateData *)deviceData;

- (void)addElement:(JCElementBaseView *)element recordUndo:(BOOL)record;

- (void)addElements:(NSArray *)elements;

- (void)deleteElement:(JCElementBaseView *)element recordUndo:(BOOL)record;

- (void)deleteElements:(NSArray *)elements;

//从印同款的预览中恢复
- (void)resetByPreview;
//显示印同款的预览图
- (void)showPrintSamePreview:(JCTemplateData *)tempData;
@end

