//
//  JCMutablePropertyView.m
//  XYFrameWork
//
//  Created by xingling xu on 2020/1/8.
//  Copyright © 2020 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCMutablePropertyView.h"
#import "JCSelectionView.h"
#import "JCMoveView.h"

/** 水平方向操作按钮 */
#define horizontalLessNormaImages   @[@"horizontal_left_normal",@"horizontal_center_normal",@"horizontal_right_normal"]
#define horizontalLessSelectmages   @[@"horizontal_left_select",@"horizontal_center_select",@"horizontal_right_select"]
#define horizontalMoreNormaImages   @[@"horizontal_left_normal",@"horizontal_center_normal",@"horizontal_right_normal",@"horizontal_equal_normal"]
#define horizontalMoreSelectmages   @[@"horizontal_left_select",@"horizontal_center_select",@"horizontal_right_select",@"horizontal_equal_select"]
/** 竖直防线操作按钮 */
#define verticalLessNormaImages     @[@"vertical_top_normal",@"vertical_center_normal",@"vertical_bottom_normal"]
#define verticalLessSelectmages     @[@"vertical_top_select",@"vertical_center_select",@"vertical_bottom_select"]
#define verticalMoreNormaImages     @[@"vertical_top_normal",@"vertical_center_normal",@"vertical_bottom_normal",@"vertical_equal_normal"]
#define verticalMoreSelectmages     @[@"vertical_top_select",@"vertical_center_select",@"vertical_bottom_select",@"vertical_equal_select"]

@interface JCMutablePropertyView () <JCSelectionViewDelegate>
@property (nonatomic, strong) JCSelectionView *horizontalView;
@property (nonatomic, strong) JCSelectionView *verticalView;
@property (nonatomic, strong) JCMoveView *moveView;
@end

@implementation JCMutablePropertyView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setUp];
    }
    return self;
}

- (void)setUp {
    [self addSubview:self.horizontalView];
    [self addSubview:self.verticalView];
    [self addSubview:self.moveView];
}

- (void)layoutSubviews {
    [super layoutSubviews];
    self.horizontalView.left = 24;
    self.horizontalView.centerY = 48;
    self.verticalView.left = 24;
    self.verticalView.centerY = 112;
    self.moveView.centerY = 80;
    self.moveView.right = SCREEN_WIDTH - 24;
}

#pragma mark -- 针对每个元素的操作
- (void)move:(JCElementBaseView *)element derection:(MoveDerection)derection {
    CGFloat moveDistance = [XYTool getScreenNumberWithMM:0.5];
    switch (derection) {
        case MoveTop:
        {
            element.top -= moveDistance;
        }break;
        case MoveRight:
        {
            element.left += moveDistance;
        }break;
        case MoveLeft:
        {
            element.left -= moveDistance;
        }break;
        case MoveBottom:
        {
            element.top += moveDistance;
        }break;
        default:
            break;
    }
}

/** 水平：左、右、中 */
- (void)move:(JCElementBaseView *)element horizontalWithIndex:(NSInteger)index {
    NSString *moveValue = @"";
    if (index == 0) {
        moveValue = @"left";
    } else if (index == 1) {
        moveValue = @"horizontal_center";
    } else if (index == 2) {
        moveValue = @"right";
    }
    JCPerformSelectorWithOneObject(self.context.drawBoard, sel(@"mutableElementsAlign:"), moveValue);
}
/** 竖直：上、下、中 */
- (void)move:(JCElementBaseView *)element vereticalWithIndex:(NSInteger)index {
    NSString *moveValue = @"";
    if (index == 0) {
        moveValue = @"top";
    } else if (index == 1) {
        moveValue = @"vertical_center";
    } else if (index == 2) {
        moveValue = @"bottom";
    }
    JCPerformSelectorWithOneObject(self.context.drawBoard, sel(@"mutableElementsAlign:"), moveValue);
}

/** 水平等间距 */
- (void)horizontalEqualSpace {
    JCPerformSelectorWithOneObject(self.context.drawBoard, sel(@"mutableElementsAlign:"), @"horizontal_equidistant");
}

/** 竖直等间距 */
- (void)verticalEqualSpace {
    JCPerformSelectorWithOneObject(self.context.drawBoard, sel(@"mutableElementsAlign:"), @"vertical_equidistant");
}

#pragma mark -- JCSelectionViewDelegate
- (void)selectionView:(JCSelectionView *)selectionView selectIndex:(NSInteger)index {
    if ([selectionView isEqual:self.horizontalView] ){
        if (index > 2) {
            [self horizontalEqualSpace];
        } else {
            /** 水平：左、右、中 */
            [self.mutableElements enumerateObjectsUsingBlock:^(JCElementBaseView *element, NSUInteger idx, BOOL * _Nonnull stop) {
                [self move:element horizontalWithIndex:index];
            }];
        }
    } else {
        if (index > 2) {
            [self verticalEqualSpace];
        } else {
            /** 竖直：上、下、中 */
            [self.mutableElements enumerateObjectsUsingBlock:^(JCElementBaseView *element, NSUInteger idx, BOOL * _Nonnull stop) {
                [self move:element vereticalWithIndex:index];
            }];
        }
    }
}

#pragma mark -- JCMoveViewDelegate
- (void)moveView:(JCMoveView *)moveView derection:(MoveDerection)derection {
    JCElementBaseView *lockView = [self.mutableElements find:^BOOL(JCElementBaseView *obj) {
        return obj.isLock == YES;
    }];
    if (lockView) {
        JCPerformSelector(self.context.baseController, sel(@"alignNotAllow")) return;
    }
    [self.mutableElements enumerateObjectsUsingBlock:^(JCElementBaseView *element, NSUInteger idx, BOOL * _Nonnull stop) {
        [self move:element derection:derection];
    }];
}

#pragma mark -- set
- (void)setMutableElements:(NSArray *)mutableElements {
    _mutableElements = mutableElements;
    if (_mutableElements.count > 2) {
        self.horizontalView.changeImage(horizontalMoreNormaImages,horizontalMoreSelectmages);
        self.verticalView.changeImage(verticalMoreNormaImages,verticalMoreSelectmages);
    } else {
        self.horizontalView.changeImage(horizontalLessNormaImages,horizontalLessSelectmages);
        self.verticalView.changeImage(verticalLessNormaImages,verticalLessSelectmages);
    }
}

#pragma mark -- lazy
- (JCSelectionView *)horizontalView {
    if (!_horizontalView) {
        _horizontalView = [[JCSelectionView alloc] initWithFrame:(CGRect){0,0,160,40} normalImageNames:horizontalLessNormaImages selectImageNames:horizontalLessSelectmages];
        _horizontalView.selectButtonHidden = YES;
        _horizontalView.delegate = self;
    }
    return _horizontalView;
}

- (JCSelectionView *)verticalView {
    if (!_verticalView) {
        _verticalView = [[JCSelectionView alloc] initWithFrame:(CGRect){0,0,160,40} normalImageNames:verticalMoreNormaImages selectImageNames:verticalMoreSelectmages];
        _verticalView.selectButtonHidden = YES;
        _verticalView.delegate = self;
    }
    return _verticalView;
}

- (JCMoveView *)moveView {
    if (!_moveView) {
        _moveView = [[JCMoveView alloc] initWithFrame:(CGRect){0,0,128,128}];
        _moveView.delegate = self;
    }
    return _moveView;
}

@end
