//
//  JCStyleModel.h
//  XYFrameWork
//
//  Created by xingling xu on 2019/12/10.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

#define Displacement            [JCStyleModel displacementModel]
#define Font                    [JCStyleModel fontModel]
#define Style                   [JCStyleModel styleModel]
#define Space                   [JCStyleModel spaceModel]
#define BarCodeContent          [JCStyleModel barCodeContentModel]
#define BarCodeType             [JCStyleModel barCodeTypeModel]
#define QrCodeType              [JCStyleModel qrCodeTypeModel]
#define Date                    [JCStyleModel dateTypeModel]
#define Time                    [JCStyleModel timeTypeModel]
#define Line                    [JCStyleModel lineTypeModel]
#define Number                  [JCStyleModel numberTypeModel]
#define Shape                   [JCStyleModel shapeTypeModel]
#define SheetCombine            [JCStyleModel sheetCombineModel]
#define SheetSize               [JCStyleModel sheetSizeModel]
#define MutableDisplacement     [JCStyleModel mutableDisplacementModel]
#define ExcelImport             [JCStyleModel excelImportModel]
#define ExcelColumn             [JCStyleModel excelColumnModel]
#define ImageProperty           [JCStyleModel imagePropertyModel]
#define SheetTextStyle          [JCStyleModel sheetTextStyleModel]

typedef enum : NSUInteger {
    JCStyleType_Displacement,
    JCStyleType_Font,
    JCStyleType_Style,
    JCStyleType_Space,
    JCStyleType_DateFormat,
    JCStyleType_TimeFormat,
    JCStyleType_BarcodeContent,
    JCStyleType_BarcodeType,
    JCStyleType_QrcodeType,
    JCStyleType_LineType,
    JCStyleType_NumberType,
    JCStyleType_ShapeType,
    JCStyleType_SheetCombine,
    JCStyleType_SheetSize,
    JCStyleType_MutableDisplacement,
    JCStyleType_ExcelImport,
    JCStyleType_ExcelColumn,
    JCStyleType_ImageProperty,
    JCStyleType_SheetTextStyle
} JCStyleType;

@interface JCStyleModel : NSObject

@property (nonatomic, copy) NSString *title;
/** 属性编辑选项类型 */
@property (nonatomic, assign) JCStyleType type;

@property (nonatomic, copy) NSString *contentViewClass;
/** 选中状态 */
@property (nonatomic, assign) BOOL selected;
/** 位移 */
+ (JCStyleModel *)displacementModel;
/** 字体选择以及大小 */
+ (JCStyleModel *)fontModel;
/** 字体样式 */
+ (JCStyleModel *)styleModel;
/** 字体间距等 */
+ (JCStyleModel *)spaceModel;
/** 条码内容编辑 */
+ (JCStyleModel *)barCodeContentModel;
/** 一维码格式 */
+ (JCStyleModel *)barCodeTypeModel;
/** 二维码 */
+ (JCStyleModel *)qrCodeTypeModel;
/** 日期格式 */
+ (JCStyleModel *)dateTypeModel;
/** 时间格式 */
+ (JCStyleModel *)timeTypeModel;
/** 线条样式 */
+ (JCStyleModel *)lineTypeModel;
/** 流水号样式 */
+ (JCStyleModel *)numberTypeModel;
/** 形状、描边公用，修改title调用 titleWith 的block*/
+ (JCStyleModel *)shapeTypeModel;
/** 表格合并、拆分 */
+ (JCStyleModel *)sheetCombineModel;
/** 行高/列宽 */
+ (JCStyleModel *)sheetSizeModel;
/** 多选位移 */
+ (JCStyleModel *)mutableDisplacementModel;
/** Excel导入 */
+ (JCStyleModel *)excelImportModel;
/** 选择列 */
+ (JCStyleModel *)excelColumnModel;
/** 修改图片阈值 */
+ (JCStyleModel *)imagePropertyModel;
/** 表格文本属性 包含文本垂直位置调整 */
+ (JCStyleModel *)sheetTextStyleModel;

/** 调整选中状态 */
- (JCStyleModel *(^)(BOOL value))select;
/** 调整title */
- (JCStyleModel *(^)(NSString *value))titleWith;
@end

NS_ASSUME_NONNULL_END
