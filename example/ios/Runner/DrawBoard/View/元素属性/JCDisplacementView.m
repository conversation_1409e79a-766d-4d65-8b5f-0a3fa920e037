//
//  JCDisplacementView.m
//  XYFrameWork
//
//  Created by xingling xu on 2019/12/10.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCDisplacementView.h"
#import "JCSelectionView.h"
#import "JCMoveView.h"

/** 微调每次移动距离,单位毫米 */
static CGFloat distance_move_every_time = 0.2;

@interface JCDisplacementView () <JCSelectionViewDelegate>
@property (nonatomic, strong) JCSelectionView *horizontalView;
@property (nonatomic, strong) JCSelectionView *verticalView;
@property (nonatomic, strong) JCMoveView *moveView;
@end

@implementation JCDisplacementView
- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setUp];
    }
    return self;
}

- (void)setUp {
    [self addSubview:self.horizontalView];
    [self addSubview:self.verticalView];
    [self addSubview:self.moveView];
}

- (void)layoutSubviews {
    [super layoutSubviews];
    self.horizontalView.left = 24;
    self.horizontalView.centerY = 48;
    self.verticalView.left = 24;
    self.verticalView.centerY = 112;
    self.moveView.centerY = 80;
    self.moveView.right = SCREEN_WIDTH - 24;
}

#pragma mark -- JCSelectionViewDelegate
- (void)selectionView:(JCSelectionView *)selectionView selectIndex:(NSInteger)index {
//    if (self.element.isLock) return;
    CGFloat boardWidth = [JCDrawInfoManager sharedManager].boardWidth;
    CGFloat boardHeight = [JCDrawInfoManager sharedManager].boardHeight;
    if ([selectionView isEqual:self.horizontalView] ){
        if (index == 0) {
            self.element.left = 0;
        } else if (index == 1) {
            self.element.centerX = boardWidth/2;
        } else if (index == 2) {
            self.element.right = boardWidth;
        }
    } else {
        if (index == 0) {
            self.element.top = 0;
        } else if (index == 1) {
            self.element.centerY = boardHeight/2;
        } else if (index == 2) {
            self.element.bottom = boardHeight;
        }
    }
}

#pragma mark -- JCMoveViewDelegate
- (void)moveView:(JCMoveView *)moveView derection:(MoveDerection)derection {
//    if (self.element.isLock) return;
    CGFloat moveDistance = [XYTool getScreenNumberWithMM:distance_move_every_time];
    switch (derection) {
        case MoveTop:
        {
            self.element.top -= moveDistance;
        }break;
        case MoveRight:
        {
            self.element.left += moveDistance;
        }break;
        case MoveLeft:
        {
            self.element.left -= moveDistance;
        }break;
        case MoveBottom:
        {
            self.element.top += moveDistance;
        }break;
        default:
            break;
    }
}

#pragma mark -- lazy
- (JCSelectionView *)horizontalView {
    if (!_horizontalView) {
        _horizontalView = [[JCSelectionView alloc] initWithFrame:(CGRect){0,0,150,40} normalImageNames:@[@"horizontal_left_normal",@"horizontal_center_normal",@"horizontal_right_normal"] selectImageNames:@[@"horizontal_left_select",@"horizontal_center_select",@"horizontal_right_select"]];
        _horizontalView.selectButtonHidden = YES;
        _horizontalView.delegate = self;
    }
    return _horizontalView;
}

- (JCSelectionView *)verticalView {
    if (!_verticalView) {
        _verticalView = [[JCSelectionView alloc] initWithFrame:(CGRect){0,0,150,40} normalImageNames:@[@"vertical_top_normal",@"vertical_center_normal",@"vertical_bottom_normal"] selectImageNames:@[@"vertical_top_select",@"vertical_center_select",@"vertical_bottom_select"]];
        _verticalView.selectButtonHidden = YES;
        _verticalView.delegate = self;
    }
    return _verticalView;
}

- (JCMoveView *)moveView {
    if (!_moveView) {
        _moveView = [[JCMoveView alloc] initWithFrame:(CGRect){0,0,128,128}];
        _moveView.delegate = self;
    }
    return _moveView;
}

@end
