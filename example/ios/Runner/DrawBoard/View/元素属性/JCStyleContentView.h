//
//  JCStyleContentView.h
//  XYFrameWork
//
//  Created by xingling xu on 2019/12/10.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import <UIKit/UIKit.h>
#import "JCElementEditManager.h"

#define Str_Int(int_number)         [NSString stringWithFormat:@"%ld",int_number]
#define Str_Float(float_number)     [NSString stringWithFormat:@"%f",float_number]

/* @ self.context.drawBoard : 对应画板的类：JCDrawBoardView
 * @ sel(@"beginUndo:") : 对应画板的beginUndo 方法,必须和EndUndoWithKey
 * @ key : 修改对应的key
 * @ value : 保存相关属性的值
 */
/** 捕捉编辑之前的属性 必须和EndUndoWithKey 配对*/
#define BeginUndoWithKey(key,value)  JCPerformSelectorWithOneObject(self.context.drawBoard, sel(@"beginUndo:"),  (@{k_element:self.element,k_action:key,key:value}))
/** 捕捉编辑之后的属性  必须和BeginUndoWithKey 配对*/
#define EndUndoWithKey(key,value)    JCPerformSelectorWithOneObject(self.context.drawBoard, sel(@"endUndo:"),  (@{k_element:self.element,k_action:key,key:value}))

NS_ASSUME_NONNULL_BEGIN

@interface JCStyleContentView : UIView
@property (nonatomic, strong) JCElementBaseView *element;
@end

NS_ASSUME_NONNULL_END
