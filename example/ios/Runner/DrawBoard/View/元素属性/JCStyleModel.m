
//
//  JCStyleModel.m
//  XYFrameWork
//
//  Created by xingling xu on 2019/12/10.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCStyleModel.h"

@implementation JCStyleModel

+ (JCStyleModel *)displacementModel {
    JCStyleModel *model = [JCStyleModel new];
    model.title = XY_LANGUAGE_TITLE_NAMED(@"app00168", @"对齐");
    model.type = JCStyleType_Displacement;
    model.contentViewClass = @"JCDisplacementView";
    return model;
}

+ (JCStyleModel *)mutableDisplacementModel {
    JCStyleModel *model = [JCStyleModel new];
    model.title = XY_LANGUAGE_TITLE_NAMED(@"app00168", @"对齐");
    model.type = JCStyleType_MutableDisplacement;
    model.contentViewClass = @"JCMutablePropertyView";
    return model;
}

+ (JCStyleModel *)fontModel {
    JCStyleModel *model = [JCStyleModel new];
    model.title = XY_LANGUAGE_TITLE_NAMED(@"app01005", @"字体");
    model.type = JCStyleType_Font;
    model.contentViewClass = @"JCFontEditView";
    return model;
}

+ (JCStyleModel *)styleModel {
    JCStyleModel *model = [JCStyleModel new];
    model.title = XY_LANGUAGE_TITLE_NAMED(@"app01006", @"样式");
    model.type = JCStyleType_Style;
    model.contentViewClass = @"JCFontStyleView";
    return model;
}

+ (JCStyleModel *)spaceModel {
    JCStyleModel *model = [JCStyleModel new];
    model.title = XY_LANGUAGE_TITLE_NAMED(@"app01007", @"间距");
    model.type = JCStyleType_Space;
    model.contentViewClass = @"JCFontSpaceView";
    return model;
}

+ (JCStyleModel *)barCodeContentModel {
    JCStyleModel *model = [JCStyleModel new];
    model.title = XY_LANGUAGE_TITLE_NAMED(@"app01040", @"内容/编码");
    model.type = JCStyleType_BarcodeContent;
    model.contentViewClass = @"JCBarcodeContentView";
    return model;
}

+ (JCStyleModel *)barCodeTypeModel {
    JCStyleModel *model = [JCStyleModel new];
    model.title = XY_LANGUAGE_TITLE_NAMED(@"app01006", @"样式");
    model.type = JCStyleType_BarcodeType;
    model.contentViewClass = @"JCBarCodeStyleView";
    return model;
}

+ (JCStyleModel *)qrCodeTypeModel {
    JCStyleModel *model = [JCStyleModel new];
    model.title = XY_LANGUAGE_TITLE_NAMED(@"app01040", @"内容/编码");
    model.type = JCStyleType_QrcodeType;
    model.contentViewClass = @"JCQrCodeView";
    return model;
}

+ (JCStyleModel *)dateTypeModel {
    JCStyleModel *model = [JCStyleModel new];
    model.title = XY_LANGUAGE_TITLE_NAMED(@"app01020", @"日期");
    model.type = JCStyleType_DateFormat;
    model.contentViewClass = @"JCDateFormatView";
    return model;
}

+ (JCStyleModel *)timeTypeModel {
    JCStyleModel *model = [JCStyleModel new];
    model.title = XY_LANGUAGE_TITLE_NAMED(@"app00010", @"时间");
    model.type = JCStyleType_TimeFormat;
    model.contentViewClass = @"JCTimeFormatView";
    return model;
}

+ (JCStyleModel *)lineTypeModel {
    JCStyleModel *model = [JCStyleModel new];
    model.title = XY_LANGUAGE_TITLE_NAMED(@"app00013", @"线条");
    model.type = JCStyleType_LineType;
    model.contentViewClass = @"JCLineStyleView";
    return model;
}

+ (JCStyleModel *)numberTypeModel {
    JCStyleModel *model = [JCStyleModel new];
    model.title = XY_LANGUAGE_TITLE_NAMED(@"app01041", @"数值");
    model.type = JCStyleType_NumberType;
    model.contentViewClass = @"JCNumberEditView";
    return model;
}

+ (JCStyleModel *)shapeTypeModel {
    JCStyleModel *model = [JCStyleModel new];
    model.title = XY_LANGUAGE_TITLE_NAMED(@"app00011", @"形状");
    model.type = JCStyleType_ShapeType;
    model.contentViewClass = @"JCShapeView";
    return model;
}

+ (JCStyleModel *)sheetCombineModel {
    JCStyleModel *model = [JCStyleModel new];
    model.title = XY_LANGUAGE_TITLE_NAMED(@"app00005", @"表格");
    model.type = JCStyleType_SheetCombine;
    model.contentViewClass = @"JCSheetCombineView";
    return model;
}

+ (JCStyleModel *)sheetSizeModel {
    JCStyleModel *model = [JCStyleModel new];
    model.title = XY_LANGUAGE_TITLE_NAMED(@"app01016", @"行高/列宽");
    model.type = JCStyleType_SheetSize;
    model.contentViewClass = @"JCSheetSizeView";
    return model;
}

+ (JCStyleModel *)excelImportModel {
    JCStyleModel *model = [JCStyleModel new];
    model.title = XY_LANGUAGE_TITLE_NAMED(@"app01008", @"内容导入");
    model.type = JCStyleType_ExcelImport;
    model.contentViewClass = @"JCExcelImportView";
    return model;
}
+ (JCStyleModel *)excelColumnModel {
    JCStyleModel *model = [JCStyleModel new];
    model.title = XY_LANGUAGE_TITLE_NAMED(@"", @"选择列");
    model.type = JCStyleType_ExcelColumn;
    return model;
}

+ (JCStyleModel *)imagePropertyModel {
    JCStyleModel *model = [JCStyleModel new];
    model.title = XY_LANGUAGE_TITLE_NAMED(@"app01067", @"属性");
    model.type = JCStyleType_ImageProperty;
    model.contentViewClass = @"JCImagePropertyView";
    return model;
}

+ (JCStyleModel *)sheetTextStyleModel {
    JCStyleModel *model = [JCStyleModel new];
    model.title = XY_LANGUAGE_TITLE_NAMED(@"app01006", @"样式");
    model.type = JCStyleType_SheetTextStyle;
    model.contentViewClass = @"JCSheetFontStyleView";
    return model;
}

- (JCStyleModel *(^)(BOOL))select {
    return ^(BOOL value){
        self.selected = value;
        return self;
    };
}

- (JCStyleModel *(^)(NSString *value))titleWith {
    return ^(NSString *value){
        self.title = value;
        return self;
    };
}
@end
