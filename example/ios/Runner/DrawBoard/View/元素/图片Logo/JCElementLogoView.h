//
//  JCElementLogoView.h
//  XYFrameWork
//
//  Created by xingling xu on 2019/11/28.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

///icon, border, image公用这个view
typedef NS_ENUM(NSUInteger, JCElementLogoViewType) {
    JCElementLogoViewTypeImage,
    JCElementLogoViewTypeIcon,
    JCElementLogoViewTypeBorder,
};
#import "JCElementBaseView.h"

NS_ASSUME_NONNULL_BEGIN

@interface JCElementLogoView : JCElementBaseView

@property(nonatomic,assign)JCElementLogoViewType type;
// 原始图片数据  不受清晰度调整影响
@property (nonatomic, strong) UIImage *image;
// 图片url
@property (nonatomic, copy) NSString *imageUrl;
// 清晰度
@property (nonatomic, copy) NSString *thresHoldValue;
// 是否是logo 如果是则有
@property (nonatomic, copy) NSString *materialId;
// 是否可自由拉伸
@property (nonatomic, assign) BOOL allowFreeZoom;
@end

NS_ASSUME_NONNULL_END
