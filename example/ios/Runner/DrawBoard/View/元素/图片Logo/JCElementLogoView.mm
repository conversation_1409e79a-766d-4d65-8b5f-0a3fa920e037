//
//  JCElementLogoView.m
//  XYFrameWork
//
//  Created by xingling xu on 2019/11/28.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCElementLogoView.h"
#import "JCElementToItem.h"
#import "JCElementModel+Transfer.h"

@interface JCElementLogoView ()
@property (nonatomic, strong) UIImageView *imageView;
@property (nonatomic, assign) CGPoint startPoint;
@property (nonatomic, assign) CGRect  startFrame;
/** 高宽比：height/width */
@property (nonatomic, assign) CGFloat  h_w_scale;
@end

@implementation JCElementLogoView

- (instancetype)init {
    if (self = [super init]) {
        [self initUI];
    }
    return self;
}

- (void)initUI {
    [self addSubview:self.imageView];
}

- (void)reloadSDKImage {
    [self reloadImage];
}

- (void)layoutSubviews {
    [super layoutSubviews];
    self.imageView.frame = self.bounds;
}

//- (void)prepareImageData {
//    if (!STR_IS_NIL(self.materialId)) {// 如果素材id存在 取本地路径
//        NSString *imageUrlString = [NSString stringWithFormat:@"%@/%@.png",RESOURCE_IMAGE_LOG_PATH,self.materialId];
//        NSData *data = [NSData dataWithContentsOfFile:imageUrlString];
//        UIImage *image = [UIImage imageWithData:data];
//        if(image != nil){
//            _image = image;
//            [self reloadImage];
//        }else{
//            [self loadUrlImage];
//        }
//    } else if (self.image) {// 来自相册等
//        [self reloadImage];
//    } else if (!STR_IS_NIL(self.imageUrl)) {// 网络上的url
//        [self loadUrlImage];
//    }
//}
//
//- (void)loadUrlImage {
//    JCWeakSelf;
//    [self.imageView sd_setImageWithURL:[NSURL URLWithString:self.imageUrl] completed:^(UIImage *image, NSError *error, SDImageCacheType cacheType, NSURL *imageURL) {
//        JCStrongSelf;
//        _image = image;
//        [self reloadImage];
//    }];
//}

- (void)reloadImage {
    if (!self.image || self.thresHoldValue.length == 0) return;
    JCElementModel *model = [JCElementToItem itemForElement:self];
    model.rotate = 0;
    dispatch_async(dispatch_queue_create(0, 0), ^{
        NSMutableDictionary *dict = [model.toSdk(YES) elementDict].mutableCopy;
        NSMutableDictionary *params = [NSMutableDictionary dictionaryWithCapacity:1];
        [params setObject:@[dict] forKey:@"elements"];
        NSString *json = [params  dc_toJSONString];
        NSInteger screenScale = [UIScreen mainScreen].scale;
        UIImage *previewImage = nil;//[JCAPI generateImagePreviewImage:json displayMultiple:DrawBoardInfo.mm2pxScale*screenScale];
        dispatch_async(dispatch_get_main_queue(), ^{
            self.imageView.image = previewImage;
            rotate(self.h_w_scale = self.size.height/self.size.width;)
            [self setNeedsLayout];
        });
    });
}

- (void)setImage:(UIImage *)image {
    _image = image;
    [self reloadImage];
}

- (void)setThresHoldValue:(NSString *)thresHoldValue {
    _thresHoldValue = thresHoldValue;
    [self reloadImage];
}

- (void)setAllowFreeZoom:(BOOL)allowFreeZoom {
    _allowFreeZoom = allowFreeZoom;
    rotate(self.h_w_scale = self.size.height/self.size.width;)
}

#pragma makr - 由于等比缩放重写父类方法
- (void)zoomDragBegin: (UIControl *) c withEvent:touches
{
    if (self.isLock) return;
    UITouch *touch = [[touches allTouches] anyObject];
    CGPoint point = [touch locationInView:self.superview];
    self.startPoint = point;
    self.startFrame = self.frame;
    if (self.delegate && [self.delegate respondsToSelector:@selector(elementViewDragBegin:)]) {
        [self.delegate elementViewDragBegin:self];
    }
}

- (void)zoomDragOutside: (UIControl *) c withEvent:touches
{
    // override do nothing
}

- (void)zoomDragMoving: (UIControl *) c withEvent:touches
{
    
    if (self.allowFreeZoom) {
        [super zoomDragMoving:c withEvent:touches];
    } else {
        [self equalMoveWithEvents:touches];
    }
    [self setNeedsDisplay];
}

- (void)equalMoveWithEvents:touches {
    if (self.isLock || self.zoomButton.hidden) return;
    UITouch *touch = [[touches allTouches] anyObject];
    CGPoint point = [touch locationInView:self.superview];
    CGFloat xlength = point.x - self.startPoint.x; //等比缩放以x移动距离作为基准
    CGFloat ylength = point.y - self.startPoint.y; //等比缩放以y移动距离作为基准
    CGFloat value = 0;
    CGFloat cLeft = self.startFrame.origin.x,cTop =self.startFrame.origin.y,cRight = cLeft+self.startFrame.size.width,cBottom = cTop+self.startFrame.size.height;
    CGFloat minWidth = [self minWidth_mm]*DrawBoardInfo.mm2pxScale;
    CGFloat minHeight = [self minHeight_mm]*DrawBoardInfo.mm2pxScale;
    if (self.width >= self.height) {
        if (self.rotate == 0) {
            value = MAX(self.startFrame.size.width+xlength,minWidth);
            self.size = (CGSize){value,value*self.h_w_scale};
            self.top = cTop;
            self.left = cLeft;
        } else if (self.rotate == 270) {
            value = MAX(self.startFrame.size.width+xlength,minWidth);
            self.size = (CGSize){value,value/self.h_w_scale};
            self.bottom = cBottom;
            self.left = cLeft;
        } else if (self.rotate == 90) {
            value = MAX(self.startFrame.size.width-xlength,minWidth);
            self.size = (CGSize){value,value/self.h_w_scale};
            self.top = cTop;
            self.right = cRight;
        } else if (self.rotate == 180) {
            value = MAX(self.startFrame.size.width-xlength,minWidth);
            self.size = (CGSize){value,value*self.h_w_scale};
            self.right = cRight;
            self.bottom = cBottom;
        }
    } else {
        if (self.rotate == 0) {
            value = MAX(self.startFrame.size.height+ylength,minHeight);
            self.size = (CGSize){value/self.h_w_scale,value};
            self.top = cTop;
            self.left = cLeft;
        } else if (self.rotate == 270) {
            value = MAX(self.startFrame.size.height-ylength,minHeight);
            self.size = (CGSize){value*self.h_w_scale,value};
            self.bottom = cBottom;
            self.left = cLeft;
        } else if (self.rotate == 90) {
            value = MAX(self.startFrame.size.height+ylength,minHeight);
            self.size = (CGSize){value*self.h_w_scale,value};
            self.top = cTop;
            self.right = cRight;
        } else if (self.rotate == 180) {
            value = MAX(self.startFrame.size.height-ylength,minHeight);
            self.size = (CGSize){value/self.h_w_scale,value};
            self.right = cRight;
            self.bottom = cBottom;
        }
    }
    [self reloadImage];
    
    if (self.delegate && [self.delegate respondsToSelector:@selector(elementViewFrameShouldChange:)]) {
        [self.delegate elementViewFrameShouldChange:self];
    }
}

#pragma mark -- lazy
- (UIImageView *)imageView {
    if (!_imageView) {
        _imageView = [[UIImageView alloc] init];
        // 最近邻居算法，图像放大不模糊
        _imageView.layer.magnificationFilter = kCAFilterNearest;
    }
    return _imageView;
}

- (CGFloat)minWidth_mm {
    return 3;
}

- (CGFloat)minHeight_mm {
    return 1;
}

- (CGFloat)maxWidth_mm {
    return 200;
}

@end
