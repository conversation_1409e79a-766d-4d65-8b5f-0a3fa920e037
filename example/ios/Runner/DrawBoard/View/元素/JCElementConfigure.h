//
//  JCElementConfigure.h
//  XYFrameWork
//
//  Created by xingling xu on 2019/11/13.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#ifndef JCElementConfigure_h
#define JCElementConfigure_h

/** weak  strong */
#define JCWeakSelf          __weak      typeof(self)     weakSelf = self;
#define JCStrongSelf        __strong    typeof(weakSelf) self     = weakSelf;

#define JCKEY_Element           @"element"
#define JCKEY_Mutable           @"mutable"
#define JCKEY_MutableElements   @"elements"

/** 获取当前value 位于 min 和  max 之间的值 ，画板缩放判断宽高用 */
static inline CGFloat CGFloatBetween(CGFloat value,CGFloat min,CGFloat max) {
    return MIN(max, MAX(min, value));
}

/** excel导入后文本显示占位分隔符 */
static NSString *const excel_component = @"⊙";
/** 文本默认占位字符串 */
static NSString *const element_text_placeholder = @"双击编辑";
/** 默认字体 */
static NSString *const text_default_font_name =  @"app01149";//@"思源黑体"
/** 默认字体code */
static NSString *const text_default_font_code = @"ZT001";
/** 虚线间隙  单位：毫米 */
static CGFloat const dash_line_default_width = 0.75;
/** 默认线宽  单位：毫米*/
static CGFloat const line_default_width = 0.4;

static CGFloat const text_default_font_mm_size = 4.9;

/** 撤销、恢复相关key */
static  NSString *k_element = @"k_element";                             // 元素
static  NSString *k_muti_elements = @"k_muti_elements";                 // 多选元素
static  NSString *k_action = @"k_action";                               // 动作
static  NSString *k_add = @"k_add";                                     // 添加元素
static  NSString *k_delete = @"k_delete";                               // 删除元素
static  NSString *k_frame = @"k_frame";                                 // 位置大小
static  NSString *k_rotate = @"k_rotate";                               // 旋转角度
static  NSString *k_text = @"k_text";                                   // 文本内容
static  NSString *k_fontname = @"k_fontname";                           // 字体名称
static  NSString *k_fontcode = @"k_fontcode";                           // 字体code
static  NSString *k_font = @"k_font";                                   // 字体大小
static  NSString *k_bold = @"k_bold";                                   // 粗体
static  NSString *k_underline = @"k_underline";                         // 下划线
static  NSString *k_italic = @"k_italic";                               // 斜体
static  NSString *k_textalignment = @"k_textalignment";                 // 文本内容对齐方式
static  NSString *k_textverticalalignment = @"k_textverticalalignment"; // 文本内容垂直对齐方式
static  NSString *k_linespace = @"k_linespace";                         // 文本行间距
static  NSString *k_wordspace = @"k_wordspace";                         // 文本字间距
static  NSString *k_barcodename = @"k_barcodename";                     // 条码类型
static  NSString *k_barcodestyle = @"k_barcodestyle";                   // 条码样式
static  NSString *k_linetype = @"k_linetype";                           // 线条类型
static  NSString *k_linewidth = @"k_linewidth";                         // 线宽
static  NSString *k_shape = @"k_shape";                                 // 线宽
static  NSString *k_sheet_action = @"k_sheet_action";                   // 表格添加行、添加列、删除行、删除列
static  NSString *k_sheet_grid = @"k_sheet_grid";                       // 表格大小缩放
static  NSString *k_sheet_rowheight = @"k_sheet_rowheight";             // 表格行高
static  NSString *k_sheet_colwidth = @"k_sheet_colwidth";               // 表格列宽
static  NSString *k_image_threshold = @"k_image_threshold";             // 图片阈值
static  NSString *k_sheet_selectindex = @"k_sheet_selectindexpath";     // 表格选中的索引
static  NSString *k_add_muti_elements = @"k_add_muti_elements";         // 添加多个元素
static  NSString *k_delete_muti_elements = @"k_delete_muti_elements";   // 删除多个元素

// 条码的所有类型匹配
#define bar_code_types     @{@"20":@"Code128",  \
                             @"21":@"UPC-A",    \
                             @"22":@"UPC-E",    \
                             @"23":@"EAN-8",     \
                             @"24":@"EAN-13",    \
                             @"25":@"Code93",   \
                             @"26":@"Code39",   \
                             @"27":@"CodeBar",  \
                             @"28":@"ITF25"}
// 二维码的所有类型匹配
#define qr_code_types     @{@"31":@"QR_CODE",       \
                            @"32":@"PDF417",        \
                            @"33":@"DATA_MATRIX",   \
                            @"34":@"AZTEC"}

/** 字体大小集合 */
#define font_size_array  @[@"14",@"16",@"18",@"20",@"22",@"24",@"26",@"28",@"30",@"32",@"34",@"36",@"40",@"44",@"48",@"58",@"64",@"70",@"84",@"98",@"126",@"146",@"158",@"170",@"194",@"248"]


/** _row_0_column_0_ */
#define STKEY(row,column) [NSString stringWithFormat:@"_row_%ld_column_%ld_",(long)row,(long)column]

/** JCElementType类型匹配  */
typedef enum : NSUInteger {
    /** 文本 */
    JCTemplateType_Text = 0,
    /** 条码 */
    JCTemplateType_BarCode,
    /** 二维码 */
    JCTemplateType_QRCode,
    /** 边框 */
    JCTemplateType_Graph,
    /** 表格 */
    JCTemplateType_Table,
    /** 线条 */
    JCTemplateType_Line,
    /** 图片 */
    JCTemplateType_Image,
    /** 时间 */
    JCTemplateType_Date,
    /** 序列号 */
    JCTemplateType_SerialNumber,
} JCTemplateType;/** 元素类型 */


typedef enum : NSUInteger {
    /** 添加文本 */
    JCAddAction_Text,
    /** 添加一维码 */
    JCAddAction_Barcode,
    /** 添加二维码 */
    JCAddAction_Qrcode,
    /** 添加表格 */
    JCAddAction_Table,
    /** 添加图片 */
    JCAddAction_Image,
    /** 添加扫描 */
    JCAddAction_Scan,
    /** 添加流水号 */
    JCAddAction_Serial,
    /** 添加Excel导入 */
    JCAddAction_ExcelImport,
    /** 添加日期 */
    JCAddAction_Date,
    /** 添加形状 */
    JCAddAction_Graph,
    /** 添加Logo素材 */
    JCAddAction_Logo,
    /** 添加线条 */
    JCAddAction_Line,
    /** 添加智能识别 */
    JCAddAction_Ocr,
    /** 选择模板 */
    JCAddAction_Template,
    /** 图标 */
    JCAddAction_Icon,
    /** 边框 */
    JCAddAction_Border,
} JCAddAction;/** 画板添加元素面板类型 */

typedef enum : NSUInteger {
    /** 清空 */
    JCElementAction_Clear = 1,
    /** 删除 */
    JCElementAction_Delete,
    /** 旋转 */
    JCElementAction_Rotate,
    /** 单选 */
    JCElementAction_SigleSelect,
    /** 多选 */
    JCElementAction_Mutiple,
    /** 可撤销 */
    JCElementAction_Undo,
    /** 没有可撤销 */
    JCElementAction_NoUndo,
    /** 恢复 */
    JCElementAction_Redo,
    /** 没有可恢复 */
    JCElementAction_NoRedo,
    /** 复制 */
    JCElementAction_Copy,
    /** 锁定 */
    JCElementAction_Lock,
    /** un锁定 */
    JCElementAction_UnLock,
    /** 展示主面板 */
    JCElementAction_ShowMain,
    /** 画板复位 */
    JCElementAction_Reset
} JCElementAction;/** 元素类型 */

typedef NS_ENUM(NSUInteger, NumberEditType) {
    /** 前缀 */
    NumberEditType_Prefix = 1,
    /** 后缀 */
    NumberEditType_Suffix,
    /** 起始 */
    NumberEditType_Start,
    /** 递增 */
    NumberEditType_Increase,
};

// Note：数据跟服务器对应，不可修改
typedef NS_ENUM(NSUInteger, JCGraphType) {
    /** 圆形 */
    JCGraphTypeCircle = 1,
    /** 椭圆 */
    JCGraphTypeOval,
    /** 矩形 */
    JCGraphTypeRect,
    /** 圆角矩形 */
    JCGraphTypeRectRound,
};

typedef NS_ENUM(NSUInteger, JCLineStyle) {
    /** 实线 */
    JCLineStyleSolid = 1,
    /** 虚线类型1 */
    JCLineStyleDotted1 = 2,
    /** 虚线类型2 */
    JCLineStyleDotted2 = 3,
};

typedef NS_ENUM(NSUInteger, BarCodeTextLocation) {
    BartTextLocationBottom = 0,
    BartTextLocationTop = 1,
    BartTextLocationNone = 2,
};

typedef NS_ENUM(NSUInteger, JCSheetAction) {
    /** nothing */
    JCSheetActionNone,
    /** 添加一行 */
    JCSheetActionAddRow,
    /** 添加一列 */
    JCSheetActionAddColumn,
    /** 删除一行 */
    JCSheetActionDeleteRow,
    /** 删除一列 */
    JCSheetActionDeleteColumn,
    /** 合并 */
    JCSheetActionMerge,
    /** 拆分 */
    JCSheetActionSplit,
};


typedef NS_OPTIONS(NSUInteger, JCFontStyle) {
    /** 默认，无 */
    JCFontStyleNone        = 0,
    /** 粗体 */
    JCFontStyleBold        = 1 << 0,
    /** 斜体 */
    JCFontStyleItalic      = 1 << 1,
    /** 下划线 */
    JCFontStyleUnderLine   = 1 << 2,
};


/** 换行模式 */
typedef NS_ENUM(NSUInteger, JCTextLineMode) {
    /** 宽高固定，内容大小自适应 */
    JCTextLineModeSizeFixed = 1,
    /** 宽度固定，高度自适应 */
    JCTextLineModeWidthFixed = 2,
    /** 宽高固定，超出内容后面加... */
    JCTextLineModeHeightFixed = 3,
    /** 宽高固定,超出内容直裁切 */
    JCTextLineModeDotTail = 4,
};

typedef NS_ENUM(NSUInteger, JCFontVertical) {
    /** 文字靠上 */
    VerticalTop = 0,
    /** 默认居中 */
    VerticalCenter,
    /** 文字靠下 */
    VerticalBottom,
};

typedef enum : NSUInteger {
    LocationTop = 0,
    LocationBottom = 1,
} EditViewLocation;

// 条码  二维码 编码类型
typedef enum : NSUInteger {
    //- BarCodeType
    JCCodeType_CODE128 = 20,
    JCCodeType_UPCA = 21,
    JCCodeType_UPCE = 22,
    JCCodeType_EAN8 = 23,
    JCCodeType_EAN13 = 24,
    JCCodeType_CODE93 = 25,
    JCCodeType_CODE39 = 26,
    JCCodeType_CODEBAR = 27,
    JCCodeType_ITF25 = 28,
    // - QRCode Type
    JCCodeType_QRCODE = 31,
    JCCodeType_PDF417 = 32,
    JCCodeType_DATAMATRIX = 33,
    JCCodeType_AZTEC = 34,
} JCCodeType;

// 模板本地保存状态
typedef enum : NSUInteger {
    // 默认刚进来的模板数据：对应首页云图标
    JCLocalType_Default,
    // 离线创建数据:对应首页手机图标
    JCLocalType_OffLineCreate,
    // 离线更新数据:对应首页手机图标
    JCLocalType_OffLineUpdate,
    // 离线删除数据:不展示，数据库记录
    JCLocalType_OffLineDelete,
    // 已经同步:所有数据下载完毕，对应首页无图标
    JCLocalType_Sync,
} JCLocalType;

#endif /* JCElementConfigure_h */
