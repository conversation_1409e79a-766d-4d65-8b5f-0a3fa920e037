//
//  JCElementGraph.m
//  XYFrameWork
//
//  Created by xingling xu on 2020/5/18.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import "JCElementGraph.h"
#import "JCElementToItem.h"
#import "JCElementModel+Transfer.h"

@interface JCElementGraph ()
@property (nonatomic, strong) UIImageView *imageView;
@property (nonatomic, assign) CGPoint startPoint;
@property (nonatomic, assign) CGRect  startFrame;
@property (nonatomic, assign) CGFloat beforeWidth;
@end

@implementation JCElementGraph

- (instancetype)init{
    self = [super init];
    if (self) {
        [self addSubview:self.imageView];
        [self.imageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.top.width.height.equalTo(self);
        }];
    }
    return self;
}


- (void)reloadSDKImage {
    [self reloadGraphImage];
}

- (void)reloadGraphImage {
    if (self.configure.graphType ==  JCGraphTypeCircle) {
        self.beforeWidth = self.width;// 记忆一下之前的宽度，便于手误返回
        self.size = (CGSize){self.height,self.height};
    } else {
        if (self.beforeWidth > 0) {
            self.width = self.beforeWidth;
        }
    }
    JCElementModel *model = [JCElementToItem itemForElement:self];
    // 因为本地获取的都是正常方向的图片，所以此处单独获取某个元素的图片设值旋转角度0，旋转动作由本地元素完成
    model.rotate = 0;
    NSMutableDictionary *dict = [model.toSdk(YES) elementDict].mutableCopy;
    NSMutableDictionary *params = [NSMutableDictionary dictionaryWithCapacity:2];
    [params setObject:@[dict] forKey:@"elements"];
    NSString *json = [params dc_toJSONString];
    NSInteger screenScale = [UIScreen mainScreen].scale;
    UIImage *image = nil;//[JCAPI generateGraphPreviewImage:json displayMultiple:DrawBoardInfo.mm2pxScale*screenScale];
    self.imageView.image = image;
    [self setNeedsLayout];
}

#pragma mark - setter
- (void)setConfigure:(JCEleBoxConfigure *)configure {
    _configure = configure;
    [self reloadGraphImage];
}

#pragma makr - 由于等比缩放重写父类方法
- (void)zoomDragBegin: (UIControl *) c withEvent:touches
{
    if (self.isLock) return;
    UITouch *touch = [[touches allTouches] anyObject];
    CGPoint point = [touch locationInView:self.superview];
    self.startPoint = point;
    self.startFrame = self.frame;
    if (self.delegate && [self.delegate respondsToSelector:@selector(elementViewDragBegin:)]) {
        [self.delegate elementViewDragBegin:self];
    }
}

- (void)zoomDragOutside: (UIControl *) c withEvent:touches
{
    // override do nothing
}

- (void)zoomDragMoving: (UIControl *) c withEvent:touches
{
    if (self.configure.graphType ==  JCGraphTypeCircle) {
        [self equalMoveWithEvents:touches];
    } else {
        [super zoomDragMoving:c withEvent:touches];
    }
    self.beforeWidth = 0;
    [self reloadGraphImage];
    [self setNeedsDisplay];
}

- (void)equalMoveWithEvents:touches {
    if (self.isLock || self.zoomButton.hidden) return;
    @autoreleasepool {
        UITouch *touch = [[touches allTouches] anyObject];
        CGPoint point = [touch locationInView:self.superview];
        CGFloat xlength = point.x - self.startPoint.x; //等比缩放以x移动距离作为基准
        CGFloat startWidth = self.startFrame.size.width;
        CGFloat minWidth = [self minWidth_mm]*DrawBoardInfo.mm2pxScale;
        CGFloat value = 0;
        CGFloat cLeft = self.startFrame.origin.x,cTop =self.startFrame.origin.y,cRight = cLeft+self.startFrame.size.width,cBottom = cTop+self.startFrame.size.height;
        if (self.rotate == 0) {
            value = MAX(startWidth+xlength,minWidth);
            self.size = (CGSize){value,value};
            self.top = cTop;
            self.left = cLeft;
        } else if (self.rotate == 270) {
            value = MAX(startWidth+xlength,minWidth);
            self.size = (CGSize){value,value};
            self.bottom = cBottom;
            self.left = cLeft;
        } else if (self.rotate == 90) {
            value = MAX(startWidth-xlength,minWidth);
            self.size = (CGSize){value,value};
            self.top = cTop;
            self.right = cRight;
        } else if (self.rotate == 180) {
            value = MAX(startWidth-xlength,minWidth);
            self.size = (CGSize){value,value};
            self.right = cRight;
            self.bottom = cBottom;
        }
    }
    if (self.delegate && [self.delegate respondsToSelector:@selector(elementViewFrameShouldChange:)]) {
        [self.delegate elementViewFrameShouldChange:self];
    }
}

#pragma mark - lazy
- (UIImageView *)imageView {
    if (!_imageView) {
        _imageView = [[UIImageView alloc] init];
        _imageView.userInteractionEnabled = YES;
    }
    return _imageView;
}


- (CGFloat)minWidth_mm {
    CGFloat minW = 1;
    if (self.configure.graphType ==  JCGraphTypeRectRound) {
        minW = 4;
    }
    return minW;
}

- (CGFloat)minHeight_mm {
    CGFloat minW = 1;
    if (self.configure.graphType ==  JCGraphTypeRectRound) {
        minW = 4;
    }
    return minW;
}

/** 元素最大宽度---子类实现 毫米 */
- (CGFloat)maxWidth_mm {
    return 300;
}

/** 元素最大高度---子类实现 毫米 */
- (CGFloat)maxHeight_mm {
    return 300;
}
@end
