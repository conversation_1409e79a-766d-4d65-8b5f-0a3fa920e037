//
//  JCEleBoxConfigure.h
//  XYFrameWork
//
//  Created by xingling xu on 2019/11/26.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import <Foundation/Foundation.h>
#import "JCElementConfigure.h"

NS_ASSUME_NONNULL_BEGIN

@interface JCEleBoxConfigure : NSObject
/** 形状 */
@property (nonatomic, assign) JCGraphType graphType;
/** 线宽 */
@property (nonatomic, assign) CGFloat lineWidth;
/** 单独保存线宽的毫米数值，为了匹配和精度的保证 */
@property (nonatomic, assign) CGFloat mmLineWidth;
/** 边框圆角半径 */
@property (nonatomic, assign) CGFloat cornerRadius;
/** 实线  or  虚线 */
@property (nonatomic, assign) JCLineStyle lineStyle;

+ (JCEleBoxConfigure *)defaultConfigure;
@end

NS_ASSUME_NONNULL_END
