//
//  JCElementBox.m
//  XYFrameWork
//
//  Created by xingling xu on 2019/11/26.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCElementBox.h"

@interface JCElementBox ()
@property (nonatomic, assign) CGPoint startPoint;
@property (nonatomic, assign) CGRect  startFrame;
@property (nonatomic, assign) CGFloat beforeWidth;
@end

@implementation JCElementBox
- (instancetype)init {
    if (self = [super init]) {
        self.backgroundColor = [UIColor clearColor];
    }
    return self;
}

- (void)setConfigure:(JCEleBoxConfigure *)configure {
    _configure = configure;
    /** 圆形以高为基准，除非缩放 */
    if (configure.graphType ==  JCGraphTypeCircle) {
        self.beforeWidth = self.width;// 记忆一下之前的宽度，便于手误返回
        self.size = (CGSize){self.height,self.height};
    } else {
        if (self.beforeWidth > 0) {
            self.width = self.beforeWidth;
        }
    }
    [self setNeedsDisplay];
}

- (void)drawRect:(CGRect)rect {
    float lineWidth = self.configure.lineWidth;
    if(lineWidth >= self.width/2){
        lineWidth = self.width/2;
    }
    CGFloat width = 0;
    CGFloat height = 0;
    if(self.rotate == 270 || self.rotate == 90){
        width = self.height-lineWidth;
        height = self.width-lineWidth;
    } else{
        width = self.width-lineWidth;
        height = self.height-lineWidth;
    }
    CGContextRef context = UIGraphicsGetCurrentContext();
    CGContextSetLineWidth(context, lineWidth);//线的宽度
    [[UIColor blackColor] set];
    
    /** 形状 */
    JCGraphType graphType = self.configure.graphType;
    /** 实线虚线 */
    JCLineStyle lineStyle = self.configure.lineStyle;
    if (lineStyle == JCLineStyleSolid) {
        CGFloat length[] = {0,0};
        CGContextSetLineDash(context, 0, length, 2);
    } else {
        CGFloat length[] = {5,5};
        CGContextSetLineDash(context, 0, length, 2);
    }
    if (graphType == JCGraphTypeOval || graphType == JCGraphTypeCircle) {
        CGRect drawRect = CGRectMake(lineWidth/2, lineWidth/2, width, height);
        CGContextAddEllipseInRect(context, drawRect);
        CGContextStrokePath(context);
    } else if (graphType == JCGraphTypeRectRound) { //圆角框
        CGContextSetStrokeColorWithColor(context, [UIColor blackColor].CGColor);
        CGContextSetFillColorWithColor(context, [UIColor clearColor].CGColor);
        CGFloat x = lineWidth/2;
        CGFloat y = lineWidth/2;
        CGContextMoveToPoint(context, x+width, y+8/2);
        CGContextAddArcToPoint(context, x+width, y+height, x+width-8/2, y+height, 8);
        CGContextAddArcToPoint(context, x, y+height, x, y+height-8/2, 8);
        CGContextAddArcToPoint(context, x, y, x+8/2, y, 8);
        CGContextAddArcToPoint(context, x+width, y, x+width, y+8/2, 8);
        CGContextDrawPath(context, kCGPathFillStroke);
    } else { //直角框
        CGContextSetFillColorWithColor(context, [UIColor clearColor].CGColor);
        CGContextSetStrokeColorWithColor(context, [UIColor blackColor].CGColor);//线框颜色
        CGContextAddRect(context,CGRectMake(lineWidth/2, lineWidth/2, width, height));
        CGContextDrawPath(context, kCGPathFillStroke);
    }
}

#pragma makr - 由于等比缩放重写父类方法
- (void)zoomDragBegin: (UIControl *) c withEvent:touches
{
    if (self.isLock) return;
    UITouch *touch = [[touches allTouches] anyObject];
    CGPoint point = [touch locationInView:self.superview];
    self.startPoint = point;
    self.startFrame = self.frame;
    JCPerformSelectorWithOneObject((id)self.delegate,@selector(elementViewDragBegin:), self)
}

- (void)zoomDragOutside: (UIControl *) c withEvent:touches
{
    // override do nothing
}

- (void)zoomDragMoving: (UIControl *) c withEvent:touches
{
    if (self.configure.graphType ==  JCGraphTypeCircle) {
        [self equalMoveWithEvents:touches];
    } else {
        [super zoomDragMoving:c withEvent:touches];
    }
    self.beforeWidth = 0;
    [self setNeedsDisplay];
}

- (void)equalMoveWithEvents:touches {
    if (self.isLock || self.zoomButton.hidden) return;
    @autoreleasepool {
        UITouch *touch = [[touches allTouches] anyObject];
        CGPoint point = [touch locationInView:self.superview];
        CGFloat xlength = point.x - self.startPoint.x; //等比缩放以x移动距离作为基准
        CGFloat startWidth = self.startFrame.size.width;
        CGFloat minWidth = [self minWidth_mm];
        CGFloat value = 0;
        CGFloat cLeft = self.startFrame.origin.x,cTop =self.startFrame.origin.y,cRight = cLeft+self.startFrame.size.width,cBottom = cTop+self.startFrame.size.height;
        if (self.rotate == 0) {
            value = MAX(startWidth+xlength,minWidth);
            self.size = (CGSize){value,value};
            self.top = cTop;
            self.left = cLeft;
        } else if (self.rotate == 270) {
            value = MAX(startWidth+xlength,minWidth);
            self.size = (CGSize){value,value};
            self.bottom = cBottom;
            self.left = cLeft;
        } else if (self.rotate == 90) {
            value = MAX(startWidth-xlength,minWidth);
            self.size = (CGSize){value,value};
            self.top = cTop;
            self.right = cRight;
        } else if (self.rotate == 180) {
            value = MAX(startWidth-xlength,minWidth);
            self.size = (CGSize){value,value};
            self.right = cRight;
            self.bottom = cBottom;
        }
    }
    JCPerformSelectorWithOneObject((id)self.delegate,@selector(elementViewFrameShouldChange:), self)
}
@end
