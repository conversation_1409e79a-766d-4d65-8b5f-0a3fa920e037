
//
//  JCEleBoxConfigure.m
//  XYFrameWork
//
//  Created by xingling xu on 2019/11/26.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCEleBoxConfigure.h"

@implementation JCEleBoxConfigure

+ (JCEleBoxConfigure *)defaultConfigure {
    JCEleBoxConfigure *configure = [[JCEleBoxConfigure alloc] init];
    
    configure.mmLineWidth = line_default_width;
    configure.lineWidth = DrawBoardInfo.mm2pxScale*line_default_width;
    configure.graphType = JCGraphTypeRect;
    configure.lineStyle = JCLineStyleSolid;
    configure.cornerRadius = 2;
    return configure;
}
@end
