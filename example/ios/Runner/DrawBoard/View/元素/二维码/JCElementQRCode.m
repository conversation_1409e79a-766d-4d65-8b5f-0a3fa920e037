//
//  JCElementQRCode.m
//  XYFrameWork
//
//  Created by xingling xu on 2019/11/26.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCElementQRCode.h"
#import "JCDrawCodeManager.h"

/** 勾股定理，直角边为a和b算出斜边长度 */
#define Hypotenuse(a,b)  sqrtf(fabs(a)*fabs(a)+fabs(b)*fabs(b))

@interface JCElementQRCode ()
@property (nonatomic, strong) UIImageView *qrCodeImageView;
@property (nonatomic, assign) CGPoint startPoint;
@property (nonatomic, assign) CGRect  startFrame;
@end

@implementation JCElementQRCode

- (instancetype)init {
    if (self = [super init]) {
        [self initUI];
    }
    return self;
}

- (void)initUI {
    [self addSubview:self.qrCodeImageView];
}

- (void)layoutSubviews {
    [super layoutSubviews];
    self.qrCodeImageView.frame = self.bounds;
}

- (void)reloadSDKImage {
    if (self.codeType != JCCodeType_PDF417) {
        CGFloat minWidth = MIN(self.width, self.height);
        self.size = (CGSize){minWidth,minWidth};
    }
    NSString *value = [DrawBoardInfo currentValueFor:self.excelValue selectRowIndex:self.excelIndex elementId:self.elementId];
    if (IsExcelString(self.excelValue)) {
        _qrCodeString = value;
    }
    [self reloadImage];
}

- (void)reloadImage {
    [JCDrawCodeManager drawQRCodeWithElement:self result:^(BOOL success, UIImage * _Nonnull image) {
        if (image) {
            self.qrCodeImageView.image = image;
        }
    }];
}

#pragma mark -- setting
- (void)setQrCodeString:(NSString *)qrCodeString {
    _qrCodeString = qrCodeString;
    [self reloadImage];
}

- (void)setCodeType:(JCCodeType)codeType {
    _codeType = codeType;
    [self reloadSDKImage];
}

#pragma mark -- set
- (void)setExcelIndex:(NSInteger)excelIndex {
    [super setExcelIndex:excelIndex];
    [self reloadSDKImage];
}

#pragma makr - 由于等比缩放重写父类方法
- (void)zoomDragBegin: (UIControl *) c withEvent:touches
{
    if (self.isLock || self.zoomButton.hidden) return;
    UITouch *touch = [[touches allTouches] anyObject];
    CGPoint point = [touch locationInView:self.superview];
    self.startPoint = point;
    self.startFrame = self.frame;
    JCPerformSelectorWithOneObject((id)self.delegate,@selector(elementViewDragBegin:), self)
}

- (void)zoomDragOutside: (UIControl *) c withEvent:touches
{
    // override do nothing
}

- (void)zoomDragMoving: (UIControl *) c withEvent:touches
{
    if (self.codeType ==  JCCodeType_PDF417) {
        [super zoomDragMoving:c withEvent:touches];
    } else {
        [self equalMoveWithEvents:touches];
    }
    [self reloadSDKImage];
    [self setNeedsDisplay];
}

- (void)equalMoveWithEvents:touches {
    if (self.isLock || self.zoomButton.hidden) return;
    @autoreleasepool {
        UITouch *touch = [[touches allTouches] anyObject];
        CGPoint point = [touch locationInView:self.superview];
        CGFloat xlength = point.x - self.startPoint.x; //等比缩放以x移动距离作为基准
        CGFloat value = 0;
        CGFloat startWidth = self.startFrame.size.width;
        CGFloat cLeft = self.startFrame.origin.x,cTop =self.startFrame.origin.y,cRight = cLeft+self.startFrame.size.width,cBottom = cTop+self.startFrame.size.height;
        if (self.rotate == 0) {
            value = MAX(startWidth+xlength,[self minWidth]);
            self.size = (CGSize){value,value};
            self.top = cTop;
            self.left = cLeft;
        } else if (self.rotate == 270) {
            value = MAX(startWidth+xlength,[self minWidth]);
            self.size = (CGSize){value,value};
            self.bottom = cBottom;
            self.left = cLeft;
        } else if (self.rotate == 90) {
            value = MAX(startWidth-xlength,[self minWidth]);
            self.size = (CGSize){value,value};
            self.top = cTop;
            self.right = cRight;
        } else if (self.rotate == 180) {
            value = MAX(startWidth-xlength,[self minWidth]);
            self.size = (CGSize){value,value};
            self.right = cRight;
            self.bottom = cBottom;
        }
    }
    JCPerformSelectorWithOneObject((id)self.delegate,@selector(elementViewFrameShouldChange:), self)
    [self reloadImage];
}

#pragma mark -- lazy
- (UIImageView *)qrCodeImageView {
    if (!_qrCodeImageView ) {
        _qrCodeImageView = [[UIImageView alloc] init];
        // 最近邻居算法，图像放大不模糊
        _qrCodeImageView.layer.magnificationFilter = kCAFilterNearest;
    }
    return _qrCodeImageView;
}

#pragma mark -- FrameProtocol
- (CGFloat)minWidth {
    return 3*DrawBoardInfo.mm2pxScale;
}

- (CGFloat)minHeight {
    return 3*DrawBoardInfo.mm2pxScale;
}

@end
