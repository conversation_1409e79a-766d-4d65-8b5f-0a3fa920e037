
//
//  JCDrawCodeManager.m
//  XYFrameWork
//
//  Created by xingling xu on 2019/11/22.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCDrawCodeManager.h"
#import "JCElementModel+Transfer.h"

// 二维码的所有类型匹配
#define bar_code_error_msg     @{@"-1":@"不支持的条码类型",         \
                                 @"0":@"正常",                    \
                                 @"1":@"长度不符合要求",            \
                                 @"2":@"存在非支持字符",            \
                                 @"3":@"存在非支持字符且长度不对",    \
                                 @"4":@"字符串为Null",             \
}

@implementation JCDrawCodeManager

+ (JCBarcodeMode)convertCodeType:(JCCodeType)codeType {
    JCBarcodeMode mode;
    if (codeType == JCCodeType_CODE128) {
        mode = JCBarcodeFormatCode128;
    } else if (codeType == JCCodeType_UPCA) {
        mode = JCBarcodeFormatUPCA;
    } else if (codeType == JCCodeType_UPCE) {
        mode = JCBarcodeFormatUPCE;
    } else if (codeType == JCCodeType_EAN8) {
        mode = JCBarcodeFormatEan8;
    } else if (codeType == JCCodeType_EAN13) {
        mode = JCBarcodeFormatEan13;
    } else if (codeType == JCCodeType_CODE93) {
        mode = JCBarcodeFormatCode93;
    } else if (codeType == JCCodeType_CODE39) {
        mode = JCBarcodeFormatCode39;
    } else if (codeType == JCCodeType_CODEBAR) {
        mode = JCBarcodeFormatCodebar;
    } else if (codeType == JCCodeType_ITF25) {
        mode = JCBarcodeFormatITF;
    }
    return mode;
}

+ (void)drawBarCodeWithElement:(JCElementBaseView *)element result:(void(^)(BOOL success, UIImage *image,NSString *errorMsg))block {
    if (![element isKindOfClass:[JCElementBarCode class]]) return;
    JCElementBarCode *barCode = (JCElementBarCode *)element;
    JCCodeType codeType = barCode.configure.codeType;
    JCBarcodeMode mode = [self convertCodeType:codeType];
    NSString *errorCode = [JCAPI checkBarCode:barCode.text withBarcodeMode:mode];
    NSString *correctValue = [JCAPI dealBarCodeText:barCode.text withBarcodeMode:mode];
    NSString *errorMsg = @"";
    
    if([barCode.text stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceCharacterSet]].length == 0){
        errorMsg = @"条码内容不规范";
        correctValue = @"";
    } else {
        if (![errorCode isEqualToString:@"0"]) {
            errorMsg = bar_code_error_msg[errorCode]?bar_code_error_msg[errorCode]:@"条码内容不规范";
        }
    }

    @autoreleasepool {
        JCElementModel *model = [JCElementToItem itemForElement:element];
        // 因为本地获取的都是正常方向的图片，所以此处单独获取某个元素的图片设值旋转角度0，旋转动作由本地元素完成
        model.rotate = 0;
        model.value = correctValue;
        if (model.height == 0) {
            model.height = 10;
        }
        // 注入Excel数据
        [model injectExternalData:DrawBoardInfo.externalData task:DrawBoardInfo.task];
        NSDictionary *dict = [model.toSdk(YES).setCurrentPageIndex(DrawBoardInfo.currentPageIndex) elementDict];
        NSMutableDictionary *params = [NSMutableDictionary dictionaryWithCapacity:2];
        [params setObject:@{@"fontDefault":[NSString stringWithFormat:@"%@.ttf",text_default_font_code]} forKey:@"usedFonts"];
        [params setObject:@[dict] forKey:@"elements"];
        NSString *json = [params  dc_toJSONString];
        NSString *fontPath = [NSString stringWithFormat:@"%@/font",DocumentsFontPath];
//        [JCAPI initImageProcessing:fontPath];
        double displayMultiple = DrawBoardInfo.mm2pxScale*3;
        UIImage *image = nil;//[JCAPI generateBarcodePreviewImage:json displayMultiple:DrawBoardInfo.mm2pxScale*3];
        if (image) {
            block(YES,image,errorMsg);
        } else {
            block(NO,nil,errorMsg);
        }
    }
}

+ (void)drawQRCodeWithElement:(JCElementBaseView *)element result:(void(^)(BOOL success, UIImage *image))block {
    if (![element isKindOfClass:[JCElementQRCode class]]) return;
    @autoreleasepool {
        JCElementModel *model = [JCElementToItem itemForElement:element];
        model.rotate = 0;
        if (model.height == 0) {
            model.height = model.width;
        }
        // 注入Excel数据
        [model injectExternalData:DrawBoardInfo.externalData task:DrawBoardInfo.task];
        NSDictionary *dict = [model.toSdk(YES).setCurrentPageIndex(DrawBoardInfo.currentPageIndex) elementDict];
        NSMutableDictionary *params = [NSMutableDictionary dictionaryWithCapacity:1];
        [params setObject:@[dict] forKey:@"elements"];
        NSString *json = [params  dc_toJSONString];
        NSInteger screenScale = [UIScreen mainScreen].scale;
        UIImage *image = nil;// [JCAPI generateQrCodePreviewImage:json displayMultiple:DrawBoardInfo.mm2pxScale*screenScale];
        if (image) {
            block(YES,image);
        } else {
            block(NO,nil);
        }
    }
}

+ (UIImage *)getBarcodeImageWith:(NSString *)value codeType:(JCCodeType)type {
    CGSize size = (CGSize){240,120};
    CGFloat mm2pxScale = 1.6;
    JCElementModel *model = [JCElementModel new];
    model.type = @"barcode";
    model.value = value;
    model.codeType = type;
    model.x = 0;
    model.y = 0;
    model.width = size.width/mm2pxScale;
    model.height = size.height/mm2pxScale;
    model.rotate = 0;
    model.fontSize = 15;
    model.textHeight = size.height/(text_default_font_mm_size*mm2pxScale);
    model.textPosition = 0;
    NSDictionary *dict = [model.toSdk(YES) elementDict];
    NSMutableDictionary *params = [NSMutableDictionary dictionaryWithCapacity:2];
    [params setObject:@{@"fontDefault":[NSString stringWithFormat:@"%@.ttf",text_default_font_code]} forKey:@"usedFonts"];
    [params setObject:@[dict] forKey:@"elements"];
    NSString *json = [params  dc_toJSONString];
    NSString *fontPath = [NSString stringWithFormat:@"%@/font",DocumentsFontPath];
//    [JCAPI initImageProcessing:fontPath];
    NSInteger screenScale = [UIScreen mainScreen].scale;
    UIImage *image = nil;//[JCAPI generateBarcodePreviewImage:json displayMultiple:mm2pxScale*screenScale];
    return image;
}

+ (UIImage *)getQrcodeImageWith:(NSString *)value codeType:(JCCodeType)type {
    CGSize size = (CGSize){120,120};
    CGFloat mm2pxScale = 1.6;
    JCElementModel *model = [JCElementModel new];
    model.type = @"qrcode";
    model.value = value;
    model.codeType = type;
    model.x = 0;
    model.y = 0;
    model.width = size.width/mm2pxScale;
    model.height = size.height/mm2pxScale;
    model.rotate = 0;
    NSDictionary *dict = [model.toSdk(YES) elementDict];
    NSMutableDictionary *params = [NSMutableDictionary dictionaryWithCapacity:2];
    [params setObject:@[dict] forKey:@"elements"];
    NSString *json = [params  dc_toJSONString];
    NSInteger screenScale = [UIScreen mainScreen].scale;
    UIImage *image = nil;//[JCAPI generateQrCodePreviewImage:json displayMultiple:mm2pxScale*screenScale];
    return image;
}

@end
