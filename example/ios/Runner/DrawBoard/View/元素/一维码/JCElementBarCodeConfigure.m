
//
//  JCElementBarCodeConfigure.m
//  XYFrameWork
//
//  Created by xingling xu on 2019/11/22.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCElementBarCodeConfigure.h"

@implementation JCElementBarCodeConfigure
+ (JCElementBarCodeConfigure *)defaultConfigure {
    JCElementBarCodeConfigure *configure = [[JCElementBarCodeConfigure alloc] init];
    configure.codeType = JCCodeType_CODE128;
    configure.textLocation = BartTextLocationBottom;
    configure.textHeight = text_default_font_mm_size;
    return configure;
}

#pragma mark -- lazy default
- (BarCodeTextLocation)textLocation {
    if (!_textLocation) {
        _textLocation = BartTextLocationBottom;
    }
    return _textLocation;
}

- (JCElementTextConfigure *)textConfigure {
    if (!_textConfigure) {
        _textConfigure = [JCElementTextConfigure new];
        _textConfigure.fontName = XY_LANGUAGE_TITLE_NAMED(text_default_font_name, @"思源黑体");
        _textConfigure.fontCode = text_default_font_code;
        _textConfigure.mmFont = text_default_font_mm_size;
        _textConfigure.textSpace = 0;
        _textConfigure.textAlignment = NSTextAlignmentCenter;
        _textConfigure.lineMode = JCTextLineModeSizeFixed;
        _textConfigure.textAlignVertical = VerticalCenter;
        _textConfigure.lineSpace = 0;
    }
    return _textConfigure;
}
@end
