//
//  JCDrawCodeManager.h
//  XYFrameWork
//
//  Created by xingling xu on 2019/11/22.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import <Foundation/Foundation.h>
#import "JCElementToItem.h"

#define Code128Image(value) [JCDrawCodeManager getBarcodeImageWith:value codeType:JCCodeType_CODE128]

NS_ASSUME_NONNULL_BEGIN

@interface JCDrawCodeManager : NSObject
/**
 * 画一维码
 */
+ (void)drawBarCodeWithElement:(JCElementBaseView *)element result:(void(^)(BOOL success, UIImage *image,NSString *errorMsg))block;
/**
 * 画二维码
 */
+ (void)drawQRCodeWithElement:(JCElementBaseView *)element result:(void(^)(BOOL success, UIImage *image))block;


+ (UIImage *)getBarcodeImageWith:(NSString *)value codeType:(JCCodeType)type;

+ (UIImage *)getQrcodeImageWith:(NSString *)value codeType:(JCCodeType)type;

@end

NS_ASSUME_NONNULL_END
