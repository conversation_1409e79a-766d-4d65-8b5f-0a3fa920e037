//
//  JCElementBarCodeConfigure.h
//  XYFrameWork
//
//  Created by xingling xu on 2019/11/22.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import <Foundation/Foundation.h>
#import "JCElementTextConfigure.h"
#import "JCElementConfigure.h"

NS_ASSUME_NONNULL_BEGIN

@interface JCElementBarCodeConfigure : NSObject
/** 条码类型编码 */
@property (nonatomic, assign) JCCodeType codeType;
/** 文字位置 默认 BarCodeLocationNone */
@property (nonatomic, assign) BarCodeTextLocation textLocation;
/** 文本区域高度 */
@property (nonatomic, assign) CGFloat textHeight;
/** 条码文本属性 */
@property (nonatomic, strong) JCElementTextConfigure *textConfigure;

+ (JCElementBarCodeConfigure *)defaultConfigure;
@end

NS_ASSUME_NONNULL_END
