//
//  JCElementBarCode.m
//  XYFrameWork
//
//  Created by xingling xu on 2019/11/22.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCElementBarCode.h"
#import "JCDrawCodeManager.h"
#import "ZXingWrapper.h"

static const CGFloat mixErrorWidth = 120;

@interface JCElementBarCode ()
@property (nonatomic, strong) UIImageView *barCodeImageView;
@property (nonatomic, strong) UIButton *errorButton;
@end

@implementation JCElementBarCode

- (instancetype)init {
    if (self = [super init]) {
        self.backgroundColor = [UIColor clearColor];
        [self initUI];
    }
    return self;
}

- (void)initUI {
    [self insertSubview:self.barCodeImageView atIndex:0];
    [self.barCodeImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.size.equalTo(self);
        make.top.left.equalTo(self);
    }];
    [self.barCodeImageView addSubview:self.errorButton];
    [self.errorButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo((CGSize){116,24});
        make.center.equalTo(self.barCodeImageView);
    }];
}

- (void)reloadSDKImage {
    NSString *value = [DrawBoardInfo currentValueFor:self.excelValue selectRowIndex:self.excelIndex elementId:self.elementId];
    if (IsExcelString(self.excelValue)) {
        _text = value;
    }
    // resize
    CGFloat minWidth = [self minWidth_mm]*DrawBoardInfo.mm2pxScale;
    rotate(
           if (self.width < minWidth) {
               self.width = minWidth;
           }
           )
    [self drawBarCodeImage];
}

#pragma mark -- set
- (void)setText:(NSString *)text {
    _text = text;
    /** 根据文本画一维码 */
    [self reloadSDKImage];
}

- (void)setConfigure:(JCElementBarCodeConfigure *)configure {
    _configure = configure;
    /** 根据条码类型重新布局 */
    [self drawBarCodeImage];
}

- (void)setExcelIndex:(NSInteger)excelIndex {
    [super setExcelIndex:excelIndex];
    [self reloadSDKImage];
}

#pragma mark -- getImage
- (void)drawBarCodeImage {
    if (!self.text || !self.configure) return;
    [JCDrawCodeManager drawBarCodeWithElement:self result:^(BOOL success, UIImage * _Nonnull image, NSString * _Nonnull errorMsg) {
//        if (errorMsg && errorMsg.length > 0) {
//            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"", errorMsg)];
//        }
        self.barCodeImageView.image  = image;
        [self showErrorMsg:errorMsg];
    }];
}

#pragma mark -  DragMove
- (void)zoomDragMoving: (UIControl *) c withEvent:touches {
    [super zoomDragMoving:c withEvent:touches];
    [self drawBarCodeImage];
}

- (void)zoomDragOutside: (UIControl *) c withEvent:touches {
    [super zoomDragOutside:c withEvent:touches];
    [self drawBarCodeImage];
}

- (void)zoomDragEnded: (UIControl *) c withEvent:touches {
    [super zoomDragEnded:c withEvent:touches];
    [self drawBarCodeImage];
}

#pragma mark - error msg
- (void)showErrorMsg:(NSString *)msg {
    self.errorButton.hidden = STR_IS_NIL(msg);
    rotate(CGFloat currentWidth = self.width);
    if (currentWidth > mixErrorWidth) {
        [self.errorButton setTitle:XY_LANGUAGE_TITLE_NAMED(@"app01066", @"不符合编码规范") forState:UIControlStateNormal];
        [self.errorButton setImage:nil forState:UIControlStateNormal];
        self.errorButton.backgroundColor = XY_HEX_RGB(0xFF6658);
        [self.errorButton mas_updateConstraints:^(MASConstraintMaker *make) {
            make.size.mas_equalTo((CGSize){116,24});
        }];
    } else {
        [self.errorButton setTitle:nil forState:UIControlStateNormal];
        [self.errorButton setImage:[UIImage imageNamed:@"draw_code_error"] forState:UIControlStateNormal];
        self.errorButton.backgroundColor =[UIColor clearColor];
        [self.errorButton mas_updateConstraints:^(MASConstraintMaker *make) {
            make.size.mas_equalTo((CGSize){24,24});
        }];
    }
}

#pragma mark -- lazy
- (UIImageView *)barCodeImageView {
    if (!_barCodeImageView) {
        _barCodeImageView = [[UIImageView alloc] init];
        _barCodeImageView.contentMode = UIViewContentModeScaleToFill;
        _barCodeImageView.backgroundColor = [UIColor clearColor];
        // 最近邻居算法，图像放大不模糊
        _barCodeImageView.layer.magnificationFilter = kCAFilterNearest;
    }
    return _barCodeImageView;
}

- (UIButton *)errorButton {
    if (!_errorButton) {
        _errorButton = [UIButton buttonWithType:UIButtonTypeCustom];
        _errorButton.backgroundColor = XY_HEX_RGB(0xFF6658);
        [_errorButton setTitleColor:XY_HEX_RGB(0xffffff) forState:UIControlStateNormal];
        [_errorButton setTitle:XY_LANGUAGE_TITLE_NAMED(@"app01066", @"不符合编码规范") forState:UIControlStateNormal];
        _errorButton.titleLabel.font = MY_FONT_Regular(13);
        _errorButton.layer.cornerRadius = 12;
        _errorButton.layer.masksToBounds = YES;
    }
    return _errorButton;
}

#pragma mark -- FrameProtocol
- (CGFloat)minWidth_mm {
    if (self.configure.codeType == JCCodeType_EAN13) {
        return 13*2;
    } else if (self.configure.codeType == JCCodeType_EAN8) {
        return 7*2;
    } else if (self.configure.codeType == JCCodeType_UPCA) {
        return 11*2;
    } else if (self.configure.codeType == JCCodeType_UPCE) {
        return 7*2;
    } else if (self.configure.codeType == JCCodeType_ITF25 || self.configure.codeType == JCCodeType_CODEBAR) {
        return 2*2;
    } else {
        return MAX(self.text.length*2,2);
    }
}

- (CGFloat)minHeight_mm {
    return self.configure.textHeight + 3;
}

- (CGFloat)maxWidth_mm {
    return 200;
}

- (CGFloat)maxHeight_mm {
    return 100;
}
@end
