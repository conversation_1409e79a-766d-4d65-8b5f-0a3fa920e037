//
//  JCElementTextConfigure.h
//  XYFrameWork
//
//  Created by xingling xu on 2019/11/22.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import <Foundation/Foundation.h>
#import "JCElementConfigure.h"

NS_ASSUME_NONNULL_BEGIN

@interface JCElementTextConfigure : NSObject
/** 字体选择集合 */
@property (nonatomic, strong) NSArray *fontsArr;
/** 字体名称 */
@property (nonatomic, copy) NSString *fontName;
/** 字体code */
@property (nonatomic, copy) NSString *fontCode;
/** 字体大小 */
//@property (nonatomic, assign) CGFloat fontSize;
/** 字体大小 毫米 */
@property (nonatomic, assign) CGFloat mmFont;
/** 字体对齐 */
@property (nonatomic, assign) NSTextAlignment textAlignment;
/** 换行模式 */
@property (nonatomic, assign) JCTextLineMode lineMode;
/** 文字上下对齐方式 */
@property (nonatomic, assign) JCFontVertical textAlignVertical;
/** 字体间距 */
@property (nonatomic, assign) CGFloat textSpace;
/** 字体样式 Use like: #粗体且斜体#（ JCFontStyleBold | JCFontStyleItalic） */
@property (nonatomic, assign) JCFontStyle fontStyle;
/** 字体行距 倍率 */
@property (nonatomic, assign) CGFloat lineSpace;

+ (JCElementTextConfigure *)defaultConfigure;

- (CGFloat)minWidth;

- (UIFont *)getFont;

@end

NS_ASSUME_NONNULL_END
