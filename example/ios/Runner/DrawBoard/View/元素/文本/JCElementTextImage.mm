//
//  JCElementTextImage.m
//  XYFrameWork
//
//  Created by xingling xu on 2020/5/5.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import "JCElementTextImage.h"
#import "JCElementToItem.h"
#import "JCElementModel+Transfer.h"

@interface JCElementTextImage ()
@property (nonatomic, strong) UIImageView *imageView;
@property (nonatomic, assign) CGPoint startPoint;
@property (nonatomic, assign) CGRect  startFrame;
@property (nonatomic, assign) CGFloat imageWidth;
@end

@implementation JCElementTextImage
- (instancetype)init{
    self = [super init];
    if (self) {
        self.backgroundColor = [UIColor clearColor];
        [self addSubview:self.imageView];
        [self.imageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.top.width.height.equalTo(self);
        }];
    }
    return self;
}

- (void)reloadSDKImage {
    rotate(self.imageWidth = self.width;)
    if (IsExcelString(self.excelValue)) {
        _text = [DrawBoardInfo currentValueFor:self.excelValue selectRowIndex:self.excelIndex elementId:self.elementId];
    }
    [self reloadTextImage];
}

- (void)reloadTextImage {
    if (![self contentShow] || [self contentShow].length <= 0 || !self.configure) return;
    if (self.imageWidth == 0) {
        rotate(self.imageWidth = self.width;)
    }
    JCElementModel *model = [JCElementToItem itemForElement:self];
    // 因为本地获取的都是正常方向的图片，所以此处单独获取某个元素的图片设值旋转角度0，旋转动作由本地元素完成
    model.rotate = 0;
    model.width = self.imageWidth/DrawBoardInfo.mm2pxScale;
    // 注入Excel数据
    [model injectExternalData:DrawBoardInfo.externalData task:DrawBoardInfo.task];
    NSMutableDictionary *dict = [model.toSdk(YES).setCurrentPageIndex(DrawBoardInfo.currentPageIndex).showPlaceHolder(YES) elementDict].mutableCopy;
    // 由于此处整合序列号和时间，需要强制替换为文本格式
    [dict setObject:@"text" forKey:@"type"];
    NSMutableDictionary *params = [NSMutableDictionary dictionaryWithCapacity:2];
    [params setObject:[XYTool fonts4SDK] forKey:@"usedFonts"];
    [params setObject:@[dict] forKey:@"elements"];
    NSString *json = [params  dc_toJSONString];
    NSString *fontPath = [NSString stringWithFormat:@"%@/font",DocumentsFontPath];
//    [JCAPI initImageProcessing:fontPath];
    NSInteger screenScale = [UIScreen mainScreen].scale;
    float width = 0,height = 0;
    UIImage *image = nil;//[JCAPI generateTextPreviewImage:json displayMultiple:DrawBoardInfo.mm2pxScale*screenScale printMultiple:DrawBoardInfo.dpiScale width:&width height:&height];
    width = width/screenScale;
    height = height/screenScale;
    CGRect frame = self.frame;
    CGFloat cLeft = frame.origin.x,cTop =frame.origin.y,cRight = cLeft+frame.size.width,cBottom = cTop+frame.size.height;
    if (self.rotate == 0) {
        self.top = cTop;
        self.left = cLeft;
        self.width = width;
        self.height = height;
    } else if (self.rotate == 90) {
        self.width = height;
        self.height = width;
        self.top = cTop;
        self.right = cRight;
    } else if (self.rotate == 180) {
        self.width = width;
        self.height = height;
        self.right = cRight;
        self.bottom = cBottom;
    } else if (self.rotate == 270) {
        self.width = height;
        self.height = width;
        self.bottom = cBottom;
        self.left = cLeft;
    }
    self.imageView.image = image;
    [self setNeedsLayout];
}

- (NSString *)contentShow {
    NSString *content = self.text;
    if (!content || 0 == content.length) { content = self.placeHolder;}
    return content?content:@" ";//日期选择无,渲染空元素
}

#pragma makr - 改变大小
- (void)zoomDragBegin: (UIControl *) c withEvent:touches
{
    if (self.isLock || self.zoomButton.hidden) return;
    UITouch *touch = [[touches allTouches] anyObject];
    CGPoint point = [touch locationInView:self.superview];
    self.startPoint = point;
    self.startFrame = self.frame;
    if (self.delegate && [self.delegate respondsToSelector:@selector(elementViewDragBegin:)]) {
        [self.delegate elementViewDragBegin:self];
    }
}
- (void)zoomDragMoving: (UIControl *) c withEvent:touches
{
    [self moveWithEvents:touches];
}

- (void)zoomDragOutside: (UIControl *) c withEvent:touches
{
    [self moveWithEvents:touches];
}

- (void)moveWithEvents:touches {
    if (self.isLock || self.zoomButton.hidden) return;
    @autoreleasepool {
        UITouch *touch = [[touches allTouches] anyObject];
        CGPoint point = [touch locationInView:self.superview];
        CGFloat xlength = point.x - self.startPoint.x; //x移动距离
        CGFloat ylength = point.y - self.startPoint.y; //y移动距离
        CGFloat startWidth = self.startFrame.size.width;
        CGFloat startHeight = self.startFrame.size.height;
        CGFloat minWidth = [self minWidth_mm]*DrawBoardInfo.mm2pxScale;
        CGFloat maxWidth = [self maxWidth_mm]*DrawBoardInfo.mm2pxScale;
        if (self.rotate == 0) {
            self.imageWidth = CGFloatBetween(startWidth+xlength, minWidth, maxWidth);
        } else if (self.rotate == 270) {
            self.imageWidth = CGFloatBetween(startHeight-ylength, minWidth, maxWidth);
        } else if (self.rotate == 90) {
            self.imageWidth = CGFloatBetween(startHeight+ylength, minWidth, maxWidth);
        } else if (self.rotate == 180) {
            self.imageWidth = CGFloatBetween(startWidth-xlength, minWidth, maxWidth);
        }
    }
    [self reloadTextImage];
    if (self.delegate && [self.delegate respondsToSelector:@selector(elementViewFrameShouldChange:)]) {
        [self.delegate elementViewFrameShouldChange:self];
    }
}

#pragma mark - setter
- (void)setPlaceHolder:(NSString *)placeHolder {
    _placeHolder = placeHolder;
}

- (void)setText:(NSString *)text {
    _text = text;
    [self reloadSDKImage];
}

- (void)setConfigure:(JCElementTextConfigure *)configure {
    _configure = configure;
    [self reloadSDKImage];
}

- (void)setExcelIndex:(NSInteger)excelIndex {
    [super setExcelIndex:excelIndex];
    [self reloadSDKImage];
}

#pragma mark - lazy
- (UIImageView *)imageView {
    if (!_imageView) {
        _imageView = [[UIImageView alloc] init];
        _imageView.userInteractionEnabled = YES;
        // 最近邻居算法，图像放大不模糊
        _imageView.layer.magnificationFilter = kCAFilterNearest;
        
    }
    return _imageView;
}

- (CGFloat)minWidth_mm {
    return self.configure.mmFont;
}

- (CGFloat)minHeight_mm {
    return self.configure.mmFont;
}


- (CGFloat)maxWidth_mm {
    return 300;
}

- (CGFloat)maxHeight_mm {
    return 300;
}
@end
