//
//  JCElementTextConfigure.m
//  XYFrameWork
//
//  Created by xingling xu on 2019/11/22.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCElementTextConfigure.h"
#import "UIFont+JCCustomFont.h"

@implementation JCElementTextConfigure

+ (instancetype)defaultConfigure {
    
    //不使用上次的字体配置,而是使用默认的
//    id configureData = [[NSUserDefaults standardUserDefaults] objectForKey:@"kLastUsedTextFontConfig"];
//    JCElementTextConfigure *configure = [JCElementTextConfigure yy_modelWithJSON:configureData];
//
//    if (configure) {
//        return configure;
//    }
    
    JCElementTextConfigure *configure = [JCElementTextConfigure new];
    configure = [[JCElementTextConfigure alloc] init];
    configure.fontName = XY_LANGUAGE_TITLE_NAMED(text_default_font_name, @"思源黑体");
    configure.fontCode = text_default_font_code;
    configure.mmFont = text_default_font_mm_size;
    configure.textAlignment = NSTextAlignmentCenter;
    configure.textAlignVertical = VerticalCenter;
    configure.fontStyle = JCFontStyleNone;
    configure.lineSpace = 0.f;
    configure.textSpace = 0.f;
    configure.lineMode = JCTextLineModeWidthFixed;
    return configure;
}

- (CGFloat)minWidth {
    CGFloat width = [@"图" jk_widthWithFont:[self getFont] constrainedToHeight:1] + self.textSpace;
    return width;
}

- (UIFont *)getFont {
    CGFloat fontS = self.mmFont;
    return [UIFont jc_fontWithFontName:self.fontName fontSize:fontS italic:self.fontStyle&JCFontStyleItalic];
}

#pragma mark -- lazy
- (NSString *)fontName {
    if (!_fontName) {
        _fontName = XY_LANGUAGE_TITLE_NAMED(text_default_font_name, @"思源黑体");
    }
    return _fontName;
}
@end
