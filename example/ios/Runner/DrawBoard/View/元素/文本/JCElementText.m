//
//  JCElement_Text.m
//  XYFrameWork
//
//  Created by xingling xu on 2019/11/22.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCElementText.h"
#import "UIFont+JCCustomFont.h"

#define LineBreakMode NSLineBreakByCharWrapping

@interface JCElementText ()
@property (nonatomic, strong) UILabel *label;
@property (nonatomic, assign) CGPoint startPoint;
@property (nonatomic, assign) CGRect  startFrame;
@end

@implementation JCElementText

- (instancetype)init {
    if (self = [super init]) {
        [self initUI];
    }
    return self;
}

- (void)initUI {
    [self addSubview:self.label];
}

- (void)layoutSubviews {
    rotate(
           CGSize size = [self.label sizeThatFits:(CGSize){self.width,CGFLOAT_MAX}];
           self.height = size.height;
           self.label.frame = self.bounds;
    );
    [super layoutSubviews];// 务必放在最后执行，because：父类要根据self.frame 更新zoombtn
}

#pragma mark -- configure
- (void)setText:(NSString *)text {
    _text = text;
    [self loadContent];
}

- (void)setConfigure:(JCElementTextConfigure *)configure {
    _configure = configure;
    [self loadContent];
}

- (void)loadContent {
    /** 字体名称、大小 */
    UIFont *font = [UIFont systemFontOfSize:self.configure.mmFont*DrawBoardInfo.mm2pxScale];
    self.label.attributedText = [[NSMutableAttributedString alloc]
                                 initWithString:[self contentShow]
                                 attributes:[NSDictionary dictionaryWithObjectsAndKeys:font,NSFontAttributeName, nil]];
    
    /** 粗体，斜体适配 */
    [self layoutWithFontStyle];
    /** 字间距，行间距 */
    [self layoutSpaceSetting];
    /** 字体对齐方式 */
    self.label.textAlignment = self.configure.textAlignment;
    
    /** 此处调整宽度是为了在单列字宽的时候调整字体大小影响展示的问题 */
    if (self.width < [self minWidth_mm]) {
        self.width = [self minWidth_mm];
    }

    [self setNeedsLayout];
}

/** 行间距，字间距 */
- (void)layoutSpaceSetting {
    CGFloat textSpace = self.configure.textSpace;
    CGFloat lineSpace = self.configure.lineSpace;
    NSMutableAttributedString *attributedString = [[NSMutableAttributedString alloc] initWithAttributedString:self.label.attributedText];
    [attributedString addAttributes:@{NSKernAttributeName:@(textSpace)} range:(NSRange){0,[attributedString length]}];
    NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
    UIFont *font = self.label.font;
    CGFloat lineHeight = font.lineHeight;
    [paragraphStyle setLineSpacing:(lineSpace - 1) * lineHeight];
    paragraphStyle.lineBreakMode = LineBreakMode;
    [attributedString addAttribute:NSParagraphStyleAttributeName value:paragraphStyle range:(NSRange){0,[attributedString length]}];
    self.label.attributedText = attributedString;
}


/** 粗体，斜体适配 */
- (void)layoutWithFontStyle {
    if (![self contentShow] || [self contentShow].length <= 0) return;
    JCFontStyle style = self.configure.fontStyle;
    
    
    /** 斜体 */
    BOOL italic = style & JCFontStyleItalic;// 是否倾斜
    UIFont *font = [UIFont jc_fontWithFontName:self.configure.fontName fontSize:self.configure.mmFont*DrawBoardInfo.mm2pxScale italic:italic];
    self.label.font = font;
    

    NSMutableAttributedString *tncString = [[NSMutableAttributedString alloc] initWithAttributedString:self.label.attributedText];
    /** 包含粗体 */
    if (style & JCFontStyleBold) {
        [tncString addAttribute:NSStrokeWidthAttributeName value:@(-3) range:(NSRange){0,[tncString length]}];
    }
    
    /** 包含下划线 */
    if (style & JCFontStyleUnderLine) {
        [tncString addAttributes:@{/*NSExpansionAttributeName:@(-0.001),*/
                                   NSUnderlineStyleAttributeName:@(NSUnderlineStyleSingle),
                                   NSUnderlineColorAttributeName:[UIColor blackColor]}
                           range:(NSRange){0,[tncString length]}];
    }
    
    self.label.attributedText = tncString;
}

- (NSString *)contentShow {
    NSString *content = self.text;
    if (!content || 0 == content.length) { content = self.placeHolder;}
    return content?content:@"";
}

#pragma makr - 改变大小
- (void)zoomDragBegin: (UIControl *) c withEvent:touches
{
    if (self.isLock || self.zoomButton.hidden) return;
    UITouch *touch = [[touches allTouches] anyObject];
    CGPoint point = [touch locationInView:self.superview];
    self.startPoint = point;
    self.startFrame = self.frame;
    JCPerformSelectorWithOneObject((id)self.delegate,@selector(elementViewDragBegin:), self)
}
- (void)zoomDragMoving: (UIControl *) c withEvent:touches
{
    [self moveWithEvents:touches];
}

- (void)zoomDragOutside: (UIControl *) c withEvent:touches
{
    [self moveWithEvents:touches];
}

- (void)moveWithEvents:touches {
    if (self.isLock || self.zoomButton.hidden) return;
    @autoreleasepool {
        UITouch *touch = [[touches allTouches] anyObject];
        CGPoint point = [touch locationInView:self.superview];
        CGFloat xlength = point.x - self.startPoint.x; //x移动距离
        CGFloat ylength = point.y - self.startPoint.y; //y移动距离
        CGFloat startWidth = self.startFrame.size.width;
        CGFloat startHeight = self.startFrame.size.height;
        CGFloat minWidth = [self minWidth_mm];
        CGFloat width = 0;
        CGFloat cLeft = self.startFrame.origin.x,cTop =self.startFrame.origin.y,cRight = cLeft+self.startFrame.size.width,cBottom = cTop+self.startFrame.size.height;
        if (self.rotate == 0) {
            width = MAX(minWidth, startWidth+xlength);
            CGSize size = [self.label sizeThatFits:(CGSize){width,CGFLOAT_MAX}];
            self.size = (CGSize){width,size.height};
            self.top = cTop;
            self.left = cLeft;
        } else if (self.rotate == 270) {
            width = MAX(minWidth, startHeight-ylength);
            CGSize size = [self.label sizeThatFits:(CGSize){width,CGFLOAT_MAX}];
            self.size = (CGSize){size.height,width};
            self.bottom = cBottom;
            self.left = cLeft;
        } else if (self.rotate == 90) {
            width = MAX(minWidth, startHeight+ylength);
            CGSize size = [self.label sizeThatFits:(CGSize){width,CGFLOAT_MAX}];
            self.size = (CGSize){size.height,width};
            self.top = cTop;
            self.right = cRight;
        } else if (self.rotate == 180) {
            width = MAX(minWidth, startWidth-xlength);
            CGSize size = [self.label sizeThatFits:(CGSize){width,CGFLOAT_MAX}];
            self.size = (CGSize){width,size.height};
            self.right = cRight;
            self.bottom = cBottom;
        }
    }
    JCPerformSelectorWithOneObject((id)self.delegate,@selector(elementViewFrameShouldChange:), self)
}

#pragma mark -- lazy
- (UILabel *)label {
    if (!_label) {
        _label = [[UILabel alloc] init];
        _label.numberOfLines = 0;
        _label.backgroundColor = [UIColor clearColor];
        _label.lineBreakMode = LineBreakMode;
        _label.clipsToBounds = YES;
    }
    return _label;
}

- (CGFloat)minWidth_mm {
    return [self.configure minWidth];
}

- (CGFloat)minHeight_mm {
    return [self.configure minWidth];
}
@end
