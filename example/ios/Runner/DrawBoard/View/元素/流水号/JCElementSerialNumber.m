//
//  JCElementSerialNumber.m
//  XYFrameWork
//
//  Created by xingling xu on 2019/11/26.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCElementSerialNumber.h"

@implementation JCElementSerialNumber

#pragma mark -- set
- (void)setStyleConfigure:(JCSerialStyleConfigure *)styleConfigure {
    _styleConfigure = styleConfigure;
    [self reloadMsg:DrawBoardInfo.currentPageIndex];
}

- (void)setExcelIndex:(NSInteger)excelIndex {
    [self reloadMsg:excelIndex];
}

- (void)reloadMsg:(NSInteger)excelIndex {
    NSInteger beginNumber = _styleConfigure.beginNumber.integerValue + excelIndex*_styleConfigure.increaseNumber;
    NSString *text = StringFromInt(beginNumber);
    NSString *prefix = _styleConfigure.prefix;
    if (prefix && prefix.length > 0) {
        text = [NSString stringWithFormat:@"%@%@",prefix,text];
    }
    
    NSString *suffix = _styleConfigure.suffix;
    if (suffix && suffix.length > 0) {
        text = [NSString stringWithFormat:@"%@%@",text,suffix];
    }
    
    self.text = text;
}

@end
