//
//  JCElementBaseView.m
//  XYFrameWork
//
//  Created by xingling xu on 2019/11/22.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCElementBaseView.h"

// S-TODO 需要将相关图片 bundle绑定
static  NSString *const zoomImageName = @"element_zoom";
static  NSString *const zoomLockImageName = @"zoom_lock";
/** 拉伸按钮宽高 */
static const CGFloat editButtonWidth = 26;

@interface JCElementBaseView ()
@property (nonatomic, assign) BOOL isMoved;
@property (nonatomic, assign) CGPoint startPoint;
@property (nonatomic, assign) CGRect  startFrame;
@property (nonatomic, strong) UIView *borderView;
@end
@implementation JCElementBaseView
- (instancetype)init {
    if (self = [super init]) {
        [self initBaseUI];
    }
    return self;
}

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self initBaseUI];
    }
    return self;
}

/** init views */
- (void)initBaseUI {
    self.backgroundColor = [UIColor clearColor];
    [self addSubview:self.borderView];
    [self addSubview:self.zoomButton];
    
    [self.borderView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
    /** 双击选中手势 */
    JCWeakSelf
    UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(doubleClick:)];
//    UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc] bk_initWithHandler:^(UIGestureRecognizer *sender, UIGestureRecognizerState state, CGPoint location) {
//        if (weakSelf.delegate && [weakSelf.delegate respondsToSelector:@selector(elementViewDoubleClick:)]) {
//            [weakSelf.delegate elementViewDoubleClick:weakSelf];
//        }
//    }];
    tapGesture.numberOfTapsRequired = 2;
    [self addGestureRecognizer:tapGesture];
}

- (void)doubleClick:(UITapGestureRecognizer *)recognizer {
    if (self.delegate && [self.delegate respondsToSelector:@selector(elementViewDoubleClick:)]) {
        [self.delegate elementViewDoubleClick:self];
    }
}

- (void)layoutSubviews {
    [super layoutSubviews];
    [self bringSubviewToFront:self.zoomButton];
    rotate(self.zoomButton.center = (CGPoint){self.width,self.height};)
}

- (void)reloadSDKImage {
    
}

#pragma mark -- set
- (void)setRotate:(CGFloat)rotate {
    // 取消缩放按钮选中
    [self resetZoomButtonWitTarget:NO];
    [self.zoomButton cancelTrackingWithEvent:nil];
    JCPerformSelector(self.context.drawBoard, sel(@"hiddenGuideLine"));
    _rotate = rotate;
    [self setNeedsLayout];
    [self resetZoomButtonWitTarget:YES];
}

- (void)setSelected:(BOOL)selected {
    _selected = selected;
    /** 是否显示边框 */
    [self setBoderState];
}
- (void)setAlignPosition:(JCAlignPosition)alignPosition {
    _alignPosition = alignPosition;
}
- (void)setIsLock:(BOOL)isLock {
    _isLock = isLock;
    [self setBoderState];
}


- (void)setBoderState {
    /** 是否显示缩放按钮 */
    self.zoomButton.hidden = !self.selected;
    [self.zoomButton setImage:[UIImage imageNamed:zoomImageName] forState:UIControlStateNormal];
    self.borderView.layer.borderColor = XY_HEX_RGB(0x00a7ff).CGColor;
    if (self.selected) {
        self.borderView.hidden = NO;
        if (self.isLock) {
            // 显示锁定框
//            self.borderView.layer.borderColor = XY_HEX_RGB(0xdddddd).CGColor;
            [self.zoomButton setImage:[UIImage imageNamed:zoomLockImageName] forState:UIControlStateNormal];
        } else {
            // 非锁定框
//            self.borderView.layer.borderColor = XY_HEX_RGB(0x00a7ff).CGColor;
        }
    } else {
        self.borderView.hidden = YES;
    }
}

- (void)resetZoomButtonWitTarget:(BOOL)addTarget{
    if (addTarget) {
        [self.zoomButton addTarget:self action:@selector(zoomDragBegin:withEvent: )forControlEvents: UIControlEventTouchDown];
        [self.zoomButton addTarget:self action:@selector(zoomDragMoving:withEvent: )forControlEvents: UIControlEventTouchDragInside];
        [self.zoomButton addTarget:self action:@selector(zoomDragOutside:withEvent: )forControlEvents: UIControlEventTouchDragOutside];
        [self.zoomButton addTarget:self action:@selector(zoomDragEnded:withEvent: )forControlEvents: UIControlEventTouchUpInside];
        [self.zoomButton addTarget:self action:@selector(zoomDragOutEnded:withEvent: )forControlEvents: UIControlEventTouchUpOutside];
    } else {
        [self.zoomButton removeTarget:self action:@selector(zoomDragBegin:withEvent: )forControlEvents: UIControlEventTouchDown];
        [self.zoomButton removeTarget:self action:@selector(zoomDragMoving:withEvent: )forControlEvents: UIControlEventTouchDragInside];
        [self.zoomButton removeTarget:self action:@selector(zoomDragOutside:withEvent: )forControlEvents: UIControlEventTouchDragOutside];
        [self.zoomButton removeTarget:self action:@selector(zoomDragEnded:withEvent: )forControlEvents: UIControlEventTouchUpInside];
        [self.zoomButton removeTarget:self action:@selector(zoomDragOutEnded:withEvent: )forControlEvents: UIControlEventTouchUpOutside];
    }
}
#pragma mark - 移动
- (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event
{
    [super touchesBegan:touches withEvent:event];
    self.isMoved = NO;
    //获取所有的触摸位置
    UITouch *touch = [touches anyObject];
    CGPoint point = [touch locationInView:self.superview];
    rotate(
           self.startPoint = point;
           self.startFrame = self.frame;
           );
    JCPerformSelectorWithOneObject((id)self.delegate,@selector(elementViewTouchBegin:), self)
}

- (void)touchesMoved:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event
{
    if (self.isLock) return;
    [super touchesMoved:touches withEvent:event];
    CGPoint point = [[touches anyObject] locationInView:self.superview];
    CGFloat xlength = point.x - self.startPoint.x; //x移动距离
    CGFloat ylength = point.y - self.startPoint.y; //y移动距离
    if(xlength != 0 || ylength != 0) {self.isMoved = YES;}
    CGFloat cX = self.startFrame.origin.x + xlength > self.superview.superview.width?self.superview.superview.width:self.startFrame.origin.x + xlength;
    CGFloat cY = self.startFrame.origin.y + ylength > self.superview.superview.height?self.superview.superview.height:self.startFrame.origin.y + ylength;
    
    rotate(
           self.origin = (CGPoint){cX,cY,};
           );
    
    if (self.isMoved) {
        JCPerformSelectorWithOneObject((id)self.delegate,@selector(elementViewFrameShouldChange:), self)
    }
}

- (void)touchesEnded:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event
{
    [super touchesEnded:touches withEvent:event];
    rotate(
           if (!self.isMoved) {
               JCPerformSelectorWithOneObject((id)self.delegate,@selector(elementViewDidClick:), self)
           }
    );
    if (self.isMoved) {
        JCPerformSelectorWithOneObject((id)self.delegate,@selector(elementViewFrameDidChange:), self)
    }
    self.isMoved = NO;
}

#pragma makr - 改变大小
- (void)zoomDragBegin: (UIControl *) c withEvent:touches
{
    if (self.isLock || self.zoomButton.hidden) return;
    UITouch *touch = [[touches allTouches] anyObject];
    CGPoint point = [touch locationInView:self.superview];
    self.startPoint = point;
    self.startFrame = self.frame;
    JCPerformSelectorWithOneObject((id)self.delegate,@selector(elementViewDragBegin:), self)
}
- (void)zoomDragMoving: (UIControl *) c withEvent:touches
{
    [self moveWithEvents:touches];
}

- (void)zoomDragOutside: (UIControl *) c withEvent:touches
{
    [self moveWithEvents:touches];
}

- (void)zoomDragEnded: (UIControl *) c withEvent:touches
{
    [self dragEnd];
}

- (void)zoomDragOutEnded: (UIControl *) c withEvent:touches
{
    [self dragEnd];
}

- (void)moveWithEvents:touches {
    if (self.isLock || self.zoomButton.hidden) return;
    @autoreleasepool {
        UITouch *touch = [[touches allTouches] anyObject];
        CGPoint point = [touch locationInView:self.superview];
        CGFloat xlength = point.x - self.startPoint.x; //x移动距离
        CGFloat ylength = point.y - self.startPoint.y; //y移动距离
        CGFloat startWidth = self.startFrame.size.width;
        CGFloat startHeight = self.startFrame.size.height;
        CGFloat minWidth = [self minWidth_mm]*DrawBoardInfo.mm2pxScale;
        CGFloat minHeight = [self minHeight_mm]*DrawBoardInfo.mm2pxScale;
        CGFloat maxWidth = [self maxWidth_mm]*DrawBoardInfo.mm2pxScale;
        CGFloat maxHeight = [self maxHeight_mm]*DrawBoardInfo.mm2pxScale;
        CGFloat cLeft = self.startFrame.origin.x,cTop =self.startFrame.origin.y,cRight = cLeft+self.startFrame.size.width,cBottom = cTop+self.startFrame.size.height;
        if (self.rotate == 0) {
            CGFloat width = CGFloatBetween(startWidth+xlength, minWidth, maxWidth);
            CGFloat height = CGFloatBetween(startHeight+ylength, minHeight, maxHeight);
            self.size = (CGSize){width,height};
            self.top = cTop;
            self.left = cLeft;
        } else if (self.rotate == 270) {
            CGFloat height = CGFloatBetween(startHeight-ylength, minWidth, maxWidth);
            CGFloat width = CGFloatBetween(startWidth+xlength, minHeight, maxHeight);
            self.size = (CGSize){width,height};
            self.bottom = cBottom;
            self.left = cLeft;
        } else if (self.rotate == 90) {
            CGFloat height = CGFloatBetween(startHeight+ylength, minWidth, maxWidth);
            CGFloat width = CGFloatBetween(startWidth-xlength, minHeight, maxHeight);
            self.size = (CGSize){width,height};
            self.top = cTop;
            self.right = cRight;
        } else if (self.rotate == 180) {
            CGFloat height = CGFloatBetween(startHeight-ylength, minHeight, maxHeight);
            CGFloat width = CGFloatBetween(startWidth-xlength, minWidth, maxWidth);
            self.size = (CGSize){width,height};
            self.right = cRight;
            self.bottom = cBottom;
        }
    }
    JCPerformSelectorWithOneObject((id)self.delegate,@selector(elementViewFrameShouldChange:), self)
}

- (void)dragEnd {
    if (self.isLock) return;
    JCPerformSelectorWithOneObject((id)self.delegate,@selector(elementViewFrameDidChange:), self);
}


#pragma mark -- lazy

- (UIButton *)zoomButton {
    if (!_zoomButton) {
        _zoomButton = [UIButton buttonWithType:UIButtonTypeCustom];
        _zoomButton.size = (CGSize){editButtonWidth,editButtonWidth};
        _zoomButton.hidden = YES;
        [_zoomButton setImage:[UIImage imageNamed:zoomImageName] forState:UIControlStateNormal];
        [_zoomButton addTarget:self action:@selector(zoomDragBegin:withEvent: )forControlEvents: UIControlEventTouchDown];
        [_zoomButton addTarget:self action:@selector(zoomDragMoving:withEvent: )forControlEvents: UIControlEventTouchDragInside];
        [_zoomButton addTarget:self action:@selector(zoomDragOutside:withEvent: )forControlEvents: UIControlEventTouchDragOutside];
        [_zoomButton addTarget:self action:@selector(zoomDragEnded:withEvent: )forControlEvents: UIControlEventTouchUpInside];
        [_zoomButton addTarget:self action:@selector(zoomDragOutEnded:withEvent: )forControlEvents: UIControlEventTouchUpOutside];
    }
    return _zoomButton;
}

- (UIView *)borderView {
    if (!_borderView) {
        _borderView = [[UIView alloc] init];
        _borderView.backgroundColor = [UIColor clearColor];
        _borderView.layer.borderWidth = 1.5;
        _borderView.layer.borderColor = XY_HEX_RGB(0x00a7ff).CGColor;
        _borderView.hidden = YES;
    }
    return _borderView;
}

#pragma mark -- hitTest
-(UIView *)hitTest:(CGPoint)point withEvent:(UIEvent *)event
{
    UIView *view = [super hitTest:point withEvent:event];
    if (view == nil) {
        for (UIButton *subView in self.subviews) {
            CGPoint myPoint = [subView convertPoint:point fromView:self];
            if (CGRectContainsPoint(subView.bounds, myPoint)) {
                if ([subView isKindOfClass:[UIButton class]]) {
                    if (subView.isEnabled) {
                        return subView;
                    } else {
                        return view;
                    }
                }
            }
        }
    }
    return view;
}

#pragma mark -- 获取不旋转的frame
- (CGRect)getOrignalFrame {
    rotate(CGRect frame = self.frame;);
    return frame;
}

#pragma mark -- JCElementFrameProtocol
- (CGFloat)minWidth_mm {
    return 3;
}

- (CGFloat)minHeight_mm {
    return 3;
}

- (CGFloat)maxWidth_mm {
    return 300;
}

- (CGFloat)maxHeight_mm {
    return 300;
}

@end
