//
//  JCElementBaseView.h
//  XYFrameWork
//
//  Created by xingling xu on 2019/11/22.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import <UIKit/UIKit.h>

typedef NS_ENUM(NSUInteger, JCAlignStyle) {
    JCAlignStyleLeft = 0,
    JCAlignStyleRight,
    JCAlignStyleTop,
    JCAlignStyleBottom,
    JCAlignStyleCenterX,
    JCAlignStyleCenterY
};

typedef struct positon {
    JCAlignStyle horizontal;
    JCAlignStyle vertical;
} JCAlignPosition;

@class JCElementBaseView;
@protocol JCElementProtocol <NSObject>
@optional
- (void)elementViewTouchBegin:(JCElementBaseView *)element;
- (void)elementViewDragBegin:(JCElementBaseView *)element;
- (void)elementViewDoubleClick:(JCElementBaseView *)element;
- (void)elementViewFrameShouldChange:(JCElementBaseView *)element;
- (void)elementViewFrameDidChange:(JCElementBaseView *)element;
- (void)elementViewDidClick:(JCElementBaseView *)element;
@end

/** 适用于所有元素，先旋转到0度执行操作然后再旋转回当前角度 */
#define rotate(...)\
        [self setTransform:CGAffineTransformMakeRotation(M_PI * (0 / 180))];\
        __VA_ARGS__;\
        [self setTransform:CGAffineTransformMakeRotation(M_PI * (self.rotate / 180))];

@interface JCElementBaseView : UIView
/** 旋转角度 */
@property (nonatomic, assign) CGFloat rotate;

/// 对齐的方式
@property(nonatomic,assign)JCAlignPosition alignPosition;
/** 是否选中 */
@property (nonatomic, assign) BOOL selected;
/** 锁定 */
@property (nonatomic, assign) BOOL isLock;

@property (nonatomic, assign) NSInteger zIndex;
/** 缩放按钮 */
@property (nonatomic, strong) UIButton *zoomButton;
/** 元素的id：时间戳，精确到毫秒 */
@property (nonatomic, copy) NSString *elementId;

// -------------      Excel Import begin   -----------------
/** 关闭excel导入功能 */
@property (nonatomic, assign) BOOL closeExcelImport;
/** 导入excel后的格式化字符串类似：0⊙1 */
@property (nonatomic, copy) NSString *excelValue;
/** 导入excel后当前选择的index */
@property (nonatomic, assign) NSInteger excelIndex;
/** 是否为excel数据导入标题 */
@property (nonatomic, assign) BOOL isTitle;
// -------------       Excel Import  end    ----------------

@property (nonatomic, weak) id<JCElementProtocol> delegate;

@property(nonatomic,assign)NSInteger selectedBarIndex;

/** for override */
- (void)zoomDragMoving: (UIControl *) c withEvent:touches;
- (void)zoomDragOutside: (UIControl *) c withEvent:touches;
- (void)zoomDragEnded: (UIControl *) c withEvent:touches;
/** 元素最小宽度---子类实现 毫米 */
- (CGFloat)minWidth_mm;
/** 元素最小高度---子类实现 毫米 */
- (CGFloat)minHeight_mm;
/** 元素最大宽度---子类实现 毫米 */
- (CGFloat)maxWidth_mm;
/** 元素最大高度---子类实现 毫米 */
- (CGFloat)maxHeight_mm;

/** 获取旋转角度为0时的frame */
- (CGRect)getOrignalFrame;

- (void)reloadSDKImage;

@end
