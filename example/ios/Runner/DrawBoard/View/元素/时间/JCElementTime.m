//
//  JCElementTime.m
//  XYFrameWork
//
//  Created by xingling xu on 2019/11/26.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCElementTime.h"

@implementation JCElementTime

- (void)setTimeConfigure:(JCEleTimeConfigure *)timeConfigure {
    _timeConfigure = timeConfigure;
    NSDate *date = timeConfigure.date;
    NSString *dateFormatt = timeConfigure.dateFormat;
    NSString *timeFromatt = timeConfigure.timeFormat;
    NSString *dateValue = @"",*timeValue = @"";
    
    // 日期
    if (!STR_IS_NIL(dateFormatt) && ![dateFormatt isEqualToString:@"无"]) {
        dateValue = [date stringWithDateFormat:dateFormatt];
    }
    // 时间
    if (!STR_IS_NIL(timeFromatt) && ![timeFromatt isEqualToString:@"无"]) {
        timeValue = [date stringWithDateFormat:timeFromatt];
    }
    
    // 时间日期都有值的时候 要在中间加空格
    NSString *componetString = (!STR_IS_NIL(dateValue) && !STR_IS_NIL(timeValue))?@"  ":@"";
    NSString *value = [NSString stringWithFormat:@"%@%@%@",dateValue,componetString,timeValue];
    if (value.length > 0) {
        self.text = value;
    }else{
        self.text = value;
    }
}

@end
