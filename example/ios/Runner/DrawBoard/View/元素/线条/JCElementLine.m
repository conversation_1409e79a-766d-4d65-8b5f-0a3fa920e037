//
//  JCElementLine.m
//  XYFrameWork
//
//  Created by xingling xu on 2019/11/26.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCElementLine.h"
#import "JCElementToItem.h"
#import "JCElementModel+Transfer.h"

@interface JCElementLine ()
@property (nonatomic, strong) UIImageView *lineImgView;
@property (nonatomic, assign) CGPoint startPoint;
@property (nonatomic, assign) CGRect  startFrame;
@end

@implementation JCElementLine

- (instancetype)init {
    self = [super init];
    if (self) {
        [self addSubview:self.lineImgView];
        [self.lineImgView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.top.width.height.equalTo(self);
        }];
    }
    return self;
}

- (void)reloadSDKImage {
    [self reloadUI];
}

- (void)reloadUI {
    if (!self.configure) return;
    JCElementModel *model = [JCElementToItem itemForElement:self];
    // 因为本地获取的都是正常方向的图片，所以此处单独获取某个元素的图片设值旋转角度0，旋转动作由本地元素完成
    model.rotate = 0;
    NSMutableDictionary *dict = [model.toSdk(YES) elementDict].mutableCopy;
    NSMutableDictionary *params = [NSMutableDictionary dictionaryWithCapacity:2];
    [params setObject:@[dict] forKey:@"elements"];
    NSString *json = [params  dc_toJSONString];
    NSInteger screenScale = [UIScreen mainScreen].scale;
    UIImage *image = nil;// [JCAPI generateLinePreviewImage:json displayMultiple:DrawBoardInfo.mm2pxScale*screenScale];
    // frame
    CGFloat value = self.configure.lineWidth+lineMargin*2;
    CGRect currentFrame = self.frame;
    CGFloat cLeft = currentFrame.origin.x,cTop = currentFrame.origin.y,cRight = cLeft+currentFrame.size.width,cBottom = cTop+currentFrame.size.height;
    if (self.rotate == 0) {
        self.height = value;
        self.top = cTop;
        self.left = cLeft;
    } else if (self.rotate == 270) {
        self.width = value;
        self.bottom = cBottom;
        self.left = cLeft;
    } else if (self.rotate == 90) {
        self.width = value;
        self.top = cTop;
        self.right = cRight;
    } else if (self.rotate == 180) {
        self.height = value;
        self.right = cRight;
        self.bottom = cBottom;
    }
    CGSize size = self.bounds.size;
    self.lineImgView.frame = (CGRect){0,lineMargin,size.width,size.height-lineMargin*2};

    self.lineImgView.image = image;
    [self setNeedsLayout];
}

#pragma mark -- set
- (void)setConfigure:(JCEleLineConfigure *)configure {
    _configure = configure;
    [self reloadUI];
}

#pragma makr - 重写父类方法
- (void)zoomDragBegin: (UIControl *) c withEvent:touches
{
    if (self.isLock || self.zoomButton.hidden) return;
    UITouch *touch = [[touches allTouches] anyObject];
    CGPoint point = [touch locationInView:self.superview];
    self.startPoint = point;
    self.startFrame = self.frame;
    JCPerformSelectorWithOneObject((id)self.delegate,@selector(elementViewDragBegin:), self)
}
- (void)zoomDragMoving: (UIControl *) c withEvent:touches
{
    [self moveWithEvents:touches];
}

- (void)zoomDragOutside: (UIControl *) c withEvent:touches
{
    [self moveWithEvents:touches];
}

- (void)moveWithEvents:touches {
    if (self.isLock || self.zoomButton.hidden) return;
    @autoreleasepool {
        UITouch *touch = [[touches allTouches] anyObject];
        CGPoint point = [touch locationInView:self.superview];
        CGFloat xlength = point.x - self.startPoint.x; //x移动距离
        CGFloat ylength = point.y - self.startPoint.y; //y移动距离
        CGFloat startWidth = self.startFrame.size.width;
        CGFloat startHeight = self.startFrame.size.height;
        CGFloat minWidth = [self minWidth];
        CGFloat width = 0;
        CGFloat cLeft = self.startFrame.origin.x,cTop =self.startFrame.origin.y,cRight = cLeft+self.startFrame.size.width,cBottom = cTop+self.startFrame.size.height;
        if (self.rotate == 0) {
            width = MAX(minWidth, startWidth+xlength);
            self.width = width;
            self.top = cTop;
            self.left = cLeft;
        } else if (self.rotate == 270) {
            width = MAX(minWidth, startHeight-ylength);
            self.height = width;
            self.bottom = cBottom;
            self.left = cLeft;
        } else if (self.rotate == 90) {
            width = MAX(minWidth, startHeight+ylength);
            self.height = width;
            self.top = cTop;
            self.right = cRight;
        } else if (self.rotate == 180) {
            width = MAX(minWidth, startWidth-xlength);
            self.width = width;
            self.right = cRight;
            self.bottom = cBottom;
        }
    }
    [self reloadUI];
    JCPerformSelectorWithOneObject((id)self.delegate,@selector(elementViewFrameShouldChange:), self)
}

#pragma mark -- lazy
- (UIImageView *)lineImgView {
    if (!_lineImgView) {
        _lineImgView = [[UIImageView alloc] init];
        // 最近邻居算法，图像放大不模糊
        _lineImgView.layer.magnificationFilter = kCAFilterNearest;
    }
    return _lineImgView;;
}

#pragma mark -- FrameProtocol
- (CGFloat)minWidth {
    return 1*DrawBoardInfo.mm2pxScale;
}

- (CGFloat)minHeight {
    return (self.configure.lineWidth+lineMargin*2)*DrawBoardInfo.mm2pxScale;
}

- (CGRect)getOrignalFrame {
    rotate(
           CGRect frame = self.frame;
           CGFloat x = frame.origin.x;
           CGFloat y = frame.origin.y+lineMargin;
           CGFloat width = frame.size.width;
           CGFloat height = frame.size.height - lineMargin*2;
           );
    return CGRectMake(x, y, width, height);
}

- (BOOL)pointInside:(CGPoint)point withEvent:(UIEvent *)event {
    CGRect bounds = self.bounds;
    //90 是希望的X 轴或者Y轴方向的点击区域的宽度或者高度
    CGFloat heightDelta = 60- bounds.size.height;
    bounds =CGRectInset(bounds, 0, -0.3* heightDelta);//注意这里是负数，扩大了之前的bounds的范围
    return CGRectContainsPoint(bounds, point);
}

@end
