//
//  JCEleLineConfigure.m
//  XYFrameWork
//
//  Created by xingling xu on 2019/11/26.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCEleLineConfigure.h"

@implementation JCEleLineConfigure

+ (JCEleLineConfigure *)defaultConfigure {
    JCEleLineConfigure *configure = [[JCEleLineConfigure alloc] init];
    configure.mmLineWidth = line_default_width;
    configure.lineWidth = DrawBoardInfo.mm2pxScale*line_default_width;
    configure.lineStyle = JCLineStyleSolid;
    return configure;
}

@end
