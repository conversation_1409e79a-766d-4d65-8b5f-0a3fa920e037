# Uncomment this line to define a global platform for your project
 platform :ios, '11.0'

 use_modular_headers!

# CocoaPods analytics sends network stats synchronously affecting flutter build latency.
ENV['COCOAPODS_DISABLE_STATS'] = 'true'

project 'Runner', {
  'Debug' => :debug,
  'Profile' => :release,
  'Release' => :release,
}

def flutter_root
  generated_xcode_build_settings_path = File.expand_path(File.join('..', 'Flutter', 'Generated.xcconfig'), __FILE__)
  unless File.exist?(generated_xcode_build_settings_path)
    raise "#{generated_xcode_build_settings_path} must exist. If you're running pod install manually, make sure flutter pub get is executed first"
  end

  File.foreach(generated_xcode_build_settings_path) do |line|
    matches = line.match(/FLUTTER_ROOT\=(.*)/)
    return matches[1].strip if matches
  end
  raise "FLUTTER_ROOT not found in #{generated_xcode_build_settings_path}. Try deleting Generated.xcconfig, then run flutter pub get"
end

require File.expand_path(File.join('packages', 'flutter_tools', 'bin', 'podhelper'), flutter_root)

flutter_ios_podfile_setup

target 'Runner' do
  flutter_install_all_ios_pods File.dirname(File.realpath(__FILE__))
  
  pod 'AFNetworking', '~> 4.0'

  pod 'MJRefresh','~> 3.1.15'
  pod 'MBProgressHUD','~> 1.1.0'
  pod 'YTKNetwork','~> 3.0'
  pod 'JSONModel','~> 1.8.0'
  pod 'SDWebImage'
  pod 'YYCache' ,'~> 1.0.3'
  pod 'FMDB' ,'~> 2.7.5'

  pod 'YYModel'
  pod 'Masonry' ,'~> 1.1.0'
  pod 'TMCache'

  pod 'JKCategories', '1.9'

  pod 'DZNEmptyDataSet'
  # 支付宝
  pod 'AlipaySDK-iOS'

  ## 友盟
  #pod 'UMCCommon', '~> 2.1.1'
  ## U-Share SDK UI模块（分享面板，建议添加）
  #pod 'UMCShare/UI'
  ## 集成微信(精简版0.2M)
  #pod 'UMCShare/Social/ReducedWeChat'
  ## 集成QQ/QZone/TIM(精简版0.5M)
  #pod 'UMCShare/Social/ReducedQQ'
  ## 集成新浪微博(精简版1M)
  #pod 'UMCShare/Social/ReducedSina'
  ## 集成钉钉
  #pod 'UMCShare/Social/DingDing'

  pod 'LBXScan/LBXNative','~> 2.5'
  pod 'LBXScan/LBXZXing','~> 2.5'
  pod 'LBXScan/UI','~> 2.5'
  pod 'LBXZBarSDK','~> 1.3'

  pod 'Bugly','~>2.5.0'
#  pod 'FLEX', :configurations => ['Debug']

  # 照片选择器
  #pod 'ZLPhotoBrowser', '~> 2.7.1'
  pod 'ZLPhotoBrowser', '~> 3.2.0'
  pod 'Protobuf'
end

post_install do |installer|
  installer.pods_project.targets.each do |target|
    flutter_additional_ios_build_settings(target)
    target.build_configurations.each do |config|
          # Here are some configurations automatically generated by flutter

          # You can enable the permissions needed here. For example to enable camera
          # permission, just remove the `#` character in front so it looks like this:
          #
          # ## dart: PermissionGroup.camera
          # 'PERMISSION_CAMERA=1'
          #
          #  Preprocessor definitions can be found in: https://github.com/Baseflow/flutter-permission-handler/blob/master/permission_handler/ios/Classes/PermissionHandlerEnums.h
          config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= [
            '$(inherited)',

            ## dart: PermissionGroup.calendar
            # 'PERMISSION_EVENTS=1',

            ## dart: PermissionGroup.reminders
            # 'PERMISSION_REMINDERS=1',

            ## dart: PermissionGroup.contacts
            # 'PERMISSION_CONTACTS=1',

            ## dart: PermissionGroup.camera
            'PERMISSION_CAMERA=1',

            ## dart: PermissionGroup.microphone
            'PERMISSION_MICROPHONE=1',

            ## dart: PermissionGroup.speech
            # 'PERMISSION_SPEECH_RECOGNIZER=1',

            ## dart: PermissionGroup.photos
            'PERMISSION_PHOTOS=1',

            ## dart: [PermissionGroup.location, PermissionGroup.locationAlways, PermissionGroup.locationWhenInUse]
            # 'PERMISSION_LOCATION=1',

            ## dart: PermissionGroup.notification
            # 'PERMISSION_NOTIFICATIONS=1',

            ## dart: PermissionGroup.mediaLibrary
            # 'PERMISSION_MEDIA_LIBRARY=1',

            ## dart: PermissionGroup.sensors
            # 'PERMISSION_SENSORS=1',

            ## dart: PermissionGroup.bluetooth
            # 'PERMISSION_BLUETOOTH=1',

            ## dart: PermissionGroup.appTrackingTransparency
            # 'PERMISSION_APP_TRACKING_TRANSPARENCY=1',

            ## dart: PermissionGroup.criticalAlerts
            # 'PERMISSION_CRITICAL_ALERTS=1'
          ]

        end
  end
end
