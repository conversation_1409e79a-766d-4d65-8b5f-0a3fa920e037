PODS:
  - AFNetworking (4.0.1):
    - AFNetworking/NSURLSession (= 4.0.1)
    - AFNetworking/Reachability (= 4.0.1)
    - AFNetworking/Security (= 4.0.1)
    - AFNetworking/Serialization (= 4.0.1)
    - AFNetworking/UIKit (= 4.0.1)
  - AFNetworking/NSURLSession (4.0.1):
    - AFNetworking/Reachability
    - AFNetworking/Security
    - AFNetworking/Serialization
  - AFNetworking/Reachability (4.0.1)
  - AFNetworking/Security (4.0.1)
  - AFNetworking/Serialization (4.0.1)
  - AFNetworking/UIKit (4.0.1):
    - AFNetworking/NSURLSession
  - AlipaySDK-iOS (15.8.11)
  - Bugly (2.5.93)
  - camera_avfoundation (0.0.1):
    - Flutter
  - DZNEmptyDataSet (1.8.1)
  - Flutter (1.0.0)
  - flutter_keyboard_visibility (0.0.1):
    - Flutter
  - FMDB (2.7.5):
    - FMDB/standard (= 2.7.5)
  - FMDB/standard (2.7.5)
  - image_picker_ios (0.0.1):
    - Flutter
  - JKCategories (1.9):
    - JKCategories/CoreData (= 1.9)
    - JKCategories/CoreLocation (= 1.9)
    - JKCategories/Foundation (= 1.9)
    - JKCategories/MapKit (= 1.9)
    - JKCategories/QuartzCore (= 1.9)
    - JKCategories/UIKit (= 1.9)
  - JKCategories/CoreData (1.9):
    - JKCategories/CoreData/NSFetchRequest (= 1.9)
    - JKCategories/CoreData/NSManagedObject (= 1.9)
    - JKCategories/CoreData/NSManagedObjectContext (= 1.9)
  - JKCategories/CoreData/NSFetchRequest (1.9)
  - JKCategories/CoreData/NSManagedObject (1.9):
    - JKCategories/CoreData/NSManagedObjectContext
  - JKCategories/CoreData/NSManagedObjectContext (1.9):
    - JKCategories/CoreData/NSFetchRequest
  - JKCategories/CoreLocation (1.9):
    - JKCategories/CoreLocation/CLLocation (= 1.9)
  - JKCategories/CoreLocation/CLLocation (1.9)
  - JKCategories/Foundation (1.9):
    - JKCategories/Foundation/NSArray (= 1.9)
    - JKCategories/Foundation/NSBundle (= 1.9)
    - JKCategories/Foundation/NSData (= 1.9)
    - JKCategories/Foundation/NSDate (= 1.9)
    - JKCategories/Foundation/NSDateFormatter (= 1.9)
    - JKCategories/Foundation/NSDictionary (= 1.9)
    - JKCategories/Foundation/NSException (= 1.9)
    - JKCategories/Foundation/NSFileHandle (= 1.9)
    - JKCategories/Foundation/NSFileManager (= 1.9)
    - JKCategories/Foundation/NSHTTPCookieStorage (= 1.9)
    - JKCategories/Foundation/NSIndexPath (= 1.9)
    - JKCategories/Foundation/NSInvocation (= 1.9)
    - JKCategories/Foundation/NSNotificationCenter (= 1.9)
    - JKCategories/Foundation/NSNumber (= 1.9)
    - JKCategories/Foundation/NSObject (= 1.9)
    - JKCategories/Foundation/NSRunLoop (= 1.9)
    - JKCategories/Foundation/NSSet (= 1.9)
    - JKCategories/Foundation/NSString (= 1.9)
    - JKCategories/Foundation/NSTimer (= 1.9)
    - JKCategories/Foundation/NSURL (= 1.9)
    - JKCategories/Foundation/NSURLConnection (= 1.9)
    - JKCategories/Foundation/NSURLRequest (= 1.9)
    - JKCategories/Foundation/NSURLSession (= 1.9)
    - JKCategories/Foundation/NSUserDefaults (= 1.9)
  - JKCategories/Foundation/NSArray (1.9)
  - JKCategories/Foundation/NSBundle (1.9)
  - JKCategories/Foundation/NSData (1.9)
  - JKCategories/Foundation/NSDate (1.9)
  - JKCategories/Foundation/NSDateFormatter (1.9)
  - JKCategories/Foundation/NSDictionary (1.9)
  - JKCategories/Foundation/NSException (1.9)
  - JKCategories/Foundation/NSFileHandle (1.9)
  - JKCategories/Foundation/NSFileManager (1.9)
  - JKCategories/Foundation/NSHTTPCookieStorage (1.9)
  - JKCategories/Foundation/NSIndexPath (1.9)
  - JKCategories/Foundation/NSInvocation (1.9)
  - JKCategories/Foundation/NSNotificationCenter (1.9)
  - JKCategories/Foundation/NSNumber (1.9)
  - JKCategories/Foundation/NSObject (1.9)
  - JKCategories/Foundation/NSRunLoop (1.9)
  - JKCategories/Foundation/NSSet (1.9)
  - JKCategories/Foundation/NSString (1.9):
    - JKCategories/Foundation/NSData
  - JKCategories/Foundation/NSTimer (1.9)
  - JKCategories/Foundation/NSURL (1.9)
  - JKCategories/Foundation/NSURLConnection (1.9)
  - JKCategories/Foundation/NSURLRequest (1.9)
  - JKCategories/Foundation/NSURLSession (1.9)
  - JKCategories/Foundation/NSUserDefaults (1.9)
  - JKCategories/MapKit (1.9):
    - JKCategories/MapKit/MKMapView (= 1.9)
  - JKCategories/MapKit/MKMapView (1.9)
  - JKCategories/QuartzCore (1.9):
    - JKCategories/QuartzCore/CAAnimation (= 1.9)
    - JKCategories/QuartzCore/CALayer (= 1.9)
    - JKCategories/QuartzCore/CAMediaTimingFunction (= 1.9)
    - JKCategories/QuartzCore/CAShapeLayer (= 1.9)
    - JKCategories/QuartzCore/CATransaction (= 1.9)
  - JKCategories/QuartzCore/CAAnimation (1.9)
  - JKCategories/QuartzCore/CALayer (1.9)
  - JKCategories/QuartzCore/CAMediaTimingFunction (1.9)
  - JKCategories/QuartzCore/CAShapeLayer (1.9)
  - JKCategories/QuartzCore/CATransaction (1.9)
  - JKCategories/UIKit (1.9):
    - JKCategories/UIKit/UIApplication (= 1.9)
    - JKCategories/UIKit/UIBarButtonItem (= 1.9)
    - JKCategories/UIKit/UIBezierPath (= 1.9)
    - JKCategories/UIKit/UIButton (= 1.9)
    - JKCategories/UIKit/UIColor (= 1.9)
    - JKCategories/UIKit/UIControl (= 1.9)
    - JKCategories/UIKit/UIDevice (= 1.9)
    - JKCategories/UIKit/UIFont (= 1.9)
    - JKCategories/UIKit/UIImage (= 1.9)
    - JKCategories/UIKit/UIImageView (= 1.9)
    - JKCategories/UIKit/UILable (= 1.9)
    - JKCategories/UIKit/UINavigationBar (= 1.9)
    - JKCategories/UIKit/UINavigationController (= 1.9)
    - JKCategories/UIKit/UINavigationItem (= 1.9)
    - JKCategories/UIKit/UIPopoverController (= 1.9)
    - JKCategories/UIKit/UIResponder (= 1.9)
    - JKCategories/UIKit/UIScreen (= 1.9)
    - JKCategories/UIKit/UIScrollView (= 1.9)
    - JKCategories/UIKit/UISearchBar (= 1.9)
    - JKCategories/UIKit/UISplitViewController (= 1.9)
    - JKCategories/UIKit/UITableView (= 1.9)
    - JKCategories/UIKit/UITableViewCell (= 1.9)
    - JKCategories/UIKit/UITextField (= 1.9)
    - JKCategories/UIKit/UITextView (= 1.9)
    - JKCategories/UIKit/UIView (= 1.9)
    - JKCategories/UIKit/UIViewController (= 1.9)
    - JKCategories/UIKit/UIWindow (= 1.9)
  - JKCategories/UIKit/UIApplication (1.9)
  - JKCategories/UIKit/UIBarButtonItem (1.9)
  - JKCategories/UIKit/UIBezierPath (1.9)
  - JKCategories/UIKit/UIButton (1.9)
  - JKCategories/UIKit/UIColor (1.9)
  - JKCategories/UIKit/UIControl (1.9)
  - JKCategories/UIKit/UIDevice (1.9)
  - JKCategories/UIKit/UIFont (1.9)
  - JKCategories/UIKit/UIImage (1.9)
  - JKCategories/UIKit/UIImageView (1.9)
  - JKCategories/UIKit/UILable (1.9)
  - JKCategories/UIKit/UINavigationBar (1.9)
  - JKCategories/UIKit/UINavigationController (1.9)
  - JKCategories/UIKit/UINavigationItem (1.9)
  - JKCategories/UIKit/UIPopoverController (1.9)
  - JKCategories/UIKit/UIResponder (1.9)
  - JKCategories/UIKit/UIScreen (1.9)
  - JKCategories/UIKit/UIScrollView (1.9)
  - JKCategories/UIKit/UISearchBar (1.9)
  - JKCategories/UIKit/UISplitViewController (1.9)
  - JKCategories/UIKit/UITableView (1.9)
  - JKCategories/UIKit/UITableViewCell (1.9)
  - JKCategories/UIKit/UITextField (1.9)
  - JKCategories/UIKit/UITextView (1.9)
  - JKCategories/UIKit/UIView (1.9)
  - JKCategories/UIKit/UIViewController (1.9)
  - JKCategories/UIKit/UIWindow (1.9)
  - JSONModel (1.8.0)
  - LBXScan/LBXNative (2.5.1):
    - LBXScan/Types (~> 2.2)
  - LBXScan/LBXZXing (2.5.1):
    - LBXScan/Types (~> 2.2)
  - LBXScan/Types (2.5.1)
  - LBXScan/UI (2.5.1):
    - LBXScan/Types (~> 2.2)
  - LBXZBarSDK (1.3.5)
  - Masonry (1.1.0)
  - MBProgressHUD (1.1.0)
  - MJRefresh (3.1.17)
  - MTBBarcodeScanner (5.0.11)
  - netal_plugin (0.0.1):
    - Flutter
  - path_provider_ios (0.0.1):
    - Flutter
  - permission_handler_apple (9.0.4):
    - Flutter
  - Protobuf (3.21.5)
  - qr_code_scanner (0.2.0):
    - Flutter
    - MTBBarcodeScanner
  - scan (0.0.1):
    - Flutter
  - SDWebImage (5.13.2):
    - SDWebImage/Core (= 5.13.2)
  - SDWebImage/Core (5.13.2)
  - shared_preferences_ios (0.0.1):
    - Flutter
  - sound_stream (0.0.1):
    - Flutter
  - sqflite (0.0.2):
    - Flutter
    - FMDB (>= 2.7.5)
  - TMCache (2.1.0)
  - YTKNetwork (3.0.6):
    - AFNetworking/NSURLSession (~> 4.0)
  - YYCache (1.0.4)
  - YYModel (1.0.4)
  - ZLPhotoBrowser (3.2.0):
    - SDWebImage

DEPENDENCIES:
  - AFNetworking (~> 4.0)
  - AlipaySDK-iOS
  - Bugly (~> 2.5.0)
  - camera_avfoundation (from `.symlinks/plugins/camera_avfoundation/ios`)
  - DZNEmptyDataSet
  - Flutter (from `Flutter`)
  - flutter_keyboard_visibility (from `.symlinks/plugins/flutter_keyboard_visibility/ios`)
  - FMDB (~> 2.7.5)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - JKCategories (= 1.9)
  - JSONModel (~> 1.8.0)
  - LBXScan/LBXNative (~> 2.5)
  - LBXScan/LBXZXing (~> 2.5)
  - LBXScan/UI (~> 2.5)
  - LBXZBarSDK (~> 1.3)
  - Masonry (~> 1.1.0)
  - MBProgressHUD (~> 1.1.0)
  - MJRefresh (~> 3.1.15)
  - netal_plugin (from `.symlinks/plugins/netal_plugin/ios`)
  - path_provider_ios (from `.symlinks/plugins/path_provider_ios/ios`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - Protobuf
  - qr_code_scanner (from `.symlinks/plugins/qr_code_scanner/ios`)
  - scan (from `.symlinks/plugins/scan/ios`)
  - SDWebImage
  - shared_preferences_ios (from `.symlinks/plugins/shared_preferences_ios/ios`)
  - sound_stream (from `.symlinks/plugins/sound_stream/ios`)
  - sqflite (from `.symlinks/plugins/sqflite/ios`)
  - TMCache
  - YTKNetwork (~> 3.0)
  - YYCache (~> 1.0.3)
  - YYModel
  - ZLPhotoBrowser (~> 3.2.0)

SPEC REPOS:
  trunk:
    - AFNetworking
    - AlipaySDK-iOS
    - Bugly
    - DZNEmptyDataSet
    - FMDB
    - JKCategories
    - JSONModel
    - LBXScan
    - LBXZBarSDK
    - Masonry
    - MBProgressHUD
    - MJRefresh
    - MTBBarcodeScanner
    - Protobuf
    - SDWebImage
    - TMCache
    - YTKNetwork
    - YYCache
    - YYModel
    - ZLPhotoBrowser

EXTERNAL SOURCES:
  camera_avfoundation:
    :path: ".symlinks/plugins/camera_avfoundation/ios"
  Flutter:
    :path: Flutter
  flutter_keyboard_visibility:
    :path: ".symlinks/plugins/flutter_keyboard_visibility/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  netal_plugin:
    :path: ".symlinks/plugins/netal_plugin/ios"
  path_provider_ios:
    :path: ".symlinks/plugins/path_provider_ios/ios"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  qr_code_scanner:
    :path: ".symlinks/plugins/qr_code_scanner/ios"
  scan:
    :path: ".symlinks/plugins/scan/ios"
  shared_preferences_ios:
    :path: ".symlinks/plugins/shared_preferences_ios/ios"
  sound_stream:
    :path: ".symlinks/plugins/sound_stream/ios"
  sqflite:
    :path: ".symlinks/plugins/sqflite/ios"

SPEC CHECKSUMS:
  AFNetworking: 7864c38297c79aaca1500c33288e429c3451fdce
  AlipaySDK-iOS: a07b6eafa847a5b29acb6c150ea16d9792d5d167
  Bugly: b8715e6ec4004b7f7fbffab0643ba80545aee3da
  camera_avfoundation: 07c77549ea54ad95d8581be86617c094a46280d9
  DZNEmptyDataSet: 9525833b9e68ac21c30253e1d3d7076cc828eaa7
  Flutter: f04841e97a9d0b0a8025694d0796dd46242b2854
  flutter_keyboard_visibility: 0339d06371254c3eb25eeb90ba8d17dca8f9c069
  FMDB: 2ce00b547f966261cd18927a3ddb07cb6f3db82a
  image_picker_ios: b786a5dcf033a8336a657191401bfdf12017dabb
  JKCategories: b5240c9f6cd6991cacced4bec47c3f508c7f3f7e
  JSONModel: 02ab723958366a3fd27da57ea2af2113658762e9
  LBXScan: 90ca10d0c38fb4a5a6980d7782354f3f69f50093
  LBXZBarSDK: db826d1265fbb4fbdbb2f30d7d1e96142e32bfff
  Masonry: 678fab65091a9290e40e2832a55e7ab731aad201
  MBProgressHUD: e7baa36a220447d8aeb12769bf0585582f3866d9
  MJRefresh: ee5b68f639775462faba4db0fd243baf4d42c2cf
  MTBBarcodeScanner: f453b33c4b7dfe545d8c6484ed744d55671788cb
  netal_plugin: d4a187a1aad82e1e9e22271f6da8c8976ffbfedf
  path_provider_ios: 14f3d2fd28c4fdb42f44e0f751d12861c43cee02
  permission_handler_apple: 44366e37eaf29454a1e7b1b7d736c2cceaeb17ce
  Protobuf: 7504b04fffcf6662ad629694db8231f5e744327f
  qr_code_scanner: bb67d64904c3b9658ada8c402e8b4d406d5d796e
  scan: aea35bb4aa59ccc8839c576a18cd57c7d492cc86
  SDWebImage: 72f86271a6f3139cc7e4a89220946489d4b9a866
  shared_preferences_ios: 548a61f8053b9b8a49ac19c1ffbc8b92c50d68ad
  sound_stream: eba0f2a5994b91d9e5009a739df6e98515a7b44f
  sqflite: 6d358c025f5b867b29ed92fc697fd34924e11904
  TMCache: 95ebcc9b3c7e90fb5fd8fc3036cba3aa781c9bed
  YTKNetwork: c16be90b06be003de9e9cd0d3b187cc8eaf35c04
  YYCache: 8105b6638f5e849296c71f331ff83891a4942952
  YYModel: 2a7fdd96aaa4b86a824e26d0c517de8928c04b30
  ZLPhotoBrowser: 3748fdbeb2b28961c2495d15131dbf413082e1b9

PODFILE CHECKSUM: 48334a8a8939c17b8e98cc4f43a6ad66890c05e8

COCOAPODS: 1.11.3
