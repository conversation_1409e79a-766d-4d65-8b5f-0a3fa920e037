// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 50;
	objects = {

/* Begin PBXBuildFile section */
		1498D2341E8E89220040F4C2 /* GeneratedPluginRegistrant.m in Sources */ = {isa = PBXBuildFile; fileRef = 1498D2331E8E89220040F4C2 /* GeneratedPluginRegistrant.m */; };
		3B3967161E833CAA004F5970 /* AppFrameworkInfo.plist in Resources */ = {isa = PBXBuildFile; fileRef = 3B3967151E833CAA004F5970 /* AppFrameworkInfo.plist */; };
		6713C8BE2681C9DC00C3DB03 /* ANTILOST.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 6713C8502681C9DB00C3DB03 /* ANTILOST.a */; };
		6713C8BF2681C9DC00C3DB03 /* SDK程序电子文件及版本说明.xlsx in Resources */ = {isa = PBXBuildFile; fileRef = 6713C8512681C9DC00C3DB03 /* SDK程序电子文件及版本说明.xlsx */; };
		6713C8C02681C9DC00C3DB03 /* LocalNotificationUtil.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C8582681C9DC00C3DB03 /* LocalNotificationUtil.m */; };
		6713C8C12681C9DC00C3DB03 /* JCVenderTool.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C8592681C9DC00C3DB03 /* JCVenderTool.m */; };
		6713C8C22681C9DC00C3DB03 /* JCRFIDModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C85C2681C9DC00C3DB03 /* JCRFIDModel.m */; };
		6713C8C32681C9DC00C3DB03 /* JCKeychainTool.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C85D2681C9DC00C3DB03 /* JCKeychainTool.m */; };
		6713C8C42681C9DC00C3DB03 /* JCApplicationManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C85E2681C9DC00C3DB03 /* JCApplicationManager.m */; };
		6713C8C52681C9DC00C3DB03 /* NullSafe.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C85F2681C9DC00C3DB03 /* NullSafe.m */; };
		6713C8C62681C9DC00C3DB03 /* JCDevicesSeriesModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C8602681C9DC00C3DB03 /* JCDevicesSeriesModel.m */; };
		6713C8C72681C9DC00C3DB03 /* JCPrinterSameTool.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C8612681C9DC00C3DB03 /* JCPrinterSameTool.m */; };
		6713C8C82681C9DC00C3DB03 /* DeviceUpdateUtil.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C8622681C9DC00C3DB03 /* DeviceUpdateUtil.m */; };
		6713C8C92681C9DC00C3DB03 /* JCAntiLostUtil.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C8632681C9DC00C3DB03 /* JCAntiLostUtil.m */; };
		6713C8CA2681C9DC00C3DB03 /* JCRfidRuleModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C8662681C9DC00C3DB03 /* JCRfidRuleModel.m */; };
		6713C8CB2681C9DC00C3DB03 /* DeviceUpdateUtil4AntiLost.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C8692681C9DC00C3DB03 /* DeviceUpdateUtil4AntiLost.m */; };
		6713C8CC2681C9DC00C3DB03 /* StyleDIY.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C86D2681C9DC00C3DB03 /* StyleDIY.m */; };
		6713C8CD2681C9DC00C3DB03 /* LBXScanPermissions.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C86E2681C9DC00C3DB03 /* LBXScanPermissions.m */; };
		6713C8CE2681C9DC00C3DB03 /* QQLBXScanViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C8712681C9DC00C3DB03 /* QQLBXScanViewController.m */; };
		6713C8CF2681C9DC00C3DB03 /* Global.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C8742681C9DC00C3DB03 /* Global.m */; };
		6713C8D02681C9DC00C3DB03 /* UIView+EasyExtend.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C8752681C9DC00C3DB03 /* UIView+EasyExtend.m */; };
		6713C8D12681C9DC00C3DB03 /* DCHUDHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C8772681C9DC00C3DB03 /* DCHUDHelper.m */; };
		6713C8D22681C9DC00C3DB03 /* MBProgressHUD.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 6713C8792681C9DC00C3DB03 /* MBProgressHUD.bundle */; };
		6713C8D32681C9DC00C3DB03 /* DCWeakTimer.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C87B2681C9DC00C3DB03 /* DCWeakTimer.m */; };
		6713C8D42681C9DC00C3DB03 /* DCGCDTimer.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C87C2681C9DC00C3DB03 /* DCGCDTimer.m */; };
		6713C8D52681C9DC00C3DB03 /* DCProxy.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C87F2681C9DC00C3DB03 /* DCProxy.m */; };
		6713C8D62681C9DC00C3DB03 /* SDAnimatedImageView+JCImageView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C8832681C9DC00C3DB03 /* SDAnimatedImageView+JCImageView.m */; };
		6713C8D72681C9DC00C3DB03 /* JCEventTrackingTool.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C8852681C9DC00C3DB03 /* JCEventTrackingTool.m */; };
		6713C8D82681C9DC00C3DB03 /* CommonCtrl.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C8882681C9DC00C3DB03 /* CommonCtrl.m */; };
		6713C8D92681C9DC00C3DB03 /* JCRFIDDeviceModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C88B2681C9DC00C3DB03 /* JCRFIDDeviceModel.m */; };
		6713C8DA2681C9DC00C3DB03 /* BabyToy.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C88F2681C9DC00C3DB03 /* BabyToy.m */; };
		6713C8DB2681C9DC00C3DB03 /* BabyOptions.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C8942681C9DC00C3DB03 /* BabyOptions.m */; };
		6713C8DC2681C9DC00C3DB03 /* BabyCallback.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C8952681C9DC00C3DB03 /* BabyCallback.m */; };
		6713C8DD2681C9DC00C3DB03 /* BabyPeripheralManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C8962681C9DC00C3DB03 /* BabyPeripheralManager.m */; };
		6713C8DE2681C9DC00C3DB03 /* BabyCentralManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C8972681C9DC00C3DB03 /* BabyCentralManager.m */; };
		6713C8DF2681C9DC00C3DB03 /* BabySpeaker.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C8982681C9DC00C3DB03 /* BabySpeaker.m */; };
		6713C8E02681C9DC00C3DB03 /* BabyRhythm.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C89A2681C9DC00C3DB03 /* BabyRhythm.m */; };
		6713C8E12681C9DC00C3DB03 /* BabyBluetooth.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C89C2681C9DC00C3DB03 /* BabyBluetooth.m */; };
		6713C8E22681C9DC00C3DB03 /* BabyDefine.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C89D2681C9DC00C3DB03 /* BabyDefine.m */; };
		6713C8E32681C9DC00C3DB03 /* JCLogUtil.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C8A22681C9DC00C3DB03 /* JCLogUtil.m */; };
		6713C8E42681C9DC00C3DB03 /* DCNormalHTTPRequst.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C8A42681C9DC00C3DB03 /* DCNormalHTTPRequst.m */; };
		6713C8E52681C9DC00C3DB03 /* NSDictionary+DCJson.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C8A72681C9DC00C3DB03 /* NSDictionary+DCJson.m */; };
		6713C8E62681C9DC00C3DB03 /* DCBaseRequst.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C8A82681C9DC00C3DB03 /* DCBaseRequst.m */; };
		6713C8E72681C9DC00C3DB03 /* JCOSSRequest.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C8A92681C9DC00C3DB03 /* JCOSSRequest.m */; };
		6713C8E82681C9DC00C3DB03 /* JCErrorInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C8AE2681C9DC00C3DB03 /* JCErrorInfo.m */; };
		6713C8E92681C9DC00C3DB03 /* Reachability.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C8B02681C9DC00C3DB03 /* Reachability.m */; };
		6713C8EA2681C9DC00C3DB03 /* libFeasyWifiSDK_V1.33.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 6713C8B22681C9DC00C3DB03 /* libFeasyWifiSDK_V1.33.a */; };
		6713C8EC2681C9DC00C3DB03 /* JCAPI.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 6713C8B52681C9DC00C3DB03 /* JCAPI.a */; };
		6713C8ED2681C9DC00C3DB03 /* NSData+YDCRC.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C8B82681C9DC00C3DB03 /* NSData+YDCRC.m */; };
		6713C8EE2681C9DC00C3DB03 /* JCDownLoadAleart.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C8BA2681C9DC00C3DB03 /* JCDownLoadAleart.m */; };
		6713C8EF2681C9DC00C3DB03 /* JCDeviceUpdateAleart.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C8BC2681C9DC00C3DB03 /* JCDeviceUpdateAleart.m */; };
		6713C8F92681C9F800C3DB03 /* JCConst.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C8F22681C9F800C3DB03 /* JCConst.m */; };
		6713C9022681CABD00C3DB03 /* DeviceDetailController.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C8FA2681CABB00C3DB03 /* DeviceDetailController.m */; };
		6713C9032681CABD00C3DB03 /* detailHeaderCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C8FD2681CABC00C3DB03 /* detailHeaderCell.m */; };
		6713C9042681CABD00C3DB03 /* DetailViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C8FF2681CABC00C3DB03 /* DetailViewCell.m */; };
		6713C9052681CABD00C3DB03 /* DevicesSearchController.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C9012681CABD00C3DB03 /* DevicesSearchController.m */; };
		6713C90A2681CAD100C3DB03 /* SettingPowerOffController.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C9072681CAD000C3DB03 /* SettingPowerOffController.m */; };
		6713C90B2681CAD100C3DB03 /* SearchHeader.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C9082681CAD000C3DB03 /* SearchHeader.m */; };
		6713C9102681CAF200C3DB03 /* BlueToothCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C90C2681CAF200C3DB03 /* BlueToothCell.m */; };
		6713C9112681CAF200C3DB03 /* CalibrationController.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C90E2681CAF200C3DB03 /* CalibrationController.m */; };
		6713C9C62681CDFD00C3DB03 /* NSTimer+addition.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C9C22681CDFD00C3DB03 /* NSTimer+addition.m */; };
		6713C9C72681CDFD00C3DB03 /* UIView+JCCategory.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C9C32681CDFD00C3DB03 /* UIView+JCCategory.m */; };
		6713C9C82681CDFD00C3DB03 /* UIColor+Extension.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C9C42681CDFD00C3DB03 /* UIColor+Extension.m */; };
		6713C9C92681CDFD00C3DB03 /* NSString+JC_Device.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C9C52681CDFD00C3DB03 /* NSString+JC_Device.m */; };
		6713C9D12681CEAC00C3DB03 /* DCBaseModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C9CB2681CEAC00C3DB03 /* DCBaseModel.m */; };
		6713C9D22681CEAC00C3DB03 /* JCUserModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C9CF2681CEAC00C3DB03 /* JCUserModel.m */; };
		6713C9D32681CEAC00C3DB03 /* DCNavigationViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C9D02681CEAC00C3DB03 /* DCNavigationViewController.m */; };
		6713CB872681CED200C3DB03 /* NSString+XYCategory.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C9D72681CED000C3DB03 /* NSString+XYCategory.m */; };
		6713CB882681CED200C3DB03 /* XYTool.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C9DA2681CED000C3DB03 /* XYTool.m */; };
		6713CB892681CED200C3DB03 /* JCBubbleTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 6713C9DF2681CED000C3DB03 /* JCBubbleTableViewCell.xib */; };
		6713CB8A2681CED200C3DB03 /* UITextView+ZWPlaceHolder.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C9E02681CED000C3DB03 /* UITextView+ZWPlaceHolder.m */; };
		6713CB8B2681CED200C3DB03 /* UIFont+JCCustomFont.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C9E12681CED000C3DB03 /* UIFont+JCCustomFont.m */; };
		6713CB8C2681CED200C3DB03 /* JCContext.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C9E42681CED000C3DB03 /* JCContext.m */; };
		6713CB8D2681CED200C3DB03 /* NSObject+JCContext.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C9E52681CED000C3DB03 /* NSObject+JCContext.m */; };
		6713CB8E2681CED200C3DB03 /* UIImage+EasyExtend.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C9E72681CED000C3DB03 /* UIImage+EasyExtend.m */; };
		6713CB8F2681CED200C3DB03 /* NSArray+EasyExtend.mm in Sources */ = {isa = PBXBuildFile; fileRef = 6713C9ED2681CED000C3DB03 /* NSArray+EasyExtend.mm */; };
		6713CB902681CED200C3DB03 /* JCBubbleTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C9EE2681CED000C3DB03 /* JCBubbleTableViewCell.m */; };
		6713CB912681CED200C3DB03 /* JCExcelForElement.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C9F12681CED000C3DB03 /* JCExcelForElement.m */; };
		6713CB922681CED200C3DB03 /* UIImage+Tool.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C9F22681CED000C3DB03 /* UIImage+Tool.m */; };
		6713CB932681CED200C3DB03 /* JCBaseScrollView+Touch.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C9F32681CED000C3DB03 /* JCBaseScrollView+Touch.m */; };
		6713CB942681CED200C3DB03 /* pinyin.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C9F62681CED000C3DB03 /* pinyin.m */; };
		6713CB952681CED200C3DB03 /* JCBaseScrollView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C9F72681CED000C3DB03 /* JCBaseScrollView.m */; };
		6713CB962681CED200C3DB03 /* JCAlert.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C9FA2681CED000C3DB03 /* JCAlert.m */; };
		6713CB972681CED200C3DB03 /* JCPrintCenter.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C9FC2681CED000C3DB03 /* JCPrintCenter.m */; };
		6713CB982681CED200C3DB03 /* JCRfidError.xib in Resources */ = {isa = PBXBuildFile; fileRef = 6713C9FD2681CED000C3DB03 /* JCRfidError.xib */; };
		6713CB992681CED200C3DB03 /* JCPrintCessView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C9FE2681CED000C3DB03 /* JCPrintCessView.m */; };
		6713CB9A2681CED200C3DB03 /* JCMessageView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713C9FF2681CED000C3DB03 /* JCMessageView.m */; };
		6713CB9B2681CED200C3DB03 /* JCPrintAntiLostCessView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA002681CED000C3DB03 /* JCPrintAntiLostCessView.m */; };
		6713CB9C2681CED200C3DB03 /* JCUniqueModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA012681CED000C3DB03 /* JCUniqueModel.m */; };
		6713CB9D2681CED200C3DB03 /* JCDeviceFirmwareRemoteModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA022681CED000C3DB03 /* JCDeviceFirmwareRemoteModel.m */; };
		6713CB9E2681CED200C3DB03 /* JCBoardGuideView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA032681CED000C3DB03 /* JCBoardGuideView.m */; };
		6713CB9F2681CED200C3DB03 /* JCPaper.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA062681CED000C3DB03 /* JCPaper.m */; };
		6713CBA02681CED200C3DB03 /* JCPaperManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA092681CED000C3DB03 /* JCPaperManager.m */; };
		6713CBA12681CED200C3DB03 /* JCPrintRecordManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA0E2681CED000C3DB03 /* JCPrintRecordManager.m */; };
		6713CBA22681CED200C3DB03 /* JCImprintManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA0F2681CED000C3DB03 /* JCImprintManager.m */; };
		6713CBA32681CED200C3DB03 /* JCDBManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA112681CED000C3DB03 /* JCDBManager.m */; };
		6713CBA42681CED200C3DB03 /* JCPrintExecuteManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA142681CED000C3DB03 /* JCPrintExecuteManager.m */; };
		6713CBA52681CED200C3DB03 /* JCPolicy4DB.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA162681CED000C3DB03 /* JCPolicy4DB.m */; };
		6713CBA62681CED200C3DB03 /* JCPolicyManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA182681CED000C3DB03 /* JCPolicyManager.m */; };
		6713CBA72681CED200C3DB03 /* JCPolicy.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA1A2681CED000C3DB03 /* JCPolicy.m */; };
		6713CBA82681CED200C3DB03 /* JCDeviceManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA1F2681CED000C3DB03 /* JCDeviceManager.m */; };
		6713CBA92681CED200C3DB03 /* JCDevice.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA202681CED000C3DB03 /* JCDevice.m */; };
		6713CBAA2681CED200C3DB03 /* JCRfidCheckManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA232681CED100C3DB03 /* JCRfidCheckManager.m */; };
		6713CBAB2681CED200C3DB03 /* JCMessageView.xib in Resources */ = {isa = PBXBuildFile; fileRef = 6713CA262681CED100C3DB03 /* JCMessageView.xib */; };
		6713CBAC2681CED200C3DB03 /* JCPaperErrorView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA2B2681CED100C3DB03 /* JCPaperErrorView.m */; };
		6713CBAD2681CED200C3DB03 /* JCRfidError.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA2D2681CED100C3DB03 /* JCRfidError.m */; };
		6713CBAE2681CED200C3DB03 /* JCDeviceOffSetInfoModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA332681CED100C3DB03 /* JCDeviceOffSetInfoModel.m */; };
		6713CBAF2681CED200C3DB03 /* JCPrinterModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA342681CED100C3DB03 /* JCPrinterModel.m */; };
		6713CBB02681CED200C3DB03 /* JCDeviceUpdateTool.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA352681CED100C3DB03 /* JCDeviceUpdateTool.m */; };
		6713CBB12681CED200C3DB03 /* JCPrintDevice.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA362681CED100C3DB03 /* JCPrintDevice.m */; };
		6713CBB22681CED200C3DB03 /* MMAlertView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA382681CED100C3DB03 /* MMAlertView.m */; };
		6713CBB32681CED200C3DB03 /* MMSheetView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA392681CED100C3DB03 /* MMSheetView.m */; };
		6713CBB42681CED200C3DB03 /* MMPopupCategory.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA3B2681CED100C3DB03 /* MMPopupCategory.m */; };
		6713CBB52681CED200C3DB03 /* MMPopupWindow.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA3D2681CED100C3DB03 /* MMPopupWindow.m */; };
		6713CBB62681CED200C3DB03 /* MMPopupView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA412681CED100C3DB03 /* MMPopupView.m */; };
		6713CBB72681CED200C3DB03 /* MMPopupItem.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA442681CED100C3DB03 /* MMPopupItem.m */; };
		6713CBB82681CED200C3DB03 /* XYView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA462681CED100C3DB03 /* XYView.m */; };
		6713CBB92681CED200C3DB03 /* JCMessageImageView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA472681CED100C3DB03 /* JCMessageImageView.m */; };
		6713CBBA2681CED200C3DB03 /* UIView+XYCategory.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA482681CED100C3DB03 /* UIView+XYCategory.m */; };
		6713CBBB2681CED200C3DB03 /* JCSelectDeviceModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA4A2681CED100C3DB03 /* JCSelectDeviceModel.m */; };
		6713CBBC2681CED200C3DB03 /* JCRecordTool.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA4D2681CED100C3DB03 /* JCRecordTool.m */; };
		6713CBBD2681CED200C3DB03 /* JCRecordModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA4F2681CED100C3DB03 /* JCRecordModel.m */; };
		6713CBBE2681CED200C3DB03 /* JCStatisticsModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA522681CED100C3DB03 /* JCStatisticsModel.m */; };
		6713CBBF2681CED200C3DB03 /* MBProgressHUD+Extension.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA552681CED100C3DB03 /* MBProgressHUD+Extension.m */; };
		6713CBC02681CED200C3DB03 /* JCMessageImageView.xib in Resources */ = {isa = PBXBuildFile; fileRef = 6713CA592681CED100C3DB03 /* JCMessageImageView.xib */; };
		6713CBC12681CED200C3DB03 /* JCBluetoothModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA5E2681CED100C3DB03 /* JCBluetoothModel.m */; };
		6713CBC22681CED200C3DB03 /* JCBluetoothManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA602681CED100C3DB03 /* JCBluetoothManager.m */; };
		6713CBC32681CED200C3DB03 /* JCPrinterTypeHelp.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA612681CED100C3DB03 /* JCPrinterTypeHelp.m */; };
		6713CBC42681CED200C3DB03 /* JCWifiManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA622681CED100C3DB03 /* JCWifiManager.m */; };
		6713CBC52681CED200C3DB03 /* JCDeviceSeriesHelp.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA632681CED100C3DB03 /* JCDeviceSeriesHelp.m */; };
		6713CBC62681CED200C3DB03 /* JCTemplateEditController.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA692681CED100C3DB03 /* JCTemplateEditController.m */; };
		6713CBC72681CED200C3DB03 /* JCTemplateData4DBManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA6C2681CED100C3DB03 /* JCTemplateData4DBManager.m */; };
		6713CBC82681CED200C3DB03 /* UIImageView+DownLoad.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA6E2681CED100C3DB03 /* UIImageView+DownLoad.m */; };
		6713CBC92681CED200C3DB03 /* JCElementToItem.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA702681CED100C3DB03 /* JCElementToItem.m */; };
		6713CBCA2681CED200C3DB03 /* JCOssApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA712681CED100C3DB03 /* JCOssApi.m */; };
		6713CBCB2681CED200C3DB03 /* JCFontDownLoadManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA722681CED100C3DB03 /* JCFontDownLoadManager.m */; };
		6713CBCC2681CED200C3DB03 /* JCStack.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA762681CED100C3DB03 /* JCStack.m */; };
		6713CBCD2681CED200C3DB03 /* JCItemToElement.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA782681CED100C3DB03 /* JCItemToElement.m */; };
		6713CBCE2681CED200C3DB03 /* JCTMDataBindGoodsInfoManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA792681CED100C3DB03 /* JCTMDataBindGoodsInfoManager.m */; };
		6713CBCF2681CED200C3DB03 /* JCElementOperateManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA7D2681CED100C3DB03 /* JCElementOperateManager.m */; };
		6713CBD02681CED200C3DB03 /* JCTemplateImageManager.mm in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA802681CED100C3DB03 /* JCTemplateImageManager.mm */; };
		6713CBD12681CED200C3DB03 /* JCScanCodeManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA822681CED100C3DB03 /* JCScanCodeManager.m */; };
		6713CBD22681CED300C3DB03 /* UIImage+uploadRequest.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA832681CED100C3DB03 /* UIImage+uploadRequest.m */; };
		6713CBD32681CED300C3DB03 /* JCBoardTemplateManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA842681CED100C3DB03 /* JCBoardTemplateManager.m */; };
		6713CBD42681CED300C3DB03 /* JCFontManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA882681CED100C3DB03 /* JCFontManager.m */; };
		6713CBD52681CED300C3DB03 /* JCElementEditManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA892681CED100C3DB03 /* JCElementEditManager.m */; };
		6713CBD62681CED300C3DB03 /* JCDefaultElementManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA8A2681CED100C3DB03 /* JCDefaultElementManager.m */; };
		6713CBD72681CED300C3DB03 /* JCDrawInfoManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA8D2681CED100C3DB03 /* JCDrawInfoManager.m */; };
		6713CBD82681CED300C3DB03 /* JCElementModel+Transfer.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA912681CED100C3DB03 /* JCElementModel+Transfer.m */; };
		6713CBD92681CED300C3DB03 /* JCFontModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA942681CED100C3DB03 /* JCFontModel.m */; };
		6713CBDA2681CED300C3DB03 /* JCTemplateData.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA952681CED100C3DB03 /* JCTemplateData.m */; };
		6713CBDB2681CED300C3DB03 /* JCElementModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA962681CED100C3DB03 /* JCElementModel.m */; };
		6713CBDC2681CED300C3DB03 /* JCTemplateData+PrintConfigure.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA972681CED100C3DB03 /* JCTemplateData+PrintConfigure.m */; };
		6713CBDD2681CED300C3DB03 /* JCTemplateData+Transfer.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA982681CED100C3DB03 /* JCTemplateData+Transfer.m */; };
		6713CBDE2681CED300C3DB03 /* JCTemplateList.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA9E2681CED100C3DB03 /* JCTemplateList.m */; };
		6713CBDF2681CED300C3DB03 /* JCTemplateData+External.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CA9F2681CED100C3DB03 /* JCTemplateData+External.m */; };
		6713CBE02681CED300C3DB03 /* JCGoodDetailInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CAA02681CED100C3DB03 /* JCGoodDetailInfo.m */; };
		6713CBE12681CED300C3DB03 /* JCTemplateData+SDK.mm in Sources */ = {isa = PBXBuildFile; fileRef = 6713CAA52681CED100C3DB03 /* JCTemplateData+SDK.mm */; };
		6713CBE22681CED300C3DB03 /* JCPrintSamePreviewModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CAA62681CED100C3DB03 /* JCPrintSamePreviewModel.m */; };
		6713CBE32681CED300C3DB03 /* JCTemplateData4DB.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CAA72681CED100C3DB03 /* JCTemplateData4DB.m */; };
		6713CBE42681CED300C3DB03 /* XYBaseModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CAA82681CED100C3DB03 /* XYBaseModel.m */; };
		6713CBE52681CED300C3DB03 /* JCFontSize.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CAA92681CED100C3DB03 /* JCFontSize.m */; };
		6713CBE62681CED300C3DB03 /* JCDisplacementView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CAAE2681CED100C3DB03 /* JCDisplacementView.m */; };
		6713CBE72681CED300C3DB03 /* JCStyleContentView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CAAF2681CED100C3DB03 /* JCStyleContentView.m */; };
		6713CBE82681CED300C3DB03 /* JCStyleModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CAB02681CED100C3DB03 /* JCStyleModel.m */; };
		6713CBE92681CED300C3DB03 /* JCMutablePropertyView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CAB22681CED100C3DB03 /* JCMutablePropertyView.m */; };
		6713CBEA2681CED300C3DB03 /* JCTemplateAntiLostCollectionView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CAB62681CED100C3DB03 /* JCTemplateAntiLostCollectionView.m */; };
		6713CBEB2681CED300C3DB03 /* JCTemplateAntiLostHorizontalCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CAB72681CED100C3DB03 /* JCTemplateAntiLostHorizontalCell.m */; };
		6713CBEC2681CED300C3DB03 /* JCTemplateAntiLostChooseView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CAB82681CED100C3DB03 /* JCTemplateAntiLostChooseView.m */; };
		6713CBED2681CED300C3DB03 /* JCAddElementBar.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CABD2681CED100C3DB03 /* JCAddElementBar.m */; };
		6713CBEE2681CED300C3DB03 /* JCElementEditBar.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CABE2681CED100C3DB03 /* JCElementEditBar.m */; };
		6713CBEF2681CED300C3DB03 /* JCActionButton.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CABF2681CED100C3DB03 /* JCActionButton.m */; };
		6713CBF02681CED300C3DB03 /* JCAddElementView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CAC02681CED100C3DB03 /* JCAddElementView.m */; };
		6713CBF12681CED300C3DB03 /* JCTagListManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CAC72681CED100C3DB03 /* JCTagListManager.m */; };
		6713CBF22681CED300C3DB03 /* JCTagModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CAC82681CED100C3DB03 /* JCTagModel.m */; };
		6713CBF32681CED300C3DB03 /* UIView+PlaceHolder.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CACA2681CED100C3DB03 /* UIView+PlaceHolder.m */; };
		6713CBF42681CED300C3DB03 /* JCTagImageTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CACB2681CED100C3DB03 /* JCTagImageTableViewCell.m */; };
		6713CBF52681CED300C3DB03 /* JCPropertyMainView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CACC2681CED100C3DB03 /* JCPropertyMainView.m */; };
		6713CBF62681CED300C3DB03 /* JCLogoCategoryModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CACD2681CED100C3DB03 /* JCLogoCategoryModel.m */; };
		6713CBF72681CED300C3DB03 /* JCProgressView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CACE2681CED100C3DB03 /* JCProgressView.m */; };
		6713CBF82681CED300C3DB03 /* JCBoardRightItemView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CACF2681CED100C3DB03 /* JCBoardRightItemView.m */; };
		6713CBF92681CED300C3DB03 /* JCPrintAlertView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CAD02681CED100C3DB03 /* JCPrintAlertView.m */; };
		6713CBFA2681CED300C3DB03 /* JCDrawNavigationBar.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CAD12681CED100C3DB03 /* JCDrawNavigationBar.m */; };
		6713CBFB2681CED300C3DB03 /* JCElementActionBar.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CAD82681CED100C3DB03 /* JCElementActionBar.m */; };
		6713CBFC2681CED300C3DB03 /* JCLineStyleView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CADA2681CED100C3DB03 /* JCLineStyleView.m */; };
		6713CBFD2681CED300C3DB03 /* JCAlignView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CADB2681CED100C3DB03 /* JCAlignView.m */; };
		6713CBFE2681CED300C3DB03 /* JCInputTextView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CADC2681CED100C3DB03 /* JCInputTextView.m */; };
		6713CBFF2681CED300C3DB03 /* JCFontSelectView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CADD2681CED100C3DB03 /* JCFontSelectView.m */; };
		6713CC002681CED300C3DB03 /* JCBorderCollectionCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CADE2681CED100C3DB03 /* JCBorderCollectionCell.m */; };
		6713CC012681CED300C3DB03 /* JCBorderView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CAE02681CED100C3DB03 /* JCBorderView.m */; };
		6713CC022681CED300C3DB03 /* JCIconBorderManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CAE12681CED100C3DB03 /* JCIconBorderManager.m */; };
		6713CC032681CED300C3DB03 /* JCImagePropertyView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CAE52681CED100C3DB03 /* JCImagePropertyView.m */; };
		6713CC042681CED300C3DB03 /* JCDateFormatView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CAE92681CED100C3DB03 /* JCDateFormatView.m */; };
		6713CC052681CED300C3DB03 /* JCTextStyleView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CAEB2681CED100C3DB03 /* JCTextStyleView.m */; };
		6713CC062681CED300C3DB03 /* JCIConObject.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CAF02681CED100C3DB03 /* JCIConObject.m */; };
		6713CC072681CED300C3DB03 /* JCIconView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CAF22681CED100C3DB03 /* JCIconView.m */; };
		6713CC082681CED300C3DB03 /* JCIconDataManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CAF32681CED100C3DB03 /* JCIconDataManager.m */; };
		6713CC092681CED300C3DB03 /* JCIconCollectionViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CAF42681CED100C3DB03 /* JCIconCollectionViewCell.m */; };
		6713CC0A2681CED300C3DB03 /* JCFontTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CAF82681CED100C3DB03 /* JCFontTableViewCell.m */; };
		6713CC0B2681CED300C3DB03 /* JCPrintSettingView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CAFB2681CED100C3DB03 /* JCPrintSettingView.m */; };
		6713CC0C2681CED300C3DB03 /* JCTagListLeftCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CAFC2681CED200C3DB03 /* JCTagListLeftCell.m */; };
		6713CC0D2681CED300C3DB03 /* JCBarData.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CAFF2681CED200C3DB03 /* JCBarData.m */; };
		6713CC0E2681CED300C3DB03 /* JCBarCollectionCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB032681CED200C3DB03 /* JCBarCollectionCell.m */; };
		6713CC0F2681CED300C3DB03 /* JCElementPropertyBar.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB042681CED200C3DB03 /* JCElementPropertyBar.m */; };
		6713CC102681CED300C3DB03 /* JCDrawNavigationBar4AntiLost.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB0A2681CED200C3DB03 /* JCDrawNavigationBar4AntiLost.m */; };
		6713CC112681CED300C3DB03 /* JCSearchTagTextField.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB0B2681CED200C3DB03 /* JCSearchTagTextField.m */; };
		6713CC122681CED300C3DB03 /* JCTagChooseListView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB0C2681CED200C3DB03 /* JCTagChooseListView.m */; };
		6713CC132681CED300C3DB03 /* JCElementBaseView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB112681CED200C3DB03 /* JCElementBaseView.m */; };
		6713CC142681CED300C3DB03 /* JCElementGraph.mm in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB152681CED200C3DB03 /* JCElementGraph.mm */; };
		6713CC152681CED300C3DB03 /* JCElementBox.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB172681CED200C3DB03 /* JCElementBox.m */; };
		6713CC162681CED300C3DB03 /* JCEleBoxConfigure.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB182681CED200C3DB03 /* JCEleBoxConfigure.m */; };
		6713CC172681CED300C3DB03 /* JCElementText.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB1A2681CED200C3DB03 /* JCElementText.m */; };
		6713CC182681CED300C3DB03 /* JCElementTextConfigure.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB1B2681CED200C3DB03 /* JCElementTextConfigure.m */; };
		6713CC192681CED300C3DB03 /* JCElementTextImage.mm in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB1E2681CED200C3DB03 /* JCElementTextImage.mm */; };
		6713CC1A2681CED300C3DB03 /* JCElementBarCodeConfigure.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB232681CED200C3DB03 /* JCElementBarCodeConfigure.m */; };
		6713CC1B2681CED300C3DB03 /* JCElementBarCode.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB242681CED200C3DB03 /* JCElementBarCode.m */; };
		6713CC1C2681CED300C3DB03 /* JCDrawCodeManager.mm in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB252681CED200C3DB03 /* JCDrawCodeManager.mm */; };
		6713CC1D2681CED300C3DB03 /* JCElementLogoView.mm in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB292681CED200C3DB03 /* JCElementLogoView.mm */; };
		6713CC1E2681CED300C3DB03 /* JCEleLineConfigure.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB2E2681CED200C3DB03 /* JCEleLineConfigure.m */; };
		6713CC1F2681CED300C3DB03 /* JCElementLine.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB2F2681CED200C3DB03 /* JCElementLine.m */; };
		6713CC202681CED300C3DB03 /* JCSerialStyleConfigure.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB322681CED200C3DB03 /* JCSerialStyleConfigure.m */; };
		6713CC212681CED300C3DB03 /* JCElementSerialNumber.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB332681CED200C3DB03 /* JCElementSerialNumber.m */; };
		6713CC222681CED300C3DB03 /* JCElementTime.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB372681CED200C3DB03 /* JCElementTime.m */; };
		6713CC232681CED300C3DB03 /* JCEleTimeConfigure.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB382681CED200C3DB03 /* JCEleTimeConfigure.m */; };
		6713CC242681CED300C3DB03 /* JCElementQRCode.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB3C2681CED200C3DB03 /* JCElementQRCode.m */; };
		6713CC252681CED300C3DB03 /* JCPrintSamePreview.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB402681CED200C3DB03 /* JCPrintSamePreview.m */; };
		6713CC262681CED300C3DB03 /* NSDate+XHExtension.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB462681CED200C3DB03 /* NSDate+XHExtension.m */; };
		6713CC272681CED300C3DB03 /* JCSliderView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB472681CED200C3DB03 /* JCSliderView.m */; };
		6713CC282681CED300C3DB03 /* UIView+Border.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB482681CED200C3DB03 /* UIView+Border.m */; };
		6713CC292681CED300C3DB03 /* JCMoveView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB492681CED200C3DB03 /* JCMoveView.m */; };
		6713CC2A2681CED300C3DB03 /* JCNormalPicker.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB4B2681CED200C3DB03 /* JCNormalPicker.m */; };
		6713CC2B2681CED300C3DB03 /* JCFontSliderView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB4C2681CED200C3DB03 /* JCFontSliderView.m */; };
		6713CC2C2681CED300C3DB03 /* JCDatePicker.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB4E2681CED200C3DB03 /* JCDatePicker.m */; };
		6713CC2D2681CED300C3DB03 /* JCStyleButton.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB522681CED200C3DB03 /* JCStyleButton.m */; };
		6713CC2E2681CED300C3DB03 /* JCStyleTitleLabel.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB532681CED200C3DB03 /* JCStyleTitleLabel.m */; };
		6713CC2F2681CED300C3DB03 /* UIImage+Dotted.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB542681CED200C3DB03 /* UIImage+Dotted.m */; };
		6713CC302681CED300C3DB03 /* JCSelectionView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB572681CED200C3DB03 /* JCSelectionView.m */; };
		6713CC312681CED300C3DB03 /* JCElementDottedView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB592681CED200C3DB03 /* JCElementDottedView.m */; };
		6713CC322681CED300C3DB03 /* JCDownloadLayout.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB5B2681CED200C3DB03 /* JCDownloadLayout.m */; };
		6713CC332681CED300C3DB03 /* JCTemplateHorizontalCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB612681CED200C3DB03 /* JCTemplateHorizontalCell.m */; };
		6713CC342681CED300C3DB03 /* JCTemplateVerticalCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB622681CED200C3DB03 /* JCTemplateVerticalCell.m */; };
		6713CC352681CED300C3DB03 /* JCTemplateTagModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB632681CED200C3DB03 /* JCTemplateTagModel.m */; };
		6713CC362681CED300C3DB03 /* JCTemplateCollectionView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB642681CED200C3DB03 /* JCTemplateCollectionView.m */; };
		6713CC372681CED300C3DB03 /* JCTemplateChoosView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB652681CED200C3DB03 /* JCTemplateChoosView.m */; };
		6713CC382681CED300C3DB03 /* JCTemplateHistoryView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB662681CED200C3DB03 /* JCTemplateHistoryView.m */; };
		6713CC392681CED300C3DB03 /* JCTag.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB682681CED200C3DB03 /* JCTag.m */; };
		6713CC3A2681CED300C3DB03 /* JCDrawBoardView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB6D2681CED200C3DB03 /* JCDrawBoardView.m */; };
		6713CC3B2681CED300C3DB03 /* JCPrintDirectionView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB6E2681CED200C3DB03 /* JCPrintDirectionView.m */; };
		6713CC3C2681CED300C3DB03 /* JCScaleResertView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB722681CED200C3DB03 /* JCScaleResertView.m */; };
		6713CC3D2681CED300C3DB03 /* JCBoardConst.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB732681CED200C3DB03 /* JCBoardConst.m */; };
		6713CC3E2681CED300C3DB03 /* JCBubbleBar.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB742681CED200C3DB03 /* JCBubbleBar.m */; };
		6713CC3F2681CED300C3DB03 /* JCCanvas.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB752681CED200C3DB03 /* JCCanvas.m */; };
		6713CC402681CED300C3DB03 /* JCElementMainView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB782681CED200C3DB03 /* JCElementMainView.m */; };
		6713CC412681CED300C3DB03 /* RulerView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB792681CED200C3DB03 /* RulerView.m */; };
		6713CC422681CED300C3DB03 /* RulerLayout.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB7C2681CED200C3DB03 /* RulerLayout.m */; };
		6713CC432681CED300C3DB03 /* JCBoardTitleView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB7D2681CED200C3DB03 /* JCBoardTitleView.m */; };
		6713CC442681CED300C3DB03 /* JCDrawBoardView+Undo.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB7E2681CED200C3DB03 /* JCDrawBoardView+Undo.m */; };
		6713CC452681CED300C3DB03 /* JCRulerView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB812681CED200C3DB03 /* JCRulerView.m */; };
		6713CC462681CED300C3DB03 /* RulerCollectionViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CB862681CED200C3DB03 /* RulerCollectionViewCell.m */; };
		6713CC762681CF9100C3DB03 /* JCWKWebView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CC492681CF9000C3DB03 /* JCWKWebView.m */; };
		6713CC772681CF9100C3DB03 /* NSTimer+addition.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CC4F2681CF9000C3DB03 /* NSTimer+addition.m */; };
		6713CC782681CF9100C3DB03 /* UIView+JCCategory.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CC502681CF9000C3DB03 /* UIView+JCCategory.m */; };
		6713CC792681CF9100C3DB03 /* UIColor+Extension.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CC512681CF9000C3DB03 /* UIColor+Extension.m */; };
		6713CC7A2681CF9100C3DB03 /* NSString+JC_Device.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CC522681CF9000C3DB03 /* NSString+JC_Device.m */; };
		6713CC7B2681CF9100C3DB03 /* JCADPopViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CC582681CF9000C3DB03 /* JCADPopViewController.m */; };
		6713CC7C2681CF9100C3DB03 /* AD关闭直线@2x.png in Resources */ = {isa = PBXBuildFile; fileRef = 6713CC5B2681CF9000C3DB03 /* AD关闭直线@2x.png */; };
		6713CC7D2681CF9100C3DB03 /* AD关闭直线@3x.png in Resources */ = {isa = PBXBuildFile; fileRef = 6713CC5C2681CF9000C3DB03 /* AD关闭直线@3x.png */; };
		6713CC7E2681CF9100C3DB03 /* 分享_钉钉@3x.png in Resources */ = {isa = PBXBuildFile; fileRef = 6713CC5D2681CF9000C3DB03 /* 分享_钉钉@3x.png */; };
		6713CC7F2681CF9100C3DB03 /* 分享********** in Resources */ = {isa = PBXBuildFile; fileRef = 6713CC5E2681CF9000C3DB03 /* 分享********** */; };
		6713CC802681CF9100C3DB03 /* 分享_朋友圈@2x.png in Resources */ = {isa = PBXBuildFile; fileRef = 6713CC5F2681CF9000C3DB03 /* 分享_朋友圈@2x.png */; };
		6713CC812681CF9100C3DB03 /* 分享_朋友圈@3x.png in Resources */ = {isa = PBXBuildFile; fileRef = 6713CC602681CF9000C3DB03 /* 分享_朋友圈@3x.png */; };
		6713CC822681CF9100C3DB03 /* 分享********** in Resources */ = {isa = PBXBuildFile; fileRef = 6713CC612681CF9000C3DB03 /* 分享********** */; };
		6713CC832681CF9100C3DB03 /* 分享_钉钉@2x.png in Resources */ = {isa = PBXBuildFile; fileRef = 6713CC622681CF9000C3DB03 /* 分享_钉钉@2x.png */; };
		6713CC842681CF9100C3DB03 /* 分享_微信@2x.png in Resources */ = {isa = PBXBuildFile; fileRef = 6713CC632681CF9000C3DB03 /* 分享_微信@2x.png */; };
		6713CC852681CF9100C3DB03 /* 分享_微信@3x.png in Resources */ = {isa = PBXBuildFile; fileRef = 6713CC642681CF9000C3DB03 /* 分享_微信@3x.png */; };
		6713CC862681CF9100C3DB03 /* AD关闭@2x.png in Resources */ = {isa = PBXBuildFile; fileRef = 6713CC652681CF9000C3DB03 /* AD关闭@2x.png */; };
		6713CC872681CF9100C3DB03 /* AD关闭@3x.png in Resources */ = {isa = PBXBuildFile; fileRef = 6713CC662681CF9000C3DB03 /* AD关闭@3x.png */; };
		6713CC882681CF9100C3DB03 /* AppDelegate+Share.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CC672681CF9000C3DB03 /* AppDelegate+Share.m */; };
		6713CC892681CF9100C3DB03 /* JCWKWebViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CC682681CF9000C3DB03 /* JCWKWebViewController.m */; };
		6713CC8A2681CF9100C3DB03 /* JCWebProgress.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CC692681CF9000C3DB03 /* JCWebProgress.m */; };
		6713CC8B2681CF9100C3DB03 /* JCPayManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CC6B2681CF9000C3DB03 /* JCPayManager.m */; };
		6713CC8C2681CF9100C3DB03 /* JCShopMallsViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CC6D2681CF9000C3DB03 /* JCShopMallsViewController.m */; };
		6713CC8D2681CF9100C3DB03 /* JCShopBaseWebViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CC6E2681CF9000C3DB03 /* JCShopBaseWebViewController.m */; };
		6713CC8E2681CF9100C3DB03 /* JCShopAliPay.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CC6F2681CF9000C3DB03 /* JCShopAliPay.m */; };
		6713CC8F2681CF9100C3DB03 /* JCFMDB.m in Sources */ = {isa = PBXBuildFile; fileRef = 6713CC752681CF9100C3DB03 /* JCFMDB.m */; };
		6713CC912681DF3A00C3DB03 /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6713CC902681DF3A00C3DB03 /* CoreGraphics.framework */; };
		6713CC932681DF4100C3DB03 /* AVFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6713CC922681DF4100C3DB03 /* AVFoundation.framework */; };
		6713CC952681DF5600C3DB03 /* libc++.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 6713CC942681DF5600C3DB03 /* libc++.tbd */; };
		6713CC972681DF5E00C3DB03 /* libiconv.2.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 6713CC962681DF5E00C3DB03 /* libiconv.2.tbd */; };
		6713CC992681DF6800C3DB03 /* libiconv.2.4.0.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 6713CC982681DF6800C3DB03 /* libiconv.2.4.0.tbd */; };
		6713CC9B2681DF6E00C3DB03 /* libbz2.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 6713CC9A2681DF6E00C3DB03 /* libbz2.tbd */; };
		6713CCAD2681E08000C3DB03 /* Localizable.strings in Resources */ = {isa = PBXBuildFile; fileRef = 6713CC9D2681E08000C3DB03 /* Localizable.strings */; };
		6713CCAE2681E08000C3DB03 /* 8.caf in Resources */ = {isa = PBXBuildFile; fileRef = 6713CCA12681E08000C3DB03 /* 8.caf */; };
		6713CCAF2681E08000C3DB03 /* 9.caf in Resources */ = {isa = PBXBuildFile; fileRef = 6713CCA22681E08000C3DB03 /* 9.caf */; };
		6713CCB02681E08000C3DB03 /* 1.caf in Resources */ = {isa = PBXBuildFile; fileRef = 6713CCA32681E08000C3DB03 /* 1.caf */; };
		6713CCB12681E08000C3DB03 /* 0.caf in Resources */ = {isa = PBXBuildFile; fileRef = 6713CCA42681E08000C3DB03 /* 0.caf */; };
		6713CCB22681E08000C3DB03 /* 2.caf in Resources */ = {isa = PBXBuildFile; fileRef = 6713CCA52681E08000C3DB03 /* 2.caf */; };
		6713CCB32681E08000C3DB03 /* 3.caf in Resources */ = {isa = PBXBuildFile; fileRef = 6713CCA62681E08000C3DB03 /* 3.caf */; };
		6713CCB42681E08000C3DB03 /* 7.caf in Resources */ = {isa = PBXBuildFile; fileRef = 6713CCA72681E08000C3DB03 /* 7.caf */; };
		6713CCB52681E08000C3DB03 /* 6.caf in Resources */ = {isa = PBXBuildFile; fileRef = 6713CCA82681E08000C3DB03 /* 6.caf */; };
		6713CCB62681E08000C3DB03 /* 4.caf in Resources */ = {isa = PBXBuildFile; fileRef = 6713CCA92681E08000C3DB03 /* 4.caf */; };
		6713CCB72681E08000C3DB03 /* 5.caf in Resources */ = {isa = PBXBuildFile; fileRef = 6713CCAA2681E08000C3DB03 /* 5.caf */; };
		6713CCB82681E08000C3DB03 /* JCSDKFont.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 6713CCAB2681E08000C3DB03 /* JCSDKFont.bundle */; };
		6713CCB92681E08000C3DB03 /* ZT001.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6713CCAC2681E08000C3DB03 /* ZT001.ttf */; };
		6713CCBB2681E2B100C3DB03 /* CocoaAsyncSocket.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6713C8B42681C9DC00C3DB03 /* CocoaAsyncSocket.framework */; };
		6713CCBC2681E2B100C3DB03 /* CocoaAsyncSocket.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 6713C8B42681C9DC00C3DB03 /* CocoaAsyncSocket.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		6756B2D726846C5E00C08B2B /* PrintData.pbobjc.m in Sources */ = {isa = PBXBuildFile; fileRef = 6756B2D626846C5E00C08B2B /* PrintData.pbobjc.m */; settings = {COMPILER_FLAGS = "-fno-objc-arc"; }; };
		6756B2D926846CAD00C08B2B /* PrintData.proto in Sources */ = {isa = PBXBuildFile; fileRef = 6756B2D826846CAD00C08B2B /* PrintData.proto */; };
		6756B2DC26848F9600C08B2B /* ImageHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = 6756B2DB26848F9600C08B2B /* ImageHelper.m */; };
		76C66C3528CB11CB00384BCB /* libswiftCore.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 76C66C3428CB11CB00384BCB /* libswiftCore.tbd */; };
		76C66C3828CB157A00384BCB /* KeepSwift.swift in Sources */ = {isa = PBXBuildFile; fileRef = 76C66C3728CB157A00384BCB /* KeepSwift.swift */; };
		9740EEB41CF90195004384FC /* Debug.xcconfig in Resources */ = {isa = PBXBuildFile; fileRef = 9740EEB21CF90195004384FC /* Debug.xcconfig */; };
		978B8F6F1D3862AE00F588F7 /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 7AFFD8EE1D35381100E5BB4D /* AppDelegate.m */; };
		97C146F31CF9000F007C117D /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 97C146F21CF9000F007C117D /* main.m */; };
		97C146FC1CF9000F007C117D /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 97C146FA1CF9000F007C117D /* Main.storyboard */; };
		97C146FE1CF9000F007C117D /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 97C146FD1CF9000F007C117D /* Assets.xcassets */; };
		97C147011CF9000F007C117D /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 97C146FF1CF9000F007C117D /* LaunchScreen.storyboard */; };
		D4F01CCE3B0E5B1B15EDB41C /* libPods-Runner.a in Frameworks */ = {isa = PBXBuildFile; fileRef = FB996FAB4E5C3D141937803C /* libPods-Runner.a */; };
/* End PBXBuildFile section */

/* Begin PBXCopyFilesBuildPhase section */
		9705A1C41CF9048500538489 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				6713CCBC2681E2B100C3DB03 /* CocoaAsyncSocket.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		1498D2321E8E86230040F4C2 /* GeneratedPluginRegistrant.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = GeneratedPluginRegistrant.h; sourceTree = "<group>"; };
		1498D2331E8E89220040F4C2 /* GeneratedPluginRegistrant.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GeneratedPluginRegistrant.m; sourceTree = "<group>"; };
		3B3967151E833CAA004F5970 /* AppFrameworkInfo.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = AppFrameworkInfo.plist; path = Flutter/AppFrameworkInfo.plist; sourceTree = "<group>"; };
		4A32C11088860DB500DEA201 /* Pods-Runner.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.debug.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.debug.xcconfig"; sourceTree = "<group>"; };
		5CBF09EDD77D488B2DF78F42 /* Pods-Runner.profile.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.profile.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.profile.xcconfig"; sourceTree = "<group>"; };
		6713C8502681C9DB00C3DB03 /* ANTILOST.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; path = ANTILOST.a; sourceTree = "<group>"; };
		6713C8512681C9DC00C3DB03 /* SDK程序电子文件及版本说明.xlsx */ = {isa = PBXFileReference; lastKnownFileType = file; path = "SDK程序电子文件及版本说明.xlsx"; sourceTree = "<group>"; };
		6713C8522681C9DC00C3DB03 /* JCAPI_Antilost.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCAPI_Antilost.h; sourceTree = "<group>"; };
		6713C8542681C9DC00C3DB03 /* DeviceUpdateUtil.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DeviceUpdateUtil.h; sourceTree = "<group>"; };
		6713C8552681C9DC00C3DB03 /* JCPrinterSameTool.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCPrinterSameTool.h; sourceTree = "<group>"; };
		6713C8562681C9DC00C3DB03 /* JCDevicesSeriesModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCDevicesSeriesModel.h; sourceTree = "<group>"; };
		6713C8572681C9DC00C3DB03 /* JCRfidRuleModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCRfidRuleModel.h; sourceTree = "<group>"; };
		6713C8582681C9DC00C3DB03 /* LocalNotificationUtil.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = LocalNotificationUtil.m; sourceTree = "<group>"; };
		6713C8592681C9DC00C3DB03 /* JCVenderTool.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCVenderTool.m; sourceTree = "<group>"; };
		6713C85A2681C9DC00C3DB03 /* JCAntiLostUtil.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCAntiLostUtil.h; sourceTree = "<group>"; };
		6713C85B2681C9DC00C3DB03 /* DeviceUpdateUtil4AntiLost.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DeviceUpdateUtil4AntiLost.h; sourceTree = "<group>"; };
		6713C85C2681C9DC00C3DB03 /* JCRFIDModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCRFIDModel.m; sourceTree = "<group>"; };
		6713C85D2681C9DC00C3DB03 /* JCKeychainTool.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCKeychainTool.m; sourceTree = "<group>"; };
		6713C85E2681C9DC00C3DB03 /* JCApplicationManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCApplicationManager.m; sourceTree = "<group>"; };
		6713C85F2681C9DC00C3DB03 /* NullSafe.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = NullSafe.m; sourceTree = "<group>"; };
		6713C8602681C9DC00C3DB03 /* JCDevicesSeriesModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCDevicesSeriesModel.m; sourceTree = "<group>"; };
		6713C8612681C9DC00C3DB03 /* JCPrinterSameTool.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCPrinterSameTool.m; sourceTree = "<group>"; };
		6713C8622681C9DC00C3DB03 /* DeviceUpdateUtil.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DeviceUpdateUtil.m; sourceTree = "<group>"; };
		6713C8632681C9DC00C3DB03 /* JCAntiLostUtil.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCAntiLostUtil.m; sourceTree = "<group>"; };
		6713C8642681C9DC00C3DB03 /* JCVenderTool.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCVenderTool.h; sourceTree = "<group>"; };
		6713C8652681C9DC00C3DB03 /* LocalNotificationUtil.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = LocalNotificationUtil.h; sourceTree = "<group>"; };
		6713C8662681C9DC00C3DB03 /* JCRfidRuleModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCRfidRuleModel.m; sourceTree = "<group>"; };
		6713C8672681C9DC00C3DB03 /* JCKeychainTool.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCKeychainTool.h; sourceTree = "<group>"; };
		6713C8682681C9DC00C3DB03 /* JCRFIDModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCRFIDModel.h; sourceTree = "<group>"; };
		6713C8692681C9DC00C3DB03 /* DeviceUpdateUtil4AntiLost.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DeviceUpdateUtil4AntiLost.m; sourceTree = "<group>"; };
		6713C86A2681C9DC00C3DB03 /* JCApplicationManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCApplicationManager.h; sourceTree = "<group>"; };
		6713C86C2681C9DC00C3DB03 /* QQLBXScanViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = QQLBXScanViewController.h; sourceTree = "<group>"; };
		6713C86D2681C9DC00C3DB03 /* StyleDIY.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = StyleDIY.m; sourceTree = "<group>"; };
		6713C86E2681C9DC00C3DB03 /* LBXScanPermissions.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = LBXScanPermissions.m; sourceTree = "<group>"; };
		6713C86F2681C9DC00C3DB03 /* Global.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Global.h; sourceTree = "<group>"; };
		6713C8702681C9DC00C3DB03 /* UIView+EasyExtend.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIView+EasyExtend.h"; sourceTree = "<group>"; };
		6713C8712681C9DC00C3DB03 /* QQLBXScanViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = QQLBXScanViewController.m; sourceTree = "<group>"; };
		6713C8722681C9DC00C3DB03 /* LBXScanPermissions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = LBXScanPermissions.h; sourceTree = "<group>"; };
		6713C8732681C9DC00C3DB03 /* StyleDIY.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = StyleDIY.h; sourceTree = "<group>"; };
		6713C8742681C9DC00C3DB03 /* Global.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = Global.m; sourceTree = "<group>"; };
		6713C8752681C9DC00C3DB03 /* UIView+EasyExtend.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIView+EasyExtend.m"; sourceTree = "<group>"; };
		6713C8772681C9DC00C3DB03 /* DCHUDHelper.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DCHUDHelper.m; sourceTree = "<group>"; };
		6713C8782681C9DC00C3DB03 /* DCHUDHelper.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DCHUDHelper.h; sourceTree = "<group>"; };
		6713C8792681C9DC00C3DB03 /* MBProgressHUD.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = MBProgressHUD.bundle; sourceTree = "<group>"; };
		6713C87B2681C9DC00C3DB03 /* DCWeakTimer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DCWeakTimer.m; sourceTree = "<group>"; };
		6713C87C2681C9DC00C3DB03 /* DCGCDTimer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DCGCDTimer.m; sourceTree = "<group>"; };
		6713C87D2681C9DC00C3DB03 /* DCProxy.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DCProxy.h; sourceTree = "<group>"; };
		6713C87E2681C9DC00C3DB03 /* DCWeakTimer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DCWeakTimer.h; sourceTree = "<group>"; };
		6713C87F2681C9DC00C3DB03 /* DCProxy.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DCProxy.m; sourceTree = "<group>"; };
		6713C8802681C9DC00C3DB03 /* DCGCDTimer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DCGCDTimer.h; sourceTree = "<group>"; };
		6713C8822681C9DC00C3DB03 /* SDAnimatedImageView+JCImageView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "SDAnimatedImageView+JCImageView.h"; sourceTree = "<group>"; };
		6713C8832681C9DC00C3DB03 /* SDAnimatedImageView+JCImageView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "SDAnimatedImageView+JCImageView.m"; sourceTree = "<group>"; };
		6713C8852681C9DC00C3DB03 /* JCEventTrackingTool.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCEventTrackingTool.m; sourceTree = "<group>"; };
		6713C8862681C9DC00C3DB03 /* JCEventTrackingTool.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCEventTrackingTool.h; sourceTree = "<group>"; };
		6713C8882681C9DC00C3DB03 /* CommonCtrl.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CommonCtrl.m; sourceTree = "<group>"; };
		6713C8892681C9DC00C3DB03 /* CommonCtrl.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CommonCtrl.h; sourceTree = "<group>"; };
		6713C88B2681C9DC00C3DB03 /* JCRFIDDeviceModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCRFIDDeviceModel.m; sourceTree = "<group>"; };
		6713C88D2681C9DC00C3DB03 /* BabyRhythm.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BabyRhythm.h; sourceTree = "<group>"; };
		6713C88E2681C9DC00C3DB03 /* BabySpeaker.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BabySpeaker.h; sourceTree = "<group>"; };
		6713C88F2681C9DC00C3DB03 /* BabyToy.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BabyToy.m; sourceTree = "<group>"; };
		6713C8902681C9DC00C3DB03 /* BabyCentralManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BabyCentralManager.h; sourceTree = "<group>"; };
		6713C8912681C9DC00C3DB03 /* BabyPeripheralManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BabyPeripheralManager.h; sourceTree = "<group>"; };
		6713C8922681C9DC00C3DB03 /* BabyDefine.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BabyDefine.h; sourceTree = "<group>"; };
		6713C8932681C9DC00C3DB03 /* BabyBluetooth.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BabyBluetooth.h; sourceTree = "<group>"; };
		6713C8942681C9DC00C3DB03 /* BabyOptions.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BabyOptions.m; sourceTree = "<group>"; };
		6713C8952681C9DC00C3DB03 /* BabyCallback.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BabyCallback.m; sourceTree = "<group>"; };
		6713C8962681C9DC00C3DB03 /* BabyPeripheralManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BabyPeripheralManager.m; sourceTree = "<group>"; };
		6713C8972681C9DC00C3DB03 /* BabyCentralManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BabyCentralManager.m; sourceTree = "<group>"; };
		6713C8982681C9DC00C3DB03 /* BabySpeaker.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BabySpeaker.m; sourceTree = "<group>"; };
		6713C8992681C9DC00C3DB03 /* BabyToy.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BabyToy.h; sourceTree = "<group>"; };
		6713C89A2681C9DC00C3DB03 /* BabyRhythm.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BabyRhythm.m; sourceTree = "<group>"; };
		6713C89B2681C9DC00C3DB03 /* BabyOptions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BabyOptions.h; sourceTree = "<group>"; };
		6713C89C2681C9DC00C3DB03 /* BabyBluetooth.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BabyBluetooth.m; sourceTree = "<group>"; };
		6713C89D2681C9DC00C3DB03 /* BabyDefine.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BabyDefine.m; sourceTree = "<group>"; };
		6713C89E2681C9DC00C3DB03 /* BabyCallback.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BabyCallback.h; sourceTree = "<group>"; };
		6713C89F2681C9DC00C3DB03 /* JCRFIDDeviceModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCRFIDDeviceModel.h; sourceTree = "<group>"; };
		6713C8A12681C9DC00C3DB03 /* JCLogUtil.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCLogUtil.h; sourceTree = "<group>"; };
		6713C8A22681C9DC00C3DB03 /* JCLogUtil.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCLogUtil.m; sourceTree = "<group>"; };
		6713C8A42681C9DC00C3DB03 /* DCNormalHTTPRequst.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DCNormalHTTPRequst.m; sourceTree = "<group>"; };
		6713C8A52681C9DC00C3DB03 /* JCOSSRequest.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCOSSRequest.h; sourceTree = "<group>"; };
		6713C8A62681C9DC00C3DB03 /* DCBaseRequst.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DCBaseRequst.h; sourceTree = "<group>"; };
		6713C8A72681C9DC00C3DB03 /* NSDictionary+DCJson.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSDictionary+DCJson.m"; sourceTree = "<group>"; };
		6713C8A82681C9DC00C3DB03 /* DCBaseRequst.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DCBaseRequst.m; sourceTree = "<group>"; };
		6713C8A92681C9DC00C3DB03 /* JCOSSRequest.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCOSSRequest.m; sourceTree = "<group>"; };
		6713C8AA2681C9DC00C3DB03 /* DCNormalHTTPRequst.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DCNormalHTTPRequst.h; sourceTree = "<group>"; };
		6713C8AB2681C9DC00C3DB03 /* NSDictionary+DCJson.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSDictionary+DCJson.h"; sourceTree = "<group>"; };
		6713C8AD2681C9DC00C3DB03 /* JCErrorInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCErrorInfo.h; sourceTree = "<group>"; };
		6713C8AE2681C9DC00C3DB03 /* JCErrorInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCErrorInfo.m; sourceTree = "<group>"; };
		6713C8B02681C9DC00C3DB03 /* Reachability.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = Reachability.m; sourceTree = "<group>"; };
		6713C8B12681C9DC00C3DB03 /* FWnetworkWIFI.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FWnetworkWIFI.h; sourceTree = "<group>"; };
		6713C8B22681C9DC00C3DB03 /* libFeasyWifiSDK_V1.33.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; path = libFeasyWifiSDK_V1.33.a; sourceTree = "<group>"; };
		6713C8B32681C9DC00C3DB03 /* NSData+YDCRC.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSData+YDCRC.h"; sourceTree = "<group>"; };
		6713C8B42681C9DC00C3DB03 /* CocoaAsyncSocket.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = CocoaAsyncSocket.framework; sourceTree = "<group>"; };
		6713C8B52681C9DC00C3DB03 /* JCAPI.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; path = JCAPI.a; sourceTree = "<group>"; };
		6713C8B62681C9DC00C3DB03 /* JCAPI.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCAPI.h; sourceTree = "<group>"; };
		6713C8B72681C9DC00C3DB03 /* Reachability.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Reachability.h; sourceTree = "<group>"; };
		6713C8B82681C9DC00C3DB03 /* NSData+YDCRC.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSData+YDCRC.m"; sourceTree = "<group>"; };
		6713C8BA2681C9DC00C3DB03 /* JCDownLoadAleart.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCDownLoadAleart.m; sourceTree = "<group>"; };
		6713C8BB2681C9DC00C3DB03 /* JCDeviceUpdateAleart.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCDeviceUpdateAleart.h; sourceTree = "<group>"; };
		6713C8BC2681C9DC00C3DB03 /* JCDeviceUpdateAleart.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCDeviceUpdateAleart.m; sourceTree = "<group>"; };
		6713C8BD2681C9DC00C3DB03 /* JCDownLoadAleart.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCDownLoadAleart.h; sourceTree = "<group>"; };
		6713C8F22681C9F800C3DB03 /* JCConst.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCConst.m; sourceTree = "<group>"; };
		6713C8F32681C9F800C3DB03 /* JCConst.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCConst.h; sourceTree = "<group>"; };
		6713C8F52681C9F800C3DB03 /* UIMacro.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UIMacro.h; sourceTree = "<group>"; };
		6713C8F62681C9F800C3DB03 /* DatabaseMacro.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DatabaseMacro.h; sourceTree = "<group>"; };
		6713C8F72681C9F800C3DB03 /* CustomFucMacro.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CustomFucMacro.h; sourceTree = "<group>"; };
		6713C8F82681C9F800C3DB03 /* DCRequestMacro.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DCRequestMacro.h; sourceTree = "<group>"; };
		6713C8FA2681CABB00C3DB03 /* DeviceDetailController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DeviceDetailController.m; sourceTree = "<group>"; };
		6713C8FB2681CABC00C3DB03 /* DeviceDetailController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DeviceDetailController.h; sourceTree = "<group>"; };
		6713C8FC2681CABC00C3DB03 /* detailHeaderCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = detailHeaderCell.h; sourceTree = "<group>"; };
		6713C8FD2681CABC00C3DB03 /* detailHeaderCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = detailHeaderCell.m; sourceTree = "<group>"; };
		6713C8FE2681CABC00C3DB03 /* DetailViewCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DetailViewCell.h; sourceTree = "<group>"; };
		6713C8FF2681CABC00C3DB03 /* DetailViewCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DetailViewCell.m; sourceTree = "<group>"; };
		6713C9002681CABC00C3DB03 /* DevicesSearchController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DevicesSearchController.h; sourceTree = "<group>"; };
		6713C9012681CABD00C3DB03 /* DevicesSearchController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DevicesSearchController.m; sourceTree = "<group>"; };
		6713C9062681CAD000C3DB03 /* SearchHeader.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SearchHeader.h; sourceTree = "<group>"; };
		6713C9072681CAD000C3DB03 /* SettingPowerOffController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SettingPowerOffController.m; sourceTree = "<group>"; };
		6713C9082681CAD000C3DB03 /* SearchHeader.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SearchHeader.m; sourceTree = "<group>"; };
		6713C9092681CAD100C3DB03 /* SettingPowerOffController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SettingPowerOffController.h; sourceTree = "<group>"; };
		6713C90C2681CAF200C3DB03 /* BlueToothCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BlueToothCell.m; sourceTree = "<group>"; };
		6713C90D2681CAF200C3DB03 /* BlueToothCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BlueToothCell.h; sourceTree = "<group>"; };
		6713C90E2681CAF200C3DB03 /* CalibrationController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CalibrationController.m; sourceTree = "<group>"; };
		6713C90F2681CAF200C3DB03 /* CalibrationController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CalibrationController.h; sourceTree = "<group>"; };
		6713C91D2681CD3200C3DB03 /* ChenYinHeader.pch */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ChenYinHeader.pch; sourceTree = "<group>"; };
		6713C9BE2681CDFD00C3DB03 /* NSString+JC_Device.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSString+JC_Device.h"; sourceTree = "<group>"; };
		6713C9BF2681CDFD00C3DB03 /* UIColor+Extension.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIColor+Extension.h"; sourceTree = "<group>"; };
		6713C9C02681CDFD00C3DB03 /* UIView+JCCategory.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIView+JCCategory.h"; sourceTree = "<group>"; };
		6713C9C12681CDFD00C3DB03 /* NSTimer+addition.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSTimer+addition.h"; sourceTree = "<group>"; };
		6713C9C22681CDFD00C3DB03 /* NSTimer+addition.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSTimer+addition.m"; sourceTree = "<group>"; };
		6713C9C32681CDFD00C3DB03 /* UIView+JCCategory.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIView+JCCategory.m"; sourceTree = "<group>"; };
		6713C9C42681CDFD00C3DB03 /* UIColor+Extension.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIColor+Extension.m"; sourceTree = "<group>"; };
		6713C9C52681CDFD00C3DB03 /* NSString+JC_Device.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSString+JC_Device.m"; sourceTree = "<group>"; };
		6713C9CB2681CEAC00C3DB03 /* DCBaseModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DCBaseModel.m; sourceTree = "<group>"; };
		6713C9CC2681CEAC00C3DB03 /* JCUserModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCUserModel.h; sourceTree = "<group>"; };
		6713C9CD2681CEAC00C3DB03 /* DCNavigationViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DCNavigationViewController.h; sourceTree = "<group>"; };
		6713C9CE2681CEAC00C3DB03 /* DCBaseModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DCBaseModel.h; sourceTree = "<group>"; };
		6713C9CF2681CEAC00C3DB03 /* JCUserModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCUserModel.m; sourceTree = "<group>"; };
		6713C9D02681CEAC00C3DB03 /* DCNavigationViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DCNavigationViewController.m; sourceTree = "<group>"; };
		6713C9D62681CED000C3DB03 /* XYTool.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XYTool.h; sourceTree = "<group>"; };
		6713C9D72681CED000C3DB03 /* NSString+XYCategory.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSString+XYCategory.m"; sourceTree = "<group>"; };
		6713C9D82681CED000C3DB03 /* VenderMaCro.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = VenderMaCro.h; sourceTree = "<group>"; };
		6713C9D92681CED000C3DB03 /* JCDrawBoardHeader.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCDrawBoardHeader.h; sourceTree = "<group>"; };
		6713C9DA2681CED000C3DB03 /* XYTool.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = XYTool.m; sourceTree = "<group>"; };
		6713C9DB2681CED000C3DB03 /* DeviceMacro.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DeviceMacro.h; sourceTree = "<group>"; };
		6713C9DC2681CED000C3DB03 /* NotificationMacro.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = NotificationMacro.h; sourceTree = "<group>"; };
		6713C9DD2681CED000C3DB03 /* NSString+XYCategory.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSString+XYCategory.h"; sourceTree = "<group>"; };
		6713C9DF2681CED000C3DB03 /* JCBubbleTableViewCell.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = JCBubbleTableViewCell.xib; sourceTree = "<group>"; };
		6713C9E02681CED000C3DB03 /* UITextView+ZWPlaceHolder.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UITextView+ZWPlaceHolder.m"; sourceTree = "<group>"; };
		6713C9E12681CED000C3DB03 /* UIFont+JCCustomFont.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIFont+JCCustomFont.m"; sourceTree = "<group>"; };
		6713C9E32681CED000C3DB03 /* NSObject+JCContext.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSObject+JCContext.h"; sourceTree = "<group>"; };
		6713C9E42681CED000C3DB03 /* JCContext.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCContext.m; sourceTree = "<group>"; };
		6713C9E52681CED000C3DB03 /* NSObject+JCContext.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSObject+JCContext.m"; sourceTree = "<group>"; };
		6713C9E62681CED000C3DB03 /* JCContext.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCContext.h; sourceTree = "<group>"; };
		6713C9E72681CED000C3DB03 /* UIImage+EasyExtend.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIImage+EasyExtend.m"; sourceTree = "<group>"; };
		6713C9E82681CED000C3DB03 /* JCBaseScrollView+Touch.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "JCBaseScrollView+Touch.h"; sourceTree = "<group>"; };
		6713C9E92681CED000C3DB03 /* UIImage+Tool.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIImage+Tool.h"; sourceTree = "<group>"; };
		6713C9EA2681CED000C3DB03 /* JCExcelForElement.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCExcelForElement.h; sourceTree = "<group>"; };
		6713C9EB2681CED000C3DB03 /* JCBaseScrollView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCBaseScrollView.h; sourceTree = "<group>"; };
		6713C9EC2681CED000C3DB03 /* pinyin.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = pinyin.h; sourceTree = "<group>"; };
		6713C9ED2681CED000C3DB03 /* NSArray+EasyExtend.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = "NSArray+EasyExtend.mm"; sourceTree = "<group>"; };
		6713C9EE2681CED000C3DB03 /* JCBubbleTableViewCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCBubbleTableViewCell.m; sourceTree = "<group>"; };
		6713C9EF2681CED000C3DB03 /* UIFont+JCCustomFont.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIFont+JCCustomFont.h"; sourceTree = "<group>"; };
		6713C9F02681CED000C3DB03 /* UITextView+ZWPlaceHolder.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UITextView+ZWPlaceHolder.h"; sourceTree = "<group>"; };
		6713C9F12681CED000C3DB03 /* JCExcelForElement.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCExcelForElement.m; sourceTree = "<group>"; };
		6713C9F22681CED000C3DB03 /* UIImage+Tool.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIImage+Tool.m"; sourceTree = "<group>"; };
		6713C9F32681CED000C3DB03 /* JCBaseScrollView+Touch.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "JCBaseScrollView+Touch.m"; sourceTree = "<group>"; };
		6713C9F42681CED000C3DB03 /* UIImage+EasyExtend.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIImage+EasyExtend.h"; sourceTree = "<group>"; };
		6713C9F52681CED000C3DB03 /* NSArray+EasyExtend.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSArray+EasyExtend.h"; sourceTree = "<group>"; };
		6713C9F62681CED000C3DB03 /* pinyin.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = pinyin.m; sourceTree = "<group>"; };
		6713C9F72681CED000C3DB03 /* JCBaseScrollView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCBaseScrollView.m; sourceTree = "<group>"; };
		6713C9F82681CED000C3DB03 /* JCBubbleTableViewCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCBubbleTableViewCell.h; sourceTree = "<group>"; };
		6713C9FA2681CED000C3DB03 /* JCAlert.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCAlert.m; sourceTree = "<group>"; };
		6713C9FB2681CED000C3DB03 /* JCPaperErrorView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCPaperErrorView.h; sourceTree = "<group>"; };
		6713C9FC2681CED000C3DB03 /* JCPrintCenter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCPrintCenter.m; sourceTree = "<group>"; };
		6713C9FD2681CED000C3DB03 /* JCRfidError.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = JCRfidError.xib; sourceTree = "<group>"; };
		6713C9FE2681CED000C3DB03 /* JCPrintCessView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCPrintCessView.m; sourceTree = "<group>"; };
		6713C9FF2681CED000C3DB03 /* JCMessageView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCMessageView.m; sourceTree = "<group>"; };
		6713CA002681CED000C3DB03 /* JCPrintAntiLostCessView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCPrintAntiLostCessView.m; sourceTree = "<group>"; };
		6713CA012681CED000C3DB03 /* JCUniqueModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCUniqueModel.m; sourceTree = "<group>"; };
		6713CA022681CED000C3DB03 /* JCDeviceFirmwareRemoteModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCDeviceFirmwareRemoteModel.m; sourceTree = "<group>"; };
		6713CA032681CED000C3DB03 /* JCBoardGuideView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCBoardGuideView.m; sourceTree = "<group>"; };
		6713CA062681CED000C3DB03 /* JCPaper.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCPaper.m; sourceTree = "<group>"; };
		6713CA072681CED000C3DB03 /* JCPaperManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCPaperManager.h; sourceTree = "<group>"; };
		6713CA082681CED000C3DB03 /* JCPaper.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCPaper.h; sourceTree = "<group>"; };
		6713CA092681CED000C3DB03 /* JCPaperManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCPaperManager.m; sourceTree = "<group>"; };
		6713CA0A2681CED000C3DB03 /* JCRfidCheckManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCRfidCheckManager.h; sourceTree = "<group>"; };
		6713CA0C2681CED000C3DB03 /* JCImprintManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCImprintManager.h; sourceTree = "<group>"; };
		6713CA0D2681CED000C3DB03 /* JCPrintRecordManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCPrintRecordManager.h; sourceTree = "<group>"; };
		6713CA0E2681CED000C3DB03 /* JCPrintRecordManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCPrintRecordManager.m; sourceTree = "<group>"; };
		6713CA0F2681CED000C3DB03 /* JCImprintManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCImprintManager.m; sourceTree = "<group>"; };
		6713CA112681CED000C3DB03 /* JCDBManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCDBManager.m; sourceTree = "<group>"; };
		6713CA122681CED000C3DB03 /* JCDBConfigure.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCDBConfigure.h; sourceTree = "<group>"; };
		6713CA132681CED000C3DB03 /* JCDBManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCDBManager.h; sourceTree = "<group>"; };
		6713CA142681CED000C3DB03 /* JCPrintExecuteManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCPrintExecuteManager.m; sourceTree = "<group>"; };
		6713CA162681CED000C3DB03 /* JCPolicy4DB.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCPolicy4DB.m; sourceTree = "<group>"; };
		6713CA172681CED000C3DB03 /* JCPolicy.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCPolicy.h; sourceTree = "<group>"; };
		6713CA182681CED000C3DB03 /* JCPolicyManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCPolicyManager.m; sourceTree = "<group>"; };
		6713CA192681CED000C3DB03 /* JCPolicy4DB.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCPolicy4DB.h; sourceTree = "<group>"; };
		6713CA1A2681CED000C3DB03 /* JCPolicy.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCPolicy.m; sourceTree = "<group>"; };
		6713CA1B2681CED000C3DB03 /* JCPolicyConfigure.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCPolicyConfigure.h; sourceTree = "<group>"; };
		6713CA1C2681CED000C3DB03 /* JCPolicyManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCPolicyManager.h; sourceTree = "<group>"; };
		6713CA1E2681CED000C3DB03 /* JCDevice.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCDevice.h; sourceTree = "<group>"; };
		6713CA1F2681CED000C3DB03 /* JCDeviceManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCDeviceManager.m; sourceTree = "<group>"; };
		6713CA202681CED000C3DB03 /* JCDevice.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCDevice.m; sourceTree = "<group>"; };
		6713CA212681CED100C3DB03 /* JCDeviceManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCDeviceManager.h; sourceTree = "<group>"; };
		6713CA222681CED100C3DB03 /* JCPrintConfigure.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCPrintConfigure.h; sourceTree = "<group>"; };
		6713CA232681CED100C3DB03 /* JCRfidCheckManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCRfidCheckManager.m; sourceTree = "<group>"; };
		6713CA242681CED100C3DB03 /* JCPrintExecuteManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCPrintExecuteManager.h; sourceTree = "<group>"; };
		6713CA252681CED100C3DB03 /* JCRfidError.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCRfidError.h; sourceTree = "<group>"; };
		6713CA262681CED100C3DB03 /* JCMessageView.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = JCMessageView.xib; sourceTree = "<group>"; };
		6713CA272681CED100C3DB03 /* JCAlert.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCAlert.h; sourceTree = "<group>"; };
		6713CA282681CED100C3DB03 /* JCMessageView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCMessageView.h; sourceTree = "<group>"; };
		6713CA292681CED100C3DB03 /* JCPrintCessView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCPrintCessView.h; sourceTree = "<group>"; };
		6713CA2A2681CED100C3DB03 /* JCPrintCenter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCPrintCenter.h; sourceTree = "<group>"; };
		6713CA2B2681CED100C3DB03 /* JCPaperErrorView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCPaperErrorView.m; sourceTree = "<group>"; };
		6713CA2C2681CED100C3DB03 /* JCPrintAntiLostCessView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCPrintAntiLostCessView.h; sourceTree = "<group>"; };
		6713CA2D2681CED100C3DB03 /* JCRfidError.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCRfidError.m; sourceTree = "<group>"; };
		6713CA2E2681CED100C3DB03 /* JCBoardGuideView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCBoardGuideView.h; sourceTree = "<group>"; };
		6713CA2F2681CED100C3DB03 /* JCDeviceFirmwareRemoteModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCDeviceFirmwareRemoteModel.h; sourceTree = "<group>"; };
		6713CA312681CED100C3DB03 /* JCSelectDeviceModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCSelectDeviceModel.h; sourceTree = "<group>"; };
		6713CA322681CED100C3DB03 /* UIView+XYCategory.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIView+XYCategory.h"; sourceTree = "<group>"; };
		6713CA332681CED100C3DB03 /* JCDeviceOffSetInfoModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCDeviceOffSetInfoModel.m; sourceTree = "<group>"; };
		6713CA342681CED100C3DB03 /* JCPrinterModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCPrinterModel.m; sourceTree = "<group>"; };
		6713CA352681CED100C3DB03 /* JCDeviceUpdateTool.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCDeviceUpdateTool.m; sourceTree = "<group>"; };
		6713CA362681CED100C3DB03 /* JCPrintDevice.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCPrintDevice.m; sourceTree = "<group>"; };
		6713CA382681CED100C3DB03 /* MMAlertView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MMAlertView.m; sourceTree = "<group>"; };
		6713CA392681CED100C3DB03 /* MMSheetView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MMSheetView.m; sourceTree = "<group>"; };
		6713CA3A2681CED100C3DB03 /* MMPopupView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MMPopupView.h; sourceTree = "<group>"; };
		6713CA3B2681CED100C3DB03 /* MMPopupCategory.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MMPopupCategory.m; sourceTree = "<group>"; };
		6713CA3C2681CED100C3DB03 /* MMPopupItem.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MMPopupItem.h; sourceTree = "<group>"; };
		6713CA3D2681CED100C3DB03 /* MMPopupWindow.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MMPopupWindow.m; sourceTree = "<group>"; };
		6713CA3E2681CED100C3DB03 /* MMSheetView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MMSheetView.h; sourceTree = "<group>"; };
		6713CA3F2681CED100C3DB03 /* MMPopupDefine.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MMPopupDefine.h; sourceTree = "<group>"; };
		6713CA402681CED100C3DB03 /* MMAlertView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MMAlertView.h; sourceTree = "<group>"; };
		6713CA412681CED100C3DB03 /* MMPopupView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MMPopupView.m; sourceTree = "<group>"; };
		6713CA422681CED100C3DB03 /* MMPopupWindow.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MMPopupWindow.h; sourceTree = "<group>"; };
		6713CA432681CED100C3DB03 /* MMPopupCategory.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MMPopupCategory.h; sourceTree = "<group>"; };
		6713CA442681CED100C3DB03 /* MMPopupItem.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MMPopupItem.m; sourceTree = "<group>"; };
		6713CA452681CED100C3DB03 /* Singleton.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Singleton.h; sourceTree = "<group>"; };
		6713CA462681CED100C3DB03 /* XYView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = XYView.m; sourceTree = "<group>"; };
		6713CA472681CED100C3DB03 /* JCMessageImageView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCMessageImageView.m; sourceTree = "<group>"; };
		6713CA482681CED100C3DB03 /* UIView+XYCategory.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIView+XYCategory.m"; sourceTree = "<group>"; };
		6713CA492681CED100C3DB03 /* JCDeviceOffSetInfoModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCDeviceOffSetInfoModel.h; sourceTree = "<group>"; };
		6713CA4A2681CED100C3DB03 /* JCSelectDeviceModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCSelectDeviceModel.m; sourceTree = "<group>"; };
		6713CA4C2681CED100C3DB03 /* JCRecordTool.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCRecordTool.h; sourceTree = "<group>"; };
		6713CA4D2681CED100C3DB03 /* JCRecordTool.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCRecordTool.m; sourceTree = "<group>"; };
		6713CA4F2681CED100C3DB03 /* JCRecordModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCRecordModel.m; sourceTree = "<group>"; };
		6713CA502681CED100C3DB03 /* JCStatisticsModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCStatisticsModel.h; sourceTree = "<group>"; };
		6713CA512681CED100C3DB03 /* JCRecordModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCRecordModel.h; sourceTree = "<group>"; };
		6713CA522681CED100C3DB03 /* JCStatisticsModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCStatisticsModel.m; sourceTree = "<group>"; };
		6713CA542681CED100C3DB03 /* MBProgressHUD+Extension.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "MBProgressHUD+Extension.h"; sourceTree = "<group>"; };
		6713CA552681CED100C3DB03 /* MBProgressHUD+Extension.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "MBProgressHUD+Extension.m"; sourceTree = "<group>"; };
		6713CA562681CED100C3DB03 /* JCPrintDevice.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCPrintDevice.h; sourceTree = "<group>"; };
		6713CA572681CED100C3DB03 /* JCDeviceUpdateTool.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCDeviceUpdateTool.h; sourceTree = "<group>"; };
		6713CA582681CED100C3DB03 /* JCPrinterModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCPrinterModel.h; sourceTree = "<group>"; };
		6713CA592681CED100C3DB03 /* JCMessageImageView.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = JCMessageImageView.xib; sourceTree = "<group>"; };
		6713CA5A2681CED100C3DB03 /* XYView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XYView.h; sourceTree = "<group>"; };
		6713CA5B2681CED100C3DB03 /* JCMessageImageView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCMessageImageView.h; sourceTree = "<group>"; };
		6713CA5D2681CED100C3DB03 /* JCWifiManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCWifiManager.h; sourceTree = "<group>"; };
		6713CA5E2681CED100C3DB03 /* JCBluetoothModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCBluetoothModel.m; sourceTree = "<group>"; };
		6713CA5F2681CED100C3DB03 /* JCDeviceSeriesHelp.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCDeviceSeriesHelp.h; sourceTree = "<group>"; };
		6713CA602681CED100C3DB03 /* JCBluetoothManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCBluetoothManager.m; sourceTree = "<group>"; };
		6713CA612681CED100C3DB03 /* JCPrinterTypeHelp.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCPrinterTypeHelp.m; sourceTree = "<group>"; };
		6713CA622681CED100C3DB03 /* JCWifiManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCWifiManager.m; sourceTree = "<group>"; };
		6713CA632681CED100C3DB03 /* JCDeviceSeriesHelp.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCDeviceSeriesHelp.m; sourceTree = "<group>"; };
		6713CA642681CED100C3DB03 /* JCBluetoothModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCBluetoothModel.h; sourceTree = "<group>"; };
		6713CA652681CED100C3DB03 /* JCBluetoothManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCBluetoothManager.h; sourceTree = "<group>"; };
		6713CA662681CED100C3DB03 /* JCPrinterTypeHelp.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCPrinterTypeHelp.h; sourceTree = "<group>"; };
		6713CA672681CED100C3DB03 /* JCUniqueModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCUniqueModel.h; sourceTree = "<group>"; };
		6713CA692681CED100C3DB03 /* JCTemplateEditController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCTemplateEditController.m; sourceTree = "<group>"; };
		6713CA6A2681CED100C3DB03 /* JCTemplateEditController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCTemplateEditController.h; sourceTree = "<group>"; };
		6713CA6C2681CED100C3DB03 /* JCTemplateData4DBManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCTemplateData4DBManager.m; sourceTree = "<group>"; };
		6713CA6D2681CED100C3DB03 /* JCElementOperateManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCElementOperateManager.h; sourceTree = "<group>"; };
		6713CA6E2681CED100C3DB03 /* UIImageView+DownLoad.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIImageView+DownLoad.m"; sourceTree = "<group>"; };
		6713CA6F2681CED100C3DB03 /* JCFontManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCFontManager.h; sourceTree = "<group>"; };
		6713CA702681CED100C3DB03 /* JCElementToItem.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCElementToItem.m; sourceTree = "<group>"; };
		6713CA712681CED100C3DB03 /* JCOssApi.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCOssApi.m; sourceTree = "<group>"; };
		6713CA722681CED100C3DB03 /* JCFontDownLoadManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCFontDownLoadManager.m; sourceTree = "<group>"; };
		6713CA732681CED100C3DB03 /* JCBoardTemplateManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCBoardTemplateManager.h; sourceTree = "<group>"; };
		6713CA742681CED100C3DB03 /* UIImage+uploadRequest.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIImage+uploadRequest.h"; sourceTree = "<group>"; };
		6713CA752681CED100C3DB03 /* JCScanCodeManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCScanCodeManager.h; sourceTree = "<group>"; };
		6713CA762681CED100C3DB03 /* JCStack.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCStack.m; sourceTree = "<group>"; };
		6713CA772681CED100C3DB03 /* JCDrawInfoManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCDrawInfoManager.h; sourceTree = "<group>"; };
		6713CA782681CED100C3DB03 /* JCItemToElement.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCItemToElement.m; sourceTree = "<group>"; };
		6713CA792681CED100C3DB03 /* JCTMDataBindGoodsInfoManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCTMDataBindGoodsInfoManager.m; sourceTree = "<group>"; };
		6713CA7A2681CED100C3DB03 /* JCTemplateImageManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCTemplateImageManager.h; sourceTree = "<group>"; };
		6713CA7B2681CED100C3DB03 /* JCDefaultElementManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCDefaultElementManager.h; sourceTree = "<group>"; };
		6713CA7C2681CED100C3DB03 /* JCElementEditManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCElementEditManager.h; sourceTree = "<group>"; };
		6713CA7D2681CED100C3DB03 /* JCElementOperateManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCElementOperateManager.m; sourceTree = "<group>"; };
		6713CA7E2681CED100C3DB03 /* JCTemplateData4DBManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCTemplateData4DBManager.h; sourceTree = "<group>"; };
		6713CA7F2681CED100C3DB03 /* UIImageView+DownLoad.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIImageView+DownLoad.h"; sourceTree = "<group>"; };
		6713CA802681CED100C3DB03 /* JCTemplateImageManager.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = JCTemplateImageManager.mm; sourceTree = "<group>"; };
		6713CA812681CED100C3DB03 /* JCStack.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCStack.h; sourceTree = "<group>"; };
		6713CA822681CED100C3DB03 /* JCScanCodeManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCScanCodeManager.m; sourceTree = "<group>"; };
		6713CA832681CED100C3DB03 /* UIImage+uploadRequest.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIImage+uploadRequest.m"; sourceTree = "<group>"; };
		6713CA842681CED100C3DB03 /* JCBoardTemplateManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCBoardTemplateManager.m; sourceTree = "<group>"; };
		6713CA852681CED100C3DB03 /* JCFontDownLoadManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCFontDownLoadManager.h; sourceTree = "<group>"; };
		6713CA862681CED100C3DB03 /* JCOssApi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCOssApi.h; sourceTree = "<group>"; };
		6713CA872681CED100C3DB03 /* JCElementToItem.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCElementToItem.h; sourceTree = "<group>"; };
		6713CA882681CED100C3DB03 /* JCFontManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCFontManager.m; sourceTree = "<group>"; };
		6713CA892681CED100C3DB03 /* JCElementEditManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCElementEditManager.m; sourceTree = "<group>"; };
		6713CA8A2681CED100C3DB03 /* JCDefaultElementManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCDefaultElementManager.m; sourceTree = "<group>"; };
		6713CA8B2681CED100C3DB03 /* JCTMDataBindGoodsInfoManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCTMDataBindGoodsInfoManager.h; sourceTree = "<group>"; };
		6713CA8C2681CED100C3DB03 /* JCItemToElement.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCItemToElement.h; sourceTree = "<group>"; };
		6713CA8D2681CED100C3DB03 /* JCDrawInfoManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCDrawInfoManager.m; sourceTree = "<group>"; };
		6713CA8F2681CED100C3DB03 /* JCTemplateData+External.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "JCTemplateData+External.h"; sourceTree = "<group>"; };
		6713CA902681CED100C3DB03 /* JCTemplateList.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCTemplateList.h; sourceTree = "<group>"; };
		6713CA912681CED100C3DB03 /* JCElementModel+Transfer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "JCElementModel+Transfer.m"; sourceTree = "<group>"; };
		6713CA922681CED100C3DB03 /* JCGoodDetailInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCGoodDetailInfo.h; sourceTree = "<group>"; };
		6713CA932681CED100C3DB03 /* JCTemplateData+SDK.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "JCTemplateData+SDK.h"; sourceTree = "<group>"; };
		6713CA942681CED100C3DB03 /* JCFontModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCFontModel.m; sourceTree = "<group>"; };
		6713CA952681CED100C3DB03 /* JCTemplateData.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCTemplateData.m; sourceTree = "<group>"; };
		6713CA962681CED100C3DB03 /* JCElementModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCElementModel.m; sourceTree = "<group>"; };
		6713CA972681CED100C3DB03 /* JCTemplateData+PrintConfigure.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "JCTemplateData+PrintConfigure.m"; sourceTree = "<group>"; };
		6713CA982681CED100C3DB03 /* JCTemplateData+Transfer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "JCTemplateData+Transfer.m"; sourceTree = "<group>"; };
		6713CA992681CED100C3DB03 /* JCFontSize.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCFontSize.h; sourceTree = "<group>"; };
		6713CA9A2681CED100C3DB03 /* XYBaseModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XYBaseModel.h; sourceTree = "<group>"; };
		6713CA9B2681CED100C3DB03 /* JCTemplateData4DB.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCTemplateData4DB.h; sourceTree = "<group>"; };
		6713CA9C2681CED100C3DB03 /* JCPrintSamePreviewModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCPrintSamePreviewModel.h; sourceTree = "<group>"; };
		6713CA9D2681CED100C3DB03 /* JCElementModel+Transfer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "JCElementModel+Transfer.h"; sourceTree = "<group>"; };
		6713CA9E2681CED100C3DB03 /* JCTemplateList.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCTemplateList.m; sourceTree = "<group>"; };
		6713CA9F2681CED100C3DB03 /* JCTemplateData+External.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "JCTemplateData+External.m"; sourceTree = "<group>"; };
		6713CAA02681CED100C3DB03 /* JCGoodDetailInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCGoodDetailInfo.m; sourceTree = "<group>"; };
		6713CAA12681CED100C3DB03 /* JCTemplateData+PrintConfigure.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "JCTemplateData+PrintConfigure.h"; sourceTree = "<group>"; };
		6713CAA22681CED100C3DB03 /* JCElementModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCElementModel.h; sourceTree = "<group>"; };
		6713CAA32681CED100C3DB03 /* JCTemplateData.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCTemplateData.h; sourceTree = "<group>"; };
		6713CAA42681CED100C3DB03 /* JCFontModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCFontModel.h; sourceTree = "<group>"; };
		6713CAA52681CED100C3DB03 /* JCTemplateData+SDK.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = "JCTemplateData+SDK.mm"; sourceTree = "<group>"; };
		6713CAA62681CED100C3DB03 /* JCPrintSamePreviewModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCPrintSamePreviewModel.m; sourceTree = "<group>"; };
		6713CAA72681CED100C3DB03 /* JCTemplateData4DB.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCTemplateData4DB.m; sourceTree = "<group>"; };
		6713CAA82681CED100C3DB03 /* XYBaseModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = XYBaseModel.m; sourceTree = "<group>"; };
		6713CAA92681CED100C3DB03 /* JCFontSize.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCFontSize.m; sourceTree = "<group>"; };
		6713CAAA2681CED100C3DB03 /* JCTemplateData+Transfer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "JCTemplateData+Transfer.h"; sourceTree = "<group>"; };
		6713CAAD2681CED100C3DB03 /* JCMutablePropertyView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCMutablePropertyView.h; sourceTree = "<group>"; };
		6713CAAE2681CED100C3DB03 /* JCDisplacementView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCDisplacementView.m; sourceTree = "<group>"; };
		6713CAAF2681CED100C3DB03 /* JCStyleContentView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCStyleContentView.m; sourceTree = "<group>"; };
		6713CAB02681CED100C3DB03 /* JCStyleModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCStyleModel.m; sourceTree = "<group>"; };
		6713CAB12681CED100C3DB03 /* JCDisplacementView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCDisplacementView.h; sourceTree = "<group>"; };
		6713CAB22681CED100C3DB03 /* JCMutablePropertyView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCMutablePropertyView.m; sourceTree = "<group>"; };
		6713CAB32681CED100C3DB03 /* JCStyleContentView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCStyleContentView.h; sourceTree = "<group>"; };
		6713CAB42681CED100C3DB03 /* JCStyleModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCStyleModel.h; sourceTree = "<group>"; };
		6713CAB62681CED100C3DB03 /* JCTemplateAntiLostCollectionView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCTemplateAntiLostCollectionView.m; sourceTree = "<group>"; };
		6713CAB72681CED100C3DB03 /* JCTemplateAntiLostHorizontalCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCTemplateAntiLostHorizontalCell.m; sourceTree = "<group>"; };
		6713CAB82681CED100C3DB03 /* JCTemplateAntiLostChooseView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCTemplateAntiLostChooseView.m; sourceTree = "<group>"; };
		6713CAB92681CED100C3DB03 /* JCTemplateAntiLostCollectionView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCTemplateAntiLostCollectionView.h; sourceTree = "<group>"; };
		6713CABA2681CED100C3DB03 /* JCTemplateAntiLostChooseView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCTemplateAntiLostChooseView.h; sourceTree = "<group>"; };
		6713CABB2681CED100C3DB03 /* JCTemplateAntiLostHorizontalCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCTemplateAntiLostHorizontalCell.h; sourceTree = "<group>"; };
		6713CABD2681CED100C3DB03 /* JCAddElementBar.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCAddElementBar.m; sourceTree = "<group>"; };
		6713CABE2681CED100C3DB03 /* JCElementEditBar.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCElementEditBar.m; sourceTree = "<group>"; };
		6713CABF2681CED100C3DB03 /* JCActionButton.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCActionButton.m; sourceTree = "<group>"; };
		6713CAC02681CED100C3DB03 /* JCAddElementView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCAddElementView.m; sourceTree = "<group>"; };
		6713CAC12681CED100C3DB03 /* JCElementEditBar.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCElementEditBar.h; sourceTree = "<group>"; };
		6713CAC22681CED100C3DB03 /* JCAddElementBar.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCAddElementBar.h; sourceTree = "<group>"; };
		6713CAC32681CED100C3DB03 /* JCAddElementView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCAddElementView.h; sourceTree = "<group>"; };
		6713CAC42681CED100C3DB03 /* JCActionButton.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCActionButton.h; sourceTree = "<group>"; };
		6713CAC62681CED100C3DB03 /* JCPrintSettingView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCPrintSettingView.h; sourceTree = "<group>"; };
		6713CAC72681CED100C3DB03 /* JCTagListManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCTagListManager.m; sourceTree = "<group>"; };
		6713CAC82681CED100C3DB03 /* JCTagModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCTagModel.m; sourceTree = "<group>"; };
		6713CAC92681CED100C3DB03 /* JCTagListLeftCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCTagListLeftCell.h; sourceTree = "<group>"; };
		6713CACA2681CED100C3DB03 /* UIView+PlaceHolder.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIView+PlaceHolder.m"; sourceTree = "<group>"; };
		6713CACB2681CED100C3DB03 /* JCTagImageTableViewCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCTagImageTableViewCell.m; sourceTree = "<group>"; };
		6713CACC2681CED100C3DB03 /* JCPropertyMainView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCPropertyMainView.m; sourceTree = "<group>"; };
		6713CACD2681CED100C3DB03 /* JCLogoCategoryModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCLogoCategoryModel.m; sourceTree = "<group>"; };
		6713CACE2681CED100C3DB03 /* JCProgressView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCProgressView.m; sourceTree = "<group>"; };
		6713CACF2681CED100C3DB03 /* JCBoardRightItemView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCBoardRightItemView.m; sourceTree = "<group>"; };
		6713CAD02681CED100C3DB03 /* JCPrintAlertView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCPrintAlertView.m; sourceTree = "<group>"; };
		6713CAD12681CED100C3DB03 /* JCDrawNavigationBar.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCDrawNavigationBar.m; sourceTree = "<group>"; };
		6713CAD22681CED100C3DB03 /* JCTagChooseListView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCTagChooseListView.h; sourceTree = "<group>"; };
		6713CAD32681CED100C3DB03 /* JCSearchTagTextField.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCSearchTagTextField.h; sourceTree = "<group>"; };
		6713CAD42681CED100C3DB03 /* JCDrawNavigationBar4AntiLost.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCDrawNavigationBar4AntiLost.h; sourceTree = "<group>"; };
		6713CAD52681CED100C3DB03 /* JCTagListManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCTagListManager.h; sourceTree = "<group>"; };
		6713CAD72681CED100C3DB03 /* JCElementActionBar.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCElementActionBar.h; sourceTree = "<group>"; };
		6713CAD82681CED100C3DB03 /* JCElementActionBar.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCElementActionBar.m; sourceTree = "<group>"; };
		6713CADA2681CED100C3DB03 /* JCLineStyleView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCLineStyleView.m; sourceTree = "<group>"; };
		6713CADB2681CED100C3DB03 /* JCAlignView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCAlignView.m; sourceTree = "<group>"; };
		6713CADC2681CED100C3DB03 /* JCInputTextView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCInputTextView.m; sourceTree = "<group>"; };
		6713CADD2681CED100C3DB03 /* JCFontSelectView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCFontSelectView.m; sourceTree = "<group>"; };
		6713CADE2681CED100C3DB03 /* JCBorderCollectionCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCBorderCollectionCell.m; sourceTree = "<group>"; };
		6713CADF2681CED100C3DB03 /* JCDateFormatView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCDateFormatView.h; sourceTree = "<group>"; };
		6713CAE02681CED100C3DB03 /* JCBorderView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCBorderView.m; sourceTree = "<group>"; };
		6713CAE12681CED100C3DB03 /* JCIconBorderManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCIconBorderManager.m; sourceTree = "<group>"; };
		6713CAE22681CED100C3DB03 /* JCFontTableViewCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCFontTableViewCell.h; sourceTree = "<group>"; };
		6713CAE42681CED100C3DB03 /* JCImagePropertyView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCImagePropertyView.h; sourceTree = "<group>"; };
		6713CAE52681CED100C3DB03 /* JCImagePropertyView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCImagePropertyView.m; sourceTree = "<group>"; };
		6713CAE62681CED100C3DB03 /* JCInputTextView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCInputTextView.h; sourceTree = "<group>"; };
		6713CAE72681CED100C3DB03 /* JCAlignView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCAlignView.h; sourceTree = "<group>"; };
		6713CAE82681CED100C3DB03 /* JCLineStyleView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCLineStyleView.h; sourceTree = "<group>"; };
		6713CAE92681CED100C3DB03 /* JCDateFormatView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCDateFormatView.m; sourceTree = "<group>"; };
		6713CAEB2681CED100C3DB03 /* JCTextStyleView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCTextStyleView.m; sourceTree = "<group>"; };
		6713CAEC2681CED100C3DB03 /* JCTextStyleView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCTextStyleView.h; sourceTree = "<group>"; };
		6713CAED2681CED100C3DB03 /* JCBorderCollectionCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCBorderCollectionCell.h; sourceTree = "<group>"; };
		6713CAEF2681CED100C3DB03 /* JCIconDataManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCIconDataManager.h; sourceTree = "<group>"; };
		6713CAF02681CED100C3DB03 /* JCIConObject.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCIConObject.m; sourceTree = "<group>"; };
		6713CAF12681CED100C3DB03 /* JCIconCollectionViewCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCIconCollectionViewCell.h; sourceTree = "<group>"; };
		6713CAF22681CED100C3DB03 /* JCIconView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCIconView.m; sourceTree = "<group>"; };
		6713CAF32681CED100C3DB03 /* JCIconDataManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCIconDataManager.m; sourceTree = "<group>"; };
		6713CAF42681CED100C3DB03 /* JCIconCollectionViewCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCIconCollectionViewCell.m; sourceTree = "<group>"; };
		6713CAF52681CED100C3DB03 /* JCIConObject.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCIConObject.h; sourceTree = "<group>"; };
		6713CAF62681CED100C3DB03 /* JCIconView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCIconView.h; sourceTree = "<group>"; };
		6713CAF72681CED100C3DB03 /* JCFontSelectView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCFontSelectView.h; sourceTree = "<group>"; };
		6713CAF82681CED100C3DB03 /* JCFontTableViewCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCFontTableViewCell.m; sourceTree = "<group>"; };
		6713CAF92681CED100C3DB03 /* JCIconBorderManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCIconBorderManager.h; sourceTree = "<group>"; };
		6713CAFA2681CED100C3DB03 /* JCBorderView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCBorderView.h; sourceTree = "<group>"; };
		6713CAFB2681CED100C3DB03 /* JCPrintSettingView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCPrintSettingView.m; sourceTree = "<group>"; };
		6713CAFC2681CED200C3DB03 /* JCTagListLeftCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCTagListLeftCell.m; sourceTree = "<group>"; };
		6713CAFD2681CED200C3DB03 /* JCTagModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCTagModel.h; sourceTree = "<group>"; };
		6713CAFF2681CED200C3DB03 /* JCBarData.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCBarData.m; sourceTree = "<group>"; };
		6713CB002681CED200C3DB03 /* JCElementPropertyBar.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCElementPropertyBar.h; sourceTree = "<group>"; };
		6713CB012681CED200C3DB03 /* JCBarCollectionCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCBarCollectionCell.h; sourceTree = "<group>"; };
		6713CB022681CED200C3DB03 /* JCBarData.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCBarData.h; sourceTree = "<group>"; };
		6713CB032681CED200C3DB03 /* JCBarCollectionCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCBarCollectionCell.m; sourceTree = "<group>"; };
		6713CB042681CED200C3DB03 /* JCElementPropertyBar.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCElementPropertyBar.m; sourceTree = "<group>"; };
		6713CB052681CED200C3DB03 /* JCLogoCategoryModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCLogoCategoryModel.h; sourceTree = "<group>"; };
		6713CB062681CED200C3DB03 /* JCProgressView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCProgressView.h; sourceTree = "<group>"; };
		6713CB072681CED200C3DB03 /* JCTagImageTableViewCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCTagImageTableViewCell.h; sourceTree = "<group>"; };
		6713CB082681CED200C3DB03 /* JCPropertyMainView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCPropertyMainView.h; sourceTree = "<group>"; };
		6713CB092681CED200C3DB03 /* UIView+PlaceHolder.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIView+PlaceHolder.h"; sourceTree = "<group>"; };
		6713CB0A2681CED200C3DB03 /* JCDrawNavigationBar4AntiLost.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCDrawNavigationBar4AntiLost.m; sourceTree = "<group>"; };
		6713CB0B2681CED200C3DB03 /* JCSearchTagTextField.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCSearchTagTextField.m; sourceTree = "<group>"; };
		6713CB0C2681CED200C3DB03 /* JCTagChooseListView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCTagChooseListView.m; sourceTree = "<group>"; };
		6713CB0D2681CED200C3DB03 /* JCDrawNavigationBar.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCDrawNavigationBar.h; sourceTree = "<group>"; };
		6713CB0E2681CED200C3DB03 /* JCPrintAlertView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCPrintAlertView.h; sourceTree = "<group>"; };
		6713CB0F2681CED200C3DB03 /* JCBoardRightItemView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCBoardRightItemView.h; sourceTree = "<group>"; };
		6713CB112681CED200C3DB03 /* JCElementBaseView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCElementBaseView.m; sourceTree = "<group>"; };
		6713CB132681CED200C3DB03 /* JCElementGraph.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCElementGraph.h; sourceTree = "<group>"; };
		6713CB142681CED200C3DB03 /* JCElementBox.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCElementBox.h; sourceTree = "<group>"; };
		6713CB152681CED200C3DB03 /* JCElementGraph.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = JCElementGraph.mm; sourceTree = "<group>"; };
		6713CB162681CED200C3DB03 /* JCEleBoxConfigure.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCEleBoxConfigure.h; sourceTree = "<group>"; };
		6713CB172681CED200C3DB03 /* JCElementBox.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCElementBox.m; sourceTree = "<group>"; };
		6713CB182681CED200C3DB03 /* JCEleBoxConfigure.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCEleBoxConfigure.m; sourceTree = "<group>"; };
		6713CB1A2681CED200C3DB03 /* JCElementText.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCElementText.m; sourceTree = "<group>"; };
		6713CB1B2681CED200C3DB03 /* JCElementTextConfigure.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCElementTextConfigure.m; sourceTree = "<group>"; };
		6713CB1C2681CED200C3DB03 /* JCElementTextImage.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCElementTextImage.h; sourceTree = "<group>"; };
		6713CB1D2681CED200C3DB03 /* JCElementText.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCElementText.h; sourceTree = "<group>"; };
		6713CB1E2681CED200C3DB03 /* JCElementTextImage.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = JCElementTextImage.mm; sourceTree = "<group>"; };
		6713CB1F2681CED200C3DB03 /* JCElementTextConfigure.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCElementTextConfigure.h; sourceTree = "<group>"; };
		6713CB212681CED200C3DB03 /* JCDrawCodeManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCDrawCodeManager.h; sourceTree = "<group>"; };
		6713CB222681CED200C3DB03 /* JCElementBarCode.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCElementBarCode.h; sourceTree = "<group>"; };
		6713CB232681CED200C3DB03 /* JCElementBarCodeConfigure.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCElementBarCodeConfigure.m; sourceTree = "<group>"; };
		6713CB242681CED200C3DB03 /* JCElementBarCode.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCElementBarCode.m; sourceTree = "<group>"; };
		6713CB252681CED200C3DB03 /* JCDrawCodeManager.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = JCDrawCodeManager.mm; sourceTree = "<group>"; };
		6713CB262681CED200C3DB03 /* JCElementBarCodeConfigure.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCElementBarCodeConfigure.h; sourceTree = "<group>"; };
		6713CB282681CED200C3DB03 /* JCElementLogoView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCElementLogoView.h; sourceTree = "<group>"; };
		6713CB292681CED200C3DB03 /* JCElementLogoView.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = JCElementLogoView.mm; sourceTree = "<group>"; };
		6713CB2A2681CED200C3DB03 /* JCElementBaseView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCElementBaseView.h; sourceTree = "<group>"; };
		6713CB2C2681CED200C3DB03 /* JCEleLineConfigure.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCEleLineConfigure.h; sourceTree = "<group>"; };
		6713CB2D2681CED200C3DB03 /* JCElementLine.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCElementLine.h; sourceTree = "<group>"; };
		6713CB2E2681CED200C3DB03 /* JCEleLineConfigure.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCEleLineConfigure.m; sourceTree = "<group>"; };
		6713CB2F2681CED200C3DB03 /* JCElementLine.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCElementLine.m; sourceTree = "<group>"; };
		6713CB312681CED200C3DB03 /* JCElementSerialNumber.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCElementSerialNumber.h; sourceTree = "<group>"; };
		6713CB322681CED200C3DB03 /* JCSerialStyleConfigure.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCSerialStyleConfigure.m; sourceTree = "<group>"; };
		6713CB332681CED200C3DB03 /* JCElementSerialNumber.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCElementSerialNumber.m; sourceTree = "<group>"; };
		6713CB342681CED200C3DB03 /* JCSerialStyleConfigure.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCSerialStyleConfigure.h; sourceTree = "<group>"; };
		6713CB362681CED200C3DB03 /* JCEleTimeConfigure.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCEleTimeConfigure.h; sourceTree = "<group>"; };
		6713CB372681CED200C3DB03 /* JCElementTime.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCElementTime.m; sourceTree = "<group>"; };
		6713CB382681CED200C3DB03 /* JCEleTimeConfigure.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCEleTimeConfigure.m; sourceTree = "<group>"; };
		6713CB392681CED200C3DB03 /* JCElementTime.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCElementTime.h; sourceTree = "<group>"; };
		6713CB3B2681CED200C3DB03 /* JCElementQRCode.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCElementQRCode.h; sourceTree = "<group>"; };
		6713CB3C2681CED200C3DB03 /* JCElementQRCode.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCElementQRCode.m; sourceTree = "<group>"; };
		6713CB3D2681CED200C3DB03 /* JCElementConfigure.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCElementConfigure.h; sourceTree = "<group>"; };
		6713CB3E2681CED200C3DB03 /* JCElements.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCElements.h; sourceTree = "<group>"; };
		6713CB402681CED200C3DB03 /* JCPrintSamePreview.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCPrintSamePreview.m; sourceTree = "<group>"; };
		6713CB412681CED200C3DB03 /* JCPrintSamePreview.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCPrintSamePreview.h; sourceTree = "<group>"; };
		6713CB432681CED200C3DB03 /* UIImage+Dotted.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIImage+Dotted.h"; sourceTree = "<group>"; };
		6713CB442681CED200C3DB03 /* JCStyleButton.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCStyleButton.h; sourceTree = "<group>"; };
		6713CB452681CED200C3DB03 /* JCStyleTitleLabel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCStyleTitleLabel.h; sourceTree = "<group>"; };
		6713CB462681CED200C3DB03 /* NSDate+XHExtension.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSDate+XHExtension.m"; sourceTree = "<group>"; };
		6713CB472681CED200C3DB03 /* JCSliderView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCSliderView.m; sourceTree = "<group>"; };
		6713CB482681CED200C3DB03 /* UIView+Border.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIView+Border.m"; sourceTree = "<group>"; };
		6713CB492681CED200C3DB03 /* JCMoveView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCMoveView.m; sourceTree = "<group>"; };
		6713CB4A2681CED200C3DB03 /* JCSelectionView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCSelectionView.h; sourceTree = "<group>"; };
		6713CB4B2681CED200C3DB03 /* JCNormalPicker.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCNormalPicker.m; sourceTree = "<group>"; };
		6713CB4C2681CED200C3DB03 /* JCFontSliderView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCFontSliderView.m; sourceTree = "<group>"; };
		6713CB4D2681CED200C3DB03 /* JCDownloadLayout.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCDownloadLayout.h; sourceTree = "<group>"; };
		6713CB4E2681CED200C3DB03 /* JCDatePicker.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCDatePicker.m; sourceTree = "<group>"; };
		6713CB4F2681CED200C3DB03 /* JCElementDottedView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCElementDottedView.h; sourceTree = "<group>"; };
		6713CB502681CED200C3DB03 /* JCSliderView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCSliderView.h; sourceTree = "<group>"; };
		6713CB512681CED200C3DB03 /* NSDate+XHExtension.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSDate+XHExtension.h"; sourceTree = "<group>"; };
		6713CB522681CED200C3DB03 /* JCStyleButton.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCStyleButton.m; sourceTree = "<group>"; };
		6713CB532681CED200C3DB03 /* JCStyleTitleLabel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCStyleTitleLabel.m; sourceTree = "<group>"; };
		6713CB542681CED200C3DB03 /* UIImage+Dotted.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIImage+Dotted.m"; sourceTree = "<group>"; };
		6713CB552681CED200C3DB03 /* UIView+Border.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIView+Border.h"; sourceTree = "<group>"; };
		6713CB562681CED200C3DB03 /* JCNormalPicker.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCNormalPicker.h; sourceTree = "<group>"; };
		6713CB572681CED200C3DB03 /* JCSelectionView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCSelectionView.m; sourceTree = "<group>"; };
		6713CB582681CED200C3DB03 /* JCMoveView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCMoveView.h; sourceTree = "<group>"; };
		6713CB592681CED200C3DB03 /* JCElementDottedView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCElementDottedView.m; sourceTree = "<group>"; };
		6713CB5A2681CED200C3DB03 /* JCDatePicker.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCDatePicker.h; sourceTree = "<group>"; };
		6713CB5B2681CED200C3DB03 /* JCDownloadLayout.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCDownloadLayout.m; sourceTree = "<group>"; };
		6713CB5C2681CED200C3DB03 /* JCFontSliderView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCFontSliderView.h; sourceTree = "<group>"; };
		6713CB5E2681CED200C3DB03 /* JCTemplateHistoryView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCTemplateHistoryView.h; sourceTree = "<group>"; };
		6713CB5F2681CED200C3DB03 /* JCTemplateChoosView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCTemplateChoosView.h; sourceTree = "<group>"; };
		6713CB602681CED200C3DB03 /* JCTag.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCTag.h; sourceTree = "<group>"; };
		6713CB612681CED200C3DB03 /* JCTemplateHorizontalCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCTemplateHorizontalCell.m; sourceTree = "<group>"; };
		6713CB622681CED200C3DB03 /* JCTemplateVerticalCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCTemplateVerticalCell.m; sourceTree = "<group>"; };
		6713CB632681CED200C3DB03 /* JCTemplateTagModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCTemplateTagModel.m; sourceTree = "<group>"; };
		6713CB642681CED200C3DB03 /* JCTemplateCollectionView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCTemplateCollectionView.m; sourceTree = "<group>"; };
		6713CB652681CED200C3DB03 /* JCTemplateChoosView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCTemplateChoosView.m; sourceTree = "<group>"; };
		6713CB662681CED200C3DB03 /* JCTemplateHistoryView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCTemplateHistoryView.m; sourceTree = "<group>"; };
		6713CB672681CED200C3DB03 /* JCTemplateHorizontalCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCTemplateHorizontalCell.h; sourceTree = "<group>"; };
		6713CB682681CED200C3DB03 /* JCTag.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCTag.m; sourceTree = "<group>"; };
		6713CB692681CED200C3DB03 /* JCTemplateCollectionView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCTemplateCollectionView.h; sourceTree = "<group>"; };
		6713CB6A2681CED200C3DB03 /* JCTemplateVerticalCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCTemplateVerticalCell.h; sourceTree = "<group>"; };
		6713CB6B2681CED200C3DB03 /* JCTemplateTagModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCTemplateTagModel.h; sourceTree = "<group>"; };
		6713CB6D2681CED200C3DB03 /* JCDrawBoardView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCDrawBoardView.m; sourceTree = "<group>"; };
		6713CB6E2681CED200C3DB03 /* JCPrintDirectionView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCPrintDirectionView.m; sourceTree = "<group>"; };
		6713CB6F2681CED200C3DB03 /* JCDrawBoardView+Undo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "JCDrawBoardView+Undo.h"; sourceTree = "<group>"; };
		6713CB702681CED200C3DB03 /* JCBoardTitleView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCBoardTitleView.h; sourceTree = "<group>"; };
		6713CB712681CED200C3DB03 /* RulerLayout.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RulerLayout.h; sourceTree = "<group>"; };
		6713CB722681CED200C3DB03 /* JCScaleResertView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCScaleResertView.m; sourceTree = "<group>"; };
		6713CB732681CED200C3DB03 /* JCBoardConst.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCBoardConst.m; sourceTree = "<group>"; };
		6713CB742681CED200C3DB03 /* JCBubbleBar.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCBubbleBar.m; sourceTree = "<group>"; };
		6713CB752681CED200C3DB03 /* JCCanvas.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCCanvas.m; sourceTree = "<group>"; };
		6713CB762681CED200C3DB03 /* JCRulerView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCRulerView.h; sourceTree = "<group>"; };
		6713CB772681CED200C3DB03 /* RulerCollectionViewCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RulerCollectionViewCell.h; sourceTree = "<group>"; };
		6713CB782681CED200C3DB03 /* JCElementMainView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCElementMainView.m; sourceTree = "<group>"; };
		6713CB792681CED200C3DB03 /* RulerView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RulerView.m; sourceTree = "<group>"; };
		6713CB7A2681CED200C3DB03 /* JCBoardConst.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCBoardConst.h; sourceTree = "<group>"; };
		6713CB7B2681CED200C3DB03 /* JCScaleResertView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCScaleResertView.h; sourceTree = "<group>"; };
		6713CB7C2681CED200C3DB03 /* RulerLayout.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RulerLayout.m; sourceTree = "<group>"; };
		6713CB7D2681CED200C3DB03 /* JCBoardTitleView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCBoardTitleView.m; sourceTree = "<group>"; };
		6713CB7E2681CED200C3DB03 /* JCDrawBoardView+Undo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "JCDrawBoardView+Undo.m"; sourceTree = "<group>"; };
		6713CB7F2681CED200C3DB03 /* JCDrawBoardView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCDrawBoardView.h; sourceTree = "<group>"; };
		6713CB802681CED200C3DB03 /* JCPrintDirectionView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCPrintDirectionView.h; sourceTree = "<group>"; };
		6713CB812681CED200C3DB03 /* JCRulerView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCRulerView.m; sourceTree = "<group>"; };
		6713CB822681CED200C3DB03 /* JCCanvas.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCCanvas.h; sourceTree = "<group>"; };
		6713CB832681CED200C3DB03 /* JCBubbleBar.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCBubbleBar.h; sourceTree = "<group>"; };
		6713CB842681CED200C3DB03 /* RulerView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RulerView.h; sourceTree = "<group>"; };
		6713CB852681CED200C3DB03 /* JCElementMainView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCElementMainView.h; sourceTree = "<group>"; };
		6713CB862681CED200C3DB03 /* RulerCollectionViewCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RulerCollectionViewCell.m; sourceTree = "<group>"; };
		6713CC492681CF9000C3DB03 /* JCWKWebView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCWKWebView.m; sourceTree = "<group>"; };
		6713CC4B2681CF9000C3DB03 /* NSString+JC_Device.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSString+JC_Device.h"; sourceTree = "<group>"; };
		6713CC4C2681CF9000C3DB03 /* UIColor+Extension.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIColor+Extension.h"; sourceTree = "<group>"; };
		6713CC4D2681CF9000C3DB03 /* UIView+JCCategory.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIView+JCCategory.h"; sourceTree = "<group>"; };
		6713CC4E2681CF9000C3DB03 /* NSTimer+addition.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSTimer+addition.h"; sourceTree = "<group>"; };
		6713CC4F2681CF9000C3DB03 /* NSTimer+addition.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSTimer+addition.m"; sourceTree = "<group>"; };
		6713CC502681CF9000C3DB03 /* UIView+JCCategory.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIView+JCCategory.m"; sourceTree = "<group>"; };
		6713CC512681CF9000C3DB03 /* UIColor+Extension.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIColor+Extension.m"; sourceTree = "<group>"; };
		6713CC522681CF9000C3DB03 /* NSString+JC_Device.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSString+JC_Device.m"; sourceTree = "<group>"; };
		6713CC532681CF9000C3DB03 /* JCWKWebViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCWKWebViewController.h; sourceTree = "<group>"; };
		6713CC542681CF9000C3DB03 /* AppDelegate+Share.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "AppDelegate+Share.h"; sourceTree = "<group>"; };
		6713CC552681CF9000C3DB03 /* JCPayManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCPayManager.h; sourceTree = "<group>"; };
		6713CC562681CF9000C3DB03 /* JCWebViewMacros.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCWebViewMacros.h; sourceTree = "<group>"; };
		6713CC572681CF9000C3DB03 /* JCWebProgress.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCWebProgress.h; sourceTree = "<group>"; };
		6713CC582681CF9000C3DB03 /* JCADPopViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCADPopViewController.m; sourceTree = "<group>"; };
		6713CC592681CF9000C3DB03 /* JCWKWebView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCWKWebView.h; sourceTree = "<group>"; };
		6713CC5B2681CF9000C3DB03 /* AD关闭直线@2x.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "AD关闭直线@2x.png"; sourceTree = "<group>"; };
		6713CC5C2681CF9000C3DB03 /* AD关闭直线@3x.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "AD关闭直线@3x.png"; sourceTree = "<group>"; };
		6713CC5D2681CF9000C3DB03 /* 分享_钉钉@3x.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "分享_钉钉@3x.png"; sourceTree = "<group>"; };
		6713CC5E2681CF9000C3DB03 /* 分享********** */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "分享**********"; sourceTree = "<group>"; };
		6713CC5F2681CF9000C3DB03 /* 分享_朋友圈@2x.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "分享_朋友圈@2x.png"; sourceTree = "<group>"; };
		6713CC602681CF9000C3DB03 /* 分享_朋友圈@3x.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "分享_朋友圈@3x.png"; sourceTree = "<group>"; };
		6713CC612681CF9000C3DB03 /* 分享********** */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "分享**********"; sourceTree = "<group>"; };
		6713CC622681CF9000C3DB03 /* 分享_钉钉@2x.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "分享_钉钉@2x.png"; sourceTree = "<group>"; };
		6713CC632681CF9000C3DB03 /* 分享_微信@2x.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "分享_微信@2x.png"; sourceTree = "<group>"; };
		6713CC642681CF9000C3DB03 /* 分享_微信@3x.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "分享_微信@3x.png"; sourceTree = "<group>"; };
		6713CC652681CF9000C3DB03 /* AD关闭@2x.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "AD关闭@2x.png"; sourceTree = "<group>"; };
		6713CC662681CF9000C3DB03 /* AD关闭@3x.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "AD关闭@3x.png"; sourceTree = "<group>"; };
		6713CC672681CF9000C3DB03 /* AppDelegate+Share.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "AppDelegate+Share.m"; sourceTree = "<group>"; };
		6713CC682681CF9000C3DB03 /* JCWKWebViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCWKWebViewController.m; sourceTree = "<group>"; };
		6713CC692681CF9000C3DB03 /* JCWebProgress.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCWebProgress.m; sourceTree = "<group>"; };
		6713CC6A2681CF9000C3DB03 /* JCADPopViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCADPopViewController.h; sourceTree = "<group>"; };
		6713CC6B2681CF9000C3DB03 /* JCPayManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCPayManager.m; sourceTree = "<group>"; };
		6713CC6D2681CF9000C3DB03 /* JCShopMallsViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCShopMallsViewController.m; sourceTree = "<group>"; };
		6713CC6E2681CF9000C3DB03 /* JCShopBaseWebViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCShopBaseWebViewController.m; sourceTree = "<group>"; };
		6713CC6F2681CF9000C3DB03 /* JCShopAliPay.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCShopAliPay.m; sourceTree = "<group>"; };
		6713CC702681CF9000C3DB03 /* JCShopBaseWebViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCShopBaseWebViewController.h; sourceTree = "<group>"; };
		6713CC712681CF9000C3DB03 /* JCShopMallsViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCShopMallsViewController.h; sourceTree = "<group>"; };
		6713CC722681CF9000C3DB03 /* JCShopAliPay.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCShopAliPay.h; sourceTree = "<group>"; };
		6713CC742681CF9100C3DB03 /* JCFMDB.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JCFMDB.h; sourceTree = "<group>"; };
		6713CC752681CF9100C3DB03 /* JCFMDB.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JCFMDB.m; sourceTree = "<group>"; };
		6713CC902681DF3A00C3DB03 /* CoreGraphics.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreGraphics.framework; path = System/Library/Frameworks/CoreGraphics.framework; sourceTree = SDKROOT; };
		6713CC922681DF4100C3DB03 /* AVFoundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AVFoundation.framework; path = System/Library/Frameworks/AVFoundation.framework; sourceTree = SDKROOT; };
		6713CC942681DF5600C3DB03 /* libc++.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = "libc++.tbd"; path = "usr/lib/libc++.tbd"; sourceTree = SDKROOT; };
		6713CC962681DF5E00C3DB03 /* libiconv.2.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libiconv.2.tbd; path = usr/lib/libiconv.2.tbd; sourceTree = SDKROOT; };
		6713CC982681DF6800C3DB03 /* libiconv.2.4.0.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libiconv.2.4.0.tbd; path = usr/lib/libiconv.2.4.0.tbd; sourceTree = SDKROOT; };
		6713CC9A2681DF6E00C3DB03 /* libbz2.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libbz2.tbd; path = usr/lib/libbz2.tbd; sourceTree = SDKROOT; };
		6713CC9E2681E08000C3DB03 /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/Localizable.strings"; sourceTree = "<group>"; };
		6713CC9F2681E08000C3DB03 /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/Localizable.strings; sourceTree = "<group>"; };
		6713CCA12681E08000C3DB03 /* 8.caf */ = {isa = PBXFileReference; lastKnownFileType = file; path = 8.caf; sourceTree = "<group>"; };
		6713CCA22681E08000C3DB03 /* 9.caf */ = {isa = PBXFileReference; lastKnownFileType = file; path = 9.caf; sourceTree = "<group>"; };
		6713CCA32681E08000C3DB03 /* 1.caf */ = {isa = PBXFileReference; lastKnownFileType = file; path = 1.caf; sourceTree = "<group>"; };
		6713CCA42681E08000C3DB03 /* 0.caf */ = {isa = PBXFileReference; lastKnownFileType = file; path = 0.caf; sourceTree = "<group>"; };
		6713CCA52681E08000C3DB03 /* 2.caf */ = {isa = PBXFileReference; lastKnownFileType = file; path = 2.caf; sourceTree = "<group>"; };
		6713CCA62681E08000C3DB03 /* 3.caf */ = {isa = PBXFileReference; lastKnownFileType = file; path = 3.caf; sourceTree = "<group>"; };
		6713CCA72681E08000C3DB03 /* 7.caf */ = {isa = PBXFileReference; lastKnownFileType = file; path = 7.caf; sourceTree = "<group>"; };
		6713CCA82681E08000C3DB03 /* 6.caf */ = {isa = PBXFileReference; lastKnownFileType = file; path = 6.caf; sourceTree = "<group>"; };
		6713CCA92681E08000C3DB03 /* 4.caf */ = {isa = PBXFileReference; lastKnownFileType = file; path = 4.caf; sourceTree = "<group>"; };
		6713CCAA2681E08000C3DB03 /* 5.caf */ = {isa = PBXFileReference; lastKnownFileType = file; path = 5.caf; sourceTree = "<group>"; };
		6713CCAB2681E08000C3DB03 /* JCSDKFont.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = JCSDKFont.bundle; sourceTree = "<group>"; };
		6713CCAC2681E08000C3DB03 /* ZT001.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = ZT001.ttf; sourceTree = "<group>"; };
		6756B2D526846C5E00C08B2B /* PrintData.pbobjc.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PrintData.pbobjc.h; sourceTree = "<group>"; };
		6756B2D626846C5E00C08B2B /* PrintData.pbobjc.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PrintData.pbobjc.m; sourceTree = "<group>"; };
		6756B2D826846CAD00C08B2B /* PrintData.proto */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.protobuf; path = PrintData.proto; sourceTree = "<group>"; };
		6756B2DA26848F9600C08B2B /* ImageHelper.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ImageHelper.h; sourceTree = "<group>"; };
		6756B2DB26848F9600C08B2B /* ImageHelper.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ImageHelper.m; sourceTree = "<group>"; };
		76C66C3428CB11CB00384BCB /* libswiftCore.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libswiftCore.tbd; path = usr/lib/swift/libswiftCore.tbd; sourceTree = SDKROOT; };
		76C66C3628CB157A00384BCB /* Runner-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "Runner-Bridging-Header.h"; sourceTree = "<group>"; };
		76C66C3728CB157A00384BCB /* KeepSwift.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = KeepSwift.swift; sourceTree = "<group>"; };
		7AFA3C8E1D35360C0083082E /* Release.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; name = Release.xcconfig; path = Flutter/Release.xcconfig; sourceTree = "<group>"; };
		7AFFD8ED1D35381100E5BB4D /* AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AppDelegate.h; sourceTree = "<group>"; };
		7AFFD8EE1D35381100E5BB4D /* AppDelegate.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AppDelegate.m; sourceTree = "<group>"; };
		9740EEB21CF90195004384FC /* Debug.xcconfig */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xcconfig; name = Debug.xcconfig; path = Flutter/Debug.xcconfig; sourceTree = "<group>"; };
		9740EEB31CF90195004384FC /* Generated.xcconfig */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xcconfig; name = Generated.xcconfig; path = Flutter/Generated.xcconfig; sourceTree = "<group>"; };
		97C146EE1CF9000F007C117D /* Runner.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Runner.app; sourceTree = BUILT_PRODUCTS_DIR; };
		97C146F21CF9000F007C117D /* main.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		97C146FB1CF9000F007C117D /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		97C146FD1CF9000F007C117D /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		97C147001CF9000F007C117D /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		97C147021CF9000F007C117D /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		CD584239BC2F0032ADC293FB /* Pods-Runner.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.release.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.release.xcconfig"; sourceTree = "<group>"; };
		FB996FAB4E5C3D141937803C /* libPods-Runner.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-Runner.a"; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		97C146EB1CF9000F007C117D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				76C66C3528CB11CB00384BCB /* libswiftCore.tbd in Frameworks */,
				6713CC9B2681DF6E00C3DB03 /* libbz2.tbd in Frameworks */,
				6713CC992681DF6800C3DB03 /* libiconv.2.4.0.tbd in Frameworks */,
				6713CC972681DF5E00C3DB03 /* libiconv.2.tbd in Frameworks */,
				6713CC952681DF5600C3DB03 /* libc++.tbd in Frameworks */,
				6713CC932681DF4100C3DB03 /* AVFoundation.framework in Frameworks */,
				6713CC912681DF3A00C3DB03 /* CoreGraphics.framework in Frameworks */,
				D4F01CCE3B0E5B1B15EDB41C /* libPods-Runner.a in Frameworks */,
				6713C8BE2681C9DC00C3DB03 /* ANTILOST.a in Frameworks */,
				6713C8EA2681C9DC00C3DB03 /* libFeasyWifiSDK_V1.33.a in Frameworks */,
				6713CCBB2681E2B100C3DB03 /* CocoaAsyncSocket.framework in Frameworks */,
				6713C8EC2681C9DC00C3DB03 /* JCAPI.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		6713C84E2681C9DB00C3DB03 /* Utils(工具类) */ = {
			isa = PBXGroup;
			children = (
				6713C84F2681C9DB00C3DB03 /* Ant */,
				6713C8532681C9DC00C3DB03 /* Tools */,
				6713C86B2681C9DC00C3DB03 /* 扫二维码 */,
				6713C8762681C9DC00C3DB03 /* ProessHUD */,
				6713C87A2681C9DC00C3DB03 /* Timer */,
				6713C8812681C9DC00C3DB03 /* runtime */,
				6713C8842681C9DC00C3DB03 /* 埋点工具类 */,
				6713C8872681C9DC00C3DB03 /* CommonControl */,
				6713C88A2681C9DC00C3DB03 /* blueTooth */,
				6713C8A02681C9DC00C3DB03 /* Log */,
				6713C8A32681C9DC00C3DB03 /* Network(网络) */,
				6713C8AC2681C9DC00C3DB03 /* error */,
				6713C8AF2681C9DC00C3DB03 /* B3S */,
				6713C8B92681C9DC00C3DB03 /* Aleart */,
			);
			path = "Utils(工具类)";
			sourceTree = "<group>";
		};
		6713C84F2681C9DB00C3DB03 /* Ant */ = {
			isa = PBXGroup;
			children = (
				6713C8502681C9DB00C3DB03 /* ANTILOST.a */,
				6713C8512681C9DC00C3DB03 /* SDK程序电子文件及版本说明.xlsx */,
				6713C8522681C9DC00C3DB03 /* JCAPI_Antilost.h */,
			);
			path = Ant;
			sourceTree = "<group>";
		};
		6713C8532681C9DC00C3DB03 /* Tools */ = {
			isa = PBXGroup;
			children = (
				6713C8542681C9DC00C3DB03 /* DeviceUpdateUtil.h */,
				6713C8552681C9DC00C3DB03 /* JCPrinterSameTool.h */,
				6713C8562681C9DC00C3DB03 /* JCDevicesSeriesModel.h */,
				6713C8572681C9DC00C3DB03 /* JCRfidRuleModel.h */,
				6713C8582681C9DC00C3DB03 /* LocalNotificationUtil.m */,
				6713C8592681C9DC00C3DB03 /* JCVenderTool.m */,
				6713C85A2681C9DC00C3DB03 /* JCAntiLostUtil.h */,
				6713C85B2681C9DC00C3DB03 /* DeviceUpdateUtil4AntiLost.h */,
				6713C85C2681C9DC00C3DB03 /* JCRFIDModel.m */,
				6713C85D2681C9DC00C3DB03 /* JCKeychainTool.m */,
				6713C85E2681C9DC00C3DB03 /* JCApplicationManager.m */,
				6713C85F2681C9DC00C3DB03 /* NullSafe.m */,
				6713C8602681C9DC00C3DB03 /* JCDevicesSeriesModel.m */,
				6713C8612681C9DC00C3DB03 /* JCPrinterSameTool.m */,
				6713C8622681C9DC00C3DB03 /* DeviceUpdateUtil.m */,
				6713C8632681C9DC00C3DB03 /* JCAntiLostUtil.m */,
				6713C8642681C9DC00C3DB03 /* JCVenderTool.h */,
				6713C8652681C9DC00C3DB03 /* LocalNotificationUtil.h */,
				6713C8662681C9DC00C3DB03 /* JCRfidRuleModel.m */,
				6713C8672681C9DC00C3DB03 /* JCKeychainTool.h */,
				6713C8682681C9DC00C3DB03 /* JCRFIDModel.h */,
				6713C8692681C9DC00C3DB03 /* DeviceUpdateUtil4AntiLost.m */,
				6713C86A2681C9DC00C3DB03 /* JCApplicationManager.h */,
			);
			path = Tools;
			sourceTree = "<group>";
		};
		6713C86B2681C9DC00C3DB03 /* 扫二维码 */ = {
			isa = PBXGroup;
			children = (
				6713C86C2681C9DC00C3DB03 /* QQLBXScanViewController.h */,
				6713C86D2681C9DC00C3DB03 /* StyleDIY.m */,
				6713C86E2681C9DC00C3DB03 /* LBXScanPermissions.m */,
				6713C86F2681C9DC00C3DB03 /* Global.h */,
				6713C8702681C9DC00C3DB03 /* UIView+EasyExtend.h */,
				6713C8712681C9DC00C3DB03 /* QQLBXScanViewController.m */,
				6713C8722681C9DC00C3DB03 /* LBXScanPermissions.h */,
				6713C8732681C9DC00C3DB03 /* StyleDIY.h */,
				6713C8742681C9DC00C3DB03 /* Global.m */,
				6713C8752681C9DC00C3DB03 /* UIView+EasyExtend.m */,
			);
			path = "扫二维码";
			sourceTree = "<group>";
		};
		6713C8762681C9DC00C3DB03 /* ProessHUD */ = {
			isa = PBXGroup;
			children = (
				6713C8772681C9DC00C3DB03 /* DCHUDHelper.m */,
				6713C8782681C9DC00C3DB03 /* DCHUDHelper.h */,
				6713C8792681C9DC00C3DB03 /* MBProgressHUD.bundle */,
			);
			path = ProessHUD;
			sourceTree = "<group>";
		};
		6713C87A2681C9DC00C3DB03 /* Timer */ = {
			isa = PBXGroup;
			children = (
				6713C87B2681C9DC00C3DB03 /* DCWeakTimer.m */,
				6713C87C2681C9DC00C3DB03 /* DCGCDTimer.m */,
				6713C87D2681C9DC00C3DB03 /* DCProxy.h */,
				6713C87E2681C9DC00C3DB03 /* DCWeakTimer.h */,
				6713C87F2681C9DC00C3DB03 /* DCProxy.m */,
				6713C8802681C9DC00C3DB03 /* DCGCDTimer.h */,
			);
			path = Timer;
			sourceTree = "<group>";
		};
		6713C8812681C9DC00C3DB03 /* runtime */ = {
			isa = PBXGroup;
			children = (
				6713C8822681C9DC00C3DB03 /* SDAnimatedImageView+JCImageView.h */,
				6713C8832681C9DC00C3DB03 /* SDAnimatedImageView+JCImageView.m */,
			);
			path = runtime;
			sourceTree = "<group>";
		};
		6713C8842681C9DC00C3DB03 /* 埋点工具类 */ = {
			isa = PBXGroup;
			children = (
				6713C8852681C9DC00C3DB03 /* JCEventTrackingTool.m */,
				6713C8862681C9DC00C3DB03 /* JCEventTrackingTool.h */,
			);
			path = "埋点工具类";
			sourceTree = "<group>";
		};
		6713C8872681C9DC00C3DB03 /* CommonControl */ = {
			isa = PBXGroup;
			children = (
				6713C8882681C9DC00C3DB03 /* CommonCtrl.m */,
				6713C8892681C9DC00C3DB03 /* CommonCtrl.h */,
			);
			path = CommonControl;
			sourceTree = "<group>";
		};
		6713C88A2681C9DC00C3DB03 /* blueTooth */ = {
			isa = PBXGroup;
			children = (
				6713C88B2681C9DC00C3DB03 /* JCRFIDDeviceModel.m */,
				6713C88C2681C9DC00C3DB03 /* BabyBuletooth */,
				6713C89F2681C9DC00C3DB03 /* JCRFIDDeviceModel.h */,
			);
			path = blueTooth;
			sourceTree = "<group>";
		};
		6713C88C2681C9DC00C3DB03 /* BabyBuletooth */ = {
			isa = PBXGroup;
			children = (
				6713C88D2681C9DC00C3DB03 /* BabyRhythm.h */,
				6713C88E2681C9DC00C3DB03 /* BabySpeaker.h */,
				6713C88F2681C9DC00C3DB03 /* BabyToy.m */,
				6713C8902681C9DC00C3DB03 /* BabyCentralManager.h */,
				6713C8912681C9DC00C3DB03 /* BabyPeripheralManager.h */,
				6713C8922681C9DC00C3DB03 /* BabyDefine.h */,
				6713C8932681C9DC00C3DB03 /* BabyBluetooth.h */,
				6713C8942681C9DC00C3DB03 /* BabyOptions.m */,
				6713C8952681C9DC00C3DB03 /* BabyCallback.m */,
				6713C8962681C9DC00C3DB03 /* BabyPeripheralManager.m */,
				6713C8972681C9DC00C3DB03 /* BabyCentralManager.m */,
				6713C8982681C9DC00C3DB03 /* BabySpeaker.m */,
				6713C8992681C9DC00C3DB03 /* BabyToy.h */,
				6713C89A2681C9DC00C3DB03 /* BabyRhythm.m */,
				6713C89B2681C9DC00C3DB03 /* BabyOptions.h */,
				6713C89C2681C9DC00C3DB03 /* BabyBluetooth.m */,
				6713C89D2681C9DC00C3DB03 /* BabyDefine.m */,
				6713C89E2681C9DC00C3DB03 /* BabyCallback.h */,
			);
			path = BabyBuletooth;
			sourceTree = "<group>";
		};
		6713C8A02681C9DC00C3DB03 /* Log */ = {
			isa = PBXGroup;
			children = (
				6713C8A12681C9DC00C3DB03 /* JCLogUtil.h */,
				6713C8A22681C9DC00C3DB03 /* JCLogUtil.m */,
			);
			path = Log;
			sourceTree = "<group>";
		};
		6713C8A32681C9DC00C3DB03 /* Network(网络) */ = {
			isa = PBXGroup;
			children = (
				6713C8A42681C9DC00C3DB03 /* DCNormalHTTPRequst.m */,
				6713C8A52681C9DC00C3DB03 /* JCOSSRequest.h */,
				6713C8A62681C9DC00C3DB03 /* DCBaseRequst.h */,
				6713C8A72681C9DC00C3DB03 /* NSDictionary+DCJson.m */,
				6713C8A82681C9DC00C3DB03 /* DCBaseRequst.m */,
				6713C8A92681C9DC00C3DB03 /* JCOSSRequest.m */,
				6713C8AA2681C9DC00C3DB03 /* DCNormalHTTPRequst.h */,
				6713C8AB2681C9DC00C3DB03 /* NSDictionary+DCJson.h */,
			);
			path = "Network(网络)";
			sourceTree = "<group>";
		};
		6713C8AC2681C9DC00C3DB03 /* error */ = {
			isa = PBXGroup;
			children = (
				6713C8AD2681C9DC00C3DB03 /* JCErrorInfo.h */,
				6713C8AE2681C9DC00C3DB03 /* JCErrorInfo.m */,
			);
			path = error;
			sourceTree = "<group>";
		};
		6713C8AF2681C9DC00C3DB03 /* B3S */ = {
			isa = PBXGroup;
			children = (
				6713C8B02681C9DC00C3DB03 /* Reachability.m */,
				6713C8B12681C9DC00C3DB03 /* FWnetworkWIFI.h */,
				6713C8B22681C9DC00C3DB03 /* libFeasyWifiSDK_V1.33.a */,
				6713C8B32681C9DC00C3DB03 /* NSData+YDCRC.h */,
				6713C8B42681C9DC00C3DB03 /* CocoaAsyncSocket.framework */,
				6713C8B52681C9DC00C3DB03 /* JCAPI.a */,
				6713C8B62681C9DC00C3DB03 /* JCAPI.h */,
				6713C8B72681C9DC00C3DB03 /* Reachability.h */,
				6713C8B82681C9DC00C3DB03 /* NSData+YDCRC.m */,
			);
			path = B3S;
			sourceTree = "<group>";
		};
		6713C8B92681C9DC00C3DB03 /* Aleart */ = {
			isa = PBXGroup;
			children = (
				6713C8BA2681C9DC00C3DB03 /* JCDownLoadAleart.m */,
				6713C8BB2681C9DC00C3DB03 /* JCDeviceUpdateAleart.h */,
				6713C8BC2681C9DC00C3DB03 /* JCDeviceUpdateAleart.m */,
				6713C8BD2681C9DC00C3DB03 /* JCDownLoadAleart.h */,
			);
			path = Aleart;
			sourceTree = "<group>";
		};
		6713C8F02681C9F800C3DB03 /* Expand(扩展) */ = {
			isa = PBXGroup;
			children = (
				6713C8F12681C9F800C3DB03 /* Const(常量) */,
				6713C8F42681C9F800C3DB03 /* Marcors(宏) */,
			);
			path = "Expand(扩展)";
			sourceTree = "<group>";
		};
		6713C8F12681C9F800C3DB03 /* Const(常量) */ = {
			isa = PBXGroup;
			children = (
				6713C8F22681C9F800C3DB03 /* JCConst.m */,
				6713C8F32681C9F800C3DB03 /* JCConst.h */,
			);
			path = "Const(常量)";
			sourceTree = "<group>";
		};
		6713C8F42681C9F800C3DB03 /* Marcors(宏) */ = {
			isa = PBXGroup;
			children = (
				6713C8F52681C9F800C3DB03 /* UIMacro.h */,
				6713C8F62681C9F800C3DB03 /* DatabaseMacro.h */,
				6713C8F72681C9F800C3DB03 /* CustomFucMacro.h */,
				6713C8F82681C9F800C3DB03 /* DCRequestMacro.h */,
			);
			path = "Marcors(宏)";
			sourceTree = "<group>";
		};
		6713C9BD2681CDFD00C3DB03 /* Category */ = {
			isa = PBXGroup;
			children = (
				6713C9BE2681CDFD00C3DB03 /* NSString+JC_Device.h */,
				6713C9BF2681CDFD00C3DB03 /* UIColor+Extension.h */,
				6713C9C02681CDFD00C3DB03 /* UIView+JCCategory.h */,
				6713C9C12681CDFD00C3DB03 /* NSTimer+addition.h */,
				6713C9C22681CDFD00C3DB03 /* NSTimer+addition.m */,
				6713C9C32681CDFD00C3DB03 /* UIView+JCCategory.m */,
				6713C9C42681CDFD00C3DB03 /* UIColor+Extension.m */,
				6713C9C52681CDFD00C3DB03 /* NSString+JC_Device.m */,
			);
			path = Category;
			sourceTree = "<group>";
		};
		6713C9CA2681CEAC00C3DB03 /* Base */ = {
			isa = PBXGroup;
			children = (
				6713C9CB2681CEAC00C3DB03 /* DCBaseModel.m */,
				6713C9CC2681CEAC00C3DB03 /* JCUserModel.h */,
				6713C9CD2681CEAC00C3DB03 /* DCNavigationViewController.h */,
				6713C9CE2681CEAC00C3DB03 /* DCBaseModel.h */,
				6713C9CF2681CEAC00C3DB03 /* JCUserModel.m */,
				6713C9D02681CEAC00C3DB03 /* DCNavigationViewController.m */,
			);
			path = Base;
			sourceTree = "<group>";
		};
		6713C9D42681CED000C3DB03 /* DrawBoard */ = {
			isa = PBXGroup;
			children = (
				6713C9D52681CED000C3DB03 /* Util */,
				6713C9DE2681CED000C3DB03 /* Dependency */,
				6713C9F92681CED000C3DB03 /* Print */,
				6713CA682681CED100C3DB03 /* Controller */,
				6713CA6B2681CED100C3DB03 /* Manager */,
				6713CA8E2681CED100C3DB03 /* Model */,
				6713CAAB2681CED100C3DB03 /* View */,
			);
			path = DrawBoard;
			sourceTree = "<group>";
		};
		6713C9D52681CED000C3DB03 /* Util */ = {
			isa = PBXGroup;
			children = (
				6713C9D62681CED000C3DB03 /* XYTool.h */,
				6713C9D72681CED000C3DB03 /* NSString+XYCategory.m */,
				6713C9D82681CED000C3DB03 /* VenderMaCro.h */,
				6713C9D92681CED000C3DB03 /* JCDrawBoardHeader.h */,
				6713C9DA2681CED000C3DB03 /* XYTool.m */,
				6713C9DB2681CED000C3DB03 /* DeviceMacro.h */,
				6713C9DC2681CED000C3DB03 /* NotificationMacro.h */,
				6713C9DD2681CED000C3DB03 /* NSString+XYCategory.h */,
			);
			path = Util;
			sourceTree = "<group>";
		};
		6713C9DE2681CED000C3DB03 /* Dependency */ = {
			isa = PBXGroup;
			children = (
				6713C9DF2681CED000C3DB03 /* JCBubbleTableViewCell.xib */,
				6713C9E02681CED000C3DB03 /* UITextView+ZWPlaceHolder.m */,
				6713C9E12681CED000C3DB03 /* UIFont+JCCustomFont.m */,
				6713C9E22681CED000C3DB03 /* CDD */,
				6713C9E72681CED000C3DB03 /* UIImage+EasyExtend.m */,
				6713C9E82681CED000C3DB03 /* JCBaseScrollView+Touch.h */,
				6713C9E92681CED000C3DB03 /* UIImage+Tool.h */,
				6713C9EA2681CED000C3DB03 /* JCExcelForElement.h */,
				6713C9EB2681CED000C3DB03 /* JCBaseScrollView.h */,
				6713C9EC2681CED000C3DB03 /* pinyin.h */,
				6713C9ED2681CED000C3DB03 /* NSArray+EasyExtend.mm */,
				6713C9EE2681CED000C3DB03 /* JCBubbleTableViewCell.m */,
				6713C9EF2681CED000C3DB03 /* UIFont+JCCustomFont.h */,
				6713C9F02681CED000C3DB03 /* UITextView+ZWPlaceHolder.h */,
				6713C9F12681CED000C3DB03 /* JCExcelForElement.m */,
				6713C9F22681CED000C3DB03 /* UIImage+Tool.m */,
				6713C9F32681CED000C3DB03 /* JCBaseScrollView+Touch.m */,
				6713C9F42681CED000C3DB03 /* UIImage+EasyExtend.h */,
				6713C9F52681CED000C3DB03 /* NSArray+EasyExtend.h */,
				6713C9F62681CED000C3DB03 /* pinyin.m */,
				6713C9F72681CED000C3DB03 /* JCBaseScrollView.m */,
				6713C9F82681CED000C3DB03 /* JCBubbleTableViewCell.h */,
			);
			path = Dependency;
			sourceTree = "<group>";
		};
		6713C9E22681CED000C3DB03 /* CDD */ = {
			isa = PBXGroup;
			children = (
				6713C9E32681CED000C3DB03 /* NSObject+JCContext.h */,
				6713C9E42681CED000C3DB03 /* JCContext.m */,
				6713C9E52681CED000C3DB03 /* NSObject+JCContext.m */,
				6713C9E62681CED000C3DB03 /* JCContext.h */,
			);
			path = CDD;
			sourceTree = "<group>";
		};
		6713C9F92681CED000C3DB03 /* Print */ = {
			isa = PBXGroup;
			children = (
				6713C9FA2681CED000C3DB03 /* JCAlert.m */,
				6713C9FB2681CED000C3DB03 /* JCPaperErrorView.h */,
				6713C9FC2681CED000C3DB03 /* JCPrintCenter.m */,
				6713C9FD2681CED000C3DB03 /* JCRfidError.xib */,
				6713C9FE2681CED000C3DB03 /* JCPrintCessView.m */,
				6713C9FF2681CED000C3DB03 /* JCMessageView.m */,
				6713CA002681CED000C3DB03 /* JCPrintAntiLostCessView.m */,
				6713CA012681CED000C3DB03 /* JCUniqueModel.m */,
				6713CA022681CED000C3DB03 /* JCDeviceFirmwareRemoteModel.m */,
				6713CA032681CED000C3DB03 /* JCBoardGuideView.m */,
				6713CA042681CED000C3DB03 /* JCPrint */,
				6713CA252681CED100C3DB03 /* JCRfidError.h */,
				6713CA262681CED100C3DB03 /* JCMessageView.xib */,
				6713CA272681CED100C3DB03 /* JCAlert.h */,
				6713CA282681CED100C3DB03 /* JCMessageView.h */,
				6713CA292681CED100C3DB03 /* JCPrintCessView.h */,
				6713CA2A2681CED100C3DB03 /* JCPrintCenter.h */,
				6713CA2B2681CED100C3DB03 /* JCPaperErrorView.m */,
				6713CA2C2681CED100C3DB03 /* JCPrintAntiLostCessView.h */,
				6713CA2D2681CED100C3DB03 /* JCRfidError.m */,
				6713CA2E2681CED100C3DB03 /* JCBoardGuideView.h */,
				6713CA2F2681CED100C3DB03 /* JCDeviceFirmwareRemoteModel.h */,
				6713CA302681CED100C3DB03 /* Tool */,
				6713CA672681CED100C3DB03 /* JCUniqueModel.h */,
			);
			path = Print;
			sourceTree = "<group>";
		};
		6713CA042681CED000C3DB03 /* JCPrint */ = {
			isa = PBXGroup;
			children = (
				6713CA052681CED000C3DB03 /* JCPaper */,
				6713CA0A2681CED000C3DB03 /* JCRfidCheckManager.h */,
				6713CA0B2681CED000C3DB03 /* PrintEnd */,
				6713CA102681CED000C3DB03 /* JCDB */,
				6713CA142681CED000C3DB03 /* JCPrintExecuteManager.m */,
				6713CA152681CED000C3DB03 /* JCPolicy */,
				6713CA1D2681CED000C3DB03 /* JCDevice */,
				6713CA222681CED100C3DB03 /* JCPrintConfigure.h */,
				6713CA232681CED100C3DB03 /* JCRfidCheckManager.m */,
				6713CA242681CED100C3DB03 /* JCPrintExecuteManager.h */,
			);
			path = JCPrint;
			sourceTree = "<group>";
		};
		6713CA052681CED000C3DB03 /* JCPaper */ = {
			isa = PBXGroup;
			children = (
				6713CA062681CED000C3DB03 /* JCPaper.m */,
				6713CA072681CED000C3DB03 /* JCPaperManager.h */,
				6713CA082681CED000C3DB03 /* JCPaper.h */,
				6713CA092681CED000C3DB03 /* JCPaperManager.m */,
			);
			path = JCPaper;
			sourceTree = "<group>";
		};
		6713CA0B2681CED000C3DB03 /* PrintEnd */ = {
			isa = PBXGroup;
			children = (
				6713CA0C2681CED000C3DB03 /* JCImprintManager.h */,
				6713CA0D2681CED000C3DB03 /* JCPrintRecordManager.h */,
				6713CA0E2681CED000C3DB03 /* JCPrintRecordManager.m */,
				6713CA0F2681CED000C3DB03 /* JCImprintManager.m */,
			);
			path = PrintEnd;
			sourceTree = "<group>";
		};
		6713CA102681CED000C3DB03 /* JCDB */ = {
			isa = PBXGroup;
			children = (
				6713CA112681CED000C3DB03 /* JCDBManager.m */,
				6713CA122681CED000C3DB03 /* JCDBConfigure.h */,
				6713CA132681CED000C3DB03 /* JCDBManager.h */,
			);
			path = JCDB;
			sourceTree = "<group>";
		};
		6713CA152681CED000C3DB03 /* JCPolicy */ = {
			isa = PBXGroup;
			children = (
				6713CA162681CED000C3DB03 /* JCPolicy4DB.m */,
				6713CA172681CED000C3DB03 /* JCPolicy.h */,
				6713CA182681CED000C3DB03 /* JCPolicyManager.m */,
				6713CA192681CED000C3DB03 /* JCPolicy4DB.h */,
				6713CA1A2681CED000C3DB03 /* JCPolicy.m */,
				6713CA1B2681CED000C3DB03 /* JCPolicyConfigure.h */,
				6713CA1C2681CED000C3DB03 /* JCPolicyManager.h */,
			);
			path = JCPolicy;
			sourceTree = "<group>";
		};
		6713CA1D2681CED000C3DB03 /* JCDevice */ = {
			isa = PBXGroup;
			children = (
				6713CA1E2681CED000C3DB03 /* JCDevice.h */,
				6713CA1F2681CED000C3DB03 /* JCDeviceManager.m */,
				6713CA202681CED000C3DB03 /* JCDevice.m */,
				6713CA212681CED100C3DB03 /* JCDeviceManager.h */,
			);
			path = JCDevice;
			sourceTree = "<group>";
		};
		6713CA302681CED100C3DB03 /* Tool */ = {
			isa = PBXGroup;
			children = (
				6713CA312681CED100C3DB03 /* JCSelectDeviceModel.h */,
				6713CA322681CED100C3DB03 /* UIView+XYCategory.h */,
				6713CA332681CED100C3DB03 /* JCDeviceOffSetInfoModel.m */,
				6713CA342681CED100C3DB03 /* JCPrinterModel.m */,
				6713CA352681CED100C3DB03 /* JCDeviceUpdateTool.m */,
				6713CA362681CED100C3DB03 /* JCPrintDevice.m */,
				6713CA372681CED100C3DB03 /* MMPopupView */,
				6713CA452681CED100C3DB03 /* Singleton.h */,
				6713CA462681CED100C3DB03 /* XYView.m */,
				6713CA472681CED100C3DB03 /* JCMessageImageView.m */,
				6713CA482681CED100C3DB03 /* UIView+XYCategory.m */,
				6713CA492681CED100C3DB03 /* JCDeviceOffSetInfoModel.h */,
				6713CA4A2681CED100C3DB03 /* JCSelectDeviceModel.m */,
				6713CA4B2681CED100C3DB03 /* 埋点工具类 */,
				6713CA532681CED100C3DB03 /* HUD_extension */,
				6713CA562681CED100C3DB03 /* JCPrintDevice.h */,
				6713CA572681CED100C3DB03 /* JCDeviceUpdateTool.h */,
				6713CA582681CED100C3DB03 /* JCPrinterModel.h */,
				6713CA592681CED100C3DB03 /* JCMessageImageView.xib */,
				6713CA5A2681CED100C3DB03 /* XYView.h */,
				6713CA5B2681CED100C3DB03 /* JCMessageImageView.h */,
				6713CA5C2681CED100C3DB03 /* BlueTool */,
			);
			path = Tool;
			sourceTree = "<group>";
		};
		6713CA372681CED100C3DB03 /* MMPopupView */ = {
			isa = PBXGroup;
			children = (
				6713CA382681CED100C3DB03 /* MMAlertView.m */,
				6713CA392681CED100C3DB03 /* MMSheetView.m */,
				6713CA3A2681CED100C3DB03 /* MMPopupView.h */,
				6713CA3B2681CED100C3DB03 /* MMPopupCategory.m */,
				6713CA3C2681CED100C3DB03 /* MMPopupItem.h */,
				6713CA3D2681CED100C3DB03 /* MMPopupWindow.m */,
				6713CA3E2681CED100C3DB03 /* MMSheetView.h */,
				6713CA3F2681CED100C3DB03 /* MMPopupDefine.h */,
				6713CA402681CED100C3DB03 /* MMAlertView.h */,
				6713CA412681CED100C3DB03 /* MMPopupView.m */,
				6713CA422681CED100C3DB03 /* MMPopupWindow.h */,
				6713CA432681CED100C3DB03 /* MMPopupCategory.h */,
				6713CA442681CED100C3DB03 /* MMPopupItem.m */,
			);
			path = MMPopupView;
			sourceTree = "<group>";
		};
		6713CA4B2681CED100C3DB03 /* 埋点工具类 */ = {
			isa = PBXGroup;
			children = (
				6713CA4C2681CED100C3DB03 /* JCRecordTool.h */,
				6713CA4D2681CED100C3DB03 /* JCRecordTool.m */,
				6713CA4E2681CED100C3DB03 /* model */,
			);
			path = "埋点工具类";
			sourceTree = "<group>";
		};
		6713CA4E2681CED100C3DB03 /* model */ = {
			isa = PBXGroup;
			children = (
				6713CA4F2681CED100C3DB03 /* JCRecordModel.m */,
				6713CA502681CED100C3DB03 /* JCStatisticsModel.h */,
				6713CA512681CED100C3DB03 /* JCRecordModel.h */,
				6713CA522681CED100C3DB03 /* JCStatisticsModel.m */,
			);
			path = model;
			sourceTree = "<group>";
		};
		6713CA532681CED100C3DB03 /* HUD_extension */ = {
			isa = PBXGroup;
			children = (
				6713CA542681CED100C3DB03 /* MBProgressHUD+Extension.h */,
				6713CA552681CED100C3DB03 /* MBProgressHUD+Extension.m */,
			);
			path = HUD_extension;
			sourceTree = "<group>";
		};
		6713CA5C2681CED100C3DB03 /* BlueTool */ = {
			isa = PBXGroup;
			children = (
				6713CA5D2681CED100C3DB03 /* JCWifiManager.h */,
				6713CA5E2681CED100C3DB03 /* JCBluetoothModel.m */,
				6713CA5F2681CED100C3DB03 /* JCDeviceSeriesHelp.h */,
				6713CA602681CED100C3DB03 /* JCBluetoothManager.m */,
				6713CA612681CED100C3DB03 /* JCPrinterTypeHelp.m */,
				6713CA622681CED100C3DB03 /* JCWifiManager.m */,
				6713CA632681CED100C3DB03 /* JCDeviceSeriesHelp.m */,
				6713CA642681CED100C3DB03 /* JCBluetoothModel.h */,
				6713CA652681CED100C3DB03 /* JCBluetoothManager.h */,
				6713CA662681CED100C3DB03 /* JCPrinterTypeHelp.h */,
			);
			path = BlueTool;
			sourceTree = "<group>";
		};
		6713CA682681CED100C3DB03 /* Controller */ = {
			isa = PBXGroup;
			children = (
				6713CA692681CED100C3DB03 /* JCTemplateEditController.m */,
				6713CA6A2681CED100C3DB03 /* JCTemplateEditController.h */,
			);
			path = Controller;
			sourceTree = "<group>";
		};
		6713CA6B2681CED100C3DB03 /* Manager */ = {
			isa = PBXGroup;
			children = (
				6713CA6C2681CED100C3DB03 /* JCTemplateData4DBManager.m */,
				6713CA6D2681CED100C3DB03 /* JCElementOperateManager.h */,
				6713CA6E2681CED100C3DB03 /* UIImageView+DownLoad.m */,
				6713CA6F2681CED100C3DB03 /* JCFontManager.h */,
				6713CA702681CED100C3DB03 /* JCElementToItem.m */,
				6713CA712681CED100C3DB03 /* JCOssApi.m */,
				6713CA722681CED100C3DB03 /* JCFontDownLoadManager.m */,
				6713CA732681CED100C3DB03 /* JCBoardTemplateManager.h */,
				6713CA742681CED100C3DB03 /* UIImage+uploadRequest.h */,
				6713CA752681CED100C3DB03 /* JCScanCodeManager.h */,
				6713CA762681CED100C3DB03 /* JCStack.m */,
				6713CA772681CED100C3DB03 /* JCDrawInfoManager.h */,
				6713CA782681CED100C3DB03 /* JCItemToElement.m */,
				6713CA792681CED100C3DB03 /* JCTMDataBindGoodsInfoManager.m */,
				6713CA7A2681CED100C3DB03 /* JCTemplateImageManager.h */,
				6713CA7B2681CED100C3DB03 /* JCDefaultElementManager.h */,
				6713CA7C2681CED100C3DB03 /* JCElementEditManager.h */,
				6713CA7D2681CED100C3DB03 /* JCElementOperateManager.m */,
				6713CA7E2681CED100C3DB03 /* JCTemplateData4DBManager.h */,
				6713CA7F2681CED100C3DB03 /* UIImageView+DownLoad.h */,
				6713CA802681CED100C3DB03 /* JCTemplateImageManager.mm */,
				6713CA812681CED100C3DB03 /* JCStack.h */,
				6713CA822681CED100C3DB03 /* JCScanCodeManager.m */,
				6713CA832681CED100C3DB03 /* UIImage+uploadRequest.m */,
				6713CA842681CED100C3DB03 /* JCBoardTemplateManager.m */,
				6713CA852681CED100C3DB03 /* JCFontDownLoadManager.h */,
				6713CA862681CED100C3DB03 /* JCOssApi.h */,
				6713CA872681CED100C3DB03 /* JCElementToItem.h */,
				6713CA882681CED100C3DB03 /* JCFontManager.m */,
				6713CA892681CED100C3DB03 /* JCElementEditManager.m */,
				6713CA8A2681CED100C3DB03 /* JCDefaultElementManager.m */,
				6713CA8B2681CED100C3DB03 /* JCTMDataBindGoodsInfoManager.h */,
				6713CA8C2681CED100C3DB03 /* JCItemToElement.h */,
				6713CA8D2681CED100C3DB03 /* JCDrawInfoManager.m */,
			);
			path = Manager;
			sourceTree = "<group>";
		};
		6713CA8E2681CED100C3DB03 /* Model */ = {
			isa = PBXGroup;
			children = (
				6713CA8F2681CED100C3DB03 /* JCTemplateData+External.h */,
				6713CA902681CED100C3DB03 /* JCTemplateList.h */,
				6713CA912681CED100C3DB03 /* JCElementModel+Transfer.m */,
				6713CA922681CED100C3DB03 /* JCGoodDetailInfo.h */,
				6713CA932681CED100C3DB03 /* JCTemplateData+SDK.h */,
				6713CA942681CED100C3DB03 /* JCFontModel.m */,
				6713CA952681CED100C3DB03 /* JCTemplateData.m */,
				6713CA962681CED100C3DB03 /* JCElementModel.m */,
				6713CA972681CED100C3DB03 /* JCTemplateData+PrintConfigure.m */,
				6713CA982681CED100C3DB03 /* JCTemplateData+Transfer.m */,
				6713CA992681CED100C3DB03 /* JCFontSize.h */,
				6713CA9A2681CED100C3DB03 /* XYBaseModel.h */,
				6713CA9B2681CED100C3DB03 /* JCTemplateData4DB.h */,
				6713CA9C2681CED100C3DB03 /* JCPrintSamePreviewModel.h */,
				6713CA9D2681CED100C3DB03 /* JCElementModel+Transfer.h */,
				6713CA9E2681CED100C3DB03 /* JCTemplateList.m */,
				6713CA9F2681CED100C3DB03 /* JCTemplateData+External.m */,
				6713CAA02681CED100C3DB03 /* JCGoodDetailInfo.m */,
				6713CAA12681CED100C3DB03 /* JCTemplateData+PrintConfigure.h */,
				6713CAA22681CED100C3DB03 /* JCElementModel.h */,
				6713CAA32681CED100C3DB03 /* JCTemplateData.h */,
				6713CAA42681CED100C3DB03 /* JCFontModel.h */,
				6713CAA52681CED100C3DB03 /* JCTemplateData+SDK.mm */,
				6713CAA62681CED100C3DB03 /* JCPrintSamePreviewModel.m */,
				6713CAA72681CED100C3DB03 /* JCTemplateData4DB.m */,
				6713CAA82681CED100C3DB03 /* XYBaseModel.m */,
				6713CAA92681CED100C3DB03 /* JCFontSize.m */,
				6713CAAA2681CED100C3DB03 /* JCTemplateData+Transfer.h */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		6713CAAB2681CED100C3DB03 /* View */ = {
			isa = PBXGroup;
			children = (
				6713CAAC2681CED100C3DB03 /* 元素属性 */,
				6713CAB52681CED100C3DB03 /* 防丢器模板选择 */,
				6713CABC2681CED100C3DB03 /* 添加新元素 */,
				6713CAC52681CED100C3DB03 /* property */,
				6713CB102681CED200C3DB03 /* 元素 */,
				6713CB3F2681CED200C3DB03 /* 模板预览 */,
				6713CB422681CED200C3DB03 /* 元素依赖 */,
				6713CB5D2681CED200C3DB03 /* 模板选择 */,
				6713CB6C2681CED200C3DB03 /* 画板 */,
			);
			path = View;
			sourceTree = "<group>";
		};
		6713CAAC2681CED100C3DB03 /* 元素属性 */ = {
			isa = PBXGroup;
			children = (
				6713CAAD2681CED100C3DB03 /* JCMutablePropertyView.h */,
				6713CAAE2681CED100C3DB03 /* JCDisplacementView.m */,
				6713CAAF2681CED100C3DB03 /* JCStyleContentView.m */,
				6713CAB02681CED100C3DB03 /* JCStyleModel.m */,
				6713CAB12681CED100C3DB03 /* JCDisplacementView.h */,
				6713CAB22681CED100C3DB03 /* JCMutablePropertyView.m */,
				6713CAB32681CED100C3DB03 /* JCStyleContentView.h */,
				6713CAB42681CED100C3DB03 /* JCStyleModel.h */,
			);
			path = "元素属性";
			sourceTree = "<group>";
		};
		6713CAB52681CED100C3DB03 /* 防丢器模板选择 */ = {
			isa = PBXGroup;
			children = (
				6713CAB62681CED100C3DB03 /* JCTemplateAntiLostCollectionView.m */,
				6713CAB72681CED100C3DB03 /* JCTemplateAntiLostHorizontalCell.m */,
				6713CAB82681CED100C3DB03 /* JCTemplateAntiLostChooseView.m */,
				6713CAB92681CED100C3DB03 /* JCTemplateAntiLostCollectionView.h */,
				6713CABA2681CED100C3DB03 /* JCTemplateAntiLostChooseView.h */,
				6713CABB2681CED100C3DB03 /* JCTemplateAntiLostHorizontalCell.h */,
			);
			path = "防丢器模板选择";
			sourceTree = "<group>";
		};
		6713CABC2681CED100C3DB03 /* 添加新元素 */ = {
			isa = PBXGroup;
			children = (
				6713CABD2681CED100C3DB03 /* JCAddElementBar.m */,
				6713CABE2681CED100C3DB03 /* JCElementEditBar.m */,
				6713CABF2681CED100C3DB03 /* JCActionButton.m */,
				6713CAC02681CED100C3DB03 /* JCAddElementView.m */,
				6713CAC12681CED100C3DB03 /* JCElementEditBar.h */,
				6713CAC22681CED100C3DB03 /* JCAddElementBar.h */,
				6713CAC32681CED100C3DB03 /* JCAddElementView.h */,
				6713CAC42681CED100C3DB03 /* JCActionButton.h */,
			);
			path = "添加新元素";
			sourceTree = "<group>";
		};
		6713CAC52681CED100C3DB03 /* property */ = {
			isa = PBXGroup;
			children = (
				6713CAC62681CED100C3DB03 /* JCPrintSettingView.h */,
				6713CAC72681CED100C3DB03 /* JCTagListManager.m */,
				6713CAC82681CED100C3DB03 /* JCTagModel.m */,
				6713CAC92681CED100C3DB03 /* JCTagListLeftCell.h */,
				6713CACA2681CED100C3DB03 /* UIView+PlaceHolder.m */,
				6713CACB2681CED100C3DB03 /* JCTagImageTableViewCell.m */,
				6713CACC2681CED100C3DB03 /* JCPropertyMainView.m */,
				6713CACD2681CED100C3DB03 /* JCLogoCategoryModel.m */,
				6713CACE2681CED100C3DB03 /* JCProgressView.m */,
				6713CACF2681CED100C3DB03 /* JCBoardRightItemView.m */,
				6713CAD02681CED100C3DB03 /* JCPrintAlertView.m */,
				6713CAD12681CED100C3DB03 /* JCDrawNavigationBar.m */,
				6713CAD22681CED100C3DB03 /* JCTagChooseListView.h */,
				6713CAD32681CED100C3DB03 /* JCSearchTagTextField.h */,
				6713CAD42681CED100C3DB03 /* JCDrawNavigationBar4AntiLost.h */,
				6713CAD52681CED100C3DB03 /* JCTagListManager.h */,
				6713CAD62681CED100C3DB03 /* BoardOperationBar */,
				6713CAD92681CED100C3DB03 /* PropertyElements */,
				6713CAFB2681CED100C3DB03 /* JCPrintSettingView.m */,
				6713CAFC2681CED200C3DB03 /* JCTagListLeftCell.m */,
				6713CAFD2681CED200C3DB03 /* JCTagModel.h */,
				6713CAFE2681CED200C3DB03 /* ElementBar */,
				6713CB052681CED200C3DB03 /* JCLogoCategoryModel.h */,
				6713CB062681CED200C3DB03 /* JCProgressView.h */,
				6713CB072681CED200C3DB03 /* JCTagImageTableViewCell.h */,
				6713CB082681CED200C3DB03 /* JCPropertyMainView.h */,
				6713CB092681CED200C3DB03 /* UIView+PlaceHolder.h */,
				6713CB0A2681CED200C3DB03 /* JCDrawNavigationBar4AntiLost.m */,
				6713CB0B2681CED200C3DB03 /* JCSearchTagTextField.m */,
				6713CB0C2681CED200C3DB03 /* JCTagChooseListView.m */,
				6713CB0D2681CED200C3DB03 /* JCDrawNavigationBar.h */,
				6713CB0E2681CED200C3DB03 /* JCPrintAlertView.h */,
				6713CB0F2681CED200C3DB03 /* JCBoardRightItemView.h */,
			);
			path = property;
			sourceTree = "<group>";
		};
		6713CAD62681CED100C3DB03 /* BoardOperationBar */ = {
			isa = PBXGroup;
			children = (
				6713CAD72681CED100C3DB03 /* JCElementActionBar.h */,
				6713CAD82681CED100C3DB03 /* JCElementActionBar.m */,
			);
			path = BoardOperationBar;
			sourceTree = "<group>";
		};
		6713CAD92681CED100C3DB03 /* PropertyElements */ = {
			isa = PBXGroup;
			children = (
				6713CADA2681CED100C3DB03 /* JCLineStyleView.m */,
				6713CADB2681CED100C3DB03 /* JCAlignView.m */,
				6713CADC2681CED100C3DB03 /* JCInputTextView.m */,
				6713CADD2681CED100C3DB03 /* JCFontSelectView.m */,
				6713CADE2681CED100C3DB03 /* JCBorderCollectionCell.m */,
				6713CADF2681CED100C3DB03 /* JCDateFormatView.h */,
				6713CAE02681CED100C3DB03 /* JCBorderView.m */,
				6713CAE12681CED100C3DB03 /* JCIconBorderManager.m */,
				6713CAE22681CED100C3DB03 /* JCFontTableViewCell.h */,
				6713CAE32681CED100C3DB03 /* Image */,
				6713CAE62681CED100C3DB03 /* JCInputTextView.h */,
				6713CAE72681CED100C3DB03 /* JCAlignView.h */,
				6713CAE82681CED100C3DB03 /* JCLineStyleView.h */,
				6713CAE92681CED100C3DB03 /* JCDateFormatView.m */,
				6713CAEA2681CED100C3DB03 /* Text */,
				6713CAED2681CED100C3DB03 /* JCBorderCollectionCell.h */,
				6713CAEE2681CED100C3DB03 /* Icon */,
				6713CAF72681CED100C3DB03 /* JCFontSelectView.h */,
				6713CAF82681CED100C3DB03 /* JCFontTableViewCell.m */,
				6713CAF92681CED100C3DB03 /* JCIconBorderManager.h */,
				6713CAFA2681CED100C3DB03 /* JCBorderView.h */,
			);
			path = PropertyElements;
			sourceTree = "<group>";
		};
		6713CAE32681CED100C3DB03 /* Image */ = {
			isa = PBXGroup;
			children = (
				6713CAE42681CED100C3DB03 /* JCImagePropertyView.h */,
				6713CAE52681CED100C3DB03 /* JCImagePropertyView.m */,
			);
			path = Image;
			sourceTree = "<group>";
		};
		6713CAEA2681CED100C3DB03 /* Text */ = {
			isa = PBXGroup;
			children = (
				6713CAEB2681CED100C3DB03 /* JCTextStyleView.m */,
				6713CAEC2681CED100C3DB03 /* JCTextStyleView.h */,
			);
			path = Text;
			sourceTree = "<group>";
		};
		6713CAEE2681CED100C3DB03 /* Icon */ = {
			isa = PBXGroup;
			children = (
				6713CAEF2681CED100C3DB03 /* JCIconDataManager.h */,
				6713CAF02681CED100C3DB03 /* JCIConObject.m */,
				6713CAF12681CED100C3DB03 /* JCIconCollectionViewCell.h */,
				6713CAF22681CED100C3DB03 /* JCIconView.m */,
				6713CAF32681CED100C3DB03 /* JCIconDataManager.m */,
				6713CAF42681CED100C3DB03 /* JCIconCollectionViewCell.m */,
				6713CAF52681CED100C3DB03 /* JCIConObject.h */,
				6713CAF62681CED100C3DB03 /* JCIconView.h */,
			);
			path = Icon;
			sourceTree = "<group>";
		};
		6713CAFE2681CED200C3DB03 /* ElementBar */ = {
			isa = PBXGroup;
			children = (
				6713CAFF2681CED200C3DB03 /* JCBarData.m */,
				6713CB002681CED200C3DB03 /* JCElementPropertyBar.h */,
				6713CB012681CED200C3DB03 /* JCBarCollectionCell.h */,
				6713CB022681CED200C3DB03 /* JCBarData.h */,
				6713CB032681CED200C3DB03 /* JCBarCollectionCell.m */,
				6713CB042681CED200C3DB03 /* JCElementPropertyBar.m */,
			);
			path = ElementBar;
			sourceTree = "<group>";
		};
		6713CB102681CED200C3DB03 /* 元素 */ = {
			isa = PBXGroup;
			children = (
				6713CB112681CED200C3DB03 /* JCElementBaseView.m */,
				6713CB122681CED200C3DB03 /* 边框 */,
				6713CB192681CED200C3DB03 /* 文本 */,
				6713CB202681CED200C3DB03 /* 一维码 */,
				6713CB272681CED200C3DB03 /* 图片Logo */,
				6713CB2A2681CED200C3DB03 /* JCElementBaseView.h */,
				6713CB2B2681CED200C3DB03 /* 线条 */,
				6713CB302681CED200C3DB03 /* 流水号 */,
				6713CB352681CED200C3DB03 /* 时间 */,
				6713CB3A2681CED200C3DB03 /* 二维码 */,
				6713CB3D2681CED200C3DB03 /* JCElementConfigure.h */,
				6713CB3E2681CED200C3DB03 /* JCElements.h */,
			);
			path = "元素";
			sourceTree = "<group>";
		};
		6713CB122681CED200C3DB03 /* 边框 */ = {
			isa = PBXGroup;
			children = (
				6713CB132681CED200C3DB03 /* JCElementGraph.h */,
				6713CB142681CED200C3DB03 /* JCElementBox.h */,
				6713CB152681CED200C3DB03 /* JCElementGraph.mm */,
				6713CB162681CED200C3DB03 /* JCEleBoxConfigure.h */,
				6713CB172681CED200C3DB03 /* JCElementBox.m */,
				6713CB182681CED200C3DB03 /* JCEleBoxConfigure.m */,
			);
			path = "边框";
			sourceTree = "<group>";
		};
		6713CB192681CED200C3DB03 /* 文本 */ = {
			isa = PBXGroup;
			children = (
				6713CB1A2681CED200C3DB03 /* JCElementText.m */,
				6713CB1B2681CED200C3DB03 /* JCElementTextConfigure.m */,
				6713CB1C2681CED200C3DB03 /* JCElementTextImage.h */,
				6713CB1D2681CED200C3DB03 /* JCElementText.h */,
				6713CB1E2681CED200C3DB03 /* JCElementTextImage.mm */,
				6713CB1F2681CED200C3DB03 /* JCElementTextConfigure.h */,
			);
			path = "文本";
			sourceTree = "<group>";
		};
		6713CB202681CED200C3DB03 /* 一维码 */ = {
			isa = PBXGroup;
			children = (
				6713CB212681CED200C3DB03 /* JCDrawCodeManager.h */,
				6713CB222681CED200C3DB03 /* JCElementBarCode.h */,
				6713CB232681CED200C3DB03 /* JCElementBarCodeConfigure.m */,
				6713CB242681CED200C3DB03 /* JCElementBarCode.m */,
				6713CB252681CED200C3DB03 /* JCDrawCodeManager.mm */,
				6713CB262681CED200C3DB03 /* JCElementBarCodeConfigure.h */,
			);
			path = "一维码";
			sourceTree = "<group>";
		};
		6713CB272681CED200C3DB03 /* 图片Logo */ = {
			isa = PBXGroup;
			children = (
				6713CB282681CED200C3DB03 /* JCElementLogoView.h */,
				6713CB292681CED200C3DB03 /* JCElementLogoView.mm */,
			);
			path = "图片Logo";
			sourceTree = "<group>";
		};
		6713CB2B2681CED200C3DB03 /* 线条 */ = {
			isa = PBXGroup;
			children = (
				6713CB2C2681CED200C3DB03 /* JCEleLineConfigure.h */,
				6713CB2D2681CED200C3DB03 /* JCElementLine.h */,
				6713CB2E2681CED200C3DB03 /* JCEleLineConfigure.m */,
				6713CB2F2681CED200C3DB03 /* JCElementLine.m */,
			);
			path = "线条";
			sourceTree = "<group>";
		};
		6713CB302681CED200C3DB03 /* 流水号 */ = {
			isa = PBXGroup;
			children = (
				6713CB312681CED200C3DB03 /* JCElementSerialNumber.h */,
				6713CB322681CED200C3DB03 /* JCSerialStyleConfigure.m */,
				6713CB332681CED200C3DB03 /* JCElementSerialNumber.m */,
				6713CB342681CED200C3DB03 /* JCSerialStyleConfigure.h */,
			);
			path = "流水号";
			sourceTree = "<group>";
		};
		6713CB352681CED200C3DB03 /* 时间 */ = {
			isa = PBXGroup;
			children = (
				6713CB362681CED200C3DB03 /* JCEleTimeConfigure.h */,
				6713CB372681CED200C3DB03 /* JCElementTime.m */,
				6713CB382681CED200C3DB03 /* JCEleTimeConfigure.m */,
				6713CB392681CED200C3DB03 /* JCElementTime.h */,
			);
			path = "时间";
			sourceTree = "<group>";
		};
		6713CB3A2681CED200C3DB03 /* 二维码 */ = {
			isa = PBXGroup;
			children = (
				6713CB3B2681CED200C3DB03 /* JCElementQRCode.h */,
				6713CB3C2681CED200C3DB03 /* JCElementQRCode.m */,
			);
			path = "二维码";
			sourceTree = "<group>";
		};
		6713CB3F2681CED200C3DB03 /* 模板预览 */ = {
			isa = PBXGroup;
			children = (
				6713CB402681CED200C3DB03 /* JCPrintSamePreview.m */,
				6713CB412681CED200C3DB03 /* JCPrintSamePreview.h */,
			);
			path = "模板预览";
			sourceTree = "<group>";
		};
		6713CB422681CED200C3DB03 /* 元素依赖 */ = {
			isa = PBXGroup;
			children = (
				6713CB432681CED200C3DB03 /* UIImage+Dotted.h */,
				6713CB442681CED200C3DB03 /* JCStyleButton.h */,
				6713CB452681CED200C3DB03 /* JCStyleTitleLabel.h */,
				6713CB462681CED200C3DB03 /* NSDate+XHExtension.m */,
				6713CB472681CED200C3DB03 /* JCSliderView.m */,
				6713CB482681CED200C3DB03 /* UIView+Border.m */,
				6713CB492681CED200C3DB03 /* JCMoveView.m */,
				6713CB4A2681CED200C3DB03 /* JCSelectionView.h */,
				6713CB4B2681CED200C3DB03 /* JCNormalPicker.m */,
				6713CB4C2681CED200C3DB03 /* JCFontSliderView.m */,
				6713CB4D2681CED200C3DB03 /* JCDownloadLayout.h */,
				6713CB4E2681CED200C3DB03 /* JCDatePicker.m */,
				6713CB4F2681CED200C3DB03 /* JCElementDottedView.h */,
				6713CB502681CED200C3DB03 /* JCSliderView.h */,
				6713CB512681CED200C3DB03 /* NSDate+XHExtension.h */,
				6713CB522681CED200C3DB03 /* JCStyleButton.m */,
				6713CB532681CED200C3DB03 /* JCStyleTitleLabel.m */,
				6713CB542681CED200C3DB03 /* UIImage+Dotted.m */,
				6713CB552681CED200C3DB03 /* UIView+Border.h */,
				6713CB562681CED200C3DB03 /* JCNormalPicker.h */,
				6713CB572681CED200C3DB03 /* JCSelectionView.m */,
				6713CB582681CED200C3DB03 /* JCMoveView.h */,
				6713CB592681CED200C3DB03 /* JCElementDottedView.m */,
				6713CB5A2681CED200C3DB03 /* JCDatePicker.h */,
				6713CB5B2681CED200C3DB03 /* JCDownloadLayout.m */,
				6713CB5C2681CED200C3DB03 /* JCFontSliderView.h */,
			);
			path = "元素依赖";
			sourceTree = "<group>";
		};
		6713CB5D2681CED200C3DB03 /* 模板选择 */ = {
			isa = PBXGroup;
			children = (
				6713CB5E2681CED200C3DB03 /* JCTemplateHistoryView.h */,
				6713CB5F2681CED200C3DB03 /* JCTemplateChoosView.h */,
				6713CB602681CED200C3DB03 /* JCTag.h */,
				6713CB612681CED200C3DB03 /* JCTemplateHorizontalCell.m */,
				6713CB622681CED200C3DB03 /* JCTemplateVerticalCell.m */,
				6713CB632681CED200C3DB03 /* JCTemplateTagModel.m */,
				6713CB642681CED200C3DB03 /* JCTemplateCollectionView.m */,
				6713CB652681CED200C3DB03 /* JCTemplateChoosView.m */,
				6713CB662681CED200C3DB03 /* JCTemplateHistoryView.m */,
				6713CB672681CED200C3DB03 /* JCTemplateHorizontalCell.h */,
				6713CB682681CED200C3DB03 /* JCTag.m */,
				6713CB692681CED200C3DB03 /* JCTemplateCollectionView.h */,
				6713CB6A2681CED200C3DB03 /* JCTemplateVerticalCell.h */,
				6713CB6B2681CED200C3DB03 /* JCTemplateTagModel.h */,
			);
			path = "模板选择";
			sourceTree = "<group>";
		};
		6713CB6C2681CED200C3DB03 /* 画板 */ = {
			isa = PBXGroup;
			children = (
				6713CB6D2681CED200C3DB03 /* JCDrawBoardView.m */,
				6713CB6E2681CED200C3DB03 /* JCPrintDirectionView.m */,
				6713CB6F2681CED200C3DB03 /* JCDrawBoardView+Undo.h */,
				6713CB702681CED200C3DB03 /* JCBoardTitleView.h */,
				6713CB712681CED200C3DB03 /* RulerLayout.h */,
				6713CB722681CED200C3DB03 /* JCScaleResertView.m */,
				6713CB732681CED200C3DB03 /* JCBoardConst.m */,
				6713CB742681CED200C3DB03 /* JCBubbleBar.m */,
				6713CB752681CED200C3DB03 /* JCCanvas.m */,
				6713CB762681CED200C3DB03 /* JCRulerView.h */,
				6713CB772681CED200C3DB03 /* RulerCollectionViewCell.h */,
				6713CB782681CED200C3DB03 /* JCElementMainView.m */,
				6713CB792681CED200C3DB03 /* RulerView.m */,
				6713CB7A2681CED200C3DB03 /* JCBoardConst.h */,
				6713CB7B2681CED200C3DB03 /* JCScaleResertView.h */,
				6713CB7C2681CED200C3DB03 /* RulerLayout.m */,
				6713CB7D2681CED200C3DB03 /* JCBoardTitleView.m */,
				6713CB7E2681CED200C3DB03 /* JCDrawBoardView+Undo.m */,
				6713CB7F2681CED200C3DB03 /* JCDrawBoardView.h */,
				6713CB802681CED200C3DB03 /* JCPrintDirectionView.h */,
				6713CB812681CED200C3DB03 /* JCRulerView.m */,
				6713CB822681CED200C3DB03 /* JCCanvas.h */,
				6713CB832681CED200C3DB03 /* JCBubbleBar.h */,
				6713CB842681CED200C3DB03 /* RulerView.h */,
				6713CB852681CED200C3DB03 /* JCElementMainView.h */,
				6713CB862681CED200C3DB03 /* RulerCollectionViewCell.m */,
			);
			path = "画板";
			sourceTree = "<group>";
		};
		6713CC472681CF9000C3DB03 /* Vender(第三方) */ = {
			isa = PBXGroup;
			children = (
				6713CC482681CF9000C3DB03 /* JCWebView */,
				6713CC6C2681CF9000C3DB03 /* Shop */,
				6713CC732681CF9100C3DB03 /* FMDB */,
			);
			path = "Vender(第三方)";
			sourceTree = "<group>";
		};
		6713CC482681CF9000C3DB03 /* JCWebView */ = {
			isa = PBXGroup;
			children = (
				6713CC492681CF9000C3DB03 /* JCWKWebView.m */,
				6713CC4A2681CF9000C3DB03 /* Category */,
				6713CC532681CF9000C3DB03 /* JCWKWebViewController.h */,
				6713CC542681CF9000C3DB03 /* AppDelegate+Share.h */,
				6713CC552681CF9000C3DB03 /* JCPayManager.h */,
				6713CC562681CF9000C3DB03 /* JCWebViewMacros.h */,
				6713CC572681CF9000C3DB03 /* JCWebProgress.h */,
				6713CC582681CF9000C3DB03 /* JCADPopViewController.m */,
				6713CC592681CF9000C3DB03 /* JCWKWebView.h */,
				6713CC5A2681CF9000C3DB03 /* ShareImages */,
				6713CC672681CF9000C3DB03 /* AppDelegate+Share.m */,
				6713CC682681CF9000C3DB03 /* JCWKWebViewController.m */,
				6713CC692681CF9000C3DB03 /* JCWebProgress.m */,
				6713CC6A2681CF9000C3DB03 /* JCADPopViewController.h */,
				6713CC6B2681CF9000C3DB03 /* JCPayManager.m */,
			);
			path = JCWebView;
			sourceTree = "<group>";
		};
		6713CC4A2681CF9000C3DB03 /* Category */ = {
			isa = PBXGroup;
			children = (
				6713CC4B2681CF9000C3DB03 /* NSString+JC_Device.h */,
				6713CC4C2681CF9000C3DB03 /* UIColor+Extension.h */,
				6713CC4D2681CF9000C3DB03 /* UIView+JCCategory.h */,
				6713CC4E2681CF9000C3DB03 /* NSTimer+addition.h */,
				6713CC4F2681CF9000C3DB03 /* NSTimer+addition.m */,
				6713CC502681CF9000C3DB03 /* UIView+JCCategory.m */,
				6713CC512681CF9000C3DB03 /* UIColor+Extension.m */,
				6713CC522681CF9000C3DB03 /* NSString+JC_Device.m */,
			);
			path = Category;
			sourceTree = "<group>";
		};
		6713CC5A2681CF9000C3DB03 /* ShareImages */ = {
			isa = PBXGroup;
			children = (
				6713CC5B2681CF9000C3DB03 /* AD关闭直线@2x.png */,
				6713CC5C2681CF9000C3DB03 /* AD关闭直线@3x.png */,
				6713CC5D2681CF9000C3DB03 /* 分享_钉钉@3x.png */,
				6713CC5E2681CF9000C3DB03 /* 分享********** */,
				6713CC5F2681CF9000C3DB03 /* 分享_朋友圈@2x.png */,
				6713CC602681CF9000C3DB03 /* 分享_朋友圈@3x.png */,
				6713CC612681CF9000C3DB03 /* 分享********** */,
				6713CC622681CF9000C3DB03 /* 分享_钉钉@2x.png */,
				6713CC632681CF9000C3DB03 /* 分享_微信@2x.png */,
				6713CC642681CF9000C3DB03 /* 分享_微信@3x.png */,
				6713CC652681CF9000C3DB03 /* AD关闭@2x.png */,
				6713CC662681CF9000C3DB03 /* AD关闭@3x.png */,
			);
			path = ShareImages;
			sourceTree = "<group>";
		};
		6713CC6C2681CF9000C3DB03 /* Shop */ = {
			isa = PBXGroup;
			children = (
				6713CC6D2681CF9000C3DB03 /* JCShopMallsViewController.m */,
				6713CC6E2681CF9000C3DB03 /* JCShopBaseWebViewController.m */,
				6713CC6F2681CF9000C3DB03 /* JCShopAliPay.m */,
				6713CC702681CF9000C3DB03 /* JCShopBaseWebViewController.h */,
				6713CC712681CF9000C3DB03 /* JCShopMallsViewController.h */,
				6713CC722681CF9000C3DB03 /* JCShopAliPay.h */,
			);
			path = Shop;
			sourceTree = "<group>";
		};
		6713CC732681CF9100C3DB03 /* FMDB */ = {
			isa = PBXGroup;
			children = (
				6713CC742681CF9100C3DB03 /* JCFMDB.h */,
				6713CC752681CF9100C3DB03 /* JCFMDB.m */,
			);
			path = FMDB;
			sourceTree = "<group>";
		};
		6713CC9C2681E08000C3DB03 /* Resource */ = {
			isa = PBXGroup;
			children = (
				6713CC9D2681E08000C3DB03 /* Localizable.strings */,
				6713CCA02681E08000C3DB03 /* bell */,
				6713CCAB2681E08000C3DB03 /* JCSDKFont.bundle */,
				6713CCAC2681E08000C3DB03 /* ZT001.ttf */,
			);
			path = Resource;
			sourceTree = "<group>";
		};
		6713CCA02681E08000C3DB03 /* bell */ = {
			isa = PBXGroup;
			children = (
				6713CCA12681E08000C3DB03 /* 8.caf */,
				6713CCA22681E08000C3DB03 /* 9.caf */,
				6713CCA32681E08000C3DB03 /* 1.caf */,
				6713CCA42681E08000C3DB03 /* 0.caf */,
				6713CCA52681E08000C3DB03 /* 2.caf */,
				6713CCA62681E08000C3DB03 /* 3.caf */,
				6713CCA72681E08000C3DB03 /* 7.caf */,
				6713CCA82681E08000C3DB03 /* 6.caf */,
				6713CCA92681E08000C3DB03 /* 4.caf */,
				6713CCAA2681E08000C3DB03 /* 5.caf */,
			);
			path = bell;
			sourceTree = "<group>";
		};
		77EF2D572D8ECA31AD6901A0 /* Pods */ = {
			isa = PBXGroup;
			children = (
				4A32C11088860DB500DEA201 /* Pods-Runner.debug.xcconfig */,
				CD584239BC2F0032ADC293FB /* Pods-Runner.release.xcconfig */,
				5CBF09EDD77D488B2DF78F42 /* Pods-Runner.profile.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		9740EEB11CF90186004384FC /* Flutter */ = {
			isa = PBXGroup;
			children = (
				3B3967151E833CAA004F5970 /* AppFrameworkInfo.plist */,
				9740EEB21CF90195004384FC /* Debug.xcconfig */,
				7AFA3C8E1D35360C0083082E /* Release.xcconfig */,
				9740EEB31CF90195004384FC /* Generated.xcconfig */,
			);
			name = Flutter;
			sourceTree = "<group>";
		};
		97C146E51CF9000F007C117D = {
			isa = PBXGroup;
			children = (
				9740EEB11CF90186004384FC /* Flutter */,
				97C146F01CF9000F007C117D /* Runner */,
				97C146EF1CF9000F007C117D /* Products */,
				77EF2D572D8ECA31AD6901A0 /* Pods */,
				D945654EBD48F3FC6E0A2CEF /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		97C146EF1CF9000F007C117D /* Products */ = {
			isa = PBXGroup;
			children = (
				97C146EE1CF9000F007C117D /* Runner.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		97C146F01CF9000F007C117D /* Runner */ = {
			isa = PBXGroup;
			children = (
				6756B2D526846C5E00C08B2B /* PrintData.pbobjc.h */,
				6756B2D626846C5E00C08B2B /* PrintData.pbobjc.m */,
				6756B2D826846CAD00C08B2B /* PrintData.proto */,
				6713CC9C2681E08000C3DB03 /* Resource */,
				6713CC472681CF9000C3DB03 /* Vender(第三方) */,
				6713C9D42681CED000C3DB03 /* DrawBoard */,
				6713C9CA2681CEAC00C3DB03 /* Base */,
				6713C9BD2681CDFD00C3DB03 /* Category */,
				6713C8FC2681CABC00C3DB03 /* detailHeaderCell.h */,
				6713C8FD2681CABC00C3DB03 /* detailHeaderCell.m */,
				6713C8FE2681CABC00C3DB03 /* DetailViewCell.h */,
				6713C8FF2681CABC00C3DB03 /* DetailViewCell.m */,
				6713C8FB2681CABC00C3DB03 /* DeviceDetailController.h */,
				6713C8FA2681CABB00C3DB03 /* DeviceDetailController.m */,
				6713C9002681CABC00C3DB03 /* DevicesSearchController.h */,
				6713C9012681CABD00C3DB03 /* DevicesSearchController.m */,
				6713C9062681CAD000C3DB03 /* SearchHeader.h */,
				6713C9082681CAD000C3DB03 /* SearchHeader.m */,
				6713C9092681CAD100C3DB03 /* SettingPowerOffController.h */,
				6713C9072681CAD000C3DB03 /* SettingPowerOffController.m */,
				6713C90D2681CAF200C3DB03 /* BlueToothCell.h */,
				6713C90C2681CAF200C3DB03 /* BlueToothCell.m */,
				6713C90F2681CAF200C3DB03 /* CalibrationController.h */,
				6713C90E2681CAF200C3DB03 /* CalibrationController.m */,
				6713C8F02681C9F800C3DB03 /* Expand(扩展) */,
				6713C84E2681C9DB00C3DB03 /* Utils(工具类) */,
				7AFFD8ED1D35381100E5BB4D /* AppDelegate.h */,
				7AFFD8EE1D35381100E5BB4D /* AppDelegate.m */,
				6756B2DA26848F9600C08B2B /* ImageHelper.h */,
				6756B2DB26848F9600C08B2B /* ImageHelper.m */,
				97C146FA1CF9000F007C117D /* Main.storyboard */,
				97C146FD1CF9000F007C117D /* Assets.xcassets */,
				97C146FF1CF9000F007C117D /* LaunchScreen.storyboard */,
				97C147021CF9000F007C117D /* Info.plist */,
				97C146F11CF9000F007C117D /* Supporting Files */,
				1498D2321E8E86230040F4C2 /* GeneratedPluginRegistrant.h */,
				1498D2331E8E89220040F4C2 /* GeneratedPluginRegistrant.m */,
				76C66C3728CB157A00384BCB /* KeepSwift.swift */,
				76C66C3628CB157A00384BCB /* Runner-Bridging-Header.h */,
			);
			path = Runner;
			sourceTree = "<group>";
		};
		97C146F11CF9000F007C117D /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				6713C91D2681CD3200C3DB03 /* ChenYinHeader.pch */,
				97C146F21CF9000F007C117D /* main.m */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		D945654EBD48F3FC6E0A2CEF /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				76C66C3428CB11CB00384BCB /* libswiftCore.tbd */,
				6713CC9A2681DF6E00C3DB03 /* libbz2.tbd */,
				6713CC982681DF6800C3DB03 /* libiconv.2.4.0.tbd */,
				6713CC962681DF5E00C3DB03 /* libiconv.2.tbd */,
				6713CC942681DF5600C3DB03 /* libc++.tbd */,
				6713CC922681DF4100C3DB03 /* AVFoundation.framework */,
				6713CC902681DF3A00C3DB03 /* CoreGraphics.framework */,
				FB996FAB4E5C3D141937803C /* libPods-Runner.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		97C146ED1CF9000F007C117D /* Runner */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 97C147051CF9000F007C117D /* Build configuration list for PBXNativeTarget "Runner" */;
			buildPhases = (
				461E78634EFC04071459A236 /* [CP] Check Pods Manifest.lock */,
				9740EEB61CF901F6004384FC /* Run Script */,
				97C146EA1CF9000F007C117D /* Sources */,
				97C146EB1CF9000F007C117D /* Frameworks */,
				97C146EC1CF9000F007C117D /* Resources */,
				9705A1C41CF9048500538489 /* Embed Frameworks */,
				3B06AD1E1E4923F5004D2608 /* Thin Binary */,
				8F2C64DF165D6ABA36CFEBB2 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Runner;
			productName = Runner;
			productReference = 97C146EE1CF9000F007C117D /* Runner.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		97C146E61CF9000F007C117D /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1300;
				ORGANIZATIONNAME = "The Chromium Authors";
				TargetAttributes = {
					97C146ED1CF9000F007C117D = {
						CreatedOnToolsVersion = 7.3.1;
						DevelopmentTeam = YSP53GL6F6;
						LastSwiftMigration = 1340;
					};
				};
			};
			buildConfigurationList = 97C146E91CF9000F007C117D /* Build configuration list for PBXProject "Runner" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 0;
			knownRegions = (
				English,
				en,
				Base,
				"zh-Hans",
			);
			mainGroup = 97C146E51CF9000F007C117D;
			productRefGroup = 97C146EF1CF9000F007C117D /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				97C146ED1CF9000F007C117D /* Runner */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		97C146EC1CF9000F007C117D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6713CCB62681E08000C3DB03 /* 4.caf in Resources */,
				6713CC842681CF9100C3DB03 /* 分享_微信@2x.png in Resources */,
				6713CCB82681E08000C3DB03 /* JCSDKFont.bundle in Resources */,
				6713CCB12681E08000C3DB03 /* 0.caf in Resources */,
				6713CC852681CF9100C3DB03 /* 分享_微信@3x.png in Resources */,
				6713CC812681CF9100C3DB03 /* 分享_朋友圈@3x.png in Resources */,
				6713CCAE2681E08000C3DB03 /* 8.caf in Resources */,
				6713CCB42681E08000C3DB03 /* 7.caf in Resources */,
				6713CC7E2681CF9100C3DB03 /* 分享_钉钉@3x.png in Resources */,
				6713CB892681CED200C3DB03 /* JCBubbleTableViewCell.xib in Resources */,
				6713C8BF2681C9DC00C3DB03 /* SDK程序电子文件及版本说明.xlsx in Resources */,
				97C147011CF9000F007C117D /* LaunchScreen.storyboard in Resources */,
				6713CCB92681E08000C3DB03 /* ZT001.ttf in Resources */,
				6713CC7F2681CF9100C3DB03 /* 分享********** in Resources */,
				6713CB982681CED200C3DB03 /* JCRfidError.xib in Resources */,
				6713CCAF2681E08000C3DB03 /* 9.caf in Resources */,
				6713CCB22681E08000C3DB03 /* 2.caf in Resources */,
				6713CC7D2681CF9100C3DB03 /* AD关闭直线@3x.png in Resources */,
				6713CCB72681E08000C3DB03 /* 5.caf in Resources */,
				3B3967161E833CAA004F5970 /* AppFrameworkInfo.plist in Resources */,
				6713CC862681CF9100C3DB03 /* AD关闭@2x.png in Resources */,
				6713CC802681CF9100C3DB03 /* 分享_朋友圈@2x.png in Resources */,
				6713CCAD2681E08000C3DB03 /* Localizable.strings in Resources */,
				6713CCB52681E08000C3DB03 /* 6.caf in Resources */,
				6713CCB32681E08000C3DB03 /* 3.caf in Resources */,
				9740EEB41CF90195004384FC /* Debug.xcconfig in Resources */,
				6713CC832681CF9100C3DB03 /* 分享_钉钉@2x.png in Resources */,
				6713CC7C2681CF9100C3DB03 /* AD关闭直线@2x.png in Resources */,
				6713CBC02681CED200C3DB03 /* JCMessageImageView.xib in Resources */,
				6713CC872681CF9100C3DB03 /* AD关闭@3x.png in Resources */,
				6713CCB02681E08000C3DB03 /* 1.caf in Resources */,
				6713CC822681CF9100C3DB03 /* 分享********** in Resources */,
				97C146FE1CF9000F007C117D /* Assets.xcassets in Resources */,
				97C146FC1CF9000F007C117D /* Main.storyboard in Resources */,
				6713CBAB2681CED200C3DB03 /* JCMessageView.xib in Resources */,
				6713C8D22681C9DC00C3DB03 /* MBProgressHUD.bundle in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		3B06AD1E1E4923F5004D2608 /* Thin Binary */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			name = "Thin Binary";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "/bin/sh \"$FLUTTER_ROOT/packages/flutter_tools/bin/xcode_backend.sh\" embed_and_thin";
		};
		461E78634EFC04071459A236 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Runner-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		8F2C64DF165D6ABA36CFEBB2 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-resources.sh",
				"${PODS_ROOT}/AlipaySDK-iOS/AlipaySDK.bundle",
				"${PODS_ROOT}/LBXScan/LBXScan/UI/CodeScan.bundle",
				"${PODS_ROOT}/MJRefresh/MJRefresh/MJRefresh.bundle",
				"${PODS_ROOT}/ZLPhotoBrowser/ZLPhotoBrowser/PhotoBrowser/resource/ZLPhotoActionSheet.xib",
				"${PODS_ROOT}/ZLPhotoBrowser/ZLPhotoBrowser/PhotoBrowser/resource/ZLPhotoBrowserCell.xib",
				"${PODS_ROOT}/ZLPhotoBrowser/ZLPhotoBrowser/PhotoBrowser/resource/ZLPhotoBrowser.bundle",
			);
			name = "[CP] Copy Pods Resources";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/AlipaySDK.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/CodeScan.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/MJRefresh.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/ZLPhotoActionSheet.nib",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/ZLPhotoBrowserCell.nib",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/ZLPhotoBrowser.bundle",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		9740EEB61CF901F6004384FC /* Run Script */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			name = "Run Script";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "/bin/sh \"$FLUTTER_ROOT/packages/flutter_tools/bin/xcode_backend.sh\" build";
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		97C146EA1CF9000F007C117D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6713C8ED2681C9DC00C3DB03 /* NSData+YDCRC.m in Sources */,
				6713CBED2681CED300C3DB03 /* JCAddElementBar.m in Sources */,
				6713CB8D2681CED200C3DB03 /* NSObject+JCContext.m in Sources */,
				6713CBD22681CED300C3DB03 /* UIImage+uploadRequest.m in Sources */,
				6713CC8A2681CF9100C3DB03 /* JCWebProgress.m in Sources */,
				6713CBBF2681CED200C3DB03 /* MBProgressHUD+Extension.m in Sources */,
				6713CC342681CED300C3DB03 /* JCTemplateVerticalCell.m in Sources */,
				6713CB962681CED200C3DB03 /* JCAlert.m in Sources */,
				6713CBD02681CED200C3DB03 /* JCTemplateImageManager.mm in Sources */,
				6713CC052681CED300C3DB03 /* JCTextStyleView.m in Sources */,
				6713CC3A2681CED300C3DB03 /* JCDrawBoardView.m in Sources */,
				6756B2DC26848F9600C08B2B /* ImageHelper.m in Sources */,
				6713CBD82681CED300C3DB03 /* JCElementModel+Transfer.m in Sources */,
				6713C9052681CABD00C3DB03 /* DevicesSearchController.m in Sources */,
				6713CBEC2681CED300C3DB03 /* JCTemplateAntiLostChooseView.m in Sources */,
				6713CC0E2681CED300C3DB03 /* JCBarCollectionCell.m in Sources */,
				6713CC1F2681CED300C3DB03 /* JCElementLine.m in Sources */,
				6713CBDF2681CED300C3DB03 /* JCTemplateData+External.m in Sources */,
				6713CC1E2681CED300C3DB03 /* JCEleLineConfigure.m in Sources */,
				6713CBF92681CED300C3DB03 /* JCPrintAlertView.m in Sources */,
				6713CC102681CED300C3DB03 /* JCDrawNavigationBar4AntiLost.m in Sources */,
				978B8F6F1D3862AE00F588F7 /* AppDelegate.m in Sources */,
				6713CBE72681CED300C3DB03 /* JCStyleContentView.m in Sources */,
				6713CBD32681CED300C3DB03 /* JCBoardTemplateManager.m in Sources */,
				6713C8DF2681C9DC00C3DB03 /* BabySpeaker.m in Sources */,
				6713CC2A2681CED300C3DB03 /* JCNormalPicker.m in Sources */,
				6713C8CB2681C9DC00C3DB03 /* DeviceUpdateUtil4AntiLost.m in Sources */,
				6713CC352681CED300C3DB03 /* JCTemplateTagModel.m in Sources */,
				6713C9022681CABD00C3DB03 /* DeviceDetailController.m in Sources */,
				6713C9112681CAF200C3DB03 /* CalibrationController.m in Sources */,
				6713CBDB2681CED300C3DB03 /* JCElementModel.m in Sources */,
				6713CB8E2681CED200C3DB03 /* UIImage+EasyExtend.m in Sources */,
				6713C8C22681C9DC00C3DB03 /* JCRFIDModel.m in Sources */,
				6713CBD12681CED200C3DB03 /* JCScanCodeManager.m in Sources */,
				6713CC252681CED300C3DB03 /* JCPrintSamePreview.m in Sources */,
				6713CC222681CED300C3DB03 /* JCElementTime.m in Sources */,
				6713CB9C2681CED200C3DB03 /* JCUniqueModel.m in Sources */,
				6756B2D926846CAD00C08B2B /* PrintData.proto in Sources */,
				6713CC152681CED300C3DB03 /* JCElementBox.m in Sources */,
				6713CC202681CED300C3DB03 /* JCSerialStyleConfigure.m in Sources */,
				6713C90A2681CAD100C3DB03 /* SettingPowerOffController.m in Sources */,
				6713CBE82681CED300C3DB03 /* JCStyleModel.m in Sources */,
				6713CC2B2681CED300C3DB03 /* JCFontSliderView.m in Sources */,
				6713CBCD2681CED200C3DB03 /* JCItemToElement.m in Sources */,
				6713CBC72681CED200C3DB03 /* JCTemplateData4DBManager.m in Sources */,
				6713C8C52681C9DC00C3DB03 /* NullSafe.m in Sources */,
				6713CC022681CED300C3DB03 /* JCIconBorderManager.m in Sources */,
				6713CBD72681CED300C3DB03 /* JCDrawInfoManager.m in Sources */,
				6713C8D82681C9DC00C3DB03 /* CommonCtrl.m in Sources */,
				6713CC2F2681CED300C3DB03 /* UIImage+Dotted.m in Sources */,
				6713CC3E2681CED300C3DB03 /* JCBubbleBar.m in Sources */,
				6713CB932681CED200C3DB03 /* JCBaseScrollView+Touch.m in Sources */,
				6713CBD92681CED300C3DB03 /* JCFontModel.m in Sources */,
				6713CBB42681CED200C3DB03 /* MMPopupCategory.m in Sources */,
				6713CC792681CF9100C3DB03 /* UIColor+Extension.m in Sources */,
				6713CBE12681CED300C3DB03 /* JCTemplateData+SDK.mm in Sources */,
				6713CC192681CED300C3DB03 /* JCElementTextImage.mm in Sources */,
				6713CC8E2681CF9100C3DB03 /* JCShopAliPay.m in Sources */,
				6713CC262681CED300C3DB03 /* NSDate+XHExtension.m in Sources */,
				6713CB872681CED200C3DB03 /* NSString+XYCategory.m in Sources */,
				6713CC3F2681CED300C3DB03 /* JCCanvas.m in Sources */,
				6713C8EF2681C9DC00C3DB03 /* JCDeviceUpdateAleart.m in Sources */,
				6713C8F92681C9F800C3DB03 /* JCConst.m in Sources */,
				6713CBB32681CED200C3DB03 /* MMSheetView.m in Sources */,
				6713CBC12681CED200C3DB03 /* JCBluetoothModel.m in Sources */,
				6713CC7A2681CF9100C3DB03 /* NSString+JC_Device.m in Sources */,
				6713CC3D2681CED300C3DB03 /* JCBoardConst.m in Sources */,
				6713CB922681CED200C3DB03 /* UIImage+Tool.m in Sources */,
				6713CBA12681CED200C3DB03 /* JCPrintRecordManager.m in Sources */,
				6713CC762681CF9100C3DB03 /* JCWKWebView.m in Sources */,
				6713C9042681CABD00C3DB03 /* DetailViewCell.m in Sources */,
				6713C8EE2681C9DC00C3DB03 /* JCDownLoadAleart.m in Sources */,
				6713CBC82681CED200C3DB03 /* UIImageView+DownLoad.m in Sources */,
				6713CB9A2681CED200C3DB03 /* JCMessageView.m in Sources */,
				6713CC3C2681CED300C3DB03 /* JCScaleResertView.m in Sources */,
				6713CBA02681CED200C3DB03 /* JCPaperManager.m in Sources */,
				6713CBAA2681CED200C3DB03 /* JCRfidCheckManager.m in Sources */,
				6713CBEF2681CED300C3DB03 /* JCActionButton.m in Sources */,
				6713CC892681CF9100C3DB03 /* JCWKWebViewController.m in Sources */,
				6713CBF72681CED300C3DB03 /* JCProgressView.m in Sources */,
				6713CC2E2681CED300C3DB03 /* JCStyleTitleLabel.m in Sources */,
				6713CC772681CF9100C3DB03 /* NSTimer+addition.m in Sources */,
				6713CB9B2681CED200C3DB03 /* JCPrintAntiLostCessView.m in Sources */,
				6713CBA52681CED200C3DB03 /* JCPolicy4DB.m in Sources */,
				6713CC162681CED300C3DB03 /* JCEleBoxConfigure.m in Sources */,
				6713CBB12681CED200C3DB03 /* JCPrintDevice.m in Sources */,
				6713CC392681CED300C3DB03 /* JCTag.m in Sources */,
				6713CC402681CED300C3DB03 /* JCElementMainView.m in Sources */,
				6713C8CA2681C9DC00C3DB03 /* JCRfidRuleModel.m in Sources */,
				6713CC242681CED300C3DB03 /* JCElementQRCode.m in Sources */,
				6713CBEE2681CED300C3DB03 /* JCElementEditBar.m in Sources */,
				6713CC312681CED300C3DB03 /* JCElementDottedView.m in Sources */,
				6713CBFA2681CED300C3DB03 /* JCDrawNavigationBar.m in Sources */,
				6713CC282681CED300C3DB03 /* UIView+Border.m in Sources */,
				6713CC132681CED300C3DB03 /* JCElementBaseView.m in Sources */,
				6713CC172681CED300C3DB03 /* JCElementText.m in Sources */,
				6713CB9E2681CED200C3DB03 /* JCBoardGuideView.m in Sources */,
				97C146F31CF9000F007C117D /* main.m in Sources */,
				6713CBEB2681CED300C3DB03 /* JCTemplateAntiLostHorizontalCell.m in Sources */,
				6713C8CD2681C9DC00C3DB03 /* LBXScanPermissions.m in Sources */,
				6713C8C12681C9DC00C3DB03 /* JCVenderTool.m in Sources */,
				6713CB902681CED200C3DB03 /* JCBubbleTableViewCell.m in Sources */,
				6756B2D726846C5E00C08B2B /* PrintData.pbobjc.m in Sources */,
				6713CBB02681CED200C3DB03 /* JCDeviceUpdateTool.m in Sources */,
				6713CB912681CED200C3DB03 /* JCExcelForElement.m in Sources */,
				6713C8E82681C9DC00C3DB03 /* JCErrorInfo.m in Sources */,
				6713CBB82681CED200C3DB03 /* XYView.m in Sources */,
				6713CC2C2681CED300C3DB03 /* JCDatePicker.m in Sources */,
				6713CBAC2681CED200C3DB03 /* JCPaperErrorView.m in Sources */,
				6713C8DE2681C9DC00C3DB03 /* BabyCentralManager.m in Sources */,
				6713CBA32681CED200C3DB03 /* JCDBManager.m in Sources */,
				6713C8E02681C9DC00C3DB03 /* BabyRhythm.m in Sources */,
				6713CC8F2681CF9100C3DB03 /* JCFMDB.m in Sources */,
				6713CBD52681CED300C3DB03 /* JCElementEditManager.m in Sources */,
				6713C8E62681C9DC00C3DB03 /* DCBaseRequst.m in Sources */,
				6713CC182681CED300C3DB03 /* JCElementTextConfigure.m in Sources */,
				6713CC422681CED300C3DB03 /* RulerLayout.m in Sources */,
				6713CBA72681CED200C3DB03 /* JCPolicy.m in Sources */,
				6713CBF52681CED300C3DB03 /* JCPropertyMainView.m in Sources */,
				6713CC232681CED300C3DB03 /* JCEleTimeConfigure.m in Sources */,
				6713CC212681CED300C3DB03 /* JCElementSerialNumber.m in Sources */,
				6713CBE22681CED300C3DB03 /* JCPrintSamePreviewModel.m in Sources */,
				6713CC072681CED300C3DB03 /* JCIconView.m in Sources */,
				6713CC432681CED300C3DB03 /* JCBoardTitleView.m in Sources */,
				6713CBC42681CED200C3DB03 /* JCWifiManager.m in Sources */,
				6713CC0D2681CED300C3DB03 /* JCBarData.m in Sources */,
				6713CC082681CED300C3DB03 /* JCIconDataManager.m in Sources */,
				6713CC462681CED300C3DB03 /* RulerCollectionViewCell.m in Sources */,
				6713CB9D2681CED200C3DB03 /* JCDeviceFirmwareRemoteModel.m in Sources */,
				6713C8E92681C9DC00C3DB03 /* Reachability.m in Sources */,
				6713CC322681CED300C3DB03 /* JCDownloadLayout.m in Sources */,
				6713CB8B2681CED200C3DB03 /* UIFont+JCCustomFont.m in Sources */,
				6713CBF62681CED300C3DB03 /* JCLogoCategoryModel.m in Sources */,
				6713C9C72681CDFD00C3DB03 /* UIView+JCCategory.m in Sources */,
				6713CBB52681CED200C3DB03 /* MMPopupWindow.m in Sources */,
				6713CBAE2681CED200C3DB03 /* JCDeviceOffSetInfoModel.m in Sources */,
				6713CB952681CED200C3DB03 /* JCBaseScrollView.m in Sources */,
				6713CBF82681CED300C3DB03 /* JCBoardRightItemView.m in Sources */,
				6713CC2D2681CED300C3DB03 /* JCStyleButton.m in Sources */,
				6713CBAD2681CED200C3DB03 /* JCRfidError.m in Sources */,
				6713C8CF2681C9DC00C3DB03 /* Global.m in Sources */,
				6713CBC52681CED200C3DB03 /* JCDeviceSeriesHelp.m in Sources */,
				6713CBCC2681CED200C3DB03 /* JCStack.m in Sources */,
				6713C8DB2681C9DC00C3DB03 /* BabyOptions.m in Sources */,
				6713C9D12681CEAC00C3DB03 /* DCBaseModel.m in Sources */,
				6713CBBD2681CED200C3DB03 /* JCRecordModel.m in Sources */,
				6713CBA42681CED200C3DB03 /* JCPrintExecuteManager.m in Sources */,
				6713C8DC2681C9DC00C3DB03 /* BabyCallback.m in Sources */,
				6713C9032681CABD00C3DB03 /* detailHeaderCell.m in Sources */,
				6713CC0A2681CED300C3DB03 /* JCFontTableViewCell.m in Sources */,
				6713C8D42681C9DC00C3DB03 /* DCGCDTimer.m in Sources */,
				6713CBBC2681CED200C3DB03 /* JCRecordTool.m in Sources */,
				6713CC412681CED300C3DB03 /* RulerView.m in Sources */,
				6713C90B2681CAD100C3DB03 /* SearchHeader.m in Sources */,
				6713CBFC2681CED300C3DB03 /* JCLineStyleView.m in Sources */,
				6713CC8C2681CF9100C3DB03 /* JCShopMallsViewController.m in Sources */,
				6713CBF12681CED300C3DB03 /* JCTagListManager.m in Sources */,
				6713CBDD2681CED300C3DB03 /* JCTemplateData+Transfer.m in Sources */,
				6713CC1A2681CED300C3DB03 /* JCElementBarCodeConfigure.m in Sources */,
				6713CBE92681CED300C3DB03 /* JCMutablePropertyView.m in Sources */,
				6713CC042681CED300C3DB03 /* JCDateFormatView.m in Sources */,
				6713CC302681CED300C3DB03 /* JCSelectionView.m in Sources */,
				6713CBCA2681CED200C3DB03 /* JCOssApi.m in Sources */,
				6713C9C82681CDFD00C3DB03 /* UIColor+Extension.m in Sources */,
				6713C8CE2681C9DC00C3DB03 /* QQLBXScanViewController.m in Sources */,
				6713CBF42681CED300C3DB03 /* JCTagImageTableViewCell.m in Sources */,
				6713C8C02681C9DC00C3DB03 /* LocalNotificationUtil.m in Sources */,
				6713C8E12681C9DC00C3DB03 /* BabyBluetooth.m in Sources */,
				6713CBA62681CED200C3DB03 /* JCPolicyManager.m in Sources */,
				6713CC8B2681CF9100C3DB03 /* JCPayManager.m in Sources */,
				6713CBF02681CED300C3DB03 /* JCAddElementView.m in Sources */,
				6713C8E32681C9DC00C3DB03 /* JCLogUtil.m in Sources */,
				6713C8D92681C9DC00C3DB03 /* JCRFIDDeviceModel.m in Sources */,
				6713C8E22681C9DC00C3DB03 /* BabyDefine.m in Sources */,
				6713C8C42681C9DC00C3DB03 /* JCApplicationManager.m in Sources */,
				6713CBA92681CED200C3DB03 /* JCDevice.m in Sources */,
				6713C8D12681C9DC00C3DB03 /* DCHUDHelper.m in Sources */,
				6713CBFE2681CED300C3DB03 /* JCInputTextView.m in Sources */,
				6713C8C72681C9DC00C3DB03 /* JCPrinterSameTool.m in Sources */,
				6713CC1D2681CED300C3DB03 /* JCElementLogoView.mm in Sources */,
				6713C8C92681C9DC00C3DB03 /* JCAntiLostUtil.m in Sources */,
				6713CBD62681CED300C3DB03 /* JCDefaultElementManager.m in Sources */,
				6713CC0C2681CED300C3DB03 /* JCTagListLeftCell.m in Sources */,
				6713CC1B2681CED300C3DB03 /* JCElementBarCode.m in Sources */,
				6713CBA82681CED200C3DB03 /* JCDeviceManager.m in Sources */,
				6713CBE02681CED300C3DB03 /* JCGoodDetailInfo.m in Sources */,
				6713CB8C2681CED200C3DB03 /* JCContext.m in Sources */,
				6713CBC32681CED200C3DB03 /* JCPrinterTypeHelp.m in Sources */,
				6713CB942681CED200C3DB03 /* pinyin.m in Sources */,
				6713CBBB2681CED200C3DB03 /* JCSelectDeviceModel.m in Sources */,
				6713C9D22681CEAC00C3DB03 /* JCUserModel.m in Sources */,
				6713CBFB2681CED300C3DB03 /* JCElementActionBar.m in Sources */,
				6713C8DA2681C9DC00C3DB03 /* BabyToy.m in Sources */,
				6713CC882681CF9100C3DB03 /* AppDelegate+Share.m in Sources */,
				6713CBF32681CED300C3DB03 /* UIView+PlaceHolder.m in Sources */,
				6713CBB92681CED200C3DB03 /* JCMessageImageView.m in Sources */,
				6713CC012681CED300C3DB03 /* JCBorderView.m in Sources */,
				6713CB992681CED200C3DB03 /* JCPrintCessView.m in Sources */,
				6713CBC92681CED200C3DB03 /* JCElementToItem.m in Sources */,
				6713C8E52681C9DC00C3DB03 /* NSDictionary+DCJson.m in Sources */,
				6713C9C92681CDFD00C3DB03 /* NSString+JC_Device.m in Sources */,
				6713CBAF2681CED200C3DB03 /* JCPrinterModel.m in Sources */,
				6713CC122681CED300C3DB03 /* JCTagChooseListView.m in Sources */,
				6713CBB22681CED200C3DB03 /* MMAlertView.m in Sources */,
				6713CB972681CED200C3DB03 /* JCPrintCenter.m in Sources */,
				6713C8C62681C9DC00C3DB03 /* JCDevicesSeriesModel.m in Sources */,
				6713C8CC2681C9DC00C3DB03 /* StyleDIY.m in Sources */,
				6713CC002681CED300C3DB03 /* JCBorderCollectionCell.m in Sources */,
				6713CBDE2681CED300C3DB03 /* JCTemplateList.m in Sources */,
				6713CC7B2681CF9100C3DB03 /* JCADPopViewController.m in Sources */,
				6713C9102681CAF200C3DB03 /* BlueToothCell.m in Sources */,
				6713CC8D2681CF9100C3DB03 /* JCShopBaseWebViewController.m in Sources */,
				6713CB8A2681CED200C3DB03 /* UITextView+ZWPlaceHolder.m in Sources */,
				6713C8DD2681C9DC00C3DB03 /* BabyPeripheralManager.m in Sources */,
				6713CBBE2681CED200C3DB03 /* JCStatisticsModel.m in Sources */,
				6713CC782681CF9100C3DB03 /* UIView+JCCategory.m in Sources */,
				6713C8D72681C9DC00C3DB03 /* JCEventTrackingTool.m in Sources */,
				6713CBFF2681CED300C3DB03 /* JCFontSelectView.m in Sources */,
				6713C8D62681C9DC00C3DB03 /* SDAnimatedImageView+JCImageView.m in Sources */,
				6713C8D02681C9DC00C3DB03 /* UIView+EasyExtend.m in Sources */,
				6713CBDC2681CED300C3DB03 /* JCTemplateData+PrintConfigure.m in Sources */,
				6713CC292681CED300C3DB03 /* JCMoveView.m in Sources */,
				6713C8D32681C9DC00C3DB03 /* DCWeakTimer.m in Sources */,
				1498D2341E8E89220040F4C2 /* GeneratedPluginRegistrant.m in Sources */,
				6713CB882681CED200C3DB03 /* XYTool.m in Sources */,
				6713CC372681CED300C3DB03 /* JCTemplateChoosView.m in Sources */,
				6713CBCB2681CED200C3DB03 /* JCFontDownLoadManager.m in Sources */,
				6713CC452681CED300C3DB03 /* JCRulerView.m in Sources */,
				6713CC0F2681CED300C3DB03 /* JCElementPropertyBar.m in Sources */,
				6713CBE42681CED300C3DB03 /* XYBaseModel.m in Sources */,
				6713CBCF2681CED200C3DB03 /* JCElementOperateManager.m in Sources */,
				6713CC362681CED300C3DB03 /* JCTemplateCollectionView.m in Sources */,
				6713CBFD2681CED300C3DB03 /* JCAlignView.m in Sources */,
				6713CC382681CED300C3DB03 /* JCTemplateHistoryView.m in Sources */,
				6713CC272681CED300C3DB03 /* JCSliderView.m in Sources */,
				6713CBB72681CED200C3DB03 /* MMPopupItem.m in Sources */,
				6713CBE32681CED300C3DB03 /* JCTemplateData4DB.m in Sources */,
				6713CC442681CED300C3DB03 /* JCDrawBoardView+Undo.m in Sources */,
				6713CBF22681CED300C3DB03 /* JCTagModel.m in Sources */,
				6713CBDA2681CED300C3DB03 /* JCTemplateData.m in Sources */,
				6713C8D52681C9DC00C3DB03 /* DCProxy.m in Sources */,
				6713CC3B2681CED300C3DB03 /* JCPrintDirectionView.m in Sources */,
				6713C8C82681C9DC00C3DB03 /* DeviceUpdateUtil.m in Sources */,
				6713CC0B2681CED300C3DB03 /* JCPrintSettingView.m in Sources */,
				6713C9C62681CDFD00C3DB03 /* NSTimer+addition.m in Sources */,
				6713CC332681CED300C3DB03 /* JCTemplateHorizontalCell.m in Sources */,
				6713CC092681CED300C3DB03 /* JCIconCollectionViewCell.m in Sources */,
				6713CC142681CED300C3DB03 /* JCElementGraph.mm in Sources */,
				6713CBBA2681CED200C3DB03 /* UIView+XYCategory.m in Sources */,
				6713CB8F2681CED200C3DB03 /* NSArray+EasyExtend.mm in Sources */,
				6713CC032681CED300C3DB03 /* JCImagePropertyView.m in Sources */,
				6713CBA22681CED200C3DB03 /* JCImprintManager.m in Sources */,
				76C66C3828CB157A00384BCB /* KeepSwift.swift in Sources */,
				6713CBEA2681CED300C3DB03 /* JCTemplateAntiLostCollectionView.m in Sources */,
				6713CBC22681CED200C3DB03 /* JCBluetoothManager.m in Sources */,
				6713CC1C2681CED300C3DB03 /* JCDrawCodeManager.mm in Sources */,
				6713CBE62681CED300C3DB03 /* JCDisplacementView.m in Sources */,
				6713CC062681CED300C3DB03 /* JCIConObject.m in Sources */,
				6713C9D32681CEAC00C3DB03 /* DCNavigationViewController.m in Sources */,
				6713CB9F2681CED200C3DB03 /* JCPaper.m in Sources */,
				6713C8E42681C9DC00C3DB03 /* DCNormalHTTPRequst.m in Sources */,
				6713CBC62681CED200C3DB03 /* JCTemplateEditController.m in Sources */,
				6713C8E72681C9DC00C3DB03 /* JCOSSRequest.m in Sources */,
				6713C8C32681C9DC00C3DB03 /* JCKeychainTool.m in Sources */,
				6713CBD42681CED300C3DB03 /* JCFontManager.m in Sources */,
				6713CBB62681CED200C3DB03 /* MMPopupView.m in Sources */,
				6713CBCE2681CED200C3DB03 /* JCTMDataBindGoodsInfoManager.m in Sources */,
				6713CC112681CED300C3DB03 /* JCSearchTagTextField.m in Sources */,
				6713CBE52681CED300C3DB03 /* JCFontSize.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		6713CC9D2681E08000C3DB03 /* Localizable.strings */ = {
			isa = PBXVariantGroup;
			children = (
				6713CC9E2681E08000C3DB03 /* zh-Hans */,
				6713CC9F2681E08000C3DB03 /* en */,
			);
			name = Localizable.strings;
			sourceTree = "<group>";
		};
		97C146FA1CF9000F007C117D /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				97C146FB1CF9000F007C117D /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		97C146FF1CF9000F007C117D /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				97C147001CF9000F007C117D /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		24247ED7217E8F7400BE4BD8 /* Profile */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Profile;
		};
		24247ED8217E8F7400BE4BD8 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_TEAM = YSP53GL6F6;
				ENABLE_BITCODE = NO;
				EXCLUDED_ARCHS = "EXCLUDED_ARCHS__EFFECTIVE_PLATFORM_SUFFIX_simulator__NATIVE_ARCH_64_BIT_x86_64=arm64 arm64e armv7 armv7s armv6 armv8 EXCLUDED_ARCHS=$(inherited) $(EXCLUDED_ARCHS__EFFECTIVE_PLATFORM_SUFFIX_$(EFFECTIVE_PLATFORM_SUFFIX)__NATIVE_ARCH_64_BIT_$(NATIVE_ARCH_64_BIT))";
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
					"$(PROJECT_DIR)/Runner/Utils(工具类)/B3S",
				);
				GCC_PREFIX_HEADER = Runner/ChenYinHeader.pch;
				INFOPLIST_FILE = Runner/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
					"$(PROJECT_DIR)/Runner/Utils(工具类)/Ant",
					"$(PROJECT_DIR)/Runner/Utils(工具类)/B3S",
					"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)",
					"$(TOOLCHAIN_DIR)/usr/lib/swift-5.0/$(PLATFORM_NAME)",
					"$(SDKROOT)/usr/lib/swift",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.niimbot.canvas;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Profile;
		};
		97C147031CF9000F007C117D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		97C147041CF9000F007C117D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		97C147061CF9000F007C117D /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9740EEB21CF90195004384FC /* Debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_TEAM = YSP53GL6F6;
				ENABLE_BITCODE = NO;
				EXCLUDED_ARCHS = "EXCLUDED_ARCHS__EFFECTIVE_PLATFORM_SUFFIX_simulator__NATIVE_ARCH_64_BIT_x86_64=arm64 arm64e armv7 armv7s armv6 armv8 EXCLUDED_ARCHS=$(inherited) $(EXCLUDED_ARCHS__EFFECTIVE_PLATFORM_SUFFIX_$(EFFECTIVE_PLATFORM_SUFFIX)__NATIVE_ARCH_64_BIT_$(NATIVE_ARCH_64_BIT))";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "arm64 i386 armv7 armv7s armv8 EXCLUDED_ARCHS__EFFECTIVE_PLATFORM_SUFFIX_simulator__NATIVE_ARCH_64_BIT_x86_64=arm64 arm64e armv7 armv7s armv6 armv8 EXCLUDED_ARCHS=$(inherited) $(EXCLUDED_ARCHS__EFFECTIVE_PLATFORM_SUFFIX_$(EFFECTIVE_PLATFORM_SUFFIX)__NATIVE_ARCH_64_BIT_$(NATIVE_ARCH_64_BIT))";
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
					"$(PROJECT_DIR)/Runner/Utils(工具类)/B3S",
				);
				GCC_PREFIX_HEADER = Runner/ChenYinHeader.pch;
				INFOPLIST_FILE = Runner/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
					"$(PROJECT_DIR)/Runner/Utils(工具类)/Ant",
					"$(PROJECT_DIR)/Runner/Utils(工具类)/B3S",
					"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)",
					"$(TOOLCHAIN_DIR)/usr/lib/swift-5.0/$(PLATFORM_NAME)",
					"$(SDKROOT)/usr/lib/swift",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.niimbot.canvas;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		97C147071CF9000F007C117D /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_TEAM = YSP53GL6F6;
				ENABLE_BITCODE = NO;
				EXCLUDED_ARCHS = "EXCLUDED_ARCHS__EFFECTIVE_PLATFORM_SUFFIX_simulator__NATIVE_ARCH_64_BIT_x86_64=arm64 arm64e armv7 armv7s armv6 armv8 EXCLUDED_ARCHS=$(inherited) $(EXCLUDED_ARCHS__EFFECTIVE_PLATFORM_SUFFIX_$(EFFECTIVE_PLATFORM_SUFFIX)__NATIVE_ARCH_64_BIT_$(NATIVE_ARCH_64_BIT))";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "arm64 armv7 armv8 armv7s i386 EXCLUDED_ARCHS__EFFECTIVE_PLATFORM_SUFFIX_simulator__NATIVE_ARCH_64_BIT_x86_64=arm64 arm64e armv7 armv7s armv6 armv8 EXCLUDED_ARCHS=$(inherited) $(EXCLUDED_ARCHS__EFFECTIVE_PLATFORM_SUFFIX_$(EFFECTIVE_PLATFORM_SUFFIX)__NATIVE_ARCH_64_BIT_$(NATIVE_ARCH_64_BIT))";
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
					"$(PROJECT_DIR)/Runner/Utils(工具类)/B3S",
				);
				GCC_PREFIX_HEADER = Runner/ChenYinHeader.pch;
				INFOPLIST_FILE = Runner/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
					"$(PROJECT_DIR)/Runner/Utils(工具类)/Ant",
					"$(PROJECT_DIR)/Runner/Utils(工具类)/B3S",
					"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)",
					"$(TOOLCHAIN_DIR)/usr/lib/swift-5.0/$(PLATFORM_NAME)",
					"$(SDKROOT)/usr/lib/swift",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.niimbot.canvas;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		97C146E91CF9000F007C117D /* Build configuration list for PBXProject "Runner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				97C147031CF9000F007C117D /* Debug */,
				97C147041CF9000F007C117D /* Release */,
				24247ED7217E8F7400BE4BD8 /* Profile */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		97C147051CF9000F007C117D /* Build configuration list for PBXNativeTarget "Runner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				97C147061CF9000F007C117D /* Debug */,
				97C147071CF9000F007C117D /* Release */,
				24247ED8217E8F7400BE4BD8 /* Profile */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 97C146E61CF9000F007C117D /* Project object */;
}
