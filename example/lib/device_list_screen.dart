import 'package:flutter/material.dart';

class DeviceListScreen extends StatefulWidget {
  final String deviceName;

  const DeviceListScreen({super.key, required this.deviceName});

  @override
  _DeviceListWidgetState createState() => _DeviceListWidgetState();
}

class _DeviceListWidgetState extends State<DeviceListScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('设备',
            style: TextStyle(
                fontSize: 20,
                color: Colors.black,
                fontWeight: FontWeight.bold)),
        centerTitle: true,
      ),
      body: Container(
        color: Colors.grey[200],
        // child: LinkDeviceWidget(),
      ),
    );
  }
}
