import 'package:flutter/material.dart';

void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: Scaffold(
        appBar: AppBar(
          title: Text('InteractiveViewer with Stack'),
        ),
        body: InteractiveViewerWithStack(),
      ),
    );
  }
}

class InteractiveViewerWithStack extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return SizedBox.expand(child: LayoutBuilder(
        builder: (BuildContext context, BoxConstraints constraints) {
      final controller = TransformationController(Matrix4.translationValues(
          (constraints.maxWidth - 300) / 2,
          (constraints.maxHeight - 300) / 2,
          0));
      return Stack(
        clipBehavior: Clip.none,
        children: [
          Positioned.fill(child:  InteractiveViewer(
            transformationController: controller,
            boundaryMargin: const EdgeInsets.all(double.infinity),
            // panEnabled: false,
            // 启用平移
            // scaleEnabled: false,
            // 启用缩放
            child: Stack(
              clipBehavior: Clip.none, // 防止 Stack 子组件被裁剪
              children: [
                // 底层的容器
                Container(
                  width: 300,
                  height: 300,
                  color: Colors.blue,
                  child: Center(
                    child: Text(
                      '这是底层容器',
                      style: TextStyle(color: Colors.white, fontSize: 20),
                    ),
                  ),
                ),
                // 叠加的按钮
                Positioned(
                  top: 100,
                  left: 100,
                  child: ElevatedButton(
                    onPressed: () {
                      print('按钮被点击');
                    },
                    child: Text('点击我'),
                  ),
                ),
                // 叠加的文本
                Positioned(
                  top: 50,
                  left: 150,
                  child: Text(
                    '叠加的文本',
                    style: TextStyle(fontSize: 18, color: Colors.white),
                  ),
                ),
              ],
            ),
          )),
          // 叠加的文本
          Positioned(
            top: 50,
            left: 150,
            child: Text(
              '叠加的文本',
              style: TextStyle(fontSize: 18, color: Colors.white),
            ),
          ),
        ],
      );
    }));
  }
}
