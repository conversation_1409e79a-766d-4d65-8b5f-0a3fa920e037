import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/plugin/goods_import_interface.dart';
import 'package:flutter_canvas_plugins_interface/plugin/goods_model.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

class GoodsImportImpl implements GoodsImportInterface {
  @override
  Future<String> pickLabel(
      BuildContext context, String currentTemplateJsonData) {
    EasyLoading.show(status: '模拟选取商品标签数据...', dismissOnTap: false);
    return Future.delayed(Duration(milliseconds: 1500), () {
      EasyLoading.dismiss();
      return DefaultAssetBundle.of(context).loadString("assets/医药价签模板.json");
    });
  }

  List<dynamic> goodsList() {
    return [
      {
        "id": *********,
        "barcode": "********",
        "categoryId": 0,
        "userCategoryId": 0,
        "name": "复方穿心莲片",
        "level": "优",
        "norm": "g",
        "originPlace": "广西金页制药有限公司",
        "priceOfficer": "王富贵",
        "retailPrice": "399.00",
        "rushPrice": "399.00",
        "unit": "盒",
        "userAccount": "",
        "userId": 1362328
      },
      {
        "id": *********,
        "barcode": "*********",
        "categoryId": 0,
        "userCategoryId": 0,
        "name": "金银花",
        "level": "优",
        "norm": "g",
        "originPlace": "福元药业有限公司",
        "priceOfficer": "钱富贵",
        "retailPrice": "350.00",
        "rushPrice": "350.00",
        "unit": "盒",
        "userAccount": "",
        "userId": 1362328
      },
      {
        "id": *********,
        "barcode": "********",
        "categoryId": 0,
        "userCategoryId": 0,
        "name": "胃药",
        "level": "优",
        "norm": "g",
        "originPlace": "浙江一新制药股份有限公司",
        "priceOfficer": "赵富贵",
        "retailPrice": "900.00",
        "rushPrice": "900.12",
        "unit": "盒",
        "userAccount": "",
        "userId": 1362328
      },
      {
        "id": *********,
        "barcode": "********",
        "categoryId": 0,
        "userCategoryId": 0,
        "name": "氯芬黄敏片(薄膜衣片)感冒通片",
        "level": "优",
        "norm": "g",
        "originPlace": "四川属中制药有限公司",
        "priceOfficer": "李富贵",
        "retailPrice": "998.64",
        "rushPrice": "990.64",
        "unit": "盒",
        "userAccount": "",
        "userId": 1362328
      },
      {
        "id": *********,
        "barcode": "",
        "categoryId": 0,
        "userCategoryId": 0,
        "name": "今麦郎蜂蜜柚子茶",
        "level": "",
        "norm": "",
        "originPlace": "",
        "priceOfficer": "",
        "retailPrice": "6",
        "rushPrice": "",
        "unit": "",
        "userAccount": "",
        "userId": 1362328
      },
      {
        "id": *********,
        "barcode": "*************",
        "categoryId": 101681,
        "userCategoryId": 101681,
        "name": "*40g安记新奥尔良包装",
        "level": "",
        "norm": "",
        "originPlace": "",
        "priceOfficer": "",
        "retailPrice": "4.8",
        "rushPrice": "",
        "unit": "",
        "userAccount": "",
        "userId": 1362328
      },
      {
        "id": *********,
        "barcode": "*************",
        "categoryId": 101079,
        "userCategoryId": 101079,
        "name": "160ml芝家宝凉拌香辣油（辣）",
        "level": "优",
        "norm": "回回",
        "originPlace": "南京",
        "priceOfficer": "故宫",
        "retailPrice": "12",
        "rushPrice": "9.9",
        "unit": "故宫博物馆",
        "userAccount": "",
        "userId": 1362328
      },
      {
        "id": *********,
        "barcode": "*************",
        "categoryId": 101079,
        "userCategoryId": 101079,
        "name": "160ml芝家宝凉拌香麻油",
        "level": "",
        "norm": "",
        "originPlace": "故宫博物院",
        "priceOfficer": "",
        "retailPrice": "12",
        "rushPrice": "",
        "unit": "",
        "userAccount": "",
        "userId": 1362328
      },
      {
        "id": *********,
        "barcode": "*************",
        "categoryId": 0,
        "userCategoryId": 0,
        "name": "*40g安记柔香盐焗粉包装",
        "level": "",
        "norm": "",
        "originPlace": "",
        "priceOfficer": "",
        "retailPrice": "4",
        "rushPrice": "",
        "unit": "",
        "userAccount": "",
        "userId": 1362328
      },
      {
        "id": *********,
        "barcode": "*************",
        "categoryId": 0,
        "userCategoryId": 0,
        "name": "*40g安记白胡椒粉包装",
        "level": "",
        "norm": "",
        "originPlace": "",
        "priceOfficer": "",
        "retailPrice": "9.5",
        "rushPrice": "",
        "unit": "",
        "userAccount": "",
        "userId": 1362328
      },
      {
        "id": *********,
        "barcode": "*************",
        "categoryId": 0,
        "userCategoryId": 0,
        "name": "*40g安记八角粉包装",
        "level": "",
        "norm": "",
        "originPlace": "",
        "priceOfficer": "",
        "retailPrice": "7",
        "rushPrice": "",
        "unit": "",
        "userAccount": "",
        "userId": 1362328
      },
      {
        "id": *********,
        "barcode": "*************",
        "categoryId": 0,
        "userCategoryId": 0,
        "name": "*40g安记黑胡椒粉包装",
        "level": "",
        "norm": "",
        "originPlace": "",
        "priceOfficer": "",
        "retailPrice": "9.5",
        "rushPrice": "",
        "unit": "",
        "userAccount": "",
        "userId": 1362328
      },
      {
        "id": *********,
        "barcode": "*************",
        "categoryId": 0,
        "userCategoryId": 0,
        "name": "*40g安记麻辣炸鸡粉包装",
        "level": "",
        "norm": "",
        "originPlace": "",
        "priceOfficer": "",
        "retailPrice": "3.8",
        "rushPrice": "",
        "unit": "",
        "userAccount": "",
        "userId": 1362328
      },
      {
        "id": *********,
        "barcode": "*************",
        "categoryId": 0,
        "userCategoryId": 0,
        "name": "*40g安记香酥炸鸡粉包装",
        "level": "",
        "norm": "",
        "originPlace": "",
        "priceOfficer": "",
        "retailPrice": "3.8",
        "rushPrice": "",
        "unit": "",
        "userAccount": "",
        "userId": 1362328
      },
      {
        "id": *********,
        "barcode": "*************",
        "categoryId": 0,
        "userCategoryId": 0,
        "name": "30g安记黑胡椒粉瓶装",
        "level": "",
        "norm": "",
        "originPlace": "",
        "priceOfficer": "",
        "retailPrice": "10.6",
        "rushPrice": "",
        "unit": "",
        "userAccount": "",
        "userId": 1362328
      },
      {
        "id": *********,
        "barcode": "*************",
        "categoryId": 0,
        "userCategoryId": 0,
        "name": "*30g安记白胡椒粉瓶装",
        "level": "",
        "norm": "",
        "originPlace": "",
        "priceOfficer": "",
        "retailPrice": "10.6",
        "rushPrice": "",
        "unit": "",
        "userAccount": "",
        "userId": 1362328
      },
      {
        "id": *********,
        "barcode": "*************",
        "categoryId": 0,
        "userCategoryId": 0,
        "name": "*18g安记五香粉瓶装",
        "level": "",
        "norm": "",
        "originPlace": "",
        "priceOfficer": "",
        "retailPrice": "7.8",
        "rushPrice": "",
        "unit": "",
        "userAccount": "",
        "userId": 1362328
      },
      {
        "id": *********,
        "barcode": "*************",
        "categoryId": 0,
        "userCategoryId": 0,
        "name": "*36g安记椒盐瓶装",
        "level": "",
        "norm": "",
        "originPlace": "",
        "priceOfficer": "",
        "retailPrice": "7.8",
        "rushPrice": "",
        "unit": "",
        "userAccount": "",
        "userId": 1362328
      }
    ];
  }

  @override
  Future<List<List<String>>> requestGoodsList(
      String ids, Function(List<Map> p1) success) {
    // TODO: implement requestGoodsList
    throw UnimplementedError();
  }

  @override
  requestSelectGoodsPreviewUrl(
      Map<String, dynamic> params,
      Function(String p1) success,
      Function(int errorCode, String errorMsg) fail) {
    // TODO: implement requestSelectGoodsPreviewUrl
    throw UnimplementedError();
  }

  @override
  updateGoodsInfo(Map<String, dynamic> params) {
    // TODO: implement updateGoodsInfo
    throw UnimplementedError();
  }

  @override
  addGoodsField(Map<String, dynamic> params, Function(Map p1) success,
      Function(int errorCode, String errorMsg) fail) {
    // TODO: implement addGoodsField
    throw UnimplementedError();
  }

  @override
  deleteGoodsField(Map<String, dynamic> params, Function(Map p1) success,
      Function(int errorCode, String errorMsg) fail) {
    // TODO: implement deleteGoodsField
    throw UnimplementedError();
  }

  @override
  getGoodsField(
      Map<String, dynamic>? params,
      Function(List<Map<String, dynamic>> p1) success,
      Function(int errorCode, String errorMsg) fail) {
    // TODO: implement getGoodsField
    throw UnimplementedError();
  }

  @override
  modifyGoodsField(Map<String, dynamic> params, Function(Map p1) success,
      Function(int errorCode, String errorMsg) fail) {
    // TODO: implement modifyGoodsField
    throw UnimplementedError();
  }

  @override
  notifyGoodsFieldUpdate() {
    // TODO: implement notifyGoodsFieldUpdate
    throw UnimplementedError();
  }

  @override
  showEditTextAlert(BuildContext context, String title, String? text,
      int maxLength, Function success) {
    // TODO: implement showEditTextAlert
    throw UnimplementedError();
  }

  @override
  Future pickGoods(BuildContext context, List<List<String>> goodsSelected,
      Map<String, dynamic>? templateData,
      {bool isFromCreateOrSwitch = false,
      List<String>? headers,
      Function(List<Map<String, dynamic>> value)? resultCallBack}) {
    // TODO: implement pickGoods
    throw UnimplementedError();
  }

  @override
  Future editGoods(BuildContext context, List<String> goodsInfo, String columnIndexGoodField, Map<String, dynamic> templateJson) {
    // TODO: implement editGoods
    throw UnimplementedError();
  }
}
