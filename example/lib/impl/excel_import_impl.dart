import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/plugin/excel_data.dart';
import 'package:flutter_canvas_plugins_interface/plugin/excel_import_interface.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:netal_plugin/models/netal_enum.dart';

class ExcelImportImpl implements ExcelImportInterface {
  @override
  Future<ExcelDataModel> excelData(BuildContext context,
      {bool bindingOneElement = false}) {
    EasyLoading.show(status: '模拟读取 Excel 文件数据...', dismissOnTap: false);
    return Future.delayed(Duration(milliseconds: 1500), () {
      EasyLoading.dismiss();

      return Future.value(ExcelDataModel(
          fileName: '测试.xlsx',
          excelId: '',
          headers: ['表头1', '表头2', '表头3'],
          displayHeader: true,
          columnData: [
            ['数据1-1', '数据1-2', '数据1-3', '数据1-4', '数据1-5', '数据1-6', '数据1-7'],
            [
              '111111',
              '222222',
              '333333',
              '444444',
              '555555',
              '666666',
              '777777'
            ],
            ['数据3-1', '数据3-2', '数据3-3', '数据3-4', '数据3-5', '数据3-6', '数据3-7'],
          ],
          bindPairs: [
            ExcelBindPair(
                columnIndex: 0,
                open: true,
                bindElementType: NetalElementType.text.name),
            if (!bindingOneElement) ...[
              ExcelBindPair(
                  columnIndex: 1,
                  open: true,
                  bindElementType: NetalElementType.barcode.name,
                  codeType: NetalBarcodeType.CODE128.value),
              ExcelBindPair(
                  columnIndex: 2,
                  open: true,
                  bindElementType: NetalElementType.qrcode.name,
                  codeType: NetalQRCodeType.QR_CODE.value)
            ]
          ]));
    });
  }

  @override
  deleteCloudFile(Map<String, dynamic> params, Function() success,
      Function(int p1, String p2) fail) {
    // TODO: implement deleteCloudFile
    throw UnimplementedError();
  }

  @override
  Future<List> getSupportSocialList() {
    // TODO: implement getSupportSocialList
    throw UnimplementedError();
  }

  @override
  handleImportExcelFromLocal(Function(String p1, Uint8List p2) resultData) {
    // TODO: implement handleImportExcelFromLocal
    throw UnimplementedError();
  }

  @override
  handleImportExcelFromSocialApp(
      String socialType, Function(String p1, Uint8List p2) resultData) {
    // TODO: implement handleImportExcelFromSocialApp
    throw UnimplementedError();
  }

  @override
  requestExcelFileDetail(
      Map<String, dynamic> params,
      Function(Map<String, dynamic> p1) success,
      Function(int p1, String p2) fail) {
    // TODO: implement requestExcelFileDetail
    throw UnimplementedError();
  }

  @override
  requestExcelFiles(Map<String, dynamic> params, Function(List p1) success,
      Function(int p1, String p2) fail) {
    // TODO: implement requestExcelFiles
    throw UnimplementedError();
  }

  @override
  requestUploadByCloudFileId(Map<String, dynamic> params,
      Function(Map p1) success, Function(int p1, String p2) fail) {
    // TODO: implement requestUploadByCloudFileId
    throw UnimplementedError();
  }

  @override
  requestUserUploadGoodsConfirm(Map<String, dynamic> params,
      Function(Map p1) success, Function(int p1, String p2) fail) {
    // TODO: implement requestUserUploadGoodsConfirm
    throw UnimplementedError();
  }

  @override
  uploadExcelFile(String fileName, Uint8List data,
      Function(Map<String, dynamic>? data) success, Function(int, String) fail,
      {bool? createCloudFile, String? contentHash}) {
    // TODO: implement uploadExcelFile
    throw UnimplementedError();
  }

  @override
  requestCloudFileInfo(Map<String, dynamic> params, Function(Map? p1) success,
      Function(int p1, String p2) fail) {
    // TODO: implement requestCloudFileInfo
    throw UnimplementedError();
  }

  @override
  Future<void> transformOldExcelToNewExcel(String url, String target) {
    // TODO: implement transformOldExcelToNewExcel
    throw UnimplementedError();
  }

  @override
  Future<void> updateCloudFile(
      String cloudId, String downloadUrl, String md5, int fileSize) {
    // TODO: implement updateCloudFile
    throw UnimplementedError();
  }

}
