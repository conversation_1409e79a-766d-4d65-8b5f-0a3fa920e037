import 'package:flutter_canvas_plugins_interface/plugin/loading_toast_interface.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

class LoadingToastImpl implements LoadingToastInterface {
  @override
  showLoading(
      {String msg = '',
      bool dismissOnTap = false,
      bool isInteractions = false}) {
    EasyLoading.show(status: msg, dismissOnTap: dismissOnTap);
  }

  @override
  dismissLoading() {
    EasyLoading.dismiss();
  }

  @override
  showToast(String? msg, {bool dismissOnTap = true}) {
    EasyLoading.showToast(msg!, dismissOnTap: true);
  }

  @override
  Future<void> showSuccess(String? msg, {bool dismissOnTap = true}) {
    return EasyLoading.showSuccess(msg!, dismissOnTap: true);
  }

  @override
  Future<void> showError(String? msg, {bool dismissOnTap = true}) {
    return EasyLoading.showError(msg!, dismissOnTap: true);
  }
}
