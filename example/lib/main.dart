import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_canvas_plugins_interface/shared/canvas_font_data.dart';
import 'package:niimbot_analytics/niimbot_analytics.dart';
import 'package:niimbot_flutter_canvas_example/mock/datasource/datasource_list_widget.dart';
import 'package:niimbot_flutter_canvas_example/mock/datasource/datasource_network_extension.dart';
import 'package:path/path.dart' show join;
import 'package:crypto/crypto.dart';

import 'package:niimbot_flutter_canvas_example/mock/font/font_family_widget.dart';
import 'package:niimbot_flutter_canvas_example/mock/material_widget/material_list_widget.dart';
import 'package:niimbot_http/business/environment/niimbot_http_env.dart';
import 'package:niimbot_http/business/services/niimbot_http_manager.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_local_storage/niimbot_sp.dart';
import 'package:niimbot_template/models/template_data.dart';
import 'package:niimbot_template/models/elements/qr_code_element.dart';
import 'package:niimbot_template/models/elements/text_element.dart';
import 'package:niimbot_template/models/elements/table_element.dart';
import 'package:niimbot_template/models/elements/bar_code_element.dart';
import 'package:niimbot_ui/niimbot_app.dart';
import 'package:path_provider/path_provider.dart';
import 'package:netal_plugin/netal_plugin.dart';
import 'package:niimbot_http/business/apis/rest/models/material_item.dart';

import 'package:niimbot_flutter_canvas/business/desktop/canvas_box_frame_desktop.dart';
import 'package:niimbot_flutter_canvas/provider/canvas_store.dart';


import 'package:niimbot_template/models/template/template_data_source.dart';
import 'package:niimbot_template/models/values/element_value.dart';

void main() async {
  Timeline.startSync('GC Events');
  Future.delayed(Duration(minutes: 10), () {
    Timeline.finishSync();
  });
  WidgetsFlutterBinding.ensureInitialized();
  await NetalPlugin().init();
  await NiimbotSp().getInstance();
  await NiimbotIntl.init();
  await NiimbotHttpManager.getDefault().switchApiEnv(NiimbotHttpApiEnv.fat);
  await NiimbotHttpManager.getDefault().updateAttributes(
      NiimbotHttpGlobalAttributes(
          languageCode: "zh-cn",
          authCode: 'usr-00-2de3c688d300410ba9f70eff4322c0ad'));
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return NiimbotApp(
      title: '新画板',
      home: const MyHomePage(title: 'Flutter画板'),
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key, required this.title});

  final String title;

  @override
  _MyHomePageState createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage>
    with SingleTickerProviderStateMixin {
  num excelList = 0;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  final TemplateData data = TemplateData(usedFonts: {}, elements: [
    // TextElement(
    //   value: '测试文本',
    //   isOpenMirror: true,
    //   isLock: true,
    //   rotate: 90,
    //   // mirrorType: ElementMirrorType.canvasCenterY,
    // ),
    // BarCodeElement(id: 'barcode'),
    // TableElement(),
    // QRCodeElement()
  ], values: [
    BarcodeValue(elementId: 'barcode', value: 'test')
  ]);

  Future<String> _saveToLocalFile(Uint8List bytes, {String? fileName}) async {
    final dir = await getApplicationDocumentsDirectory();
    final curFileName = fileName ?? '${md5.convert(bytes).toString()}.png';
    final file = File(join(dir.path, 'niimbot-flutter-canvas', 'photos', curFileName));
    if (await file.exists()) {
      return file.path;
    }
    await file.create(recursive: true);
    await file.writeAsBytes(bytes);
    return file.path;
  }

  static T _getLocalStorage<T>(String key,
      {bool encrypt = true, required T defaultValue}) {
    return NiimbotSp().getLocalStorage(key) ?? defaultValue;
  }

  static Future<bool> _setLocalStorage<T>(String key, T value,
      {bool encrypt = true}) {
    return NiimbotSp().setLocalStorage(key, value);
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      // onTap: InputUtils.unFocus,
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        body: CanvasBoxFrameDesktop(
          controller: CanvasStore(
            data: data,
            localFileSave: _saveToLocalFile,
            printArea: [1, 2, 3, 4],
          ),
          language: 'en',
          materialWidget: (Function(MaterialItem item)? callback) {
            return IconAlertWidget(
              materialSelect: callback,
            );
          },
          getdataSources:
              NiimbotHttpManager.getDefault().requestDataSourcesList,
          uploadExcelToOss: DataSourcesList.uploadExcelToOss,
          updateCloudFile: DataSourcesList.updateCloudFile,
          transformOldExcelToNewExcel:
              DataSourcesList.transformOldExcelToNewExcel,
          isLoginFunction: (bool showLogin) async {
            return true;
          },
          isLoginVipFunction: () async {
            return true;
          },
          fontFamilyWidget: (Function(CanvasFontData? fontItem)? onChanged,
              String? defaultValue) {
            return FontFamilyWidget(
              value: defaultValue,
              onChanged: (CanvasFontData? model) {
                onChanged!(model);
              },
            );
          },
          onPrint: onPrintData,
          onImportDataSource: (TemplateDataSource dataSource) async {
            final uploadUrl = await DataSourcesList.onImportDataSource(
              dataSource,
            );
            if (uploadUrl != null) return dataSource.copyWith(uri: uploadUrl);
            return dataSource;
          },
          getLocalStorage: _getLocalStorage,
          setLocalStorage: _setLocalStorage,
        ),
      ),
    );
  }

  /// 打印入口
  onPrintData(BuildContext context, TemplateData templateData) async {
    print('----  canvasData ----\n${jsonEncode(templateData)}');
    print(
        '----  dataSourceBindInfo ----\n${templateData.dataSourceBindInfo?.page}');
    print(
        '----  dataSourceBindInfo ----\n${templateData.dataSourceBindInfo?.total}');
  }
}
