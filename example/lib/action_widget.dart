// import 'package:drawboard_dart_sdk/api.dart';
import 'package:flutter/material.dart';

class ActionWidget extends StatefulWidget {
  final Size? templateMMSize;

  const ActionWidget({super.key,  this.templateMMSize});

  @override
  State<ActionWidget> createState() => _ActionWidgetState();
}

class _ActionWidgetState extends State<ActionWidget> {
  // List<WordsResult> _wordsResult = [];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0.5,
      ),
      body: Container(
          width: double.infinity,
          child: Center(
            child: Text('sample'),
          )),
    );
  }
}
