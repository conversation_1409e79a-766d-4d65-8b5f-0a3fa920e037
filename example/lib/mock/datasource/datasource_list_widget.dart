import 'dart:io';
import 'dart:typed_data';
import 'package:crypto/src/md5.dart';
import 'package:collection/collection.dart';

import 'package:niimbot_http/business/services/niimbot_http_manager.dart';
import 'package:niimbot_flutter_canvas_example/mock/datasource/datasource_network_extension.dart';
import 'package:niimbot_template/models/template/template_data_source.dart';
import '../network/graphQlService.dart';
import 'package:niimbot_excel/niimbot_data_source_utils.dart';

class DataSourcesList {
  const DataSourcesList();

  static Future<String?> onImportDataSource(
      TemplateDataSource dataSource) async {
    // File file = await File(dataSource.uri);
    // Uint8List fileData = await file.readAsBytes();
    // int fileSize = await file.length();
    // var ossUrl = await NiimbotHttpManager.getDefault().uploadExcel(
    //     fileData: fileData, fileName: '${dataSource.name}.xlsx' ?? "自定义名称");

    var localPath =
        await NiimbotDataSourceUtils.buildLocalDataSourcePath(dataSource.hash);
    print('${localPath}localPathlocalPathlocalPathlocalPath');
    File file = File(localPath);
    Uint8List fileData = await file.readAsBytes();
    int fileSize = await file.length();
    String _name = dataSource.name ?? "";
    print('${_name} _name_name_name');
    final dataSourceName = (_name.endsWith('.xlsx') || _name.endsWith('.xls'))
        ? dataSource.name
        : '${dataSource.name}.xlsx';
    print('${dataSourceName} dataSourceNamedataSourceName');
    var ossUrl = await NiimbotHttpManager.getDefault()
        .uploadExcel(fileData: fileData, fileName: dataSourceName ?? "自定义名称");
    print('${ossUrl} ossUrlossUrl');

    final dataSources =
        await NiimbotHttpManager.getDefault().requestDataSourcesList();

    /// 生成hash
    final String md5Hash = md5.convert(fileData).toString();
    final firstDataSource = dataSources.firstWhereOrNull((element) =>
        element['md5'] == dataSource.hash || element['md5'] == md5Hash);
    if (firstDataSource != null) {
      return firstDataSource['path'];
    }

    final isUploadOss = dataSource.uri.contains('http');

    if (isUploadOss) {
      return dataSource.uri;
    }

    SettingNetwork().createCloudFile(
        downloadUrl: ossUrl,
        fileSize: fileSize,
        md5: md5Hash,
        name: dataSourceName ?? md5Hash,
        errorFunction: (errorMsg) {
          print('${errorMsg}');
        });
  }

  static Future<String> uploadExcelToOss(String path, String name) async {
    bool network = true;
    if (network == true) {
      print('${path} 解析的地址');
      File file = await File(path);
      Uint8List fileData = await file.readAsBytes();
      var ossUrl = await NiimbotHttpManager.getDefault()
          .uploadExcel(fileData: fileData, fileName: name ?? "自定义名称");
      return ossUrl;
    } else {
      print('离线不能进行上传的逻辑');
      return "";
    }
  }

  static Future<bool> transformOldExcelToNewExcel(
      String ossUrl, String savePath) async {
    bool network = true;
    if (network == true) {
      await NiimbotHttpManager.getDefault().downloadNewExcel(ossUrl, savePath);
      return true;
    } else {
      print('离线不能进行excel转换的逻辑');
      return false;
    }
  }

  static Future updateCloudFile(
      {required String cloudId,
      required String downloadUrl,
      required String md5,
      required int fileSize}) async {
    return await NiimbotHttpManager.getDefault().updateCloudFile(
        cloudId: cloudId,
        downloadUrl: downloadUrl,
        md5: md5,
        fileSize: fileSize);
  }
}
