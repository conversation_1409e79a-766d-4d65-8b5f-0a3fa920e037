import 'package:niimbot_http/business/apis/rest/models/api_list_response.dart';
import 'package:niimbot_http/business/apis/rest/models/api_response.dart';
import 'package:niimbot_http/business/services/niimbot_http_manager.dart';
import 'package:niimbot_http/business/services/niimbot_rest_service.dart';
import 'package:niimbot_http/business/utils/aliyun_oss_utils.dart';
import 'package:niimbot_http/business/apis/graphql/schema.graphql.dart';

extension DataSourcesNetworkData on NiimbotHttpManager {
  /// 获取我的云文件， 数据源
  // requestDataSourcesList(
  //     {Function(List)? success, Function? errorFunction}) async {
  //   getService<NiimbotRestService>().client.post('/user/cloudFile/my',
  //       data: {"page": 1, "limit": 10000}).then((response) {
  //     if (response.data['status_code'] == 1) {
  //       List dataList = response.data['data']['list'];
  //       success!(dataList);
  //     } else {
  //       errorFunction!(response.data['message']);
  //     }
  //   }).catchError((err) {
  //     print('${err} jijiji');
  //   });
  // }
  /// 获取我的云文件， 数据源
  Future<List<dynamic>> requestDataSourcesList() async {
    final res = await getService<NiimbotRestService>().client.post(
      '/user/cloudFile/my',
      data: {"page": 1, "limit": 10000},
    );
    return ApiResponse<ApiListResponse<dynamic>?>().parse(res.data,
            fromJsonT: (data) {
          if (data is Map<String, dynamic>) {
            return ApiListResponse.fromJson(data, (item) => item);
          }
          return null;
        })?.list ??
        [];
  }

  Future downloadNewExcel(String ossUrl, String savePath) async {
    print('${savePath} 保存的地址');
    try {
      final res = await getService<NiimbotRestService>().client.download(
          '/user/cloudFile/transform', savePath,
          method: 'POST', data: {"link": ossUrl});
      print('${res} resresres的返回值');
      return res;
    } catch (e) {
      print('${e} 错误的信息');
    }
  }

  uploadExcel({required List<int> fileData, required String fileName}) async {
    final uploadRes = await AliyunOSSUtils.upload(
        Input$OssTokenQuery(
          module: Enum$ModuleEnum.UserTemplate,
          id: '156132',
          openid: '166b72c2b88a11eb9302a683e7b21ab9',
        ),
        fileData,
        fileName);
    print('${uploadRes} uploadResuploadResuploadResuploadResuploadRes');
    return uploadRes;
  }

  updateCloudFile(
      {required String cloudId,
      required String downloadUrl,
      required String md5,
      required int fileSize}) async {
    try {
      final params = {
        "downloadUrl": downloadUrl,
        "md5": md5,
        "fileSize": fileSize
      };
      print('${params} 更新的最新的内容');
      final res = await getService<NiimbotRestService>()
          .client
          .put('/user/cloudFile/${cloudId}', data: params);
      return ApiResponse().parse(res.data);
    } catch (e) {
      print('${e} 错误的信息');
    }
  }
}
