import 'package:flutter/material.dart';
import 'package:niimbot_ui/styles/theme.dart';

import 'package:niimbot_flutter_canvas/model/material/material_category.dart';

class MaterialListDataLeftWidget extends StatefulWidget {
  final List<MaterialCategory> categoryList;
  final MaterialCategory currentMaterialCategory;
  final Function(MaterialCategory materialCategory)? onTapMaterialCatergory;

  const MaterialListDataLeftWidget(
      {required this.categoryList,
      required this.currentMaterialCategory,
      this.onTapMaterialCatergory,
      super.key});

  @override
  State<MaterialListDataLeftWidget> createState() =>
      _MaterialListDataLeftWidgetState();
}

class _MaterialListDataLeftWidgetState
    extends State<MaterialListDataLeftWidget> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(left: 10.0, top: 10.0, right: 0, bottom: 10.0),
      child: SingleChildScrollView(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: widget.categoryList
              .map((e) => _singleMaterialCatergoryItem(e))
              .toList(),
        ),
      ),
      width: 140,
      height: double.infinity,
    );
  }

  int _mouseIndex = -1;

  Widget _singleMaterialCatergoryItem(MaterialCategory materialCategory) {
    int index = widget.categoryList.indexOf(materialCategory);
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      onEnter: (event) {
        _mouseIndex = index;
        setState(() {});
      },
      onExit: (event) {
        _mouseIndex = -1;
        setState(() {});
      },
      child: GestureDetector(
          onTap: () {
            widget.onTapMaterialCatergory!(materialCategory);
          },
          child: Container(
            width: 121,
            margin: EdgeInsets.only(top: 3),
            padding: EdgeInsets.symmetric(horizontal: 10, vertical: 7),
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.all(Radius.circular(6.0)),
              color: _bgColorHandle(index),
            ),
            child: Text(
              materialCategory.name,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: _isChoose(index)
                  ? NiimbotTheme.of(context).typography.bodyStrong?.copyWith(
                      color: NiimbotTheme.of(context).colors.brandColor)
                  : NiimbotTheme.of(context).typography.body,
            ),
          )),
    );
  }

  Color _bgColorHandle(int index) {
    if (_isChoose(index)) {
      return NiimbotTheme.of(context).colors.translucentFillColorQuarternary;
    }

    if (_mouseIndex == index) {
      return NiimbotTheme.of(context).colors.translucentFillColorQuarternary;
    }

    return NiimbotTheme.of(context).colors.systemFillColorWhite;
  }

  bool _isChoose(int index) {
    return widget.categoryList[index].id == widget.currentMaterialCategory.id;
  }
}
