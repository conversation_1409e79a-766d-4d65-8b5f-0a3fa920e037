import 'package:niimbot_http/business/services/niimbot_http_manager.dart';
import 'package:niimbot_http/business/services/niimbot_rest_service.dart';
import 'package:niimbot_http/business/apis/rest/models/material_item.dart';

import 'package:niimbot_flutter_canvas/model/material/material_category.dart';

extension MaterialNetworkData on NiimbotHttpManager {
  ///素材分类
  requestMaterialCategory(
      {Function(List<MaterialCategory>)? success,
      Function? errorFunction}) async {
    getService<NiimbotRestService>().client.post('/materialIndCat/list', data: {
      "countVip": false,
    }).then((materialData) {
      // print('--------------${materialData.data}');
      // print('--------------${materialData.request}');
      // print('--------------${materialData.headers}');
      // print('--------------${materialData.request.path}');
      // print('--------------${materialData.request.queryParams}');
      // print('--------------${materialData.request.data}');
      // print('--------------${materialData.request.method}');
      print('--------------${materialData.request.options?.baseUrl}');
      if (materialData.data['status_code'] == 1) {
        List dataList = materialData.data['data'];
        List<MaterialCategory> materialList = [];
        dataList.forEach((element) {
          if (element['cat'] != null &&
              element['cat'] is List &&
              element['cat'].length > 0) {
            List catList = element['cat'];
            materialList.addAll(
                catList.map((e) => MaterialCategory.fromJson(e)).toList());
          }
        });
        success!(materialList);
      } else {
        errorFunction!(materialData.data['message']);
      }
    });
  }

  ///素材列表
  requestMaterialList(
      {required int page,
      required MaterialCategory currentMaterialCategory,
      String keyword = '',
      Function(List<MaterialItem> item, int page, int total)? success,
      Function? errorFunction}) {
    Map<String, dynamic> data = {
      "page": page,
      "limit": 100,
      "materialCategoryId": currentMaterialCategory.id,
      'materialIndustryId': 1, //currentMaterialCategory.parentId,
      // "vip": {}
    };
    if (keyword.isNotEmpty) {
      data['keyword'] = keyword;
    }
    // print('--------------${currentMaterialCategory.name}');
    // print('--------------${currentMaterialCategory.id}');
    // print('--------------${currentMaterialCategory.parentId}');
    // print('--------------${keyword}');

    getService<NiimbotRestService>()
        .client
        .post('/materialLib/page', data: data)
        .then((material) {
      // print('--------------${material.data}');
      // print('--------------${material.request}');
      // print('--------------${material.headers}');
      // print('--------------${material.request.path}');
      // print('--------------${material.request.queryParams}');
      // print('--------------${material.request.data}');
      // print('--------------${material.request.method}');
      // print('--------------${material.request.options?.baseUrl}');
      // print('--------------${material.request.options?.extra}');

      if (material.data['status_code'] == 1) {
        List dataList = material.data['data']['list'];
        int page = material.data['data']['page'];
        int total = material.data['data']['total'];

        List<MaterialItem> materialList =
            dataList.map((e) => MaterialItem.fromJson(e)).toList();
        success!(materialList, page, total);
      } else {
        errorFunction!(material.data['message']);
      }
    });
  }
}
