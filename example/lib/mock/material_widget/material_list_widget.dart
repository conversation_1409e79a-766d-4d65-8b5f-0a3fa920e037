import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import './material_network_extension.dart';
import 'package:niimbot_http/business/services/niimbot_http_manager.dart';
import 'package:niimbot_http/business/apis/rest/models/material_item.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:niimbot_ui/styles/typography_style.dart';
import "package:niimbot_local_storage/niimbot_sp.dart";

import 'material_list_data_cache.dart';
import 'material_list_data_left_widget.dart';
import './material_list_data_right_widget.dart';
import './material_list_head_widget.dart';
import 'package:niimbot_flutter_canvas/model/material/material_category.dart';

///左边选择图标弹出的面板框
class IconAlertWidget extends StatefulWidget {
  final bool isBorder;
  final Function(MaterialItem item)? materialSelect;

  IconAlertWidget({
    this.isBorder = false,
    this.materialSelect,
    super.key,
  });

  @override
  State<IconAlertWidget> createState() => _IconAlertWidgetState();
}

class _IconAlertWidgetState extends State<IconAlertWidget> {
  List<MaterialCategory> categoryList = [];
  List<MaterialItem> categoryItemList = [];
  MaterialCategory? currentCategory;
  int currentPage = 1;
  int total = 0;

  List<MaterialItem> recentCategoryItemList = [];

  final GlobalKey<MaterialListDataRightWidgetState> _globalKeyRight =
      GlobalKey();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (categoryList.isEmpty) {
        _loadRecentCategoryItemData();
        _requestCategoryFromNet();
      }
    });
  }

  _loadRecentCategoryItemData() async {
    await NiimbotSp().getInstance();
    List<String> listStr = NiimbotSp().getStringList(
        MaterialListDataCache.Canvas_Material_Item_List_Recent_Use);
    recentCategoryItemList =
        listStr.map((e) => MaterialItem.fromJson(json.decode(e))).toList();
  }

  ///素材列表
  _requestCategoryListFromNet({required bool isRefresh, String kw = ''}) {
    if (currentCategory?.id == -2) {
      categoryItemList.clear();
      if (kw.isNotEmpty) {
        recentCategoryItemList.forEach((element) {
          if (element.name.contains(kw)) {
            categoryItemList.add(element);
          }
        });
      } else {
        categoryItemList.addAll(recentCategoryItemList);
      }
      setState(() {});
      _globalKeyRight.currentState?.scrollToTop();
      return;
    }
    if (isRefresh) {
      currentPage = 1;
    } else {
      if (categoryItemList.length >= total) {
      } else {
        currentPage += 1;
      }
    }
    if (!isRefresh && categoryItemList.length >= total) {
      return;
    }
    NiimbotHttpManager.getDefault().requestMaterialList(
        page: currentPage,
        currentMaterialCategory: currentCategory!,
        success: (List<MaterialItem> list, int tempPage, int tempTotal) {
          total = tempTotal;
          currentPage = tempPage;
          if (isRefresh) {
            categoryItemList.clear();
            _globalKeyRight.currentState?.scrollToTop();
          }
          categoryItemList.addAll(list);
          setState(() {});
        },
        errorFunction: (String? error) {});
  }

  ///素材分类
  _requestCategoryFromNet() {
    NiimbotHttpManager.getDefault().requestMaterialCategory(
        success: (List<MaterialCategory> list) {
          categoryList.clear();
          categoryList.addAll(list);
          if (categoryList.length > 0) {
            if (recentCategoryItemList.length > 0) {
              MaterialCategory recent = MaterialCategory(
                  id: -2,
                  name: NiimbotIntl.getIntlMessage("app01276", "最近使用"),
                  parentId: -2);
              categoryList.insert(0, recent);
            }
            currentCategory = categoryList.first;
            _requestCategoryListFromNet(isRefresh: true);
          }
          setState(() {});
        },
        errorFunction: (String error) {});
  }

  @override
  Widget build(BuildContext context) {
    final niimbotColors = NiimbotTheme.of(context).colors;
    return Container(
      width: 394,
      height: MediaQuery.of(context).size.height * 0.7,
      child: Column(
        children: [
          MaterialListHeadWidget(
            searchFunction: (String kw) {
              _requestCategoryListFromNet(kw: kw, isRefresh: true);
            },
          ),
          Divider(color: niimbotColors.dividerColor, height: 1),
          Expanded(
            child: Container(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  currentCategory == null || categoryList.isEmpty
                      ? Container()
                      : MaterialListDataLeftWidget(
                          currentMaterialCategory: currentCategory!,
                          categoryList: categoryList,
                          onTapMaterialCatergory: (MaterialCategory category) {
                            currentCategory = category;
                            _requestCategoryListFromNet(isRefresh: true);
                          },
                        ),
                  Container(width: 1, color: niimbotColors.dividerColor),
                  categoryItemList.isEmpty
                      ? Expanded(
                          child: Center(
                          child: Text(
                            NiimbotIntl.getIntlMessage("pc0070", "暂无素材分类列表"),
                            style: TextStyle(
                                fontWeight: NiimbotFontWeight.regular,
                                color: NiimbotTheme.of(context)
                                    .colors
                                    .systemFillColorBlack,
                                fontSize: 14),
                          ),
                        ))
                      : MaterialListDataRightWidget(
                          key: _globalKeyRight,
                          categoryItem: categoryItemList,
                          onTapMaterial: (MaterialItem item) {
                            widget.materialSelect!(item);
                          },
                          onLoadData: () {
                            _requestCategoryListFromNet(isRefresh: false);
                          },
                        ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    super.dispose();
  }
}
