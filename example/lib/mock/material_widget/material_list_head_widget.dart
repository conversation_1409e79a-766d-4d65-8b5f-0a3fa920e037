import 'package:flutter/material.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_ui/styles/theme.dart';

class MaterialListHeadWidget extends StatelessWidget {
  final Function(String kw)? searchFunction;

  const MaterialListHeadWidget({this.searchFunction, super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10, vertical: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            child: Text(
      NiimbotIntl.getIntlMessage("pc0008", "图标"),
              style: NiimbotTheme.maybeOf(context)?.typography.titleStrong,
            ),
            margin: EdgeInsets.only(left: 6),
          ),
          // Container(
          //   width: 220,
          //   child: NiimbotInput(
          //     width: 228,
          //     height: 32,
          //     prefixIcon:
          //         NiimbotIcons.search(size: 16.00, color: Color(0x3C3C3C43)),
          //     placeholder: '搜索素材',
          //     onChanged: (String value) {
          //       searchFunction!(value);
          //     },
          //     onClear: (value) {
          //       searchFunction!('');
          //     },
          //   ),
          // )
        ],
      ),
    );
  }
}
