import 'dart:convert';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:niimbot_http/business/apis/rest/models/material_item.dart';
import 'package:niimbot_local_storage/niimbot_sp.dart';
import 'package:niimbot_ui/styles/theme.dart';

import 'material_list_data_cache.dart';
import 'package:niimbot_flutter_canvas/extensions/material_item.dart';

class MaterialListDataRightWidget extends StatefulWidget {
  final List<MaterialItem> categoryItem;
  final Function(MaterialItem item)? onTapMaterial;
  final Function? onLoadData;

  const MaterialListDataRightWidget({
    required this.categoryItem,
    this.onTapMaterial,
    this.onLoadData,
    super.key,
  });

  @override
  State<MaterialListDataRightWidget> createState() =>
      MaterialListDataRightWidgetState();
}

class MaterialListDataRightWidgetState
    extends State<MaterialListDataRightWidget> {
  ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(() {
      if (_scrollController.position.pixels ==
          _scrollController.position.maxScrollExtent) {
        widget.onLoadData!();
      }
    });
  }

  scrollToTop() {
    _scrollController.animateTo(0,
        duration: Duration(milliseconds: 200), curve: Curves.easeInOut);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Expanded(
      flex: 1,
      child: Container(
        alignment: Alignment.topLeft,
        child: GridView.builder(
          padding: EdgeInsets.symmetric(vertical: 12, horizontal: 15),
          controller: _scrollController,
          shrinkWrap: true,
          itemCount: widget.categoryItem.length,
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 4,
            mainAxisSpacing: 12,
            crossAxisSpacing: 12,
            childAspectRatio: 1,
          ),
          itemBuilder: (context, index) {
            // Widget temp =_singleMaterialCategoryListItem(index, context);
            // return Draggable(
            //     rootOverlay: true,
            //     feedback: temp,
            //     ignoringFeedbackSemantics: false,
            //     data: '',
            //     onDragStarted: () {},
            //     onDragEnd: (DraggableDetails details) {},
            //     onDragCompleted: () {},
            //     onDraggableCanceled: (Velocity velocity, Offset offset) {},
            //     child: temp);
            return _singleMaterialCategoryListItem(index, context);
          },
        ),
      ),
    );
  }

  Widget _singleMaterialCategoryListItem(int index, context) {
    final niimbotColors = NiimbotTheme.of(context).colors;
    MaterialItem e = widget.categoryItem[index];
    return GestureDetector(
      onTap: () async {
        widget.onTapMaterial!(e);
        _onHandleRecentUseClick(e);
      },
      child: Container(
        padding: EdgeInsets.all(6.0),
        decoration: BoxDecoration(
          border: Border.all(
            color: niimbotColors.dividerColor,
            width: 1,
          ),
          borderRadius: BorderRadius.circular(8.0),
        ),
        child: CachedNetworkImage(
          imageUrl: e.thumbnail,
          width: 32,
          height: 32,
        ),
      ),
    );
  }

  void _onHandleRecentUseClick(MaterialItem e) async {
    await NiimbotSp().getInstance();
    List<String> listStr = [];
    listStr.addAll(NiimbotSp().getStringList(
        MaterialListDataCache.Canvas_Material_Item_List_Recent_Use));
    List<MaterialItem> list = [];
    list.addAll(
        listStr.map((el) => MaterialItem.fromJson(json.decode(el))).toList());
    list.removeWhere((element) => element.id == e.id);
    list.insert(0, e);
    List<String> jsonStrList =
        list.map((e) => json.encode(e.toJson())).toList();
    NiimbotSp().setStringList(
        MaterialListDataCache.Canvas_Material_Item_List_Recent_Use,
        jsonStrList);
  }
}
