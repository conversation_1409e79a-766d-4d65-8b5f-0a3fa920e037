import 'package:niimbot_http/business/services/niimbot_http_manager.dart';
import 'package:niimbot_http/business/services/niimbot_graphql_service.dart';
import 'package:graphql_flutter/graphql_flutter.dart';

class SettingNetwork {
  void createCloudFile(
      {required String downloadUrl,
      required int fileSize,
      required String md5,
      required String name,
      Function? success,
      Function? errorFunction}) {
    String mutation = '''
mutation CREATE_CLOUD_FILE(\$input: CreateCloudFileInput!) {
  createCloudFile(input: \$input) {
    id
    userId
    downloadUrl
    name
    appCode
    source
    fileSize
    md5
    version
    createTime
  }
}
            ''';
    NiimbotHttpManager.getDefault()
        .getService<NiimbotGraphQLService>()
        .client
        .gqlClient
        .mutate(MutationOptions(document: gql(mutation), variables: {
          "input": {
            "appCode": 3,
            "downloadUrl": downloadUrl,
            "fileSize": fileSize,
            "md5": md5,
            "name": name,
            "source": 3,
          }
        }))
        .then((res) {
      if (res.exception?.graphqlErrors.isNotEmpty ?? false) {
        if (errorFunction != null) {
          errorFunction(res.exception?.graphqlErrors);
        }
      } else {
        if (success != null) {
          success(res.data);
        }
      }
    }).catchError((error) {
      print('${error} 报错的信息');
    });
  }
}
