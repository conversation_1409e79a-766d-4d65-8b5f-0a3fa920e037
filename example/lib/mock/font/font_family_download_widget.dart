import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:niimbot_ui/icons/icons.dart';
import 'package:niimbot_ui/styles/theme.dart';
import 'package:flutter_canvas_plugins_interface/shared/canvas_font_data.dart';
import 'package:niimbot_ui/widgets/niimbot_toast/niimbot_toast_controller.dart';
import 'font_family_file_manager.dart';

import 'package:niimbot_flutter_canvas/business/desktop/models/canvas_element_font_download_model.dart';

class FontFamilyDownloadWidget extends StatefulWidget {
  final Map<String, String> usedFonts;
  final List<CanvasFontData> options;
  final String? value;
  final Function(CanvasFontData?, bool) onChanged;
  final Function(CanvasFontData?, bool) downloadSuccess;

  const FontFamilyDownloadWidget({
    required this.usedFonts,
    required this.options,
    this.value,
    required this.onChanged,
    required this.downloadSuccess,
    super.key,
  });

  @override
  State<FontFamilyDownloadWidget> createState() =>
      _FontFamilyDownloadWidgetState();
}

class _FontFamilyDownloadWidgetState extends State<FontFamilyDownloadWidget> {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 256,
      child: _popupBottomWidget(),
    );
  }

  SingleChildScrollView _popupBottomWidget() {
    List<Widget> widgetList = [];
    for (int i = 0; i < widget.options.length; i++) {
      widgetList.add(_singleSelectWidget(i));
    }
    return SingleChildScrollView(
      padding: const EdgeInsets.all(5.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: widgetList,
      ),
    );
  }

  int _mouseIndex = -1;
  int _tapIndex = -1;

  Column _singleSelectWidget(int index) {
    String name = widget.options[index].name.toString();
    String code = widget.options[index].code.toString();
    String thumbnailUrl = widget.options[index].thumbnailUrl.toString();

    bool isDisabled = !widget.usedFonts.keys.contains(code);
    bool downloading = false;
    double? progressValue = 0.0;
    bool needDownload = isDisabled;

    /// Todo vip
    if (downloadingList.map((e) => e.value).toList().contains(code)) {
      downloading = true;
      progressValue = downloadingList
              .firstWhere((element) => element.value == code)
              .process ??
          0.0;
    }

    return Column(
      children: [
        SizedBox(height: 3),
        MouseRegion(
          // cursor:
          //     !isDisabled ? SystemMouseCursors.click : SystemMouseCursors.noDrop,
          cursor: SystemMouseCursors.click,
          onEnter: (event) {
            if (!isDisabled) {
              _mouseIndex = index;
              setState(() {});
            }
          },
          onExit: (event) {
            if (!isDisabled) {
              _mouseIndex = -1;
              setState(() {});
            }
          },
          child: GestureDetector(
            onTapDown: (TapDownDetails detail) {
              if (!isDisabled) {
                _tapIndex = index;
                setState(() {});
              }
            },
            onTapCancel: () {
              if (!isDisabled) {
                _tapIndex = -1;
                setState(() {});
              }
            },
            onTapUp: (details) {
              if (!isDisabled) {
                _tapIndex = -1;
                setState(() {});
              }
            },
            onTap: () {
              CanvasFontData curVal = widget.options[index];
              widget.onChanged(curVal, isDisabled);
              if (isDisabled) {
                //   widget.onChanged(curVal);
                // } else {
                _onDownload(curVal);
              }
            },
            child: Container(
              width: 260,
              padding: const EdgeInsets.symmetric(horizontal: 6.0),
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.all(Radius.circular(5.0)),
                color: _bgColorHandle(code, index),
              ),
              child: Row(
                children: [
                  _isChoose(code)
                      ? SizedBox(
                          width: 24,
                          child: NiimbotIcons.check(
                              size: 24,
                              color:
                                  NiimbotTheme.of(context).colors.brandColor),
                        )
                      : const SizedBox(
                          width: 24,
                        ),
                  Expanded(
                    flex: 1,
                    child: Container(
                      constraints: BoxConstraints(maxWidth: 150),
                      height: 36,
                      alignment: Alignment.centerLeft,
                      child: Opacity(
                        opacity: isDisabled ? 0.6 : 1.0,
                        child: CachedNetworkImage(
                            height: 18.0,
                            imageUrl: thumbnailUrl,
                            placeholder: (BuildContext ctx, String text) {
                              return Container(
                                height: 36,
                                child: Text(
                                  name,
                                  style:
                                      NiimbotTheme.of(context).typography.body,
                                ),
                              );
                            },
                            errorWidget: (_, __, ___) {
                              return Container(
                                alignment: AlignmentDirectional.centerStart,
                                child: Text(
                                  name,
                                  style:
                                      NiimbotTheme.of(context).typography.body,
                                ),
                              );
                            }),
                      ),
                    ),
                  ),
                  needDownload
                      ? downloading
                          ? SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(
                                backgroundColor: NiimbotTheme.of(context)
                                    .colors
                                    .brandColorDisabled,
                                valueColor: new AlwaysStoppedAnimation(
                                    NiimbotTheme.of(context).colors.brandColor),
                                value: progressValue,
                                strokeWidth: 2.0,
                              ),
                            )
                          : NiimbotIcons.downloadArrow(
                              color: NiimbotTheme.of(context)
                                  .colors
                                  .textFillColorSecondary,
                              size: 16,
                            )
                      : Container(),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  List<CanvasElementFontDownloadModel> downloadingList = [];

  void _onDownload(CanvasFontData currentVal) async {
    if (downloadingList
        .map((e) => e.value)
        .toList()
        .contains(currentVal.code)) {
      return;
    }
    if (downloadingList.length >= 3) {
      NiimbotToastController().show(
          NiimbotIntl.getIntlMessage("app100000539", "最多同时进行三个任务"),
          icon: NiimbotIcons.tips(
            color: NiimbotTheme.of(context).colors.textFillColorPrimary,
            size: 20.00,
          ));
      return;
    }
    CanvasElementFontDownloadModel model =
        CanvasElementFontDownloadModel(value: currentVal.code, process: 0.0);
    downloadingList.add(model);
    FontFamilyFileManager().downloadFontItem(currentVal, (double process) {
      model.process = process;
      if (mounted) setState(() {});
    }, (bool success) {
      if (success) {
        downloadingList.remove(model);
        widget.downloadSuccess(currentVal, downloadingList.isEmpty);
      }
      if (mounted) setState(() {});
    });
  }

  Color _bgColorHandle(String value, int index) {
    if (_isChoose(value)) {
      return NiimbotTheme.of(context).colors.translucentFillColorQuarternary;
    }
    if (_tapIndex == index) {
      return NiimbotTheme.of(context).colors.translucentFillColorTertiary;
    }
    if (_mouseIndex == index) {
      return NiimbotTheme.of(context).colors.translucentFillColorQuarternary;
    }

    return NiimbotTheme.of(context).colors.solidBackgroundFillColorBase;
  }

  ///当前是否选择
  bool _isChoose(String value) {
    if (widget.value == null) {
      return false;
    }
    return widget.value == value;
  }
}
