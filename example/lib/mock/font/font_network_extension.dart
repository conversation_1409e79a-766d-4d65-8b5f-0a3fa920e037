
import 'package:niimbot_http/business/services/niimbot_http_manager.dart';
import 'package:niimbot_http/business/services/niimbot_rest_service.dart';
import 'package:dio/dio.dart';

extension FontNetworkData on NiimbotHttpManager {
  /// 获取字体分类列表
  requestFontLibClassList(
      {Function(Map<String, String>)? success, Function? errorFunction}) async {
    getService<NiimbotRestService>()
        .client
        .post('/content/fontlib/classifies', data: {}).then((response) {
      if (response.data['status_code'] == 1) {
        print('object====${response.data}');
        List dataList = response.data['data'];
        Map<String, String> datasource = Map();
        for (var dataJson in dataList) {
          String languageCode =
              dataJson['languageCode'].toString().toLowerCase();
          datasource[languageCode] = dataJson['id'].toString();
        }
        print('object====$datasource');
        success!(datasource);
      } else {
        errorFunction!(response.data['message']);
      }
    });
  }

  /// 获取字体库列表
  requestFontLibList(List<String> languageCodes,
      {Function(List)? success, Function? errorFunction}) async {
    try {
      var response = await Dio().get(
          'https://oss-print.niimbot.com/public_resources/static_resources/font.json');
      List dataList = response.data;
      dataList = dataList
          .where((element) =>
              element['status'] == 1 &&
              element['platformComplexDtos'].any((v) => v['code'] == 'CP001'))
          .toList();
      dataList.sort((a, b) {
        int aVal = int.tryParse(a['classifyId']) != null
            ? int.parse(a['classifyId'])
            : 0;
        int bVal = int.tryParse(b['classifyId']) != null
            ? int.parse(b['classifyId'])
            : 0;
        return aVal - bVal;
      });
      success!(dataList);
    } catch (e) {
      errorFunction?.call(e);
    }

  }
}
