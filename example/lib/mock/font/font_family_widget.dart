import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/shared/canvas_font_data.dart';
import 'package:niimbot_ui/widgets/niimbot_popover/niimbot_popover.dart';
import 'package:netal_plugin/netal_plugin.dart';

import 'font_family_download_widget.dart';
import 'font_family_file_manager.dart';
import 'package:niimbot_flutter_canvas/business/desktop/widgets/element_attribute/common/border_choose_widget.dart';

class FontFamilyWidget extends StatefulWidget {
  final String? value;
  final ValueChanged<CanvasFontData?> onChanged;

  FontFamilyWidget({
    this.value,
    required this.onChanged,
    super.key,
  });

  @override
  State<FontFamilyWidget> createState() => _FontFamilyWidgetState();
}

class _FontFamilyWidgetState extends State<FontFamilyWidget> {
  List<CanvasFontData> fontFamilyOptions = FontFamilyFileManager().defaultFonts;
  Map<String, String> usedFonts = NetalPlugin().getUsedFonts();
  List<String> usedFontKeys = [];
  final GlobalKey<NiimbotPopoverState> _globalKey = GlobalKey();

  /// 字体点击顺序
  List<CanvasFontData> tapFonts = [];

  @override
  void initState() {
    super.initState();

    /// 兼容鸿蒙字体读不到的情况
    if (!usedFonts.keys.contains('Harmony')) {
      usedFonts['Harmony'] = '鸿蒙.ttf';
    }

    usedFontKeys = usedFonts.keys.toList();
    FontFamilyFileManager().getFontFamilyOptions(success: (List dataList) {
      _updateFontFamilyOptions(data: dataList);
    }, error: (String errMsg) {
      _updateFontFamilyOptions();
    });
  }

  @override
  void didUpdateWidget(FontFamilyWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.value != oldWidget.value &&
        widget.value != null &&
        widget.value != 'Harmony') {
      FontFamilyFileManager().loadFontToApp(widget.value!);
    }
  }

  _updateFontFamilyOptions({List? data, bool? refresh}) {
    FontFamilyFileManager().updateFontFamilyOptions(usedFontKeys,
        selectValue: widget.value,
        data: data, callback: (List<CanvasFontData> dataList) {
      fontFamilyOptions = dataList;
      if (refresh == true) {
        setState(() {});
      }
    });
  }

  _onChanged(CanvasFontData? data) {
    if (tapFonts.last.code == data?.code) {
      tapFonts = [];
    } else {
      tapFonts =
          tapFonts.where((element) => element.code != data?.code).toList();
    }
    widget.onChanged(data);
    ContextMenuController.removeAny();
  }

  @override
  Widget build(BuildContext context) {
    final list =
        fontFamilyOptions.where((element) => element.code == widget.value);
    String label =
        list.length > 0 ? (list.first.name ?? '') : (widget.value ?? '');

    return InkWell(
        onHover: (e) {
          if (e) _updateFontFamilyOptions(refresh: true);
        },
        onTap: () {
          _updateFontFamilyOptions();
        },
        child: NiimbotPopover(
          key: _globalKey,
          child: BorderChooseWidget(
            title: label,
          ),
          content: FontFamilyDownloadWidget(
            usedFonts: usedFonts,
            options: fontFamilyOptions,
            value: widget.value,
            downloadSuccess: (e, downloadEnd) {
              String code = e?.code ?? '';
              if (usedFontKeys.isNotEmpty) {
                usedFontKeys = usedFonts.keys.toList();
                usedFonts[code] = '${code}.ttf';
                usedFontKeys = [code, ...usedFontKeys];
              } else {
                usedFonts[code] = '${code}.ttf';
                usedFontKeys = usedFonts.keys.toList();
              }
              if (downloadEnd && tapFonts.length > 0) _onChanged(tapFonts.last);
            },
            onChanged: (CanvasFontData? data, bool disabled) {
              if (data != null) tapFonts.add(data);
              if (!disabled) _onChanged(data);
            },
          ),
        ));
  }
}
