import 'dart:io';
import 'package:flutter/services.dart';
import 'package:niimbot_flutter_canvas_example/mock/font/font_network_extension.dart';
import 'package:niimbot_http/business/services/niimbot_http_manager.dart';
import 'package:niimbot_intl/niimbot_intl.dart';
import 'package:path/path.dart' as path;
import 'package:netal_plugin/netal_plugin.dart';
import 'package:flutter_canvas_plugins_interface/shared/canvas_font_data.dart';
import 'package:dio/dio.dart';

/// 维护字体文件状态
class FontFamilyFileManager {
  FontFamilyFileManager._internal();

  static FontFamilyFileManager? _instance;

  /// 字体分类
  Map<String, String> initFontLibClassData = {};

  /// 字体列表
  List initFontFamily = [];
  List<CanvasFontData> initFontFamilyOptions = [];

  /// 默认插入本地的鸿蒙等字体
  List<CanvasFontData> defaultFonts = [
    CanvasFontData(
        id: -1, code: 'Harmony', name: '鸿蒙', url: '鸿蒙.ttf', isVip: false),
  ];

  /// 桌面端支持语言
  List<String> languageCodes = [
    'zh-cn',
    'zh-cn-t',
    'en',
    'ko',
    'ja',
    'de',
    'fr',
    'ru',
  ];

  factory FontFamilyFileManager() {
    if (_instance == null) {
      _instance = FontFamilyFileManager._internal();
    }
    return _instance!;
  }

  // InternetFileStorageIO storageIO = InternetFileStorageIO();

  /// 查询文件是否存在
  bool findFileEx(String filename) {
    final file = File(path.join(
      NetalPlugin().getFontDir(),
      filename,
    ));
    return file.existsSync();
  }

  /// 检查文件是否存在，存在就删除文件
  void deleteFileIfFind(String filename) {
    bool value = findFileEx(filename);
    if (value) {
      deleteFileEx(filename);
    }
  }

  /// 删除文件
  void deleteFileEx(String filename) {
    final file = File(path.join(
      NetalPlugin().getFontDir(),
      filename,
    ));
    file.deleteSync();
  }

  /// 下载字体
  void downloadFontItem(
      CanvasFontData fontItem,
      Function(double progress) progressCallback,
      Function(bool success) downloadCallback) {
    String fontName = '${fontItem.code}.ttf';
    final savePath = path.join(
      NetalPlugin().getFontDir(),
      fontName,
    );
    Dio().download(fontItem.url, savePath,
        onReceiveProgress: (receivedLength, contentLength) {
      double progress = receivedLength / contentLength;
      progressCallback.call(progress);
    }).then((res) async {
      Uint8List data = await res.data.transform(Uint8List.fromList).single;
      readFont(data, fontItem.code ?? '');
      downloadCallback.call(true);
    }, onError: (e) {
      deleteFileIfFind(fontName);
      downloadCallback.call(false);
    });
    // ;
    // custom_internet_file.InternetFile.get(
    //   fontItem.url,
    //   force: true,
    //   option: new custom_internet_file.InternetFileOption(),
    //   progress: (receivedLength, contentLength) {
    //     double progress = receivedLength / contentLength;
    //     progressCallback.call(progress);
    //   },
    //   storage: storageIO,
    //   storageAdditional: storageIO.additional(
    //     filename: fontName,
    //     location: NetalPlugin().getFontDir(),
    //   ),
    // );
  }

  /// 动态设置字体
  readFont(Uint8List fontPath, String code) async {
    // var fontLoader = FontLoader(code);
    // fontLoader.loadFont(fontPath, code);
  }

  /// 获取服务器字体分类
  void getFontLibClassList(
      {Function(Map<String, String> mapData)? success,
      Function(String err)? error}) {
    if (initFontLibClassData.isEmpty) {
      NiimbotHttpManager.getDefault().requestFontLibClassList(
          success: (Map<String, String> res) {
            initFontLibClassData = res;
            success?.call(initFontLibClassData);
          },
          errorFunction: error);
    } else {
      success?.call(initFontLibClassData);
    }
  }

  /// 获取服务器字体
  void getFontFamilyOptions(
      {Function(List dataList)? success, Function(String err)? error}) {
    if (initFontFamily.isEmpty) {
      NiimbotHttpManager.getDefault().requestFontLibList(languageCodes,
          success: (List dataList) {
        initFontFamily.clear();
        initFontFamily.addAll(dataList);
        success?.call(initFontFamily);
      }, errorFunction: error);
    } else {
      success?.call(initFontFamily);
    }
  }

  /// 获取当前语言
  String getLanguageCode() {
    String languageCode = NiimbotIntl.getCurrentLocale().languageCode;
    if (languageCode == 'zh') {
      if (NiimbotIntl.getCurrentLocale().scriptCode == 'Hant') {
        languageCode = 'zh-cn-t';
      } else {
        languageCode = 'zh-cn';
      }
    } else if (languageCode == 'zh_TW' || languageCode == 'zh_Hant') {
      languageCode = 'zh-cn-t';
    }
    return languageCode;
  }

  /// 字体排序
  List updateFontFamilySort(List dataList) {
    /// 获取当前语言
    String languageCode = getLanguageCode();
    List<String> currentLang = languageCodes
        .where((element) => element.toLowerCase() == languageCode.toLowerCase())
        .toList();
    List<String> restLang = languageCodes
        .where((element) => element.toLowerCase() != languageCode.toLowerCase())
        .toList();
    List<String> newLanguageCodes = [...currentLang, ...restLang];
    List<String> fontLibClassIdSort = newLanguageCodes.fold([], (res, e) {
      var id = initFontLibClassData[e.toLowerCase()];
      if (id != null) {
        res.add(id);
      }
      return res;
    });
    List newDataList = fontLibClassIdSort.fold([], (res, e) {
      res.addAll(dataList
          .where((element) => element['classifyId'].toString() == e.toString())
          .toList());
      return res;
    });
    newDataList.addAll(dataList
        .where((element) =>
            !fontLibClassIdSort.contains(element['classifyId'].toString()))
        .toList());
    return newDataList;
  }

  /// 更新字体列表
  void updateFontFamilyOptions(List<String> usedFonts,
      {List? data,
      String? selectValue,
      Function(List<CanvasFontData> dataList)? callback}) {
    getFontLibClassList(success: (Map<String, String> map) {
      if (initFontFamilyOptions.isEmpty) {
        List datasource = updateFontFamilySort([...(data ?? initFontFamily)]);
        List<CanvasFontData> fontDataList = [...defaultFonts];
        for (var fontDataJson in datasource) {
          fontDataList.add(CanvasFontData(
              code: fontDataJson['code'] as String,
              isVip: fontDataJson['isVip'] as bool,
              thumbnailUrl: fontDataJson['thumbnailUrl'] as String,
              id: fontDataJson['id'] as int,
              url: fontDataJson['path'] as String,
              name: fontDataJson['name'] as String));
        }

        /// 当前选择的
        List<CanvasFontData> selectedFonts = fontDataList
            .where((element) => element.code == selectValue)
            .toList();

        /// 已下载的
        List<CanvasFontData> usedFontsData = fontDataList
            .where((element) =>
                usedFonts.contains(element.code) && element.code != selectValue)
            .toList();

        /// 未下载和未使用的
        List<CanvasFontData> notFindFontsData = fontDataList
            .where((element) =>
                (!findFileEx('${element.code}.ttf') ||
                    !usedFonts.contains(element.code)) &&
                element.code != selectValue)
            .toList();
        initFontFamilyOptions.clear();
        initFontFamilyOptions.addAll(selectedFonts);
        initFontFamilyOptions.addAll(usedFontsData);
        initFontFamilyOptions.addAll(notFindFontsData);
      } else {
        /// 切换过语言，更新当前字体排序
        String languageCode = getLanguageCode();
        List datasource = updateFontFamilySort([...(data ?? initFontFamily)]);
        String? classId = initFontLibClassData[languageCode.toLowerCase()];
        List<String> codeList = datasource.fold([], (previousValue, element) {
          if (element['classifyId'] == classId) {
            previousValue.add(element['code']);
          }
          return previousValue;
        });

        /// 当前选择的
        List<CanvasFontData> selectedFonts = initFontFamilyOptions
            .where((element) => element.code == selectValue)
            .toList();

        /// 已下载的
        List<CanvasFontData> downloadFonts = initFontFamilyOptions
            .where((element) =>
                usedFonts.contains(element.code) && element.code != selectValue)
            .toList();

        /// 未下载的
        List<CanvasFontData> notFindFontsData = initFontFamilyOptions
            .where((element) =>
                !usedFonts.contains(element.code) &&
                element.code != selectValue)
            .toList();

        List<CanvasFontData> beforeNotFindFontsData = notFindFontsData
            .where((element) => codeList.contains(element.code))
            .toList();

        List<CanvasFontData> afterNotFindFontsData = notFindFontsData
            .where((element) => !codeList.contains(element.code))
            .toList();
        initFontFamilyOptions.clear();
        initFontFamilyOptions.addAll(selectedFonts);
        initFontFamilyOptions.addAll(downloadFonts);
        initFontFamilyOptions.addAll(beforeNotFindFontsData);
        initFontFamilyOptions.addAll(afterNotFindFontsData);
      }
      callback?.call(initFontFamilyOptions);
    });
  }

  /// 已下载字体添加到本地运用
  loadFontToApp(String fontFamily) {
    final fontDir = NetalPlugin().getFontDir();
    File('$fontDir/$fontFamily.ttf').readAsBytes().then((value) {
      readFont(value, fontFamily);
    });
  }
}
