name: niimbot_flutter_canvas_example
description: niimbot canvas example app.
version: 1.0.0+3

dependencies:
  flutter:
    sdk: flutter

  # 画板组件
  niimbot_flutter_canvas:
    path: ../
  flutter_easyloading: ^3.0.5
  niimbot_local_storage: ^1.2.0
  niimbot_http: ^0.4.0

  #dependency_overrides:
  #  niimbot_http:
  #    path: ../../niimbot_http/niimbot_http
  #  niimbot_template:
  #    path: ../../niimbot_template
#  niimbot_ui:
#    path: ../../niimbot_ui
#  netal_plugin:
#    path: ../../netal_plugin
# For information on the generic Dart part of this file, see the
# following page: https://www.dartlang.org/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/
    - assets/images/
    - assets/images/element/
    - assets/fonts/
    - assets/deborah_ufw/

  fonts:
    - family: ZCOOL
      fonts:
        - asset: assets/fonts/ZCOOLXiaoWei-Regular.ttf
          weight: 700
    - family: Raleway
      fonts:
        - asset: assets/fonts/Raleway-Regular.ttf
          weight: 700
    - family: MaShanZheng
      fonts:
        - asset: assets/fonts/MaShanZheng-Regular.ttf
          weight: 700
    - family: NotoSansSC
      fonts:
        - asset: assets/fonts/NotoSansSC-Regular.otf
          weight: 700

environment:
  sdk: ">=2.19.0 <3.0.0"
  flutter: ">=3.7.0"