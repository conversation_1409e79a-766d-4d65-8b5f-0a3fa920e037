ext {
    android = [compileSdkVersion         : 28,
               suppotrSdkVersion         : "28.0.3",
               applicationId             : "com.niimbotkit.kitdemo2",
               minSdkVersion             : 21,
               targetSdkVersion          : 28,
               versionCode               : 100,
               pluginVersionName         : "1.0.0",
               //jcenter 版本号 打包上传时版本名字
               jcenterArchivesVersionName: "1.0.0",
               //内部仓库版本号
               didiArchivesVersionName   : "1000.0.22",
               versionName               : "1.0.0",
               glide_version             : "4.9.0",
               kotlin_version            : "1.3.61"
    ]

    dependencies = [
            "multidex"                : 'androidx.multidex:multidex:2.0.0',
            "appcompat"               : 'androidx.appcompat:appcompat:1.1.0',
            "cardview"                : 'androidx.cardview:cardview:1.0.0',
            "recyclerview"            : 'androidx.recyclerview:recyclerview:1.0.0',
            "design"                  : 'com.google.android.material:material:1.0.0',
            "kotlin"                  : "org.jetbrains.kotlin:kotlin-stdlib-jdk7:${android["kotlin_version"]}",
            "constraintLayout"        : 'androidx.constraintlayout:constraintlayout:1.1.3',
            "retrofit2"               : "com.squareup.retrofit2:retrofit:2.8.1",
            "retrofit2_rxjava2"       : "com.squareup.retrofit2:adapter-rxjava2:2.8.1",
            "retrofit2_gson"          : "com.squareup.retrofit2:converter-gson:2.8.1",
            "okhttp"                  : "com.squareup.okhttp3:okhttp:3.12.1",
            "okhttp_v2"               : "com.squareup.okhttp:okhttp:2.7.5",
            "utilcode"                : 'com.blankj:utilcodex:1.28.0',
            "glide"                   : "com.github.bumptech.glide:glide:${android["glide_version"]}",
            "glide_v3"                : 'com.github.bumptech.glide:glide:3.8.0',
            "glide_okhttp3"           : "com.github.bumptech.glide:okhttp3-integration:${android["glide_version"]}",
            "glide_compiler"          : "com.github.bumptech.glide:compiler:${android["glide_version"]}",
            "picasso"                 : 'com.squareup.picasso:picasso:2.71828',
            "fresco"                  : 'com.facebook.fresco:fresco:1.13.0',
            "fresco-processors"       : "jp.wasabeef:fresco-processors:2.1.0",
            "image-loader"            : 'com.nostra13.universalimageloader:universal-image-loader:1.9.5',
            "rootbeer-lib"            : 'com.scottyab:rootbeer-lib:0.0.8',
            "gson"                    : 'com.google.code.gson:gson:2.8.2',
            "zxing"                   : 'me.dm7.barcodescanner:zxing:1.9.8',
            "free_reflection"         : 'me.weishu:free_reflection:2.1.0',
            "leakcanary-android"      : 'com.squareup.leakcanary:leakcanary-android:2.2',
            "haha"                    : 'com.squareup.haha:haha:2.0.4',
            "debug-db"                : "com.amitshekhar.android:debug-db:1.0.6",
            "debug-db-encrypt"        : "com.amitshekhar.android:debug-db-encrypt:1.0.6",
            "weex_inspector"          : "com.taobao.android:weex_inspector:0.24.2.11",
            "weex_sdk"                : "com.taobao.android:weex_sdk:0.26.0",
            "brvah"                   : 'com.github.CymChad:BaseRecyclerViewAdapterHelper:3.0.2',
            "easy_refresh_layout"     : 'com.github.anzaizai:EasyRefreshLayout:1.3.1',
            "android_spinkit"         : 'com.github.ybq:Android-SpinKit:1.4.0',
            "jsonviewer"              : "com.yuyh.json:jsonviewer:1.0.6",
            "room_runtime"            : 'androidx.room:room-runtime:2.0.0',
            "room_compile"            : 'androidx.room:room-compiler:2.0.0',
            "um_analytics"            : 'com.umeng.umsdk:analytics:8.0.0',
            "um_common"               : 'com.umeng.umsdk:common:2.0.0',
            "okgo"                    : "com.lzy.net:okgo:3.0.4",
            "abridge"                 : "com.sjtu.yifei:abridge:1.0.1",
            "junit"                   : "junit:junit:4.12",
            "eventbus"                : "org.greenrobot:eventbus:3.1.1",
            "toasty"                  : "com.github.GrenderG:Toasty:1.4.2",
            "fastjson"                : "com.alibaba:fastjson:1.1.61.android",
            "BaseRecyclerViewAdapterHelper" :  "com.github.CymChad:BaseRecyclerViewAdapterHelper:2.9.47",
            "kotlinx-coroutines-android": "org.jetbrains.kotlinx:kotlinx-coroutines-android:1.3.0",
            "core-ktx" :  'androidx.core:core-ktx:1.2.0',
            "brucetoo" :  'com.brucetoo.pickview:library:1.2.3',
            "greendao" : 'org.greenrobot:greendao:3.2.2',
            "pickviewlibrary" :  'com.niimbot.pickviewlibrary:pickviewlibrary:1.0.0',
            "appupgradelibrary": 'com.niimbot.appupgradelibrary:appupgradelibrary:1.1.0',
            "luban" : 'top.zibin:Luban:1.1.8',
            "swipe-panel": 'com.blankj:swipe-panel:1.2',
            "chinese-utils": 'com.luhuiguo:chinese-utils:1.0',
            "jcprintsdk": 'com.jingchen.printsdk:api:3.0.2-beta3',
            "utillibrary": 'com.niimbot.utillibrary:utiliylibrary:1.0.12-alpha2',
            "languagelibrary": 'com.niimbot.languagelibrary:languagelibrary:1.0.3-alpha',
            "okgolibrary": 'com.niimbot.okhttplibrary:okgolibrary:1.0.15-alpha4',
            "appframework_library": 'com.niimbot.appframework_library:appframework_library:1.0.6-alpha11',
            "jcsdk_extension_library": 'com.niimbot.jcsdk_extension_library:jcsdk_extension_library:1.0.0-alpha',
            "connectlibrary": 'com.niimbot.deviceconnect_library:connectlibrary:1.0.2-alpha',
            "scanlibrary": 'com.niimbot.scanlibrary:scanlibrary:1.0.3-alpha'
    ]
}
