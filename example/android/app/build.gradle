def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterRoot = localProperties.getProperty('flutter.sdk')
if (flutterRoot == null) {
    throw new GradleException("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply from: "$flutterRoot/packages/flutter_tools/gradle/flutter.gradle"
apply plugin: 'kotlin-android-extensions'
//apply plugin: 'com.google.protobuf'

android {
    compileSdkVersion 32

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
      //  main.proto.srcDirs += '../../protos'
    }

    lintOptions {
        disable 'InvalidPackage'
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "com.niimbot.canvas"
        minSdkVersion 21
        targetSdkVersion 32
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig signingConfigs.debug
        }
    }
    sourceSets{
        main{
            jniLibs.srcDir(['libs'])
        }
    }

    repositories {
        flatDir {
            dirs 'libs'
        }
    }
    packagingOptions {
        pickFirst 'lib/arm64-v8a/libc++_shared.so'
        pickFirst 'lib/armeabi-v7a/libc++_shared.so'
    }
}

flutter {
    source '../..'
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"

    implementation rootProject.ext.dependencies["constraintLayout"]
    implementation rootProject.ext.dependencies["appcompat"]
    implementation rootProject.ext.dependencies["design"]
    implementation rootProject.ext.dependencies["core-ktx"]
    implementation rootProject.ext.dependencies["kotlinx-coroutines-android"]
   // implementation rootProject.ext.dependencies["BaseRecyclerViewAdapterHelper"]
//    implementation rootProject.ext.dependencies["fastjson"]
////    implementation rootProject.ext.dependencies["brucetoo"]
//    implementation rootProject.ext.dependencies["eventbus"]
//    implementation rootProject.ext.dependencies["glide"]
//    implementation rootProject.ext.dependencies["utilcode"]
//    implementation rootProject.ext.dependencies["zxing"]
//    implementation rootProject.ext.dependencies["utillibrary"]
//    implementation rootProject.ext.dependencies["languagelibrary"]
//    implementation rootProject.ext.dependencies["okgolibrary"]
//    implementation rootProject.ext.dependencies["appframework_library"]
////    implementation rootProject.ext.dependencies["jcprintsdk"]
//    implementation rootProject.ext.dependencies["jcsdk_extension_library"]
//    implementation rootProject.ext.dependencies["connectlibrary"]
//    implementation rootProject.ext.dependencies["scanlibrary"]

  //  implementation(name:'jcprintersdk', ext:'aar')
  //  implementation 'com.google.protobuf:protobuf-lite:3.0.1'

    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'androidx.test:runner:1.1.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.1.1'
}


//protobuf {
//    protoc {
//        artifact = 'com.google.protobuf:protoc:3.6.1'
//    }
//    plugins {
//        javalite {
//            artifact = 'com.google.protobuf:protoc-gen-javalite:3.0.0'
//        }
//    }
//    generateProtoTasks {
//        all().each { task ->
//            task.plugins {
//                javalite { }
//            }
//        }
//    }
//}

