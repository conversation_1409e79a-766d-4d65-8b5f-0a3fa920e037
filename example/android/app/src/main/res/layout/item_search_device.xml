<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="horizontal" android:layout_width="match_parent"
    android:layout_height="76dp"
    android:layout_marginTop="9dp"
    android:gravity="center_vertical">

    <ImageView
        android:id="@+id/deviceIcon"
        android:layout_width="52dp"
        android:layout_height="52dp"
        android:layout_marginLeft="14dp"
        android:scaleType="centerInside"
        android:src="@drawable/search_device_d11" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="14dp"
        android:orientation="vertical">

        <TextView
            android:id="@+id/deviceName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="D11-13D3CEI9"
            android:textSize="16sp"
            android:textColor="#262626"/>

        <TextView
            android:id="@+id/deviceConnectStatus"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="7dp"
            android:text="未连接"
            android:textSize="14sp"
            android:textColor="#999999"
            android:drawablePadding="3dp"/>

    </LinearLayout>

</LinearLayout>