buildscript {
    ext.kotlin_version = '1.6.10'
    ext.protobufVersion = '0.8.8'
    apply from: "config.gradle"

    repositories {
        maven { url 'https://maven.aliyun.com/repository/central'}
        maven { url 'https://maven.aliyun.com/repository/jcenter'}
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/public'}
        //jekins打包要读这个东东，不要换位置了！！！
        maven {
            url "http://***********:8082/artifactory/libs-release-local"
            allowInsecureProtocol = true
        }
        google()
        jcenter()
        mavenCentral()
        maven { url 'https://jitpack.io' }
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:4.1.0'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath "com.google.protobuf:protobuf-gradle-plugin:$protobufVersion"
    }
}

allprojects {
    repositories {
        //jekins打包要读这个东东，不要换位置了！！！
//        maven {
//            url "http://***********:8082/artifactory/libs-release-local"
//           // allowInsecureProtocol = true
//        }
        maven { url 'https://maven.aliyun.com/repository/central'}
        maven { url 'https://maven.aliyun.com/repository/jcenter'}
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/public'}
        //jekins打包要读这个东东，不要换位置了！！！
        maven {
            url "http://***********:8082/artifactory/libs-release-local"
            allowInsecureProtocol = true
        }
        google()
        jcenter()
        mavenCentral()
        maven { url 'https://jitpack.io' }
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(':app')
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
